(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2077],{497754:(e,t)=>{var n;!function(){"use strict";var o={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var s=typeof n;if("string"===s||"number"===s)e.push(n);else if(Array.isArray(n)&&n.length){var i=r.apply(null,n);i&&e.push(i)}else if("object"===s)for(var a in n)o.call(n,a)&&n[a]&&e.push(a)}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()},288276:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},173405:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},825549:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},380327:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>o});const o=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},331774:(e,t,n)=>{"use strict";function o(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>o})},34735:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>w,InputClasses:()=>f})
;var o=n(50959),r=n(497754),s=n(650151),i=n(525388),a=n(800417),l=n(380327),c=n(331774);var u=n(288276),d=n.n(u);function h(e){let t="";return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function p(e,t,n,o){const{removeRoundBorder:s,className:i,intent:a="default",borderStyle:l="thin",size:u,highlight:p,disabled:m,readonly:f,stretch:g,noReadonlyStyles:v,isFocused:w}=e,C=h(s??(0,c.getGroupCellRemoveRoundBorders)(n));return r(d().container,d()[`container-${u}`],d()[`intent-${a}`],d()[`border-${l}`],u&&d()[`size-${u}`],C,p&&d()["with-highlight"],m&&d().disabled,f&&!v&&d().readonly,w&&d().focused,g&&d().stretch,t&&d().grouped,!o&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],i)}function m(e,t,n){const{highlight:o,highlightRemoveRoundBorder:s}=e;if(!o)return d().highlight;const i=h(s??(0,c.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],i)}const f={FontSizeMedium:(0,s.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,s.ensureDefined)(d()["font-size-large"])},g={passive:!1};function v(e,t){const{style:n,id:r,role:s,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:f,onMouseUp:v,onKeyDown:w,onClick:C,tabIndex:R,startSlot:x,middleSlot:y,endSlot:b,onWheel:S,onWheelNoPassive:E=null,size:D,tag:N="span",type:P}=e,{isGrouped:W,cellState:_,disablePositionAdjustment:M=!1}=(0,o.useContext)(l.ControlGroupContext),I=function(e,t=null,n){const r=(0,o.useRef)(null),s=(0,o.useRef)(null),i=(0,o.useCallback)((()=>{if(null===r.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),a=(0,o.useCallback)((()=>{if(null===r.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),l=(0,o.useCallback)((e=>{a(),r.current=e,i()}),[]);return(0,o.useEffect)((()=>(s.current=[e,t,n],i(),a)),[e,t,n]),l}("wheel",E,g),z=N;return o.createElement(z,{type:P,style:n,id:r,role:s,className:p(e,W,_,M),tabIndex:R,ref:(0,i.useMergedRefs)([t,I]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:f,onMouseUp:v,onKeyDown:w,onClick:C,onWheel:S,...(0,a.filterDataProps)(e),...(0,a.filterAriaProps)(e)},x,y,b,o.createElement("span",{className:m(e,_,D)}))}v.displayName="ControlSkeleton";const w=o.forwardRef(v)},102691:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>d,BeforeSlot:()=>a,EndSlot:()=>u,MiddleSlot:()=>c,StartSlot:()=>l});var o=n(50959),r=n(497754),s=n(173405),i=n.n(s);function a(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(i()["before-slot"],t)},n)}function l(e){const{className:t,interactive:n=!0,icon:s=!1,children:a}=e;return o.createElement("span",{className:r(i()["inner-slot"],n&&i().interactive,s&&i().icon,t)},a)}function c(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(i()["inner-slot"],i()["inner-middle-slot"],t)},n)}function u(e){
const{className:t,interactive:n=!0,icon:s=!1,children:a}=e;return o.createElement("span",{className:r(i()["inner-slot"],n&&i().interactive,s&&i().icon,t)},a)}function d(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(i()["after-slot"],t)},n)}},654936:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>w});var o=n(50959),r=n(497754),s=n(800417),i=n(269842),a=n(1811),l=n(525388),c=n(21778),u=n(383836),d=n(603548),h=n(34735),p=n(102691),m=n(825549),f=n.n(m);function g(e){return!(0,s.isAriaAttribute)(e)&&!(0,s.isDataAttribute)(e)}function v(e){const{id:t,title:n,role:i,tabIndex:a,placeholder:l,name:c,type:u,value:d,defaultValue:m,draggable:v,autoComplete:w,autoFocus:C,autoCapitalize:R,autoCorrect:x,maxLength:y,min:b,max:S,step:E,pattern:D,inputMode:N,onSelect:P,onFocus:W,onBlur:_,onKeyDown:M,onKeyUp:I,onKeyPress:z,onChange:A,onDragStart:T,size:F="small",className:O,inputClassName:L,disabled:Z,readonly:U,containerTabIndex:k,startSlot:B,endSlot:j,reference:G,containerReference:H,onContainerFocus:V,...K}=e,$=(0,s.filterProps)(K,g),q={...(0,s.filterAriaProps)(K),...(0,s.filterDataProps)(K),id:t,title:n,role:i,tabIndex:a,placeholder:l,name:c,type:u,value:d,defaultValue:m,draggable:v,autoComplete:w,autoFocus:C,autoCapitalize:R,autoCorrect:x,maxLength:y,min:b,max:S,step:E,pattern:D,inputMode:N,onSelect:P,onFocus:W,onBlur:_,onKeyDown:M,onKeyUp:I,onKeyPress:z,onChange:A,onDragStart:T};return o.createElement(h.ControlSkeleton,{...$,disabled:Z,readonly:U,tabIndex:k,className:r(f().container,O),size:F,ref:H,onFocus:V,startSlot:B,middleSlot:o.createElement(p.MiddleSlot,null,o.createElement("input",{...q,className:r(f().input,f()[`size-${F}`],L,B&&f()["with-start-slot"],j&&f()["with-end-slot"]),disabled:Z,readOnly:U,ref:G})),endSlot:j})}function w(e){e=(0,c.useControl)(e);const{disabled:t,autoSelectOnFocus:n,tabIndex:r=0,onFocus:s,onBlur:h,reference:p,containerReference:m=null}=e,f=(0,o.useRef)(null),g=(0,o.useRef)(null),[w,C]=(0,u.useFocus)(),R=t?void 0:w?-1:r,x=t?void 0:w?r:-1,{isMouseDown:y,handleMouseDown:b,handleMouseUp:S}=(0,d.useIsMouseDown)(),E=(0,i.createSafeMulticastEventHandler)(C.onFocus,(function(e){n&&!y.current&&(0,a.selectAllContent)(e.currentTarget)}),s),D=(0,i.createSafeMulticastEventHandler)(C.onBlur,h),N=(0,o.useCallback)((e=>{f.current=e,p&&("function"==typeof p&&p(e),"object"==typeof p&&(p.current=e))}),[f,p]);return o.createElement(v,{...e,isFocused:w,containerTabIndex:R,tabIndex:x,onContainerFocus:function(e){g.current===e.target&&null!==f.current&&f.current.focus()},onFocus:E,onBlur:D,reference:N,containerReference:(0,l.useMergedRefs)([g,m]),onMouseDown:b,onMouseUp:S})}},21778:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>s});var o=n(269842),r=n(383836);function s(e){const{onFocus:t,onBlur:n,intent:s,highlight:i,disabled:a}=e,[l,c]=(0,r.useFocus)(void 0,a),u=(0,o.createSafeMulticastEventHandler)(a?void 0:c.onFocus,t),d=(0,o.createSafeMulticastEventHandler)(a?void 0:c.onBlur,n);return{...e,intent:s||(l?"primary":"default"),highlight:i??l,onFocus:u,onBlur:d}}},383836:(e,t,n)=>{
"use strict";n.d(t,{useFocus:()=>r});var o=n(50959);function r(e,t){const[n,r]=(0,o.useState)(!1);(0,o.useEffect)((()=>{t&&n&&r(!1)}),[t,n]);const s={onFocus:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!0)}),[e]),onBlur:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!1)}),[e])};return[n,s]}},603548:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>r});var o=n(50959);function r(){const e=(0,o.useRef)(!1),t=(0,o.useCallback)((()=>{e.current=!0}),[e]),n=(0,o.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},855393:(e,t,n)=>{"use strict";n.d(t,{useIsomorphicLayoutEffect:()=>r});var o=n(50959);function r(e,t){("undefined"==typeof window?o.useEffect:o.useLayoutEffect)(e,t)}},525388:(e,t,n)=>{"use strict";n.d(t,{useMergedRefs:()=>s});var o=n(50959),r=n(273388);function s(e){return(0,o.useCallback)((0,r.mergeRefs)(e),e)}},778199:(e,t,n)=>{"use strict";function o(e,t,n,o,r){function s(r){if(e>r.timeStamp)return;const s=r.target;void 0!==n&&null!==t&&null!==s&&s.ownerDocument===o&&(t.contains(s)||n(r))}return r.click&&o.addEventListener("click",s,!1),r.mouseDown&&o.addEventListener("mousedown",s,!1),r.touchEnd&&o.addEventListener("touchend",s,!1),r.touchStart&&o.addEventListener("touchstart",s,!1),()=>{o.removeEventListener("click",s,!1),o.removeEventListener("mousedown",s,!1),o.removeEventListener("touchend",s,!1),o.removeEventListener("touchstart",s,!1)}}n.d(t,{addOutsideEventListener:()=>o})},908783:(e,t,n)=>{"use strict";n.d(t,{useOutsideEvent:()=>i});var o=n(50959),r=n(855393),s=n(778199);function i(e){const{click:t,mouseDown:n,touchEnd:i,touchStart:a,handler:l,reference:c}=e,u=(0,o.useRef)(null),d=(0,o.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,r.useIsomorphicLayoutEffect)((()=>{const e={click:t,mouseDown:n,touchEnd:i,touchStart:a},o=c?c.current:u.current;return(0,s.addOutsideEventListener)(d.current,o,l,document,e)}),[t,n,i,a,l]),c||u}},183787:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>r});var o=n(50959);const r=o.forwardRef(((e,t)=>{const{icon:n="",title:r,ariaLabel:s,ariaLabelledby:i,ariaHidden:a,...l}=e,c=!!(r||s||i);return o.createElement("span",{role:"img",...l,ref:t,"aria-label":s,"aria-labelledby":i,"aria-hidden":a||!c,title:r,dangerouslySetInnerHTML:{__html:n}})}))},878112:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>o.Icon});var o=n(183787)},730654:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>c,PortalContext:()=>u});var o=n(50959),r=n(632227),s=n(925931),i=n(801808),a=n(481564),l=n(682925);class c extends o.PureComponent{constructor(){super(...arguments),this._uuid=(0,s.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"";const t=this.props.className
;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE,"true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),r.createPortal(o.createElement(u.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,i.getRootOverlapManager)():this.context}}c.contextType=l.SlotContext;const u=o.createContext(null)},682925:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>r,SlotContext:()=>s});var o=n(50959);class r extends o.Component{shouldComponentUpdate(){return!1}render(){return o.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const s=o.createContext(null)},800417:(e,t,n)=>{"use strict";function o(e){return s(e,i)}function r(e){return s(e,a)}function s(e,t){const n=Object.entries(e).filter(t),o={};for(const[e,t]of n)o[e]=t;return o}function i(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function a(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>r,filterDataProps:()=>o,filterProps:()=>s,isAriaAttribute:()=>a,isDataAttribute:()=>i})},1811:(e,t,n)=>{"use strict";function o(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>o})},273388:(e,t,n)=>{"use strict";function o(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function r(e){return o([e])}n.d(t,{isomorphicRef:()=>r,mergeRefs:()=>o})},801808:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>i,getRootOverlapManager:()=>l});var o=n(650151),r=n(481564);class s{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class i{constructor(e=document){this._storage=new s,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e);if(void 0!==n)return n;this.registerWindow(e);const o=this._document.createElement("div");if(o.style.position=t.position,o.style.zIndex=this._index.toString(),o.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(o);else if(t.index<=0)this._container.insertBefore(o,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(o,e)}}else"reverse"===t.direction?this._container.insertBefore(o,this._container.firstChild):this._container.appendChild(o);return this._windows.set(e,o),++this._index,o}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e)
;void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveLastWindowToTop(){const e=this._storage.getItems(),t=e[e.length-1];t&&this.moveToTop(t)}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,n)=>{e.hasAttribute(r.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(r.FOCUS_TRAP_DATA_ATTRIBUTE,e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const a=new WeakMap;function l(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,o.ensureDefined)(a.get(t));{const t=new i(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return a.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}var c;!function(e){e[e.BaseZindex=150]="BaseZindex"}(c||(c={}))},269842:(e,t,n)=>{"use strict";function o(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>o})},285089:(e,t,n)=>{"use strict";n.d(t,{setFixedBodyState:()=>l});var o=n(735922);const r=()=>!window.matchMedia("(min-width: 768px)").matches,s=()=>!window.matchMedia("(min-width: 1280px)").matches;let i=0,a=!1;function l(e){const{body:t}=document,n=t.querySelector(".widgetbar-wrap");if(e&&1==++i){const e=(0,o.getCSSProperty)(t,"overflow"),r=(0,o.getCSSPropertyNumericValue)(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&((0,o.setStyle)(n,"right",`${(0,o.getScrollbarWidth)()}px`),t.style.paddingRight=`${r+(0,o.getScrollbarWidth)()}px`,a=!0),t.classList.add("i-no-scroll")}else if(!e&&i>0&&0==--i&&(t.classList.remove("i-no-scroll"),a)){(0,o.setStyle)(n,"right","0px");let e=0;e=n?(l=(0,o.getContentWidth)(n),r()?0:s()?45:Math.min(Math.max(l,45),450)):0,t.scrollHeight<=t.clientHeight&&(e-=(0,o.getScrollbarWidth)()),t.style.paddingRight=(e<0?0:e)+"px",a=!1}var l}},651674:(e,t,n)=>{"use strict";n.d(t,{createReactRoot:()=>d});var o=n(50959),r=n(632227),s=n(904237);const i=(0,o.createContext)({isOnMobileAppPage:()=>!1,isRtl:!1,locale:"en"});var a=n(69111),l=n(431520);const c={iOs:"old",android:"new",old:"old",new:"new",any:"any"};function u(e){const[t]=(0,o.useState)({isOnMobileAppPage:e=>(0,a.isOnMobileAppPage)(c[e]),isRtl:(0,l.isRtl)(),locale:window.locale});return o.createElement(i.Provider,{value:t},e.children)}function d(e,t,n="legacy"){const i=o.createElement(u,null,e);if("modern"===n){const e=(0,s.createRoot)(t);return e.render(i),{render(t){e.render(o.createElement(u,null,t))},unmount(){e.unmount()}}}return r.render(i,t),{render(e){r.render(o.createElement(u,null,e),t)},unmount(){r.unmountComponentAtNode(t)}}}},63192:(e,t,n)=>{"use strict";n.d(t,{DialogsOpenerManager:()=>o,dialogsOpenerManager:()=>r});class o{constructor(){this._storage=new Map}setAsOpened(e,t){this._storage.set(e,t)}setAsClosed(e){this._storage.delete(e)}isOpened(e){
return this._storage.has(e)}getDialogPayload(e){return this._storage.get(e)}}const r=new o},8361:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>o.Portal,PortalContext:()=>o.PortalContext});var o=n(730654)},904237:(e,t,n)=>{"use strict";var o=n(632227);t.createRoot=o.createRoot,o.hydrateRoot},244610:e=>{e.exports={dialog:"dialog-UGdC69sw",dialogInner:"dialogInner-UGdC69sw",titleWrapper:"titleWrapper-UGdC69sw",title:"title-UGdC69sw",infoHint:"infoHint-UGdC69sw",form:"form-UGdC69sw",inputWrapper:"inputWrapper-UGdC69sw",input:"input-UGdC69sw",hint:"hint-UGdC69sw",error:"error-UGdC69sw"}},151300:(e,t,n)=>{"use strict";n.r(t),n.d(t,{showChangeIntervalDialog:()=>b});var o=n(50959),r=n(497754),s=n.n(r),i=n(609838),a=n(654936),l=n(34735),c=n(688401),u=n(59216),d=n(878112),h=n(785508);const p=i.t(null,void 0,n(279290)),m=i.t(null,void 0,n(696941));function f(e){const{className:t,isSecondsEnabled:n}=e;return o.createElement(d.Icon,{icon:h,className:s()("apply-common-tooltip",t),title:n?m:p})}var g=n(989175),v=n(28964);var w=n(244610);const C=8;function R(e){const{initVal:t,selectOnInit:r,onClose:d}=e,h=(0,o.useRef)(null),[p,m]=(0,o.useState)(t.toUpperCase()),R=(0,o.useMemo)((()=>v.Interval.parse(p)),[p]),x=function(e,t){return(0,o.useMemo)((()=>{if(!t.isValid()||!(0,g.intervalIsSupported)(e))return!1;const n=v.Interval.normalize(e);return null!==n&&(0,g.isResolutionMultiplierValid)(n)}),[e,t])}(p,R),y=(0,o.useMemo)((()=>{if(!x)return null;return(0,g.getTranslatedResolutionModel)(R.value()).hint}),[x,R]);return(0,o.useLayoutEffect)((()=>{r?h.current?.select():h.current?.focus()}),[r]),o.createElement(u.PopupDialog,{className:w.dialog,"data-dialog-name":"change-interval-dialog",isOpened:!0,onClickOutside:d,onFocus:function(){h.current?.focus()},onKeyDown:function(e){27===e.keyCode&&d?.()}},o.createElement("div",{className:w.dialogInner},o.createElement("div",{className:w.titleWrapper},o.createElement("div",{className:w.title},i.t(null,void 0,n(102569))),o.createElement(f,{className:w.infoHint,isSecondsEnabled:(0,g.isSecondsEnabled)()})),o.createElement("form",{className:w.form,onSubmit:function(e){e.preventDefault();const t=c.linking.interval.value(),n=v.Interval.normalize(p);n&&t!==n&&x&&c.linking.interval.setValue(n);d?.()}},o.createElement(a.InputControl,{className:s()(w.inputWrapper,l.InputClasses.FontSizeLarge),inputClassName:w.input,type:"text",size:"large",reference:h,value:p,maxLength:C,intent:x?void 0:"danger",onChange:function(e){const{value:t}=e.target;m(t.toUpperCase())}})),x?o.createElement("div",{className:w.hint},y):o.createElement("div",{className:s()(w.hint,w.error)},i.t(null,void 0,n(787859)))))}var x=n(63192),y=n(651674);function b(e){if(x.dialogsOpenerManager.isOpened("ChangeIntervalDialog")||x.dialogsOpenerManager.isOpened("SymbolSearch"))return;const t=document.createElement("div"),{initVal:n,selectOnInit:r,onClose:s}=e,i=o.createElement(R,{initVal:n,selectOnInit:r,onClose:function(){a.unmount(),x.dialogsOpenerManager.setAsClosed("ChangeIntervalDialog"),s?.()}}),a=(0,y.createReactRoot)(i,t)
;x.dialogsOpenerManager.setAsOpened("ChangeIntervalDialog")}},785508:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 8.5h1.5V14"/><circle fill="currentColor" cx="9" cy="5" r="1"/><path stroke="currentColor" d="M16.5 9a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0z"/></svg>'},925931:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>o});let o=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);