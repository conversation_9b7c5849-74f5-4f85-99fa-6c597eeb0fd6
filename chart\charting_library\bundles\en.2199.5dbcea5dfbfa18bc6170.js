(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2199],{348473:e=>{e.exports={en:["Real-time data for {symbolName}"]}},684455:e=>{e.exports={en:["is provided by {exchange} exchange."]}},923230:e=>{e.exports={en:["Fr"]}},930961:e=>{e.exports={en:["Mo"]}},894748:e=>{e.exports={en:["Sa"]}},875005:e=>{e.exports={en:["Su"]}},392578:e=>{e.exports={en:["We"]}},608765:e=>{e.exports={en:["Th"]}},244254:e=>{e.exports={en:["Tu"]}},243206:e=>{e.exports={en:["Could not get Pine source code."]}},565495:e=>{e.exports={en:["Collapse pane"]}},840225:e=>{e.exports={en:["Continuous futures contracts"]}},260657:e=>{e.exports={en:["Contract expired"]}},58796:e=>{e.exports={en:["Cboe One"]}},963245:e=>{e.exports={en:["Change symbol"]}},845639:e=>{e.exports={en:["Chart values"]}},928214:e=>{e.exports={en:["Create a free account"]}},639301:e=>{e.exports={en:["All good things must come to an end - this contract has reached expiry!"]}},29938:e=>{e.exports={en:["All's well — market is open."]}},528896:e=>{e.exports={en:["April"]}},811081:e=>{e.exports={en:["August"]}},810842:e=>{e.exports={en:["Bar change values"]}},570032:e=>{e.exports={en:["Buy real-time data"]}},199452:e=>{e.exports={en:["Go to editor"]}},513930:e=>{e.exports={en:["Double click"]}},178992:e=>{e.exports={en:["Data error"]}},932925:e=>{e.exports={en:["Data is updated once a day."]}},833039:e=>{e.exports={en:["Data is updated once per second, even if there are more updates on the market."]}},543348:e=>{e.exports={en:["Data is delayed"]}},138368:e=>{e.exports={en:["Data on our Basic plan is updated once per second, even if there are more updates on the market."]}},890082:e=>{e.exports={en:["December"]}},419679:e=>{e.exports={en:["Defaulted bond"]}},866260:e=>{e.exports={en:["Delete pane"]}},254602:e=>{e.exports={en:["Delisted"]}},331683:e=>{e.exports={en:["Delisted alert"]}},50035:e=>{e.exports={en:["Derived Data"]}},705805:e=>{e.exports={en:["End of day data"]}},57335:e=>{e.exports={en:["Error"]}},23302:e=>{e.exports={en:["Evening. Market is open for post-market trading."]}},443383:e=>{e.exports={en:["Exchange disclaimer"]}},763538:e=>{e.exports={en:["Exchange timezone"]}},581069:e=>{e.exports={en:["February"]}},605447:e=>{e.exports={en:["Fill out Exchange Agreements"]}},44454:e=>{e.exports={en:["Flag Symbol"]}},922928:e=>{e.exports={en:["Fri"]}},503570:e=>{e.exports={en:["Friday"]}},759269:e=>{e.exports={en:["From our family to yours, wishing you a merry festive season."]}},414181:e=>{e.exports={en:["HKEX and/or any subsidiaries endeavour to ensure the accuracy and reliability of the information provided but do not guarantee its accuracy or reliability and accept no liability (whether in tort or contract or otherwise) for any loss or damage arising from any inaccuracies or omissions."]}},987845:e=>{e.exports={en:["Holiday"]}},675119:e=>{e.exports={en:["Halal symbol"]}},934987:e=>{e.exports={en:["Happy holidays, traders"]}},44036:e=>{e.exports={en:["Indicator arguments"]}},610319:e=>{e.exports={en:["Indicator error"]}},407511:e=>{e.exports={
en:["Indicator titles"]}},151353:e=>{e.exports={en:["Indicator values"]}},448758:e=>{e.exports={en:["Indicators update"]}},195400:e=>{e.exports={en:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"]}},63975:e=>{e.exports={en:["It'll close in {remainingTime}."]}},681509:e=>{e.exports={en:["It'll go to post-market trading in {remainingTime}."]}},758470:e=>{e.exports={en:["It'll open for pre-market trading in {remainingTime}."]}},299822:e=>{e.exports={en:["It'll open in {remainingTime}."]}},266595:e=>{e.exports={en:["It’ll open fully in {remainingTime}."]}},100200:e=>{e.exports={en:["January"]}},206608:e=>{e.exports={en:["July"]}},661487:e=>{e.exports={en:["June"]}},991006:e=>{e.exports={en:["One update per second"]}},579744:e=>{e.exports={en:["Oops. Something has gone wrong with one or more of your indicators. Please expand the indicator list for more details."]}},964730:e=>{e.exports={en:["Oops. Something has gone wrong with one or more of your indicators. Resize the panes to see the details."]}},284937:e=>{e.exports={en:["Obligation not paid. Issuer payments are over a month late, this is due to coupon or principal debt."]}},137997:e=>{e.exports={en:["October"]}},825765:e=>{e.exports={en:["Open market status"]}},172723:e=>{e.exports={en:["Opened in detached editor"]}},439664:e=>{e.exports={en:["Opened in editor"]}},672423:e=>{e.exports={en:["Last day change values"]}},801356:e=>{e.exports={en:["Let it snow"]}},327741:e=>{e.exports={en:["Learn more"]}},274079:e=>{e.exports={en:["Move pane down"]}},407310:e=>{e.exports={en:["Move pane up"]}},837150:e=>{e.exports={en:["Mon"]}},419573:e=>{e.exports={en:["Monday"]}},437117:e=>{e.exports={en:["More"]}},765420:e=>{e.exports={en:["Morning. Market is open for pre-market trading."]}},461206:e=>{e.exports={en:["Maximize chart"]}},90165:e=>{e.exports={en:["Maximize pane"]}},125734:e=>{e.exports={en:["May"]}},394670:e=>{e.exports={en:["Manage panes"]}},193878:e=>{e.exports={en:["March"]}},241410:e=>{e.exports={en:["Market open"]}},762464:e=>{e.exports={en:["Market closed"]}},741392:e=>{e.exports={en:["Market is currently on holiday. Lucky them."]}},604607:e=>{e.exports={en:["November"]}},787142:e=>{e.exports={en:["Source code"]}},232273:e=>{e.exports={en:["Sat"]}},130348:e=>{e.exports={en:["Saturday"]}},590761:e=>{e.exports={en:["Scroll to the left"]}},983040:e=>{e.exports={en:["Scroll to the most recent bar"]}},825131:e=>{e.exports={en:["Scroll to the right"]}},632179:e=>{e.exports={en:["September"]}},685786:e=>{e.exports={en:["Show Object Tree"]}},974759:e=>{e.exports={en:["Show interval settings"]}},586158:e=>{e.exports={en:["Study Error"]}},396341:e=>{e.exports={en:["Stop the snow"]}},377493:e=>{e.exports={en:["Sun"]}},661480:e=>{e.exports={en:["Sunday"]}},923079:e=>{e.exports={en:["Symbol price source"]}},14771:e=>{e.exports={en:["Symbol title"]}},871847:e=>{e.exports={
en:["Synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."]}},944138:e=>{e.exports={en:["Synthetic symbol"]}},673897:e=>{e.exports={en:["Post-market"]}},23998:e=>{e.exports={en:["Paid plans feature faster data updates."]}},236018:e=>{e.exports={en:["Pre-market"]}},994972:e=>{e.exports={en:["Primary listing"]}},920987:e=>{e.exports={en:["Real-time data for this symbol is not supported right now. We may support it in the future."]}},131539:e=>{e.exports={en:["Real-time data for {symbolName} is provided by {exchange} exchange."]}},631142:e=>{e.exports={en:["Restore chart"]}},512486:e=>{e.exports={en:["Restore pane"]}},711532:e=>{e.exports={en:["Wed"]}},894226:e=>{e.exports={en:["Wednesday"]}},807281:e=>{e.exports={en:["To get real-time data for {description}, please buy the real-time data package."]}},771388:e=>{e.exports={en:["Thu"]}},879137:e=>{e.exports={en:["Thursday"]}},295246:e=>{e.exports={en:["The main, or first, stock exchange where a company's stock is listed and traded."]}},925608:e=>{e.exports={en:["The source code of this script version is open in the Pine Editor."]}},733161:e=>{e.exports={en:["The source code of this script version is open in the detached Pine Editor."]}},624669:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."]}},352668:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."]}},484484:e=>{e.exports={en:['This is a shariah-compliant bond, often referred to as a "sukuk," meaning that it complies with Islamic law that prohibits interest. Unlike conventional bonds, which involve paying interest, sukuk represent ownership in an underlying asset or project and investors earn returns based on profit-sharing or rental income.']}},667607:e=>{e.exports={en:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."]}},983556:e=>{e.exports={en:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."]}},344492:e=>{e.exports={en:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."]}},619481:e=>{e.exports={en:["This refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."]}},453272:e=>{e.exports={en:["This symbol doesn't exist, please pick another one."]}},90589:e=>{e.exports={en:["This symbol is calculated by TradingView using the rate from other exchanges."]}},452176:e=>{e.exports={en:["Time for a walk — this market is closed."]}},811916:e=>{
e.exports={en:["Tue"]}},682160:e=>{e.exports={en:["Tuesday"]}},13865:e=>{e.exports={en:["Unflag Symbol"]}},937644:e=>{e.exports={en:["Volume"]}},958744:e=>{e.exports={en:["You need to update one or more of your indicators. Check the indicator list for details."]}},592663:e=>{e.exports={en:["You need to update one or more of your indicators. Resize the panes or the window to see the details."]}},297038:e=>{e.exports={en:["Zoom in"]}},188710:e=>{e.exports={en:["Zoom out"]}},196227:e=>{e.exports={en:["change open market status visibility"]}},927426:e=>{e.exports={en:["change bar change visibility"]}},479637:e=>{e.exports={en:["change chart values visibility"]}},463050:e=>{e.exports={en:["change indicator titles visibility"]}},749583:e=>{e.exports={en:["change indicator values visibility"]}},878310:e=>{e.exports={en:["change indicator arguments visibility"]}},66307:e=>{e.exports={en:["change last day change visibility"]}},288167:e=>{e.exports={en:["change symbol description visibility"]}},412050:e=>{e.exports={en:["change symbol field visibility"]}},796201:e=>{e.exports={en:["change volume values visibility"]}},59938:e=>{e.exports={en:["less than 1 minute"]}},351382:e=>{e.exports={en:["show {title}"]}},451320:e=>{e.exports={en:["{days} and {hours}"]}},655154:e=>{e.exports={en:["{exchange} by {originalExchange}"]}},283187:e=>{e.exports={en:["{hours} and {minutes}"]}},651211:e=>{e.exports={en:["{listedExchange} real-time data is available for free to registered users."]}},851931:e=>{e.exports={en:["Data is updated once every {amount} second, even if there are more updates on the market.","Data is updated once every {amount} seconds, even if there are more updates on the market."]}},683978:e=>{e.exports={en:["Data on our Basic plan is updated once every {amount} second, even if there are more updates on the market.","Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."]}},946960:e=>{e.exports={en:["Hide indicator legend","Hide indicators legend"]}},36050:e=>{e.exports={en:["One update every {amount} second","One update every {amount} seconds"]}},436553:e=>{e.exports={en:["Show indicator legend","Show indicators legend"]}},262218:e=>{e.exports={en:["{number} day","{number} days"]}},165463:e=>{e.exports={en:["{number} hour","{number} hours"]}},221730:e=>{e.exports={en:["{number} hour","{number} hours"]}},782796:e=>{e.exports={en:["{number} minute","{number} minutes"]}},532547:e=>{e.exports={en:["{number} minute","{number} minutes"]}},881227:e=>{e.exports={en:["{symbolName} data is delayed by {time} minute because of exchange requirements.","{symbolName} data is delayed by {time} minutes because of exchange requirements."]}}}]);