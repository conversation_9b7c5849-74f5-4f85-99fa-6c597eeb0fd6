(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3005],{497754:(e,t)=>{var r;!function(){"use strict";var s={}.hasOwnProperty;function n(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)&&r.length){var a=n.apply(null,r);a&&e.push(a)}else if("object"===i)for(var o in r)s.call(r,o)&&r[o]&&e.push(o)}}return e.join(" ")}e.exports?(n.default=n,e.exports=n):void 0===(r=function(){return n}.apply(t,[]))||(e.exports=r)}()},146913:e=>{e.exports={wrap:"wrap-wXGVFOC9",wrapWithArrowsOuting:"wrapWithArrowsOuting-wXGVFOC9",wrapOverflow:"wrapOverflow-wXGVFOC9",scrollWrap:"scrollWrap-wXGVFOC9",noScrollBar:"noScrollBar-wXGVFOC9",icon:"icon-wXGVFOC9",scrollLeft:"scrollLeft-wXGVFOC9",scrollRight:"scrollRight-wXGVFOC9",isVisible:"isVisible-wXGVFOC9",iconWrap:"iconWrap-wXGVFOC9",fadeLeft:"fadeLeft-wXGVFOC9",fadeRight:"fadeRight-wXGVFOC9"}},718736:(e,t,r)=>{"use strict";r.d(t,{useFunctionalRefObject:()=>i});var s=r(50959),n=r(855393);function i(e){const t=(0,s.useMemo)((()=>function(e){const t=r=>{e(r),t.current=r};return t.current=null,t}((e=>{o.current(e)}))),[]),r=(0,s.useRef)(null),i=t=>{if(null===t)return a(r.current,t),void(r.current=null);r.current!==e&&(r.current=e,a(r.current,t))},o=(0,s.useRef)(i);return o.current=i,(0,n.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return o.current(t.current),()=>o.current(null)}),[e]),t}function a(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},975228:(e,t,r)=>{"use strict";r.d(t,{hoverMouseEventFilter:()=>i,useAccurateHover:()=>a,useHover:()=>n});var s=r(50959);function n(){const[e,t]=(0,s.useState)(!1);return[e,{onMouseOver:function(e){i(e)&&t(!0)},onMouseOut:function(e){i(e)&&t(!1)}}]}function i(e){return!e.currentTarget.contains(e.relatedTarget)}function a(e){const[t,r]=(0,s.useState)(!1);return(0,s.useEffect)((()=>{const t=t=>{if(null===e.current)return;const s=e.current.contains(t.target);r(s)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},855393:(e,t,r)=>{"use strict";r.d(t,{useIsomorphicLayoutEffect:()=>n});var s=r(50959);function n(e,t){("undefined"==typeof window?s.useEffect:s.useLayoutEffect)(e,t)}},664332:(e,t,r)=>{"use strict";r.d(t,{useResizeObserver:()=>a});var s=r(50959),n=r(855393),i=r(718736);function a(e,t=[]){const{callback:r,ref:a=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),o=(0,s.useRef)(null),l=(0,s.useRef)(r);l.current=r;const c=(0,i.useFunctionalRefObject)(a),u=(0,s.useCallback)((e=>{c(e),null!==o.current&&(o.current.disconnect(),null!==e&&o.current.observe(e))}),[c,o]);return(0,n.useIsomorphicLayoutEffect)((()=>(o.current=new ResizeObserver(((e,t)=>{l.current(e,t)})),c.current&&u(c.current),()=>{o.current?.disconnect()})),[c,...t]),u}},183787:(e,t,r)=>{"use strict";r.d(t,{Icon:()=>n});var s=r(50959);const n=s.forwardRef(((e,t)=>{const{icon:r="",title:n,ariaLabel:i,ariaLabelledby:a,ariaHidden:o,...l}=e,c=!!(n||i||a);return s.createElement("span",{
role:"img",...l,ref:t,"aria-label":i,"aria-labelledby":a,"aria-hidden":o||!c,title:n,dangerouslySetInnerHTML:{__html:r}})}))},878112:(e,t,r)=>{"use strict";r.d(t,{Icon:()=>s.Icon});var s=r(183787)},269842:(e,t,r)=>{"use strict";function s(...e){return t=>{for(const r of e)void 0!==r&&r(t)}}r.d(t,{createSafeMulticastEventHandler:()=>s})},670086:(e,t,r)=>{"use strict";r.d(t,{FragmentMap:()=>n});var s=r(50959);function n(e){if(e.map){return s.Children.toArray(e.children).map(e.map)}return e.children}},438980:(e,t,r)=>{"use strict";r.d(t,{Measure:()=>n});var s=r(664332);function n(e){const{children:t,onResize:r}=e;return t((0,s.useResizeObserver)(r||(()=>{}),[null===r]))}},522224:(e,t,r)=>{"use strict";r.d(t,{hoverMouseEventFilter:()=>s.hoverMouseEventFilter,useAccurateHover:()=>s.useAccurateHover,useHover:()=>s.useHover});var s=r(975228)},906132:(e,t,r)=>{"use strict";var s=r(522134);function n(){}function i(){}i.resetWarningCache=n,e.exports=function(){function e(e,t,r,n,i,a){if(a!==s){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var r={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:n};return r.PropTypes=r,r}},719036:(e,t,r)=>{e.exports=r(906132)()},522134:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},513818:e=>{e.exports={group:"group-MBOVGQRI",separator:"separator-MBOVGQRI",noLeftDecoration:"noLeftDecoration-MBOVGQRI",noRightDecoration:"noRightDecoration-MBOVGQRI",noMinimalWidth:"noMinimalWidth-MBOVGQRI",separatorWrap:"separatorWrap-MBOVGQRI"}},932169:e=>{e.exports={separator:"separator-xVhBjD5m"}},858312:e=>{e.exports={"css-value-header-toolbar-height":"38px",toolbar:"toolbar-qqNP9X6e",isHidden:"isHidden-qqNP9X6e",overflowWrap:"overflowWrap-qqNP9X6e",customButton:"customButton-qqNP9X6e",hover:"hover-qqNP9X6e",clicked:"clicked-qqNP9X6e"}},687730:e=>{e.exports={wrap:"wrap-_psvpUP2",icon:"icon-_psvpUP2"}},208297:e=>{e.exports={"css-value-header-toolbar-height":"38px",innerWrap:"innerWrap-OhqNVIYA",inner:"inner-OhqNVIYA",fake:"fake-OhqNVIYA",fill:"fill-OhqNVIYA",collapse:"collapse-OhqNVIYA",button:"button-OhqNVIYA",iconButton:"iconButton-OhqNVIYA",hidden:"hidden-OhqNVIYA",content:"content-OhqNVIYA",desktopPublish:"desktopPublish-OhqNVIYA",mobilePublish:"mobilePublish-OhqNVIYA"}},333086:(e,t,r)=>{"use strict";var s;function n(e){e.dispatchEvent(new CustomEvent("roving-tabindex:main-element"))}function i(e){e.dispatchEvent(new CustomEvent("roving-tabindex:secondary-element"))}r.d(t,{becomeMainElement:()=>n,becomeSecondaryElement:()=>i}),function(e){e.MainElement="roving-tabindex:main-element",e.SecondaryElement="roving-tabindex:secondary-element"}(s||(s={}))},934084:(e,t,r)=>{"use strict";r.d(t,{INTERVALS:()=>n})
;var s=r(609838);const n=[{name:"",label:s.t(null,{context:"interval"},r(379930))},{name:"H",label:s.t(null,{context:"interval"},r(335157))},{name:"D",label:s.t(null,{context:"interval"},r(723970))},{name:"W",label:s.t(null,{context:"interval"},r(507938))},{name:"M",label:s.t(null,{context:"interval"},r(718193))}]},602069:(e,t,r)=>{"use strict";r.d(t,{Toolbar:()=>d});var s=r(50959),n=r(650151),i=r(269842),a=r(930202),o=r(442092),l=r(333086),c=r(718736),u=r(74336);const d=(0,s.forwardRef)((function(e,t){const{onKeyDown:r,orientation:d,blurOnEscKeydown:h=!0,blurOnClick:m=!0,...p}=e,v=(0,c.useFunctionalRefObject)(t);return(0,s.useLayoutEffect)((()=>{const e=(0,n.ensureNotNull)(v.current),t=()=>{const t=(0,o.queryTabbableElements)(e).sort(o.navigationOrderComparator);if(0===t.length){const[t]=(0,o.queryFocusableElements)(e).sort(o.navigationOrderComparator);if(void 0===t)return;(0,l.becomeMainElement)(t)}if(t.length>1){const[,...e]=t;for(const t of e)(0,l.becomeSecondaryElement)(t)}};return window.addEventListener("keyboard-navigation-activation",t),()=>window.removeEventListener("keyboard-navigation-activation",t)}),[]),s.createElement("div",{...u.MouseClickAutoBlurHandler.attributes(m),...p,role:"toolbar","aria-orientation":d,ref:v,onKeyDown:(0,i.createSafeMulticastEventHandler)((function(e){if(e.defaultPrevented)return;if(!(document.activeElement instanceof HTMLElement))return;const t=(0,a.hashFromEvent)(e);if(h&&27===t)return e.preventDefault(),void document.activeElement.blur();if("vertical"!==d&&37!==t&&39!==t)return;if("vertical"===d&&38!==t&&40!==t)return;const r=e.currentTarget,s=(0,o.queryFocusableElements)(r).sort(o.navigationOrderComparator);if(0===s.length)return;const n=s.indexOf(document.activeElement);if(-1===n)return;e.preventDefault();const i=()=>{const e=(n+s.length-1)%s.length;(0,l.becomeSecondaryElement)(s[n]),(0,l.becomeMainElement)(s[e]),s[e].focus()},c=()=>{const e=(n+s.length+1)%s.length;(0,l.becomeSecondaryElement)(s[n]),(0,l.becomeMainElement)(s[e]),s[e].focus()};switch((0,o.mapKeyCodeToDirection)(t)){case"inlinePrev":"vertical"!==d&&i();break;case"inlineNext":"vertical"!==d&&c();break;case"blockPrev":"vertical"===d&&i();break;case"blockNext":"vertical"===d&&c()}}),r),"data-tooltip-show-on-focus":"true"})}))},385735:(e,t,r)=>{"use strict";r.r(t),r.d(t,{HeaderToolbarRenderer:()=>Fe});var s=r(50959),n=r(632227),i=r(650151),a=r(497754),o=r.n(a),l=r(685459),c=r.n(l),u=r(920057),d=r(283873),h=r(763193),m=r(440891),p=r(559410),v=r(719036),f=r(972535),g=r(670086),S=r(932169);function b(e){return s.createElement("div",{className:o()(S.separator,e.className)})}var y=r(513818);function C(e){const{children:t,className:r,noLeftDecoration:n,noRightDecoration:i,noMinimalWidth:o,onClick:l,removeSeparator:c}=e;return s.createElement(s.Fragment,null,!c&&s.createElement("div",{className:y.separatorWrap},s.createElement(b,{className:y.separator})),s.createElement("div",{className:a(r,y.group,{[y.noMinimalWidth]:o,[y.noLeftDecoration]:n,[y.noRightDecoration]:i}),onClick:l},t))}
var E=r(438980),_=r(609838),w=r(878112),M=r(687730),R=r(245820);const k={text:_.t(null,void 0,r(148161))};function I(e){return s.createElement("div",{className:M.wrap},s.createElement(w.Icon,{className:M.icon,icon:R}),k.text)}var V=r(351329),F=r(176616),O=r(431520),W=r(661380),L=r(146913);const N={isVisibleScrollbar:!0,shouldMeasure:!0,hideButtonsFrom:1};function T(e){return s.createElement("div",{className:a(L.fadeLeft,e.className,{[L.isVisible]:e.isVisible})})}function B(e){return s.createElement("div",{className:a(L.fadeRight,e.className,{[L.isVisible]:e.isVisible})})}function A(e){return s.createElement(x,{...e,className:L.scrollLeft})}function D(e){return s.createElement(x,{...e,className:L.scrollRight})}function x(e){return s.createElement("div",{className:a(e.className,{[L.isVisible]:e.isVisible}),onClick:e.onClick},s.createElement("div",{className:L.iconWrap},s.createElement(w.Icon,{icon:W,className:L.icon})))}const P=function(e=A,t=D,r=T,n=B){var o;return(o=class extends s.PureComponent{constructor(e){super(e),this._scroll=s.createRef(),this._handleScrollLeft=()=>{if(this.props.onScrollButtonClick)return void this.props.onScrollButtonClick("left");const e=this.props.scrollStepSize||this.state.widthWrap-50;this.animateTo(Math.max(0,this.currentPosition()-e))},this._handleScrollRight=()=>{if(this.props.onScrollButtonClick)return void this.props.onScrollButtonClick("right");const e=this.props.scrollStepSize||this.state.widthWrap-50;this.animateTo(Math.min((this.state.widthContent||0)-(this.state.widthWrap||0),this.currentPosition()+e))},this._handleResizeWrap=([e])=>{const t=e.target.getBoundingClientRect();this.props.onMeasureWrap&&this.props.onMeasureWrap(t),this.setState({widthWrap:t.width}),this._checkButtonsVisibility()},this._handleResizeContent=([e])=>{const t=e.target.getBoundingClientRect();this.props.onMeasureContent&&this.props.onMeasureContent(t);const{shouldDecreaseWidthContent:r,buttonsWidthIfDecreasedWidthContent:s}=this.props;r&&s?this.setState({widthContent:t.width+2*s}):this.setState({widthContent:t.width})},this._handleScroll=()=>{const{onScroll:e}=this.props;e&&e(this.currentPosition(),this.isAtLeft(),this.isAtRight()),this._checkButtonsVisibility()},this._checkButtonsVisibility=()=>{const{isVisibleLeftButton:e,isVisibleRightButton:t}=this.state,r=this.isAtLeft(),s=this.isAtRight();r||e?r&&e&&this.setState({isVisibleLeftButton:!1}):this.setState({isVisibleLeftButton:!0}),s||t?s&&t&&this.setState({isVisibleRightButton:!1}):this.setState({isVisibleRightButton:!0})},this.state={widthContent:0,widthWrap:0,isVisibleRightButton:!1,isVisibleLeftButton:!1}}componentDidMount(){this._checkButtonsVisibility()}componentDidUpdate(e,t){t.widthWrap===this.state.widthWrap&&t.widthContent===this.state.widthContent||this._handleScroll()}currentPosition(){return this._scroll.current?(0,O.isRtl)()?(0,O.getLTRScrollLeft)(this._scroll.current):this._scroll.current.scrollLeft:0}isAtLeft(){return!this._isOverflowed()||this.currentPosition()<=(0,i.ensureDefined)(this.props.hideButtonsFrom)}isAtRight(){
return!this._isOverflowed()||this.currentPosition()+this.state.widthWrap>=this.state.widthContent-(0,i.ensureDefined)(this.props.hideButtonsFrom)}animateTo(e,t=F.dur){const r=this._scroll.current;r&&((0,O.isRtl)()&&(e=(0,O.getLTRScrollLeftOffset)(r,e)),t<=0?r.scrollLeft=Math.round(e):(0,V.doAnimate)({onStep(e,t){r.scrollLeft=Math.round(t)},from:r.scrollLeft,to:Math.round(e),easing:F.easingFunc.easeInOutCubic,duration:t}))}render(){const{children:i,isVisibleScrollbar:o,isVisibleFade:l,isVisibleButtons:c,shouldMeasure:u,shouldDecreaseWidthContent:d,buttonsWidthIfDecreasedWidthContent:h,onMouseOver:m,onMouseOut:p,scrollWrapClassName:v,fadeClassName:f}=this.props,{isVisibleRightButton:g,isVisibleLeftButton:S}=this.state,b=d&&h;return s.createElement(E.Measure,{onResize:u?this._handleResizeWrap:null},(d=>s.createElement("div",{className:L.wrapOverflow,onMouseOver:m,onMouseOut:p,ref:d},s.createElement("div",{className:a(L.wrap,b?L.wrapWithArrowsOuting:"")},s.createElement("div",{className:a(L.scrollWrap,v,{[L.noScrollBar]:!o}),onScroll:this._handleScroll,ref:this._scroll},s.createElement(E.Measure,{onResize:u?this._handleResizeContent:null},i)),l&&s.createElement(r,{isVisible:S,className:f}),l&&s.createElement(n,{isVisible:g,className:f}),c&&s.createElement(e,{onClick:this._handleScrollLeft,isVisible:S}),c&&s.createElement(t,{onClick:this._handleScrollRight,isVisible:g})))))}_isOverflowed(){const{widthContent:e,widthWrap:t}=this.state;return e>t}}).defaultProps=N,o}(A,D,T,B);var H,q=r(409174);!function(e){e.SymbolSearch="header-toolbar-symbol-search",e.Intervals="header-toolbar-intervals",e.ChartStyles="header-toolbar-chart-styles",e.Compare="header-toolbar-compare",e.Indicators="header-toolbar-indicators",e.StudyTemplates="header-toolbar-study-templates",e.Dropdown="header-toolbar-dropdown",e.Alerts="header-toolbar-alerts",e.Layouts="header-toolbar-layouts",e.SaveLoad="header-toolbar-save-load",e.UndoRedo="header-toolbar-undo-redo",e.Properties="header-toolbar-properties",e.QuickSearch="header-toolbar-quick-search",e.PublishDesktop="header-toolbar-publish-desktop",e.PublishMobile="header-toolbar-publish-mobile",e.Fullscreen="header-toolbar-fullscreen",e.Screenshot="header-toolbar-screenshot",e.Replay="header-toolbar-replay",e.Financials="header-toolbar-financials"}(H||(H={}));var z=r(522224),G=r(244693),X=r(602069),U=r(208297);const Q=(0,G.registryContextType)(),Y=m.enabled("widget");class j extends s.PureComponent{constructor(e,t){super(e,t),this._handleMouseOver=e=>{(0,z.hoverMouseEventFilter)(e)&&this.setState({isHovered:!0})},this._handleMouseOut=e=>{(0,z.hoverMouseEventFilter)(e)&&this.setState({isHovered:!1})},this._handleInnerResize=([e])=>{const t=e.contentRect.width,{onWidthChange:r}=this.props;r&&r(t)},this._handleMeasureAvailableSpace=e=>{const{onAvailableSpaceChange:t}=this.props;t&&t(e.width)},this._processCustoms=e=>{const{isFake:t,displayMode:r}=this.props,{tools:n}=this.context;return e.map((e=>s.createElement(C,{key:e.id},(e=>{switch(e.type){case"Button":return s.createElement(n.Custom,{...e.params,
isFake:t});case"TradingViewStyledButton":return s.createElement(n.CustomTradingViewStyledButton,{...e.params,className:U.button,displayMode:r});case"Dropdown":return s.createElement(n.Dropdown,{displayMode:r,params:e.params});default:return null}})(e))))},this._fixLastGroup=(e,t,r)=>{if(t===r.length-1&&s.isValidElement(e)&&e.type===C){const t=void 0!==this.context.tools.Publish&&!this.props.readOnly;return s.cloneElement(e,{noRightDecoration:t})}return e},(0,G.validateRegistry)(t,{tools:v.any.isRequired}),this.state={isHovered:!1}}render(){const{tools:e}=this.context,{features:t,displayMode:r,chartSaver:n,studyMarket:i,readOnly:o,saveLoadSyncEmitter:l,leftCustomElements:c,rightCustomElements:u,showScrollbarWhen:d,isFake:h=!1}=this.props,{isHovered:p}=this.state,v=this._processCustoms(c),S=this._processCustoms(u),b=d.includes(r);return s.createElement(X.Toolbar,{className:a(U.inner,{[U.fake]:h}),onContextMenu:q.preventDefaultForContextMenu,"data-is-fake-main-panel":h,"aria-hidden":h},s.createElement(P,{isVisibleFade:f.mobiletouch&&b,isVisibleButtons:!f.mobiletouch&&b&&p,isVisibleScrollbar:!1,shouldMeasure:!h,onMouseOver:this._handleMouseOver,onMouseOut:this._handleMouseOut,onMeasureWrap:this._handleMeasureAvailableSpace},(c=>s.createElement("div",{className:U.content,ref:c,role:"none"},s.createElement(E.Measure,{onResize:h?this._handleInnerResize:null},(c=>s.createElement("div",{className:U.innerWrap,ref:c},s.createElement(g.FragmentMap,{map:this._fixLastGroup},!o&&s.Children.toArray([(e.SymbolSearch||!Y&&e.Compare)&&s.createElement(C,{key:"symbol"},e.SymbolSearch&&s.createElement(e.SymbolSearch,{id:h?void 0:H.SymbolSearch,isActionsVisible:t.allowSymbolSearchSpread}),e.Compare&&s.createElement(e.Compare,{id:h?void 0:H.Compare,className:U.button,displayMode:r})),e.DateRange&&s.createElement(C,{key:"range"},s.createElement(e.DateRange,null)),e.Intervals&&s.createElement(C,{key:"intervals"},s.createElement(e.Intervals,{id:h?void 0:H.Intervals,isShownQuicks:t.allowFavoriting,isFavoritingAllowed:t.allowFavoriting,displayMode:r,isFake:h})),e.Bars&&s.createElement(C,{key:"styles"},s.createElement(e.Bars,{id:h?void 0:H.ChartStyles,isShownQuicks:t.allowFavoriting,isFavoritingAllowed:t.allowFavoriting,displayMode:r,isFake:h})),Y&&e.Compare&&!e.SymbolSearch&&s.createElement(C,{key:"compare"},s.createElement(e.Compare,{id:h?void 0:H.Compare,className:U.button,displayMode:r})),e.Indicators&&s.createElement(C,{key:"indicators"},s.createElement(e.Indicators,{id:h?void 0:H.Indicators,className:U.button,studyMarket:i,displayMode:r}),e.Templates&&s.createElement(e.Templates,{id:h?void 0:H.StudyTemplates,isShownQuicks:t.allowFavoriting,isFavoritingAllowed:t.allowFavoriting,displayMode:r})),e.Alert&&s.createElement(C,{key:"alert"},s.createElement(e.Alert,{id:h?void 0:H.Alerts,className:U.button,displayMode:r}),e.Replay&&s.createElement(e.Replay,{id:h?void 0:H.Replay,className:U.button,displayMode:r})),e.AlertReferral&&!m.enabled("hide_alert_referral_tool")&&s.createElement(C,{key:"alert-referral"},s.createElement(e.AlertReferral,{
className:U.button,displayMode:r})),e.ScalePercentage&&s.createElement(C,{key:"percentage"},s.createElement(e.ScalePercentage,null)),e.ScaleLogarithm&&s.createElement(C,{key:"logarithm"},s.createElement(e.ScaleLogarithm,null)),...v]),function(e){const t=e.findIndex((e=>s.isValidElement(e)&&!!e.key&&-1!==e.key.toString().indexOf("view-only-badge")));return[t].filter((e=>e>=0)).forEach((t=>{e=s.Children.map(e,((e,r)=>{if(s.isValidElement(e)){switch([t-1,t,t+1].indexOf(r)){case 0:const t={noRightDecoration:!0};e=s.cloneElement(e,t);break;case 1:const r={noLeftDecoration:!0,noRightDecoration:!0};e=s.cloneElement(e,r);break;case 2:const n={noLeftDecoration:!0};e=s.cloneElement(e,n)}}return e}))})),e}(s.Children.toArray([o&&s.createElement(C,{key:"view-only-badge",removeSeparator:!0},s.createElement(I,null)),!o&&e.UndoRedo&&s.createElement(C,{key:"undo-redo"},s.createElement(e.UndoRedo,{id:h?void 0:H.UndoRedo})),s.createElement(C,{removeSeparator:!0,key:"gap-1",className:a(U.fill,h&&U.collapse)}),(e.Layout||e.SaveLoad)&&s.createElement(C,{key:"layout",removeSeparator:!0},!o&&e.Layout&&s.createElement(e.Layout,{id:h?void 0:H.Layouts}),e.SaveLoad&&s.createElement(e.SaveLoad,{id:h?void 0:H.SaveLoad,chartSaver:n,isReadOnly:o,displayMode:r,isFake:h,stateSyncEmitter:l})),e.SaveLoadReferral&&s.createElement(C,{key:"save-load-referral"},s.createElement(e.SaveLoadReferral,{isReadOnly:o,displayMode:r})),t.showLaunchInPopupButton&&e.OpenPopup&&!m.enabled("hide_open_popup_button")&&s.createElement(C,{key:"popup"},s.createElement(e.OpenPopup,null)),!o&&(e.Properties||e.Fullscreen||!Y&&e.Screenshot)&&s.createElement(C,{key:"properties"},!o&&e.QuickSearch&&s.createElement(e.QuickSearch,{id:h?void 0:H.QuickSearch,className:U.iconButton}),!o&&e.Properties&&s.createElement(e.Properties,{id:h?void 0:H.Properties,className:U.iconButton}),s.createElement(s.Fragment,null,!o&&e.Fullscreen&&s.createElement(C,{key:"fullscreen",onClick:this._trackFullscreenButtonClick,removeSeparator:!0},s.createElement(e.Fullscreen,{id:h?void 0:H.Fullscreen})),!Y&&e.Screenshot&&s.createElement(e.Screenshot,{id:h?void 0:H.Screenshot,className:U.iconButton}))),Y&&!o&&e.Fullscreen&&s.createElement(C,{key:"fullscreen",onClick:this._trackFullscreenButtonClick,removeSeparator:!0},s.createElement(e.Fullscreen,{id:h?void 0:H.Fullscreen})),Y&&e.Screenshot&&s.createElement(C,{key:"screenshot",removeSeparator:!0},s.createElement(e.Screenshot,{id:h?void 0:H.Screenshot,className:U.iconButton})),!o&&e.Publish&&!m.enabled("hide_publish_button")&&s.createElement(C,{key:"publish",className:U.mobilePublish,removeSeparator:!0},s.createElement(e.Publish,{id:h?void 0:H.PublishMobile})),...S]))))))))),e.Publish&&!o&&!h&&!m.enabled("hide_publish_button")&&s.createElement(e.Publish,{id:H.PublishDesktop,className:U.desktopPublish}))}_trackFullscreenButtonClick(){0}}var K;j.contextType=Q,function(e){e[e.Left=0]="Left",e[e.Self=1]="Self",e[e.Right=2]="Right"}(K||(K={}));var J=r(240534),$=r(891346);class Z extends $.CommonJsonStoreService{constructor(e,t,r=[]){
super(e,t,"FAVORITE_CHART_STYLES_CHANGED","StyleWidget.quicks",r)}}var ee=r(989175),te=r(423614);class re extends $.AbstractJsonStoreService{constructor(e,t,r){super(e,t,"FAVORITE_INTERVALS_CHANGED","IntervalWidget.quicks",r)}_serialize(e){return(0,te.uniq)(e.map(ee.normalizeIntervalString))}_deserialize(e){return(0,te.uniq)((0,ee.convertResolutionsFromSettings)(e).filter(ee.isResolutionMultiplierValid).map(ee.normalizeIntervalString))}}var se=r(688401),ne=r(329452),ie=r(870122),ae=r(717866);class oe extends $.AbstractJsonStoreService{constructor(e,t,r=[]){super(e,t,"CUSTOM_INTERVALS_CHANGED","IntervalWidget.intervals",r)}set(e,t){const r=()=>{super.set(e,t)};e.length,this.get().length,r()}_serialize(e){return(0,te.uniq)(e.map(ee.normalizeIntervalString))}_deserialize(e){return(0,te.uniq)([...(0,ee.convertResolutionsFromSettings)(e).filter(ee.isResolutionMultiplierValid).map(ee.normalizeIntervalString)])}}const le=new oe(ae.TVXWindowEvents,ie);var ce=r(934084);class ue{constructor(e){this._customIntervalsService=le,this._supportedIntervalsMayChange=new ne.Delegate,this._fireSupportedIntervalsMayChange=()=>{this._supportedIntervalsMayChange.fire()},this._resolutionGetter=()=>[],this._resolutionGetter=e,se.linking.supportedResolutions.subscribe(this._fireSupportedIntervalsMayChange),se.linking.range.subscribe(this._fireSupportedIntervalsMayChange),se.linking.seconds.subscribe(this._fireSupportedIntervalsMayChange),se.linking.ticks.subscribe(this._fireSupportedIntervalsMayChange),se.linking.intraday.subscribe(this._fireSupportedIntervalsMayChange),se.linking.dataFrequencyResolution.subscribe(this._fireSupportedIntervalsMayChange)}destroy(){se.linking.supportedResolutions.unsubscribe(this._fireSupportedIntervalsMayChange),se.linking.range.unsubscribe(this._fireSupportedIntervalsMayChange),se.linking.seconds.unsubscribe(this._fireSupportedIntervalsMayChange),se.linking.ticks.unsubscribe(this._fireSupportedIntervalsMayChange),se.linking.intraday.unsubscribe(this._fireSupportedIntervalsMayChange),se.linking.dataFrequencyResolution.unsubscribe(this._fireSupportedIntervalsMayChange)}getDefaultIntervals(){return this._resolutionGetter?this._resolutionGetter().map(ee.normalizeIntervalString):[]}getCustomIntervals(){return this._customIntervalsService.get()}add(e,t,r){if(!this.isValidInterval(e,t))return null;const s=(0,ee.normalizeIntervalString)(`${e}${t}`),n=this.getCustomIntervals();return this._isIntervalDefault(s)||n.includes(s)?null:(this._customIntervalsService.set((0,ee.sortResolutions)([...n,s])),s)}remove(e){this._customIntervalsService.set(this.getCustomIntervals().filter((t=>t!==e)))}isValidInterval(e,t){return(0,ee.isResolutionMultiplierValid)(`${e}${t}`)}isSupportedInterval(e){return(0,ee.isAvailable)(e)}supportedIntervalsMayChange(){return this._supportedIntervalsMayChange}getOnChange(){return this._customIntervalsService.getOnChange()}getPossibleIntervals(){return ce.INTERVALS}getResolutionUtils(){return{getMaxResolutionValue:ee.getMaxResolutionValue,getTranslatedResolutionModel:ee.getTranslatedResolutionModel,
mergeResolutions:ee.mergeResolutions,sortResolutions:ee.sortResolutions}}_isIntervalDefault(e){return this.getDefaultIntervals().includes(e)}}var de=r(186661);function he(e){return e.replace(/(@[^-]+-[^-]+).*$/,"$1")}const me={};let pe=null;class ve{constructor(e=ie){this._favorites=[],this._favoritesChanged=new ne.Delegate,this._settings=e,ae.TVXWindowEvents.on("StudyFavoritesChanged",(e=>{const t=JSON.parse(e);this._loadFromState(t.favorites||[])})),this._settings.onSync.subscribe(this,this._loadFavs),this._loadFavs()}isFav(e){const t=this.favId(e);return-1!==this._findFavIndex(t)}toggleFavorite(e){this.isFav(e)?this.removeFavorite(e):this.addFavorite(e)}addFavorite(e){const t=this.favId(e);this._favorites.push(ge(t)),this._favoritesChanged.fire(t),this._saveFavs()}removeFavorite(e){const t=this.favId(e),r=this._findFavIndex(t);-1!==r&&(this._favorites.splice(r,1),this._favoritesChanged.fire(t)),this._saveFavs()}favId(e){return he(e)}favorites(){return this._favorites}favoritePineIds(){return this._favorites.filter((e=>"pine"===e.type)).map((e=>e.pineId))}favoritesChanged(){return this._favoritesChanged}static getInstance(){return null===pe&&(pe=new ve),pe}static create(e){return new ve(e)}_loadFavs(){const e=this._settings.getJSON("studyMarket.favorites",[]);this._loadFromState(e)}_saveFavs(){const e=this._stateToSave();this._settings.setJSON("studyMarket.favorites",e,{forceFlush:!0}),ae.TVXWindowEvents.emit("StudyFavoritesChanged",JSON.stringify({favorites:e}))}_stateToSave(){return this._favorites.map(fe)}_loadFromState(e){this._favorites=e.map((e=>ge(function(e){return e in me?me[e]:e}(e)))),this._favoritesChanged.fire()}_findFavIndex(e){return this._favorites.findIndex((t=>e===fe(t)))}}function fe(e){return"java"===e.type?e.studyId:e.pineId}function ge(e){return isPineIdString(e)?{type:"pine",pineId:e}:{type:"java",studyId:e}}var Se=r(28964);const be={[Se.ResolutionKind.Ticks]:!1,[Se.ResolutionKind.Seconds]:!1,[Se.ResolutionKind.Minutes]:!1,[Se.SpecialResolutionKind.Hours]:!1,[Se.ResolutionKind.Days]:!1,[Se.ResolutionKind.Range]:!1};class ye extends $.CommonJsonStoreService{constructor(e,t,r=be){super(e,t,"INTERVALS_MENU_VIEW_STATE_CHANGED","IntervalWidget.menu.viewState",r)}isAllowed(e){return Object.keys(be).includes(e)}}const Ce={Area:3,"HLC area":16,Bars:0,Candles:1,"Heiken Ashi":8,"Hollow Candles":9,Line:2,Renko:4,Kagi:5,"Point & figure":6,"Line Break":7,Baseline:10,LineWithMarkers:14,Stepline:15,Columns:13,"High-low":12},Ee=["1","30","60"];function _e(e=[]){let t=e.map((e=>Ce[e]))||[1,4,5,6];return m.enabled("widget")&&(t=[0,1,3]),t}function we(e=[]){return(0,ee.mergeResolutions)(e,m.enabled("star_some_intervals_by_default")?Ee:[])}new re(ae.TVXWindowEvents,ie,we()),new Z(ae.TVXWindowEvents,ie,_e());const Me={tools:v.any.isRequired,isFundamental:v.any,chartApiInstance:v.any,availableTimeFrames:v.any,chartWidgetCollection:v.any,windowMessageService:v.any,favoriteChartStylesService:v.any,favoriteIntervalsService:v.any,intervalService:v.any,recentStudyTemplatesService:v.any,studyTemplates:v.any,
chartChangesWatcher:v.any,saveChartService:v.any,sharingChartService:v.any,loadChartService:v.any,chartWidget:v.any,favoriteScriptsModel:v.any,intervalsMenuViewStateService:v.any,templatesMenuViewStateService:v.any,openGlobalSearch:v.any,snapshotUrl:v.any};var Re=r(441827),ke=r(858312);const Ie=[];class Ve extends s.PureComponent{constructor(e){super(e),this._saveLoadSyncEmitter=new(c()),this._handleFullWidthChange=e=>{this._fullWidth=e,this.setState({measureValid:!1})},this._handleFavoritesWidthChange=e=>{this._favoritesWidth=e,this.setState({measureValid:!1})},this._handleCollapseWidthChange=e=>{this._collapseWidth=e,this.setState({measureValid:!1})},this._handleMeasure=e=>{this.setState({availableWidth:e,measureValid:!1})};const{tools:t,windowMessageService:r,chartWidgetCollection:s,chartApiInstance:n,availableTimeFrames:a,isFundamental:o,favoriteIntervalsService:l,favoriteChartStylesService:d,recentStudyTemplatesService:h,studyTemplates:p,saveChartService:v,sharingChartService:f,loadChartService:g,snapshotUrl:S,openGlobalSearch:b}=e;this._showScrollbarWhen=(0,i.ensureDefined)(e.allowedModes).slice(-1),this._panelWidthChangeHandlers={full:this._handleFullWidthChange,medium:this._handleFavoritesWidthChange,small:this._handleCollapseWidthChange};const{chartChangesWatcher:y}=e;this._chartChangesWatcher=y;const C=_e(this.props.defaultFavoriteStyles);this._favoriteChartStylesService=d||new Z(ae.TVXWindowEvents,ie,C);const E=we(this.props.defaultFavoriteIntervals);this._favoriteIntervalsService=l||new re(ae.TVXWindowEvents,ie,E),this._intervalsMenuViewStateService=new ye(ae.TVXWindowEvents,ie),this._intervalService=new ue(n.defaultResolutions),this._registry={tools:t,isFundamental:o,chartWidgetCollection:s,windowMessageService:r,chartApiInstance:n,availableTimeFrames:a,recentStudyTemplatesService:h,studyTemplates:p,saveChartService:v,sharingChartService:f,loadChartService:g,intervalsMenuViewStateService:this._intervalsMenuViewStateService,favoriteChartStylesService:this._favoriteChartStylesService,favoriteIntervalsService:this._favoriteIntervalsService,intervalService:this._intervalService,chartChangesWatcher:this._chartChangesWatcher,chartWidget:s.activeChartWidget.value(),favoriteScriptsModel:ve.getInstance(),templatesMenuViewStateService:this._templatesMenuVuewStateService,snapshotUrl:S,openGlobalSearch:b},this.state={isVisible:!0,availableWidth:0,displayMode:"full",measureValid:!1,leftCustomElements:[],rightCustomElements:[]},this._readOnly=s.readOnly(),this._features={allowFavoriting:m.enabled("items_favoriting"),showIdeasButton:Boolean(this.props.ideas),showLaunchInPopupButton:Boolean(this.props.popupButton),allowSymbolSearchSpread:m.enabled("header_symbol_search")&&m.enabled("show_spread_operators"),allowToolbarHiding:m.enabled("collapsible_header")},this._setDisplayMode=(0,u.default)(this._setDisplayMode,100),this._negotiateResizer()}componentDidUpdate(e,t){const{isVisible:r,measureValid:s}=this.state;r!==t.isVisible&&(p.emit("toggle_header",r),this._negotiateResizer()),s||this._setDisplayMode()}render(){
const{resizerBridge:e,allowedModes:t,...r}=this.props,{displayMode:n,isVisible:o,leftCustomElements:l,rightCustomElements:c}=this.state,u={features:this._features,readOnly:this._readOnly,isFake:!1,saveLoadSyncEmitter:this._saveLoadSyncEmitter,leftCustomElements:l,rightCustomElements:c,...r},d={...u,isFake:!0,showScrollbarWhen:Ie},h=(0,i.ensureDefined)(t),m=this.props.tools.PublishButtonManager||s.Fragment;return s.createElement(G.RegistryProvider,{value:this._registry,validation:Me},s.createElement(m,null,s.createElement("div",{className:a(ke.toolbar,{[ke.isHidden]:!o}),onClick:this.props.onClick},s.createElement("div",{className:ke.overflowWrap},h.map((e=>s.createElement(j,{key:e,displayMode:e,onWidthChange:this._panelWidthChangeHandlers[e],...d}))),s.createElement(j,{key:"live",showScrollbarWhen:this._showScrollbarWhen,displayMode:n,onAvailableSpaceChange:this._handleMeasure,...u})))))}addButton(e,t){if(!t.useTradingViewStyle)return this._addCustomHTMLButton(e,t.align);this._addCustomTradingViewStyledButton(e,t)}removeButton(e){const{leftCustomElements:t,rightCustomElements:r}=this.state;if((0,d.default)(e))this._removeCustomElementToState(e);else{const s=t=>"element"in t.params&&t.params.element===e,n=t.find(s)?.id??r.find(s)?.id;(0,h.default)(n)||this._removeCustomElementToState(n)}}addDropdown(e,t){const{leftCustomElements:r,rightCustomElements:s}=this.state,n={type:"Dropdown",id:e,params:t};"left"===t.align?this.setState({leftCustomElements:[...r,n]}):this.setState({rightCustomElements:[...s,n]})}updateDropdown(e,t){const r=t=>"Dropdown"===t.type&&t.id===e,s=this.state.leftCustomElements.find(r)||this.state.rightCustomElements.find(r);void 0!==s&&(s.params={...s.params,...t},this.setState({leftCustomElements:this.state.leftCustomElements.slice(),rightCustomElements:this.state.rightCustomElements.slice()}))}removeDropdown(e){const t=t=>"Dropdown"===t.type&&t.id!==e,r=this.state.leftCustomElements.filter(t),s=this.state.rightCustomElements.filter(t);this.setState({leftCustomElements:r,rightCustomElements:s})}_negotiateResizer(){this.props.resizerBridge.negotiateHeight(this.state.isVisible?de.HEADER_TOOLBAR_HEIGHT_EXPANDED:de.HEADER_TOOLBAR_HEIGHT_COLLAPSED)}_setDisplayMode(){const{availableWidth:e}=this.state,{allowedModes:t}=this.props,r={full:this._fullWidth,medium:this._favoritesWidth,small:this._collapseWidth},s=(0,i.ensureDefined)(t);let n=s.map((e=>r[e])).findIndex((t=>e>=t));-1===n&&(n=s.length-1);const a=s[n];this.setState({measureValid:!0,displayMode:a})}_addCustomHTMLButton(e,t="left"){const r=new J.WatchedValue(0),s=(0,Re.parseHtmlElement)(`<div class="apply-common-tooltip ${ke.customButton}">`),n={type:"Button",id:e,params:{key:Number(new Date),element:s,width:r}};return this._addCustomElementToState(t,n),s}_addCustomTradingViewStyledButton(e,t){const r={type:"TradingViewStyledButton",id:e,params:{key:Number(new Date),text:t.text,title:t.title,onClick:t.onClick}};this._addCustomElementToState(t.align,r)}_addCustomElementToState(e,t){const{leftCustomElements:r,rightCustomElements:s}=this.state
;"left"===e?this.setState({leftCustomElements:[...r,t]}):this.setState({rightCustomElements:[...s,t]})}_removeCustomElementToState(e){this.setState({leftCustomElements:this.state.leftCustomElements.filter((t=>t.id!==e)),rightCustomElements:this.state.rightCustomElements.filter((t=>t.id!==e))})}}Ve.defaultProps={allowedModes:["full","medium"]};class Fe{constructor(e,t){this._component=null,this._handleRef=e=>{this._component=e},this._container=e,n.render(s.createElement(Ve,{...t,ref:this._handleRef}),this._container)}destroy(){n.unmountComponentAtNode(this._container)}getComponent(){return(0,i.ensureNotNull)(this._component)}}},244693:(e,t,r)=>{"use strict";r.d(t,{RegistryProvider:()=>l,registryContextType:()=>c,validateRegistry:()=>o});var s=r(50959),n=r(719036),i=r.n(n);const a=s.createContext({});function o(e,t){i().checkPropTypes(t,e,"context","RegistryContext")}function l(e){const{validation:t,value:r}=e;return o(r,t),s.createElement(a.Provider,{value:r},e.children)}function c(){return a}},661380:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 10" width="20" height="10"><path fill="none" stroke="currentColor" stroke-width="1.5" d="M2 1l8 8 8-8"/></svg>'},245820:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M4.56 14a10.05 10.05 0 00.52.91c.41.69 1.04 1.6 1.85 2.5C8.58 19.25 10.95 21 14 21c3.05 0 5.42-1.76 7.07-3.58A17.18 17.18 0 0023.44 14a9.47 9.47 0 00-.52-.91c-.41-.69-1.04-1.6-1.85-2.5C19.42 8.75 17.05 7 14 7c-3.05 0-5.42 1.76-7.07 3.58A17.18 17.18 0 004.56 14zM24 14l.45-.21-.01-.03a7.03 7.03 0 00-.16-.32c-.11-.2-.28-.51-.5-.87-.44-.72-1.1-1.69-1.97-2.65C20.08 7.99 17.45 6 14 6c-3.45 0-6.08 2-7.8 3.92a18.18 18.18 0 00-2.64 3.84v.02h-.01L4 14l-.45-.21-.1.21.1.21L4 14l-.45.21.01.03a5.85 5.85 0 00.16.32c.11.2.28.51.5.87.44.72 1.1 1.69 1.97 2.65C7.92 20.01 10.55 22 14 22c3.45 0 6.08-2 7.8-3.92a18.18 18.18 0 002.64-3.84v-.02h.01L24 14zm0 0l.45.21.1-.21-.1-.21L24 14zm-10-3a3 3 0 100 6 3 3 0 000-6zm-4 3a4 4 0 118 0 4 4 0 01-8 0z"/></svg>'}}]);