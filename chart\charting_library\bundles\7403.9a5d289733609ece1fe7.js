(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7403,9937],{61442:e=>{e.exports={button:"button-PYEOTd6i",disabled:"disabled-PYEOTd6i",hidden:"hidden-PYEOTd6i",icon:"icon-PYEOTd6i",dropped:"dropped-PYEOTd6i"}},845946:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC",iconOnly:"iconOnly-D4RPB3ZC",link:"link-D4RPB3ZC",brand:"brand-D4RPB3ZC",primary:"primary-D4RPB3ZC",secondary:"secondary-D4RPB3ZC",gray:"gray-D4RPB3ZC",green:"green-D4RPB3ZC",red:"red-D4RPB3ZC",black:"black-D4RPB3ZC","black-friday":"black-friday-D4RPB3ZC","cyber-monday":"cyber-monday-D4RPB3ZC",slot:"slot-D4RPB3ZC",xsmall:"xsmall-D4RPB3ZC",withStartSlot:"withStartSlot-D4RPB3ZC",withEndSlot:"withEndSlot-D4RPB3ZC",startSlotWrap:"startSlotWrap-D4RPB3ZC",endSlotWrap:"endSlotWrap-D4RPB3ZC",small:"small-D4RPB3ZC",medium:"medium-D4RPB3ZC",large:"large-D4RPB3ZC",xlarge:"xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC",adjustPosition:"adjustPosition-D4RPB3ZC",firstRow:"firstRow-D4RPB3ZC",firstCol:"firstCol-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC",textWrap:"textWrap-D4RPB3ZC",multilineContent:"multilineContent-D4RPB3ZC",secondaryText:"secondaryText-D4RPB3ZC",primaryText:"primaryText-D4RPB3ZC"}},811362:e=>{e.exports={wrapper:"wrapper-GZajBGIm",input:"input-GZajBGIm",view:"view-GZajBGIm",danger:"danger-GZajBGIm"}},4052:e=>{e.exports={box:"box-ywH2tsV_",noOutline:"noOutline-ywH2tsV_",disabled:"disabled-ywH2tsV_","intent-danger":"intent-danger-ywH2tsV_",checked:"checked-ywH2tsV_",check:"check-ywH2tsV_",icon:"icon-ywH2tsV_",dot:"dot-ywH2tsV_",disableActiveStyles:"disableActiveStyles-ywH2tsV_"}},865592:e=>{e.exports={checkbox:"checkbox-vyj6oJxw",reverse:"reverse-vyj6oJxw",label:"label-vyj6oJxw",baseline:"baseline-vyj6oJxw"}},288276:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh",
"size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},173405:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},667797:e=>{e.exports={menuWrap:"menuWrap-Kq3ruQo8",isMeasuring:"isMeasuring-Kq3ruQo8",scrollWrap:"scrollWrap-Kq3ruQo8",momentumBased:"momentumBased-Kq3ruQo8",menuBox:"menuBox-Kq3ruQo8",isHidden:"isHidden-Kq3ruQo8"}},148982:(e,t,n)=>{"use strict";n.d(t,{Caret:()=>u,CaretButton:()=>d});var r=n(50959),s=n(497754),o=n.n(s),i=n(878112),a=n(602948),l=n(61442);function c(e){const{isDropped:t}=e;return r.createElement(i.Icon,{className:o()(l.icon,t&&l.dropped),icon:a})}function u(e){const{className:t,disabled:n,isDropped:s}=e;return r.createElement("span",{className:o()(l.button,n&&l.disabled,t)},r.createElement(c,{isDropped:s}))}function d(e){const{className:t,tabIndex:n=-1,disabled:s,isDropped:i,...a}=e;return r.createElement("button",{...a,type:"button",tabIndex:n,disabled:s,className:o()(l.button,s&&l.disabled,t)},r.createElement(c,{isDropped:i}))}},373989:(e,t,n)=>{"use strict";function r(e){return"brand"===e?"black":"blue"===e?"brand":e}n.d(t,{renameColors:()=>r})},228837:(e,t,n)=>{"use strict";n.d(t,{SquareButton:()=>b});var r=n(50959),s=n(497754),o=n(331774),i=n(373989),a=n(845946),l=n.n(a);const c="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text apply-common-tooltip";function u(e){const{size:t="medium",variant:n="primary",stretch:r=!1,startSlot:a,endSlot:u,iconOnly:d=!1,className:h,isGrouped:p,cellState:m,disablePositionAdjustment:f=!1,primaryText:g,secondaryText:b,isAnchor:v=!1}=e,x=(0,i.renameColors)(e.color??"brand"),_=function(e){let t="";return 0!==e&&(1&e&&(t=s(t,l()["no-corner-top-left"])),2&e&&(t=s(t,l()["no-corner-top-right"])),4&e&&(t=s(t,l()["no-corner-bottom-right"])),8&e&&(t=s(t,l()["no-corner-bottom-left"]))),t}((0,o.getGroupCellRemoveRoundBorders)(m)),C=d&&(a||u);return s(h,l().button,l()[t],l()[x],l()[n],r&&l().stretch,a&&l().withStartIcon,u&&l().withEndIcon,C&&l().iconOnly,_,p&&l().grouped,p&&!f&&l().adjustPosition,p&&m.isTop&&l().firstRow,p&&m.isLeft&&l().firstCol,g&&b&&l().multilineContent,v&&l().link,c)}function d(e){const{startSlot:t,iconOnly:n,children:o,endSlot:i,primaryText:a,secondaryText:u}=e;if(t&&i&&n)return r.createElement("span",{className:s(l().slot,l().startSlotWrap)},t);const d=n&&(t??i),h=!t&&!i&&!n&&!o&&a&&u;return r.createElement(r.Fragment,null,t&&r.createElement("span",{className:s(l().slot,l().startSlotWrap)},t),o&&!d&&r.createElement("span",{className:l().content},o),i&&r.createElement("span",{
className:s(l().slot,l().endSlotWrap)},i),h&&!d&&function(e){return e.primaryText&&e.secondaryText&&r.createElement("div",{className:s(l().textWrap,c)},r.createElement("span",{className:l().primaryText}," ",e.primaryText," "),"string"==typeof e.secondaryText?r.createElement("span",{className:l().secondaryText}," ",e.secondaryText," "):r.createElement("span",{className:l().secondaryText},r.createElement("span",null,e.secondaryText.firstLine),r.createElement("span",null,e.secondaryText.secondLine)))}(e))}var h=n(601198),p=n(380327),m=n(800417);function f(e,t){return n=>{if(t)return n.preventDefault(),void n.stopPropagation();e?.(n)}}function g(e){const{className:t,color:n,variant:r,size:s,stretch:o,animated:i,iconOnly:a,startSlot:l,endSlot:c,primaryText:u,secondaryText:d,...h}=e;return{...h,...(0,m.filterDataProps)(e),...(0,m.filterAriaProps)(e)}}function b(e){const{reference:t,tooltipText:n,disabled:s,onClick:o,onMouseOver:i,onMouseOut:a,onMouseDown:l,...c}=e,{isGrouped:m,cellState:b,disablePositionAdjustment:v}=(0,r.useContext)(p.ControlGroupContext),x=u({...c,isGrouped:m,cellState:b,disablePositionAdjustment:v}),_=n??(e.primaryText?[e.primaryText,e.secondaryText].join(" "):(0,h.getTextForTooltip)(e.children));return r.createElement("button",{...g(c),"aria-disabled":s,tabIndex:e.tabIndex??(s?-1:0),className:x,ref:t,onClick:f(o,s),onMouseDown:f(l,s),"data-overflow-tooltip-text":_},r.createElement(d,{...c}))}n(15378)},408323:(e,t,n)=>{"use strict";n.d(t,{CheckboxInput:()=>c});var r=n(50959),s=n(497754),o=n(800417),i=n(611031),a=n(811362),l=n.n(a);function c(e){const t=s(l().wrapper,e.className);return r.createElement("span",{className:t,title:e.title,style:e.style},r.createElement("input",{id:e.id,tabIndex:e.tabIndex,className:s(e.intent&&l()[e.intent],l().input),type:"checkbox",name:e.name,checked:e.checked,disabled:e.disabled,value:e.value,autoFocus:e.autoFocus,role:e.role,onChange:function(){e.onChange?.(e.value)},ref:e.reference,"aria-required":e["aria-required"],"aria-describedby":e["aria-describedby"],"aria-invalid":e["aria-invalid"],...(0,o.filterDataProps)(e)}),r.createElement(i.CheckboxView,{className:l().view,indeterminate:e.indeterminate,checked:e.checked,disabled:e.disabled,intent:e.intent,tabIndex:e.tabIndex}))}},611031:(e,t,n)=>{"use strict";n.d(t,{CheckboxView:()=>c});var r=n(50959),s=n(497754),o=n(878112),i=n(465890),a=n(4052),l=n.n(a);function c(e){const{indeterminate:t,checked:n,tabIndex:a,className:c,disabled:u,disableActiveStyles:d,intent:h,hideIcon:p,...m}=e,f=t||!n||p?"":i,g=s(l().box,l()[`intent-${h}`],!t&&l().check,!!t&&l().dot,-1===a&&l().noOutline,c,n&&l().checked,u&&l().disabled,d&&l().disableActiveStyles);return r.createElement("span",{className:g,...m},r.createElement(o.Icon,{icon:f,className:l().icon}))}},302946:(e,t,n)=>{"use strict";n.d(t,{Checkbox:()=>c,CheckboxView:()=>u.CheckboxView});var r=n(50959),s=n(497754),o=n(230789),i=n(408323),a=n(865592),l=n.n(a);class c extends r.PureComponent{render(){
const{inputClassName:e,labelClassName:t,...n}=this.props,o=s(this.props.className,l().checkbox,{[l().reverse]:Boolean(this.props.labelPositionReverse),[l().baseline]:Boolean(this.props.labelAlignBaseline)}),a=s(l().label,t,{[l().disabled]:this.props.disabled});let c=null;return this.props.label&&(c=r.createElement("span",{className:a,title:this.props.title},this.props.label)),r.createElement("label",{className:o},r.createElement(i.CheckboxInput,{...n,className:e}),c)}}c.defaultProps={value:"on"};(0,o.makeSwitchGroupItem)(c);var u=n(611031)},331774:(e,t,n)=>{"use strict";function r(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>r})},34735:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>v,InputClasses:()=>f});var r=n(50959),s=n(497754),o=n(650151),i=n(525388),a=n(800417),l=n(380327),c=n(331774);var u=n(288276),d=n.n(u);function h(e){let t="";return 0!==e&&(1&e&&(t=s(t,d()["no-corner-top-left"])),2&e&&(t=s(t,d()["no-corner-top-right"])),4&e&&(t=s(t,d()["no-corner-bottom-right"])),8&e&&(t=s(t,d()["no-corner-bottom-left"]))),t}function p(e,t,n,r){const{removeRoundBorder:o,className:i,intent:a="default",borderStyle:l="thin",size:u,highlight:p,disabled:m,readonly:f,stretch:g,noReadonlyStyles:b,isFocused:v}=e,x=h(o??(0,c.getGroupCellRemoveRoundBorders)(n));return s(d().container,d()[`container-${u}`],d()[`intent-${a}`],d()[`border-${l}`],u&&d()[`size-${u}`],x,p&&d()["with-highlight"],m&&d().disabled,f&&!b&&d().readonly,v&&d().focused,g&&d().stretch,t&&d().grouped,!r&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],i)}function m(e,t,n){const{highlight:r,highlightRemoveRoundBorder:o}=e;if(!r)return d().highlight;const i=h(o??(0,c.getGroupCellRemoveRoundBorders)(t));return s(d().highlight,d().shown,d()[`size-${n}`],i)}const f={FontSizeMedium:(0,o.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,o.ensureDefined)(d()["font-size-large"])},g={passive:!1};function b(e,t){const{style:n,id:s,role:o,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:f,onMouseUp:b,onKeyDown:v,onClick:x,tabIndex:_,startSlot:C,middleSlot:y,endSlot:w,onWheel:R,onWheelNoPassive:P=null,size:D,tag:S="span",type:N}=e,{isGrouped:W,cellState:E,disablePositionAdjustment:T=!1}=(0,r.useContext)(l.ControlGroupContext),M=function(e,t=null,n){const s=(0,r.useRef)(null),o=(0,r.useRef)(null),i=(0,r.useCallback)((()=>{if(null===s.current||null===o.current)return;const[e,t,n]=o.current;null!==t&&s.current.addEventListener(e,t,n)}),[]),a=(0,r.useCallback)((()=>{if(null===s.current||null===o.current)return;const[e,t,n]=o.current;null!==t&&s.current.removeEventListener(e,t,n)}),[]),l=(0,r.useCallback)((e=>{a(),s.current=e,i()}),[]);return(0,r.useEffect)((()=>(o.current=[e,t,n],i(),a)),[e,t,n]),l}("wheel",P,g),B=S;return r.createElement(B,{type:N,style:n,id:s,role:o,className:p(e,W,E,T),tabIndex:_,ref:(0,i.useMergedRefs)([t,M]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:f,onMouseUp:b,
onKeyDown:v,onClick:x,onWheel:R,...(0,a.filterDataProps)(e),...(0,a.filterAriaProps)(e)},C,y,w,r.createElement("span",{className:m(e,E,D)}))}b.displayName="ControlSkeleton";const v=r.forwardRef(b)},102691:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>d,BeforeSlot:()=>a,EndSlot:()=>u,MiddleSlot:()=>c,StartSlot:()=>l});var r=n(50959),s=n(497754),o=n(173405),i=n.n(o);function a(e){const{className:t,children:n}=e;return r.createElement("span",{className:s(i()["before-slot"],t)},n)}function l(e){const{className:t,interactive:n=!0,icon:o=!1,children:a}=e;return r.createElement("span",{className:s(i()["inner-slot"],n&&i().interactive,o&&i().icon,t)},a)}function c(e){const{className:t,children:n}=e;return r.createElement("span",{className:s(i()["inner-slot"],i()["inner-middle-slot"],t)},n)}function u(e){const{className:t,interactive:n=!0,icon:o=!1,children:a}=e;return r.createElement("span",{className:s(i()["inner-slot"],n&&i().interactive,o&&i().icon,t)},a)}function d(e){const{className:t,children:n}=e;return r.createElement("span",{className:s(i()["after-slot"],t)},n)}},975228:(e,t,n)=>{"use strict";n.d(t,{hoverMouseEventFilter:()=>o,useAccurateHover:()=>i,useHover:()=>s});var r=n(50959);function s(){const[e,t]=(0,r.useState)(!1);return[e,{onMouseOver:function(e){o(e)&&t(!0)},onMouseOut:function(e){o(e)&&t(!1)}}]}function o(e){return!e.currentTarget.contains(e.relatedTarget)}function i(e){const[t,n]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{const t=t=>{if(null===e.current)return;const r=e.current.contains(t.target);n(r)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},908783:(e,t,n)=>{"use strict";n.d(t,{useOutsideEvent:()=>i});var r=n(50959),s=n(855393),o=n(778199);function i(e){const{click:t,mouseDown:n,touchEnd:i,touchStart:a,handler:l,reference:c}=e,u=(0,r.useRef)(null),d=(0,r.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,s.useIsomorphicLayoutEffect)((()=>{const e={click:t,mouseDown:n,touchEnd:i,touchStart:a},r=c?c.current:u.current;return(0,o.addOutsideEventListener)(d.current,r,l,document,e)}),[t,n,i,a,l]),c||u}},823030:(e,t,n)=>{"use strict";n.d(t,{SubmenuContext:()=>s,SubmenuHandler:()=>o});var r=n(50959);const s=r.createContext(null);function o(e){const[t,n]=(0,r.useState)(null),o=(0,r.useRef)(null),i=(0,r.useRef)(new Map);return(0,r.useEffect)((()=>()=>{null!==o.current&&clearTimeout(o.current)}),[]),r.createElement(s.Provider,{value:{current:t,setCurrent:function(e){null!==o.current&&(clearTimeout(o.current),o.current=null);null===t?n(e):o.current=setTimeout((()=>{o.current=null,n(e)}),100)},registerSubmenu:function(e,t){return i.current.set(e,t),()=>{i.current.delete(e)}},isSubmenuNode:function(e){return Array.from(i.current.values()).some((t=>t(e)))}}},e.children)}},730654:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>c,PortalContext:()=>u});var r=n(50959),s=n(632227),o=n(925931),i=n(801808),a=n(481564),l=n(682925);class c extends r.PureComponent{constructor(){super(...arguments),this._uuid=(0,o.nanoid)()}
componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"";const t=this.props.className;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE,"true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),s.createPortal(r.createElement(u.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,i.getRootOverlapManager)():this.context}}c.contextType=l.SlotContext;const u=r.createContext(null)},682925:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>s,SlotContext:()=>o});var r=n(50959);class s extends r.Component{shouldComponentUpdate(){return!1}render(){return r.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const o=r.createContext(null)},230789:(e,t,n)=>{"use strict";n.d(t,{SwitchGroup:()=>i,makeSwitchGroupItem:()=>a});var r=n(50959),s=n(755883);const o=(0,r.createContext)({getName:()=>"",getValues:()=>[],getOnChange:()=>s.default,subscribe:s.default,unsubscribe:s.default});class i extends r.PureComponent{constructor(e){super(e),this._subscriptions=new Set,this._getName=()=>this.props.name,this._getValues=()=>this.props.values,this._getOnChange=()=>this.props.onChange,this._subscribe=e=>{this._subscriptions.add(e)},this._unsubscribe=e=>{this._subscriptions.delete(e)},this.state={switchGroupContext:{getName:this._getName,getValues:this._getValues,getOnChange:this._getOnChange,subscribe:this._subscribe,unsubscribe:this._unsubscribe}}}render(){return r.createElement(o.Provider,{value:this.state.switchGroupContext},this.props.children)}componentDidUpdate(e){this._notify(this._getUpdates(this.props.values,e.values))}_notify(e){this._subscriptions.forEach((t=>t(e)))}_getUpdates(e,t){return[...t,...e].filter((n=>t.includes(n)?!e.includes(n):e.includes(n)))}}function a(e){var t;return t=class extends r.PureComponent{constructor(){super(...arguments),this._onChange=e=>{this.context.getOnChange()(e)},this._onUpdate=e=>{e.includes(this.props.value)&&this.forceUpdate()}}componentDidMount(){this.context.subscribe(this._onUpdate)}render(){return r.createElement(e,{...this.props,name:this._getName(),onChange:this._onChange,checked:this._isChecked()})}componentWillUnmount(){this.context.unsubscribe(this._onUpdate)}_getName(){return this.context.getName()}_isChecked(){return this.context.getValues().includes(this.props.value)}},t.contextType=o,t}},801808:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>i,getRootOverlapManager:()=>l});var r=n(650151),s=n(481564);class o{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){
return this._storage.includes(e)}getItems(){return this._storage}}class i{constructor(e=document){this._storage=new o,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e);if(void 0!==n)return n;this.registerWindow(e);const r=this._document.createElement("div");if(r.style.position=t.position,r.style.zIndex=this._index.toString(),r.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(r);else if(t.index<=0)this._container.insertBefore(r,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(r,e)}}else"reverse"===t.direction?this._container.insertBefore(r,this._container.firstChild):this._container.appendChild(r);return this._windows.set(e,r),++this._index,r}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveLastWindowToTop(){const e=this._storage.getItems(),t=e[e.length-1];t&&this.moveToTop(t)}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,n)=>{e.hasAttribute(s.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(s.FOCUS_TRAP_DATA_ATTRIBUTE,e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const a=new WeakMap;function l(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,r.ensureDefined)(a.get(t));{const t=new i(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return a.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}var c;!function(e){e[e.BaseZindex=150]="BaseZindex"}(c||(c={}))},285089:(e,t,n)=>{"use strict";n.d(t,{setFixedBodyState:()=>l});var r=n(735922);const s=()=>!window.matchMedia("(min-width: 768px)").matches,o=()=>!window.matchMedia("(min-width: 1280px)").matches;let i=0,a=!1;function l(e){const{body:t}=document,n=t.querySelector(".widgetbar-wrap");if(e&&1==++i){const e=(0,r.getCSSProperty)(t,"overflow"),s=(0,r.getCSSPropertyNumericValue)(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&((0,r.setStyle)(n,"right",`${(0,r.getScrollbarWidth)()}px`),t.style.paddingRight=`${s+(0,r.getScrollbarWidth)()}px`,a=!0),t.classList.add("i-no-scroll")}else if(!e&&i>0&&0==--i&&(t.classList.remove("i-no-scroll"),a)){(0,r.setStyle)(n,"right","0px");let e=0;e=n?(l=(0,r.getContentWidth)(n),
s()?0:o()?45:Math.min(Math.max(l,45),450)):0,t.scrollHeight<=t.clientHeight&&(e-=(0,r.getScrollbarWidth)()),t.style.paddingRight=(e<0?0:e)+"px",a=!1}var l}},522224:(e,t,n)=>{"use strict";n.d(t,{hoverMouseEventFilter:()=>r.hoverMouseEventFilter,useAccurateHover:()=>r.useAccurateHover,useHover:()=>r.useHover});var r=n(975228)},930052:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>s});var r=n(50959);class s extends r.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addEventListener("change",this._handleChange)}_unsubscribe(e){e.removeEventListener("change",this._handleChange)}}},230553:(e,t,n)=>{"use strict";n.d(t,{MenuContext:()=>r});const r=n(50959).createContext(null)},510618:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_MENU_THEME:()=>g,Menu:()=>v});var r=n(50959),s=n(497754),o=n.n(s),i=n(650151),a=n(822960),l=n(409174),c=n(753327),u=n(370981),d=n(801808),h=n(926032),p=n(823030),m=n(230553),f=n(667797);const g=f;var b;!function(e){e[e.IndentFromWindow=0]="IndentFromWindow"}(b||(b={}));class v extends r.PureComponent{constructor(e){super(e),this._containerRef=null,this._scrollWrapRef=null,this._raf=null,this._scrollRaf=null,this._scrollTimeout=void 0,this._manager=new d.OverlapManager,this._hotkeys=null,this._scroll=0,this._handleContainerRef=e=>{this._containerRef=e,this.props.reference&&("function"==typeof this.props.reference&&this.props.reference(e),"object"==typeof this.props.reference&&(this.props.reference.current=e))},this._handleScrollWrapRef=e=>{this._scrollWrapRef=e,"function"==typeof this.props.scrollWrapReference&&this.props.scrollWrapReference(e),"object"==typeof this.props.scrollWrapReference&&(this.props.scrollWrapReference.current=e)},this._handleCustomRemeasureDelegate=()=>{this._resizeForced(),this._handleMeasure()},this._handleMeasure=({callback:e,forceRecalcPosition:t}={})=>{if(this.state.isMeasureValid&&!t)return;const{position:n}=this.props,r=(0,i.ensureNotNull)(this._containerRef);let s=r.getBoundingClientRect();const o=document.documentElement.clientHeight,l=document.documentElement.clientWidth,c=this.props.closeOnScrollOutsideOffset??0;let u=o-0-c;const d=s.height>u;if(d){(0,i.ensureNotNull)(this._scrollWrapRef).style.overflowY="scroll",s=r.getBoundingClientRect()}const{width:h,height:p}=s,m="function"==typeof n?n({contentWidth:h,contentHeight:p,availableWidth:l,availableHeight:o}):n,f=m?.indentFromWindow?.left??0,g=l-(m.overrideWidth??h)-(m?.indentFromWindow?.right??0),b=(0,a.clamp)(m.x,f,Math.max(f,g)),v=(m?.indentFromWindow?.top??0)+c,x=o-(m.overrideHeight??p)-(m?.indentFromWindow?.bottom??0);let _=(0,
a.clamp)(m.y,v,Math.max(v,x));if(m.forbidCorrectYCoord&&_<m.y&&(u-=m.y-_,_=m.y),t&&void 0!==this.props.closeOnScrollOutsideOffset&&m.y<=this.props.closeOnScrollOutsideOffset)return void this._handleGlobalClose(!0);const C=m.overrideHeight??(d?u:void 0);this.setState({appearingMenuHeight:t?this.state.appearingMenuHeight:C,appearingMenuWidth:t?this.state.appearingMenuWidth:m.overrideWidth,appearingPosition:{x:b,y:_},isMeasureValid:!0},(()=>{this.props.doNotRestorePosition||this._restoreScrollPosition(),e&&e()}))},this._restoreScrollPosition=()=>{const e=document.activeElement,t=(0,i.ensureNotNull)(this._containerRef);if(null!==e&&t.contains(e))try{e.scrollIntoView()}catch(e){}else(0,i.ensureNotNull)(this._scrollWrapRef).scrollTop=this._scroll},this._resizeForced=()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0})},this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null})))},this._handleGlobalClose=e=>{this.props.onClose(e)},this._handleSlot=e=>{this._manager.setContainer(e)},this._handleScroll=()=>{this._scroll=(0,i.ensureNotNull)(this._scrollWrapRef).scrollTop},this._handleScrollOutsideEnd=()=>{clearTimeout(this._scrollTimeout),this._scrollTimeout=setTimeout((()=>{this._handleMeasure({forceRecalcPosition:!0})}),80)},this._handleScrollOutside=e=>{e.target!==this._scrollWrapRef&&(this._handleScrollOutsideEnd(),null===this._scrollRaf&&(this._scrollRaf=requestAnimationFrame((()=>{this._handleMeasure({forceRecalcPosition:!0}),this._scrollRaf=null}))))},this.state={}}componentDidMount(){this._handleMeasure({callback:this.props.onOpen});const{customCloseDelegate:e=u.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.subscribe(this,this._handleGlobalClose),t?.subscribe(null,this._handleCustomRemeasureDelegate),window.addEventListener("resize",this._resize);const n=null!==this.context;this._hotkeys||n||(this._hotkeys=h.createGroup({desc:"Popup menu"}),this._hotkeys.add({desc:"Close",hotkey:27,handler:()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleGlobalClose()}})),this.props.repositionOnScroll&&window.addEventListener("scroll",this._handleScrollOutside,{capture:!0})}componentDidUpdate(){this._handleMeasure()}componentWillUnmount(){const{customCloseDelegate:e=u.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.unsubscribe(this,this._handleGlobalClose),t?.unsubscribe(null,this._handleCustomRemeasureDelegate),window.removeEventListener("resize",this._resize),window.removeEventListener("scroll",this._handleScrollOutside,{capture:!0}),this._hotkeys&&(this._hotkeys.destroy(),this._hotkeys=null),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),null!==this._scrollRaf&&(cancelAnimationFrame(this._scrollRaf),this._scrollRaf=null),this._scrollTimeout&&clearTimeout(this._scrollTimeout)}render(){
const{id:e,role:t,"aria-label":n,"aria-labelledby":s,"aria-activedescendant":i,"aria-hidden":a,"aria-describedby":u,"aria-invalid":d,children:h,minWidth:g,theme:b=f,className:v,maxHeight:_,onMouseOver:C,onMouseOut:y,onKeyDown:w,onFocus:R,onBlur:P}=this.props,{appearingMenuHeight:D,appearingMenuWidth:S,appearingPosition:N,isMeasureValid:W}=this.state,E={"--ui-kit-menu-max-width":`${N&&N.x}px`,maxWidth:"calc(100vw - var(--ui-kit-menu-max-width) - 6px)"};return r.createElement(m.MenuContext.Provider,{value:this},r.createElement(p.SubmenuHandler,null,r.createElement(c.SlotContext.Provider,{value:this._manager},r.createElement("div",{id:e,role:t,"aria-label":n,"aria-labelledby":s,"aria-activedescendant":i,"aria-hidden":a,"aria-describedby":u,"aria-invalid":d,className:o()(v,b.menuWrap,!W&&b.isMeasuring),style:{height:D,left:N&&N.x,minWidth:g,position:"fixed",top:N&&N.y,width:S,...this.props.limitMaxWidth&&E},"data-name":this.props["data-name"],"data-tooltip-show-on-focus":this.props["data-tooltip-show-on-focus"],ref:this._handleContainerRef,onScrollCapture:this.props.onScroll,onContextMenu:l.preventDefaultForContextMenu,tabIndex:this.props.tabIndex,onMouseOver:C,onMouseOut:y,onKeyDown:w,onFocus:R,onBlur:P},r.createElement("div",{className:o()(b.scrollWrap,!this.props.noMomentumBasedScroll&&b.momentumBased),style:{overflowY:void 0!==D?"scroll":"auto",maxHeight:_},onScrollCapture:this._handleScroll,ref:this._handleScrollWrapRef},r.createElement(x,{className:b.menuBox},h)))),r.createElement(c.Slot,{reference:this._handleSlot})))}update(e){e?this._resizeForced():this._resize()}focus(e){this._containerRef?.focus(e)}blur(){this._containerRef?.blur()}}function x(e){const t=(0,i.ensureNotNull)((0,r.useContext)(p.SubmenuContext)),n=r.useRef(null);return r.createElement("div",{ref:n,className:e.className,onMouseOver:function(e){if(!(null!==t.current&&e.target instanceof Node&&(r=e.target,n.current?.contains(r))))return;var r;t.isSubmenuNode(e.target)||t.setCurrent(null)},"data-name":"menu-inner"},e.children)}v.contextType=p.SubmenuContext},28466:(e,t,n)=>{"use strict";n.d(t,{CloseDelegateContext:()=>o});var r=n(50959),s=n(370981);const o=r.createContext(s.globalCloseDelegate)},8361:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>r.Portal,PortalContext:()=>r.PortalContext});var r=n(730654)},753327:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>r.Slot,SlotContext:()=>r.SlotContext});var r=n(682925)},742554:(e,t,n)=>{"use strict";n.d(t,{TouchScrollContainer:()=>c});var r=n(50959),s=n(259142),o=n(650151),i=n(601227);const a=CSS.supports("overscroll-behavior","none");let l=0;const c=(0,r.forwardRef)(((e,t)=>{const{children:n,...o}=e,c=(0,r.useRef)(null);return(0,r.useImperativeHandle)(t,(()=>c.current)),(0,r.useLayoutEffect)((()=>{if(i.CheckMobile.iOS())return l++,null!==c.current&&(a?1===l&&(document.body.style.overscrollBehavior="none"):(0,s.disableBodyScroll)(c.current,{allowTouchMove:u(c)})),()=>{l--,null!==c.current&&(a?0===l&&(document.body.style.overscrollBehavior=""):(0,s.enableBodyScroll)(c.current))}}),[]),r.createElement("div",{ref:c,...o
},n)}));function u(e){return t=>{const n=(0,o.ensureNotNull)(e.current),r=document.activeElement;return!n.contains(t)||null!==r&&n.contains(r)&&r.contains(t)}}},467025:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_MAX:()=>o,checkValueBoundaries:()=>i});var r=n(609838),s=n(822960);const o=9e15;function i(e){const{value:t,min:i=-1/0,max:a=o,boundariesErrorMessages:l}=e,c=function(e,t,n){const r=e>=t,o=e<=n;return{passMin:r,passMax:o,pass:r&&o,clamped:(0,s.clamp)(e,t,n)}}(t,i,a);let u;return c.passMax||(u=l?.greaterThanMax??r.t(null,{replace:{max:String(a)}},n(631331))),c.passMin||(u=l?.lessThanMin??r.t(null,{replace:{min:String(i)}},n(324216))),{isPassed:c.pass,msg:u,clampedValue:c.clamped}}},465890:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11 9" width="11" height="9" fill="none"><path stroke="currentColor" stroke-width="2" d="M0.999878 4L3.99988 7L9.99988 1"/></svg>'}}]);