(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6166],{497754:(e,t)=>{var r;!function(){"use strict";var o={}.hasOwnProperty;function n(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var s=typeof r;if("string"===s||"number"===s)e.push(r);else if(Array.isArray(r)&&r.length){var i=n.apply(null,r);i&&e.push(i)}else if("object"===s)for(var a in r)o.call(r,a)&&r[a]&&e.push(a)}}return e.join(" ")}e.exports?(n.default=n,e.exports=n):void 0===(r=function(){return n}.apply(t,[]))||(e.exports=r)}()},349931:e=>{e.exports={container:"container-kfvcmk8t",centerElement:"centerElement-kfvcmk8t",text:"text-kfvcmk8t"}},555581:e=>{e.exports={container:"container-zLVm6B4t",content:"content-zLVm6B4t",arrowHolder:"arrowHolder-zLVm6B4t","arrowHolder--below":"arrowHolder--below-zLVm6B4t","arrowHolder--above":"arrowHolder--above-zLVm6B4t","arrowHolder--before":"arrowHolder--before-zLVm6B4t","arrowHolder--after":"arrowHolder--after-zLVm6B4t","arrowHolder--above-fix":"arrowHolder--above-fix-zLVm6B4t","arrowHolder--before-rtl-fix":"arrowHolder--before-rtl-fix-zLVm6B4t","arrowHolder--after-ltr-fix":"arrowHolder--after-ltr-fix-zLVm6B4t",label:"label-zLVm6B4t",closeButton:"closeButton-zLVm6B4t"}},183787:(e,t,r)=>{"use strict";r.d(t,{Icon:()=>n});var o=r(50959);const n=o.forwardRef(((e,t)=>{const{icon:r="",title:n,ariaLabel:s,ariaLabelledby:i,ariaHidden:a,...l}=e,d=!!(n||s||i);return o.createElement("span",{role:"img",...l,ref:t,"aria-label":s,"aria-labelledby":i,"aria-hidden":a||!d,title:n,dangerouslySetInnerHTML:{__html:r}})}))},878112:(e,t,r)=>{"use strict";r.d(t,{Icon:()=>o.Icon});var o=r(183787)},730654:(e,t,r)=>{"use strict";r.d(t,{Portal:()=>d,PortalContext:()=>c});var o=r(50959),n=r(632227),s=r(925931),i=r(801808),a=r(481564),l=r(682925);class d extends o.PureComponent{constructor(){super(...arguments),this._uuid=(0,s.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"";const t=this.props.className;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE,"true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),n.createPortal(o.createElement(c.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,i.getRootOverlapManager)():this.context}}d.contextType=l.SlotContext;const c=o.createContext(null)},682925:(e,t,r)=>{"use strict";r.d(t,{Slot:()=>n,SlotContext:()=>s});var o=r(50959);class n extends o.Component{shouldComponentUpdate(){return!1}render(){return o.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const s=o.createContext(null)},
801808:(e,t,r)=>{"use strict";r.d(t,{OverlapManager:()=>i,getRootOverlapManager:()=>l});var o=r(650151),n=r(481564);class s{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class i{constructor(e=document){this._storage=new s,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,r=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,r),this._container=r}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const r=this._windows.get(e);if(void 0!==r)return r;this.registerWindow(e);const o=this._document.createElement("div");if(o.style.position=t.position,o.style.zIndex=this._index.toString(),o.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(o);else if(t.index<=0)this._container.insertBefore(o,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(o,e)}}else"reverse"===t.direction?this._container.insertBefore(o,this._container.firstChild):this._container.appendChild(o);return this._windows.set(e,o),++this._index,o}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveLastWindowToTop(){const e=this._storage.getItems(),t=e[e.length-1];t&&this.moveToTop(t)}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,r)=>{e.hasAttribute(n.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(n.FOCUS_TRAP_DATA_ATTRIBUTE,e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const a=new WeakMap;function l(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,o.ensureDefined)(a.get(t));{const t=new i(e),r=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return a.set(r,t),t.setContainer(r),e.body.appendChild(r),t}}var d;!function(e){e[e.BaseZindex=150]="BaseZindex"}(d||(d={}))},215593:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ChartEventHintRenderer:()=>p});var o=r(50959),n=r(632227),s=r(497754),i=r(878112),a=(r(8361),r(333765)),l=r(555581);o.PureComponent;function d(e){const{className:t,containerClassName:r,contentClassName:n,reference:d,style:c,arrow:h=!0,arrowClassName:p,arrowReference:u,onClose:m,arrowStyle:f,children:_,role:w,...v}=e;return o.createElement("div",{...v,className:t,ref:d,style:c,"data-role":w},h&&o.createElement("div",{className:p,ref:u,style:f}),o.createElement("div",{
className:s(l.container,r)},o.createElement("div",{className:s(l.content,n)},_),m&&o.createElement(i.Icon,{className:l.closeButton,icon:a,onClick:m})))}var c=r(349931);function h(e){const{bottomOffset:t,text:r,onClose:n}=e;return o.createElement("div",{className:c.container,style:{bottom:t}},o.createElement("div",{className:c.centerElement},o.createElement(d,{arrow:!1,onClose:n},o.createElement("div",{className:c.text},r))))}class p{constructor(e){this.type=0,this._wrap=document.createElement("div"),this._container=e}show(e,t){if(!this._wrap)return;this.hide(),this._container.append(this._wrap);const r={text:e,onClose:()=>{t&&t(),this.hide()},bottomOffset:Array.from(this._container.children).reduce(((e,t)=>(t.getAttribute("data-is-chart-toolbar-component")&&(e+=t.clientHeight),e)),32)};n.render(o.createElement(h,{...r}),this._wrap)}hide(){this._wrap&&(n.unmountComponentAtNode(this._wrap),this._wrap.remove())}destroy(){this.hide(),delete this._wrap}}},8361:(e,t,r)=>{"use strict";r.d(t,{Portal:()=>o.Portal,PortalContext:()=>o.PortalContext});var o=r(730654)},333765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},925931:(e,t,r)=>{"use strict";r.d(t,{nanoid:()=>o});let o=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);