"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[8820],{585958:(e,r,o)=>{o.r(r),o.d(r,{LineToolFlagMark:()=>a});var t=o(86441),n=o(889868),i=o(792535),s=o(981856);class a extends n.LineDataSource{constructor(e,r,n,i){super(e,r??a.createProperties(e.backgroundTheme().spawnOwnership()),n,i),Promise.all([o.e(6290),o.e(6881),o.e(5579),o.e(1583)]).then(o.bind(o,976375)).then((({FlagMarkPaneView:e})=>{const r=new e(this,this.model());r.setAnchors(new t.Point(0,0)),this._setPaneViews([r])}))}pointsCount(){return 1}name(){return"Flag Mark"}static createProperties(e,r){r&&void 0===r.flagColor&&(r.flagColor="#318757");const o=new i.DefaultProperty({defaultName:"linetoolflagmark",state:r,theme:e});return this._configureProperties(o),o}_normalizePoint(e,r){return super._normalizePointWithoutOffset(e)??super._normalizePoint(e,r)}_getPropertyDefinitionsViewModelClass(){return Promise.all([o.e(6406),o.e(8511),o.e(5234),o.e(4590),o.e(8537)]).then(o.bind(o,518597)).then((e=>e.FlagMarkDefinitionsViewModel))}static _configureProperties(e){super._configureProperties(e),e.addChild("backgroundsColors",new s.LineToolColorsProperty([e.childs().flagColor])),e.addExcludedKey("backgroundsColors",3)}}a.version=2}}]);