"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1455],{351502:(e,t,i)=>{i.d(t,{DateAndPriceBaseProperties:()=>y,allPropertiesStateKeysBase:()=>m,nonThemedFactoryDefaultsBase:()=>c,themedFactoryDefaultsBase:()=>d});var s=i(926048),r=i(111982),o=i(589637),a=i(811199),l=i(571772),n=i(915780);const u=(0,s.getHexColorByName)("color-tv-blue-500"),c={linewidth:2,fontsize:12,fillLabelBackground:!0,fillBackground:!0,backgroundTransparency:60,showVolume:!0,intervalsVisibilities:{...a.intervalsVisibilitiesDefaults},customText:{visible:!1,fontsize:12,bold:!1,italic:!1}},d=new Map([[r.StdTheme.Light,{textcolor:(0,s.getHexColorByName)("color-black"),labelBackgroundColor:(0,s.getHexColorByName)("color-white"),linecolor:u,backgroundColor:(0,o.generateColor)(u,85),shadow:"rgba(0, 0, 0, 0.2)",customText:{color:u}}],[r.StdTheme.Dark,{textcolor:(0,s.getHexColorByName)("color-white"),labelBackgroundColor:(0,s.getHexColorByName)("color-cold-gray-800"),linecolor:u,backgroundColor:(0,o.generateColor)(u,85),shadow:"rgba(0, 0, 0, 0.4)",customText:{color:u}}]]),m=["customText.text"];function h(e){const{showVolume:t=!0,...i}=e;return{...i,showVolume:t}}class y extends n.LineDataSourceProperty{constructor({nonThemedDefaultsKeys:e,themedDefaultsKeys:t,state:i,...s}){super({nonThemedDefaultsKeys:e,themedDefaultsKeys:t,templateKeys:[...e??[],...t??[],...m],state:i?h(i):void 0,...s});const r=i?.customText;this._textProperty=new l.Property(r?.text??""),this.childs().customText?.addChild("text",this._textProperty)}template(){const e=super.template();return e.customText.text=this._textProperty.value(),e}}},736227:(e,t,i)=>{i.r(t),i.d(t,{LineToolDateAndPriceRange:()=>T});var s=i(650151),r=i(889868),o=i(342529),a=i(926048),l=i(111982),n=i(792535),u=i(171721),c=i(999710),d=i(351502);const m=(0,a.getHexColorByName)("color-tv-blue-500"),h={...d.nonThemedFactoryDefaultsBase,drawBorder:!1,borderWidth:1},y=new Map([[l.StdTheme.Light,{...d.themedFactoryDefaultsBase.get(l.StdTheme.Light),borderColor:m}],[l.StdTheme.Dark,{...d.themedFactoryDefaultsBase.get(l.StdTheme.Dark),borderColor:m}]]),p=(0,n.extractThemedColors)((0,s.ensureDefined)(y.get(l.StdTheme.Light)),(0,s.ensureDefined)(y.get(l.StdTheme.Dark))),f=(0,n.extractAllPropertiesKeys)((0,s.ensureDefined)(y.get(l.StdTheme.Light))),b=(0,n.extractAllPropertiesKeys)(h),g=[...new Set([...f,...b,...c.commonLineToolPropertiesStateKeys,...d.allPropertiesStateKeysBase])];class v extends d.DateAndPriceBaseProperties{static create(e,t){return new this({defaultName:"linetooldateandpricerange",factoryDefaultsSupplier:()=>(0,u.factoryDefaultsForCurrentTheme)(h,y),nonThemedDefaultsKeys:b,themedDefaultsKeys:f,allStateKeys:g,themedColors:p,replaceThemedColorsOnThemeChange:!0,state:t,theme:e})}}class T extends r.LineDataSource{constructor(e,t,s,r){super(e,t??T.createProperties(e.backgroundTheme().spawnOwnership()),s,r),this._volumeCalculator=null,Promise.all([i.e(6290),i.e(6881),i.e(5579),i.e(1583)]).then(i.bind(i,111253)).then((e=>{
this._setPaneViews([new e.DateAndPriceRangePaneView(this,this._model)])}))}destroy(){super.destroy(),null!==this._volumeCalculator&&this._volumeCalculator.destroy()}pointsCount(){return 2}name(){return"Date and Price Range"}template(){return this._properties.template()}volume(){if(null===this._volumeCalculator)return NaN;const e=this.points();return this._volumeCalculator.volume(e[0].index,e[1].index)}setOwnerSource(e){e===this._model.mainSeries()&&((0,s.assert)(null===this._volumeCalculator),this._volumeCalculator=new o.SeriesTimeRangeVolumeCalculator(this._model.mainSeries())),super.setOwnerSource(e)}static createProperties(e,t){const i=v.create(e,t);return this._configureProperties(i),i}async _getPropertyDefinitionsViewModelClass(){return(await Promise.all([i.e(6406),i.e(8511),i.e(5234),i.e(4590),i.e(8537)]).then(i.bind(i,297366))).GeneralDatePriceRangeDefinitionsViewModel}}},999710:(e,t,i)=>{i.d(t,{commonLineToolPropertiesStateKeys:()=>s});const s=["symbolStateVersion","zOrderVersion","frozen","title","interval","symbol","currencyId","unitId","visible","intervalsVisibilities.ticks","intervalsVisibilities.seconds","intervalsVisibilities.secondsFrom","intervalsVisibilities.secondsTo","intervalsVisibilities.minutes","intervalsVisibilities.minutesFrom","intervalsVisibilities.minutesTo","intervalsVisibilities.hours","intervalsVisibilities.hoursFrom","intervalsVisibilities.hoursTo","intervalsVisibilities.days","intervalsVisibilities.daysFrom","intervalsVisibilities.daysTo","intervalsVisibilities.weeks","intervalsVisibilities.weeksFrom","intervalsVisibilities.weeksTo","intervalsVisibilities.months","intervalsVisibilities.monthsFrom","intervalsVisibilities.monthsTo","intervalsVisibilities.ranges"];var r,o,a;!function(e){e[e.NotShared=0]="NotShared",e[e.SharedInLayout=1]="SharedInLayout",e[e.GloballyShared=2]="GloballyShared"}(r||(r={})),function(e){e.BeforeAllAction="BeforeAll",e.CustomAction="CustomAction"}(o||(o={})),function(e){e.FloatingToolbarButton="FloatingToolbarButton",e.Default="Default"}(a||(a={}))},915780:(e,t,i)=>{i.d(t,{LineDataSourceProperty:()=>l});var s=i(154834),r=i(916738),o=i(650151),a=i(792535);class l extends a.DefaultProperty{constructor({templateKeys:e,...t}){super({ignoreAllowSavingDefaults:!0,saveNonDefaultUserPreferencesOnly:!0,...t}),this._templateKeys=(0,o.ensureDefined)(e||this._allDefaultsKeys)}template(){return(0,a.extractState)(this.state(),this._templateKeys,[])}applyTemplate(e){this.mergeAndFire((0,a.extractState)((0,r.default)((0,s.default)(this._factoryDefaultsSupplier()),e),this._templateKeys))}}},171721:(e,t,i)=>{i.d(t,{factoryDefaultsForCurrentTheme:()=>n});var s=i(916738),r=i(154834),o=i(650151),a=i(702054),l=i(111982);function n(e,t){const i=a.watchedTheme.value()??l.StdTheme.Light,n=(0,r.default)(e);return(0,s.default)(n,(0,o.ensureDefined)(t.get(i))),n}}}]);