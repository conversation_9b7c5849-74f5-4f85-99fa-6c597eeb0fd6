"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6432],{62064:(e,r,i)=>{i.r(r),i.d(r,{LineToolTriangle:()=>o});var t=i(792535),n=i(889868),s=i(981856);class o extends n.LineDataSource{constructor(e,r,t,n){super(e,r??o.createProperties(e.backgroundTheme().spawnOwnership()),t,n),Promise.all([i.e(6290),i.e(6881),i.e(5579),i.e(1583)]).then(i.bind(i,764366)).then((({TrianglePaneView:e})=>{this._setPaneViews([new e(this,this._model)])}))}pointsCount(){return 3}name(){return"Triangle"}static createProperties(e,r){const i=new t.DefaultProperty({defaultName:"linetooltriangle",state:r,theme:e});return this._configureProperties(i),i}async _getPropertyDefinitionsViewModelClass(){return(await Promise.all([i.e(6406),i.e(8511),i.e(5234),i.e(4590),i.e(8537)]).then(i.bind(i,779890))).GeneralFiguresDefinitionsViewModelBase}static _configureProperties(e){super._configureProperties(e),e.addChild("linesColors",new s.LineToolColorsProperty([e.childs().color]))}}}}]);