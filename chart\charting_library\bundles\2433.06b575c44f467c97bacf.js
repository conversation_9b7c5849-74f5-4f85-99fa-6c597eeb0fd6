(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2433],{259142:function(e,r){var t,n,o;n=[r],t=function(e){"use strict";function r(e){if(Array.isArray(e)){for(var r=0,t=Array(e.length);r<e.length;r++)t[r]=e[r];return t}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var t=!1;if("undefined"!=typeof window){var n={get passive(){t=!0}};window.addEventListener("testPassive",null,n),window.removeEventListener("testPassive",null,n)}var o="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),i=[],u=!1,c=-1,s=void 0,a=void 0,l=function(e){return i.some((function(r){return!(!r.options.allowTouchMove||!r.options.allowTouchMove(e))}))},f=function(e){var r=e||window.event;return!!l(r.target)||1<r.touches.length||(r.preventDefault&&r.preventDefault(),!1)},d=function(){setTimeout((function(){void 0!==a&&(document.body.style.paddingRight=a,a=void 0),void 0!==s&&(document.body.style.overflow=s,s=void 0)}))};e.disableBodyScroll=function(e,n){if(o){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!i.some((function(r){return r.targetElement===e}))){var d={targetElement:e,options:n||{}};i=[].concat(r(i),[d]),e.ontouchstart=function(e){1===e.targetTouches.length&&(c=e.targetTouches[0].clientY)},e.ontouchmove=function(r){var t,n,o,i;1===r.targetTouches.length&&(n=e,i=(t=r).targetTouches[0].clientY-c,!l(t.target)&&(n&&0===n.scrollTop&&0<i||(o=n)&&o.scrollHeight-o.scrollTop<=o.clientHeight&&i<0?f(t):t.stopPropagation()))},u||(document.addEventListener("touchmove",f,t?{passive:!1}:void 0),u=!0)}}else{v=n,setTimeout((function(){if(void 0===a){var e=!!v&&!0===v.reserveScrollBarGap,r=window.innerWidth-document.documentElement.clientWidth;e&&0<r&&(a=document.body.style.paddingRight,document.body.style.paddingRight=r+"px")}void 0===s&&(s=document.body.style.overflow,document.body.style.overflow="hidden")}));var p={targetElement:e,options:n||{}};i=[].concat(r(i),[p])}var v},e.clearAllBodyScrollLocks=function(){o?(i.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),u&&(document.removeEventListener("touchmove",f,t?{passive:!1}:void 0),u=!1),i=[],c=-1):(d(),i=[])},e.enableBodyScroll=function(e){if(o){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,i=i.filter((function(r){return r.targetElement!==e})),u&&0===i.length&&(document.removeEventListener("touchmove",f,t?{passive:!1}:void 0),u=!1)}else 1===i.length&&i[0].targetElement===e?(d(),i=[]):i=i.filter((function(r){return r.targetElement!==e}))}},void 0===(o="function"==typeof t?t.apply(r,n):t)||(e.exports=o)},947488:(e,r,t)=>{"use strict";t.d(r,{BehaviorSubject:()=>o});var n=t(446685),o=function(e){function r(r){var t=e.call(this)||this;return t._value=r,t}return(0,n.__extends)(r,e),
Object.defineProperty(r.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),r.prototype._subscribe=function(r){var t=e.prototype._subscribe.call(this,r);return!t.closed&&r.next(this._value),t},r.prototype.getValue=function(){var e=this,r=e.hasError,t=e.thrownError,n=e._value;if(r)throw t;return this._throwIfClosed(),n},r.prototype.next=function(r){e.prototype.next.call(this,this._value=r)},r}(t(737538).Subject)},815544:(e,r,t)=>{"use strict";t.d(r,{Observable:()=>f});var n=t(620210),o=t(303448),i=t(836080),u=t(472484);function c(e){return 0===e.length?u.identity:1===e.length?e[0]:function(r){return e.reduce((function(e,r){return r(e)}),r)}}var s=t(777254),a=t(38323),l=t(263535),f=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(r){var t=new e;return t.source=this,t.operator=r,t},e.prototype.subscribe=function(e,r,t){var i,u=this,c=(i=e)&&i instanceof n.Subscriber||function(e){return e&&(0,a.isFunction)(e.next)&&(0,a.isFunction)(e.error)&&(0,a.isFunction)(e.complete)}(i)&&(0,o.isSubscription)(i)?e:new n.SafeSubscriber(e,r,t);return(0,l.errorContext)((function(){var e=u,r=e.operator,t=e.source;c.add(r?r.call(c,t):t?u._subscribe(c):u._trySubscribe(c))})),c},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(r){e.error(r)}},e.prototype.forEach=function(e,r){var t=this;return new(r=d(r))((function(r,n){var o;o=t.subscribe((function(r){try{e(r)}catch(e){n(e),null==o||o.unsubscribe()}}),n,r)}))},e.prototype._subscribe=function(e){var r;return null===(r=this.source)||void 0===r?void 0:r.subscribe(e)},e.prototype[i.observable]=function(){return this},e.prototype.pipe=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return c(e)(this)},e.prototype.toPromise=function(e){var r=this;return new(e=d(e))((function(e,t){var n;r.subscribe((function(e){return n=e}),(function(e){return t(e)}),(function(){return e(n)}))}))},e.create=function(r){return new e(r)},e}();function d(e){var r;return null!==(r=null!=e?e:s.config.Promise)&&void 0!==r?r:Promise}},737538:(e,r,t)=>{"use strict";t.d(r,{Subject:()=>a});var n=t(446685),o=t(815544),i=t(303448),u=(0,t(530634).createErrorClass)((function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}})),c=t(3955),s=t(263535),a=function(e){function r(){var r=e.call(this)||this;return r.closed=!1,r.observers=[],r.isStopped=!1,r.hasError=!1,r.thrownError=null,r}return(0,n.__extends)(r,e),r.prototype.lift=function(e){var r=new l(this,this);return r.operator=e,r},r.prototype._throwIfClosed=function(){if(this.closed)throw new u},r.prototype.next=function(e){var r=this;(0,s.errorContext)((function(){var t,o;if(r._throwIfClosed(),!r.isStopped){var i=r.observers.slice();try{for(var u=(0,n.__values)(i),c=u.next();!c.done;c=u.next()){c.value.next(e)}}catch(e){t={error:e}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(t)throw t.error}}}}))},r.prototype.error=function(e){var r=this;(0,s.errorContext)((function(){if(r._throwIfClosed(),
!r.isStopped){r.hasError=r.isStopped=!0,r.thrownError=e;for(var t=r.observers;t.length;)t.shift().error(e)}}))},r.prototype.complete=function(){var e=this;(0,s.errorContext)((function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var r=e.observers;r.length;)r.shift().complete()}}))},r.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=null},Object.defineProperty(r.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),r.prototype._trySubscribe=function(r){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,r)},r.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},r.prototype._innerSubscribe=function(e){var r=this,t=r.hasError,n=r.isStopped,o=r.observers;return t||n?i.EMPTY_SUBSCRIPTION:(o.push(e),new i.Subscription((function(){return(0,c.arrRemove)(o,e)})))},r.prototype._checkFinalizedStatuses=function(e){var r=this,t=r.hasError,n=r.thrownError,o=r.isStopped;t?e.error(n):o&&e.complete()},r.prototype.asObservable=function(){var e=new o.Observable;return e.source=this,e},r.create=function(e,r){return new l(e,r)},r}(o.Observable),l=function(e){function r(r,t){var n=e.call(this)||this;return n.destination=r,n.source=t,n}return(0,n.__extends)(r,e),r.prototype.next=function(e){var r,t;null===(t=null===(r=this.destination)||void 0===r?void 0:r.next)||void 0===t||t.call(r,e)},r.prototype.error=function(e){var r,t;null===(t=null===(r=this.destination)||void 0===r?void 0:r.error)||void 0===t||t.call(r,e)},r.prototype.complete=function(){var e,r;null===(r=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===r||r.call(e)},r.prototype._subscribe=function(e){var r,t;return null!==(t=null===(r=this.source)||void 0===r?void 0:r.subscribe(e))&&void 0!==t?t:i.EMPTY_SUBSCRIPTION},r}(a)},620210:(e,r,t)=>{"use strict";t.d(r,{SafeSubscriber:()=>v,Subscriber:()=>p});var n=t(446685),o=t(38323),i=t(303448),u=t(777254),c=t(880842),s=t(177441),a=l("C",void 0,void 0);function l(e,r,t){return{kind:e,value:r,error:t}}var f=t(692402),d=t(263535),p=function(e){function r(r){var t=e.call(this)||this;return t.isStopped=!1,r?(t.destination=r,(0,i.isSubscription)(r)&&r.add(t)):t.destination=m,t}return(0,n.__extends)(r,e),r.create=function(e,r,t){return new v(e,r,t)},r.prototype.next=function(e){this.isStopped?y(function(e){return l("N",e,void 0)}(e),this):this._next(e)},r.prototype.error=function(e){this.isStopped?y(l("E",void 0,e),this):(this.isStopped=!0,this._error(e))},r.prototype.complete=function(){this.isStopped?y(a,this):(this.isStopped=!0,this._complete())},r.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},r.prototype._next=function(e){this.destination.next(e)},r.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},r.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},r
}(i.Subscription),v=function(e){function r(r,t,n){var i,c=e.call(this)||this;if((0,o.isFunction)(r))i=r;else if(r){var a;i=r.next,t=r.error,n=r.complete,c&&u.config.useDeprecatedNextContext?(a=Object.create(r)).unsubscribe=function(){return c.unsubscribe()}:a=r,i=null==i?void 0:i.bind(a),t=null==t?void 0:t.bind(a),n=null==n?void 0:n.bind(a)}return c.destination={next:i?h(i,c):s.noop,error:h(null!=t?t:b,c),complete:n?h(n,c):s.noop},c}return(0,n.__extends)(r,e),r}(p);function h(e,r){return function(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];try{e.apply(void 0,(0,n.__spreadArray)([],(0,n.__read)(r),!1))}catch(e){u.config.useDeprecatedSynchronousErrorHandling?(0,d.captureError)(e):(0,c.reportUnhandledError)(e)}}}function b(e){throw e}function y(e,r){var t=u.config.onStoppedNotification;t&&f.timeoutProvider.setTimeout((function(){return t(e,r)}))}var m={closed:!0,next:s.noop,error:b,complete:s.noop}},303448:(e,r,t)=>{"use strict";t.d(r,{EMPTY_SUBSCRIPTION:()=>s,Subscription:()=>c,isSubscription:()=>a});var n=t(446685),o=t(38323),i=(0,t(530634).createErrorClass)((function(e){return function(r){e(this),this.message=r?r.length+" errors occurred during unsubscription:\n"+r.map((function(e,r){return r+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=r}})),u=t(3955),c=function(){function e(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._teardowns=null}var r;return e.prototype.unsubscribe=function(){var e,r,t,u,c;if(!this.closed){this.closed=!0;var s=this._parentage;if(s)if(this._parentage=null,Array.isArray(s))try{for(var a=(0,n.__values)(s),f=a.next();!f.done;f=a.next()){f.value.remove(this)}}catch(r){e={error:r}}finally{try{f&&!f.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}else s.remove(this);var d=this.initialTeardown;if((0,o.isFunction)(d))try{d()}catch(e){c=e instanceof i?e.errors:[e]}var p=this._teardowns;if(p){this._teardowns=null;try{for(var v=(0,n.__values)(p),h=v.next();!h.done;h=v.next()){var b=h.value;try{l(b)}catch(e){c=null!=c?c:[],e instanceof i?c=(0,n.__spreadArray)((0,n.__spreadArray)([],(0,n.__read)(c),!1),(0,n.__read)(e.errors),!1):c.push(e)}}}catch(e){t={error:e}}finally{try{h&&!h.done&&(u=v.return)&&u.call(v)}finally{if(t)throw t.error}}}if(c)throw new i(c)}},e.prototype.add=function(r){var t;if(r&&r!==this)if(this.closed)l(r);else{if(r instanceof e){if(r.closed||r._hasParent(this))return;r._addParent(this)}(this._teardowns=null!==(t=this._teardowns)&&void 0!==t?t:[]).push(r)}},e.prototype._hasParent=function(e){var r=this._parentage;return r===e||Array.isArray(r)&&r.includes(e)},e.prototype._addParent=function(e){var r=this._parentage;this._parentage=Array.isArray(r)?(r.push(e),r):r?[r,e]:e},e.prototype._removeParent=function(e){var r=this._parentage;r===e?this._parentage=null:Array.isArray(r)&&(0,u.arrRemove)(r,e)},e.prototype.remove=function(r){var t=this._teardowns;t&&(0,u.arrRemove)(t,r),r instanceof e&&r._removeParent(this)},e.EMPTY=((r=new e).closed=!0,r),e}(),s=c.EMPTY;function a(e){
return e instanceof c||e&&"closed"in e&&(0,o.isFunction)(e.remove)&&(0,o.isFunction)(e.add)&&(0,o.isFunction)(e.unsubscribe)}function l(e){(0,o.isFunction)(e)?e():e.unsubscribe()}},777254:(e,r,t)=>{"use strict";t.d(r,{config:()=>n});var n={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},839874:(e,r,t)=>{"use strict";t.d(r,{from:()=>g});var n=t(771035),o=t(72117),i=t(116217),u=t(138966);function c(e,r){return void 0===r&&(r=0),(0,i.operate)((function(t,n){t.subscribe(new u.OperatorSubscriber(n,(function(t){return(0,o.executeSchedule)(n,e,(function(){return n.next(t)}),r)}),(function(){return(0,o.executeSchedule)(n,e,(function(){return n.complete()}),r)}),(function(t){return(0,o.executeSchedule)(n,e,(function(){return n.error(t)}),r)})))}))}function s(e,r){return void 0===r&&(r=0),(0,i.operate)((function(t,n){n.add(e.schedule((function(){return t.subscribe(n)}),r))}))}var a=t(815544);var l=t(844299),f=t(38323);function d(e,r){if(!e)throw new Error("Iterable cannot be null");return new a.Observable((function(t){(0,o.executeSchedule)(t,r,(function(){var n=e[Symbol.asyncIterator]();(0,o.executeSchedule)(t,r,(function(){n.next().then((function(e){e.done?t.complete():t.next(e.value)}))}),0,!0)}))}))}var p=t(921139),v=t(844400),h=t(931474),b=t(889606),y=t(740335),m=t(188605),_=t(456311);function w(e,r){if(null!=e){if((0,p.isInteropObservable)(e))return function(e,r){return(0,n.innerFrom)(e).pipe(s(r),c(r))}(e,r);if((0,h.isArrayLike)(e))return function(e,r){return new a.Observable((function(t){var n=0;return r.schedule((function(){n===e.length?t.complete():(t.next(e[n++]),t.closed||this.schedule())}))}))}(e,r);if((0,v.isPromise)(e))return function(e,r){return(0,n.innerFrom)(e).pipe(s(r),c(r))}(e,r);if((0,y.isAsyncIterable)(e))return d(e,r);if((0,b.isIterable)(e))return function(e,r){return new a.Observable((function(t){var n;return(0,o.executeSchedule)(t,r,(function(){n=e[l.iterator](),(0,o.executeSchedule)(t,r,(function(){var e,r,o;try{r=(e=n.next()).value,o=e.done}catch(e){return void t.error(e)}o?t.complete():t.next(r)}),0,!0)})),function(){return(0,f.isFunction)(null==n?void 0:n.return)&&n.return()}}))}(e,r);if((0,_.isReadableStreamLike)(e))return function(e,r){return d((0,_.readableStreamLikeToAsyncGenerator)(e),r)}(e,r)}throw(0,m.createInvalidObservableTypeError)(e)}function g(e,r){return r?w(e,r):(0,n.innerFrom)(e)}},275734:(e,r,t)=>{"use strict";t.d(r,{fromEventPattern:()=>u});var n=t(815544),o=t(38323),i=t(91034);function u(e,r,t){return t?u(e,r).pipe((0,i.mapOneOrManyArgs)(t)):new n.Observable((function(t){var n=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return t.next(1===e.length?e[0]:e)},i=e(n);return(0,o.isFunction)(r)?function(){return r(n,i)}:void 0}))}},771035:(e,r,t)=>{"use strict";t.d(r,{innerFrom:()=>h});var n=t(446685),o=t(931474),i=t(844400),u=t(815544),c=t(921139),s=t(740335),a=t(188605),l=t(889606),f=t(456311),d=t(38323),p=t(880842),v=t(836080);function h(e){
if(e instanceof u.Observable)return e;if(null!=e){if((0,c.isInteropObservable)(e))return m=e,new u.Observable((function(e){var r=m[v.observable]();if((0,d.isFunction)(r.subscribe))return r.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")}));if((0,o.isArrayLike)(e))return y=e,new u.Observable((function(e){for(var r=0;r<y.length&&!e.closed;r++)e.next(y[r]);e.complete()}));if((0,i.isPromise)(e))return h=e,new u.Observable((function(e){h.then((function(r){e.closed||(e.next(r),e.complete())}),(function(r){return e.error(r)})).then(null,p.reportUnhandledError)}));if((0,s.isAsyncIterable)(e))return b(e);if((0,l.isIterable)(e))return t=e,new u.Observable((function(e){var r,o;try{for(var i=(0,n.__values)(t),u=i.next();!u.done;u=i.next()){var c=u.value;if(e.next(c),e.closed)return}}catch(e){r={error:e}}finally{try{u&&!u.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}e.complete()}));if((0,f.isReadableStreamLike)(e))return r=e,b((0,f.readableStreamLikeToAsyncGenerator)(r))}var r,t,h,y,m;throw(0,a.createInvalidObservableTypeError)(e)}function b(e){return new u.Observable((function(r){(function(e,r){var t,o,i,u;return(0,n.__awaiter)(this,void 0,void 0,(function(){var c,s;return(0,n.__generator)(this,(function(a){switch(a.label){case 0:a.trys.push([0,5,6,11]),t=(0,n.__asyncValues)(e),a.label=1;case 1:return[4,t.next()];case 2:if((o=a.sent()).done)return[3,4];if(c=o.value,r.next(c),r.closed)return[2];a.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return s=a.sent(),i={error:s},[3,11];case 6:return a.trys.push([6,,9,10]),o&&!o.done&&(u=t.return)?[4,u.call(t)]:[3,8];case 7:a.sent(),a.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return r.complete(),[2]}}))}))})(e,r).catch((function(e){return r.error(e)}))}))}},138966:(e,r,t)=>{"use strict";t.d(r,{OperatorSubscriber:()=>o});var n=t(446685),o=function(e){function r(r,t,n,o,i){var u=e.call(this,r)||this;return u.onFinalize=i,u._next=t?function(e){try{t(e)}catch(e){r.error(e)}}:e.prototype._next,u._error=o?function(e){try{o(e)}catch(e){r.error(e)}finally{this.unsubscribe()}}:e.prototype._error,u._complete=n?function(){try{n()}catch(e){r.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,u}return(0,n.__extends)(r,e),r.prototype.unsubscribe=function(){var r,t=this.closed;e.prototype.unsubscribe.call(this),!t&&(null===(r=this.onFinalize)||void 0===r||r.call(this))},r}(t(620210).Subscriber)},997345:(e,r,t)=>{"use strict";t.d(r,{map:()=>i});var n=t(116217),o=t(138966);function i(e,r){return(0,n.operate)((function(t,n){var i=0;t.subscribe(new o.OperatorSubscriber(n,(function(t){n.next(e.call(r,t,i++))})))}))}},925186:(e,r,t)=>{"use strict";t.d(r,{mergeAll:()=>i});var n=t(114501),o=t(472484);function i(e){return void 0===e&&(e=1/0),(0,n.mergeMap)(o.identity,e)}},114501:(e,r,t)=>{"use strict";t.d(r,{mergeMap:()=>a});var n=t(997345),o=t(771035),i=t(116217),u=t(72117),c=t(138966);var s=t(38323);function a(e,r,t){return void 0===t&&(t=1/0),(0,s.isFunction)(r)?a((function(t,i){
return(0,n.map)((function(e,n){return r(t,e,i,n)}))((0,o.innerFrom)(e(t,i)))}),t):("number"==typeof r&&(t=r),(0,i.operate)((function(r,n){return function(e,r,t,n,i,s,a,l){var f=[],d=0,p=0,v=!1,h=function(){!v||f.length||d||r.complete()},b=function(e){return d<n?y(e):f.push(e)},y=function(e){s&&r.next(e),d++;var l=!1;(0,o.innerFrom)(t(e,p++)).subscribe(new c.OperatorSubscriber(r,(function(e){null==i||i(e),s?b(e):r.next(e)}),(function(){l=!0}),void 0,(function(){if(l)try{d--;for(var e=function(){var e=f.shift();a?(0,u.executeSchedule)(r,a,(function(){return y(e)})):y(e)};f.length&&d<n;)e();h()}catch(e){r.error(e)}})))};return e.subscribe(new c.OperatorSubscriber(r,b,(function(){v=!0,h()}))),function(){null==l||l()}}(r,n,e,t)})))}},757604:(e,r,t)=>{"use strict";t.d(r,{startWith:()=>s});var n=t(925186);var o=t(595940),i=t(839874);function u(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return(0,n.mergeAll)(1)((0,i.from)(e,(0,o.popScheduler)(e)))}var c=t(116217);function s(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var t=(0,o.popScheduler)(e);return(0,c.operate)((function(r,n){(t?u(e,r,t):u(e,r)).subscribe(n)}))}},692402:(e,r,t)=>{"use strict";t.d(r,{timeoutProvider:()=>o});var n=t(446685),o={setTimeout:function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var t=o.delegate;return((null==t?void 0:t.setTimeout)||setTimeout).apply(void 0,(0,n.__spreadArray)([],(0,n.__read)(e),!1))},clearTimeout:function(e){var r=o.delegate;return((null==r?void 0:r.clearTimeout)||clearTimeout)(e)},delegate:void 0}},844299:(e,r,t)=>{"use strict";t.d(r,{iterator:()=>n});var n="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"},836080:(e,r,t)=>{"use strict";t.d(r,{observable:()=>n});var n="function"==typeof Symbol&&Symbol.observable||"@@observable"},595940:(e,r,t)=>{"use strict";t.d(r,{popNumber:()=>s,popResultSelector:()=>u,popScheduler:()=>c});var n=t(38323),o=t(337160);function i(e){return e[e.length-1]}function u(e){return(0,n.isFunction)(i(e))?e.pop():void 0}function c(e){return(0,o.isScheduler)(i(e))?e.pop():void 0}function s(e,r){return"number"==typeof i(e)?e.pop():r}},3955:(e,r,t)=>{"use strict";function n(e,r){if(e){var t=e.indexOf(r);0<=t&&e.splice(t,1)}}t.d(r,{arrRemove:()=>n})},530634:(e,r,t)=>{"use strict";function n(e){var r=e((function(e){Error.call(e),e.stack=(new Error).stack}));return r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r}t.d(r,{createErrorClass:()=>n})},263535:(e,r,t)=>{"use strict";t.d(r,{captureError:()=>u,errorContext:()=>i});var n=t(777254),o=null;function i(e){if(n.config.useDeprecatedSynchronousErrorHandling){var r=!o;if(r&&(o={errorThrown:!1,error:null}),e(),r){var t=o,i=t.errorThrown,u=t.error;if(o=null,i)throw u}}else e()}function u(e){n.config.useDeprecatedSynchronousErrorHandling&&o&&(o.errorThrown=!0,o.error=e)}},72117:(e,r,t)=>{"use strict";function n(e,r,t,n,o){void 0===n&&(n=0),void 0===o&&(o=!1);var i=r.schedule((function(){t(),o?e.add(this.schedule(null,n)):this.unsubscribe()}),n);if(e.add(i),!o)return i}t.d(r,{
executeSchedule:()=>n})},472484:(e,r,t)=>{"use strict";function n(e){return e}t.d(r,{identity:()=>n})},931474:(e,r,t)=>{"use strict";t.d(r,{isArrayLike:()=>n});var n=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e}},740335:(e,r,t)=>{"use strict";t.d(r,{isAsyncIterable:()=>o});var n=t(38323);function o(e){return Symbol.asyncIterator&&(0,n.isFunction)(null==e?void 0:e[Symbol.asyncIterator])}},38323:(e,r,t)=>{"use strict";function n(e){return"function"==typeof e}t.d(r,{isFunction:()=>n})},921139:(e,r,t)=>{"use strict";t.d(r,{isInteropObservable:()=>i});var n=t(836080),o=t(38323);function i(e){return(0,o.isFunction)(e[n.observable])}},889606:(e,r,t)=>{"use strict";t.d(r,{isIterable:()=>i});var n=t(844299),o=t(38323);function i(e){return(0,o.isFunction)(null==e?void 0:e[n.iterator])}},844400:(e,r,t)=>{"use strict";t.d(r,{isPromise:()=>o});var n=t(38323);function o(e){return(0,n.isFunction)(null==e?void 0:e.then)}},456311:(e,r,t)=>{"use strict";t.d(r,{isReadableStreamLike:()=>u,readableStreamLikeToAsyncGenerator:()=>i});var n=t(446685),o=t(38323);function i(e){return(0,n.__asyncGenerator)(this,arguments,(function(){var r,t,o;return(0,n.__generator)(this,(function(i){switch(i.label){case 0:r=e.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,(0,n.__await)(r.read())];case 3:return t=i.sent(),o=t.value,t.done?[4,(0,n.__await)(void 0)]:[3,5];case 4:return[2,i.sent()];case 5:return[4,(0,n.__await)(o)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return r.releaseLock(),[7];case 10:return[2]}}))}))}function u(e){return(0,o.isFunction)(null==e?void 0:e.getReader)}},337160:(e,r,t)=>{"use strict";t.d(r,{isScheduler:()=>o});var n=t(38323);function o(e){return e&&(0,n.isFunction)(e.schedule)}},116217:(e,r,t)=>{"use strict";t.d(r,{operate:()=>o});var n=t(38323);function o(e){return function(r){if(function(e){return(0,n.isFunction)(null==e?void 0:e.lift)}(r))return r.lift((function(r){try{return e(r,this)}catch(e){this.error(e)}}));throw new TypeError("Unable to lift unknown Observable type")}}},91034:(e,r,t)=>{"use strict";t.d(r,{mapOneOrManyArgs:()=>u});var n=t(446685),o=t(997345),i=Array.isArray;function u(e){return(0,o.map)((function(r){return function(e,r){return i(r)?e.apply(void 0,(0,n.__spreadArray)([],(0,n.__read)(r),!1)):e(r)}(e,r)}))}},177441:(e,r,t)=>{"use strict";function n(){}t.d(r,{noop:()=>n})},880842:(e,r,t)=>{"use strict";t.d(r,{reportUnhandledError:()=>i});var n=t(777254),o=t(692402);function i(e){o.timeoutProvider.setTimeout((function(){var r=n.config.onUnhandledError;if(!r)throw e;r(e)}))}},188605:(e,r,t)=>{"use strict";function n(e){return new TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}t.d(r,{createInvalidObservableTypeError:()=>n})},446685:(e,r,t)=>{"use strict";t.d(r,{__asyncGenerator:()=>f,__asyncValues:()=>d,__await:()=>l,__awaiter:()=>i,__extends:()=>o,
__generator:()=>u,__read:()=>s,__spreadArray:()=>a,__values:()=>c});var n=function(e,r){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])},n(e,r)};function o(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function t(){this.constructor=e}n(e,r),e.prototype=null===r?Object.create(r):(t.prototype=r.prototype,new t)}function i(e,r,t,n){return new(t||(t=Promise))((function(o,i){function u(e){try{s(n.next(e))}catch(e){i(e)}}function c(e){try{s(n.throw(e))}catch(e){i(e)}}function s(e){var r;e.done?o(e.value):(r=e.value,r instanceof t?r:new t((function(e){e(r)}))).then(u,c)}s((n=n.apply(e,r||[])).next())}))}function u(e,r){var t,n,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,n=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=r.call(e,u)}catch(e){i=[6,e],n=0}finally{t=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}}Object.create;function c(e){var r="function"==typeof Symbol&&Symbol.iterator,t=r&&e[r],n=0;if(t)return t.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")}function s(e,r){var t="function"==typeof Symbol&&e[Symbol.iterator];if(!t)return e;var n,o,i=t.call(e),u=[];try{for(;(void 0===r||r-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(o)throw o.error}}return u}function a(e,r){for(var t=0,n=r.length,o=e.length;t<n;t++,o++)e[o]=r[t];return e}function l(e){return this instanceof l?(this.v=e,this):new l(e)}function f(e,r,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,o=t.apply(e,r||[]),i=[];return n={},u("next"),u("throw"),u("return"),n[Symbol.asyncIterator]=function(){return this},n;function u(e){o[e]&&(n[e]=function(r){return new Promise((function(t,n){i.push([e,r,t,n])>1||c(e,r)}))})}function c(e,r){try{(t=o[e](r)).value instanceof l?Promise.resolve(t.value.v).then(s,a):f(i[0][2],t)}catch(e){f(i[0][3],e)}
var t}function s(e){c("next",e)}function a(e){c("throw",e)}function f(e,r){e(r),i.shift(),i.length&&c(i[0][0],i[0][1])}}function d(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,t=e[Symbol.asyncIterator];return t?t.call(e):(e=c(e),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(t){r[t]=e[t]&&function(r){return new Promise((function(n,o){(function(e,r,t,n){Promise.resolve(n).then((function(r){e({value:r,done:t})}),r)})(n,o,(r=e[t](r)).done,r.value)}))}}}Object.create},674765:(e,r,t)=>{"use strict";t.d(r,{default:()=>z});var n=t(909275);const o=function(e){return function(r){return null==e?void 0:e[r]}}({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"});var i=t(576750),u=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,c=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");const s=function(e){return(e=(0,i.default)(e))&&e.replace(u,o).replace(c,"")};var a=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;const l=function(e){return e.match(a)||[]};var f=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;const d=function(e){return f.test(e)}
;var p="\\ud800-\\udfff",v="\\u2700-\\u27bf",h="a-z\\xdf-\\xf6\\xf8-\\xff",b="A-Z\\xc0-\\xd6\\xd8-\\xde",y="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",m="["+y+"]",_="\\d+",w="["+v+"]",g="["+h+"]",S="[^"+p+y+_+v+h+b+"]",x="(?:\\ud83c[\\udde6-\\uddff]){2}",E="[\\ud800-\\udbff][\\udc00-\\udfff]",O="["+b+"]",A="(?:"+g+"|"+S+")",T="(?:"+O+"|"+S+")",I="(?:['’](?:d|ll|m|re|s|t|ve))?",P="(?:['’](?:D|LL|M|RE|S|T|VE))?",F="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\\ud83c[\\udffb-\\udfff])?",C="[\\ufe0e\\ufe0f]?",j=C+F+("(?:\\u200d(?:"+["[^"+p+"]",x,E].join("|")+")"+C+F+")*"),k="(?:"+[w,x,E].join("|")+")"+j,R=RegExp([O+"?"+g+"+"+I+"(?="+[m,O,"$"].join("|")+")",T+"+"+P+"(?="+[m,O+A,"$"].join("|")+")",O+"?"+A+"+"+I,O+"+"+P,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",_,k].join("|"),"g");const L=function(e){return e.match(R)||[]};const U=function(e,r,t){return e=(0,i.default)(e),void 0===(r=t?void 0:r)?d(e)?L(e):l(e):e.match(r)||[]};var N=RegExp("['’]","g");const z=function(e){return function(r){return(0,n.default)(U(s(r).replace(N,"")),e,"")}}((function(e,r,t){return e+(t?"-":"")+r.toLowerCase()}))},755883:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});const n=function(){}},925931:(e,r,t)=>{"use strict";t.d(r,{nanoid:()=>n});let n=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,r)=>e+=(r&=63)<36?r.toString(36):r<62?(r-26).toString(36).toUpperCase():r>62?"-":"_"),"")}}]);