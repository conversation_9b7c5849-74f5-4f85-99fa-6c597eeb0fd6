// Quick test to verify indicator sources functionality
// Run this in the browser console

console.log('🧪 Quick Test: Indicator Sources Functionality');

// Test 1: Check initial sources
console.log('\n📋 Initial sources:', window.sourcesConfig.sources);

// Test 2: Add RSI and check if it appears as a source
console.log('\n🔄 Adding RSI...');
try {
    const rsiId = window.addRSI(14, false);
    console.log('✅ RSI added with ID:', rsiId);
    
    // Wait a moment then check sources
    setTimeout(() => {
        console.log('📊 Sources after RSI:', window.sourcesConfig.sources);
        const hasRSI = window.sourcesConfig.sources.some(s => s.includes('RSI'));
        console.log(hasRSI ? '✅ RSI appears in sources!' : '❌ RSI not in sources yet');
    }, 1000);
} catch (error) {
    console.error('❌ RSI test failed:', error);
}

// Test 3: Add MACD and check sources
setTimeout(() => {
    console.log('\n🔄 Adding MACD...');
    try {
        const macdId = window.addMACD(12, 26, 9, false);
        console.log('✅ MACD added with ID:', macdId);
        
        setTimeout(() => {
            console.log('📊 Sources after MACD:', window.sourcesConfig.sources);
            const hasMACD = window.sourcesConfig.sources.some(s => s.includes('MACD'));
            console.log(hasMACD ? '✅ MACD appears in sources!' : '❌ MACD not in sources yet');
        }, 1000);
    } catch (error) {
        console.error('❌ MACD test failed:', error);
    }
}, 2000);

// Test 4: Final summary
setTimeout(() => {
    console.log('\n🎯 Final Test Results:');
    console.log('📈 Total indicators tracked:', window.sourcesConfig._indicatorSources.size);
    console.log('🔗 All available sources:', window.sourcesConfig.sources);
    console.log('\n🎉 Test completed! Check if indicator outputs appear in the sources list.');
}, 4000);
