(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7125],{825549:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},411224:e=>{e.exports={innerLabel:"innerLabel-DjbvBF5Y"}},893236:e=>{e.exports={controlWrapper:"controlWrapper-DBTazUk2",hidden:"hidden-DBTazUk2",control:"control-DBTazUk2",controlIncrease:"controlIncrease-DBTazUk2",controlDecrease:"controlDecrease-DBTazUk2",controlIcon:"controlIcon-DBTazUk2",title:"title-DBTazUk2"}},332965:e=>{e.exports={errors:"errors-UKx97h9K",show:"show-UKx97h9K",error:"error-UKx97h9K","visually-hidden":"visually-hidden-UKx97h9K"}},562996:e=>{e.exports={"error-icon":"error-icon-UhKAouIg","intent-danger":"intent-danger-UhKAouIg","intent-warning":"intent-warning-UhKAouIg"}},41437:e=>{e.exports={"static-messages":"static-messages-cF4vS9J8",errors:"errors-cF4vS9J8",warnings:"warnings-cF4vS9J8","success-mesages":"success-mesages-cF4vS9J8","input-description":"input-description-cF4vS9J8",message:"message-cF4vS9J8"}},654936:(e,t,s)=>{"use strict";s.d(t,{InputControl:()=>b});var r=s(50959),n=s(497754),a=s(800417),o=s(269842),i=s(1811),l=s(525388),c=s(21778),u=s(383836),h=s(603548),d=s(34735),p=s(102691),m=s(825549),g=s.n(m);function f(e){return!(0,a.isAriaAttribute)(e)&&!(0,a.isDataAttribute)(e)}function v(e){const{id:t,title:s,role:o,tabIndex:i,placeholder:l,name:c,type:u,value:h,defaultValue:m,draggable:v,autoComplete:b,autoFocus:S,autoCapitalize:w,autoCorrect:C,maxLength:E,min:M,max:y,step:F,pattern:A,inputMode:I,onSelect:R,onFocus:k,onBlur:B,onKeyDown:x,onKeyUp:D,onKeyPress:V,onChange:z,onDragStart:_,size:N="small",className:P,inputClassName:T,disabled:W,readonly:O,containerTabIndex:U,startSlot:H,endSlot:K,reference:L,containerReference:Y,onContainerFocus:J,...$}=e,q=(0,a.filterProps)($,f),X={...(0,a.filterAriaProps)($),...(0,a.filterDataProps)($),id:t,title:s,role:o,tabIndex:i,placeholder:l,name:c,type:u,value:h,defaultValue:m,draggable:v,autoComplete:b,autoFocus:S,autoCapitalize:w,autoCorrect:C,maxLength:E,min:M,max:y,step:F,pattern:A,inputMode:I,onSelect:R,onFocus:k,onBlur:B,onKeyDown:x,onKeyUp:D,onKeyPress:V,onChange:z,onDragStart:_};return r.createElement(d.ControlSkeleton,{...q,disabled:W,readonly:O,tabIndex:U,className:n(g().container,P),size:N,ref:Y,onFocus:J,startSlot:H,middleSlot:r.createElement(p.MiddleSlot,null,r.createElement("input",{...X,className:n(g().input,g()[`size-${N}`],T,H&&g()["with-start-slot"],K&&g()["with-end-slot"]),disabled:W,readOnly:O,ref:L})),endSlot:K})}function b(e){e=(0,c.useControl)(e);const{disabled:t,autoSelectOnFocus:s,tabIndex:n=0,onFocus:a,onBlur:d,reference:p,containerReference:m=null}=e,g=(0,r.useRef)(null),f=(0,r.useRef)(null),[b,S]=(0,u.useFocus)(),w=t?void 0:b?-1:n,C=t?void 0:b?n:-1,{isMouseDown:E,handleMouseDown:M,handleMouseUp:y}=(0,h.useIsMouseDown)(),F=(0,
o.createSafeMulticastEventHandler)(S.onFocus,(function(e){s&&!E.current&&(0,i.selectAllContent)(e.currentTarget)}),a),A=(0,o.createSafeMulticastEventHandler)(S.onBlur,d),I=(0,r.useCallback)((e=>{g.current=e,p&&("function"==typeof p&&p(e),"object"==typeof p&&(p.current=e))}),[g,p]);return r.createElement(v,{...e,isFocused:b,containerTabIndex:w,tabIndex:C,onContainerFocus:function(e){f.current===e.target&&null!==g.current&&g.current.focus()},onFocus:F,onBlur:A,reference:I,containerReference:(0,l.useMergedRefs)([f,m]),onMouseDown:M,onMouseUp:y})}},21778:(e,t,s)=>{"use strict";s.d(t,{useControl:()=>a});var r=s(269842),n=s(383836);function a(e){const{onFocus:t,onBlur:s,intent:a,highlight:o,disabled:i}=e,[l,c]=(0,n.useFocus)(void 0,i),u=(0,r.createSafeMulticastEventHandler)(i?void 0:c.onFocus,t),h=(0,r.createSafeMulticastEventHandler)(i?void 0:c.onBlur,s);return{...e,intent:a||(l?"primary":"default"),highlight:o??l,onFocus:u,onBlur:h}}},603548:(e,t,s)=>{"use strict";s.d(t,{useIsMouseDown:()=>n});var r=s(50959);function n(){const e=(0,r.useRef)(!1),t=(0,r.useCallback)((()=>{e.current=!0}),[e]),s=(0,r.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:s}}},1811:(e,t,s)=>{"use strict";function r(e){null!==e&&e.setSelectionRange(0,e.value.length)}s.d(t,{selectAllContent:()=>r})},794087:(e,t,s)=>{"use strict";s.d(t,{InputWithError:()=>m});var r,n=s(50959),a=s(497754),o=s(34735),i=s(102691),l=s(481476),c=s(892106),u=s(579184),h=s(411224);!function(e){e.Large="large",e.Medium="medium"}(r||(r={}));const d={large:o.InputClasses.FontSizeLarge,medium:o.InputClasses.FontSizeMedium},p={attachment:u.anchors.top.attachment,targetAttachment:u.anchors.top.targetAttachment,attachmentOffsetY:-4};function m(e){const{className:t,inputClassName:s,stretch:r=!0,errorMessage:o,fontSizeStyle:u="large",endSlot:m,button:g,error:f,warning:v,innerLabel:b,inputReference:S,children:w,customErrorsAttachment:C,...E}=e,M=f&&void 0!==o?[o]:void 0,y=v&&void 0!==o?[o]:void 0,F=a(h.inputContainer,d[u],t),A=b?n.createElement(i.StartSlot,{className:h.innerLabel,interactive:!1},b):void 0,I=m||g||w?n.createElement(i.EndSlot,null,m,g,w):void 0;return n.createElement(l.FormInput,{...E,className:F,inputClassName:s,errors:M,warnings:y,hasErrors:f,hasWarnings:v,messagesPosition:c.MessagesPosition.Attached,customErrorsAttachment:{...p,...C},messagesRoot:"document",inheritMessagesWidthFromTarget:!0,disableMessagesRtlStyles:!0,iconHidden:!0,stretch:r,reference:S,startSlot:A,endSlot:I})}},576805:(e,t,s)=>{"use strict";s.d(t,{NumberInputView:()=>C});var r=s(50959),n=s(972535),a=s(794087),o=s(525388),i=s(497754),l=s(609838),c=s(878112),u=s(409174),h=s(602948),d=s(893236);function p(e){const t=i(d.control,d.controlIncrease),n=i(d.control,d.controlDecrease);return r.createElement(r.Fragment,null,void 0!==e.title&&r.createElement("div",{className:d.title},e.title),r.createElement("div",{className:d.controlWrapper},(e.defaultButtonsVisible||e.title)&&r.createElement(r.Fragment,null,r.createElement("button",{type:"button",tabIndex:-1,
"aria-label":l.t(null,void 0,s(539832)),className:t,onClick:e.increaseValue,onMouseDown:u.preventDefault},r.createElement(c.Icon,{icon:h,className:d.controlIcon})),r.createElement("button",{type:"button",tabIndex:-1,"aria-label":l.t(null,void 0,s(455319)),className:n,onClick:e.decreaseValue,onMouseDown:u.preventDefault},r.createElement(c.Icon,{icon:h,className:d.controlIcon})))))}var m=s(522224),g=s(383836),f=s(269842),v=s(180185);const b=[38],S=[40];var w;function C(e){const[t,s]=(0,m.useHover)(),[i,l]=(0,g.useFocus)(),c=(0,r.useRef)(null),u=(0,f.createSafeMulticastEventHandler)(l.onFocus,e.onFocus),h=(0,f.createSafeMulticastEventHandler)(l.onBlur,e.onBlur),d=(0,r.useCallback)((t=>{!e.disabled&&i&&(t.preventDefault(),t.deltaY<0?e.onValueByStepChange(1):e.onValueByStepChange(-1))}),[i,e.disabled,e.onValueByStepChange]);return r.createElement(a.InputWithError,{...s,id:e.id,name:e.name,pattern:e.pattern,borderStyle:e.borderStyle,fontSizeStyle:e.fontSizeStyle,value:e.value,className:e.className,inputClassName:e.inputClassName,autoComplete:e.autoComplete,button:function(){const{button:s,forceShowControls:a,disabled:o,title:l}=e,c=!o&&!n.mobiletouch&&(a||i||t);return o?void 0:r.createElement(r.Fragment,null,s??r.createElement(p,{increaseValue:w,decreaseValue:C,defaultButtonsVisible:c,title:l}))}(),disabled:e.disabled,placeholder:e.placeholder,innerLabel:e.innerLabel,endSlot:e.endSlot,containerReference:(0,o.useMergedRefs)([c,e.containerReference]),inputReference:e.inputReference,inputMode:e.inputMode,type:e.type,warning:e.warning,error:e.error,errorMessage:e.errorMessage,onClick:e.onClick,onFocus:u,onBlur:h,onChange:e.onValueChange,onKeyDown:function(t){if(e.disabled||0!==(0,v.modifiersFromEvent)(t.nativeEvent))return;let s=b,r=S;e.controlDecKeyCodes&&(r=r.concat(e.controlDecKeyCodes));e.controlIncKeyCodes&&(s=s.concat(e.controlIncKeyCodes));(r.includes(t.keyCode)||s.includes(t.keyCode))&&(t.preventDefault(),e.onValueByStepChange(r.includes(t.keyCode)?-1:1));e.onKeyDown&&e.onKeyDown(t)},onWheelNoPassive:d,stretch:e.stretch,intent:e.intent,highlight:e.highlight,highlightRemoveRoundBorder:e.highlightRemoveRoundBorder,autoSelectOnFocus:e.autoSelectOnFocus,customErrorsAttachment:e.customErrorsAttachment,tabIndex:e.tabIndex,"data-property-id":e["data-name"],"aria-current":e["aria-current"],"aria-controls":e["aria-controls"],readonly:e.readonly,noReadonlyStyles:e.noReadonlyStyles,"data-name":e["data-name"]});function w(){e.disabled||(c.current?.focus(),e.onValueByStepChange(1))}function C(){e.disabled||(c.current?.focus(),e.onValueByStepChange(-1))}}!function(e){e[e.Dec=-1]="Dec",e[e.Inc=1]="Inc"}(w||(w={}))},587125:(e,t,s)=>{"use strict";s.d(t,{NumberInput:()=>b});var r,n,a=s(50959),o=s(609838),i=s(960521),l=s(601227),c=s(576805),u=s(563223),h=s(822960),d=s(431520),p=s(467025);!function(e){e.Integer="integer",e.Float="float",e.Fractional="fractional"}(r||(r={})),function(e){e.Input="input",e.Step="step"}(n||(n={}));const m=new u.NumericFormatter({ignoreLocaleNumberFormat:!0
}),g=/^[-−]?[0-9]*$/,f=/^([-−]?([0-9]+\.?[0-9]*)|([-−]?[0-9]*))$/,v=/^([-−]?([0-9]+'?[0-9]*([0-9]+'?)[0-9]*)|([-−]?[0-9]*))$/;class b extends a.PureComponent{constructor(e){super(e),this._onFocus=e=>{this.setState({focused:!0}),this.props.onFocus&&this.props.onFocus(e)},this._onBlur=e=>{this.setState({focused:!1}),!1!==this.props.shouldApplyValueOnBlur&&(this.setState({displayValue:S(this.props,this.props.value)}),this.props.errorHandler&&this.props.errorHandler(!1)),this.props.onBlur&&this.props.onBlur(e)},this._onValueChange=e=>{const t=e.target.value,s=(0,d.stripLTRMarks)(t);if(void 0!==this.props.onEmptyString&&""===t&&this.props.onEmptyString(),"fractional"===this.props.mode&&!v.test(s))return;if("float"===this.props.mode&&!f.test(s))return;if("integer"===this.props.mode&&!g.test(s))return;const r=w(t,this.props.formatter),n=r.res?this._checkValueBoundaries(r.value):{isPassed:!1,msg:void 0},a=r.res&&!n.isPassed,o=r.res&&r.suggest&&!this.state.focused?r.suggest:t,i=a&&n.msg?n.msg:this._errMsg;this.setState({displayValue:o,errorMsg:i}),r.res&&n.isPassed&&this.props.onValueChange(r.value,"input"),this.props.errorHandler&&this.props.errorHandler(!r.res||a)},this._onValueByStepChange=e=>{const{roundByStep:t=!0,step:s=1,uiStep:r,min:n=s,formatter:a}=this.props,o=w(this.state.displayValue,a),l=r??s;let c=s;if(o.res){const r=new i.Big(o.value),a=r.minus(n).mod(s);let u=r.plus(e*l);!a.eq(0)&&t&&(u=u.plus((e>0?0:1)*l).minus(a)),c=u.toNumber()}const{isPassed:u,clampedValue:h}=this._checkValueBoundaries(c);c=u?c:h,this.setState({displayValue:S(this.props,c)}),this.props.onValueChange(c,"step"),this.props.errorHandler&&this.props.errorHandler(!1)};const{value:t}=e;this._errMsg=o.t(null,void 0,s(71300)),this.state={value:t,displayValue:S(e,t),focused:!1,errorMsg:this._errMsg}}render(){const{inputMode:e,errorMessage:t,autoSelectOnFocus:s,...r}=this.props;return a.createElement(c.NumberInputView,{...r,inputMode:e??this.state.inputMode,value:(0,d.startWithLTR)((0,d.stripLTRMarks)(this.state.displayValue)),errorMessage:t||this.state.errorMsg,onValueChange:this._onValueChange,onValueByStepChange:this._onValueByStepChange,onFocus:this._onFocus,onBlur:this._onBlur,autoSelectOnFocus:void 0!==s?s:!l.CheckMobile.any()})}componentDidMount(){this.setState({inputMode:l.CheckMobile.iOS()?void 0:"numeric"})}componentDidUpdate(e){this.props.shouldApplyValueOnFormatterChange&&e.formatter!==this.props.formatter&&this.setState({displayValue:S(this.props,this.props.value)})}getClampedValue(){const{min:e=-1/0,max:t=p.DEFAULT_MAX}=this.props,s=w(this.state.displayValue,this.props.formatter);return s.res?(0,h.clamp)(s.value,e,t):null}static getDerivedStateFromProps(e,t){const{alwaysUpdateValueFromProps:s,value:r,forceApplyValueFromProps:n}=e;return t.focused&&!s||t.value===r&&!n?null:{value:r,displayValue:S(e,r)}}_checkValueBoundaries(e){const{min:t,max:s,boundariesErrorMessages:r}=this.props;return(0,p.checkValueBoundaries)({value:e,min:t,max:s,boundariesErrorMessages:r})}}function S(e,t){const{useFormatter:s=!0,formatter:r,mode:n}=e
;return s&&"integer"!==n?function(e,t=m){return null!==e?t.format(e):""}(t,r):function(e){if(null===e)return"";return u.NumericFormatter.formatNoE(e)}(t)}function w(e,t=m){return t.parse?t.parse(e):{res:!1,error:"Formatter does not support parse"}}},579184:(e,t,s)=>{"use strict";s.d(t,{anchors:()=>a,makeAnchorable:()=>o});var r,n=s(50959);!function(e){e.Top="top",e.Bottom="bottom",e.TopRight="topRight"}(r||(r={}));const a={bottom:{attachment:{horizontal:"left",vertical:"top"},targetAttachment:{horizontal:"left",vertical:"bottom"}},top:{attachment:{horizontal:"left",vertical:"bottom"},targetAttachment:{horizontal:"left",vertical:"top"}},topRight:{attachment:{horizontal:"right",vertical:"bottom"},targetAttachment:{horizontal:"right",vertical:"top"}},bottomRight:{attachment:{horizontal:"right",vertical:"top"},targetAttachment:{horizontal:"right",vertical:"bottom"}}};function o(e){var t;return(t=class extends n.PureComponent{render(){const{anchor:t="bottom"}=this.props;return n.createElement(e,{...this.props,attachment:a[t].attachment,targetAttachment:a[t].targetAttachment})}}).displayName="Anchorable Component",t}},841037:(e,t,s)=>{"use strict";s.d(t,{makeAttachable:()=>a});var r=s(50959),n=s(632227);function a(e){var t;return(t=class extends r.PureComponent{constructor(e){super(e),this._getComponentInstance=e=>{this._instance=e},this._throttleCalcProps=()=>{requestAnimationFrame((()=>this.setState(this._calcProps(this.props))))},this.state=this._getStateFromProps()}componentDidMount(){this._instanceElem=n.findDOMNode(this._instance),this.props.attachOnce||this._subscribe(),this.setState(this._calcProps(this.props))}componentDidUpdate(e){e.children===this.props.children&&e.top===this.props.top&&e.left===this.props.left&&e.width===this.props.width||this.setState(this._getStateFromProps(),(()=>this.setState(this._calcProps(this.props))))}render(){return r.createElement("div",{style:{position:"absolute",width:"100%",top:0,left:0}},r.createElement(e,{...this.props,ref:this._getComponentInstance,top:this.state.top,bottom:void 0!==this.state.bottom?this.state.bottom:"auto",right:void 0!==this.state.right?this.state.right:"auto",left:this.state.left,width:this.state.width,maxWidth:this.state.maxWidth},this.props.children))}componentWillUnmount(){this._unsubsribe()}_getStateFromProps(){return{bottom:this.props.bottom,left:this.props.left,right:this.props.right,top:void 0!==this.props.top?this.props.top:-1e4,width:this.props.inheritWidthFromTarget?this.props.target&&this.props.target.getBoundingClientRect().width:this.props.width,maxWidth:this.props.inheritMaxWidthFromTarget&&this.props.target&&this.props.target.getBoundingClientRect().width}}_calcProps(e){if(e.target&&e.attachment&&e.targetAttachment){const t=this._calcTargetProps(e.target,e.attachment,e.targetAttachment);if(null===t)return{};const{width:s,inheritWidthFromTarget:r=!0,inheritMaxWidthFromTarget:n=!1}=this.props,a={width:r?t.width:s,maxWidth:n?t.width:void 0};switch(e.attachment.vertical){case"bottom":case"middle":a.top=t.y;break;default:
a[e.attachment.vertical]=t.y}switch(e.attachment.horizontal){case"right":case"center":a.left=t.x;break;default:a[e.attachment.horizontal]=t.x}return a}return{}}_calcTargetProps(e,t,s){const r=e.getBoundingClientRect(),n=this._instanceElem.getBoundingClientRect(),a="parent"===this.props.root?this._getCoordsRelToParentEl(e,r):this._getCoordsRelToDocument(r);if(null===a)return null;const o=this._getDimensions(n),i=this._getDimensions(r).width;let l=0,c=0;switch(t.vertical){case"top":c=a[s.vertical];break;case"bottom":c=a[s.vertical]-o.height;break;case"middle":c=a[s.vertical]-o.height/2}switch(t.horizontal){case"left":l=a[s.horizontal];break;case"right":l=a[s.horizontal]-o.width;break;case"center":l=a[s.horizontal]-o.width/2}return"number"==typeof this.props.attachmentOffsetY&&(c+=this.props.attachmentOffsetY),"number"==typeof this.props.attachmentOffsetX&&(l+=this.props.attachmentOffsetX),{x:l,y:c,width:i}}_getCoordsRelToDocument(e){const t=pageYOffset,s=pageXOffset,r=e.top+t,n=e.bottom+t,a=e.left+s;return{top:r,bottom:n,left:a,right:e.right+s,middle:(r+e.height)/2,center:a+e.width/2}}_getCoordsRelToParentEl(e,t){const s=e.offsetParent;if(null===s)return null;const r=s.scrollTop,n=s.scrollLeft,a=e.offsetTop+r,o=e.offsetLeft+n,i=t.width+o;return{top:a,bottom:t.height+a,left:o,right:i,middle:(a+t.height)/2,center:(o+t.width)/2}}_getDimensions(e){return{height:e.height,width:e.width}}_subscribe(){"document"===this.props.root&&(window.addEventListener("scroll",this._throttleCalcProps,!0),window.addEventListener("resize",this._throttleCalcProps))}_unsubsribe(){window.removeEventListener("scroll",this._throttleCalcProps,!0),window.removeEventListener("resize",this._throttleCalcProps)}}).displayName="Attachable Component",t}},481476:(e,t,s)=>{"use strict";s.d(t,{FormInput:()=>c});var r=s(50959),n=s(654936),a=s(892106),o=s(102691),i=s(269842),l=s(525388);function c(e){const{intent:t,onFocus:s,onBlur:c,onMouseOver:u,onMouseOut:h,containerReference:d=null,endSlot:p,hasErrors:m,hasWarnings:g,hasSuccessMessages:f,errors:v,warnings:b,successMessages:S,alwaysShowAttachedErrors:w,iconHidden:C,messagesPosition:E,messagesAttachment:M,customErrorsAttachment:y,messagesRoot:F,inheritMessagesWidthFromTarget:A,disableMessagesRtlStyles:I,"aria-required":R,"aria-invalid":k,"aria-label":B,inputDescription:x,...D}=e,V=(0,a.useControlValidationLayout)({hasErrors:m,hasWarnings:g,hasSuccessMessages:f,errors:v,warnings:b,successMessages:S,alwaysShowAttachedErrors:w,iconHidden:C,messagesPosition:E,messagesAttachment:M,customErrorsAttachment:y,messagesRoot:F,inheritMessagesWidthFromTarget:A,disableMessagesRtlStyles:I,inputDescription:x}),z=(0,i.createSafeMulticastEventHandler)(s,V.onFocus),_=(0,i.createSafeMulticastEventHandler)(c,V.onBlur),N=(0,i.createSafeMulticastEventHandler)(u,V.onMouseOver),P=(0,i.createSafeMulticastEventHandler)(h,V.onMouseOut);return r.createElement(r.Fragment,null,r.createElement(n.InputControl,{...D,intent:V.intent??t,onFocus:z,onBlur:_,onMouseOver:N,onMouseOut:P,containerReference:(0,
l.useMergedRefs)([d,V.containerReference]),endSlot:r.createElement(r.Fragment,null,V.icon&&r.createElement(o.EndSlot,{icon:!0},V.icon),p),"aria-required":R,"aria-invalid":k,"aria-describedby":V.ariaIds,"aria-label":B}),V.renderedErrors)}},892106:(e,t,s)=>{"use strict";s.d(t,{MessagesPosition:()=>E,useControlValidationLayout:()=>x});var r=s(50959),n=s(497754);function a(e,t){(0,r.useEffect)((()=>(t&&t(e),()=>{t&&t(e)})),[])}var o=s(383836),i=s(522224),l=s(102691),c=s(579184),u=s(874485),h=s(841037),d=s(332965),p=s(431520);class m extends r.PureComponent{render(){const{children:e=[],show:t=!1,customErrorClass:s,disableRtlStyles:a,messageIdCallback:o}=this.props,i=n(d.errors,{[d.show]:t},s),l=e.map(((e,t)=>r.createElement(f,{key:t,messageIdCallback:o},e)));let c={position:"absolute",top:this.props.top,width:this.props.width,height:this.props.height,bottom:void 0!==this.props.bottom?this.props.bottom:"100%",right:void 0!==this.props.right?this.props.right:0,left:this.props.left,zIndex:this.props.zIndex,maxWidth:this.props.maxWidth};if((0,p.isRtl)()&&!a){const{left:e,right:t}=c;c={...c,left:t,right:e}}return r.createElement("div",{style:c,className:i},l)}}const g=(0,u.makeOverlapable)((0,h.makeAttachable)(m));function f(e){const{children:t,messageIdCallback:s,...n}=e;return r.createElement("div",{...n,className:d.error},t)}function v(e){const{children:t,messageIdCallback:s,...n}=e,o=r.useId();return a(o,s),r.createElement("span",{...n,className:d["visually-hidden"],id:o},t)}var b=s(878112),S=s(616658),w=s(562996);function C(e){const{intent:t="danger"}=e;return r.createElement(b.Icon,{icon:S,className:n(w["error-icon"],w[`intent-${t}`])})}var E,M,y=s(41437);!function(e){e[e.Attached=0]="Attached",e[e.Static=1]="Static",e[e.Hidden=2]="Hidden"}(E||(E={})),function(e){e.Top="top",e.Bottom="bottom"}(M||(M={}));const F={top:{attachment:c.anchors.topRight.attachment,targetAttachment:c.anchors.topRight.targetAttachment,attachmentOffsetY:-4},bottom:{attachment:c.anchors.bottomRight.attachment,targetAttachment:c.anchors.bottomRight.targetAttachment,attachmentOffsetY:4}};function A(e){const{isOpened:t,target:s,errorAttachment:n=M.Top,customErrorsAttachment:a,root:o="parent",inheritWidthFromTarget:i=!1,disableRtlStyles:l,children:c,messageIdCallback:u}=e,{attachment:h,targetAttachment:d,attachmentOffsetY:p}=a??F[n];return r.createElement(r.Fragment,null,r.createElement(g,{isOpened:t,target:a?.target??s,root:o,inheritWidthFromTarget:i,attachment:h,targetAttachment:d,attachmentOffsetY:p,disableRtlStyles:l,messageIdCallback:u,inheritMaxWidthFromTarget:!0,show:!0},c),r.createElement(v,{messageIdCallback:u},c))}function I(e,t){return Boolean(e)&&void 0!==t&&t.length>0}function R(e,t,s){return e===E.Attached&&I(t,s)}function k(e,t,s){return e===E.Static&&I(t,s)}function B(e,t,s){
const{hasErrors:r,hasWarnings:n,hasSuccessMessages:a,alwaysShowAttachedErrors:o,iconHidden:i,errors:l,warnings:c,successMessages:u,messagesPosition:h=E.Static}=e,d=R(h,r,l),p=R(h,n,c),m=d&&(t||s||Boolean(o)),g=!m&&p&&(t||s),f=k(h,r,l),v=!f&&k(h,n,c),b=!f&&!v&&k(h,a,u),S=!i&&Boolean(r),w=!i&&!S&&Boolean(n),C=function(e,t,s){return Boolean(s)?"success":Boolean(e)?"danger":Boolean(t)?"warning":void 0}(r,n,a);return{hasAttachedErrorMessages:d,hasAttachedWarningMessages:p,showAttachedErrorMessages:m,showAttachedWarningMessages:g,showStaticErrorMessages:f,showStaticWarningMessages:v,showStaticSuccessMessages:b,showErrorIcon:S,showWarningIcon:w,intent:C}}function x(e){const{errors:t,warnings:s,successMessages:a,messagesAttachment:c,customErrorsAttachment:u,messagesRoot:h,inheritMessagesWidthFromTarget:d,disableMessagesRtlStyles:p,inputDescription:m}=e,[g,f]=(0,o.useFocus)(),[v,b]=(0,i.useHover)(),S=(0,r.useRef)(null),[w,E]=r.useState(void 0),M=(0,r.useRef)(new Map),F=r.useCallback((e=>{if(!e)return;const t=M.current;t.has(e)?t.delete(e):t.set(e,e),0!==t.size?E(Array.from(t.keys()).join(" ")):E(void 0)}),[E,M.current]),{hasAttachedErrorMessages:I,hasAttachedWarningMessages:R,showAttachedErrorMessages:k,showAttachedWarningMessages:x,showStaticErrorMessages:V,showStaticWarningMessages:z,showStaticSuccessMessages:_,showErrorIcon:N,showWarningIcon:P,intent:T}=B(e,g,v),W=N||P?r.createElement(C,{intent:N?"danger":"warning"}):void 0,O=I?r.createElement(A,{errorAttachment:c,customErrorsAttachment:u,isOpened:k,target:S.current,root:h,inheritWidthFromTarget:d,disableRtlStyles:p,children:t,messageIdCallback:F}):void 0,U=R?r.createElement(A,{errorAttachment:c,isOpened:x,target:S.current,root:h,inheritWidthFromTarget:d,disableRtlStyles:p,children:s,messageIdCallback:F}):void 0,H=V?r.createElement(l.AfterSlot,{className:n(y["static-messages"],y.errors)},t?.map(((e,t)=>r.createElement(D,{key:t,messageIdCallback:F},e)))):void 0,K=z?r.createElement(l.AfterSlot,{className:n(y["static-messages"],y.warnings)},s?.map(((e,t)=>r.createElement(D,{key:t,messageIdCallback:F},e)))):void 0,L=_?r.createElement(l.AfterSlot,{className:n(y["static-messages"],y["success-mesages"])},a?.map(((e,t)=>r.createElement(D,{key:t,messageIdCallback:F},e)))):void 0,Y=!V&&!z&&!_&&m?r.createElement(l.AfterSlot,{className:n(y["static-messages"],y["input-description"])},r.createElement(D,{messageIdCallback:F},m)):void 0;return{ariaIds:w,icon:W,renderedErrors:O??U??H??K??L??Y,containerReference:S,intent:T,...f,...b}}function D(e){const{children:t,messageIdCallback:s,...n}=e,o=r.useId();return a(o,s),r.createElement("span",{...n,className:y.message,"aria-live":"assertive",id:o},t)}},616658:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M9 16A7 7 0 1 0 9 2a7 7 0 0 0 0 14Zm0 1A8 8 0 1 0 9 1a8 8 0 0 0 0 16ZM8 5a1 1 0 0 1 2 0v4a1 1 0 1 1-2 0V5Zm1 7a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"/></svg>'}}]);