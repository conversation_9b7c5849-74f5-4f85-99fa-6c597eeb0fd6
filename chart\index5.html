
<!DOCTYPE html>
<html>
	<head>
		<title>TradingView Trading Platform demo</title>

		<!-- Fix for iOS Safari zooming bug -->
		<meta
			name="viewport"
			content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0"
		/>

		<script
			type="text/javascript"
			src="charting_library/charting_library.standalone.js"
		></script>
		<script type="text/javascript" src="datafeeds/tv-datafeed.js"></script>
		<script type="text/javascript" src="broker-sample/dist/bundle.js"></script>
		<!-- Load Open Interest Custom Indicators -->
		<script type="text/javascript" src="open_interest_indicator.js"></script>
		<script type="text/javascript" src="simple_open_interest.js"></script>
		<!-- Load Candlestick Pattern Indicators -->
		<script type="text/javascript" src="candlestick_patterns_indicator.js"></script>
		<script type="text/javascript" src="candlestick_patterns_pine.js"></script>
		<script type="text/javascript" src="simple_candlestick_patterns.js"></script>
		<!-- Socket.IO for WebSocket connectivity -->
		<script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>

		<script type="text/javascript">
			window.studies={};
			// Create a dynamic sources object that can be updated
			window.sourcesConfig = {
				_sources: ['open','high','low','close','hl2','hlc3','ohlc4','volume','oi'],
				_indicatorSources: new Map(), // Map to track indicator sources
				get sources() {
					return this._sources;
				},
				set sources(newSources) {
					console.log('Updating sources from:', this._sources, 'to:', newSources);
					this._sources = newSources;
					console.log('Sources updated to:', this._sources);
				},
				add: function(source) {
					if (!this._sources.includes(source)) {
						this._sources.push(source);
						console.log('Added source:', source, 'Current sources:', this._sources);
					}
				},
				remove: function(source) {
					const index = this._sources.indexOf(source);
					if (index > -1) {
						this._sources.splice(index, 1);
						console.log('Removed source:', source, 'Current sources:', this._sources);
					}
				},
				// Add indicator sources when indicators are created
				addIndicatorSource: function(indicatorId, indicatorName, outputs = []) {
					if (!outputs.length) {
						// Default outputs for common indicators
						outputs = this.getDefaultOutputs(indicatorName);
					}

					this._indicatorSources.set(indicatorId, {
						name: indicatorName,
						outputs: outputs
					});

					// Add each output as a source
					outputs.forEach(output => {
						const sourceName = `${indicatorName}: ${output}`;
						this.add(sourceName);
					});

					console.log(`📊 Added indicator sources for ${indicatorName}:`, outputs);
				},
				// Remove indicator sources when indicators are removed
				removeIndicatorSource: function(indicatorId) {
					const indicator = this._indicatorSources.get(indicatorId);
					if (indicator) {
						// Remove each output from sources
						indicator.outputs.forEach(output => {
							const sourceName = `${indicator.name}: ${output}`;
							this.remove(sourceName);
						});

						this._indicatorSources.delete(indicatorId);
						console.log(`📊 Removed indicator sources for ${indicator.name}`);
					}
				},
				// Get default outputs for common indicators
				getDefaultOutputs: function(indicatorName) {
					const outputMap = {
						'RSI': ['RSI'],
						'Relative Strength Index': ['RSI'],
						'MACD': ['MACD', 'Signal', 'Histogram'],
						'Moving Average': ['MA'],
						'Moving Average Exponential': ['EMA'],
						'Moving Average Weighted': ['WMA'],
						'Bollinger Bands': ['Upper Band', 'Middle Band', 'Lower Band'],
						'Stochastic': ['%K', '%D'],
						'Williams %R': ['%R'],
						'Volume': ['Volume'],
						'ATR': ['ATR'],
						'CCI': ['CCI'],
						'Momentum': ['Momentum'],
						'ROC': ['ROC'],
						'Aroon': ['Aroon Up', 'Aroon Down'],
						'ADX': ['ADX', '+DI', '-DI'],
						'Parabolic SAR': ['SAR'],
						'Ichimoku Cloud': ['Tenkan', 'Kijun', 'Senkou A', 'Senkou B'],
						'VWAP': ['VWAP'],
						'Awesome Oscillator': ['AO'],
						'Chaikin Money Flow': ['CMF'],
						'Money Flow Index': ['MFI'],
						'On Balance Volume': ['OBV'],
						'Price Volume Trend': ['PVT'],
						'Accumulation/Distribution': ['A/D'],
						'Force Index': ['FI'],
						'Ease of Movement': ['EMV'],
						'Trix': ['TRIX'],
						'Ultimate Oscillator': ['UO'],
						'Vortex Indicator': ['VI+', 'VI-'],
						'Open Interest': ['OI'],
						'Flexible SMA': ['SMA'],
						'Flexible RSI': ['RSI']
					};

					return outputMap[indicatorName] || [indicatorName];
				}
			};

			// For backward compatibility
			window.sources = window.sourcesConfig.sources;
			function getParameterByName(name) {
				name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
				var regex = new RegExp('[\\?&]' + name + '=([^&#]*)'),
					results = regex.exec(location.search);
				return results === null
					? ''
					: decodeURIComponent(results[1].replace(/\+/g, ' '));
			}

			const codeSvg = (style) => `<svg style="${style}" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.93556 4.5061L3.43556 8.5061L3.00342 8.99998L3.43556 9.49386L6.93556 13.4939L8.06443 12.5061L4.99657 8.99998L8.06443 5.49386L6.93556 4.5061ZM11.0644 13.4939L14.5644 9.49386L14.9966 8.99998L14.5644 8.5061L11.0644 4.5061L9.93556 5.49386L13.0034 8.99998L9.93556 12.5061L11.0644 13.4939Z" fill="currentColor"/></svg>`;
			const customCSS = `#documentation-toolbar-button {
				all: unset;
				position: relative;
				color: #FFF;
				font-size: 14px;
				font-weight: 400;
				line-height: 18px;
				letter-spacing: 0.15408px;
				padding: 5px 12px;
				border-radius: 80px;
				background: #2962FF;
				cursor: pointer;
			}
			#documentation-toolbar-button:hover {
				background: #1E53E5;
			}
			#documentation-toolbar-button:active {
				background: #1948CC;
			}
			#theme-toggle {
				display: flex;
				flex-direction: row;
				align-items: center;
				gap: 12px;
			}
			.switcher {
				display: inline-block;
				position: relative;
				flex: 0 0 auto;
				width: 38px;
				height: 20px;
				vertical-align: middle;
				z-index: 0;
				-webkit-tap-highlight-color: transparent;
			}

			.switcher input {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				opacity: 0;
				z-index: 1;
				cursor: default;
			}

			.switcher .thumb-wrapper {
				display: block;
				border-radius: 20px;
				position: relative;
				z-index: 0;
				width: 100%;
				height: 100%;
			}

			.switcher .track {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				border-radius: 20px;
				background-color: #a3a6af;
			}

			#theme-switch:checked + .thumb-wrapper .track {
				background-color: #2962ff;
			}

			.switcher .thumb {
				display: block;
				width: 14px;
				height: 14px;
				border-radius: 14px;
				transition-duration: 250ms;
				transition-property: transform;
				transition-timing-function: ease-out;
				transform: translate(3px, 3px);
				background: #ffffff;
			}

			[dir=rtl] .switcher .thumb {
				transform: translate(-3px, 3px);
			}

			.switcher input:checked + .thumb-wrapper .thumb {
				transform: translate(21px, 3px);
			}

			[dir=rtl] .switcher input:checked + .thumb-wrapper .thumb {
				transform: translate(-21px, 3px);
			}

			#documentation-toolbar-button:focus-visible:before,
			.switcher:focus-within:before {
				content: '';
				display: block;
				position: absolute;
				top: -2px;
				right: -2px;
				bottom: -2px;
				left: -2px;
				border-radius: 16px;
				outline: #2962FF solid 2px;
			}

			/* Custom styling for exchange icons in search results */
			.exchangeCell-oRSs8UQo .cell-oRSs8UQo {
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
			}

			.exchangeCell-oRSs8UQo .cell-oRSs8UQo::before {
				content: '';
				width: 20px !important;
				height: 20px !important;
				background-size: contain !important;
				background-repeat: no-repeat !important;
				background-position: center !important;
				display: inline-block !important;
				margin-right: 4px !important;
			}

			/* Exchange specific icons */
			.exchangeCell-oRSs8UQo .cell-oRSs8UQo[data-exchange="NSE"]::before,
			.exchangeCell-oRSs8UQo .cell-oRSs8UQo[data-exchange="NFO"]::before {
				background-image: url('./data/svg/nse.svg') !important;
			}

			.exchangeCell-oRSs8UQo .cell-oRSs8UQo[data-exchange="BSE"]::before,
			.exchangeCell-oRSs8UQo .cell-oRSs8UQo[data-exchange="BFO"]::before {
				background-image: url('./data/svg/bse.svg') !important;
			}

			.exchangeCell-oRSs8UQo .cell-oRSs8UQo[data-exchange="MCX"]::before {
				background-image: url('./data/svg/mcx.svg') !important;
			}

			.exchangeCell-oRSs8UQo .cell-oRSs8UQo[data-exchange="CDS"]::before {
				background-image: url('./data/svg/cds.svg') !important;
			}

			/* Hide text in exchange column, show only icon */
			.exchangeCell-oRSs8UQo .cell-oRSs8UQo {
				font-size: 0 !important;
			}

			.exchangeCell-oRSs8UQo .cell-oRSs8UQo::after {
				content: attr(data-exchange) !important;
				font-size: 11px !important;
				color: var(--tv-color-popup-element-text, #131722) !important;
				margin-left: 2px !important;
			}

			/* Fix for button click issues */
			.buttonsWrapper-l31H9iuA,
			[data-name="actions"],
			.button-D4RPB3ZC,
			[role="button"] {
				pointer-events: auto !important;
				cursor: pointer !important;
				z-index: 999 !important;
			}

			.buttonsWrapper-l31H9iuA button,
			[data-name="actions"] button,
			.button-D4RPB3ZC {
				pointer-events: auto !important;
				cursor: pointer !important;
				position: relative !important;
				z-index: 1000 !important;
			}

			/* Ensure settings button is clickable */
			[data-name="settings"],
			[title*="Settings"],
			[title*="Chart settings"] {
				pointer-events: auto !important;
				cursor: pointer !important;
				z-index: 9999 !important;
				position: relative !important;
			}

			/* Remove any overlay that might block clicks */
			.buttonsWrapper-l31H9iuA::before,
			.buttonsWrapper-l31H9iuA::after {
				pointer-events: none !important;
			}

			/* ========================================
			   INDICATOR MANAGER UI STYLES
			   ======================================== */
			#indicator-management-ui {
				font-size: 14px;
				line-height: 1.4;
			}

			#indicator-management-ui h4 {
				font-size: 16px;
				font-weight: 600;
				margin: 0 0 12px 0;
			}

			#indicator-management-ui select,
			#indicator-management-ui button {
				font-size: 13px;
				transition: all 0.2s ease;
			}

			#indicator-management-ui button:hover {
				opacity: 0.9;
				transform: translateY(-1px);
			}

			#indicator-management-ui select:focus,
			#indicator-management-ui button:focus {
				outline: 2px solid #2196F3;
				outline-offset: 2px;
			}

			/* Floating button animation */
			#indicator-manager-btn {
				animation: pulse 2s infinite;
			}

			@keyframes pulse {
				0% { box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3); }
				50% { box-shadow: 0 2px 8px rgba(33, 150, 243, 0.6); }
				100% { box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3); }
			}

			/* Scrollbar styling for indicator UI */
			#indicator-management-ui ::-webkit-scrollbar {
				width: 6px;
			}

			#indicator-management-ui ::-webkit-scrollbar-track {
				background: #2a2e39;
			}

			#indicator-management-ui ::-webkit-scrollbar-thumb {
				background: #3a3e4a;
				border-radius: 3px;
			}

			#indicator-management-ui ::-webkit-scrollbar-thumb:hover {
				background: #4a4e5a;
			}

			/* Indicator list item styling */
			#current-indicators-list > div {
				transition: all 0.2s ease;
			}

			#current-indicators-list > div:hover {
				background: #3a3e4a !important;
				transform: translateX(2px);
			}`;

			async function fetchNews() {
				const response = await fetch('https://demo-feed-data.tradingview.com/tv_news');
				const xml = await response.text();
				const parser = new DOMParser();
				const dom = parser.parseFromString(xml, 'application/xml');
				const items = dom.querySelectorAll('item');

				return Array.from(items).map(item => {
					const title = item.querySelector('title').textContent;
					const link = item.querySelector('link').textContent;
					const description = item.querySelector('description')?.textContent ?? '';
					const pubDate = item.querySelector('pubDate').textContent;
					const contentNode = Array.from(item.childNodes).find(el => el.tagName === 'content:encoded');
					let decodedContent = '';
					if (contentNode) {
						const tempElement = document.createElement("div");
						tempElement.innerHTML = contentNode.textContent ?? '';
						decodedContent = tempElement.innerText;
					}
					return {
						// fullDescription: decodedContent,
						link,
						published: new Date(pubDate).valueOf(),
						shortDescription: decodedContent ? (decodedContent.slice(0, 150) + '...') : '',
						source: 'TradingView',
						title,
					};
				});
			}
		
			function initOnReady() {
				const cssBlob = new Blob([customCSS], {
					type: "text/css",
				});
				const cssBlobUrl = URL.createObjectURL(cssBlob);

				// WebSocket connection for live data
				let liveDataSocket = null;
				let subscribedSymbols = new Map(); // Map of subscriberUID -> symbolInfo
				let realtimeCallbacks = new Map(); // Map of subscriberUID -> callback

				// Initialize WebSocket connection
				function initLiveDataWebSocket() {
					try {
						liveDataSocket = io('http://localhost:5001');

						liveDataSocket.on('connect', () => {
							//console.log('🔌 Connected to Live Data Server');
						});

						liveDataSocket.on('disconnect', () => {
							//console.log('🔌 Disconnected from Live Data Server');
						});

						liveDataSocket.on('price_update', (data) => {
							//console.log('📊 Live price update:', data);

							// Find matching subscriptions and trigger callbacks
							for (let [subscriberUID, callback] of realtimeCallbacks) {
								const symbolInfo = subscribedSymbols.get(subscriberUID);
								if (symbolInfo && symbolInfo.instrument_key === data.instrument_key) {
									// Create TradingView bar format for real-time update
									const realtimeBar = {
										time: Math.floor(data.timestamp * 1000), // Convert to milliseconds
										open: data.last_price, // For real-time, we only update close/LTP
										high: data.last_price,
										low: data.last_price,
										close: data.last_price,
										volume: 0 // Volume not available in LTP
									};

									//console.log('📈 Triggering real-time callback for:', symbolInfo.symbol, realtimeBar);
									callback(realtimeBar);
								}
							}
						});

						liveDataSocket.on('error', (error) => {
							console.error('❌ WebSocket error:', error);
						});

					} catch (error) {
						console.error('❌ Failed to initialize WebSocket:', error);
					}
				}

				// Custom Datafeed with CSV integration and live data
				class SampleDatafeed {
					constructor() {
						this.symbolNames = new Map(); // Store symbol names
						this.configuration = {
							supports_marks: false,
							supports_timescale_marks: true,
							supported_resolutions: ['1', '3', '5', '15', '30', '45', '60', '120', '180', '240', '1D', '1W', '1M'],
							exchanges: [
								{value: '', name: 'All Exchanges', desc: ''},
								{value: 'NSE', name: 'NSE', desc: 'National Stock Exchange'},
								{value: 'BSE', name: 'BSE', desc: 'Bombay Stock Exchange'},
								{value: 'MCX', name: 'MCX', desc: 'Multi Commodity Exchange'},
								{value: 'NFO', name: 'NFO', desc: 'NSE F&O'},
								{value: 'BFO', name: 'BFO', desc: 'BSE F&O'},
								{value: 'CDS', name: 'CDS', desc: 'Currency Derivative Segment'}
							],
							symbols_types: [
								{name: 'All types', value: ''},
								{name: 'Stock', value: 'EQ'},
								{name: 'Stock Future', value: 'FUTSTK'},
								{name: 'Stock Option', value: 'OPTSTK'},
								{name: 'Index Future', value: 'FUTIDX'},
								{name: 'Index Option', value: 'OPTIDX'},
								{name: 'Commodity Future', value: 'FUTCOM'},
								{name: 'Commodity Option', value: 'OPTCOM'},
								{name: 'Currency Future', value: 'FUTCUR'},
								{name: 'Currency Option', value: 'OPTCUR'},
								{name: 'Index', value: 'IDX'}
							]
						};
						this._quotesSubscriptions={}
						this.csvSymbols = [];
						this.csvLoaded = false;
						this.logoCache = new Map(); // Cache for logos
						this.availableLogos = new Set(); // Pre-loaded logo list
						this.loadAvailableLogos(); // Load logo list first
						this.loadCSVSymbols();
					}

					// Pre-load available logo list (much faster than checking each file)
					async loadAvailableLogos() {
						try {
							//console.log('📋 Loading available logos list...');

							// Option 1: Try to load a logos.json file (recommended)
							try {
								const response = await fetch('./data/logos.json');
								if (response.ok) {
									const logosList = await response.json();
									this.availableLogos = new Set(logosList.map(name => name.toUpperCase()));
									//console.log(`✅ Loaded ${this.availableLogos.size} logos from logos.json`);
									return;
								}
							} catch (e) {
								//console.log('📋 logos.json not found, using fallback method');
							}

							// Option 2: Pre-defined popular logos list (fastest)
							const popularLogos = [
								'RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK', 'KOTAKBANK',
								'HINDUNILVR', 'ITC', 'LT', 'AXISBANK', 'BHARTIARTL', 'ASIANPAINT',
								'MARUTI', 'BAJFINANCE', 'HCLTECH', 'WIPRO', 'ULTRACEMCO', 'TITAN',
								'NESTLEIND', 'POWERGRID', 'NTPC', 'COALINDIA', 'TECHM', 'SUNPHARMA',
								'ONGC', 'TATAMOTORS', 'BAJAJFINSV', 'JSWSTEEL', 'TATACONSUM',
								'DRREDDY', 'GRASIM', 'INDUSINDBK', 'BRITANNIA', 'ADANIPORTS',
								'CIPLA', 'DIVISLAB', 'HINDALCO', 'TATASTEEL', 'BPCL', 'EICHERMOT',
								'HEROMOTOCO', 'BAJAJ-AUTO', 'SBILIFE', 'HDFCLIFE', 'IOC',
								'APOLLOHOSP', 'SHREECEM', 'PIDILITIND', 'GODREJCP', 'DABUR'
							];

							this.availableLogos = new Set(popularLogos);
							//console.log(`📋 Using ${this.availableLogos.size} pre-defined popular logos`);

						} catch (error) {
							console.error('❌ Error loading logos list:', error);
							this.availableLogos = new Set();
						}
					}

					// Parse CSV data with better error handling
					parseCSV(csvText) {
						try {
							//console.log('🔧 Parsing CSV data...');
							const lines = csvText.split('\n');
							//console.log(`📄 Total lines: ${lines.length}`);

							if (lines.length < 2) {
								throw new Error('CSV file appears to be empty or has no data rows');
							}

							const headers = lines[0].split(',').map(h => h.trim().replace(/['"]/g, ''));
							//console.log('📋 CSV Headers:', headers);

							const symbols = [];
							let successCount = 0;
							let errorCount = 0;

							for (let i = 1; i < lines.length; i++) {
								const line = lines[i].trim();
								if (line) {
									try {
										// Handle CSV with quotes and commas properly
										const values = this.parseCSVLine(line);

										if (values.length >= 7) { // At least basic fields
											const symbol = {
												exchange: values[0] || '',
												exchangeSegment: values[1] || '',
												symbolCode: values[2] || '',
												tradingSymbol: values[3] || '',
												name: values[4] || '',
												lastPrice: parseFloat(values[5]) || 0,
												instrument: values[6] || '',
												lotSize: parseInt(values[7]) || 1,
												strikePrice: parseFloat(values[8]) || 0,
												expiryDate: values[9] || '',
												tickSize: parseFloat(values[10]) || 0.01
											};

											// Only add valid symbols
											if (symbol.tradingSymbol && symbol.exchange) {
												const [logoUrl,fallBackURL] = this.getSymbolLogoFast({
													ticker: symbol.tradingSymbol,
													description: symbol.name,
													exchange: symbol.exchange
												});

												const exchangeLogoUrl = this.getExchangeLogo(symbol.exchange);
												const tvSymbol = {
													symbol: `${symbol.exchange}:${symbol.tradingSymbol}`,
													full_name: `${symbol.exchange}:${symbol.tradingSymbol}`,
													description: symbol.name || symbol.tradingSymbol,
													exchange: symbol.exchange,
													ticker: symbol.tradingSymbol,
													type: this.getSymbolType(symbol.instrument),
													session: '0915-1530',
													timezone: 'Asia/Kolkata',
													minmov: 1,
													pricescale: Math.round(1 / symbol.tickSize) || 100,
													has_intraday: true,
													has_no_volume: false,
													volume_precision: 0,
													data_status: 'streaming',
													instrument: symbol.instrument,
													lotSize: symbol.lotSize,
													lastPrice: symbol.lastPrice,
													// Symbol logo
													logo_urls: [logoUrl],
													// Exchange logo for search results
													exchange_logo: exchangeLogoUrl,
													// Additional properties for better display
													listed_exchange: symbol.exchange,
													original_exchange: symbol.exchange
												};

												symbols.push(tvSymbol);
												successCount++;
											}
										}
									} catch (e) {
										errorCount++;
										if (errorCount < 5) { // Log first few errors
											//console.log(`⚠️ Line ${i} parse error:`, e.message, 'Line:', line.substring(0, 100));
										}
									}
								}
							}

							//console.log(`✅ CSV parsing complete: ${successCount} successful, ${errorCount} errors`);
							//console.log('🔬 Sample parsed symbols:', symbols.slice(0, 3));

							return symbols;

						} catch (error) {
							console.error('❌ CSV parsing failed:', error);
							return [];
						}
					}

					// Parse NSE.json data
					parseNSEJson(nseData) {
						try {
							//console.log('🔧 Parsing NSE.json data...');
							//console.log(`📄 Total instruments: ${nseData.length}`);

							if (!Array.isArray(nseData) || nseData.length === 0) {
								throw new Error('NSE.json appears to be empty or invalid');
							}

							const symbols = [];
							let successCount = 0;
							let errorCount = 0;

							for (let i = 0; i < nseData.length; i++) {
								const instrument = nseData[i];

								try {
									if (instrument &&
										instrument.trading_symbol &&
										instrument.instrument_key &&
										instrument.segment) {

										// Create TradingView format symbol based on segment
										const tvSymbol = this.createTVSymbolFromNSE(instrument);

										if (tvSymbol) {
											symbols.push(tvSymbol);
											successCount++;
										}
									}
								} catch (e) {
									errorCount++;
									if (errorCount < 5) { // Log first few errors
										//console.log(`⚠️ Instrument ${i} parse error:`, e.message, 'Instrument:', instrument);
									}
								}
							}

							//console.log(`✅ NSE.json parsing complete: ${successCount} successful, ${errorCount} errors`);
							//console.log('🔬 Sample parsed symbols:', symbols.slice(0, 3));

							return symbols;

						} catch (error) {
							console.error('❌ NSE.json parsing failed:', error);
							return [];
						}
					}

					// Create TradingView symbol from NSE instrument
					createTVSymbolFromNSE(instrument) {
						const segment = instrument.segment;
						const tradingSymbol = instrument.trading_symbol;
						const instrumentType = instrument.instrument_type || '';
						const name = instrument.name || tradingSymbol;

						// Determine exchange and symbol format
						let exchange, symbol, type;

						switch (segment) {
							case 'NSE_EQ':
								exchange = 'NSE';
								symbol = `NSE:${tradingSymbol}-EQ`;
								type = 'EQ';
								break;

							case 'NSE_FO':
								exchange = 'NFO';
								if (instrumentType === 'FUT') {
									symbol = `NFO:${instrument.underlying_symbol || tradingSymbol}-FUT`;
									type = this.isIndexInstrument(instrument) ? 'FUTIDX' : 'FUTSTK';
								} else {
									symbol = `NFO:${tradingSymbol}`;
									type = this.isIndexInstrument(instrument) ? 'OPTIDX' : 'OPTSTK';
								}
								break;

							case 'NSE_COM':
								exchange = 'MCX';
								symbol = `MCX:${tradingSymbol}`;
								type = 'commodity';
								break;

							case 'NCD_FO':
								exchange = 'CDS';
								symbol = `CDS:${tradingSymbol}`;
								type = 'currency';
								break;

							default:
								exchange = 'NSE';
								symbol = `NSE:${tradingSymbol}`;
								type = 'EQ';
								break;
						}

						// Get logos
						const [logoUrl, fallBackURL] = this.getSymbolLogoFast({
							ticker: tradingSymbol,
							description: name,
							exchange: exchange
						});

						// //console.log(logoUrl)
						const exchangeLogoUrl = this.getExchangeLogo(exchange);
						// Create TradingView symbol object
						return {
							symbol: symbol,
							full_name: symbol,
							description: name,
							exchange: exchange,
							ticker: tradingSymbol,
							type: type,
							session: '0915-1530',
							timezone: 'Asia/Kolkata',
							minmov: 1,
							pricescale: Math.round(1 / (instrument.tick_size || 0.05)) || 100,
							has_intraday: true,
							has_no_volume: false,
							volume_precision: 0,
							data_status: 'streaming',
							instrument: instrumentType,
							lotSize: instrument.lot_size || 1,
							tickSize: instrument.tick_size || 0.05,
							segment: segment,
							instrument_key: instrument.instrument_key,
							// Symbol logo
							logo_urls: [logoUrl],
							// Exchange logo for search results
							exchange_logo: exchangeLogoUrl,
							// Additional properties for better display
							listed_exchange: exchange,
							original_exchange: exchange,
							// F&O specific data
							strike_price: instrument.strike_price || 0,
							expiry: instrument.expiry || null,
							underlying_symbol: instrument.underlying_symbol || null
						};
					}

					// Determine if instrument is index-based or stock-based
					isIndexInstrument(instrument) {
						if (!instrument || !instrument.trading_symbol) {
							return false;
						}

						const tradingSymbol = instrument.trading_symbol.toUpperCase();
						const underlyingSymbol = (instrument.underlying_symbol || '').toUpperCase();

						// List of known index symbols
						const indexSymbols = [
							'NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY', 'NIFTYNXT50',
							'NIFTYIT', 'NIFTYPHARMA', 'NIFTYBANK', 'NIFTYFMCG', 'NIFTYMETAL',
							'NIFTYAUTO', 'NIFTYREALTY', 'NIFTYPSE', 'NIFTYENERGY', 'NIFTYINFRA',
							'NIFTYMEDIA', 'NIFTYPVTBANK', 'SENSEX', 'BANKEX'
						];

						// Check if trading symbol or underlying symbol matches any index
						for (const indexSymbol of indexSymbols) {
							if (tradingSymbol.includes(indexSymbol) || underlyingSymbol.includes(indexSymbol)) {
								return true;
							}
						}

						return false;
					}

					// Better CSV line parsing to handle quotes and commas
					parseCSVLine(line) {
						const result = [];
						let current = '';
						let inQuotes = false;

						for (let i = 0; i < line.length; i++) {
							const char = line[i];

							if (char === '"') {
								inQuotes = !inQuotes;
							} else if (char === ',' && !inQuotes) {
								result.push(current.trim());
								current = '';
							} else {
								current += char;
							}
						}

						result.push(current.trim());
						return result;
					}

					// Get exchange logo URL
					getExchangeLogo(exchange) {
						if (!exchange) return './data/svg/nse.svg';

						switch(exchange.toUpperCase()) {
							case 'NSE':
							case 'NFO':
								return './data/svg/nse.svg';
							case 'BSE':
							case 'BFO':
								return './data/svg/bse.svg';
							case 'MCX':
								return './data/svg/mcx.svg';
							case 'CDS':
								return './data/svg/cds.svg';
							default:
								return './data/svg/nse.svg';
						}
					}

					getSymbolLogoFast(symbol) {
						// Get clean symbol name from CSV name field
						let cleanName = '';

						if (symbol.ticker && symbol.ticker.trim()) {
							cleanName = symbol.ticker.trim();
						} else if (symbol.ticker) {
							cleanName = symbol.ticker.replace(/-[A-Z]+$/, '');
						}

						// Clean the name
						cleanName = (symbol.exchange+'_'+cleanName
							.replace(/-EQ$|_EQ$|-BE$|_BE$|-SM$|_SM$|-BZ$|_BZ$/gi, '')
							.replace(/-FUT$|_FUT$|-OPT$|_OPT$/gi, '')
							.replace(/\s+/g, '')
							.replace(/[^a-zA-Z0-9]/g, ''))
							.toLowerCase();
						// //console.log(cleanName)
						// Check if logo exists in our pre-loaded list
						const hasLogo = this.availableLogos.has(cleanName);

						// Exchange fallback
						let exchangeLogo = './data/svg/nse.svg';
						if (symbol.exchange) {
							switch(symbol.exchange.toUpperCase()) {
								case 'BSE':
								case 'BFO':
									exchangeLogo = './data/svg/bse.svg';
									break;
								case 'MCX':
									exchangeLogo = './data/svg/mcx.svg';
									break;
								case 'CDS':
									exchangeLogo = './data/svg/cds.svg';
									break;
							}
						}

						// Return logo URL or fallback immediately
						return [`./data/svg/${cleanName}.svg` , exchangeLogo];
					}

					getSymbolType(instrument) {
						switch(instrument) {
							case 'EQ': return 'stock';
							case 'FUT': return 'futures';
							case 'FUTIDX': return 'futures';  // Index futures
							case 'OPT': return 'option';
							case 'OPTIDX': return 'option';   // Index options
							case 'IDX': return 'index';
							case 'FUTCOM': return 'futures'; // Commodity futures
							case 'OPTCOM': return 'option';  // Commodity options
							case 'FUTSTK': return 'futures'; // Stock futures
							case 'OPTSTK': return 'option';  // Stock options
							case 'FUTCUR': return 'futures'; // Currency futures
							case 'OPTCUR': return 'option';  // Currency options
							default: return 'stock';
						}
					}

					// Load NSE.json data
					async loadCSVSymbols() {
						try {
							//console.log('🔄 Loading NSE.json...');
							//console.log('🔍 Trying path: ./NSE.json');

							const response = await fetch('./NSE.json');
							//console.log('📡 Response status:', response.status, response.statusText);

							if (!response.ok) {
								//console.log('❌ NSE.json fetch failed. Trying alternative paths...');

								// Try alternative paths
								const altPaths = [
									'./data/NSE.json',
									'../NSE.json',
									'./json/NSE.json',
									'./assets/NSE.json'
								];

								for (const path of altPaths) {
									try {
										//console.log(`🔍 Trying: ${path}`);
										const altResponse = await fetch(path);
										if (altResponse.ok) {
											//console.log(`✅ Found NSE.json at: ${path}`);
											const jsonData = await altResponse.json();
											this.csvSymbols = this.parseNSEJson(jsonData);
											this.csvLoaded = true;
											//console.log(`📊 Loaded ${this.csvSymbols.length} symbols from ${path}`);
											return;
										}
									} catch (e) {
										//console.log(`❌ ${path} failed:`, e.message);
									}
								}

								throw new Error(`NSE.json not found in any path. Status: ${response.status}`);
							}

							const jsonData = await response.json();
							//console.log('📄 NSE.json loaded, instruments count:', jsonData.length);
							//console.log('📄 Sample instrument:', jsonData[0]);

							this.csvSymbols = this.parseNSEJson(jsonData);
							this.csvLoaded = true;

							//console.log(`📊 Successfully loaded ${this.csvSymbols.length} symbols from NSE.json`);
							//console.log('🔬 Sample symbols:', this.csvSymbols.slice(0, 3));

							// Log exchange distribution
							const exchangeCounts = {};
							this.csvSymbols.forEach(s => {
								exchangeCounts[s.exchange] = (exchangeCounts[s.exchange] || 0) + 1;
							});
							//console.log('📈 Exchange distribution:', exchangeCounts);

							// Log segment distribution
							const segmentCounts = {};
							this.csvSymbols.forEach(s => {
								segmentCounts[s.segment] = (segmentCounts[s.segment] || 0) + 1;
							});
							//console.log('📊 Segment distribution:', segmentCounts);

							// Log instrument type distribution
							const typeCounts = {};
							this.csvSymbols.forEach(s => {
								typeCounts[s.type] = (typeCounts[s.type] || 0) + 1;
							});
							//console.log('🎯 Instrument type distribution:', typeCounts);

						} catch (error) {
							console.error('❌ Error loading NSE.json:', error);
							//console.log('💡 Debug info:');
							//console.log('   - Check if NSE.json exists in chart folder');
							//console.log('   - Check browser console for CORS errors');
							//console.log('   - Try running from http server (not file://)');
							//console.log('   - Verify NSE.json is valid JSON format');
							this.csvSymbols = []; // Keep name for compatibility
							this.csvLoaded = false; // Keep name for compatibility
						}
					}

					// Convert resolution to minutes
					getResolutionInMinutes(resolution) {
						if (resolution.includes('D')) return parseInt(resolution) * 24 * 60;
						if (resolution.includes('W')) return parseInt(resolution) * 7 * 24 * 60;
						if (resolution.includes('M')) return parseInt(resolution) * 30 * 24 * 60;
						return parseInt(resolution);
					}

					// Get watchlist symbols from NSE.json data
					getWatchlistSymbols() {
						//console.log('📋 Getting watchlist symbols from NSE.json...');
						//console.log('📊 NSE.json Status - Loaded:', this.csvLoaded, 'Count:', this.csvSymbols ? this.csvSymbols.length : 0);

						// Default fallback symbols with proper display names
						const fallbackSymbols = [
							'###NIFTY INDICES',
							'Nifty 50|NSE_INDEX|Nifty 50',
							'Nifty Bank|NSE_INDEX|Nifty Bank',
							'Nifty IT|NSE_INDEX|Nifty IT',
							'###TOP STOCKS',
							'Reliance Industries Limited|NSE_EQ|INE848E01016',
							'Tata Consultancy Services Limited|NSE_EQ|INE467B01029',
							'Infosys Limited|NSE_EQ|INE009A01021',
							'HDFC Bank Limited|NSE_EQ|INE040A01034',
							'ICICI Bank Limited|NSE_EQ|INE090A01013',
							'Kotak Mahindra Bank Limited|NSE_EQ|INE200A01026',
							'###F&O STOCKS',
							'ITC Limited|NSE_EQ|INE002A01018',
							'Larsen & Toubro Limited|NSE_EQ|INE018A01030',
							'Axis Bank Limited|NSE_EQ|INE238A01034',
							'Bharti Airtel Limited|NSE_EQ|INE397D01024',
							'###SECTORAL',
							'Asian Paints Limited|NSE_EQ|INE239A01016',
							'Maruti Suzuki India Limited|NSE_EQ|INE585B01010',
							'Bajaj Finance Limited|NSE_EQ|INE296A01024',
							'HCL Technologies Limited|NSE_EQ|INE860A01027'
						];

						if (!this.csvLoaded || !this.csvSymbols || this.csvSymbols.length === 0) {
							//console.log('📋 NSE.json not loaded, using fallback watchlist symbols');
							return fallbackSymbols;
						}

						// Extract symbols from loaded NSE.json data
						const watchlistSymbols = [];
						const foundSymbols = new Map(); // Map to store symbol -> full name

						// Popular stocks to look for with their trading symbols
						const popularStocks = {
							'RELIANCE': { category: 'top', name: 'Reliance Industries Limited' },
							'TCS': { category: 'top', name: 'Tata Consultancy Services Limited' },
							'INFY': { category: 'top', name: 'Infosys Limited' },
							'HDFCBANK': { category: 'top', name: 'HDFC Bank Limited' },
							'ICICIBANK': { category: 'top', name: 'ICICI Bank Limited' },
							'KOTAKBANK': { category: 'top', name: 'Kotak Mahindra Bank Limited' },
							'ITC': { category: 'fo', name: 'ITC Limited' },
							'LT': { category: 'fo', name: 'Larsen & Toubro Limited' },
							'AXISBANK': { category: 'fo', name: 'Axis Bank Limited' },
							'BHARTIARTL': { category: 'fo', name: 'Bharti Airtel Limited' },
							'ASIANPAINT': { category: 'sectoral', name: 'Asian Paints Limited' },
							'MARUTI': { category: 'sectoral', name: 'Maruti Suzuki India Limited' },
							'BAJFINANCE': { category: 'sectoral', name: 'Bajaj Finance Limited' },
							'HCLTECH': { category: 'sectoral', name: 'HCL Technologies Limited' }
						};

						const indices = [];
						const topStocks = [];
						const foStocks = [];
						const sectoral = [];

						//console.log('🔍 Searching through', this.csvSymbols.length, 'symbols...');

						// Search through NSE.json data
						for (const instrument of this.csvSymbols) {
							// Look for indices
							if (instrument.segment === 'NSE_INDEX') {
								const instrumentKey = instrument.instrument_key;
								if (instrumentKey && (
									instrumentKey.includes('Nifty 50') ||
									instrumentKey.includes('Nifty Bank') ||
									instrumentKey.includes('Nifty IT')
								)) {
									// //console.log(instrumentKey)
									indices.push(instrumentKey);
									foundSymbols.set(instrumentKey, instrument.name || instrumentKey);
								}
							}

							// Look for equity stocks
							if (instrument.segment === 'NSE_EQ' && instrument.instrument_type === 'EQ') {
								const tradingSymbol = instrument.trading_symbol;
								const instrumentKey = instrument.instrument_key;
								const name = instrument.name;

								if (tradingSymbol && popularStocks[tradingSymbol] && instrumentKey) {
									const stockInfo = popularStocks[tradingSymbol];
									foundSymbols.set(instrumentKey, name || stockInfo.name);

									if (stockInfo.category === 'top') {
										topStocks.push(instrumentKey);
									} else if (stockInfo.category === 'fo') {
										foStocks.push(instrumentKey);
									} else if (stockInfo.category === 'sectoral') {
										sectoral.push(instrumentKey);
									}
								}
							}
						}
						// //console.log(indices)
						// //console.log('🔍 Found symbols:', {
						// 	indices: indices.length,
						// 	topStocks: topStocks.length,
						// 	foStocks: foStocks.length,
						// 	sectoral: sectoral.length
						// });

						// Build watchlist with categories and display names
						if (indices.length > 0) {
							watchlistSymbols.push('###NIFTY INDICES');
							indices.forEach(instrumentKey => {
								const displayName = foundSymbols.get(instrumentKey) || instrumentKey;
								// Use display name as symbol for TradingView
								watchlistSymbols.push(displayName + '|' + instrumentKey);
							});
						}

						if (topStocks.length > 0) {
							watchlistSymbols.push('###TOP STOCKS');
							topStocks.forEach(instrumentKey => {
								const displayName = foundSymbols.get(instrumentKey) || instrumentKey;
								watchlistSymbols.push(displayName + '|' + instrumentKey);
							});
						}

						if (foStocks.length > 0) {
							watchlistSymbols.push('###F&O STOCKS');
							foStocks.forEach(instrumentKey => {
								const displayName = foundSymbols.get(instrumentKey) || instrumentKey;
								watchlistSymbols.push(displayName + '|' + instrumentKey);
							});
						}

						if (sectoral.length > 0) {
							watchlistSymbols.push('###SECTORAL');
							sectoral.forEach(instrumentKey => {
								const displayName = foundSymbols.get(instrumentKey) || instrumentKey;
								watchlistSymbols.push(displayName + '|' + instrumentKey);
							});
						}

						// Store symbol names for reference
						this.symbolNames = foundSymbols;

						// Use fallback if no symbols found
						if (watchlistSymbols.length <= 4) { // Only category headers
							//console.log('📋 Insufficient symbols found in NSE.json, using fallback');
							return fallbackSymbols;
						}
						// //console.log(watchlistSymbols)
						//console.log(`📋 Generated watchlist with ${watchlistSymbols.length} symbols from NSE.json`);
						//console.log('📋 Symbol names:', Array.from(foundSymbols.entries()).slice(0, 5));
						return watchlistSymbols;
					}

					// Get full name for a symbol
					getSymbolFullName(instrumentKey) {
						if (this.symbolNames && this.symbolNames.has(instrumentKey)) {
							return this.symbolNames.get(instrumentKey);
						}

						// Fallback to searching in csvSymbols
						if (this.csvSymbols) {
							const found = this.csvSymbols.find(s => s.instrument_key === instrumentKey);
							if (found) {
								return found.name || found.trading_symbol || instrumentKey;
							}
						}

						return instrumentKey; // Return instrument key if no name found
					}

					// Extract instrument key from display format "Display Name|InstrumentKey"
					extractInstrumentKey(symbolName) {
						if (symbolName && symbolName.includes(':')) {
							return symbolName.split(':')[0];
						}
						if (symbolName && symbolName.includes('|')) {
							const parts = symbolName.split('|');
							if (parts.length >= 2) {
								// Return the instrument key part (everything after first |)
								return parts.slice(1).join('|');
							}
						}
						return symbolName; // Return as-is if no pipe separator
					}

					// Extract display name from format "Display Name|InstrumentKey"
					extractDisplayName(symbolName) {
						// //console.log(symbolName)
						if(symbolName && symbolName.includes('|') && symbolName.includes(':')){
							return symbolName.split('|')[1].split(':')[1];
						}
						if (symbolName && symbolName.includes('|')) {
							const parts = symbolName.split('|');
							if (parts.length >= 2) {
								return parts[0]; // Return the display name part
							}
						}
						return symbolName; // Return as-is if no pipe separator
					}







					onReady(callback) {
						//console.log('[onReady]: Method call');
						setTimeout(() => callback(this.configuration), 0);
					}

					// Get popular symbols from CSV
					getPopularSymbols(onResultReadyCallback) {
						//console.log('🔥 Getting popular symbols from NSE.json...');
						//console.log('📊 NSE.json Status - Loaded:', this.csvLoaded, 'Count:', this.csvSymbols.length);

						if (!this.csvLoaded || this.csvSymbols.length === 0) {
							//console.log('⚠️ NSE.json not loaded yet, waiting...');
							// Wait a bit and try again
							setTimeout(() => {
								if (this.csvLoaded && this.csvSymbols.length > 0) {
									//console.log('✅ NSE.json loaded, trying again...');
									this.getPopularSymbols(onResultReadyCallback);
								} else {
									//console.log('❌ NSE.json still not loaded, returning empty');
									onResultReadyCallback([]);
								}
							}, 1000);
							return;
						}

						// Get top 50 equity symbols from NSE
						const popularSymbols = this.csvSymbols
							.filter(s => s.exchange === 'NSE' && (s.instrument === 'EQ' || s.type === 'stock'))
							.slice(0, 50);

						//console.log(`🎯 Returning ${popularSymbols.length} popular NSE equity symbols`);
						//console.log('🔬 Popular symbols sample:', popularSymbols.slice(0, 3));
						onResultReadyCallback(popularSymbols);
					}

					// Search symbols using NSE.json data via PHP backend
					async searchSymbols(userInput, exchange, symbolType, onResultReadyCallback) {
						//console.log('🔍 [searchSymbols]: Search for:', userInput, 'exchange:', exchange, 'type:', symbolType);

						try {
							// Use PHP backend to search NSE.json
							const searchQuery = userInput || '';
							const url = `./upstox_symbol_search.php?query=${encodeURIComponent(searchQuery)}&limit=100&symbolType=${symbolType}`;

							//console.log('🌐 Fetching symbols from:', url);
							const response = await fetch(url);

							if (!response.ok) {
								throw new Error(`HTTP ${response.status}: ${response.statusText}`);
							}

							const data = await response.json();
							//console.log('📊 Received symbols:', data.length);

							let filteredSymbols = data;

							// Filter by exchange if specified
							if (exchange && exchange !== '') {
								const beforeCount = filteredSymbols.length;
								filteredSymbols = filteredSymbols.filter(s => s.exchange === exchange);
								//console.log(`🏢 After exchange filter "${exchange}": ${filteredSymbols.length} symbols (was ${beforeCount})`);
							}

							// Filter by symbol type if specified
							if (symbolType && symbolType !== '') {
								const beforeCount = filteredSymbols.length;
								filteredSymbols = filteredSymbols.filter(s => s.type === symbolType);
								//console.log(`📈 After type filter "${symbolType}": ${filteredSymbols.length} symbols (was ${beforeCount})`);
							}

							// Limit results for performance
							const limitedSymbols = filteredSymbols.slice(0, 50);
							// //console.log(limitedSymbols)
							//console.log(`🎯 Final result: ${limitedSymbols.length} symbols`);
							//console.log('📋 Sample results:', limitedSymbols.slice(0, 3));

							onResultReadyCallback(limitedSymbols);

						} catch (error) {
							console.error('❌ Error searching symbols:', error);

							// Fallback to basic symbols
							const fallbackSymbols = [
								{
									symbol: 'NSE:RELIANCE-EQ',
									full_name: 'NSE:RELIANCE-EQ',
									description: 'Reliance Industries Limited',
									exchange: 'NSE',
									ticker: 'RELIANCE',
									type: 'stock'
								},
								{
									symbol: 'NFO:NIFTY-FUT',
									full_name: 'NFO:NIFTY-FUT',
									description: 'Nifty 50 Future',
									exchange: 'NFO',
									ticker: 'NIFTY',
									type: 'futures'
								},
								{
									symbol: 'NSE:TCS-EQ',
									full_name: 'NSE:TCS-EQ',
									description: 'Tata Consultancy Services Limited',
									exchange: 'NSE',
									ticker: 'TCS',
									type: 'stock'
								}
							];

							//console.log('🔄 Using fallback symbols:', fallbackSymbols.length);
							onResultReadyCallback(fallbackSymbols);
						}
					}

					resolveSymbol(symbolName, onSymbolResolvedCallback, onResolveErrorCallback) {
						//console.log('[resolveSymbol]: Method call', symbolName);

						// Extract instrument key if symbol is in display format
						const instrumentKey = this.extractInstrumentKey(symbolName);
						const displayName = this.extractDisplayName(symbolName);

						//console.log('[resolveSymbol]: Extracted - Display:', displayName, 'Instrument:', instrumentKey);

						// Try to find symbol in NSE.json data
						let symbolInfo = null;

						if (this.csvLoaded && this.csvSymbols.length > 0) {
							const foundSymbol = this.csvSymbols.find(s =>
								s.symbol === instrumentKey ||
								s.ticker === instrumentKey ||
								s.full_name === displayName ||
								s.instrument_key === instrumentKey ||
								s.symbol === symbolName ||
								s.ticker === symbolName ||
								s.full_name === symbolName ||
								s.instrument_key === symbolName
							);

							if (foundSymbol) {
								symbolInfo = foundSymbol;
								//console.log('✅ Symbol found in NSE.json:', foundSymbol);
							}
						}

						// //console.log(symbolInfo)
						// Special handling for NSE_INDEX|Nifty 50 format
						if (!symbolInfo && symbolName.includes('NSE_INDEX|')) {
							//console.log('🔍 Handling NSE_INDEX format:', symbolName);

							// Extract the index name
							const indexName = symbolName.replace('NSE_INDEX|', '');
							// //console.log
							// Create symbol info for Nifty 50 index
							symbolInfo = {
								symbol: symbolName,
								full_name: symbolName,
								ticker: indexName,
								name: indexName,
								description: `${indexName} Index`,
								type: 'index',
								session: '0915-1530',
								timezone: 'Asia/Kolkata',
								exchange: 'NSE',
								minmov: 1,
								pricescale: 100,
								has_intraday: true,
								has_no_volume: false,
								has_weekly_and_monthly: true,
								supported_resolutions: this.configuration.supported_resolutions,
								volume_precision: 0,
								data_status: 'streaming',
								instrument_key: symbolName,
								logo_urls: [this.getSymbolLogoFast({
									ticker: indexName,
									description: indexName,
									exchange: 'NSE'
								})[0]]
							};

							//console.log('✅ Created NSE_INDEX symbol info:', symbolInfo);
						}

						// Fallback to default symbol info
						if (!symbolInfo) {
							//console.log('⚠️ Symbol not found, creating fallback for:', symbolName);

							let exchange, ticker, instrumentKey;

							// Handle different symbol formats
							if (symbolName.includes('NSE_INDEX|')) {
								// NSE Index format
								exchange = 'NSE';
								ticker = symbolName.replace('NSE_INDEX|', '');
								instrumentKey = symbolName; // Use the full NSE_INDEX|... format
							} else if (symbolName.includes('|')) {
								// General instrument_key format
								const parts = symbolName.split('|');
								exchange = parts[0].split('_')[0] || 'NSE';
								ticker = parts[1] || parts[0];
								instrumentKey = symbolName;
							} else {
								// Traditional format (NSE:SYMBOL-EQ)
								const exchangeMatch = symbolName.match(/^([A-Z]+):/);
								exchange = exchangeMatch ? exchangeMatch[1] : 'NSE';
								ticker = symbolName.replace(/^[A-Z]+:/, '').replace(/-EQ$/, '');
								instrumentKey = symbolName;
							}

							// Fast logo generation for fallback
							const [logoUrl, fallBackURL] = this.getSymbolLogoFast({
								ticker: ticker,
								description: ticker,
								exchange: exchange
							});

							symbolInfo = {
								symbol: instrumentKey || symbolName,
								full_name: displayName !== symbolName ? displayName : symbolName,
								ticker: ticker,
								name: displayName !== symbolName ? displayName : symbolName,
								description: displayName !== symbolName ? displayName : `${ticker}`,
								type: symbolName.includes('INDEX') ? 'index' : 'stock',
								session: '0915-1530',
								timezone: 'Asia/Kolkata',
								exchange: exchange,
								minmov: 1,
								pricescale: 100,
								has_intraday: true,
								has_no_volume: false,
								has_weekly_and_monthly: true,
								supported_resolutions: this.configuration.supported_resolutions,
								volume_precision: 0,
								data_status: 'streaming',
								instrument_key: instrumentKey, // This is crucial for API
								logo_urls: [logoUrl]
							};

							//console.log('✅ Created fallback symbol info:', symbolInfo);
							window.bars = {};
						}

						if(onSymbolResolvedCallback==true){
							return symbolInfo
						}
						setTimeout(() => onSymbolResolvedCallback(symbolInfo), 0);
					}

					getBars(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) {
						//console.log('[getBars]: Method call', symbolInfo.ticker, resolution, periodParams);
						//console.log('[getBars]: Period from', new Date(periodParams.from * 1000), 'to', new Date(periodParams.to * 1000));

						// Only use API - no fallback data
						this.fetchUpstoxData(symbolInfo, resolution, periodParams)
							.then(bars => {
								if (bars && bars.length > 0) {
									if(window.bars==undefined){
										window.bars={}
									}
									for(var i = 0;i<bars.length;i++){
										window.bars[bars[i].time] = bars[i];
									}
									//console.log(`[getBars]: ✅ data - ${bars.length} bars for ${symbolInfo.ticker}`);
									onHistoryCallback(bars, { noData: false });
								} else {
									//console.log(`[getBars]: ❌ No data available for ${symbolInfo.ticker}`);
									onHistoryCallback([], { noData: true });
								}
							})
							.catch(error => {
								console.error('[getBars]: ❌ API error:', error);
								//console.log('[getBars]: 🚫 No fallback - only mode');
								onErrorCallback('API error: ' + error.message);
							});
					}

					/**
					 * Get data retrieval limits based on timeframe (similar to API limits)
					 * @param {string} interval - The interval (e.g., '1minute', '5minute', etc.)
					 * @param {number} toTimestamp - The 'to' timestamp in seconds
					 * @returns {object} - Object with hasLimit, fromTime, and limitType
					 */
					getDataRetrievalLimit(interval, toTimestamp) {
						const now = toTimestamp;

						// Define limits based on API documentation
						const limits = {
							// Minutes (1-15 minutes): 1 month limit
							'1minute': { days: 7, limitType: '1 month' },
							'3minute': { months: 1, limitType: '1 month' },
							'5minute': { months: 1, limitType: '1 month' },
							'15minute': { months: 1, limitType: '1 month' },

							// 30 minutes and 1 hour: 1 quarter (3 months) limit
							'30minute': { months: 3, limitType: '1 quarter' },
							'1hour': { months: 3, limitType: '1 quarter' },

							// 4 hour: 1 quarter limit
							'4hour': { months: 3, limitType: '1 quarter' },

							// Daily: 1 decade limit (but practically no limit for most use cases)
							'1day': { years: 10, limitType: '1 decade' },

							// Weekly and Monthly: No limit
							'1week': { hasLimit: false, limitType: 'no limit' },
							'1month': { hasLimit: false, limitType: 'no limit' }
						};

						const limit = limits[interval];

						if (!limit || limit.hasLimit === false) {
							// No limit - return all data
							return {
								hasLimit: false,
								fromTime: 0,
								limitType: 'no limit'
							};
						}

						// Calculate the 'from' time based on the limit
						let fromTime;
						
						if (limit.days) {
							// Subtract days from the 'to' date
							const toDate = new Date(now * 1000);
							toDate.setDate(toDate.getDate() - limit.days); // ✅ Correctly subtracts days
							fromTime = Math.floor(toDate.getTime() / 1000);
						} else if (limit.months) {
							// Subtract months from the 'to' date
							const toDate = new Date(now * 1000);
							toDate.setMonth(toDate.getMonth() - limit.months);
							fromTime = Math.floor(toDate.getTime() / 1000);
						} else if (limit.years) {
							// Subtract years from the 'to' date
							const toDate = new Date(now * 1000);
							toDate.setFullYear(toDate.getFullYear() - limit.years);
							fromTime = Math.floor(toDate.getTime() / 1000);
						}

						return {
							hasLimit: true,
							fromTime: fromTime,
							limitType: limit.limitType
						};
					}

					async fetchUpstoxData(symbolInfo, resolution, periodParams) {
						try {
							// Convert TradingView resolution to interval
							const intervalMap = {
								'1': '1minute',
								'3': '3minute', // Will aggregate
								'5': '5minute', // Will aggregate
								'15': '15minute', // Will aggregate
								'30': '30minute',
								'60': '1hour',
								'240': '4hour', // Will aggregate
								'1D': '1day',
								'1W': '1week',
								'1M': '1month'
							};

							const upstoxInterval = intervalMap[resolution] || 'day';

							// //console.log(data.bars)
							// Apply data retrieval limits based on timeframe
							const dataLimit = this.getDataRetrievalLimit(upstoxInterval, periodParams.to);

							// Apply timeframe-based limits
							if (dataLimit.hasLimit) {
								var fromDate = new Date(dataLimit.fromTime*1000+86400000*7).toISOString().split('T')[0];;
							} else{
								// Convert timestamps to dates
								var fromDate = new Date(periodParams.from * 1000).toISOString().split('T')[0];
				
							}
							const toDate = new Date(periodParams.to * 1000).toISOString().split('T')[0];

							// //console.log(symbolInfo)
							// Build API URL using instrument_key
							const symbol = symbolInfo.instrument_key || symbolInfo.symbol;
							const apiUrl = `./upstox_data_fetcher.php?symbol=${encodeURIComponent(symbol)}&interval=${upstoxInterval}&from=${fromDate}&to=${toDate}`;

							//console.log('[fetchUpstoxData]: 🎯 Symbol Info:', {
							// 	symbol: symbolInfo.symbol,
							// 	ticker: symbolInfo.ticker,
							// 	instrument_key: symbolInfo.instrument_key,
							// 	type: symbolInfo.type
							// });
							//console.log('[fetchUpstoxData]: 📡 Fetching from:', apiUrl);

							const response = await fetch(apiUrl);
							if (!response.ok) {
								throw new Error(`HTTP ${response.status}: ${response.statusText}`);
							}

							const data = await response.json();
							//console.log('[fetchUpstoxData]: 📊 API Response:', data);

							if (!data.success) {
								throw new Error(data.error || 'API request failed');
							}

							// Convert bars to TradingView format and filter by time range with limits
							const bars = data.bars
								.filter(bar => {
									const barTime = bar.time / 1000; // Convert to seconds

									// Apply timeframe-based limits
									if (dataLimit.hasLimit) {
										return barTime >= dataLimit.fromTime && barTime <= periodParams.to;
									} else {
										// No limit - use all available data up to 'to' date
										return barTime <= periodParams.to;
									}
								})
								.map(bar => ({
									time: bar.time,
									open: bar.open,
									high: bar.high,
									low: bar.low,
									close: bar.close,
									volume: bar.volume,
									oi: bar.oi
								}));

							//console.log(`[fetchUpstoxData]: 📊 Applied ${dataLimit.hasLimit ? dataLimit.limitType : 'no'} limit for ${upstoxInterval}`);

							//console.log(`[fetchUpstoxData]: ✅ Processed ${bars.length} bars`);
							return bars;

						} catch (error) {
							console.error('[fetchUpstoxData]: ❌ Error:', error);
							throw error;
						}
					}

					getFallbackBars(symbolInfo, resolution, periodParams, onHistoryCallback) {
						//console.log('[getFallbackBars]: ❌ No data available');

						// Return empty data - no fallback to sample or mock data
						//console.log('[getFallbackBars]: 🚫 No fallback data - only mode');
						onHistoryCallback([], { noData: true });
					}

					subscribeBars(symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) {
						//console.log('[subscribeBars]: Method call with subscriberUID:', subscriberUID);
						//console.log('[subscribeBars]: Symbol info:', symbolInfo);

						// Store subscription info
						subscribedSymbols.set(subscriberUID, symbolInfo);
						realtimeCallbacks.set(subscriberUID, onRealtimeCallback);

						// Subscribe to live data via WebSocket
						if (liveDataSocket && liveDataSocket.connected) {
							const subscriptionData = {
								symbol: symbolInfo.symbol,
								instrument_key: symbolInfo.instrument_key || symbolInfo.symbol,
								resolution: resolution
							};

							//console.log('[subscribeBars]: Subscribing to live data:', subscriptionData);
							liveDataSocket.emit('subscribe', subscriptionData);
						} else {
							//console.log('[subscribeBars]: WebSocket not connected, skipping live subscription');
						}
					}

					unsubscribeBars(subscriberUID) {
						//console.log('[unsubscribeBars]: Method call with subscriberUID:', subscriberUID);

						// Get symbol info before removing
						const symbolInfo = subscribedSymbols.get(subscriberUID);

						// Remove from local storage
						subscribedSymbols.delete(subscriberUID);
						realtimeCallbacks.delete(subscriberUID);

						// Unsubscribe from WebSocket
						if (liveDataSocket && liveDataSocket.connected && symbolInfo) {
							const unsubscriptionData = {
								instrument_key: symbolInfo.instrument_key || symbolInfo.symbol
							};

							//console.log('[unsubscribeBars]: Unsubscribing from live data:', unsubscriptionData);
							liveDataSocket.emit('unsubscribe', unsubscriptionData);
						}
					}
					getQuotes(symbols, onDataCallback, onErrorCallback) {
						const data = [];
						

						// console.log(symbols)
						symbols.forEach((symbol)=>{
							data.push({
								n: symbol,
								s: 'ok',
								v: {
									ch: Math.random() * (5 - 1) + 1,
									chp: Math.random() * (5 - 1) + 1,
									lp: Math.random() * (10 - 1) + 1,
									ask: Math.random() * (10 - 1) + 1,
									bid: Math.random() * (10 - 1) + 1,
									spread: 0.20,
									open_price: Math.random() * (5 - 1) + 1,
									high_price: Math.random() * (5 - 1) + 1,
									low_price: Math.random() * (5 - 1) + 1,
									prev_close_price: Math.random() * (5 - 1) + 1,
									original_name: symbol,
									volume: Math.random() * (5 - 1) + 1,
								},
							});
						});
						// To ensure the callback is only evoked when the library is ready - see Asynchronous callbacks
						setTimeout(() => onDataCallback(data), 0);
					}
					subscribeQuotes(symbols, fastSymbols, onRealtimeCallback, listenerGUID) {
						// In this example, `_quotesSubscriptions` is a global variable used to clear the subscription in `unsubscribeQuotes`
						this._quotesSubscriptions[listenerGUID] = setInterval(() => this.getQuotes(symbols.concat(fastSymbols), onRealtimeCallback, () => undefined), 5000);
					}
					unsubscribeQuotes(listenerGUID) {
						clearInterval(this._quotesSubscriptions[listenerGUID]);
					}
				}

				const datafeed = new SampleDatafeed();

				// Layout Management Functions
				function getSavedLayouts() {
					try {
						const layouts = localStorage.getItem('tradingview_layouts');
						if (layouts) {
							const parsed = JSON.parse(layouts);
							// //console.log('📋 Found', Object.keys(parsed).length, 'saved layouts');
							return Object.keys(parsed).map((id) =>{
								// //console.log(parsed[id])
								return {
									id: id,
									name: parsed[id].name || `Layout ${id}`,
									timestamp: parsed[id].timestamp/1000 || Date.now(),
									symbol:parsed[id].symbol,
									resolution:parsed[id].timeframe,
									// exchange:parsed[id].exchange||'hi'
								}
							});
						}
					} catch (error) {
						console.error('❌ Error loading layouts:', error);
					}
					return [];
				}

				function saveLayout(chartData) {
					try {
						const layoutId = 'layout_' + Date.now();
						const layoutName = chartData.name || `Layout ${new Date().toLocaleString()}`;

						// //console.log(chartData)
						const layouts = JSON.parse(localStorage.getItem('tradingview_layouts') || '{}');
						layouts[layoutId] = {
							id: layoutId,
							name: layoutName,
							content: chartData.content,
							timestamp: Date.now(),
							symbol: JSON.parse(chartData.content).symbol || 'Unknown',
							timeframe: chartData.resolution || '1D'
						};
						// //console.log(layouts)

						localStorage.setItem('tradingview_layouts', JSON.stringify(layouts));
						// //console.log('💾 Layout saved:', layoutName, 'ID:', layoutId);

						// Show success message
						showNotification('✅ Layout saved: ' + layoutName, 'success');

						return layoutId;
					} catch (error) {
						console.error('❌ Error saving layout:', error);
						showNotification('❌ Failed to save layout', 'error');
						return null;
					}
				}

				function getLayout(chartId) {
					try {
						const layouts = JSON.parse(localStorage.getItem('tradingview_layouts') || '{}');
						if (layouts[chartId]) {
							// //console.log('📖 Loading layout:', layouts[chartId].name);
							return layouts[chartId].content;
						}
					} catch (error) {
						console.error('❌ Error loading layout:', error);
					}
					return null;
				}

				function removeLayout(chartId) {
					try {
						const layouts = JSON.parse(localStorage.getItem('tradingview_layouts') || '{}');
						if (layouts[chartId]) {
							const layoutName = layouts[chartId].name;
							delete layouts[chartId];
							localStorage.setItem('tradingview_layouts', JSON.stringify(layouts));
							// //console.log('🗑️ Layout deleted:', layoutName);
							showNotification('Layout deleted: ' + layoutName, 'info');
						}
					} catch (error) {
						console.error('❌ Error deleting layout:', error);
					}
				}

				// Notification system
				function showNotification(message, type = 'info') {
					// Create notification element
					const notification = document.createElement('div');
					notification.style.cssText = `
						position: fixed;
						top: 20px;
						right: 20px;
						padding: 12px 20px;
						border-radius: 6px;
						color: white;
						font-family: Arial, sans-serif;
						font-size: 14px;
						z-index: 10000;
						max-width: 300px;
						box-shadow: 0 4px 12px rgba(0,0,0,0.3);
						transition: all 0.3s ease;
					`;

					// Set colors based on type
					switch (type) {
						case 'success':
							notification.style.backgroundColor = '#4CAF50';
							break;
						case 'error':
							notification.style.backgroundColor = '#f44336';
							break;
						case 'warning':
							notification.style.backgroundColor = '#ff9800';
							break;
						default:
							notification.style.backgroundColor = '#2196F3';
					}

					notification.textContent = message;
					document.body.appendChild(notification);

					// Auto remove after 3 seconds
					setTimeout(() => {
						notification.style.opacity = '0';
						notification.style.transform = 'translateX(100%)';
						setTimeout(() => {
							if (notification.parentNode) {
								notification.parentNode.removeChild(notification);
							}
						}, 300);
					}, 3000);
				}

				// Symbol Details Provider
				async function getSymbolDetails(symbol) {
					try {

						// Extract instrument key if symbol is in display format
						const instrumentKey = datafeed.extractInstrumentKey ? datafeed.extractInstrumentKey(symbol) : symbol;
						const displayName = datafeed.extractDisplayName ? datafeed.extractDisplayName(symbol) : symbol;


						// Get basic symbol info from NSE.json
						let symbolInfo = null;
						if (datafeed && datafeed.csvSymbols && datafeed.csvSymbols.length > 0) {
							symbolInfo = datafeed.csvSymbols.find(s =>
								s.instrument_key === instrumentKey ||
								s.symbol === instrumentKey ||
								s.trading_symbol === instrumentKey
							);
						}

						// Prepare details object
						const details = {
							title: displayName || symbol,
							items: []
						};

						// Add basic information from NSE.json
						if (symbolInfo) {
							details.items.push(
								{ title: 'Full Name', value: symbolInfo.name || 'N/A' },
								{ title: 'Trading Symbol', value: symbolInfo.trading_symbol || 'N/A' },
								{ title: 'Exchange', value: symbolInfo.exchange || 'NSE' },
								{ title: 'Segment', value: symbolInfo.segment || 'N/A' },
								{ title: 'Instrument Type', value: symbolInfo.instrument_type || 'N/A' },
								{ title: 'Instrument Key', value: symbolInfo.instrument_key || 'N/A' }
							);

							// Add tick size if available
							if (symbolInfo.tick_size) {
								details.items.push({ title: 'Tick Size', value: symbolInfo.tick_size });
							}

							// Add lot size if available
							if (symbolInfo.lot_size) {
								details.items.push({ title: 'Lot Size', value: symbolInfo.lot_size });
							}

							// Add expiry if available (for F&O)
							if (symbolInfo.expiry) {
								details.items.push({ title: 'Expiry', value: symbolInfo.expiry });
							}

							// Add strike price if available (for options)
							if (symbolInfo.strike_price) {
								details.items.push({ title: 'Strike Price', value: symbolInfo.strike_price });
							}
						} else {
							details.items.push(
								{ title: 'Symbol', value: symbol },
								{ title: 'Status', value: 'Symbol not found in NSE data' }
							);
						}

						// Try to get live market data from API
						try {
							const marketData = await fetchLiveMarketData(instrumentKey);
							if (marketData) {
								details.items.push(
									{ title: '--- Live Data ---', value: '' },
									{ title: 'Last Price', value: `₹${marketData.last_price || 'N/A'}` },
									{ title: 'Change', value: marketData.change ? `₹${marketData.change}` : 'N/A' },
									{ title: 'Change %', value: marketData.change_percent ? `${marketData.change_percent}%` : 'N/A' },
									{ title: 'Volume', value: marketData.volume || 'N/A' },
									{ title: 'High', value: marketData.high ? `₹${marketData.high}` : 'N/A' },
									{ title: 'Low', value: marketData.low ? `₹${marketData.low}` : 'N/A' },
									{ title: 'Open', value: marketData.open ? `₹${marketData.open}` : 'N/A' },
									{ title: 'Previous Close', value: marketData.prev_close ? `₹${marketData.prev_close}` : 'N/A' }
								);
							}
						} catch (error) {
							//console.log('⚠️ Could not fetch live market data:', error.message);
							details.items.push({ title: 'Live Data', value: 'Not available' });
						}

						// Add timestamp
						details.items.push({ title: 'Updated', value: new Date().toLocaleTimeString() });

						//console.log('📊 Symbol details prepared:', details);
						return details;

					} catch (error) {
						console.error('❌ Error in getSymbolDetails:', error);
						return {
							title: symbol,
							items: [
								{ title: 'Error', value: 'Failed to load symbol details' },
								{ title: 'Message', value: error.message }
							]
						};
					}
				}

				// Fetch live market data from API
				async function fetchLiveMarketData(instrumentKey) {
					try {
						// Use the test LTP API endpoint
						const response = await fetch(`./test_upstox_ltp.php?instrument_key=${encodeURIComponent(instrumentKey)}`);
						const data = await response.json();

						if (data.success && data.data && data.data.data) {
							const marketData = data.data.data[instrumentKey];
							if (marketData) {
								return {
									last_price: marketData.last_price,
									change: marketData.net_change,
									change_percent: marketData.pct_change,
									volume: marketData.volume,
									high: marketData.ohlc?.high,
									low: marketData.ohlc?.low,
									open: marketData.ohlc?.open,
									prev_close: marketData.ohlc?.close
								};
							}
						}
						return null;
					} catch (error) {
						//console.log('⚠️ Error fetching live market data:', error);
						return null;
					}
				}

				// Global functions for manual layout management
				window.listLayouts = function() {
					const layouts = getSavedLayouts();
					//console.log('📋 Saved Layouts:');
					layouts.forEach(layout => {
						//console.log(`  ${layout.id}: ${layout.name} (${new Date(layout.timestamp).toLocaleString()})`);
					});
					return layouts;
				};

				window.deleteLayout = function(layoutId) {
					removeLayout(layoutId);
					//console.log('🗑️ Layout deleted:', layoutId);
				};

				window.clearAllLayouts = function() {
					if (confirm('Are you sure you want to delete all saved layouts?')) {
						localStorage.removeItem('tradingview_layouts');
						//console.log('🗑️ All layouts cleared');
						showNotification('🗑️ All layouts cleared', 'info');
					}
				};

				// Global function to test symbol details
				window.testSymbolDetails = async function(symbol) {
					//console.log('🧪 Testing symbol details for:', symbol);
					try {
						const details = await getSymbolDetails(symbol);
						//console.log('📊 Symbol Details Result:');
						//console.log('Title:', details.title);
						details.items.forEach(item => {
							//console.log(`  ${item.title}: ${item.value}`);
						});
						return details;
					} catch (error) {
						console.error('❌ Error testing symbol details:', error);
						return null;
					}
				};

				// Function to analyze candlestick patterns from chart data
				window.analyzeCurrentChartPatterns = function(sensitivity = 0.5) {
					try {
						if (!window.tvWidget || !window.tvWidget.chart) {
							console.error('❌ TradingView widget not available');
							return [];
						}

						// Get current symbol info
						const chart = window.tvWidget.chart();
						const symbolInfo = chart.symbol();

						//console.log('🕯️ Analyzing candlestick patterns for:', symbolInfo);
						//console.log('💡 Note: This is a demonstration. Real implementation would need chart data access.');

						// For demonstration, use sample data
						// In a real implementation, you would get actual OHLC data from the chart
						const sampleOHLC = [
							{ open: 2450, high: 2465, low: 2440, close: 2460 },
							{ open: 2460, high: 2470, low: 2445, close: 2455 },
							{ open: 2455, high: 2460, low: 2430, close: 2435 },
							{ open: 2435, high: 2480, low: 2430, close: 2475 },
							{ open: 2475, high: 2485, low: 2470, close: 2480 }
						];

						//console.log('📊 Using sample OHLC data for pattern analysis...');
						return analyzeCandlestickPatterns(sampleOHLC, sensitivity);

					} catch (error) {
						console.error('❌ Error analyzing chart patterns:', error);
						return [];
					}
				};

				// Function to set up automatic pattern detection
				window.enablePatternDetection = function(interval = 5000) {
					if (window.patternDetectionInterval) {
						clearInterval(window.patternDetectionInterval);
					}

					//console.log('🔄 Enabling automatic pattern detection every', interval / 1000, 'seconds');

					window.patternDetectionInterval = setInterval(() => {
						const patterns = analyzeCurrentChartPatterns(0.5);
						if (patterns.length > 0) {
							//console.log('🚨 New patterns detected:', patterns.map(p => p.name).join(', '));

							// Show notification for strong patterns
							const strongPatterns = patterns.filter(p => p.strength >= 0.8);
							if (strongPatterns.length > 0) {
								const patternNames = strongPatterns.map(p => p.name).join(', ');
								showNotification(`🕯️ Strong Pattern: ${patternNames}`, 'info');
							}
						}
					}, interval);

					return 'Pattern detection enabled';
				};

				// Function to disable automatic pattern detection
				window.disablePatternDetection = function() {
					if (window.patternDetectionInterval) {
						clearInterval(window.patternDetectionInterval);
						window.patternDetectionInterval = null;
						//console.log('⏹️ Pattern detection disabled');
						return 'Pattern detection disabled';
					}
					return 'Pattern detection was not running';
				};

				// Function to create widget with watchlist symbols
				function createWidgetWithWatchlist() {
					const isDark =
						window.matchMedia &&
						window.matchMedia('(prefers-color-scheme: dark)').matches;
					const theme = getParameterByName('theme') || (isDark ? 'dark' : 'light');

					// Try to get NSE.json symbols if available, otherwise use fallback
					let defaultSymbols;
					if (datafeed && datafeed.csvLoaded && datafeed.csvSymbols && datafeed.csvSymbols.length > 0) {
						//console.log('📋 NSE.json available, using dynamic symbols');
						defaultSymbols = datafeed.getWatchlistSymbols();
					} else {
						//console.log('📋 NSE.json not ready, using fallback symbols');
						defaultSymbols = [
							'###INDICES',
							'Nifty',
							'Banknifty',
							'###POPULAR STOCKS',
							'RELIANCE',  // RELIANCE
							'TCS',  // TCS
							'INFY',  // INFY
							'HDFCBANK',  // HDFCBANK
							'ICICIBANK',  // ICICIBANK
							'KOTAKBANK',  // KOTAKBANK
							'ITC',  // ITC
							'LT',  // LT
							'AXISBANK',  // AXISBANK
							'BHARTIARTL',  // BHARTIARTL
							'ASIANPAINT',  // ASIANPAINT
							'MARUTI',  // MARUTI
							'BAJFINANCE',  // 
							'HCLTECH',  // HCLTECH
						];
					}
					const STORAGE_KEY = "tv_local_save_data_v1";

					const save_load_adapter = {
						charts: [],
						studyTemplates: [],
						drawingTemplates: [],
						chartTemplates: [],
						drawings: {},

						// ✅ Load data from localStorage when adapter is created
						_loadFromStorage() {
							const saved = localStorage.getItem(STORAGE_KEY);
							if (saved) {
							try {
								const parsed = JSON.parse(saved);
								this.charts = parsed.charts || [];
								this.studyTemplates = parsed.studyTemplates || [];
								this.drawingTemplates = parsed.drawingTemplates || [];
								this.chartTemplates = parsed.chartTemplates || [];
								this.drawings = parsed.drawings || {};
							} catch (e) {
								console.error("Failed to parse saved data", e);
							}
							}
						},

						// ✅ Save current state to localStorage
						_saveToStorage() {
							const data = {
							charts: this.charts,
							studyTemplates: this.studyTemplates,
							drawingTemplates: this.drawingTemplates,
							chartTemplates: this.chartTemplates,
							drawings: this.drawings,
							};
							localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
						},

						// ✅ Chart Management
						getAllCharts() {
							return Promise.resolve(this.charts);
						},

						removeChart(id) {
							this.charts = this.charts.filter((c) => c.id !== id);
							this._saveToStorage();
							return Promise.resolve();
						},

						saveChart(chartData) {
							if (!chartData.id) {
							chartData.id = Math.random().toString();
							} else {
							this.removeChart(chartData.id);
							}
							chartData.symbol=JSON.parse(chartData.content).symbol
							// console.log()
							const savedChartData = {
							...chartData,
							id: chartData.id,
							timestamp: Math.round(Date.now() / 1000),
							};

							this.charts.push(savedChartData);
							this._saveToStorage();

							return Promise.resolve(chartData.id);
						},

						getChartContent(id) {
							const chart = this.charts.find((c) => c.id === id);
							// console.log(chart)
							if (chart) {
							return Promise.resolve(chart.content);
							}
							return Promise.reject("Chart not found");
						},

						// ✅ Study Templates
						removeStudyTemplate(studyTemplateData) {
							this.studyTemplates = this.studyTemplates.filter(
							(t) => t.name !== studyTemplateData.name
							);
							this._saveToStorage();
							return Promise.resolve();
						},

						getStudyTemplateContent(studyTemplateData) {
							const tpl = this.studyTemplates.find(
							(t) => t.name === studyTemplateData.name
							);
							return tpl ? Promise.resolve(tpl.content) : Promise.reject();
						},

						saveStudyTemplate(studyTemplateData) {
							this.studyTemplates = this.studyTemplates.filter(
							(t) => t.name !== studyTemplateData.name
							);
							this.studyTemplates.push(studyTemplateData);
							this._saveToStorage();
							return Promise.resolve();
						},

						getAllStudyTemplates() {
							return Promise.resolve(this.studyTemplates);
						},

						// ✅ Drawing Templates
						removeDrawingTemplate(toolName, templateName) {
							this.drawingTemplates = this.drawingTemplates.filter(
							(t) => t.name !== templateName
							);
							this._saveToStorage();
							return Promise.resolve();
						},

						loadDrawingTemplate(toolName, templateName) {
							const tpl = this.drawingTemplates.find((t) => t.name === templateName);
							return tpl ? Promise.resolve(tpl.content) : Promise.reject();
						},

						saveDrawingTemplate(toolName, templateName, content) {
							this.drawingTemplates = this.drawingTemplates.filter(
							(t) => t.name !== templateName
							);
							this.drawingTemplates.push({ name: templateName, content });
							this._saveToStorage();
							return Promise.resolve();
						},

						getDrawingTemplates() {
							return Promise.resolve(this.drawingTemplates.map((tpl) => tpl.name));
						},

						// ✅ Chart Templates
						async getAllChartTemplates() {
							return this.chartTemplates.map((x) => x.name);
						},

						async saveChartTemplate(templateName, content) {
							const existing = this.chartTemplates.find((x) => x.name === templateName);
							if (existing) {
							existing.content = content;
							} else {
							this.chartTemplates.push({ name: templateName, content });
							}
							this._saveToStorage();
						},

						async getChartTemplateContent(templateName) {
							// console.log('hi')
							const theme = this.chartTemplates.find((x) => x.name === templateName);
							return theme ? { content: structuredClone(theme.content) } : {};
						},

						// ✅ Drawings Storage
						saveLineToolsAndGroups(layoutId, chartId, state) {
							const drawings = state.sources;
							const key = this._getDrawingKey(layoutId, chartId);

							if (!this.drawings[key]) {
							this.drawings[key] = {};
							}

							for (let [k, s] of drawings) {
							if (s === null) {
								delete this.drawings[key][k];
							} else {
								this.drawings[key][k] = s;
							}
							}

							this._saveToStorage();
							return Promise.resolve();
						},

						loadLineToolsAndGroups(layoutId, chartId, requestType, requestContext) {
							const key = this._getDrawingKey(layoutId, chartId);
							const rawSources = this.drawings[key];
							if (!rawSources) return null;
							const sources = new Map();
							for (let [k, s] of Object.entries(rawSources)) {
							sources.set(k, s);
							}
							return Promise.resolve({ sources });
						},

						_getDrawingKey(layoutId, chartId) {
							return `${layoutId}/${chartId}`;
						},
					};

					// ✅ Load from localStorage immediately when the adapter is created
					save_load_adapter._loadFromStorage();
					// //console.log(defaultSymbols?)
					//console.log('🎯 Creating TradingView widget with', defaultSymbols.length, 'watchlist symbols');

					var widget = (window.tvWidget = new TradingView.widget({
					debug: false, // Enable debug for troubleshooting
					fullscreen: true,
					symbol: 'NSE_INDEX|Nifty 50', // Use proper symbol format that datafeed can resolve
					interval: '5', // 5 minute interval like trading_terminal.html
					container: 'tv_chart_container',
					//	BEWARE: no trailing slash is expected in feed URL
					datafeed: datafeed,
					library_path: 'charting_library/',
					locale: getParameterByName('lang') || 'en',
					custom_css_url: cssBlobUrl,

					disabled_features: [
						'use_localstorage_for_settings',
						'open_account_manager',
						'trading_account_manager',
						'show_object_tree',
						'dom_widget'
					],
					show_object_tree:false,
					enabled_features: [
						'study_templates',
						'show_symbol_logos',
						'show_exchange_logos',
						// Technical Analysis Features
						'studies_overrides',
						'create_volume_indicator_by_default',
						'volume_force_overlay',
						'left_toolbar',
						'header_indicators',
						'header_compare',
						'header_undo_redo',
						'header_screenshot',
						'header_chart_type',
						'header_resolutions',
						'header_settings',
						'legend_context_menu',
						'display_market_status',
						'remove_library_container_border',
						'chart_property_page_style',
						'property_pages',
						'show_chart_property_page',
						'chart_property_page_scales',
						'chart_property_page_background',
						'timeframes_toolbar',
						'edit_buttons_in_legend',
						'context_menus',
						'control_bar',
						'widget_logo',
						'items_favoriting',
						'save_chart_properties_to_local_storage',
						'header_saveload',
						'chart_template_storage',
						'study_template_storage'
					],
					overrides: {
						"mainSeriesProperties.candleStyle.upColor": "#4CAF50",
						"mainSeriesProperties.candleStyle.downColor": "#F44336",
						"mainSeriesProperties.candleStyle.borderUpColor": "#4CAF50",
						"mainSeriesProperties.candleStyle.borderDownColor": "#F44336",
						"mainSeriesProperties.candleStyle.wickUpColor": "#4CAF50",
						"mainSeriesProperties.candleStyle.wickDownColor": "#F44336"
					},

					// Study Templates and Indicators Configuration
					studies_overrides: {
						// PineJS-based Open Interest Indicators Styling
						"Open Interest.plot_0.color": "#2196F3",
						"Open Interest.plot_0.linewidth": 2,
						"OI Volume Ratio.plot_0.color": "#FF9800",
						"OI Volume Ratio.plot_0.linewidth": 2,
						"Moving Average.plot_0.source": 'volume',
					},

					// Custom indicators configuration
					custom_indicators_getter: function(PineJS) {
						//console.log('🔧 Loading Open Interest indicators...');

						return Promise.resolve([
							{
								name: "Candlestick Patterns",
								metainfo: {
									_metainfoVersion: 51,
									id: "Candlestick Patterns@tv-basicstudies-1",
									name: "Candlestick Patterns",
									description: "Candlestick Patterns",
									shortDescription: "Candlestick Patterns",
									is_hidden_study: !1,
									is_price_study: !0,
									isCustomIndicator: !0,
									linkedToSeries: !0,
									format: {
										type: "price",
										precision: 2
									},
									plots: [
										{
											id: "abandoned_baby_bull_plot",
											type: "shapes"
										}, {
											id: "abandoned_baby_bear_plot",
											type: "shapes"
										}, {
											id: "dark_cloud_cover_plot",
											type: "shapes"
										}, {
											id: "doji_plot",
											type: "shapes"
										}, {
											id: "doji_star_bullish_plot",
											type: "shapes"
										}, {
											id: "doji_star_bearish_plot",
											type: "shapes"
										}, {
											id: "downside_tasuki_gap_plot",
											type: "shapes"
										}, {
											id: "dragonfly_doji_plot",
											type: "shapes"
										}, {
											id: "engulfing_bull_plot",
											type: "shapes"
										}, {
											id: "engulfing_bear_plot",
											type: "shapes"
										}, {
											id: "evening_doji_star_plot",
											type: "shapes"
										}, {
											id: "evening_star_plot",
											type: "shapes"
										}, {
											id: "falling_three_methods_plot",
											type: "shapes"
										}, {
											id: "falling_window_plot",
											type: "shapes"
										}, {
											id: "gravestone_doji_plot",
											type: "shapes"
										}, {
											id: "hammer_plot",
											type: "shapes"
										}, {
											id: "hanging_man_plot",
											type: "shapes"
										}, {
											id: "harami_cross_bull_plot",
											type: "shapes"
										}, {
											id: "harami_cross_bear_plot",
											type: "shapes"
										}, {
											id: "harami_bull_plot",
											type: "shapes"
										}, {
											id: "harami_bear_plot",
											type: "shapes"
										}, {
											id: "inverted_hammer_plot",
											type: "shapes"
										}, {
											id: "kicking_bull_plot",
											type: "shapes"
										}, {
											id: "kicking_bear_plot",
											type: "shapes"
										}, {
											id: "long_lower_shadow_plot",
											type: "shapes"
										}, {
											id: "long_upper_shadow_plot",
											type: "shapes"
										}, {
											id: "marubozu_black_plot",
											type: "shapes"
										}, {
											id: "marubozu_white_plot",
											type: "shapes"
										}, {
											id: "morning_doji_star_plot",
											type: "shapes"
										}, {
											id: "morning_star_plot",
											type: "shapes"
										}, {
											id: "on_neck_plot",
											type: "shapes"
										}, {
											id: "piercing_plot",
											type: "shapes"
										}, {
											id: "rising_three_methods_plot",
											type: "shapes"
										}, {
											id: "rising_window_plot",
											type: "shapes"
										}, {
											id: "shooting_star_plot",
											type: "shapes"
										}, {
											id: "spinning_top_black_plot",
											type: "shapes"
										}, {
											id: "spinning_top_white_plot",
											type: "shapes"
										}, {
											id: "three_black_crows_plot",
											type: "shapes"
										}, {
											id: "three_white_soldiers_plot",
											type: "shapes"
										}, {
											id: "tri_star_bull_plot",
											type: "shapes"
										}, {
											id: "tri_star_bear_plot",
											type: "shapes"
										}, {
											id: "tweezer_bottom_plot",
											type: "shapes"
										}, {
											id: "tweezer_top_plot",
											type: "shapes"
										}, {
											id: "upside_tasuki_gap_plot",
											type: "shapes"
										}
									],
									defaults: {
										styles: {
											abandoned_baby_bull_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											abandoned_baby_bear_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											dark_cloud_cover_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											doji_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#797b86",
												textColor: "white"
											},
											doji_star_bullish_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											doji_star_bearish_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											downside_tasuki_gap_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											dragonfly_doji_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											engulfing_bull_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											engulfing_bear_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											evening_doji_star_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											evening_star_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											falling_three_methods_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											falling_window_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											gravestone_doji_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											hammer_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											hanging_man_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											harami_cross_bull_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											harami_cross_bear_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											harami_bull_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											harami_bear_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											inverted_hammer_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											kicking_bull_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											kicking_bear_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											long_lower_shadow_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											long_upper_shadow_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											marubozu_black_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											marubozu_white_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											morning_doji_star_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											morning_star_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											on_neck_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											piercing_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											rising_three_methods_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											rising_window_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											shooting_star_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											spinning_top_black_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#797b86",
												textColor: "white"
											},
											spinning_top_white_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#797b86",
												textColor: "white"
											},
											three_black_crows_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											three_white_soldiers_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											tri_star_bull_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											tri_star_bear_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											tweezer_bottom_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											},
											tweezer_top_plot: {
												
												plottype: "shape_label_down",
												location: "AboveBar",
												transparency: 0,
												color: "#F7525F",
												textColor: "white"
											},
											upside_tasuki_gap_plot: {
												
												plottype: "shape_label_up",
												location: "BelowBar",
												transparency: 0,
												color: "#4CAF50",
												textColor: "white"
											}
										},
										precision: 2,
										inputs: {
											trend: "SMA50",
											patternType: "Both",
											AbandonedBabyInput: !0,
											DarkCloudCoverInput: !0,
											DojiInput: !0,
											DojiStarInput: !0,
											DownsideTasukiGapInput: !0,
											DragonflyDojiInput: !0,
											EngulfingInput: !0,
											EveningDojiStarInput: !0,
											EveningStarInput: !0,
											FallingThreeMethodsInput: !0,
											FallingWindowInput: !0,
											GravestoneDojiInput: !0,
											HammerInput: !0,
											HangingManInput: !0,
											HaramiCrossInput: !0,
											HaramiInput: !0,
											InvertedHammerInput: !0,
											KickingInput: !0,
											LongLowerShadowInput: !0,
											LongUpperShadowInput: !0,
											MarubozuBlackInput: !0,
											MarubozuWhiteInput: !0,
											MorningDojiStarInput: !0,
											MorningStarInput: !0,
											OnNeckInput: !0,
											PiercingInput: !0,
											RisingThreeMethodsInput: !0,
											RisingWindowInput: !0,
											ShootingStarInput: !0,
											SpinningTopBlackInput: !0,
											SpinningTopWhiteInput: !0,
											ThreeBlackCrowsInput: !0,
											ThreeWhiteSoldiersInput: !0,
											TriStarInput: !0,
											TweezerBottomInput: !0,
											TweezerTopInput: !0,
											UpsideTasukiGapInput: !0
										}
									},
									styles: {
										abandoned_baby_bull_plot: {
											
											idx: 0,
											title: "Abandoned Baby Bullish",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Abandoned Baby Bullish",
											size: "small",
											textColor: "white"
										},
										abandoned_baby_bear_plot: {
											
											idx: 1,
											title: "Abandoned Baby Bearish",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Abandoned Baby Bearish",
											size: "small",
											textColor: "white"
										},
										dark_cloud_cover_plot: {
											
											idx: 2,
											title: "Dark Cloud Cover",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Dark Cloud Cover",
											size: "small",
											textColor: "white"
										},
										doji_plot: {
											
											idx: 3,
											title: "Doji",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#797b86",
											text: "Doji",
											size: "small",
											textColor: "white"
										},
										doji_star_bullish_plot: {
											
											idx: 4,
											title: "Doji Star Bullish",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Doji Star Bullish",
											size: "small",
											textColor: "white"
										},
										doji_star_bearish_plot: {
											
											idx: 5,
											title: "Doji Star Bearish",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Doji Star Bearish",
											size: "small",
											textColor: "white"
										},
										downside_tasuki_gap_plot: {
											
											idx: 6,
											title: "Downside Tasuki Gap",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Downside Tasuki Gap",
											size: "small",
											textColor: "white"
										},
										dragonfly_doji_plot: {
											
											idx: 7,
											title: "Dragonfly Doji",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Dragonfly Doji",
											size: "small",
											textColor: "white"
										},
										engulfing_bull_plot: {
											
											idx: 8,
											title: "Bullish Engulfing",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Bullish Engulfing",
											size: "small",
											textColor: "white"
										},
										engulfing_bear_plot: {
											
											idx: 9,
											title: "Bearish Engulfing",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Bearish Engulfing",
											size: "small",
											textColor: "white"
										},
										evening_doji_star_plot: {
											
											idx: 10,
											title: "Evening Doji Star",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Evening Doji Star",
											size: "small",
											textColor: "white"
										},
										evening_star_plot: {
											
											idx: 11,
											title: "Evening Star",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Evening Star",
											size: "small",
											textColor: "white"
										},
										falling_three_methods_plot: {
											
											idx: 12,
											title: "Falling Three Methods",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Falling Three Methods",
											size: "small",
											textColor: "white"
										},
										falling_window_plot: {
											
											idx: 13,
											title: "Falling Window",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Falling Window",
											size: "small",
											textColor: "white"
										},
										gravestone_doji_plot: {
											
											idx: 14,
											title: "Gravestone Doji",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Gravestone Doji",
											size: "small",
											textColor: "white"
										},
										hammer_plot: {
											
											idx: 15,
											title: "Hammer",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Hammer",
											size: "small",
											textColor: "white"
										},
										hanging_man_plot: {
											
											idx: 16,
											title: "Hanging Man",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Hanging Man",
											size: "small",
											textColor: "white"
										},
										harami_cross_bull_plot: {
											
											idx: 17,
											title: "Harami Cross Bullish",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Harami Cross Bullish",
											size: "small",
											textColor: "white"
										},
										harami_cross_bear_plot: {
											
											idx: 18,
											title: "Harami Cross Bearish",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Harami Cross Bearish",
											size: "small",
											textColor: "white"
										},
										harami_bull_plot: {
											
											idx: 19,
											title: "Harami Bullish",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Harami Bullish",
											size: "small",
											textColor: "white"
										},
										harami_bear_plot: {
											
											idx: 20,
											title: "Harami Bearish",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Harami Bearish",
											size: "small",
											textColor: "white"
										},
										inverted_hammer_plot: {
											
											idx: 21,
											title: "Inverted Hammer",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Inverted Hammer",
											size: "small",
											textColor: "white"
										},
										kicking_bull_plot: {
											
											idx: 22,
											title: "Kicking Bullish",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Kicking Bullish",
											size: "small",
											textColor: "white"
										},
										kicking_bear_plot: {
											
											idx: 23,
											title: "Kicking Bearish",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Kicking Bearish",
											size: "small",
											textColor: "white"
										},
										long_lower_shadow_plot: {
											
											idx: 24,
											title: "Long Lower Wicks",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Long Lower Wicks",
											size: "small",
											textColor: "white"
										},
										long_upper_shadow_plot: {
											
											idx: 25,
											title: "Long Upper Wicks",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Long Upper Wicks",
											size: "small",
											textColor: "white"
										},
										marubozu_black_plot: {
											
											idx: 26,
											title: "Marubozu Bearish",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Marubozu Bearish",
											size: "small",
											textColor: "white"
										},
										marubozu_white_plot: {
											
											idx: 27,
											title: "Marubozu Bullish",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Marubozu Bullish",
											size: "small",
											textColor: "white"
										},
										morning_doji_star_plot: {
											
											idx: 28,
											title: "Morning Doji Star",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Morning Doji Star",
											size: "small",
											textColor: "white"
										},
										morning_star_plot: {
											
											idx: 29,
											title: "Morning Star",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Morning Star",
											size: "small",
											textColor: "white"
										},
										on_neck_plot: {
											
											idx: 30,
											title: "On Neck",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "On Neck",
											size: "small",
											textColor: "white"
										},
										piercing_plot: {
											
											idx: 31,
											title: "Piercing",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Piercing",
											size: "small",
											textColor: "white"
										},
										rising_three_methods_plot: {
											
											idx: 32,
											title: "Rising Three Methods",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Rising Three Methods",
											size: "small",
											textColor: "white"
										},
										rising_window_plot: {
											
											idx: 33,
											title: "Rising Window",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Rising Window",
											size: "small",
											textColor: "white"
										},
										shooting_star_plot: {
											
											idx: 34,
											title: "Shooting Star",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Shooting Star",
											size: "small",
											textColor: "white"
										},
										spinning_top_black_plot: {
											
											idx: 35,
											title: "Spinning Top Bearish",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#797b86",
											text: "Spinning Top Bearish",
											size: "small",
											textColor: "white"
										},
										spinning_top_white_plot: {
											
											idx: 36,
											title: "Spinning Top Bullish",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#797b86",
											text: "Spinning Top Bullish",
											size: "small",
											textColor: "white"
										},
										three_black_crows_plot: {
											
											idx: 37,
											title: "Three Black Crows",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Three Black Crows",
											size: "small",
											textColor: "white"
										},
										three_white_soldiers_plot: {
											
											idx: 38,
											title: "Three White Soldiers",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Three White Soldiers",
											size: "small",
											textColor: "white"
										},
										tri_star_bull_plot: {
											
											idx: 39,
											title: "Tri Star Bullish",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Tri Star Bullish",
											size: "small",
											textColor: "white"
										},
										tri_star_bear_plot: {
											
											idx: 40,
											title: "Tri Star Bearish",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Tri Star Bearish",
											size: "small",
											textColor: "white"
										},
										tweezer_bottom_plot: {
											
											idx: 41,
											title: "Tweezer Bottom",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Tweezer Bottom",
											size: "small",
											textColor: "white"
										},
										tweezer_top_plot: {
											
											idx: 42,
											title: "Tweezer Top",
											plottype: "shape_label_down",
											location: "AboveBar",
											transparency: 0,
											color: "#F7525F",
											text: "Tweezer Top",
											size: "small",
											textColor: "white"
										},
										upside_tasuki_gap_plot: {
											
											idx: 43,
											title: "Upside Tasuki Gap",
											plottype: "shape_label_up",
											location: "BelowBar",
											transparency: 0,
											color: "#4CAF50",
											text: "Upside Tasuki Gap",
											size: "small",
											textColor: "white"
										}
									},
									inputs: [{
											id: "trend",
											name: "Detect Trend Based On",
											type: "text",
											defval: "SMA50",
											options: ["SMA50", "SMA50,SMA200", "No Detection"],
											optionsTitles: {
												SMA50: "SMA50",
												"SMA50,SMA200": "SMA50,SMA200",
												"No Detection": "No Detection"
											}
										}, {
											id: "patternType",
											name: "Pattern Type",
											type: "text",
											defval: "Both",
											options: ["Both", "Bullish", "Bearish"],
											optionsTitles: {
												Both: "Both",
												Bullish: "Bullish",
												Bearish: "Bearish"
											}
										}, {
											id: "AbandonedBabyInput",
											name: "Abandoned Baby",
											type: "bool",
											defval: !0
										}, {
											id: "DarkCloudCoverInput",
											name: "Dark Cloud Cover",
											type: "bool",
											defval: !1
										}, {
											id: "DojiInput",
											name: "Doji",
											type: "bool",
											defval: !0
										}, {
											id: "DojiStarInput",
											name: "Doji Star",
											type: "bool",
											defval: !1
										}, {
											id: "DownsideTasukiGapInput",
											name: "Downside Tasuki Gap",
											type: "bool",
											defval: !1
										}, {
											id: "DragonflyDojiInput",
											name: "Dragonfly Doji",
											type: "bool",
											defval: !0
										}, {
											id: "EngulfingInput",
											name: "Engulfing",
											type: "bool",
											defval: !0
										}, {
											id: "EveningDojiStarInput",
											name: "Evening Doji Star",
											type: "bool",
											defval: !1
										}, {
											id: "EveningStarInput",
											name: "Evening Star",
											type: "bool",
											defval: !1
										}, {
											id: "FallingThreeMethodsInput",
											name: "Falling Three Methods",
											type: "bool",
											defval: !1
										}, {
											id: "FallingWindowInput",
											name: "Falling Window",
											type: "bool",
											defval: !1
										}, {
											id: "GravestoneDojiInput",
											name: "Gravestone Doji",
											type: "bool",
											defval: !1
										}, {
											id: "HammerInput",
											name: "Hammer",
											type: "bool",
											defval: !0
										}, {
											id: "HangingManInput",
											name: "Hanging Man",
											type: "bool",
											defval: !1
										}, {
											id: "HaramiCrossInput",
											name: "Harami Cross",
											type: "bool",
											defval: !1
										}, {
											id: "HaramiInput",
											name: "Harami",
											type: "bool",
											defval: !1
										}, {
											id: "InvertedHammerInput",
											name: "Inverted Hammer",
											type: "bool",
											defval: !1
										}, {
											id: "KickingInput",
											name: "Kicking",
											type: "bool",
											defval: !1
										}, {
											id: "LongLowerShadowInput",
											name: "Long Lower Wicks",
											type: "bool",
											defval: !1
										}, {
											id: "LongUpperShadowInput",
											name: "Long Upper Wicks",
											type: "bool",
											defval: !1
										}, {
											id: "MarubozuBlackInput",
											name: "Marubozu Bearish",
											type: "bool",
											defval: !1
										}, {
											id: "MarubozuWhiteInput",
											name: "Marubozu Bullish",
											type: "bool",
											defval: !1
										}, {
											id: "MorningDojiStarInput",
											name: "Morning Doji Star",
											type: "bool",
											defval: !1
										}, {
											id: "MorningStarInput",
											name: "Morning Star",
											type: "bool",
											defval: !1
										}, {
											id: "OnNeckInput",
											name: "On Neck",
											type: "bool",
											defval: !1
										}, {
											id: "PiercingInput",
											name: "Piercing",
											type: "bool",
											defval: !1
										}, {
											id: "RisingThreeMethodsInput",
											name: "Rising Three Methods",
											type: "bool",
											defval: !1
										}, {
											id: "RisingWindowInput",
											name: "Rising Window",
											type: "bool",
											defval: !1
										}, {
											id: "ShootingStarInput",
											name: "Shooting Star",
											type: "bool",
											defval: !1
										}, {
											id: "SpinningTopBlackInput",
											name: "Spinning Top Bearish",
											type: "bool",
											defval: !1
										}, {
											id: "SpinningTopWhiteInput",
											name: "Spinning Top Bullish",
											type: "bool",
											defval: !1
										}, {
											id: "ThreeBlackCrowsInput",
											name: "Three Black Crows",
											type: "bool",
											defval: !1
										}, {
											id: "ThreeWhiteSoldiersInput",
											name: "Three White Soldiers",
											type: "bool",
											defval: !1
										}, {
											id: "TriStarInput",
											name: "Tri Star",
											type: "bool",
											defval: !1
										}, {
											id: "TweezerBottomInput",
											name: "Tweezer Bottom",
											type: "bool",
											defval: !1
										}, {
											id: "TweezerTopInput",
											name: "Tweezer Top",
											type: "bool",
											defval: !1
										}, {
											id: "UpsideTasukiGapInput",
											name: "Upside Tasuki Gap",
											type: "bool",
											defval: !1
										}]
								},
								constructor: function() {
									this.init = function(context, inputData) {
										this._context = context,
										this._input = inputData
									}
									,
									this.main = function(ctx, inputVals) {
										// try {
										const patterns = new Array(46).fill(NaN);
										this._context = ctx,
										this._input = inputVals;
										const smaFifty = "SMA50"
										, smaCombo = "SMA50,SMA200"
										, filterType = this._input(0)
										, signalDirection = this._input(1)
										, enableBullish = "Both" === signalDirection || "Bullish" === signalDirection
										, enableBearish = "Both" === signalDirection || "Bearish" === signalDirection
										, openPrice = PineJS.Std.open(this._context)
										, highPrice = PineJS.Std.high(this._context)
										, lowPrice = PineJS.Std.low(this._context)
										, closePrice = PineJS.Std.close(this._context)
										, openVar = this._context.new_var(openPrice)
										, lowVar = this._context.new_var(lowPrice)
										, closeVar = this._context.new_var(closePrice)
										, highVar = this._context.new_var(highPrice);
										// console.log([openPrice,highPrice,lowPrice,closePrice])
										let bearishTrend = !0
										, bullishTrend = !0;
										const bearishSignal = this._context.new_var(1)
										, bullishSignal = this._context.new_var(1);
										if (filterType === smaFifty) {
											// console.log('hi')
											const sma50 = PineJS.Std.sma(closeVar, 50, this._context);
											bearishTrend = closePrice < sma50,
											bullishTrend = closePrice > sma50,
											bearishSignal.set(bearishTrend ? 1 : 0),
											bullishSignal.set(bullishTrend ? 1 : 0)
										} else if (filterType === smaCombo) {
											const sma50 = PineJS.Std.sma(closeVar, 50, this._context)
											, sma200 = PineJS.Std.sma(closeVar, 200, this._context);
											bearishTrend = closePrice < sma50 && sma50 < sma200,
											bullishTrend = closePrice > sma50 && sma50 > sma200,
											bearishSignal.set(bearishTrend ? 1 : 0),
											bullishSignal.set(bullishTrend ? 1 : 0)
										} else
											bearishSignal.set(1),
											bullishSignal.set(1);

										// console.log(bearishSignal,bullishSignal)
										const emaPeriod = 14
										, shadowThreshold = 5
										, balancePercent = 100
										, bodyPercentage = 5
										, multiplier = 2
										, upperBound = Math.max(openPrice, closePrice)
										, upperVar = this._context.new_var(upperBound)
										, lowerBound = Math.min(openPrice, closePrice)
										, lowerVar = this._context.new_var(lowerBound)
										, bodySize = upperBound - lowerBound
										, bodySizeVar = this._context.new_var(bodySize)
										, emaBody = PineJS.Std.ema(bodySizeVar, emaPeriod, this._context)
										, smallBody = bodySize < emaBody
										, smallBodyVar = this._context.new_var(smallBody ? 1 : 0)
										, largeBody = bodySize > emaBody
										, largeBodyVar = this._context.new_var(largeBody ? 1 : 0)
										, upperShadow = highPrice - upperBound
										, upperShadowVar = this._context.new_var(upperShadow)
										, lowerShadow = lowerBound - lowPrice
										, lowerShadowVar = this._context.new_var(lowerShadow)
										, longUpperShadow = upperShadow > shadowThreshold / 100 * bodySize
										, longLowerShadow = lowerShadow > shadowThreshold / 100 * bodySize
										, greenCandle = openPrice < closePrice
										, greenVar = this._context.new_var(greenCandle ? 1 : 0)
										, redCandle = openPrice > closePrice
										, redVar = this._context.new_var(redCandle ? 1 : 0)
										, totalRange = highPrice - lowPrice
										, rangeVar = this._context.new_var(totalRange)
										, midPoint = bodySize / 2 + lowerBound
										, midVar = this._context.new_var(midPoint)
										, upperRatio = Math.abs(upperShadow - lowerShadow) / lowerShadow * 100
										, lowerRatio = Math.abs(lowerShadow - upperShadow) / upperShadow * 100
										, shadowBalance = upperShadow === lowerShadow || upperRatio < balancePercent && lowerRatio < balancePercent
										, isSmallBody = totalRange > 0 && bodySize <= totalRange * (bodyPercentage / 100)
										, smallBodyPattern = this._context.new_var(isSmallBody ? 1 : 0)
										, dojiPattern = isSmallBody && shadowBalance
										, dojiVar = this._context.new_var(dojiPattern ? 1 : 0)
										, pattern1Enabled = this._input(2);
										
										// console.log(longLowerShadow)
										if (pattern1Enabled && enableBullish) {
											const bearFilter = bearishSignal.get(2)
											, redPrev = redVar.get(2)
											, smallPrev = smallBodyPattern.get(1)
											, lowPrev = lowVar.get(2)
											, highCurr = highVar.get(1)
											, isGreen = greenCandle
											, currentLow = lowPrice
											, prevHigh = highVar.get(1);
											bearFilter && redPrev && smallPrev && lowPrev > highCurr && isGreen && prevHigh < currentLow && (patterns[0] = 1)
										}
										if (pattern1Enabled && enableBearish) {
											const bullFilter = bullishSignal.get(2)
											, greenPrev = greenVar.get(2)
											, smallPrev = smallBodyPattern.get(1)
											, highPrev = highVar.get(2)
											, lowCurr = lowVar.get(1);
											bullFilter && greenPrev && smallPrev && highPrev < lowCurr && redCandle && lowCurr > highPrice && (patterns[1] = 1)
										}
										if (this._input(3) && enableBearish) {
											const bullFilter = bullishSignal.get(1)
											, greenPrev = greenVar.get(1)
											, largePrev = largeBodyVar.get(1)
											, highPrev = highVar.get(1)
											, midPrev = midVar.get(1)
											, openPrev = openVar.get(1);
											bullFilter && greenPrev && largePrev && redCandle && openPrice >= highPrev && closePrice < midPrev && closePrice > openPrev && (patterns[2] = 1)
										}
										if (this._input(4)) {
											!dojiPattern || isSmallBody && upperShadow <= bodySize || isSmallBody && lowerShadow <= bodySize || (patterns[3] = 1)
										}
										const pattern5Enabled = this._input(5);
										if (pattern5Enabled && enableBullish) {
											const bearFilter = bearishTrend
											, redPrev = redVar.get(1)
											, largePrev = largeBodyVar.get(1)
											, smallCurr = isSmallBody
											, upperCurr = upperBound
											, lowerPrev = lowerVar.get(1);
											bearFilter && redPrev && largePrev && smallCurr && upperCurr < lowerPrev && (patterns[4] = 1)
										}
										if (pattern5Enabled && enableBearish) {
											const bullFilter = bullishTrend
											, greenPrev = greenVar.get(1)
											, largePrev = largeBodyVar.get(1)
											, smallCurr = isSmallBody
											, lowerCurr = lowerBound
											, upperPrev = upperVar.get(1);
											bullFilter && greenPrev && largePrev && smallCurr && lowerCurr > upperPrev && (patterns[5] = 1)
										}
										if (this._input(6) && enableBearish) {
											const largePrev2 = largeBodyVar.get(2)
											, smallPrev1 = smallBodyVar.get(1)
											, bearFilter = bearishTrend
											, redPrev2 = redVar.get(2)
											, upperPrev1 = upperVar.get(1)
											, lowerPrev2 = lowerVar.get(2)
											, redPrev1 = redVar.get(1);
											largePrev2 && smallPrev1 && bearFilter && redPrev2 && upperPrev1 < lowerPrev2 && redPrev1 && greenCandle && upperBound <= lowerPrev2 && upperBound >= upperPrev1 && (patterns[6] = 1)
										}
										if (this._input(7) && enableBullish) {
											isSmallBody && upperShadow <= bodySize && (patterns[7] = 1)
										}
										const pattern8Enabled = this._input(8);
										if (pattern8Enabled && enableBullish) {
											const bearFilter = bearishTrend
											, isGreen = greenCandle
											, isLarge = largeBody
											, redPrev = redVar.get(1)
											, smallPrev = smallBodyVar.get(1)
											, currClose = closePrice
											, prevOpen = openVar.get(1)
											, currOpen = openPrice
											, prevClose = closeVar.get(1);
											bearFilter && isGreen && isLarge && redPrev && smallPrev && currClose >= prevOpen && currOpen <= prevClose && (currClose > prevOpen || currOpen < prevClose) && (patterns[8] = 1)
										}
										if (pattern8Enabled && enableBearish) {
											const bullFilter = bullishTrend
											, isRed = redCandle
											, isLarge = largeBody
											, greenPrev = greenVar.get(1)
											, smallPrev = smallBodyVar.get(1)
											, currClose = closePrice
											, prevOpen = openVar.get(1)
											, currOpen = openPrice
											, prevClose = closeVar.get(1);
											bullFilter && isRed && isLarge && greenPrev && smallPrev && currClose <= prevOpen && currOpen >= prevClose && (currClose < prevOpen || currOpen > prevClose) && (patterns[9] = 1)
										}
										if (this._input(9) && enableBearish) {
											const largePrev2 = largeBodyVar.get(2)
											, smallPrev1 = smallBodyPattern.get(1)
											, isLarge = largeBody
											, bullFilter = bullishTrend
											, greenPrev2 = greenVar.get(2)
											, isRed = redCandle
											, midPrev2 = midVar.get(2)
											, lowerCurr = lowerBound
											, lowerPrev1 = lowerVar.get(1)
											, upperPrev2 = upperVar.get(2);
											largePrev2 && smallPrev1 && isLarge && bullFilter && greenPrev2 && lowerPrev1 > upperPrev2 && isRed && lowerCurr <= midPrev2 && lowerCurr > lowerPrev1 && lowerPrev1 > upperPrev2 && (patterns[10] = 1)
										}
										if (this._input(10) && enableBearish) {
											const largePrev2 = largeBodyVar.get(2)
											, smallPrev1 = smallBodyVar.get(1)
											, isLarge = largeBody
											, bullFilter = bullishTrend
											, greenPrev2 = greenVar.get(2)
											, isRed = redCandle
											, lowerPrev1 = lowerVar.get(1)
											, lowerPrev2 = lowerVar.get(2)
											, upperPrev2 = upperVar.get(2)
											, upperCurr = upperBound
											, lowerCurr = lowerBound
											, midPrev2 = midVar.get(2);
											largePrev2 && smallPrev1 && isLarge && bullFilter && greenPrev2 && lowerPrev1 > upperPrev2 && isRed && lowerCurr <= midPrev2 && lowerCurr > lowerPrev2 && lowerPrev1 > upperCurr && (patterns[11] = 1)
										}
										if (this._input(11) && enableBearish) {
											const bearFilter4 = bearishSignal.get(4)
											, large4 = largeBodyVar.get(4)
											, red4 = redVar.get(4)
											, small3 = smallBodyVar.get(3)
											, green3 = greenVar.get(3)
											, open3 = openVar.get(3)
											, close3 = closeVar.get(3)
											, small2 = smallBodyVar.get(2)
											, green2 = greenVar.get(2)
											, open2 = openVar.get(2)
											, close2 = closeVar.get(2)
											, small1 = smallBodyVar.get(1)
											, green1 = greenVar.get(1)
											, open1 = openVar.get(1)
											, close1 = closeVar.get(1)
											, low4 = lowVar.get(4)
											, high4 = highVar.get(4)
											, close4 = closeVar.get(4);
											bearFilter4 && large4 && red4 && small3 && green3 && open3 > low4 && close3 < high4 && small2 && green2 && open2 > low4 && close2 < high4 && small1 && green1 && open1 > low4 && close1 < high4 && largeBody && redCandle && closePrice < close4 && (patterns[12] = 1)
										}
										if (this._input(12) && enableBearish) {
											const bearFilter1 = bearishSignal.get(1)
											, prevRange = highVar.get(1) - lowVar.get(1)
											, currRange = totalRange
											, prevLow = lowVar.get(1);
											bearFilter1 && 0 !== currRange && 0 !== prevRange && highPrice < prevLow && (patterns[13] = 1)
										}
										if (this._input(13) && enableBearish) {
											isSmallBody && lowerShadow <= bodySize && (patterns[14] = 1)
										}
										if (this._input(14) && enableBullish) {
											smallBody && bodySize > 0 && lowerBound > (highPrice + lowPrice) / 2 && lowerShadow >= multiplier * bodySize && !longUpperShadow && bearishTrend && (patterns[15] = 1)
										}
										if (this._input(15) && enableBearish) {
											smallBody && bodySize > 0 && lowerBound > (highPrice + lowPrice) / 2 && lowerShadow >= multiplier * bodySize && !longUpperShadow && bullishTrend && (patterns[16] = 1)
										}
										const pattern16Enabled = this._input(16);
										if (pattern16Enabled && enableBullish) {
											const largePrev = largeBodyVar.get(1)
											, redPrev = redVar.get(1)
											, bearFilter = bearishSignal.get(1)
											, smallCurr = isSmallBody
											, highWithin = highPrice <= upperVar.get(1)
											, lowWithin = lowPrice >= lowerVar.get(1);
											largePrev && redPrev && bearFilter && smallCurr && highWithin && lowWithin && (patterns[17] = 1)
										}
										if (pattern16Enabled && enableBearish) {
											const largePrev = largeBodyVar.get(1)
											, greenPrev = greenVar.get(1)
											, bullFilter = bullishSignal.get(1)
											, smallCurr = isSmallBody
											, highWithin = highPrice <= upperVar.get(1)
											, lowWithin = lowPrice >= lowerVar.get(1);
											largePrev && greenPrev && bullFilter && smallCurr && highWithin && lowWithin && (patterns[18] = 1)
										}
										const pattern17Enabled = this._input(17);
										if (pattern17Enabled && enableBullish) {
											const largePrev = largeBodyVar.get(1)
											, redPrev = redVar.get(1)
											, bearFilter = bearishSignal.get(1)
											, isGreen = greenCandle
											, isSmall = smallBody
											, highWithin = highPrice <= upperVar.get(1)
											, lowWithin = lowPrice >= lowerVar.get(1);
											largePrev && redPrev && bearFilter && isGreen && isSmall && highWithin && lowWithin && (patterns[19] = 1)
										}
										if (pattern17Enabled && enableBearish) {
											const largePrev = largeBodyVar.get(1)
											, greenPrev = greenVar.get(1)
											, bullFilter = bullishSignal.get(1)
											, isRed = redCandle
											, isSmall = smallBody
											, highWithin = highPrice <= upperVar.get(1)
											, lowWithin = lowPrice >= lowerVar.get(1);
											largePrev && greenPrev && bullFilter && isRed && isSmall && highWithin && lowWithin && (patterns[20] = 1)
										}
										if (this._input(18) && enableBullish) {
											// console.log(smallBody , bodySize > 0 , upperBound < (highPrice + lowPrice) / 2 , upperShadow >= multiplier * bodySize , !longLowerShadow , bearishTrend , (patterns[21] = 1))
											smallBody && bodySize > 0 && upperBound < (highPrice + lowPrice) / 2 && upperShadow >= multiplier * bodySize && !longLowerShadow && bearishTrend && (patterns[21] = 1)
										}
										const pattern19Enabled = this._input(19);
										if (pattern19Enabled && enableBullish) {
											const threshold = 5
											, conditions = largeBody && highPrice - upperBound <= threshold / 100 * bodySize && lowerBound - lowPrice <= threshold / 100 * bodySize && greenCandle
											, highDiff = highVar.get(1) - upperVar.get(1)
											, lowDiff = lowerVar.get(1) - lowVar.get(1)
											, bodyPrev = bodySizeVar.get(1)
											, largePrev = largeBodyVar.get(1)
											, redPrev = redVar.get(1);
											conditions && (largePrev && highDiff <= threshold / 100 * bodyPrev && lowDiff <= threshold / 100 * bodyPrev && redPrev) && highVar.get(1) < lowPrice && (patterns[22] = 1)
										}
										// console.log(pattern19Enabled,enableBearish)
										if (pattern19Enabled && enableBearish) {
											const threshold = 5
											, conditions = largeBody && highPrice - upperBound <= threshold / 100 * bodySize && lowerBound - lowPrice <= threshold / 100 * bodySize && redCandle
											, highDiff = highVar.get(1) - upperVar.get(1)
											, lowDiff = lowerVar.get(1) - lowVar.get(1)
											, bodyPrev = bodySizeVar.get(1)
											, largePrev = largeBodyVar.get(1)
											, greenPrev = greenVar.get(1);
											// console.log(conditions, [largePrev , highDiff <= threshold / 100 * bodyPrev ,lowDiff <= threshold / 100 * bodyPrev , greenPrev] , lowVar.get(1) > highPrice  )
											conditions && (largePrev && highDiff <= threshold / 100 * bodyPrev && lowDiff <= threshold / 100 * bodyPrev && greenPrev) && lowVar.get(1) > highPrice && (patterns[23] = 1)
										}
										if (this._input(20) && enableBullish) {
											lowerShadow > totalRange / 100 * 75 && (patterns[24] = 1)
										}
										if (this._input(21) && enableBearish) {
											upperShadow > totalRange / 100 * 75 && (patterns[25] = 1)
										}
										if (this._input(22) && enableBearish) {
											const threshold = 5;
											redCandle && largeBody && upperShadow <= threshold / 100 * bodySize && lowerShadow <= threshold / 100 * bodySize && (patterns[26] = 1)
										}
										if (this._input(23) && enableBullish) {
											const threshold = 5;
											greenCandle && largeBody && upperShadow <= threshold / 100 * bodySize && lowerShadow <= threshold / 100 * bodySize && (patterns[27] = 1)
										}
										if (this._input(24) && enableBullish) {
											const largePrev2 = largeBodyVar.get(2)
											, smallPrev1 = smallBodyPattern.get(1)
											, isLarge = largeBody
											, bearFilter = bearishTrend
											, redPrev2 = redVar.get(2)
											, upperPrev1 = upperVar.get(1)
											, lowerPrev2 = lowerVar.get(2)
											, isGreen = greenCandle
											, upperCurr = upperBound
											, midPrev2 = midVar.get(2)
											, upperPrev2 = upperVar.get(2);
											largePrev2 && smallPrev1 && isLarge && bearFilter && redPrev2 && upperPrev1 < lowerPrev2 && isGreen && upperCurr >= midPrev2 && upperCurr < upperPrev2 && upperPrev1 < lowerBound && (patterns[28] = 1)
										}
										if (this._input(25) && enableBullish) {
											const largePrev2 = largeBodyVar.get(2)
											, smallPrev1 = smallBodyVar.get(1)
											, isLarge = largeBody
											, bearFilter = bearishTrend
											, redPrev2 = redVar.get(2)
											, upperPrev1 = upperVar.get(1)
											, lowerPrev2 = lowerVar.get(2)
											, isGreen = greenCandle
											, upperCurr = upperBound
											, midPrev2 = midVar.get(2)
											, upperPrev2 = upperVar.get(2);
											largePrev2 && smallPrev1 && isLarge && bearFilter && redPrev2 && upperPrev1 < lowerPrev2 && isGreen && upperCurr >= midPrev2 && upperCurr < upperPrev2 && upperPrev1 < lowerBound && (patterns[29] = 1)
										}
										if (this._input(26) && enableBullish) {
											const bearFilter = bearishTrend
											, redPrev = redVar.get(1)
											, largePrev = largeBodyVar.get(1)
											, isGreen = greenCandle
											, isSmall = smallBody
											, currOpen = openPrice
											, prevClose = closeVar.get(1)
											, currClose = closePrice
											, prevLow = lowVar.get(1)
											, currRange = totalRange
											, emaVal = emaBody
											, closeDiff = Math.abs(currClose - prevLow);
											bearFilter && redPrev && largePrev && isGreen && currOpen < prevClose && isSmall && 0 !== currRange && closeDiff <= .05 * emaVal && (patterns[30] = 1)
										}
										if (this._input(27) && enableBullish) {
											const bearFilter = bearishSignal.get(1)
											, redPrev = redVar.get(1)
											, largePrev = largeBodyVar.get(1)
											, isGreen = greenCandle
											, currOpen = openPrice
											, currClose = closePrice
											, prevLow = lowVar.get(1)
											, prevOpen = openVar.get(1)
											, midPrev = midVar.get(1);
											bearFilter && redPrev && largePrev && isGreen && currOpen <= prevLow && currClose > midPrev && currClose < prevOpen && (patterns[31] = 1)
										}
										if (this._input(28) && enableBullish) {
											const bullFilter4 = bullishSignal.get(4)
											, large4 = largeBodyVar.get(4)
											, green4 = greenVar.get(4)
											, small3 = smallBodyVar.get(3)
											, red3 = redVar.get(3)
											, open3 = openVar.get(3)
											, close3 = closeVar.get(3)
											, small2 = smallBodyVar.get(2)
											, red2 = redVar.get(2)
											, open2 = openVar.get(2)
											, close2 = closeVar.get(2)
											, small1 = smallBodyVar.get(1)
											, red1 = redVar.get(1)
											, open1 = openVar.get(1)
											, close1 = closeVar.get(1)
											, isLarge = largeBody
											, isGreen = greenCandle
											, currClose = closePrice
											, high4 = highVar.get(4)
											, low4 = lowVar.get(4)
											, close4 = closeVar.get(4);
											bullFilter4 && large4 && green4 && small3 && red3 && open3 < high4 && close3 > low4 && small2 && red2 && open2 < high4 && close2 > low4 && small1 && red1 && open1 < high4 && close1 > low4 && isLarge && isGreen && currClose > close4 && (patterns[32] = 1)
										}
										if (this._input(29) && enableBullish) {
											const bullFilter = bullishSignal.get(1)
											, currRange = totalRange
											, prevRange = rangeVar.get(1)
											, currLow = lowPrice
											, prevHigh = highVar.get(1);
											bullFilter && 0 !== currRange && 0 !== prevRange && currLow > prevHigh && (patterns[33] = 1)
										}
										if (this._input(30) && enableBearish) {
											smallBody && bodySize > 0 && upperBound < (highPrice + lowPrice) / 2 && upperShadow >= multiplier * bodySize && !longLowerShadow && bullishTrend && (patterns[34] = 1)
										}
										if (this._input(31)) {
											const threshold = totalRange / 100 * 34;
											lowerShadow >= threshold && upperShadow >= threshold && !isSmallBody && redCandle && (patterns[35] = 1)
										}
										if (this._input(32)) {
											const threshold = totalRange / 100 * 34;
											lowerShadow >= threshold && upperShadow >= threshold && !isSmallBody && greenCandle && (patterns[36] = 1)
										}
										if (this._input(33) && enableBearish) {
											const threshold = 5
											, isLarge = largeBody
											, largePrev1 = largeBodyVar.get(1)
											, largePrev2 = largeBodyVar.get(2)
											, isRed = redCandle
											, redPrev1 = redVar.get(1)
											, redPrev2 = redVar.get(2)
											, lowerCurr = lowerShadow
											, lowerPrev1 = lowerShadowVar.get(1)
											, lowerPrev2 = lowerShadowVar.get(2)
											, rangeCurr = totalRange
											, rangePrev1 = rangeVar.get(1)
											, rangePrev2 = rangeVar.get(2)
											, closeCurr = closePrice
											, closePrev1 = closeVar.get(1)
											, closePrev2 = closeVar.get(2)
											, openCurr = openPrice
											, openPrev1 = openVar.get(1)
											, openPrev2 = openVar.get(2);
											isLarge && largePrev1 && largePrev2 && isRed && redPrev1 && redPrev2 && rangeCurr * threshold / 100 > lowerCurr && rangePrev1 * threshold / 100 > lowerPrev1 && rangePrev2 * threshold / 100 > lowerPrev2 && (closeCurr < closePrev1 && closePrev1 < closePrev2 && openCurr > closePrev1 && openCurr < openPrev1 && openPrev1 > closePrev2 && openPrev1 < openPrev2) && (patterns[37] = 1)
										}
										if (this._input(34) && enableBullish) {
											const threshold = 5
											, isLarge = largeBody
											, largePrev1 = largeBodyVar.get(1)
											, largePrev2 = largeBodyVar.get(2)
											, isGreen = greenCandle
											, greenPrev1 = greenVar.get(1)
											, greenPrev2 = greenVar.get(2)
											, rangeCurr = totalRange
											, rangePrev1 = rangeVar.get(1)
											, rangePrev2 = rangeVar.get(2)
											, upperCurr = upperShadow
											, upperPrev1 = upperShadowVar.get(1)
											, upperPrev2 = upperShadowVar.get(2)
											, closeCurr = closePrice
											, closePrev1 = closeVar.get(1)
											, closePrev2 = closeVar.get(2)
											, openCurr = openPrice
											, openPrev1 = openVar.get(1)
											, openPrev2 = openVar.get(2);
											 
											isLarge && largePrev1 && largePrev2 && isGreen && greenPrev1 && greenPrev2 && rangeCurr * threshold / 100 > upperCurr && rangePrev1 * threshold / 100 > upperPrev1 && rangePrev2 * threshold / 100 > upperPrev2 && (closeCurr > closePrev1 && closePrev1 > closePrev2 && openCurr < closePrev1 && openCurr > openPrev1 && openPrev1 < closePrev2 && openPrev1 > openPrev2) && (patterns[38] = 1)

										}

											
										const pattern35Enabled = this._input(35);
										if (pattern35Enabled && enableBullish) {
											const currentDoji = dojiPattern
											, dojiPrev1 = dojiVar.get(1)
											, dojiPrev2 = dojiVar.get(2)
											, lowerCurr = lowerBound
											, upperPrev1 = upperVar.get(1)
											, lowerPrev2 = lowerVar.get(2)
											, bearFilter2 = bearishSignal.get(2);
											currentDoji && dojiPrev1 && dojiPrev2 && bearFilter2 && lowerPrev2 > upperPrev1 && upperPrev1 < lowerCurr && (patterns[39] = 1)
										}
										if (pattern35Enabled && enableBearish) {
											const currentDoji = dojiPattern
											, dojiPrev1 = dojiVar.get(1)
											, dojiPrev2 = dojiVar.get(2)
											, bullFilter2 = bullishSignal.get(2)
											, lowerCurr = lowerBound
											, lowerPrev1 = lowerVar.get(1)
											, upperPrev1 = upperVar.get(1)
											, upperPrev2 = upperVar.get(2);
											currentDoji && dojiPrev1 && dojiPrev2 && bullFilter2 && upperPrev2 < lowerPrev1 && lowerCurr > upperPrev1 && (patterns[40] = 1)
										}
										if (this._input(36) && enableBullish) {
											const bearFilter = bearishSignal.get(1)
											, smallCurr = isSmallBody
											, longUpper = longUpperShadow
											, longLower = longLowerShadow
											, lowCurr = lowVar.get(0)
											, lowPrev = lowVar.get(1)
											, lowBalance = Math.abs(lowCurr - lowPrev) <= .05 * emaBody
											, redPrev = redVar.get(1)
											, isGreen = greenCandle
											, largePrev = largeBodyVar.get(1);
											bearFilter && (!smallCurr || longUpper && longLower) && lowBalance && redPrev && isGreen && largePrev && (patterns[41] = 1)
										}
										if (this._input(37) && enableBearish) {
											const bullFilter = bullishSignal.get(1)
											, smallCurr = isSmallBody
											, longUpper = longUpperShadow
											, longLower = longLowerShadow
											, highCurr = highVar.get(0)
											, highPrev = highVar.get(1)
											, highBalance = Math.abs(highCurr - highPrev) <= .05 * emaBody
											, greenPrev = greenVar.get(1)
											, isRed = redCandle
											, largePrev = largeBodyVar.get(1);
											bullFilter && (!smallCurr || longUpper && longLower) && highBalance && greenPrev && isRed && largePrev && (patterns[42] = 1)
										}
										if (this._input(38) && enableBullish) {
											const largePrev2 = largeBodyVar.get(2)
											, greenPrev2 = greenVar.get(2)
											, smallPrev1 = smallBodyVar.get(1)
											, greenPrev1 = greenVar.get(1)
											, lowerPrev1 = lowerVar.get(1)
											, upperPrev2 = upperVar.get(2);
											largePrev2 && smallPrev1 && bullishTrend && greenPrev2 && greenPrev1 && redCandle && lowerPrev1 > upperPrev2 && (lowerBound >= upperPrev2 && lowerBound <= lowerPrev1) && (patterns[43] = 1)
										}
										return patterns
									}
								}
							},
							{
								name: 'Flexible SMA',
								metainfo: {
									_metainfoVersion: 51,
									id: 'flexible_sma@tv-basicstudies-1',
									name: 'Flexible SMA',
									description: 'Simple Moving Average for OHLCV and Open Interest',
									shortDescription: 'Flex SMA',
									isCustomIndicator: true,
									is_price_study: false,
									format: {
										type: 'price',
										precision: 2,
									},
									plots: [
										{
											id: 'plot_0',
											type: 'line',
										},
										{
											id: 'plot_1',
											type: 'line',
										}
									],
									defaults: {
										styles: {
											plot_0: {
												linestyle: 0,
												linewidth: 1,
												plottype: 0,
												trackPrice: false,
												transparency: 50,
												visible: true,
												color: '#9E9E9E',
												title: 'Raw Data'
											},
											plot_1: {
												linestyle: 0,
												linewidth: 2,
												plottype: 0,
												trackPrice: false,
												transparency: 0,
												visible: true,
												color: '#FF5722',
												title: 'SMA'
											}
										},
										precision: 2,
										inputs: {
											period: 14,
											source2: 'close'
										}
									},
									styles: {
										plot_0: {
											title: 'Raw Data',
											histogramBase: 0,
										},
										plot_1: {
											title: 'SMA',
											histogramBase: 0,
										}
									},
									inputs: [
										{
											id: 'period',
											name: 'SMA Period',
											defval: 14,
											type: 'integer',
											min: 1,
											max: 500
										},
										{
											id: 'source2',
											name: 'Data Source',
											defval: 'close',
											type: 'text',
											options: window.sourcesConfig.sources
										}
									]
								},
								constructor: function() {
									this.init = function(context, inputCallback) {
										this._context = context;
										this._input = inputCallback;
									};
									
									this.main = function(context, inputCallback) {
										this._context = context;
										this._input = inputCallback;

										this._context.select_sym(0);
										
										// Get inputs
										const period = this._input(0);
										// console.log(period)
										const source = this._input(1);
										// console.log(source)
										// Get the raw data value based on selected source
										let rawValue = 0;
										
										switch(source) {
											case 'open':
												rawValue = PineJS.Std.open(this._context);
												break;
											case 'high':
												rawValue = PineJS.Std.high(this._context);
												break;
											case 'low':
												rawValue = PineJS.Std.low(this._context);
												break;
											case 'close':
												rawValue = PineJS.Std.close(this._context);
												break;
											case 'volume':
												rawValue = PineJS.Std.volume(this._context);
												break;
											case 'oi':
												if(window.bars && window.bars[PineJS.Std.time(this._context)]) {
													rawValue = window.bars[PineJS.Std.time(this._context)].oi;
												} else {
													rawValue = 0;
												}
												break;
											default:
												console.log('Invalid source:', source);
												rawValue = PineJS.Std.close(this._context);
										}

										// Create a new variable for the raw value
										const rawVar = this._context.new_var(rawValue);
										
										// Calculate SMA using TradingView's built-in function
										const smaValue = PineJS.Std.sma(rawVar, period, this._context);

										// console.log(rawVar, period)
										// Return both raw value and its SMA
										return [rawValue, smaValue];
									};
								}
							},
							{
								name: 'Flexible RSI',
								metainfo: {
									_metainfoVersion: 51,
									id: 'flexible_rsi@tv-basicstudies-1',
									name: 'Flexible RSI',
									description: 'Relative Strength Index for OHLCV and Open Interest',
									shortDescription: 'Flex RSI',
									isCustomIndicator: true,
									is_price_study: false,
									format: {
										type: 'price',
										precision: 2,
									},
									plots: [
										{
											id: 'plot_0',
											type: 'line',
										},
										{
											id: 'plot_1',
											type: 'line',
										},
										{
											id: 'plot_2',
											type: 'line',
										},
										{
											id: 'plot_3',
											type: 'line',
										}
									],
									defaults: {
										styles: {
											plot_0: {
												linestyle: 0,
												linewidth: 1,
												plottype: 0,
												trackPrice: false,
												transparency: 50,
												visible: false,
												color: '#9E9E9E',
												title: 'Raw Data'
											},
											plot_1: {
												linestyle: 0,
												linewidth: 2,
												plottype: 0,
												trackPrice: false,
												transparency: 0,
												visible: true,
												color: '#2196F3',
												title: 'RSI'
											},
											plot_2: {
												linestyle: 2,
												linewidth: 1,
												plottype: 0,
												trackPrice: false,
												transparency: 0,
												visible: true,
												color: '#FF5722',
												title: 'Overbought (70)'
											},
											plot_3: {
												linestyle: 2,
												linewidth: 1,
												plottype: 0,
												trackPrice: false,
												transparency: 0,
												visible: true,
												color: '#4CAF50',
												title: 'Oversold (30)'
											}
										},
										precision: 2,
										inputs: {
											period: 14,
											source2: 'close',
											overbought: 70,
											oversold: 30
										}
									},
									styles: {
										plot_0: {
											title: 'Raw Data',
											histogramBase: 0,
										},
										plot_1: {
											title: 'RSI',
											histogramBase: 0,
										},
										plot_2: {
											title: 'Overbought Level',
											histogramBase: 0,
										},
										plot_3: {
											title: 'Oversold Level',
											histogramBase: 0,
										}
									},
									inputs: [
										{
											id: 'period',
											name: 'RSI Period',
											defval: 14,
											type: 'integer',
											min: 2,
											max: 500
										},
										{
											id: 'source2',
											name: 'Data Source',
											defval: 'close',
											type: 'text',
											options: window.sourcesConfig.sources
										},
										{
											id: 'overbought',
											name: 'Overbought Level',
											defval: 70,
											type: 'integer',
											min: 50,
											max: 100
										},
										{
											id: 'oversold',
											name: 'Oversold Level',
											defval: 30,
											type: 'integer',
											min: 0,
											max: 50
										}
									]
								},
								constructor: function() {
									this.init = function(context, inputCallback) {
										this._context = context;
										this._input = inputCallback;
									};

									this.main = function(context, inputCallback) {
										this._context = context;
										this._input = inputCallback;

										this._context.select_sym(0);

										// Get inputs
										const period = this._input(0);
										const source = this._input(1);
										const overboughtLevel = this._input(2);
										const oversoldLevel = this._input(3);

										// Set minimum depth for RSI calculation
										this._context.setMinimumAdditionalDepth(period);

										// Get the raw data value based on selected source
										let rawValue = 0;

										switch(source) {
											case 'open':
												rawValue = PineJS.Std.open(this._context);
												break;
											case 'high':
												rawValue = PineJS.Std.high(this._context);
												break;
											case 'low':
												rawValue = PineJS.Std.low(this._context);
												break;
											case 'close':
												rawValue = PineJS.Std.close(this._context);
												break;
											case 'volume':
												rawValue = PineJS.Std.volume(this._context);
												break;
											case 'oi':
												if(window.bars && window.bars[PineJS.Std.time(this._context)]) {
													rawValue = window.bars[PineJS.Std.time(this._context)].oi;
												} else {
													rawValue = 0;
												}
												break;
											default:
												rawValue = PineJS.Std.close(this._context);
										}

										// Create a new variable for the raw value
										const rawVar = this._context.new_var(rawValue);

										// Try to use TradingView's built-in RSI function first
										let rsiValue;

										// Manual RSI calculation as fallback
										// Calculate price changes
										const change = PineJS.Std.change(rawVar);

										// Separate gains and losses manually
										const gains = PineJS.Std.max(change, 0);
										const minChange = PineJS.Std.min(change, 0);
										// Convert negative losses to positive by multiplying by -1
										const losses = this._context.new_var(-1 * minChange);

										console.log(gains)
										console.log(losses)
										// Create variables for gains and losses
										const gainsVar = this._context.new_var(gains);
										const lossesVar = losses;

										// Calculate RMA (Relative Moving Average) for gains and losses
										const avgGains = PineJS.Std.rma(gainsVar, period, this._context);
										const avgLosses = PineJS.Std.rma(lossesVar, period, this._context);

										// Simple RSI calculation: 100 - (100 / (1 + avgGains/avgLosses))
										// Handle division by zero
										if (avgLosses === 0) {
											rsiValue = 100;
										} else if (avgGains === 0) {
											rsiValue = 0;
										} else {
											const rs = avgGains / avgLosses;
											rsiValue = 100 - (100 / (1 + rs));
										}

										// Debug logging
										// console.log(`Flexible RSI Debug - Source: ${source}, Period: ${period}, Raw: ${rawValue}, RSI: ${rsiValue}`);
										console.log([PineJS.Std.time(this._context),rsiValue])
										// Return raw value, RSI, overbought level, and oversold level
										return [rawValue, rsiValue, overboughtLevel, oversoldLevel];
									};
								}
							},
							{
								name: 'Open Interest',
								metainfo: {
									_metainfoVersion: 51,
									id: 'openinterest@tv-basicstudies-1',
									name: 'Open Interest',
									description: 'Open Interest Indicator',
									shortDescription: 'OI',
									isCustomIndicator: true,
									is_price_study: false,
									format: {
										type: 'price',
										precision: 0,
									},
									plots: [
										{
											id: 'plot_0',
											type: 'line',
										}
									],
									defaults: {
										styles: {
											plot_0: {
												linestyle: 0,
												linewidth: 2,
												plottype: 0,
												trackPrice: false,
												transparency: 0,
												visible: true,
												color: '#2196F3'
											}
										},
										precision: 0,
										inputs: {
										}
									},
									styles: {
										plot_0: {
											title: 'Open Interest',
											histogramBase: 0,
										}
									},
									inputs: [
									]
								},
								constructor: function() {
									this.main = function(context, inputCallback) {
										this._context = context;
										this._input = inputCallback;

										this._context.select_sym(0);
										// //console.log(/)
										// Get close price using PineJS
										const close = PineJS.Std.close(this._context);
										const volume = PineJS.Std.volume(this._context);

										if(window.bars[PineJS.Std.time(this._context)]){
											var openInterest =window.bars[PineJS.Std.time(this._context)].oi
										}else{
											var openInterest =0
										}

										//console.log('OI Calculated:', openInterest, 'Close:', close, 'Volume:', volume);

										return [openInterest];
									};
								}
							}
						]);
					},

					// Set preferred timezone to UTC+0530 (Asia/Kolkata)
					timezone: 'Asia/Kolkata',

					// // Enable save/load layouts
					// save_load_adapter: {
						
					// 	getAllStudyTemplates: function () {
					// 		return Promise.resolve(this.studyTemplates);
					// 	},
					// 	getAllCharts: function() {
					// 		return new Promise(function(resolve) {
					// 			const savedLayouts = getSavedLayouts();
					// 			resolve(savedLayouts);
					// 		});
					// 	},
					// 	removeChart: function(chartId) {
					// 		return new Promise(function(resolve) {
					// 			removeLayout(chartId);
					// 			resolve();
					// 		});
					// 	},
					// 	saveChart: function(chartData) {
					// 		return new Promise(function(resolve) {
					// 			const layoutId = saveLayout(chartData);
					// 			resolve(layoutId);
					// 		});
					// 	},
					// 	getChartContent: function(chartId) {
					// 		return new Promise(function(resolve) {
					// 			const layout = getLayout(chartId);
					// 			resolve(layout);
					// 		});
					// 	}
					// },

					save_load_adapter:save_load_adapter,
					widgetbar: {
						details: false,
						news: false,
						watchlist: true,
						datawindow: true,
						watchlist_settings: {
							default_symbols: defaultSymbols,
						},
					},

					// Custom details provider
					details_provider: function(symbol, callback) {
						getSymbolDetails(symbol).then(function(details) {
							callback(details);
						}).catch(function(error) {
							console.error('Error fetching symbol details:', error);
							callback({
								title: symbol,
								items: [
									{ title: 'Error', value: 'Unable to load details' }
								]
							});
						});
					},

					// News provider for the news widget
					news_provider: function(symbol, callback) {
						fetchNews().then(function(newsItems) {
							callback({
								title: 'Market News',
								newsItems: newsItems || []
							});
						}).catch(function(e) {
							console.error('Error fetching news:', e);
							callback({
								title: 'Market News',
								newsItems: []
							});
						});
					},

					theme: theme,



				}));

				
				//console.log('🎯 TradingView widget initialized successfully');

				// Initialize WebSocket for live data
				//console.log('🔌 Initializing live data WebSocket...');
				initLiveDataWebSocket();

				// ========================================
				// INDICATOR MANAGEMENT SYSTEM
				// ========================================

				// Global indicator management object
				window.IndicatorManager = {
					widget: null,
					chart: null,
					indicators: new Map(), // Map of indicator ID -> indicator info

					// Initialize the indicator manager
					init: function(tvWidget) {
						this.widget = tvWidget;
						this.chart = tvWidget.chart();
						console.log('🔧 Indicator Manager initialized');
					},

					// Get all available indicators from TradingView
					getAvailableIndicators: function() {
						// Common TradingView indicators that support overlaying
						return [
							'Moving Average', 'Moving Average Exponential', 'Moving Average Weighted',
							'RSI', 'MACD', 'Bollinger Bands', 'Stochastic', 'Williams %R',
							'Flexible SMA', 'Flexible RSI',
							'Volume', 'ATR', 'CCI', 'Momentum', 'ROC', 'Aroon', 'ADX',
							'Parabolic SAR', 'Ichimoku Cloud', 'VWAP', 'Awesome Oscillator',
							'Chaikin Money Flow', 'Money Flow Index', 'On Balance Volume',
							'Price Volume Trend', 'Accumulation/Distribution', 'Force Index',
							'Ease of Movement', 'Trix', 'Ultimate Oscillator', 'Vortex Indicator',
							'Open Interest', 'OI Volume Ratio', 'Candlestick Patterns'
						];
					},

					// Add indicator with proper TradingView parameters
					addIndicator: function(indicatorName, options = {}) {
						if (!this.chart) {
							console.error('❌ Chart not available');
							return null;
						}

						try {
							const {
								forceOverlay = false,     // Force overlay on main chart or existing indicator
								inputs = [],              // Indicator inputs/parameters
								styles = {},              // Indicator styling
								callback = null           // Callback function
							} = options;

							console.log(`🔧 Adding indicator: ${indicatorName}`, options);

							// Use TradingView's createStudy method with proper parameters
							const studyId = this.chart.createStudy(
								indicatorName,    // Study name
								forceOverlay,     // Force overlay (true = overlay, false = new pane)
								false,            // Lock (always false for user studies)
								inputs,           // Study inputs
								callback,         // Callback function
								styles            // Study styles
							);

							if (studyId) {
								// Store indicator info
								this.indicators.set(studyId, {
									id: studyId,
									name: indicatorName,
									forceOverlay: forceOverlay,
									inputs: inputs,
									styles: styles,
									addedAt: new Date()
								});

								// Add indicator outputs as available sources
								window.sourcesConfig.addIndicatorSource(studyId, indicatorName);

								console.log(`✅ Indicator added successfully: ${indicatorName} (ID: ${studyId})`);
								console.log(`📊 Updated sources:`, window.sourcesConfig.sources);
								return studyId;
							} else {
								console.error(`❌ Failed to add indicator: ${indicatorName}`);
								return null;
							}
						} catch (error) {
							console.error(`❌ Error adding indicator ${indicatorName}:`, error);
							return null;
						}
					},

					// Add indicator as overlay on main chart
					addOverlayIndicator: function(indicatorName, inputs = [], styles = {}) {
						return this.addIndicator(indicatorName, {
							forceOverlay: true,
							inputs: inputs,
							styles: styles
						});
					},

					// Add indicator to new pane
					addIndicatorToNewPane: function(indicatorName, inputs = [], styles = {}) {
						return this.addIndicator(indicatorName, {
							forceOverlay: false,
							inputs: inputs,
							styles: styles
						});
					},

					// Remove indicator by ID
					removeIndicator: function(indicatorId) {
						if (!this.chart) {
							console.error('❌ Chart not available');
							return false;
						}

						try {
							// Remove indicator sources first
							window.sourcesConfig.removeIndicatorSource(indicatorId);

							this.chart.removeEntity(indicatorId);
							this.indicators.delete(indicatorId);
							console.log(`✅ Indicator removed: ${indicatorId}`);
							console.log(`📊 Updated sources:`, window.sourcesConfig.sources);
							return true;
						} catch (error) {
							console.error(`❌ Error removing indicator ${indicatorId}:`, error);
							return false;
						}
					},

					// Get all current indicators
					getAllIndicators: function() {
						if (!this.chart) return [];

						try {
							// Try to get studies from TradingView chart
							const studies = this.chart.getAllStudies();
							const indicatorList = [];

							studies.forEach(study => {
								const indicatorInfo = {
									id: study.id || study.entityId,
									name: study.name || study.title || 'Unknown Indicator',
									type: study.type || 'indicator'
								};

								// Update our internal tracking
								if (indicatorInfo.id && !this.indicators.has(indicatorInfo.id)) {
									this.indicators.set(indicatorInfo.id, {
										...indicatorInfo,
										addedAt: new Date()
									});
								}

								indicatorList.push(indicatorInfo);
							});

							console.log('📊 Current indicators:', indicatorList);
							return indicatorList;
						} catch (error) {
							console.error('❌ Error getting indicators:', error);
							// Fallback to internal tracking
							return Array.from(this.indicators.values());
						}
					},

					// Show indicator management UI
					showIndicatorUI: function() {
						this.createIndicatorManagementUI();
					},

					// Create indicator management UI
					createIndicatorManagementUI: function() {
						// Remove existing UI if present
						const existingUI = document.getElementById('indicator-management-ui');
						if (existingUI) {
							existingUI.remove();
						}

						// Create UI container
						const uiContainer = document.createElement('div');
						uiContainer.id = 'indicator-management-ui';
						uiContainer.style.cssText = `
							position: fixed;
							top: 50px;
							right: 20px;
							width: 350px;
							max-height: 80vh;
							background: #1e222d;
							border: 1px solid #3a3e4a;
							border-radius: 8px;
							box-shadow: 0 4px 12px rgba(0,0,0,0.3);
							z-index: 10000;
							font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
							color: #d1d4dc;
							overflow: hidden;
							display: flex;
							flex-direction: column;
						`;

						// Create header
						const header = document.createElement('div');
						header.style.cssText = `
							padding: 12px 16px;
							background: #2a2e39;
							border-bottom: 1px solid #3a3e4a;
							display: flex;
							justify-content: space-between;
							align-items: center;
							font-weight: 600;
						`;
						header.innerHTML = `
							<span>📊 Indicator Manager</span>
							<button onclick="document.getElementById('indicator-management-ui').remove()"
								style="background: none; border: none; color: #d1d4dc; cursor: pointer; font-size: 18px;">×</button>
						`;

						// Create content area
						const content = document.createElement('div');
						content.style.cssText = `
							padding: 16px;
							overflow-y: auto;
							flex: 1;
						`;

						// Add indicator section
						const addSection = document.createElement('div');
						addSection.style.marginBottom = '20px';
						addSection.innerHTML = `
							<h4 style="margin: 0 0 12px 0; color: #4CAF50;">Add New Indicator</h4>
							<select id="indicator-select" style="width: 100%; padding: 8px; margin-bottom: 8px; background: #2a2e39; border: 1px solid #3a3e4a; color: #d1d4dc; border-radius: 4px;">
								<option value="">Select an indicator...</option>
								${this.getAvailableIndicators().map(ind => `<option value="${ind}">${ind}</option>`).join('')}
							</select>
							<div style="display: flex; gap: 8px; margin-bottom: 8px;">
								<button id="add-new-pane" style="flex: 1; padding: 8px; background: #2196F3; border: none; color: white; border-radius: 4px; cursor: pointer;">New Pane</button>
								<button id="add-overlay" style="flex: 1; padding: 8px; background: #FF9800; border: none; color: white; border-radius: 4px; cursor: pointer;">Overlay</button>
							</div>
						`;

						// Current indicators section
						const currentSection = document.createElement('div');
						currentSection.innerHTML = `
							<h4 style="margin: 0 0 12px 0; color: #FF9800;">Current Indicators</h4>
							<div id="current-indicators-list"></div>
							<button id="refresh-indicators" style="width: 100%; padding: 8px; background: #4CAF50; border: none; color: white; border-radius: 4px; cursor: pointer; margin-top: 8px;">Refresh List</button>
						`;

						content.appendChild(addSection);
						content.appendChild(currentSection);
						uiContainer.appendChild(header);
						uiContainer.appendChild(content);
						document.body.appendChild(uiContainer);

						// Add event listeners
						this.setupIndicatorUIEvents();
						this.refreshIndicatorsList();
					},

					// Setup event listeners for the UI
					setupIndicatorUIEvents: function() {
						const addNewPaneBtn = document.getElementById('add-new-pane');
						const addOverlayBtn = document.getElementById('add-overlay');
						const indicatorSelect = document.getElementById('indicator-select');
						const refreshBtn = document.getElementById('refresh-indicators');

						// Add to new pane
						addNewPaneBtn.addEventListener('click', () => {
							const selectedIndicator = indicatorSelect.value;
							if (selectedIndicator) {
								this.addIndicatorToNewPane(selectedIndicator);
								this.refreshIndicatorsList();
								indicatorSelect.value = '';
							} else {
								alert('Please select an indicator first');
							}
						});

						// Add as overlay
						addOverlayBtn.addEventListener('click', () => {
							const selectedIndicator = indicatorSelect.value;
							if (selectedIndicator) {
								this.addOverlayIndicator(selectedIndicator);
								this.refreshIndicatorsList();
								indicatorSelect.value = '';
							} else {
								alert('Please select an indicator first');
							}
						});

						// Refresh indicators list
						refreshBtn.addEventListener('click', () => {
							this.refreshIndicatorsList();
						});
					},

					// Refresh the indicators list in UI
					refreshIndicatorsList: function() {
						const listContainer = document.getElementById('current-indicators-list');
						if (!listContainer) return;

						// Get current indicators from chart
						const currentIndicators = this.getAllIndicators();

						listContainer.innerHTML = '';

						if (currentIndicators.length === 0) {
							listContainer.innerHTML = '<p style="color: #888; font-style: italic;">No indicators added yet</p>';
							return;
						}

						currentIndicators.forEach(indicator => {
							const indicatorDiv = document.createElement('div');
							indicatorDiv.style.cssText = `
								padding: 8px;
								margin-bottom: 8px;
								background: #2a2e39;
								border-radius: 4px;
								border-left: 3px solid #2196F3;
							`;

							indicatorDiv.innerHTML = `
								<div style="display: flex; justify-content: space-between; align-items: center;">
									<div>
										<strong>${indicator.name}</strong>
										<br><small style="color: #888;">ID: ${indicator.id}</small>
									</div>
									<button onclick="window.IndicatorManager.removeIndicator('${indicator.id}'); window.IndicatorManager.refreshIndicatorsList();"
										style="background: #F44336; border: none; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">Remove</button>
								</div>
							`;

							listContainer.appendChild(indicatorDiv);
						});
					}
				};

				// Add Open Interest indicators when chart is ready
				widget.onChartReady(() => {
					//console.log('📈 Chart is ready! Adding PineJS-based Open Interest indicators...');

					// Initialize Indicator Manager
					window.IndicatorManager.init(widget);
					console.log('🔧 Indicator Manager ready!');

					try {
						// Add PineJS-based Open Interest indicators
						setTimeout(() => {
							try {
								widget.chart().createStudy('Open Interest', false, false, [1000]);
								//console.log('✅ Open Interest indicator added successfully!');
							} catch (e) {
								//console.log('❌ Open Interest failed:', e.message);
							}

							try {
								widget.chart().createStudy('OI Volume Ratio', false, false, [1000]);
								//console.log('✅ OI Volume Ratio indicator added successfully!');
							} catch (e) {
								//console.log('❌ OI Volume Ratio failed:', e.message);
							}

							//console.log('💡 Tip: Use the indicators button (📊) to add more Open Interest indicators');
							//console.log('📊 Current OI calculation: OI = Close × Multiplier');
						}, 2000);

						// Add Candlestick Pattern Detection indicator
						setTimeout(() => {
							try {
								// Try to add the candlestick patterns indicator
								if (typeof CandlestickPatternsPine !== 'undefined') {
									widget.chart().createStudy('Candlestick Patterns', false, false, {
										show_bullish: true,
										show_bearish: true,
										sensitivity: 0.5
									});
									//console.log('✅ Candlestick Patterns indicator added successfully!');
								} else {
									//console.log('⚠️ Candlestick Patterns indicator not loaded');
								}
							} catch (e) {
								//console.log('❌ Candlestick Patterns failed:', e.message);
								//console.log('💡 You can manually add it via the indicators menu');
							}

							//console.log('🕯️ Candlestick Pattern Detection Features:');
							//console.log('  📈 Bullish Patterns: Hammer, Inverted Hammer, Bullish Engulfing, Piercing, Morning Star, Three White Soldiers');
							//console.log('  📉 Bearish Patterns: Shooting Star, Hanging Man, Bearish Engulfing, Dark Cloud, Evening Star, Three Black Crows');
							//console.log('  🎯 Pattern Sensitivity: Adjustable from 0.1 to 1.0');
							//console.log('  🏷️ Pattern Labels: Shows pattern names on detection');
						}, 3000);

						// ========================================
						// GLOBAL CONVENIENCE FUNCTIONS
						// ========================================

						// Quick functions for easy access
						window.addIndicator = function(name, options = {}) {
							return window.IndicatorManager.addIndicator(name, options);
						};

						window.removeIndicator = function(id) {
							return window.IndicatorManager.removeIndicator(id);
						};

						window.listIndicators = function() {
							const indicators = window.IndicatorManager.getAllIndicators();
							console.table(indicators);
							return indicators;
						};

						window.showIndicatorManager = function() {
							window.IndicatorManager.showIndicatorUI();
						};

						// Add indicator to new pane
						window.addIndicatorToNewPane = function(name, inputs = []) {
							return window.IndicatorManager.addIndicatorToNewPane(name, inputs);
						};

						// Add indicator as overlay
						window.addOverlayIndicator = function(name, inputs = []) {
							return window.IndicatorManager.addOverlayIndicator(name, inputs);
						};

						// Quick add common indicators
						window.addRSI = function(period = 14, overlay = false) {
							return window.IndicatorManager.addIndicator('RSI', {
								inputs: [period],
								forceOverlay: overlay
							});
						};

						window.addMACD = function(fast = 12, slow = 26, signal = 9, overlay = false) {
							return window.IndicatorManager.addIndicator('MACD', {
								inputs: [fast, slow, signal],
								forceOverlay: overlay
							});
						};

						window.addBollingerBands = function(period = 20, stdDev = 2) {
							return window.IndicatorManager.addIndicator('Bollinger Bands', {
								inputs: [period, stdDev],
								forceOverlay: true  // Bollinger Bands typically overlay on price
							});
						};

						window.addMovingAverage = function(period = 20, type = 'SMA') {
							const indicatorName = type === 'EMA' ? 'Moving Average Exponential' :
												 type === 'WMA' ? 'Moving Average Weighted' : 'Moving Average';
							return window.IndicatorManager.addIndicator(indicatorName, {
								inputs: [period],
								forceOverlay: true  // Moving averages typically overlay on price
							});
						};

						window.addVolume = function() {
							return window.IndicatorManager.addIndicatorToNewPane('Volume');
						};

						window.addFlexibleSMA = function(period = 14, source = 'close') {
							// Refresh the indicator definition with current sources
							window.refreshFlexibleIndicators();
							return window.IndicatorManager.addIndicator('Flexible SMA', {
								inputs: [period, source],
								forceOverlay: false  // Can be used as overlay or separate pane
							});
						};

						window.addFlexibleRSI = function(period = 14, source = 'close', overbought = 70, oversold = 30) {
							// Refresh the indicator definition with current sources
							window.refreshFlexibleIndicators();
							return window.IndicatorManager.addIndicatorToNewPane('Flexible RSI', {
								inputs: [period, source, overbought, oversold]
							});
						};

						// Function to refresh flexible indicator definitions with current sources
						window.refreshFlexibleIndicators = function() {
							console.log('📊 Refreshing indicator definitions with current sources...');
							console.log('Current sources for new indicators:', window.sourcesConfig.sources);

							// Update the options for existing flexible indicators
							if (window.studies && window.studies['Flexible SMA']) {
								window.studies['Flexible SMA'].metainfo.inputs[1].options = window.sourcesConfig.sources;
							}
							if (window.studies && window.studies['Flexible RSI']) {
								window.studies['Flexible RSI'].metainfo.inputs[1].options = window.sourcesConfig.sources;
							}

							console.log('✅ Indicator definitions updated with latest sources');
						};

						// Function to update sources dynamically
						window.updateSources = function(newSources) {
							window.sourcesConfig.sources = newSources;
							window.sources = window.sourcesConfig.sources; // Update backward compatibility
							console.log('Sources updated. New indicators will use:', window.sourcesConfig.sources);
							return window.sourcesConfig.sources;
						};

						// Function to add a single source
						window.addSource = function(source) {
							window.sourcesConfig.add(source);
							window.sources = window.sourcesConfig.sources; // Update backward compatibility
							return window.sourcesConfig.sources;
						};

						// Function to remove a single source
						window.removeSource = function(source) {
							window.sourcesConfig.remove(source);
							window.sources = window.sourcesConfig.sources; // Update backward compatibility
							return window.sourcesConfig.sources;
						};

						// Function to get current sources
						window.getSources = function() {
							console.log('Current sources:', window.sourcesConfig.sources);
							return window.sourcesConfig.sources;
						};

						// Help function
						window.indicatorHelp = function() {
							console.log('📊 Indicator Manager Commands:');
							console.log('');
							console.log('🔧 Basic Functions:');
							console.log('  showIndicatorManager() - Open indicator management UI');
							console.log('  addIndicator(name, options) - Add any indicator');
							console.log('  removeIndicator(id) - Remove indicator by ID');
							console.log('  listIndicators() - List all current indicators');
							console.log('');
							console.log('📈 Quick Add Functions:');
							console.log('  addRSI(period=14, overlay=false) - Add RSI indicator');
							console.log('  addMACD(fast=12, slow=26, signal=9, overlay=false) - Add MACD');
							console.log('  addBollingerBands(period=20, stdDev=2) - Add Bollinger Bands (overlay)');
							console.log('  addMovingAverage(period=20, type="SMA") - Add Moving Average (overlay)');
							console.log('  addVolume() - Add Volume indicator');
							console.log('  addFlexibleSMA(period=14, source="close") - Add Flexible SMA');
							console.log('  addFlexibleRSI(period=14, source="close", overbought=70, oversold=30) - Add Flexible RSI');
							console.log('  updateSources(["open","high","low","close","volume","oi","custom"]) - Update available sources');
							console.log('  addSource("custom") - Add a single source');
							console.log('  removeSource("volume") - Remove a single source');
							console.log('  getSources() - View current available sources');
							console.log('');
							console.log('🎯 Advanced Functions:');
							console.log('  addIndicatorToNewPane(name, inputs) - Add to new pane');
							console.log('  addOverlayIndicator(name, inputs) - Add as overlay');
							console.log('');
							console.log('💡 Examples:');
							console.log('  addRSI(21) - Add RSI with 21 period to new pane');
							console.log('  addRSI(21, true) - Add RSI with 21 period as overlay');
							console.log('  addMACD(12, 26, 9, true) - Add MACD as overlay');
							console.log('  addMovingAverage(50, "EMA") - Add 50-period EMA overlay');
							console.log('  addBollingerBands(20, 2.5) - Add Bollinger Bands overlay');
							console.log('  addFlexibleSMA(20, "volume") - Add 20-period SMA on volume');
							console.log('  addFlexibleRSI(14, "oi", 80, 20) - Add RSI on Open Interest with custom levels');
							console.log('');
							console.log('🎨 Available Indicators:');
							console.log('  Technical: RSI, MACD, Bollinger Bands, Stochastic, ATR, CCI');
							console.log('  Moving Averages: Moving Average, Moving Average Exponential, Moving Average Weighted');
							console.log('  Volume: Volume, On Balance Volume, Chaikin Money Flow, Money Flow Index');
							console.log('  Oscillators: Williams %R, Momentum, ROC, Awesome Oscillator');
							console.log('  Trend: ADX, Parabolic SAR, Ichimoku Cloud, Aroon');
							console.log('  Custom: Open Interest, OI Volume Ratio, Candlestick Patterns, Flexible SMA, Flexible RSI');
						};

						// Add a floating button for quick access
						function createIndicatorManagerButton() {
							
						}

						// Create the floating button after a delay
						setTimeout(() => {
							createIndicatorManagerButton();
							console.log('📊 Indicator Manager ready! Click the 📊 button or use showIndicatorManager()');
							console.log('💡 Type indicatorHelp() for available commands');
							console.log('🎯 Key Features:');
							console.log('  • Add indicators to new panes or overlay on existing ones');
							console.log('  • Use forceOverlay: true to overlay any indicator');
							console.log('  • Quick functions: addRSI(), addMACD(), addBollingerBands(), etc.');
							console.log('  • Full UI for managing all indicators');
						}, 4000);

						// Update watchlist with NSE.json symbols after data is loaded
						setTimeout(() => {
							updateWatchlistFromNSE();
						}, 3000);

						// Show layout management info
						setTimeout(() => {
							const savedLayouts = getSavedLayouts();
							if (savedLayouts.length > 0) {
								//console.log('💾 Found', savedLayouts.length, 'saved layouts');
								//console.log('💡 Use the save/load button in the toolbar to manage layouts');
							} else {
								//console.log('💾 No saved layouts found');
								//console.log('💡 Use the save/load button in the toolbar to save your first layout');
							}

							//console.log('🔧 Layout Management Commands:');
							//console.log('  listLayouts() - Show all saved layouts');
							//console.log('  deleteLayout(id) - Delete a specific layout');
							//console.log('  clearAllLayouts() - Delete all layouts');

							//console.log('🕯️ Candlestick Pattern Detection Commands:');
							//console.log('  testCandlestickPatterns() - Test with sample data');
							//console.log('  analyzeCurrentChartPatterns() - Analyze current chart');
							//console.log('  enablePatternDetection() - Enable auto-detection');
							//console.log('  disablePatternDetection() - Disable auto-detection');
							//console.log('  candlestickDetector.setSensitivity(0.5) - Adjust sensitivity');
						}, 4000);

					} catch (error) {
						console.error('❌ Error adding Open Interest indicators:', error);
					}
				});

				// Function to generate watchlist symbols from NSE.json (no widget recreation)
				function updateWatchlistFromNSE() {
					try {
						if (datafeed && datafeed.csvLoaded && datafeed.csvSymbols && datafeed.csvSymbols.length > 0) {
							//console.log('📋 Generating watchlist symbols from NSE.json...');
							const watchlistSymbols = datafeed.getWatchlistSymbols();

							// Store symbols for reference
							window.dynamicWatchlistSymbols = watchlistSymbols;
							//console.log('📋 Watchlist symbols generated:', watchlistSymbols.length, 'symbols');
							//console.log('💡 Access via: window.dynamicWatchlistSymbols');

							// Log the symbols for verification
							//console.log('📋 Generated watchlist symbols:');
							watchlistSymbols.forEach((symbol, index) => {
								if (symbol.startsWith('###')) {
									//console.log(`📂 ${symbol}`);
								} else {
									const name = datafeed.getSymbolFullName(symbol);
									//console.log(`  📊 ${symbol} - ${name}`);
								}
							});

							return watchlistSymbols;

						} else {
							//console.log('📋 NSE.json not loaded yet, cannot generate symbols');
							return null;
						}
					} catch (error) {
						console.error('❌ Error generating watchlist symbols:', error);
						return null;
					}
				}

				// Global function to manually update watchlist (for debugging)
				window.updateWatchlist = function() {
					//console.log('🔄 Manually updating watchlist...');
					updateWatchlistFromNSE();
				};

				// Function to get NSE.json symbols without recreating widget
				window.getNSEWatchlistSymbols = function() {
					if (!datafeed || !datafeed.csvLoaded) {
						//console.log('❌ NSE.json not loaded yet');
						return null;
					}

					//console.log('� Getting watchlist symbols from NSE.json...');
					const watchlistSymbols = datafeed.getWatchlistSymbols();
					//console.log('📋 Found', watchlistSymbols.length, 'symbols');
					return watchlistSymbols;
				};

				// Global function to show current watchlist symbols (for debugging)
				window.showWatchlistSymbols = function() {
					if (datafeed && datafeed.getWatchlistSymbols) {
						const symbols = datafeed.getWatchlistSymbols();
						//console.log('📋 Current watchlist symbols:', symbols);

						// Show symbol names if available
						if (datafeed.symbolNames && datafeed.symbolNames.size > 0) {
							//console.log('📋 Symbol names mapping:');
							for (const [key, name] of datafeed.symbolNames) {
								//console.log(`  ${key} -> ${name}`);
							}
						}

						return symbols;
					} else {
						//console.log('❌ Datafeed not available');
						return [];
					}
				};

				// Global function to get symbol full name (for debugging)
				window.getSymbolName = function(instrumentKey) {
					if (datafeed && datafeed.getSymbolFullName) {
						const name = datafeed.getSymbolFullName(instrumentKey);
						//console.log(`📋 ${instrumentKey} -> ${name}`);
						return name;
					} else {
						//console.log('❌ Datafeed not available');
						return instrumentKey;
					}
				};

				widget.headerReady().then(() => {
					const themeToggleEl = widget.createButton({
						useTradingViewStyle: false,
						align: 'right',
					});
					themeToggleEl.dataset.internalAllowKeyboardNavigation = 'true';
					themeToggleEl.id = 'theme-toggle';
					themeToggleEl.innerHTML = `<label for="theme-switch" id="theme-switch-label">Dark Mode</label>
					<div class="switcher">
						<input type="checkbox" id="theme-switch" tabindex="-1">
						<span class="thumb-wrapper">
							<span class="track"></span>
							<span class="thumb"></span>
						</span>
					</div>`;
					themeToggleEl.title = 'Toggle theme';
					const checkboxEl = themeToggleEl.querySelector('#theme-switch');
					checkboxEl.checked = theme === 'dark';
					checkboxEl.addEventListener('change', function() {
						const themeToSet = this.checked ? 'dark' : 'light'
						widget.changeTheme(themeToSet, { disableUndo: true });
					});


					const themeSwitchCheckbox = themeToggleEl.querySelector('#theme-switch');

					const handleRovingTabindexMainElement = (e) => {
						e.target.tabIndex = 0;
					};
					const handleRovingTabindexSecondaryElement = (e) => {
						e.target.tabIndex = -1;
					};

					themeSwitchCheckbox.addEventListener('roving-tabindex:main-element', handleRovingTabindexMainElement);
					themeSwitchCheckbox.addEventListener('roving-tabindex:secondary-element', handleRovingTabindexSecondaryElement);
					
					// Add custom JavaScript to handle exchange icons
					const addExchangeIcons = () => {
						setTimeout(() => {
							const exchangeCells = document.querySelectorAll('.exchangeCell-oRSs8UQo .cell-oRSs8UQo');
							exchangeCells.forEach(cell => {
								const exchangeText = cell.textContent.trim();
								if (exchangeText && !cell.hasAttribute('data-exchange')) {
									cell.setAttribute('data-exchange', exchangeText);
								}
							});
						}, 500);
					};

					// Simple button click fix
					const fixButtonClicks = () => {
						setTimeout(() => {
							//console.log('🔧 Applying simple button fixes...');

							// Fix all buttons with pointer events
							const allButtons = document.querySelectorAll('button, [role="button"], .button-D4RPB3ZC');
							allButtons.forEach(btn => {
								btn.style.pointerEvents = 'auto';
								btn.style.cursor = 'pointer';
							});

							//console.log(`✅ Fixed ${allButtons.length} buttons`);
						}, 1000);
					};

					// Run fixes
					addExchangeIcons();
					fixButtonClicks();

					// Simple monitoring
					setInterval(() => {
						addExchangeIcons();
						fixButtonClicks();
					}, 5000);
				});

				// Add TradingView header buttons and dropdowns
				widget.headerReady().then(function() {
					// Console Help Button

					// Option Chain Button
					var optionChainButton = widget.createButton();
					optionChainButton.textContent = '📋 Options';
					optionChainButton.addEventListener('click', function() {
						console.log('🔗 Option Chain clicked');
						const currentSymbol = widget.chart().symbol();
						console.log('Opening option chain for:', currentSymbol);
						openOptionChain(currentSymbol);
					});

					// Compare/Underlying Symbol Button
					var compareButton = widget.createButton();
					compareButton.textContent = '📊 Compare';
					compareButton.addEventListener('click', function() {
						console.log('📊 Compare Underlying clicked');

						// Get current symbol and try to find underlying
						const currentSymbol = widget.chart().symbol();
						if (currentSymbol) {
							console.log('Current symbol:', currentSymbol);

							// Example: If symbol is AAPL240315C00150000, underlying would be AAPL
							let underlyingSymbol = currentSymbol;

							// Remove option suffixes (basic pattern matching)
							underlyingSymbol = underlyingSymbol.replace(/\\d{6}[CP]\\d+/g, '');
							underlyingSymbol = underlyingSymbol.replace(/\\.(TO|V|L)$/, ''); // Remove exchange suffixes

							console.log('Underlying symbol:', underlyingSymbol);

							// Add to compare
							if (underlyingSymbol !== currentSymbol) {
								try {
									widget.chart().createStudy('Compare', false, false, [underlyingSymbol]);
									console.log('Added underlying symbol to compare:', underlyingSymbol);
									alert(`Added ${underlyingSymbol} to compare view`);
								} catch (error) {
									console.error('Error adding compare:', error);
									alert(`Adding ${underlyingSymbol} to compare view`);
								}
							} else {
								// If no underlying detected, let user choose
								const compareSymbol = prompt('Enter symbol to compare with ' + currentSymbol + ':');
								if (compareSymbol) {
									try {
										widget.chart().createStudy('Compare', false, false, [compareSymbol]);
										console.log('Added symbol to compare:', compareSymbol);
									} catch (error) {
										console.error('Error adding compare:', error);
									}
								}
							}
						}
					});

					// Index Symbols Button (opens modal)
					var indexButton = widget.createButton();
					indexButton.textContent = '📈 Indices';
					indexButton.addEventListener('click', function() {
						console.log('📈 Index Symbols clicked');
						openModal('indexModal');
					});

					// Bar Replay Button
					var barReplayButton = widget.createButton();
					barReplayButton.textContent = '⏮️ Replay';
					barReplayButton.addEventListener('click', function() {
						console.log('⏮️ Bar Replay clicked');

						// Toggle replay mode
						if (this.textContent.includes('⏮️')) {
							this.textContent = '⏸️ Stop Replay';
							console.log('Starting bar replay mode...');
							alert('Bar Replay Mode Activated\\n\\nImplement historical playback logic:\\n- Step through historical bars\\n- Control playback speed\\n- Pause/resume functionality\\n- Jump to specific dates');
						} else {
							this.textContent = '⏮️ Replay';
							console.log('Stopping bar replay mode...');
							alert('Bar Replay Mode Stopped');
						}
					});
				});

				window.frames[0].focus();

				return widget;
			}

			// Initialize widget with default symbols
			createWidgetWithWatchlist();
		}

		window.addEventListener('DOMContentLoaded', initOnReady, false);
		</script>

		<style>
			/* Modal Styles */
			.trading-modal {
				display: none;
				position: fixed;
				z-index: 10000;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.5);
				backdrop-filter: blur(5px);
			}

			.modal-content {
				background-color: #fefefe;
				margin: 5% auto;
				padding: 0;
				border: none;
				border-radius: 12px;
				width: 90%;
				max-width: 800px;
				max-height: 80vh;
				overflow: hidden;
				box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
				animation: modalSlideIn 0.3s ease-out;
			}

			@keyframes modalSlideIn {
				from {
					opacity: 0;
					transform: translateY(-50px) scale(0.9);
				}
				to {
					opacity: 1;
					transform: translateY(0) scale(1);
				}
			}

			.modal-header {
				background: linear-gradient(135deg, #2196F3, #1976D2);
				color: white;
				padding: 20px;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.modal-title {
				margin: 0;
				font-size: 20px;
				font-weight: 600;
			}

			.modal-close {
				background: none;
				border: none;
				color: white;
				font-size: 24px;
				cursor: pointer;
				padding: 0;
				width: 30px;
				height: 30px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: background-color 0.2s;
			}

			.modal-close:hover {
				background-color: rgba(255, 255, 255, 0.2);
			}

			.modal-body {
				padding: 20px;
				max-height: 60vh;
				overflow-y: auto;
			}

			/* Option Chain Styles */
			.option-chain-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20px;
				padding: 15px;
				background: #f8f9fa;
				border-radius: 8px;
			}

			.symbol-info {
				display: flex;
				gap: 30px;
			}

			.price-info {
				text-align: center;
			}

			.price-value {
				font-size: 18px;
				font-weight: bold;
				margin-bottom: 2px;
			}

			.price-change {
				font-size: 12px;
			}

			.price-change.positive {
				color: #4CAF50;
			}

			.price-change.negative {
				color: #F44336;
			}

			.chain-controls {
				display: flex;
				align-items: center;
				gap: 15px;
			}

			.chain-stats {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				gap: 20px;
				margin-bottom: 20px;
				padding: 10px;
				background: #f0f8ff;
				border-radius: 6px;
				font-size: 12px;
			}

			.stat-item {
				text-align: center;
			}

			.stat-label {
				color: #666;
				margin-bottom: 2px;
			}

			.stat-value {
				font-weight: bold;
				color: #333;
			}

			.option-chain-table {
				width: 100%;
				border-collapse: collapse;
				font-size: 12px;
			}

			.option-chain-table th {
				background: #f5f5f5;
				padding: 8px 4px;
				text-align: center;
				font-weight: 600;
				border-bottom: 2px solid #ddd;
				font-size: 11px;
				color: #666;
			}

			.option-chain-table td {
				padding: 6px 4px;
				text-align: center;
				border-bottom: 1px solid #eee;
				position: relative;
			}

			.option-row {
				transition: background-color 0.2s;
			}

			.option-row:hover {
				background-color: #f9f9f9;
			}

			.option-row.atm {
				background-color: #fff3cd;
				border: 1px solid #ffeaa7;
			}

			.option-row.itm {
				background-color: #d4edda;
			}

			.option-row.otm {
				background-color: #f8d7da;
			}

			.strike-price {
				font-weight: bold;
				background: #e3f2fd !important;
				color: #1976D2;
			}

			.calls-section {
				background: linear-gradient(90deg, transparent 0%, #e8f5e8 100%);
			}

			.puts-section {
				background: linear-gradient(90deg, #ffeaea 0%, transparent 100%);
			}

			.volume-bar {
				position: absolute;
				bottom: 0;
				left: 0;
				height: 3px;
				background: #2196F3;
				opacity: 0.6;
			}

			.oi-change-positive {
				color: #4CAF50;
			}

			.oi-change-negative {
				color: #F44336;
			}

			.ltp-change-positive {
				color: #4CAF50;
			}

			.ltp-change-negative {
				color: #F44336;
			}

			.option-row.selected {
				background-color: #e3f2fd !important;
				border: 2px solid #2196F3 !important;
			}

			.option-row:hover {
				cursor: pointer;
			}

			/* Add visual indicators for volume bars */
			.volume-cell {
				position: relative;
				overflow: hidden;
			}

			.volume-cell::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				height: 2px;
				background: linear-gradient(90deg, #2196F3, #64B5F6);
				width: var(--volume-width, 0%);
				opacity: 0.7;
			}

			/* Index Symbols Styles */
			.index-grid {
				display: grid;
				grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
				gap: 15px;
			}

			.index-card {
				background: #f8f9fa;
				border: 1px solid #e0e0e0;
				border-radius: 8px;
				padding: 15px;
				cursor: pointer;
				transition: all 0.2s ease;
			}

			.index-card:hover {
				background: #e3f2fd;
				border-color: #2196F3;
				transform: translateY(-2px);
				box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
			}

			.index-symbol {
				font-size: 18px;
				font-weight: bold;
				color: #1976D2;
				margin-bottom: 5px;
			}

			.index-name {
				color: #666;
				font-size: 14px;
				margin-bottom: 8px;
			}

			.index-price {
				font-size: 16px;
				font-weight: 600;
				color: #333;
			}
		</style>
	</head>

	<body style="margin: 0px">
		<div id="tv_chart_container"></div>

		<!-- Option Chain Modal -->
		<div id="optionChainModal" class="trading-modal">
			<div class="modal-content">
				<div class="modal-header">
					<h2 class="modal-title">📋 Option Chain</h2>
					<button class="modal-close" onclick="closeModal('optionChainModal')">&times;</button>
				</div>
				<div class="modal-body">
					<div id="optionChainContent">
						<!-- Header with Symbol Info -->
						<div class="option-chain-header">
							<div class="symbol-info">
								<div class="price-info">
									<div style="font-weight: bold; margin-bottom: 5px;">Fut:</div>
									<div class="price-value" id="futPrice">24,843.40</div>
									<div class="price-change positive" id="futChange">133.20 (0.54%)</div>
								</div>
								<div class="price-info">
									<div style="font-weight: bold; margin-bottom: 5px;">Spot:</div>
									<div class="price-value" id="spotPrice">24,821.10</div>
									<div class="price-change positive" id="spotChange">140.20 (0.57%)</div>
								</div>
							</div>
							<div class="chain-controls">
								<span>Expiry:</span>
								<select id="expirationSelect" style="padding: 6px 10px; border-radius: 4px; border: 1px solid #ccc;">
									<option value="2024-07-31">31 July 25</option>
									<option value="2024-08-29">29 August 25</option>
									<option value="2024-09-26">26 September 25</option>
									<option value="2024-10-31">31 October 25</option>
								</select>
								<button style="padding: 6px; border: none; background: #f5f5f5; border-radius: 4px; cursor: pointer;">🔄</button>
								<button style="padding: 6px; border: none; background: #f5f5f5; border-radius: 4px; cursor: pointer;">⚙️</button>
								<button style="padding: 6px; border: none; background: #f5f5f5; border-radius: 4px; cursor: pointer;">⛶</button>
							</div>
						</div>

						<!-- Stats Row -->
						<div class="chain-stats">
							<div class="stat-item">
								<div class="stat-label">PCR</div>
								<div class="stat-value">0.712</div>
							</div>
							<div class="stat-item">
								<div class="stat-label">Max Pain</div>
								<div class="stat-value">24850</div>
							</div>
							<div class="stat-item">
								<div class="stat-label">Market Lot</div>
								<div class="stat-value">75</div>
							</div>
							<div class="stat-item">
								<div class="stat-label">Days for Expiry</div>
								<div class="stat-value">1</div>
							</div>
						</div>

						<!-- Option Chain Table -->
						<table class="option-chain-table">
							<thead>
								<tr>
									<th>OI Change (%)</th>
									<th>Volume (lakhs)</th>
									<th>IV</th>
									<th>LTP (change %) CE</th>
									<th class="strike-price">Strike Price</th>
									<th>PE LTP (change %)</th>
									<th>IV</th>
									<th>Volume (lakhs)</th>
									<th>OI Change (%)</th>
								</tr>
							</thead>
							<tbody id="optionChainTableBody">
								<tr class="option-row otm">
									<td class="oi-change-positive">59.40%</td>
									<td class="volume-cell" style="--volume-width: 60%;">167.74</td>
									<td>1,361.08</td>
									<td class="calls-section ltp-change-positive">307.60 <span style="color: #4CAF50;">(46.37%)</span></td>
									<td class="strike-price">24550</td>
									<td class="puts-section ltp-change-negative">10.90 <span style="color: #F44336;">(-77.27%)</span></td>
									<td>1,260.38</td>
									<td class="volume-cell" style="--volume-width: 29%;">820.78</td>
									<td class="oi-change-positive">29.65%</td>
								</tr>
								<tr class="option-row otm">
									<td class="oi-change-negative">-10.49%</td>
									<td class="volume-cell" style="--volume-width: 33%;">925.64</td>
									<td>1,260.38</td>
									<td class="calls-section ltp-change-positive">260.40 <span style="color: #4CAF50;">(50.00%)</span></td>
									<td class="strike-price">24600</td>
									<td class="puts-section ltp-change-negative">14.25 <span style="color: #F44336;">(-76.75%)</span></td>
									<td>1,191.71</td>
									<td class="volume-cell" style="--volume-width: 75%;">2,116.44</td>
									<td class="oi-change-positive">28.96%</td>
								</tr>
								<tr class="option-row otm">
									<td class="oi-change-negative">-14.94%</td>
									<td class="volume-cell" style="--volume-width: 40%;">1,126.89</td>
									<td>1,202.39</td>
									<td class="calls-section ltp-change-positive">217.30 <span style="color: #4CAF50;">(53.73%)</span></td>
									<td class="strike-price">24650</td>
									<td class="puts-section ltp-change-negative">20.35 <span style="color: #F44336;">(-74.31%)</span></td>
									<td>1,144.41</td>
									<td class="volume-cell" style="--volume-width: 65%;">1,812.43</td>
									<td class="oi-change-positive">108.76%</td>
								</tr>
								<tr class="option-row otm">
									<td class="oi-change-negative">-8.43%</td>
									<td class="volume-cell" style="--volume-width: 100%;">2,803.10</td>
									<td>1,145.94</td>
									<td class="calls-section ltp-change-positive">173.80 <span style="color: #4CAF50;">(54.01%)</span></td>
									<td class="strike-price">24700</td>
									<td class="puts-section ltp-change-negative">28.00 <span style="color: #F44336;">(-72.19%)</span></td>
									<td>1,092.53</td>
									<td class="volume-cell" style="--volume-width: 100%;">2,812.54</td>
									<td class="oi-change-positive">75.54%</td>
								</tr>
								<tr class="option-row otm">
									<td class="oi-change-negative">-24.35%</td>
									<td class="volume-cell" style="--volume-width: 56%;">1,569.86</td>
									<td>1,103.21</td>
									<td class="calls-section ltp-change-positive">136.00 <span style="color: #4CAF50;">(52.21%)</span></td>
									<td class="strike-price">24750</td>
									<td class="puts-section ltp-change-negative">39.45 <span style="color: #F44336;">(-69.02%)</span></td>
									<td>1,057.43</td>
									<td class="volume-cell" style="--volume-width: 40%;">1,133.70</td>
									<td class="oi-change-positive">113.59%</td>
								</tr>
								<tr class="option-row atm">
									<td class="oi-change-negative">-24.05%</td>
									<td class="volume-cell" style="--volume-width: 94%;">2,634.81</td>
									<td>1,071.17</td>
									<td class="calls-section ltp-change-positive"><strong>102.55</strong> <span style="color: #4CAF50;">(46.29%)</span></td>
									<td class="strike-price" style="background: #4CAF50; color: white; font-weight: bold;">24800</td>
									<td class="puts-section ltp-change-negative"><strong>55.85</strong> <span style="color: #F44336;">(-64.70%)</span></td>
									<td>1,031.49</td>
									<td class="volume-cell" style="--volume-width: 54%;">1,511.16</td>
									<td class="oi-change-positive">107.29%</td>
								</tr>
								<tr class="option-row itm">
									<td class="oi-change-negative">-5.98%</td>
									<td class="volume-cell" style="--volume-width: 44%;">1,229.92</td>
									<td>1,074.22</td>
									<td class="calls-section ltp-change-positive">75.25 <span style="color: #4CAF50;">(35.34%)</span></td>
									<td class="strike-price">24850</td>
									<td class="puts-section ltp-change-negative">78.75 <span style="color: #F44336;">(-59.25%)</span></td>
									<td>1,026.92</td>
									<td class="volume-cell" style="--volume-width: 15%;">410.83</td>
									<td class="oi-change-positive">90.83%</td>
								</tr>
								<tr class="option-row itm">
									<td class="oi-change-negative">-13.30%</td>
									<td class="volume-cell" style="--volume-width: 54%;">1,515.65</td>
									<td>1,092.53</td>
									<td class="calls-section ltp-change-positive">54.70 <span style="color: #4CAF50;">(23.62%)</span></td>
									<td class="strike-price">24900</td>
									<td class="puts-section ltp-change-negative">108.15 <span style="color: #F44336;">(-53.24%)</span></td>
									<td>1,040.65</td>
									<td class="volume-cell" style="--volume-width: 13%;">357.34</td>
									<td class="oi-change-positive">13.38%</td>
								</tr>
								<tr class="option-row itm">
									<td class="oi-change-negative">-9.72%</td>
									<td class="volume-cell" style="--volume-width: 25%;">709.79</td>
									<td>1,123.05</td>
									<td class="calls-section ltp-change-positive">40.10 <span style="color: #4CAF50;">(12.64%)</span></td>
									<td class="strike-price">24950</td>
									<td class="puts-section ltp-change-negative">143.00 <span style="color: #F44336;">(-47.66%)</span></td>
									<td>1,066.59</td>
									<td class="volume-cell" style="--volume-width: 2%;">68.39</td>
									<td class="oi-change-negative">-4.28%</td>
								</tr>
								<tr class="option-row itm">
									<td class="oi-change-negative">-5.70%</td>
									<td class="volume-cell" style="--volume-width: 52%;">1,448.63</td>
									<td>1,165.77</td>
									<td class="calls-section ltp-change-positive">29.45 <span style="color: #4CAF50;">(1.90%)</span></td>
									<td class="strike-price">25000</td>
									<td class="puts-section ltp-change-negative">182.50 <span style="color: #F44336;">(-42.34%)</span></td>
									<td>1,089.48</td>
									<td class="volume-cell" style="--volume-width: 6%;">169.04</td>
									<td class="oi-change-negative">-6.12%</td>
								</tr>
							</tbody>
						</table>

						<div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 6px; font-size: 12px;">
							<p><strong>Note:</strong> This is a demo option chain with sample NIFTY 50 data. Integrate with your options data provider for real-time data.</p>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Index Symbols Modal -->
		<div id="indexModal" class="trading-modal">
			<div class="modal-content">
				<div class="modal-header">
					<h2 class="modal-title">📈 Index Symbols</h2>
					<button class="modal-close" onclick="closeModal('indexModal')">&times;</button>
				</div>
				<div class="modal-body">
					<div class="index-grid">
						<div class="index-card" onclick="selectIndex('SPY')">
							<div class="index-symbol">SPY</div>
							<div class="index-name">SPDR S&P 500 ETF Trust</div>
							<div class="index-price">$428.50 <span style="color: #4CAF50;">+0.85%</span></div>
						</div>

						<div class="index-card" onclick="selectIndex('QQQ')">
							<div class="index-symbol">QQQ</div>
							<div class="index-name">Invesco QQQ Trust</div>
							<div class="index-price">$368.20 <span style="color: #4CAF50;">+1.20%</span></div>
						</div>

						<div class="index-card" onclick="selectIndex('IWM')">
							<div class="index-symbol">IWM</div>
							<div class="index-name">iShares Russell 2000 ETF</div>
							<div class="index-price">$198.75 <span style="color: #F44336;">-0.45%</span></div>
						</div>

						<div class="index-card" onclick="selectIndex('DIA')">
							<div class="index-symbol">DIA</div>
							<div class="index-name">SPDR Dow Jones Industrial Average ETF</div>
							<div class="index-price">$348.90 <span style="color: #4CAF50;">+0.32%</span></div>
						</div>

						<div class="index-card" onclick="selectIndex('VIX')">
							<div class="index-symbol">VIX</div>
							<div class="index-name">CBOE Volatility Index</div>
							<div class="index-price">$14.25 <span style="color: #F44336;">-2.15%</span></div>
						</div>

						<div class="index-card" onclick="selectIndex('GLD')">
							<div class="index-symbol">GLD</div>
							<div class="index-name">SPDR Gold Shares</div>
							<div class="index-price">$185.40 <span style="color: #4CAF50;">+0.75%</span></div>
						</div>

						<div class="index-card" onclick="selectIndex('TLT')">
							<div class="index-symbol">TLT</div>
							<div class="index-name">iShares 20+ Year Treasury Bond ETF</div>
							<div class="index-price">$92.15 <span style="color: #F44336;">-0.28%</span></div>
						</div>

						<div class="index-card" onclick="selectIndex('XLF')">
							<div class="index-symbol">XLF</div>
							<div class="index-name">Financial Select Sector SPDR Fund</div>
							<div class="index-price">$38.65 <span style="color: #4CAF50;">+1.05%</span></div>
						</div>

						<div class="index-card" onclick="selectIndex('XLK')">
							<div class="index-symbol">XLK</div>
							<div class="index-name">Technology Select Sector SPDR Fund</div>
							<div class="index-price">$178.30 <span style="color: #4CAF50;">+1.85%</span></div>
						</div>

						<div class="index-card" onclick="selectIndex('XLE')">
							<div class="index-symbol">XLE</div>
							<div class="index-name">Energy Select Sector SPDR Fund</div>
							<div class="index-price">$89.20 <span style="color: #F44336;">-1.20%</span></div>
						</div>
					</div>

					<div style="margin-top: 20px; text-align: center;">
						<input type="text" id="customSymbolInput" placeholder="Enter custom symbol (e.g., AAPL)"
							   style="padding: 10px; border: 1px solid #ccc; border-radius: 4px; margin-right: 10px; width: 200px;">
						<button onclick="selectCustomSymbol()"
								style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">
							Load Symbol
						</button>
					</div>
				</div>
			</div>
		</div>

		<script>
			// Modal Functions
			function openModal(modalId) {
				document.getElementById(modalId).style.display = 'block';
			}

			function closeModal(modalId) {
				document.getElementById(modalId).style.display = 'none';
			}

			// Close modal when clicking outside
			window.onclick = function(event) {
				const modals = document.querySelectorAll('.trading-modal');
				modals.forEach(modal => {
					if (event.target === modal) {
						modal.style.display = 'none';
					}
				});
			}

			// Option Chain Functions
			function openOptionChain(symbol) {
				const modal = document.getElementById('optionChainModal');
				const modalTitle = modal.querySelector('.modal-title');

				modalTitle.textContent = `${symbol} Option Chain`;
				openModal('optionChainModal');

				// Simulate loading option chain data
				setTimeout(() => {
					loadOptionChainData(symbol);
				}, 500);
			}

			function loadOptionChainData(symbol) {
				// This would typically fetch real option chain data from your API
				// For demo purposes, we'll show static data

				// Update header prices based on symbol
				updateHeaderPrices(symbol);

				// The table body is already populated with sample data
				// In a real implementation, you would fetch and populate this data
				console.log(`Loading option chain data for ${symbol}`);

				// Add click handlers for option rows
				addOptionRowHandlers();
			}

			function updateHeaderPrices(symbol) {
				// Sample price updates - replace with real API data
				const prices = {
					'NIFTY': { fut: 24843.40, futChange: 133.20, futChangePercent: 0.54, spot: 24821.10, spotChange: 140.20, spotChangePercent: 0.57 },
					'BANKNIFTY': { fut: 51234.50, futChange: -234.80, futChangePercent: -0.46, spot: 51198.30, spotChange: -198.60, spotChangePercent: -0.39 },
					'SENSEX': { fut: 81456.20, futChange: 456.30, futChangePercent: 0.56, spot: 81423.10, spotChange: 423.40, spotChangePercent: 0.52 }
				};

				const priceData = prices[symbol] || prices['NIFTY'];

				document.getElementById('futPrice').textContent = priceData.fut.toLocaleString();
				document.getElementById('futChange').textContent = `${priceData.futChange > 0 ? '+' : ''}${priceData.futChange.toFixed(2)} (${priceData.futChangePercent > 0 ? '+' : ''}${priceData.futChangePercent.toFixed(2)}%)`;
				document.getElementById('futChange').className = `price-change ${priceData.futChange >= 0 ? 'positive' : 'negative'}`;

				document.getElementById('spotPrice').textContent = priceData.spot.toLocaleString();
				document.getElementById('spotChange').textContent = `${priceData.spotChange > 0 ? '+' : ''}${priceData.spotChange.toFixed(2)} (${priceData.spotChangePercent > 0 ? '+' : ''}${priceData.spotChangePercent.toFixed(2)}%)`;
				document.getElementById('spotChange').className = `price-change ${priceData.spotChange >= 0 ? 'positive' : 'negative'}`;
			}

			function addOptionRowHandlers() {
				const optionRows = document.querySelectorAll('.option-row');
				optionRows.forEach(row => {
					row.addEventListener('click', function() {
						// Remove previous selection
						document.querySelectorAll('.option-row.selected').forEach(r => r.classList.remove('selected'));
						// Add selection to clicked row
						this.classList.add('selected');

						// Get strike price from the row
						const strikeCell = this.querySelector('.strike-price');
						if (strikeCell) {
							const strike = strikeCell.textContent;
							console.log(`Selected option with strike: ${strike}`);
							// Here you could open an order form or show option details
						}
					});
				});
			}

			// Index Selection Functions
			function selectIndex(symbol) {
				console.log('Selected index:', symbol);
				if (window.tvWidget) {
					try {
						window.tvWidget.chart().setSymbol(symbol);
						closeModal('indexModal');
						console.log('Switched to symbol:', symbol);
					} catch (error) {
						console.error('Error switching symbol:', error);
					}
				}
			}

			function selectCustomSymbol() {
				const customSymbol = document.getElementById('customSymbolInput').value.trim().toUpperCase();
				if (customSymbol) {
					selectIndex(customSymbol);
					document.getElementById('customSymbolInput').value = '';
				}
			}

			// Handle Enter key in custom symbol input
			document.addEventListener('DOMContentLoaded', function() {
				const customInput = document.getElementById('customSymbolInput');
				if (customInput) {
					customInput.addEventListener('keypress', function(e) {
						if (e.key === 'Enter') {
							selectCustomSymbol();
						}
					});
				}
			});
		</script>

	</body>
</html>
