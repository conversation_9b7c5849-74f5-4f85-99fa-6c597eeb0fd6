(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[8890],{259142:function(e,t){var n,o,r;o=[t],n=function(e){"use strict";function t(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var n=!1;if("undefined"!=typeof window){var o={get passive(){n=!0}};window.addEventListener("testPassive",null,o),window.removeEventListener("testPassive",null,o)}var r="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),a=[],i=!1,l=-1,s=void 0,c=void 0,u=function(e){return a.some((function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))}))},d=function(e){var t=e||window.event;return!!u(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},m=function(){setTimeout((function(){void 0!==c&&(document.body.style.paddingRight=c,c=void 0),void 0!==s&&(document.body.style.overflow=s,s=void 0)}))};e.disableBodyScroll=function(e,o){if(r){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!a.some((function(t){return t.targetElement===e}))){var m={targetElement:e,options:o||{}};a=[].concat(t(a),[m]),e.ontouchstart=function(e){1===e.targetTouches.length&&(l=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var n,o,r,a;1===t.targetTouches.length&&(o=e,a=(n=t).targetTouches[0].clientY-l,!u(n.target)&&(o&&0===o.scrollTop&&0<a||(r=o)&&r.scrollHeight-r.scrollTop<=r.clientHeight&&a<0?d(n):n.stopPropagation()))},i||(document.addEventListener("touchmove",d,n?{passive:!1}:void 0),i=!0)}}else{h=o,setTimeout((function(){if(void 0===c){var e=!!h&&!0===h.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(c=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===s&&(s=document.body.style.overflow,document.body.style.overflow="hidden")}));var p={targetElement:e,options:o||{}};a=[].concat(t(a),[p])}var h},e.clearAllBodyScrollLocks=function(){r?(a.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),i&&(document.removeEventListener("touchmove",d,n?{passive:!1}:void 0),i=!1),a=[],l=-1):(m(),a=[])},e.enableBodyScroll=function(e){if(r){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,a=a.filter((function(t){return t.targetElement!==e})),i&&0===a.length&&(document.removeEventListener("touchmove",d,n?{passive:!1}:void 0),i=!1)}else 1===a.length&&a[0].targetElement===e?(m(),a=[]):a=a.filter((function(t){return t.targetElement!==e}))}},void 0===(r="function"==typeof n?n.apply(t,o):n)||(e.exports=r)},262453:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1",
"preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},845946:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC",iconOnly:"iconOnly-D4RPB3ZC",link:"link-D4RPB3ZC",brand:"brand-D4RPB3ZC",primary:"primary-D4RPB3ZC",secondary:"secondary-D4RPB3ZC",gray:"gray-D4RPB3ZC",green:"green-D4RPB3ZC",red:"red-D4RPB3ZC",black:"black-D4RPB3ZC","black-friday":"black-friday-D4RPB3ZC","cyber-monday":"cyber-monday-D4RPB3ZC",slot:"slot-D4RPB3ZC",xsmall:"xsmall-D4RPB3ZC",withStartSlot:"withStartSlot-D4RPB3ZC",withEndSlot:"withEndSlot-D4RPB3ZC",startSlotWrap:"startSlotWrap-D4RPB3ZC",endSlotWrap:"endSlotWrap-D4RPB3ZC",small:"small-D4RPB3ZC",medium:"medium-D4RPB3ZC",large:"large-D4RPB3ZC",xlarge:"xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC",adjustPosition:"adjustPosition-D4RPB3ZC",firstRow:"firstRow-D4RPB3ZC",firstCol:"firstCol-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC",textWrap:"textWrap-D4RPB3ZC",multilineContent:"multilineContent-D4RPB3ZC",secondaryText:"secondaryText-D4RPB3ZC",primaryText:"primaryText-D4RPB3ZC"}},288276:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},173405:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw",
"inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},504665:e=>{e.exports={loader:"loader-UL6iwcBa",static:"static-UL6iwcBa",item:"item-UL6iwcBa","tv-button-loader":"tv-button-loader-UL6iwcBa",medium:"medium-UL6iwcBa",small:"small-UL6iwcBa",black:"black-UL6iwcBa",white:"white-UL6iwcBa",gray:"gray-UL6iwcBa",primary:"primary-UL6iwcBa"}},896108:e=>{e.exports={"tablet-normal-breakpoint":"(max-width: 768px)","small-height-breakpoint":"(max-height: 360px)","tablet-small-breakpoint":"(max-width: 440px)"}},333233:e=>{e.exports={actionButton:"actionButton-k53vexPa",small:"small-k53vexPa",hiddenTitle:"hiddenTitle-k53vexPa"}},504291:e=>{e.exports={label:"label-nb7ji1l2"}},531336:e=>{e.exports={popupDialog:"popupDialog-B02UUUN3",wrap:"wrap-B02UUUN3",main:"main-B02UUUN3",small:"small-B02UUUN3",title:"title-B02UUUN3",content:"content-B02UUUN3",html:"html-B02UUUN3",footer:"footer-B02UUUN3",close:"close-B02UUUN3",marginWithoutCloseButton:"marginWithoutCloseButton-B02UUUN3"}},536718:e=>{e.exports={"default-drawer-min-top-distance":"100px",wrap:"wrap-_HnK0UIN",positionBottom:"positionBottom-_HnK0UIN",backdrop:"backdrop-_HnK0UIN",drawer:"drawer-_HnK0UIN",positionLeft:"positionLeft-_HnK0UIN"}},149128:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},270762:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var o=n(50959),r=n(497754),a=n(878112),i=(n(15378),n(262453));function l(e){const{size:t="large",preservePaddings:n,isLink:o,flipIconOnRtl:a,className:l}=e;return r(i["nav-button"],i[`size-${t}`],n&&i["preserve-paddings"],a&&i["flip-icon"],o&&i.link,l)}function s(e){const{children:t,icon:n}=e;return o.createElement(o.Fragment,null,o.createElement("span",{className:i.background}),o.createElement(a.Icon,{icon:n,className:i.icon,"aria-hidden":!0}),t&&o.createElement("span",{className:i["visually-hidden"]},t))}const c=(0,o.forwardRef)(((e,t)=>{const{icon:n,type:r="button",preservePaddings:a,flipIconOnRtl:i,size:c,"aria-label":u,...d}=e;return o.createElement("button",{...d,className:l({...e,children:u}),ref:t,type:r},o.createElement(s,{icon:n},u))}));c.displayName="NavButton";var u=n(591365),d=n(273388);(0,o.forwardRef)(((e,t)=>{const{icon:n,renderComponent:r,"aria-label":a,...i}=e,c=r??u.CustomComponentDefaultLink;return o.createElement(c,{...i,className:l({...e,children:a,isLink:!0}),reference:(0,d.isomorphicRef)(t)},o.createElement(s,{icon:n},a))})).displayName="NavAnchorButton"},373989:(e,t,n)=>{"use strict";function o(e){return"brand"===e?"black":"blue"===e?"brand":e}n.d(t,{renameColors:()=>o})},228837:(e,t,n)=>{"use strict";n.d(t,{SquareButton:()=>v});var o=n(50959),r=n(497754),a=n(331774),i=n(373989),l=n(845946),s=n.n(l);const c="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text apply-common-tooltip";function u(e){
const{size:t="medium",variant:n="primary",stretch:o=!1,startSlot:l,endSlot:u,iconOnly:d=!1,className:m,isGrouped:p,cellState:h,disablePositionAdjustment:f=!1,primaryText:g,secondaryText:v,isAnchor:C=!1}=e,b=(0,i.renameColors)(e.color??"brand"),w=function(e){let t="";return 0!==e&&(1&e&&(t=r(t,s()["no-corner-top-left"])),2&e&&(t=r(t,s()["no-corner-top-right"])),4&e&&(t=r(t,s()["no-corner-bottom-right"])),8&e&&(t=r(t,s()["no-corner-bottom-left"]))),t}((0,a.getGroupCellRemoveRoundBorders)(h)),D=d&&(l||u);return r(m,s().button,s()[t],s()[b],s()[n],o&&s().stretch,l&&s().withStartIcon,u&&s().withEndIcon,D&&s().iconOnly,w,p&&s().grouped,p&&!f&&s().adjustPosition,p&&h.isTop&&s().firstRow,p&&h.isLeft&&s().firstCol,g&&v&&s().multilineContent,C&&s().link,c)}function d(e){const{startSlot:t,iconOnly:n,children:a,endSlot:i,primaryText:l,secondaryText:u}=e;if(t&&i&&n)return o.createElement("span",{className:r(s().slot,s().startSlotWrap)},t);const d=n&&(t??i),m=!t&&!i&&!n&&!a&&l&&u;return o.createElement(o.Fragment,null,t&&o.createElement("span",{className:r(s().slot,s().startSlotWrap)},t),a&&!d&&o.createElement("span",{className:s().content},a),i&&o.createElement("span",{className:r(s().slot,s().endSlotWrap)},i),m&&!d&&function(e){return e.primaryText&&e.secondaryText&&o.createElement("div",{className:r(s().textWrap,c)},o.createElement("span",{className:s().primaryText}," ",e.primaryText," "),"string"==typeof e.secondaryText?o.createElement("span",{className:s().secondaryText}," ",e.secondaryText," "):o.createElement("span",{className:s().secondaryText},o.createElement("span",null,e.secondaryText.firstLine),o.createElement("span",null,e.secondaryText.secondLine)))}(e))}var m=n(601198),p=n(380327),h=n(800417);function f(e,t){return n=>{if(t)return n.preventDefault(),void n.stopPropagation();e?.(n)}}function g(e){const{className:t,color:n,variant:o,size:r,stretch:a,animated:i,iconOnly:l,startSlot:s,endSlot:c,primaryText:u,secondaryText:d,...m}=e;return{...m,...(0,h.filterDataProps)(e),...(0,h.filterAriaProps)(e)}}function v(e){const{reference:t,tooltipText:n,disabled:r,onClick:a,onMouseOver:i,onMouseOut:l,onMouseDown:s,...c}=e,{isGrouped:h,cellState:v,disablePositionAdjustment:C}=(0,o.useContext)(p.ControlGroupContext),b=u({...c,isGrouped:h,cellState:v,disablePositionAdjustment:C}),w=n??(e.primaryText?[e.primaryText,e.secondaryText].join(" "):(0,m.getTextForTooltip)(e.children));return o.createElement("button",{...g(c),"aria-disabled":r,tabIndex:e.tabIndex??(r?-1:0),className:b,ref:t,onClick:f(a,r),onMouseDown:f(s,r),"data-overflow-tooltip-text":w},o.createElement(d,{...c}))}n(15378)},331774:(e,t,n)=>{"use strict";function o(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>o})},34735:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>C,InputClasses:()=>f});var o=n(50959),r=n(497754),a=n(650151),i=n(525388),l=n(800417),s=n(380327),c=n(331774);var u=n(288276),d=n.n(u);function m(e){let t=""
;return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function p(e,t,n,o){const{removeRoundBorder:a,className:i,intent:l="default",borderStyle:s="thin",size:u,highlight:p,disabled:h,readonly:f,stretch:g,noReadonlyStyles:v,isFocused:C}=e,b=m(a??(0,c.getGroupCellRemoveRoundBorders)(n));return r(d().container,d()[`container-${u}`],d()[`intent-${l}`],d()[`border-${s}`],u&&d()[`size-${u}`],b,p&&d()["with-highlight"],h&&d().disabled,f&&!v&&d().readonly,C&&d().focused,g&&d().stretch,t&&d().grouped,!o&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],i)}function h(e,t,n){const{highlight:o,highlightRemoveRoundBorder:a}=e;if(!o)return d().highlight;const i=m(a??(0,c.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],i)}const f={FontSizeMedium:(0,a.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,a.ensureDefined)(d()["font-size-large"])},g={passive:!1};function v(e,t){const{style:n,id:r,role:a,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:m,onMouseDown:f,onMouseUp:v,onKeyDown:C,onClick:b,tabIndex:w,startSlot:D,middleSlot:y,endSlot:x,onWheel:R,onWheelNoPassive:N=null,size:B,tag:E="span",type:P}=e,{isGrouped:S,cellState:k,disablePositionAdjustment:T=!1}=(0,o.useContext)(s.ControlGroupContext),L=function(e,t=null,n){const r=(0,o.useRef)(null),a=(0,o.useRef)(null),i=(0,o.useCallback)((()=>{if(null===r.current||null===a.current)return;const[e,t,n]=a.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),l=(0,o.useCallback)((()=>{if(null===r.current||null===a.current)return;const[e,t,n]=a.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),s=(0,o.useCallback)((e=>{l(),r.current=e,i()}),[]);return(0,o.useEffect)((()=>(a.current=[e,t,n],i(),l)),[e,t,n]),s}("wheel",N,g),Z=E;return o.createElement(Z,{type:P,style:n,id:r,role:a,className:p(e,S,k,T),tabIndex:w,ref:(0,i.useMergedRefs)([t,L]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:m,onMouseDown:f,onMouseUp:v,onKeyDown:C,onClick:b,onWheel:R,...(0,l.filterDataProps)(e),...(0,l.filterAriaProps)(e)},D,y,x,o.createElement("span",{className:h(e,k,B)}))}v.displayName="ControlSkeleton";const C=o.forwardRef(v)},102691:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>d,BeforeSlot:()=>l,EndSlot:()=>u,MiddleSlot:()=>c,StartSlot:()=>s});var o=n(50959),r=n(497754),a=n(173405),i=n.n(a);function l(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(i()["before-slot"],t)},n)}function s(e){const{className:t,interactive:n=!0,icon:a=!1,children:l}=e;return o.createElement("span",{className:r(i()["inner-slot"],n&&i().interactive,a&&i().icon,t)},l)}function c(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(i()["inner-slot"],i()["inner-middle-slot"],t)},n)}function u(e){const{className:t,interactive:n=!0,icon:a=!1,children:l}=e;return o.createElement("span",{className:r(i()["inner-slot"],n&&i().interactive,a&&i().icon,t)},l)}function d(e){
const{className:t,children:n}=e;return o.createElement("span",{className:r(i()["after-slot"],t)},n)}},383836:(e,t,n)=>{"use strict";n.d(t,{useFocus:()=>r});var o=n(50959);function r(e,t){const[n,r]=(0,o.useState)(!1);(0,o.useEffect)((()=>{t&&n&&r(!1)}),[t,n]);const a={onFocus:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!0)}),[e]),onBlur:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!1)}),[e])};return[n,a]}},252130:(e,t,n)=>{"use strict";n.d(t,{useIsMounted:()=>r});var o=n(50959);const r=()=>{const e=(0,o.useRef)(!1);return(0,o.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}},525388:(e,t,n)=>{"use strict";n.d(t,{useMergedRefs:()=>a});var o=n(50959),r=n(273388);function a(e){return(0,o.useCallback)((0,r.mergeRefs)(e),e)}},778199:(e,t,n)=>{"use strict";function o(e,t,n,o,r){function a(r){if(e>r.timeStamp)return;const a=r.target;void 0!==n&&null!==t&&null!==a&&a.ownerDocument===o&&(t.contains(a)||n(r))}return r.click&&o.addEventListener("click",a,!1),r.mouseDown&&o.addEventListener("mousedown",a,!1),r.touchEnd&&o.addEventListener("touchend",a,!1),r.touchStart&&o.addEventListener("touchstart",a,!1),()=>{o.removeEventListener("click",a,!1),o.removeEventListener("mousedown",a,!1),o.removeEventListener("touchend",a,!1),o.removeEventListener("touchstart",a,!1)}}n.d(t,{addOutsideEventListener:()=>o})},664332:(e,t,n)=>{"use strict";n.d(t,{useResizeObserver:()=>i});var o=n(50959),r=n(855393),a=n(718736);function i(e,t=[]){const{callback:n,ref:i=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),l=(0,o.useRef)(null),s=(0,o.useRef)(n);s.current=n;const c=(0,a.useFunctionalRefObject)(i),u=(0,o.useCallback)((e=>{c(e),null!==l.current&&(l.current.disconnect(),null!==e&&l.current.observe(e))}),[c,l]);return(0,r.useIsomorphicLayoutEffect)((()=>(l.current=new ResizeObserver(((e,t)=>{s.current(e,t)})),c.current&&u(c.current),()=>{l.current?.disconnect()})),[c,...t]),u}},234404:(e,t,n)=>{"use strict";n.d(t,{Loader:()=>s});var o,r=n(50959),a=n(497754),i=n(504665),l=n.n(i);function s(e){const{className:t,size:n="medium",staticPosition:o,color:i="black"}=e,s=a(l().item,l()[i],l()[n]);return r.createElement("span",{className:a(l().loader,o&&l().static,t)},r.createElement("span",{className:s}),r.createElement("span",{className:s}),r.createElement("span",{className:s}))}!function(e){e.Medium="medium",e.Small="small"}(o||(o={}))},800417:(e,t,n)=>{"use strict";function o(e){return a(e,i)}function r(e){return a(e,l)}function a(e,t){const n=Object.entries(e).filter(t),o={};for(const[e,t]of n)o[e]=t;return o}function i(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function l(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>r,filterDataProps:()=>o,filterProps:()=>a,isAriaAttribute:()=>l,isDataAttribute:()=>i})},269842:(e,t,n)=>{"use strict";function o(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>o})},651674:(e,t,n)=>{"use strict";n.d(t,{createReactRoot:()=>d});var o=n(50959),r=n(632227),a=n(904237)
;const i=(0,o.createContext)({isOnMobileAppPage:()=>!1,isRtl:!1,locale:"en"});var l=n(69111),s=n(431520);const c={iOs:"old",android:"new",old:"old",new:"new",any:"any"};function u(e){const[t]=(0,o.useState)({isOnMobileAppPage:e=>(0,l.isOnMobileAppPage)(c[e]),isRtl:(0,s.isRtl)(),locale:window.locale});return o.createElement(i.Provider,{value:t},e.children)}function d(e,t,n="legacy"){const i=o.createElement(u,null,e);if("modern"===n){const e=(0,a.createRoot)(t);return e.render(i),{render(t){e.render(o.createElement(u,null,t))},unmount(){e.unmount()}}}return r.render(i,t),{render(e){r.render(o.createElement(u,null,e),t)},unmount(){r.unmountComponentAtNode(t)}}}},996038:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>r});var o=n(896108);const r={SmallHeight:o["small-height-breakpoint"],TabletSmall:o["tablet-small-breakpoint"],TabletNormal:o["tablet-normal-breakpoint"]}},737350:(e,t,n)=>{"use strict";n.d(t,{SimpleDialogContext:()=>o});const o=n(50959).createContext({isSmallTablet:!1,dialogCloseHandler:()=>{}})},480994:(e,t,n)=>{"use strict";n.r(t),n.d(t,{confirmModule:()=>b,renameModule:()=>w,showSimpleDialog:()=>y,warningModule:()=>D});var o=n(50959),r=n(609838),a=n(859878);function i(e){return"html"in e?{html:e.html}:"text"in e?{content:e.text}:{content:e.content}}var l=n(737350),s=n(73007),c=n(195680),u=n(504291);function d(e){const{maxLength:t,value:n,placeholder:r,onValueChange:a,nameInputRef:i,source:d=[],autocompleteFilter:m,emojiPicker:p}=e,{isSmallTablet:h}=(0,o.useContext)(l.SimpleDialogContext),f=(0,c.useAutoSelect)();return o.createElement(o.Fragment,null,function(){if("content"in e)return o.createElement("div",{className:u.label},e.content);if("html"in e)return o.createElement("div",{className:u.label,dangerouslySetInnerHTML:{__html:e.html}});return null}(),o.createElement(s.Autocomplete,{maxLength:t,value:n,onChange:function(e){a(e)},allowUserDefinedValues:!0,preventOnFocusOpen:!0,noEmptyText:!0,source:d,preventSearchOnEmptyQuery:!0,filter:m,setupHTMLInput:function(e){f.current=e,i&&(i.current=e)},size:h?"large":void 0,placeholder:r,suggestionsInPortal:!0,emojiPicker:p}))}function m(e){return Boolean(e.trim())}function p(e){const{buttonText:t,intentButton:o,actions:a,onConfirm:i}=e,l=[{name:"ok",title:t||r.t(null,void 0,n(819295)),intent:o,handler:({dialogClose:e})=>{i?.(),e()}}];return a&&a.forEach((e=>l.push(e))),l}var h=n(650151),f=n(753327),g=n(63192),v=n(651674);const C=new g.DialogsOpenerManager;const b=function(e){const{title:t,onClose:l=()=>{},mainButtonText:s,mainButtonIntent:c,cancelButtonText:u,closeOnOutsideClick:d,onConfirm:m,onCancel:p,backdrop:h}=e,f=i(e);return o.createElement(a.SimpleDialog,{...f,backdrop:h,title:t||r.t(null,void 0,n(464770)),onClose:l,actions:[{name:"yes",title:s||r.t(null,void 0,n(955512)),intent:c||"success",handler:m},{name:"no",type:"button",title:u||r.t(null,void 0,n(38733)),appearance:"stroke",intent:"default",handler:e=>{p?p(e):e.dialogClose()}}],dataName:"confirm-dialog",closeOnOutsideClick:d})},w=function(e){
const{title:t,maxLength:l,initValue:s,placeholder:c,onClose:u=()=>{},mainButtonText:p,mainButtonIntent:h,cancelButtonText:f,validator:g=m,onRename:v,source:C,autocompleteFilter:b,onCancel:w,emojiPicker:D}=e,y=(0,o.useRef)(null),[x,R]=(0,o.useState)(s||""),[N,B]=(0,o.useState)((()=>g(x))),E=i(e);return o.createElement(a.SimpleDialog,{title:t||r.t(null,void 0,n(606321)),content:o.createElement(d,{...E,nameInputRef:y,maxLength:l,placeholder:c,value:x,onValueChange:function(e){R(e),B(g(e))},source:C,autocompleteFilter:b,emojiPicker:D}),onClose:u,actions:[{disabled:!N,name:"save",title:p||r.t(null,void 0,n(664e3)),intent:h||"primary",handler:({dialogClose:e,innerManager:t})=>v({newValue:x,focusInput:P,dialogClose:e,innerManager:t})},{name:"cancel",type:"button",title:f||r.t(null,void 0,n(904543)),appearance:"stroke",intent:"default",handler:e=>{w?w(e):e.dialogClose()}}],dataName:"rename-dialog"});function P(){y.current&&y.current.focus()}},D=function(e){const{title:t,closeOnOutsideClick:l,onClose:s=()=>{},backdrop:c}=e,u=i(e);return o.createElement(a.SimpleDialog,{...u,title:t||r.t(null,void 0,n(966719)),onClose:s,backdrop:c,actions:p(e),dataName:"warning-dialog",closeOnOutsideClick:l})},y=function(e,t,n){const{title:r}=e;let a=`${r}_`;if(a+="text"in e?e.text:"html"in e?e.html:e.id,C.isOpened(a))return(0,h.ensureDefined)(C.getDialogPayload(a)).closeHandler;const i=document.createElement("div"),l=()=>{e.onClose?.(),s.unmount(),C.setAsClosed(a)},s=(0,v.createReactRoot)(o.createElement(f.SlotContext.Provider,{value:n||null},o.createElement(t,{...e,onClose:l})),i);return C.setAsOpened(a,{closeHandler:l}),l}},859878:(e,t,n)=>{"use strict";n.d(t,{SimpleDialog:()=>x});var o=n(50959),r=n(497754),a=n(270762),i=n(559410),l=n(59216),s=n(180185),c=n(930052),u=n(206594),d=n(996038),m=n(742554),p=n(805184),h=n(234404),f=n(650151),g=n(252130),v=n(753327),C=n(737350),b=n(333233);function w(e){const{disabled:t,name:n,title:a,appearance:i,intent:l,handler:s,reference:c,type:u,className:d}=e,{isSmallTablet:m,dialogCloseHandler:w}=(0,o.useContext)(C.SimpleDialogContext),D=(0,f.ensureNotNull)((0,o.useContext)(v.SlotContext)),y=(0,g.useIsMounted)(),[x,R]=(0,o.useState)(!1);return o.createElement(p.Button,{type:u,disabled:t,reference:c,className:r(b.actionButton,d,m&&b.small),name:n,size:m?"l":void 0,appearance:i,intent:l,onClick:function(){if(x)return;const e=s({dialogClose:w,innerManager:D});e&&(R(!0),e.then((()=>{y.current&&R(!1)})))}},o.createElement("span",{className:r(x&&b.hiddenTitle)},a),x&&o.createElement(h.Loader,{color:"white"}))}var D=n(507720),y=n(531336);function x(e){const{title:t,onClose:n,actions:p,dataName:h,popupDialogClassName:f,contentClassName:g,wrapperClassName:v,backdrop:b,closeOnOutsideClick:x=!0,showCloseButton:R=!0,closeOnEscapePress:N=!0,events:B=!0,centeredOnResize:E}=e;(0,o.useEffect)((()=>(i.subscribe(u.CLOSE_POPUPS_AND_DIALOGS_COMMAND,n,null),()=>{i.unsubscribe(u.CLOSE_POPUPS_AND_DIALOGS_COMMAND,n,null)})),[n]);const[P,S]=(0,o.useState)(!0),k=(0,o.useRef)(null),T=(0,o.useRef)(null);return(0,
o.useEffect)((()=>(window.addEventListener("resize",z),()=>{window.removeEventListener("resize",z)})),[T,E]),o.createElement(c.MatchMedia,{rule:d.DialogBreakpoints.TabletSmall},(i=>o.createElement(C.SimpleDialogContext.Provider,{value:{isSmallTablet:i,dialogCloseHandler:n}},o.createElement(l.PopupDialog,{className:r(y.popupDialog,f),componentRef:T,isOpened:P,backdrop:b,onClickBackdrop:B?Z:void 0,onClickOutside:x?Z:void 0,onKeyDown:L,autofocus:!0,fixedBody:!0},o.createElement("div",{className:r(y.wrap,v),"data-name":h},o.createElement("div",{className:r(y.main,!R&&y.marginWithoutCloseButton,i&&y.small)},t&&o.createElement("div",{className:r(y.title,i&&y.small)},t),function(t){if("html"in e)return o.createElement(m.TouchScrollContainer,{className:r(y.content,t&&y.small,y.html,g),dangerouslySetInnerHTML:{__html:e.html}});if("content"in e)return o.createElement(m.TouchScrollContainer,{className:r(y.content,t&&y.small,g)},e.content);return null}(i),p&&p.length>0&&o.createElement("div",{className:r(y.footer,i&&y.small)},p.map(((e,t)=>o.createElement(w,{...e,key:e.name,reference:0===t?k:void 0}))))),R&&o.createElement(a.NavButton,{"aria-label":"close",size:"medium",preservePaddings:!0,className:r(y.close,i&&y.small),icon:D,onClick:Z,"data-name":"close","data-role":"button"}))))));function L(e){switch((0,s.hashFromEvent)(e)){case 27:P&&N&&(e.preventDefault(),n());break;case 13:const t=document.activeElement;if(e.defaultPrevented||t instanceof HTMLButtonElement&&"submit"!==t.type)return;if(P&&p&&p.length){e.preventDefault();const t=k.current;t&&t.click()}}}function Z(){S(!1),n()}function z(){E&&T.current?.centerAndFit?.()}}},63192:(e,t,n)=>{"use strict";n.d(t,{DialogsOpenerManager:()=>o,dialogsOpenerManager:()=>r});class o{constructor(){this._storage=new Map}setAsOpened(e,t){this._storage.set(e,t)}setAsClosed(e){this._storage.delete(e)}isOpened(e){return this._storage.has(e)}getDialogPayload(e){return this._storage.get(e)}}const r=new o},163694:(e,t,n)=>{"use strict";n.d(t,{DrawerContext:()=>i,DrawerManager:()=>a});var o=n(50959),r=n(285089);class a extends o.PureComponent{constructor(e){super(e),this._isBodyFixed=!1,this._addDrawer=e=>{this.setState((t=>({stack:[...t.stack,e]})))},this._removeDrawer=e=>{this.setState((t=>({stack:t.stack.filter((t=>t!==e))})))},this.state={stack:[]}}componentDidUpdate(e,t){!t.stack.length&&this.state.stack.length&&((0,r.setFixedBodyState)(!0),this._isBodyFixed=!0),t.stack.length&&!this.state.stack.length&&this._isBodyFixed&&((0,r.setFixedBodyState)(!1),this._isBodyFixed=!1)}componentWillUnmount(){this.state.stack.length&&this._isBodyFixed&&(0,r.setFixedBodyState)(!1)}render(){return o.createElement(i.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.stack.length?this.state.stack[this.state.stack.length-1]:null}},this.props.children)}}const i=o.createContext(null)},759339:(e,t,n)=>{"use strict";n.d(t,{Drawer:()=>p});var o=n(50959),r=n(650151),a=n(497754),i=n(924910),l=n(8361),s=n(163694),c=n(28466),u=n(742554),d=n(536718);var m;function p(e){
const{position:t="Bottom",onClose:n,children:u,reference:m,className:p,theme:f=d}=e,g=(0,r.ensureNotNull)((0,o.useContext)(s.DrawerContext)),[v]=(0,o.useState)((()=>(0,i.randomHash)())),C=(0,o.useRef)(null),b=(0,o.useContext)(c.CloseDelegateContext);return(0,o.useLayoutEffect)((()=>((0,r.ensureNotNull)(C.current).focus({preventScroll:!0}),b.subscribe(g,n),g.addDrawer(v),()=>{g.removeDrawer(v),b.unsubscribe(g,n)})),[]),o.createElement(l.Portal,null,o.createElement("div",{ref:m,className:a(d.wrap,d[`position${t}`])},v===g.currentDrawer&&o.createElement("div",{className:d.backdrop,onClick:n}),o.createElement(h,{className:a(f.drawer,d[`position${t}`],p),ref:C,"data-name":e["data-name"]},u)))}!function(e){e.Left="Left",e.Bottom="Bottom"}(m||(m={}));const h=(0,o.forwardRef)(((e,t)=>{const{className:n,...r}=e;return o.createElement(u.TouchScrollContainer,{className:a(d.drawer,n),tabIndex:-1,ref:t,...r})}))},624216:(e,t,n)=>{"use strict";n.d(t,{PopupMenu:()=>m});var o=n(50959),r=n(632227),a=n(688987),i=n(8361),l=n(510618),s=n(28466);const c=o.createContext(void 0);var u=n(908783);const d=o.createContext({setMenuMaxWidth:!1});function m(e){const{controller:t,children:n,isOpened:m,closeOnClickOutside:p=!0,doNotCloseOn:h,onClickOutside:f,onClose:g,onKeyboardClose:v,"data-name":C="popup-menu-container",...b}=e,w=(0,o.useContext)(s.CloseDelegateContext),D=o.useContext(d),y=(0,o.useContext)(c),x=(0,u.useOutsideEvent)({handler:function(e){f&&f(e);if(!p)return;const t=(0,a.default)(h)?h():null==h?[]:[h];if(t.length>0&&e.target instanceof Node)for(const n of t){const t=r.findDOMNode(n);if(t instanceof Node&&t.contains(e.target))return}g()},mouseDown:!0,touchStart:!0});return m?o.createElement(i.Portal,{top:"0",left:"0",right:"0",bottom:"0",pointerEvents:"none"},o.createElement("span",{ref:x,style:{pointerEvents:"auto"}},o.createElement(l.Menu,{...b,onClose:g,onKeyboardClose:v,onScroll:function(t){const{onScroll:n}=e;n&&n(t)},customCloseDelegate:w,customRemeasureDelegate:y,ref:t,"data-name":C,limitMaxWidth:D.setMenuMaxWidth,"data-tooltip-show-on-focus":"true"},n))):null}},515783:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetCaret:()=>s});var o=n(50959),r=n(497754),a=n(878112),i=n(149128),l=n(100578);function s(e){const{dropped:t,className:n}=e;return o.createElement(a.Icon,{className:r(n,i.icon,{[i.dropped]:t}),icon:l})}},742554:(e,t,n)=>{"use strict";n.d(t,{TouchScrollContainer:()=>c});var o=n(50959),r=n(259142),a=n(650151),i=n(601227);const l=CSS.supports("overscroll-behavior","none");let s=0;const c=(0,o.forwardRef)(((e,t)=>{const{children:n,...a}=e,c=(0,o.useRef)(null);return(0,o.useImperativeHandle)(t,(()=>c.current)),(0,o.useLayoutEffect)((()=>{if(i.CheckMobile.iOS())return s++,null!==c.current&&(l?1===s&&(document.body.style.overscrollBehavior="none"):(0,r.disableBodyScroll)(c.current,{allowTouchMove:u(c)})),()=>{s--,null!==c.current&&(l?0===s&&(document.body.style.overscrollBehavior=""):(0,r.enableBodyScroll)(c.current))}}),[]),o.createElement("div",{ref:c,...a},n)}));function u(e){return t=>{const n=(0,
a.ensureNotNull)(e.current),o=document.activeElement;return!n.contains(t)||null!==o&&n.contains(o)&&o.contains(t)}}},904237:(e,t,n)=>{"use strict";var o=n(632227);t.createRoot=o.createRoot,o.hydrateRoot},162458:(e,t,n)=>{"use strict";n.d(t,{HorizontalAttachEdge:()=>r,HorizontalDropDirection:()=>i,VerticalAttachEdge:()=>o,VerticalDropDirection:()=>a,getPopupPositioner:()=>c});var o,r,a,i,l=n(650151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(o||(o={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(r||(r={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(a||(a={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(i||(i={}));const s={verticalAttachEdge:o.Bottom,horizontalAttachEdge:r.Left,verticalDropDirection:a.FromTopToBottom,horizontalDropDirection:i.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(e,t){return n=>{const{contentWidth:c,contentHeight:u,availableHeight:d}=n,m=(0,l.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:p=s.horizontalAttachEdge,horizontalDropDirection:h=s.horizontalDropDirection,horizontalMargin:f=s.horizontalMargin,verticalMargin:g=s.verticalMargin,matchButtonAndListboxWidths:v=s.matchButtonAndListboxWidths}=t;let C=t.verticalAttachEdge??s.verticalAttachEdge,b=t.verticalDropDirection??s.verticalDropDirection;C===o.AutoStrict&&(d<m.y+m.height+g+u?(C=o.Top,b=a.FromBottomToTop):(C=o.Bottom,b=a.FromTopToBottom));const w=C===o.Top?-1*g:g,D=p===r.Right?m.right:m.left,y=C===o.Top?m.top:m.bottom,x={x:D-(h===i.FromRightToLeft?c:0)+f,y:y-(b===a.FromBottomToTop?u:0)+w};return v&&(x.overrideWidth=m.width),x}}},100578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},507720:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 17 17" width="17" height="17" fill="currentColor"><path d="m.58 1.42.82-.82 15 15-.82.82z"/><path d="m.58 15.58 15-15 .82.82-15 15z"/></svg>'},925931:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>o});let o=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);