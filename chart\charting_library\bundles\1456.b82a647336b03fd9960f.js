(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1456],{869174:e=>{e.exports={container:"container-TCHLKPuQ","container-danger":"container-danger-TCHLKPuQ","light-title":"light-title-TCHLKPuQ","light-icon":"light-icon-TCHLKPuQ",icon:"icon-TCHLKPuQ",header:"header-TCHLKPuQ","light-container-danger":"light-container-danger-TCHLKPuQ","container-warning":"container-warning-TCHLKPuQ","light-container-warning":"light-container-warning-TCHLKPuQ","container-success":"container-success-TCHLKPuQ","light-container-success":"light-container-success-TCHLKPuQ","container-default":"container-default-TCHLKPuQ","light-container-default":"light-container-default-TCHLKPuQ","text-wrap":"text-wrap-TCHLKPuQ","light-text-wrap":"light-text-wrap-TCHLKPuQ","close-button":"close-button-TCHLKPuQ","light-close-button":"light-close-button-TCHLKPuQ",informerBody:"informerBody-TCHLKPuQ",mainProblem:"mainProblem-TCHLKPuQ","header-inline":"header-inline-TCHLKPuQ","header-new-line":"header-new-line-TCHLKPuQ"}},597683:e=>{e.exports={wrapper:"wrapper-HLS9OacM",help:"help-HLS9OacM"}},324171:e=>{e.exports={menuButton:"menuButton-xw2UKKgr",select:"select-xw2UKKgr",selectMenu:"selectMenu-xw2UKKgr",title:"title-xw2UKKgr",titleWithError:"titleWithError-xw2UKKgr"}},215773:e=>{e.exports={title:"title-DPlbqdw3",input:"input-DPlbqdw3",wait:"wait-DPlbqdw3",checkboxWrapper:"checkboxWrapper-DPlbqdw3",checkboxLabel:"checkboxLabel-DPlbqdw3"}},628573:e=>{e.exports={customField:"customField-64DiJaqR"}},953868:e=>{e.exports={title:"title-GhO6ya4a",input:"input-GhO6ya4a",wait:"wait-GhO6ya4a"}},99505:e=>{e.exports={button:"button-tFul0OhX","button-children":"button-children-tFul0OhX",hiddenArrow:"hiddenArrow-tFul0OhX",invisibleFocusHandler:"invisibleFocusHandler-tFul0OhX"}},165630:e=>{e.exports={placeholder:"placeholder-V6ceS6BN"}},942544:(e,t,n)=>{"use strict";n.d(t,{useControlDisclosure:()=>l});var o=n(772069);function l(e){const{intent:t,highlight:n,...l}=e,{isFocused:r,...a}=(0,o.useDisclosure)(l);return{...a,isFocused:r,highlight:n??r,intent:t??(r?"primary":"default")}}},918460:(e,t,n)=>{"use strict";n.d(t,{Informer:()=>g});var o,l,r=n(50959),a=n(497754),s=n(878112),i=n(389986),c=n(800417),u=n(530162),d=n(527941),h=n(499084),m=n(869174),b=n.n(m);!function(e){e.Danger="danger",e.Warning="warning",e.Success="success",e.Default="default"}(o||(o={})),function(e){e.Inline="inline",e.NewLine="new-line"}(l||(l={}));const p={danger:u,warning:u,success:h,default:d};function g(e){const{informerIntent:t,content:n,className:o,header:l,isIconShown:u=!0,isCloseButtonShown:d,icon:h,onCloseClick:m,closeButtonLabel:g="Close",headerPlacement:C="inline",children:x,isLight:v}=e;return r.createElement("div",{className:a(b().container,b()[`container-${t}`],v&&b()[`light-container-${t}`],o),...(0,c.filterDataProps)(e),...(0,c.filterAriaProps)(e)},r.createElement("div",{className:b().informerBody},n&&r.createElement("div",{className:b().mainProblem},u&&r.createElement(s.Icon,{className:a(b().icon,v&&b()["light-icon"]),icon:h??p[t]}),r.createElement("div",{
className:a(b()["text-wrap"],v&&b()["light-text-wrap"])},l&&r.createElement("span",{className:a(v&&b()["light-title"],b().header,b()[`header-${v?"new-line":C}`])},l),r.createElement("span",{"aria-live":"assertive"}," ",n))),x),d&&r.createElement(i.CloseButton,{"aria-label":g,onClick:m,className:a(v&&b()["light-close-button"],b()["close-button"]),size:v?"xxsmall":"xsmall",preservePaddings:!v}))}},423994:(e,t,n)=>{"use strict";n.d(t,{CustomFields:()=>L});var o=n(50959),l=n(794087),r=n(302946),a=n(215773);function s(e){const t=o.createElement("span",{className:a.checkboxLabel},e.checkboxTitle);return o.createElement("div",null,e.title&&e.title.length>0&&o.createElement("div",{className:a.title},e.title),o.createElement(l.InputWithError,{className:e.className,inputClassName:a.input,type:e.inputType,value:e.text,placeholder:e.placeholder,onChange:e.onInputChange,onClick:e.onClick,error:void 0!==e.errorMessage,errorMessage:e.errorMessage}),o.createElement("div",{className:a.checkboxWrapper},o.createElement(r.Checkbox,{checked:e.checked,label:t,onChange:e.onCheckboxToggle})))}var i=n(661851);function c(e){const{inputType:t,text$:n,getText:l,setText:r,checked$:c,getChecked:u,setChecked:d,title:h,checkboxTitle:m,placeholder:b,isWait:p,onControlFocused:g,errorMessage$:C}=e,x=(0,i.useObservable)(n,l()),v=(0,i.useObservable)(c,u()),f=(0,i.useObservable)(C);return o.createElement("div",{className:a.customFieldWithCheckboxContainer},o.createElement(s,{className:p&&a.wait,inputType:t,text:x,checked:v,title:h,checkboxTitle:m,placeholder:b,onInputChange:e=>r(e.currentTarget.value),onCheckboxToggle:()=>{d(!v),g.fire()},onClick:()=>g.fire(),errorMessage:f}))}var u=n(609838),d=n(497754),h=n.n(d),m=n(529631),b=n(892106),p=n(269842),g=n(525388);const C=o.forwardRef(((e,t)=>{const{intent:n,onFocus:l,onBlur:r,onMouseOver:a,onMouseOut:s,hasErrors:i,hasWarnings:c,errors:u,warnings:d,alwaysShowAttachedErrors:h,messagesPosition:C,messagesAttachment:x,inheritMessagesWidthFromTarget:v,inputDescription:f,...w}=e,k=(0,b.useControlValidationLayout)({hasErrors:i,hasWarnings:c,errors:u,warnings:d,alwaysShowAttachedErrors:h,messagesPosition:C,messagesAttachment:x,iconHidden:!0,inheritMessagesWidthFromTarget:v}),E=(0,p.createSafeMulticastEventHandler)(l,k.onFocus),T=(0,p.createSafeMulticastEventHandler)(r,k.onBlur),y=(0,p.createSafeMulticastEventHandler)(a,k.onMouseOver),I=(0,p.createSafeMulticastEventHandler)(s,k.onMouseOut);return o.createElement(o.Fragment,null,o.createElement(m.Select,{...w,intent:k.intent??n,onFocus:E,onBlur:T,onMouseOver:y,onMouseOut:I,ref:(0,g.useMergedRefs)([k.containerReference,t]),"aria-describedby":k.ariaIds,"aria-invalid":i}),k.renderedErrors)}));C.displayName="FormSelect";var x=n(192063),v=n(679458),f=n(930052),w=n(996038),k=n(324171);function E(e){const{items:t,forceUserToSelectValue:l,alwaysShowAttachedErrors$:r,selectedItem:a,disabled:s,title:c,onClick:d,onItemSelected:m,onClose:p}=e,g=(0,o.useMemo)((()=>function(e){return e.map((e=>({value:e.value,content:e.text})))}(t)),[t]),E=(0,
o.useMemo)((()=>Object.values(g).map(((e,t)=>o.createElement(x.PopupMenuItem,{key:e.value,label:e.content??e.value,onClickArg:t,onClick:P})))),[g]),T=(0,i.useObservable)(r),y=void 0===a&&l;function I(e){return o.createElement(C,{value:a,items:g,className:k.select,menuClassName:e?k.selectMenu:void 0,onClick:M,onChange:S,disabled:s,placeholder:void 0===a?u.t(null,void 0,n(707343)):void 0,addPlaceholderToItems:!1,hasErrors:y,messagesPosition:b.MessagesPosition.Static,inheritMessagesWidthFromTarget:!0,hideArrowButton:g.length<=1,stretch:!0,alwaysShowAttachedErrors:Boolean(T)&&y,matchButtonAndListboxWidths:!0})}return o.createElement(f.MatchMedia,{rule:w.DialogBreakpoints.TabletSmall},(e=>o.createElement(o.Fragment,null,o.createElement("div",{className:h()(k.title,y&&k.titleWithError)},y?u.t(null,{replace:{fieldTitle:c}},n(712501)):c),e?o.createElement(v.ToolWidgetMenu,{className:k.menuButton,content:I(e),children:E,arrow:!1,onClose:N,isDrawer:!0,closeOnClickOutside:!0}):I(e))));function M(){void 0!==d&&d()}function S(e){m(e)}function N(){void 0!==p&&p()}function P(e){m(t[e||0].value),N()}}function T(e){const{title:t,items:n,selectedItem$:l,getSelectedItem:r,setSelectedItem:a,forceUserToSelectValue:s,alwaysShowAttachedErrors$:c,disabled:u,onControlFocused:d,onClose:h}=e,m=(0,i.useObservable)(l,r());return o.createElement(E,{title:t,items:n,selectedItem:m,onItemSelected:e=>a(e),disabled:u,onClick:()=>d?.fire(),onClose:h,forceUserToSelectValue:s,alwaysShowAttachedErrors$:c})}var y=n(834073),I=n(597683);function M(e){const{title:t,help:n,disabled:l,checked$:a,getChecked:s,setChecked:c,onControlFocused:u}=e,d=(0,i.useObservable)(a,s());return o.createElement("div",{className:I.wrapper,onFocus:function(){u?.fire()}},o.createElement(r.Checkbox,{label:t,checked:d,onChange:function(){c(!d)},disabled:l}),void 0!==n&&""!==n&&o.createElement(y.TradingInformer,{informerMessage:n,className:I.help}))}var S=n(953868);function N(e){return o.createElement("div",null,e.title&&e.title.length>0&&o.createElement("div",{className:S.title},e.title),o.createElement(l.InputWithError,{className:e.className,inputClassName:S.input,type:e.inputType,value:e.text,placeholder:e.placeholder,onChange:e.onInputChange,onClick:e.onClick,error:void 0!==e.errorMessage,errorMessage:e.errorMessage,disabled:e.disabled}))}function P(e){const{inputType:t,text$:n,getText:l,setText:r,title:a,placeholder:s,isWait:c,onControlFocused:u,errorMessage$:d,disabled:h}=e,m=(0,i.useObservable)(n,l()),b=(0,i.useObservable)(d);return o.createElement(N,{className:c&&S.wait,inputType:t,text:m,title:a,placeholder:s,onInputChange:e=>r(e.currentTarget.value),onClick:()=>u.fire(),errorMessage:b,disabled:h})}var F=n(628573);function L(e){const{customFieldModels:t,isWait:n,forceUserToSelectValue:l,onClose:r}=e;return o.createElement(o.Fragment,null,t.map((e=>o.createElement("div",{key:e.id,className:F.customField},"TextField"===e.type&&o.createElement(P,{inputType:e.inputType,text$:e.text$,getText:e.getText,setText:e.setText,placeholder:e.placeholder,title:e.title,isWait:n,
onControlFocused:e.onControlFocused,errorMessage$:e.errorMessage$,disabled:e.disabled}),"TextWithCheckBox"===e.type&&o.createElement(c,{inputType:e.inputType,text$:e.text$,getText:e.getText,setText:e.setText,placeholder:e.placeholder,checked$:e.checked$,getChecked:e.getChecked,setChecked:e.setChecked,title:e.title,checkboxTitle:e.checkboxTitle,isWait:n,onControlFocused:e.onControlFocused,errorMessage$:e.errorMessage$}),"Checkbox"===e.type&&o.createElement(M,{title:e.title,help:e.help,checked$:e.checked$,getChecked:e.getChecked,setChecked:e.setChecked,disabled:e.disabled,onControlFocused:e.onControlFocused}),"ComboBox"===e.type&&o.createElement(T,{title:e.title,items:e.items,selectedItem$:e.selectedItem$,getSelectedItem:e.getSelectedItem,setSelectedItem:e.setSelectedItem,onControlFocused:e.onControlFocused,onClose:r,disabled:e.items.length<=1,forceUserToSelectValue:l,alwaysShowAttachedErrors$:e.alwaysShowAttachedErrors$})))))}},444144:(e,t,n)=>{"use strict";n.d(t,{ControlDisclosureView:()=>h});var o=n(50959),l=n(497754),r=n.n(l),a=n(525388),s=n(34735),i=n(102691),c=n(904925),u=n(763802),d=n(99505);const h=o.forwardRef(((e,t)=>{const{listboxId:n,className:l,listboxClassName:h,listboxTabIndex:m,hideArrowButton:b,matchButtonAndListboxWidths:p,popupPosition:g,disabled:C,isOpened:x,scrollWrapReference:v,repositionOnScroll:f,closeOnHeaderOverlap:w,listboxReference:k,size:E="small",onClose:T,onOpen:y,onListboxFocus:I,onListboxBlur:M,onListboxKeyDown:S,buttonChildren:N,children:P,caretClassName:F,buttonContainerClassName:L,listboxAria:K,...O}=e,H=(0,o.useRef)(null),B=!b&&o.createElement(i.EndSlot,null,o.createElement(u.Caret,{isDropped:x,disabled:C,className:F}));return o.createElement(c.PopupMenuDisclosureView,{buttonRef:H,listboxId:n,listboxClassName:h,listboxTabIndex:m,isOpened:x,onClose:T,onOpen:y,listboxReference:k,scrollWrapReference:v,onListboxFocus:I,onListboxBlur:M,onListboxKeyDown:S,listboxAria:K,matchButtonAndListboxWidths:p,popupPosition:g,button:o.createElement(s.ControlSkeleton,{...O,"data-role":"listbox",disabled:C,className:r()(d.button,l),size:E,ref:(0,a.useMergedRefs)([H,t]),middleSlot:o.createElement(i.MiddleSlot,null,o.createElement("span",{className:r()(d["button-children"],b&&d.hiddenArrow,L)},N)),endSlot:B}),popupChildren:P,repositionOnScroll:f,closeOnHeaderOverlap:w})}));h.displayName="ControlDisclosureView"},763802:(e,t,n)=>{"use strict";n.d(t,{Caret:()=>o.Caret,CaretButton:()=>o.CaretButton});var o=n(148982)},529631:(e,t,n)=>{"use strict";n.d(t,{Select:()=>f});var o=n(50959),l=n(855393),r=n(414823),a=n(525388),s=n(930617),i=n(192063),c=n(290484),u=n(920057);var d=n(648621),h=n(953517),m=n(444144),b=n(942544),p=n(431520),g=n(165630);function C(e){return!e.readonly}function x(e,t){return t?.id??(0,r.createDomId)(e,"item",t?.value)}function v(e){const{selectedItem:t,placeholder:n}=e;if(!t)return o.createElement("span",{className:g.placeholder},n);const l=t.selectedContent??t.content??t.value;return o.createElement("span",null,l)}const f=o.forwardRef(((e,t)=>{
const{id:n,menuClassName:g,menuItemClassName:f,tabIndex:w,disabled:k,highlight:E,intent:T,hideArrowButton:y,placeholder:I,addPlaceholderToItems:M=!1,value:S,"aria-labelledby":N,onFocus:P,onBlur:F,onClick:L,onChange:K,onKeyDown:O,repositionOnScroll:H=!0,openMenuOnEnter:B=!0,"aria-describedby":W,"aria-invalid":$,...A}=e;let{items:D}=e;if(I&&M){D=[{value:void 0,content:I,id:(0,r.createDomId)(n,"placeholder")},...D]}const{listboxId:Q,isOpened:R,isFocused:V,buttonTabIndex:U,listboxTabIndex:q,highlight:X,intent:z,open:G,onOpen:j,close:_,toggle:J,buttonFocusBindings:Y,onButtonClick:Z,buttonRef:ee,listboxRef:te,buttonAria:ne}=(0,b.useControlDisclosure)({id:n,disabled:k,buttonTabIndex:w,intent:T,highlight:E,onFocus:P,onBlur:F,onClick:L}),oe=D.filter(C),le=oe.find((e=>e.value===S)),[re,ae]=o.useState(I&&M?oe[0].value:le?.value),[se,ie,ce]=(0,s.useKeepActiveItemIntoView)({activeItem:le});(0,l.useIsomorphicLayoutEffect)((()=>ae(le?.value)),[S]);const ue=(0,r.joinDomIds)(N,n),de=ue.length>0?ue:void 0,he=(0,o.useMemo)((()=>({role:"listbox","aria-labelledby":N,"aria-activedescendant":x(n,le)})),[N,le]),me=(0,o.useCallback)((e=>e.value===re),[re]),be=(0,o.useCallback)((()=>(_(),K&&K(re))),[_,K,re]),pe=(0,d.useItemsKeyboardNavigation)("vertical",p.isRtl,oe,me,(e=>{ae(e.value)}),!1,{next:[40],previous:[38]}),ge=(0,h.useKeyboardToggle)(J,R||B),Ce=(0,h.useKeyboardToggle)(be),xe=(0,h.useKeyboardClose)(R,Te),ve=(0,h.useKeyboardOpen)(R,G),fe=(0,h.useKeyboardEventHandler)([ge,xe,ve]),we=(0,h.useKeyboardEventHandler)([pe,Ce,xe]),ke=function(e){const t=(0,o.useRef)(""),n=(0,o.useMemo)((()=>(0,c.default)((()=>{t.current=""}),500)),[]),l=(0,o.useMemo)((()=>(0,u.default)(e,200)),[e]);return(0,o.useCallback)((e=>{e.key.length>0&&e.key.length<3&&(t.current+=e.key,l(t.current,e),n())}),[n,l])}(((t,n)=>{const o=function(e,t,n){return e.find((e=>{const o=t.toLowerCase();return!e.readonly&&(n?n(e).toLowerCase().startsWith(o):!e.readonly&&("string"==typeof e.content&&e.content.toLowerCase().startsWith(o)||"string"==typeof e.textContent&&e.textContent.toLowerCase().startsWith(o)||String(e.value??"").toLowerCase().startsWith(o)))}))}(oe,t,e.getSearchKey);void 0!==o&&K&&(n.stopPropagation(),R||G(),K(o.value))}));return o.createElement(m.ControlDisclosureView,{...A,...ne,...Y,id:n,role:"button",tabIndex:U,"aria-owns":ne["aria-controls"],"aria-haspopup":"listbox","aria-labelledby":de,disabled:k,hideArrowButton:y,isFocused:V,isOpened:R,highlight:X,intent:z,ref:(0,a.useMergedRefs)([ee,t]),onClick:Z,onOpen:function(){ce(le,{duration:0}),j()},onClose:Te,onKeyDown:function(e){fe(e),O&&O(e);e.defaultPrevented||ke(e)},listboxId:Q,listboxTabIndex:q,listboxClassName:g,listboxAria:he,"aria-describedby":W,"aria-invalid":$,listboxReference:te,scrollWrapReference:se,onListboxKeyDown:function(e){we(e),e.defaultPrevented||ke(e)},buttonChildren:o.createElement(v,{selectedItem:le??null,placeholder:I}),repositionOnScroll:H},D.map(((e,t)=>{if(e.readonly)return o.createElement(o.Fragment,{key:`readonly_item_${t}`},e.content);const l=x(n,e)
;return o.createElement(i.PopupMenuItem,{key:l,id:l,className:f,role:"option","aria-selected":S===e.value,isActive:re===e.value,label:e.content??e.value,onClick:Ee,onClickArg:e.value,isDisabled:e.disabled,reference:t=>ie(e,t)})})));function Ee(e){K&&(K(e),ae(e))}function Te(){ae(le?.value),_()}}));f.displayName="Select"}}]);