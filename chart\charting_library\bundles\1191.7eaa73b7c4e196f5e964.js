(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1191],{946105:e=>{e.exports={default:"default-EZuD3gZZ",danger:"danger-EZuD3gZZ",warning:"warning-EZuD3gZZ",success:"success-EZuD3gZZ",neutral:"neutral-EZuD3gZZ","neutral-light":"neutral-light-EZuD3gZZ",small:"small-EZuD3gZZ",medium:"medium-EZuD3gZZ",large:"large-EZuD3gZZ",iconWrapper:"iconWrapper-EZuD3gZZ",icon:"icon-EZuD3gZZ"}},485866:e=>{e.exports={button:"button-Pdm74BOe",text:"text-Pdm74BOe"}},512732:e=>{e.exports={colorPicker:"colorPicker-_3m87SIH",item:"item-_3m87SIH",selected:"selected-_3m87SIH"}},124685:e=>{e.exports={contextActions:"contextActions-D_fUDt4_",hideIfNoHover:"hideIfNoHover-D_fUDt4_",someContextActionFocused:"someContextActionFocused-D_fUDt4_"}},930170:e=>{e.exports={customPopoverItem:"customPopoverItem-eQTOre3a",clickable:"clickable-eQTOre3a"}},117959:e=>{e.exports={"menu-divider":"menu-divider-YZ5qU_gy","menu-divider-line":"menu-divider-line-YZ5qU_gy"}},214802:e=>{e.exports={fixed:"fixed-tNjElQXz",footer:"footer-tNjElQXz",hasDivider:"hasDivider-tNjElQXz",header:"header-tNjElQXz"}},937101:e=>{e.exports={right:"right-X1mHu7cR",arrow:"arrow-X1mHu7cR"}},61722:e=>{e.exports={wrap:"wrap-ZZBnHzkX"}},618822:e=>{e.exports={titleLine:"titleLine-LXzo3rzz",title:"title-LXzo3rzz",nestedPopovers:"nestedPopovers-LXzo3rzz",space:"space-LXzo3rzz",icon:"icon-LXzo3rzz"}},885389:e=>{e.exports={wrapper:"wrapper-U_380LxV",left:"left-U_380LxV",withLeftSlot:"withLeftSlot-U_380LxV",title:"title-U_380LxV",titleWrapper:"titleWrapper-U_380LxV"}},193139:e=>{e.exports={radioButtonView:"radioButtonView-zzLJI6BH",checked:"checked-zzLJI6BH",disabled:"disabled-zzLJI6BH"}},845658:e=>{e.exports={small:"small-CtnpmPzP",medium:"medium-CtnpmPzP",large:"large-CtnpmPzP",switchView:"switchView-CtnpmPzP",checked:"checked-CtnpmPzP",disabled:"disabled-CtnpmPzP",track:"track-CtnpmPzP",thumb:"thumb-CtnpmPzP"}},420071:e=>{e.exports={switcher:"switcher-fwE97QDf",input:"input-fwE97QDf",thumbWrapper:"thumbWrapper-fwE97QDf",disabled:"disabled-fwE97QDf",checked:"checked-fwE97QDf"}},541140:e=>{e.exports={"icon-wrapper":"icon-wrapper-dikdewwx","with-link":"with-link-dikdewwx","with-tooltip":"with-tooltip-dikdewwx","no-active-state":"no-active-state-dikdewwx"}},139784:(e,t,o)=>{"use strict";o.d(t,{useTooltip:()=>a});var n=o(50959),r=o(799573);var i=o(718736);const l=200;function a(e,t=null){const{showTooltip:o,hideTooltip:a,onClick:c,doNotShowTooltipOnTouch:s=!1}=e,u=(0,i.useFunctionalRefObject)(t),d=function(){const[e,t]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{t(r.mobiletouch)}),[]),e}(),p=d&&s?void 0:e.tooltip;(0,n.useEffect)((()=>{const e=()=>a&&a();return document.addEventListener("scroll",e,!0),()=>document.removeEventListener("scroll",e,!0)}),[u,a]);return{onBlur:(0,n.useCallback)((e=>{a&&a()}),[a]),onFocus:(0,n.useCallback)((e=>{!e.target.matches(":hover")&&o&&e.target.matches(":focus-visible")&&o(e.currentTarget,{tooltipDelay:l})}),[o]),onClick:(0,n.useCallback)((e=>{d&&u?.current?.focus(),c&&c(e)}),[c,u,d]),tooltip:p,
className:void 0!==p?"apply-common-tooltip":void 0,ref:u}}},89489:(e,t,o)=>{"use strict";o.d(t,{MenuPopover:()=>Fe,useMenuAnchorProps:()=>He.useMenuAnchorProps});var n=o(50959),r=(o(319937),o(723991)),i=o(592790),l=o(604244);function a(e){const{popoverItemRef:t,popoverItemButtonRef:o,popoverItemButtonId:a,role:c,ariaHaspopup:s,ariaControls:u,ariaExpanded:d,ariaLabel:p,leftSlot:m,title:v,description:h,selected:f,href:b,onClick:C,rel:E,...k}=e,g=k;return n.createElement(r.PopoverItem,{popoverItemRef:t,popoverItemButtonRef:o,popoverItemButtonId:a,role:c,ariaHaspopup:s,ariaControls:u,ariaExpanded:d,ariaLabel:p,leftSlot:m,title:v,description:h,href:b,rightSlot:n.createElement(i.BlockIcon,{icon:l}),selected:f,onClick:C,rel:E,...g})}var c=o(718736),s=o(646538),u=o(344346),d=o(35574),p=o(126338);function m(e){const t=(0,n.useRef)(null),o=(0,n.useRef)(null),{popoverItemRef:i=t,popoverItemButtonRef:l=o,popoverItemButtonId:a,role:m,ariaControls:v,ariaLabel:h,leftSlot:f,title:b,description:C,rightSlot:E,isOpen:k,selected:g,disabled:x,hotkey:w,onClick:I,ariaHaspopup:O,hideContextActionsIfNoHover:S,...A}=e,y=A,N=(0,c.useFunctionalRefObject)(i),P=(0,c.useFunctionalRefObject)(l),{onFocus:L,onKeyDown:R}=(0,s.useHorizontalMenuKeyboardNavigation)(),[T,B]=(0,n.useState)([]),D=(0,d.useIsFirstRender)(),_=(0,u.useWrappedIntoContextActionsContext)(E,B,S),z=(0,u.useWrappedIntoContextActionsContext)(b,B,S);return n.createElement("div",{onFocus:L,onKeyDown:R},n.createElement(r.PopoverItem,{popoverItemButtonId:a,leftSlot:f,title:z,description:C,rightSlot:_,isOpen:k,selected:g,disabled:x,hotkey:w,onClick:I,popoverItemRef:N,popoverItemButtonRef:P,role:m,ariaHaspopup:O,ariaControls:v,ariaExpanded:!0,ariaLabel:h,ariaBusy:D,outsideOfButtonSlot:n.createElement(p.ContextActionsSlot,{id:v,ariaLabelledby:a,ariaOwns:T}),...y}))}var v=o(679864),h=o(354648),f=o(497754),b=o.n(f),C=o(902129),E=o(885389);function k(e){const{id:t,role:o,leftSlot:r,title:i,onClick:l,ariaLabel:a,rightSlot:c,rightSlotAriaLabelledby:s,...u}=e,d=u;return n.createElement(C.TitleSectionPopoverItem,{...d,id:t,role:o,onClick:l,rightSlot:c,rightSlotAriaLabelledby:s,ariaLabel:a,className:b()(E.wrapper,r&&E.withLeftSlot)},n.createElement("div",{className:E.titleWrapper},r&&n.createElement("div",{className:E.left},r),n.createElement("div",{className:E.title},i)))}var g=o(317140);function x(e){const{title:t,onClick:o,...r}=e,i=r;return n.createElement(k,{title:t,onClick:o,leftSlot:n.createElement(h.SvgIconPopoverItemPart,{icon:g}),...i})}var w=o(241032),I=o(930202),O=o(656613),S=o(19952),A=o(393728),y=o(528457),N=o(865081),P=o(314485),L=o(756036),R=o(608753),T=o(603949),B=o(990390);const D=(0,R.windowEvent)("mousemove");var _,z=o(615309),F=o(937101);!function(e){e.Click="click",e.Hover="hover"}(_||(_={}));const H="hover",Z=300;function M(e){const t=(0,n.useId)(),o=(0,n.useRef)(null),l=(0,n.useRef)(null),a=(0,
n.useId)(),{children:s,popoverItemRef:u=o,popoverItemButtonId:d=a,popoverItemButtonRef:p=l,nestedPopoverRole:m,role:h,ariaLabel:f,leftSlot:b,openOn:C=H,closeOnHoverAwayDelayMs:E=Z,disabled:k,title:g,idOfContentWrapperElement:S=t,selected:R,description:_,rightSlot:M,...$}=e,U=(0,c.useFunctionalRefObject)(p),K=(0,c.useFunctionalRefObject)(u),{focusFirst:X}=(0,n.useMemo)((()=>(0,N.getSelectorKeyboardNavigation)(`[${w.DATA_IS_POPOVER_ITEM_BUTTON}]`)),[]),j=$,{isRtl:Q,closePopover:J,isMobilePositioner:G}=(0,A.usePopoverContext)(),q=G?"click":C,Y=(0,P.flipArrowsOnRtl)(37,Q),ee=(0,P.flipArrowsOnRtl)(39,Q),{isOpen:te,onOpen:oe,onClose:ne}=(0,y.useNestedPopoverOpenState)(U,J),re=(0,n.useRef)(null),ie=(0,n.useCallback)((()=>{if(te)ne(!0);else{oe();const e=re.current;e&&X(e)}}),[te,ne,oe,X]),le=(0,n.useCallback)((e=>{(0,I.hashFromEvent)(e)!==ee||ie()}),[ee,ie]);!function(e,t,o=0,n,r){(0,B.useConditionalEffect)(e,(()=>{let e=!1,i=-1;return D((l=>{const a={x:l.clientX,y:l.clientY},c=n.current,s=(0,T.isInExcludedArea)(a,r)||c&&(0,L.isXYInsideElementThroughPortals)(c,a.x,a.y)||!1;s!==e&&(clearTimeout(i),e=s,i=setTimeout((()=>{e||t(!1,"currentLevel")}),o))}))}),[n,o,r,t])}("hover"===q&&void 0!==E,ne,E,re,K);const ae="hover"===q&&void 0!==E?oe:void 0;return n.createElement(n.Fragment,null,n.createElement(r.PopoverItem,{leftSlot:b,title:g,description:_,role:h,popoverItemRef:K,popoverItemButtonRef:U,onClick:e=>{"button"in e&&"click"!==q||ie()},onKeyDownPopoverItemButton:le,rightSlot:n.createElement("div",{className:F.right},M,n.createElement(i.BlockIcon,{icon:z,className:F.arrow})),isOpen:te,ariaHaspopup:m,ariaExpanded:te,ariaControls:S,popoverItemButtonId:d,selected:R,disabled:k,onMouseEnter:ae,...j}),n.createElement(v.PopoverKeyboardNavigator,{idOfContentWrapperElement:S,role:m,anchored:{type:"element",at:K},isOpen:te,onClose:ne,placementRules:W.placementRules,ifNonePlacementRuleMatched:W.ifNonePlacementRuleMatched,width:V,height:O.POPOVER_DEFAULT_HEIGHT,isRtl:Q,refToContentWrapperElement:re,ariaLabelledby:d},G&&n.createElement(x,{title:g,onClick:()=>ne(!0,"currentLevel")}),n.createElement("div",{onKeyDown:e=>{const t=e.currentTarget,o=document.activeElement,n=(0,I.hashFromEvent)(e),r=o&&t.contains(o)&&"true"!==o.getAttribute(w.DATA_NESTED_POPOVER_OPEN);if(n===Y&&r)return ne(!0,"currentLevel"),void e.stopPropagation()}},s)))}const V=[{by:"content"}],W={placementRules:[{...S.PLACEMENT_RIGHT_ALIGN_TOP,offset:{x:0,y:-6}},{...S.PLACEMENT_LEFT_ALIGN_TOP,offset:{x:0,y:-6}},{...S.PLACEMENT_RIGHT_ALIGN_BOTTOM,offset:{x:0,y:6}},{...S.PLACEMENT_LEFT_ALIGN_BOTTOM,offset:{x:0,y:6}}],ifNonePlacementRuleMatched:O.POPOVER_DEFAULT_IF_NONE_PLACEMENT_RULE_MATCHED};var $=o(923058),U=o(512732);function K(e){const t=(0,n.useId)(),o=(0,n.useId)(),{colors:r,selectedColor:i,onChange:l,popoverItemRef:a,popoverItemButtonRef:c,popoverItemButtonId:s=t,role:u,ariaHaspopup:d,ariaControls:p=o,ariaLabel:v,isOpen:h,selected:f,disabled:b,hotkey:C,itemRole:E,ariaOrientation:k,rightSlotRole:g,...x}=e,w=x,I=(0,n.useCallback)((()=>{
const e=((r.findIndex((e=>e.color===i))??r.length-1)+1)%r.length;l(r[e].color)}),[r,i,l]);return n.createElement(n.Fragment,null,n.createElement(m,{popoverItemRef:a,popoverItemButtonRef:c,role:u,ariaHaspopup:d,ariaControls:p,ariaLabel:v,isOpen:h,selected:f,disabled:b,hotkey:C,leftSlot:"",title:"",description:"",onClick:I,rightSlot:n.createElement(X,{colors:r,selectedColor:i,onChange:l,id:p,ariaLabeledBy:s,role:g,itemRole:E,ariaOrientation:k}),popoverItemButtonId:s,...w}))}function X(e){const{selectedColor:t,colors:o,onChange:r,role:i,ariaOrientation:l,itemRole:a,id:c,ariaLabeledBy:s}=e;return n.createElement("div",{className:U.colorPicker,role:i,id:c,"aria-labelledby":s,"aria-orientation":l},o.map((e=>n.createElement(j,{key:e.color,color:e,selectedColor:t,onChange:r,role:a}))))}function j(e){const{color:t,selectedColor:o,onChange:r,role:i}=e;return n.createElement($.ContextAction,null,n.createElement("button",{className:b()(U.item,t.color===o&&U.selected,t.className),title:t.title,onClick:()=>r(t.color),role:i,"aria-checked":t.color===o}))}var Q=o(603369),J=o(167554),G=o(879413),q=o(664332);function Y(e){const t=(0,n.useContext)(G.FixedHeaderFooterHeightContext)["header"===e?"setFixedHeaderHeight":"setFixedFooterHeight"];return(0,q.useResizeObserver)({callback:e=>t(e[0].borderBoxSize[0].blockSize)})}var ee=o(214802);const te={[w.DATA_POSITIONER_FIXED_PART]:!0};function oe(e){const{children:t,dividerRole:o,showDivider:r=!0,...i}=e,l=i,a=Y("footer");return n.createElement("div",{...te,...l,className:b()(ee.fixed,ee.footer,r&&ee.hasDivider),ref:a},r&&n.createElement(Q.DividerPopoverItem,{role:o}),t)}const ne={[w.DATA_POSITIONER_FIXED_PART]:!0};function re(e){const{children:t,dividerRole:o,showDivider:r=!0,...i}=e,l=i,a=Y("header");return n.createElement("div",{...ne,...l,className:b()(ee.fixed,ee.header,r&&ee.hasDivider),ref:a},t,r&&n.createElement(Q.DividerPopoverItem,{role:o}))}var ie;!function(e){e.AllLevels="allLevels",e.CurrentLevel="currentLevel",e.DoNotClose="doNotClose"}(ie||(ie={}));const le="currentLevel";var ae;!function(e){e.DesktopOnly="desktopOnly",e.MobileOnly="mobileOnly",e.Always="always"}(ae||(ae={}));var ce=o(685075),se=o(810410),ue=o(485866);function de(e){const{title:t,children:o,buttonRole:r,buttonAriaHaspopup:i,popoverRole:l}=e,a=(0,n.useRef)(null),c=(0,n.useRef)(null),s=(0,n.useId)(),u=(0,n.useId)(),{closePopover:d}=(0,A.usePopoverContext)(),{isOpen:p,onOpen:m,onClose:h}=(0,y.useNestedPopoverOpenState)(a,d),f=(0,n.useCallback)((()=>{p?h(!0):m()}),[p,m,h]);return n.createElement(n.Fragment,null,n.createElement("button",{id:s,className:ue.button,onClick:f,ref:a,role:r,"aria-haspopup":i,"aria-controls":u,"aria-label":t},n.createElement("div",{className:ue.text},t)),n.createElement(v.PopoverKeyboardNavigator,{role:l,isOpen:p,onClose:h,anchored:{at:a,type:"element"},refToContentWrapperElement:c,idOfContentWrapperElement:u,ariaLabelledby:s},o))}const pe=(0,n.createContext)(undefined);var me=o(984950);function ve(e){
const{popoverItemRef:t,popoverItemButtonRef:o,popoverItemButtonId:i,role:l,title:a,description:c,checked:s,onClick:u,ariaChecked:d,...p}=e,m=p,v=(0,n.useCallback)((()=>{u?.(!s)}),[u,s]);return n.createElement(r.PopoverItem,{popoverItemRef:t,popoverItemButtonRef:o,popoverItemButtonId:i,ariaChecked:d,role:l,title:a,description:c,rightSlot:n.createElement(me.SwitchView,{checked:s}),onClick:v,...m})}var he=o(404568),fe=o(61722);function be(e){const{popoverItemRef:t,popoverItemButtonRef:o,popoverItemButtonId:i,role:l,title:a,description:c,checked:s,onClick:u,ariaChecked:d,...p}=e,m=p;return n.createElement(r.PopoverItem,{popoverItemRef:t,popoverItemButtonRef:o,popoverItemButtonId:i,ariaChecked:d,role:l,title:a,description:c,leftSlot:n.createElement("div",{className:fe.wrap},n.createElement(he.RadioButtonView,{checked:s})),onClick:u,...m})}function Ce(e){return t=>{const{isAnchorPositioner:o,isMobilePositioner:r}=(0,A.usePopoverContext)(),{visible:i="always",...l}=t;return"always"===i||o&&"desktopOnly"===i||r&&"mobileOnly"===i?n.createElement(e,{...l}):null}}function Ee(e){const t=(0,n.useContext)(pe),{closePopover:o}=(0,A.usePopoverContext)(),{onClick:r,closeOnClick:i=t??le}=e;return(0,n.useCallback)((()=>{r?.(),"doNotClose"!==i&&o(!0,{currentLevel:"currentLevel",allLevels:"allLevels"}[i])}),[r,i,o])}const ke=Ce((e=>{const t=Ee(e);return n.createElement(r.PopoverItem,{...ze(e),disabled:e.disabled,hotkey:e.hotkey,onClick:t,role:void 0!==e.selected?"menuitemcheckbox":"menuitem",ariaLabel:e.title,ellipsis:e.ellipsis,selected:e.selected,ariaChecked:e.selected??void 0,rightSlot:e.rightText,...e.dataAttributes})})),ge=Ce((e=>{const t=Ee(e);return n.createElement(a,{...ze(e),onClick:t,href:e.href,role:"menuitem",ariaLabel:e.title,selected:e.selected,rel:e.rel,...e.dataAttributes})})),xe=Ce((e=>{const t=(0,n.useId)();return n.createElement(k,{id:t,title:e.title,role:e.submenus?.length?"menuitem":void 0,ariaLabel:e.title,rightSlot:n.createElement(Le,{submenus:e.submenus,openSubmenuOn:e.openSubmenuOn})})})),we=Ce((e=>{const t=Ee(e),o=(0,n.useId)();return n.createElement(m,{...ze(e),popoverItemButtonId:o,hotkey:e.hotkey,ariaHaspopup:"menu",role:"menuitem",rightSlot:n.createElement(n.Fragment,null,e.contextActions.map(((e,t)=>n.createElement(Ie,{component:e[0],props:e[1],key:t})))),onClick:t,selected:e.selected,...e.dataAttributes})}));function Ie(e){const t=e.component;return n.createElement($.ContextAction,null,n.createElement(t,{role:"menuitem",...e.props}))}const Oe=Ce((e=>n.createElement(M,{...ze(e),role:"menuitem",nestedPopoverRole:"menu",openOn:e.openSubmenuOn,rightSlot:e.rightText,...e.dataAttributes},n.createElement(_e,{items:e.submenu,openSubmenuOn:e.openSubmenuOn})))),Se=Ce((e=>n.createElement(K,{colors:e.colors,selectedColor:e.selectedColor,onChange:e.onChange,ariaLabel:e.title,role:"menuitem",ariaHaspopup:"menu",rightSlotRole:"menu",ariaOrientation:"horizontal",itemRole:"menuitemradio"}))),Ae=Ce((e=>n.createElement(Q.DividerPopoverItem,{role:"separator"}))),ye=Ce((e=>n.createElement(ce.CheckboxPopoverItem,{title:e.title,
description:e.description,onClick:e.onClick,checked:e.checked,ariaChecked:e.checked,role:"menuitemcheckbox",...e.dataAttributes}))),Ne=Ce((e=>n.createElement(ve,{title:e.title,description:e.description,onClick:e.onClick,checked:e.checked,ariaChecked:e.checked,role:"menuitemcheckbox",...e.dataAttributes}))),Pe=Ce((e=>n.createElement(be,{title:e.title,description:e.description,onClick:e.onClick,checked:e.checked,ariaChecked:e.checked,role:"menuitemradio",...e.dataAttributes})));function Le(e){return e.submenus?n.createElement(n.Fragment,null,e.submenus.map(((t,o)=>n.createElement($.ContextAction,{key:`${t.title}:${o}`},n.createElement(de,{title:t.title},n.createElement(_e,{items:t.items,openSubmenuOn:e.openSubmenuOn})))))):null}const Re=Ce((e=>{const t=(0,n.useId)();return n.createElement(J.SectionPopoverItem,{id:t,title:e.title,itemRole:e.wrappable?"menuitemcheckbox":e.submenus?.length?"menuitem":void 0,wrappable:e.wrappable,defaultWrapped:e.defaultWrapped,ariaLabel:e.title,rightSlotAriaLabelledby:t,rightSlot:e.submenus?.length?n.createElement(Le,{submenus:e.submenus,openSubmenuOn:e.openSubmenuOn}):void 0},n.createElement(_e,{items:e.items,openSubmenuOn:e.openSubmenuOn}))})),Te=Ce((e=>{const{items:t}=e;return n.createElement(oe,{dividerRole:"separator"},n.createElement(_e,{items:t,openSubmenuOn:e.openSubmenuOn}))})),Be=Ce((e=>{const{items:t}=e;return n.createElement(re,{dividerRole:"separator"},n.createElement(_e,{items:t,openSubmenuOn:e.openSubmenuOn}))})),De=Ce((e=>{const{children:t,label:o}=e;return n.createElement(se.CustomPopoverItem,{disabled:!0,role:"menuitem",ariaLabel:o,focusable:!0},t)}));function _e(e){const{items:t}=e,o=(0,n.useMemo)((()=>"function"==typeof t?t():t),[t]);return n.createElement(n.Fragment,null,o.map(((t,o)=>{if(!t)return null;switch(t.type){case void 0:case"basic":return n.createElement(ke,{key:`basic-${t.title}:${t.description}:${t.id}`,iconSvg:t.iconSvg,iconUrl:t.iconUrl,iconJsx:t.iconJsx,iconSpace:t.iconSpace,title:t.title,description:t.description,onClick:t.onClick,hotkey:t.hotkey,disabled:t.disabled,ellipsis:t.ellipsis,closeOnClick:t.closeOnClick,selected:t.selected,visible:t.visible,rightText:t.rightText,dataAttributes:t.dataAttributes});case"link":return n.createElement(ge,{key:`link-${t.title}:${t.description}:${t.id}`,iconSvg:t.iconSvg,iconUrl:t.iconUrl,iconJsx:t.iconJsx,iconSpace:t.iconSpace,title:t.title,description:t.description,href:t.href,selected:t.selected,closeOnClick:t.closeOnClick,visible:t.visible,onClick:t.onClick,dataAttributes:t.dataAttributes,rel:t.rel});case"title":return n.createElement(xe,{key:`title-${t.title}`,title:t.title,visible:t.visible,submenus:t.submenus,openSubmenuOn:e.openSubmenuOn});case"contextActions":return n.createElement(we,{key:`contextActions-${t.title}:${t.description}:${t.id}`,iconSvg:t.iconSvg,iconUrl:t.iconUrl,iconJsx:t.iconJsx,iconSpace:t.iconSpace,title:t.title,description:t.description,contextActions:t.contextActions,onClick:t.onClick,hotkey:t.hotkey,selected:t.selected,closeOnClick:t.closeOnClick,visible:t.visible,
dataAttributes:t.dataAttributes});case"subMenu":return n.createElement(Oe,{key:`subMenu-${t.title}:${t.description}:${t.id}`,iconSvg:t.iconSvg,iconUrl:t.iconUrl,iconJsx:t.iconJsx,iconSpace:t.iconSpace,title:t.title,description:t.description,submenu:t.submenu,visible:t.visible,rightText:t.rightText,openSubmenuOn:e.openSubmenuOn,dataAttributes:t.dataAttributes});case"colorPicker":return n.createElement(Se,{key:`colorPicker-${t.title}:${t.id}`,title:t.title,colors:t.colors,selectedColor:t.selectedColor,onChange:t.onChange,visible:t.visible});case"divider":return n.createElement(Ae,{key:`divider-${o}:${t.id}`,visible:t.visible});case"checkbox":return n.createElement(ye,{key:`checkbox-${o}:${t.id}`,title:t.title,description:t.description,onClick:t.onClick,checked:t.checked,visible:t.visible,dataAttributes:t.dataAttributes});case"switch":return n.createElement(Ne,{key:`checkbox-${o}:${t.id}`,title:t.title,description:t.description,onClick:t.onClick,checked:t.checked,visible:t.visible,dataAttributes:t.dataAttributes});case"radio":return n.createElement(Pe,{key:`checkbox-${o}:${t.id}`,title:t.title,description:t.description,onClick:t.onClick,checked:t.checked,visible:t.visible,dataAttributes:t.dataAttributes});case"section":return n.createElement(Re,{key:`section-${t.title}:${t.id}`,title:t.title,items:t.items,wrappable:t.wrappable,defaultWrapped:t.defaultWrapped,visible:t.visible,submenus:t.submenus,openSubmenuOn:e.openSubmenuOn});case"header":return n.createElement(Be,{key:"header",items:t.items,visible:t.visible,openSubmenuOn:e.openSubmenuOn});case"footer":return n.createElement(Te,{key:"footer",items:t.items,visible:t.visible,openSubmenuOn:e.openSubmenuOn});case"nonInteractive":return n.createElement(De,{key:"nonInteractive-"+o,label:t.label,children:t.children})}})))}function ze(e){return{title:e.title,description:e.description,leftSlot:(0,h.iconPart)(e)}}function Fe(e){const{items:t,anchorId:o,anchorButtonRef:r,contentId:i,isOpen:l,onClose:a,placementRules:c,width:s,height:u,refToContentWrapperElement:d,repositionTriggers:p,openSubmenuOn:m="hover"}=e;return n.createElement(v.PopoverKeyboardNavigator,{role:"menu",ariaLabelledby:o,isOpen:l,onClose:a,anchored:{type:"element",at:r},idOfContentWrapperElement:i,placementRules:c,width:s,height:u,refToContentWrapperElement:d,repositionTriggers:p},n.createElement(_e,{items:t,openSubmenuOn:m}))}var He=o(499421)},499421:(e,t,o)=>{"use strict";o.d(t,{useMenuAnchorProps:()=>l});var n=o(319937),r=o(928141),i=o(937901);function l(e){return function(e){const{isOpen:t,onClose:o,contentId:l,anchorButtonRef:a,anchorId:c,handleAnchorClick:s,handleAnchorKeyDown:u}=(0,n.useDefaultButtonAnchorProps)({anchorId:e.anchorId,openOnEnter:e.openOnEnter});return{isOpen:t,anchorProps:{id:c,role:"button",onClick:s,onKeyDown:u,...(0,i.createRefProp)(e.refStyle,a),...(0,r.createAriaAttributesProps)(e.ariaStyle,{expanded:t,controls:l,haspopup:"menu"})},popoverProps:{isOpen:t,anchorId:c,anchorButtonRef:a,contentId:l,onClose:o}}}({openOnEnter:e?.openOnEnter,refStyle:e?.refStyle??"reference",
ariaStyle:e?.ariaStyle??"kebab-case",anchorId:e?.anchorId})}},646538:(e,t,o)=>{"use strict";o.d(t,{useHorizontalMenuKeyboardNavigation:()=>v});var n=o(241032),r=o(393728),i=o(923058),l=o(865081),a=o(930202),c=o(745845),s=o(32556),u=o(314485);function d(e,t={}){const{focusNext:o,focusPrev:n,focusFirst:r,focusLast:i}=(0,l.getSelectorKeyboardNavigation)(e,t.allowNestedFocusable),d={...t.horizontal?{39:o,37:n}:{40:o,38:n},...t.handleHomeEnd?{36:r,35:i}:{}};return{onFocus:function(o){const n=o.currentTarget;o.target===n&&function(o){const n=(0,c.getSelectorTreeWalker)(o,e).nextNode();n&&(0,s.isHTMLOrSVGElement)(n)&&n!==o&&n.focus(t.focusOptions)}(n)},onKeyDown:function(e){const o=(0,u.flipArrowsOnRtl)((0,a.hashFromEvent)(e),t.isRtl),n=e.currentTarget,r=e.target;if(!(r instanceof Node))return;if(!n.contains(r))return;const i=d[o];if(i){e.preventDefault();const o=i?.(n,t.focusOptions);o&&e.stopPropagation()}}}}const p=`[${n.DATA_IS_CONTEXT_ACTION}]`,m=i.CONTEXT_ACTION_FOCUSABLE_TAGS.map((e=>`${p} ${e}`)).join(","),v=()=>{const{isRtl:e}=(0,r.usePopoverContext)();return d(m+`,[${n.DATA_IS_POPOVER_ITEM_BUTTON}]`,{horizontal:!0,isRtl:e,allowNestedFocusable:!0})}},923058:(e,t,o)=>{"use strict";o.d(t,{CONTEXT_ACTION_FOCUSABLE_TAGS:()=>u,ContextAction:()=>p});var n=o(50959),r=o(497754),i=o.n(r),l=o(344346),a=o(241032),c=o(124685);const s={[a.DATA_IS_CONTEXT_ACTION]:!0},u=["a","button","input",'[aria-role="button"]'],d=u.join(",");function p(e){const{children:t}=e,{id:o,hideContextActionsIfNoHover:r,focusedContextActionsCounter:a,setFocusedContextActionsCounter:u}=(0,l.useContextAction)(),p=(0,n.useRef)(null);return(0,n.useEffect)((()=>{const e=p.current;if(!e)return;const t=e.querySelector(d);return t?function(e,t){const o=()=>{t((e=>e+1))},n=()=>{t((e=>e-1))};return e.addEventListener("focus",o),e.addEventListener("blur",n),()=>{e.removeEventListener("focus",o),e.removeEventListener("blur",n)}}(t,u):void 0}),[t,u]),n.createElement("span",{...s,id:o,ref:p,onClick:e=>{e.stopPropagation()},className:i()(c.contextActions,r&&c.hideIfNoHover,a>0&&c.someContextActionFocused)},t)}},344346:(e,t,o)=>{"use strict";o.d(t,{useContextAction:()=>l,useWrappedIntoContextActionsContext:()=>i});var n=o(50959);const r=(0,n.createContext)({setIds:()=>{},hideContextActionsIfNoHover:!1,focusedContextActionsCounter:0,setFocusedContextActionsCounter:()=>{}});function i(e,t,o){const[i,l]=(0,n.useState)(0),a=(0,n.useMemo)((()=>({focusedContextActionsCounter:i,setFocusedContextActionsCounter:l,setIds:t,hideContextActionsIfNoHover:!!o})),[i,l,t,o]);return(0,n.useMemo)((()=>e?n.createElement(r.Provider,{value:a},e):void 0),[e,a])}function l(){const{setIds:e,hideContextActionsIfNoHover:t,focusedContextActionsCounter:o,setFocusedContextActionsCounter:i}=(0,n.useContext)(r),l=(0,n.useId)();return(0,n.useEffect)((()=>(e((e=>[...e,l])),()=>{e((e=>e.filter((e=>e!==l))))})),[e,l]),{id:l,hideContextActionsIfNoHover:t,focusedContextActionsCounter:o,setFocusedContextActionsCounter:i}}},126338:(e,t,o)=>{"use strict";o.d(t,{ContextActionsSlot:()=>r});var n=o(50959)
;function r(e){const{id:t,ariaLabelledby:o,ariaOwns:r}=e;return n.createElement("div",{id:t,role:"menu","aria-labelledby":o,"aria-orientation":"horizontal","aria-owns":r.join(" "),"aria-hidden":0===r.length})}},810410:(e,t,o)=>{"use strict";o.d(t,{CustomPopoverItem:()=>c});var n=o(50959),r=o(497754),i=o.n(r),l=o(241032),a=o(930170);const c=(0,n.forwardRef)(((e,t)=>{const{id:o,children:r,onClick:c,disabled:s,focusable:u,role:d,ariaLabel:p,...m}=e,v=m,h=c?"button":"div",f=u||c,b=s?void 0:c;return n.createElement(h,{id:o,className:i()(a.customPopoverItem,b&&a.clickable),onClick:b,"aria-disabled":s,"aria-label":p,role:d,tabIndex:f?0:void 0,ref:t,...v,[l.DATA_IS_POPOVER_ITEM_BUTTON]:!!f||void 0},n.createElement("div",{inert:s?"true":void 0},r))}))},603369:(e,t,o)=>{"use strict";o.d(t,{DividerPopoverItem:()=>i});var n=o(50959),r=o(117959);function i(e){const{role:t,...o}=e,i=o;return n.createElement("div",{className:r["menu-divider"],role:t,...i},n.createElement("div",{className:r["menu-divider-line"]}))}},167554:(e,t,o)=>{"use strict";o.d(t,{SectionPopoverItem:()=>d});var n=o(50959),r=o(497754),i=o.n(r),l=o(878112),a=o(902129),c=o(347531),s=o(368182),u=o(618822);function d(e){const{id:t,title:o,children:r,itemRole:d,wrappable:p,defaultWrapped:m,ariaLabel:v,rightSlot:h,rightSlotAriaLabelledby:f,...b}=e,C=b,[E,k]=(0,n.useState)(m??!1),g=(0,n.useId)(),x=(0,n.useId)();return n.createElement(n.Fragment,null,n.createElement(a.TitleSectionPopoverItem,{...C,id:t,role:d,onClick:p?()=>k((e=>!e)):void 0,rightSlot:h,rightSlotAriaLabelledby:f,focusable:p,ariaExpanded:p?!E:void 0,ariaControls:p?x:void 0,ariaLabel:v,className:u.titleLine},n.createElement("div",{className:u.title,id:g},o),p&&n.createElement(n.Fragment,null,n.createElement("div",{className:u.space}),n.createElement(l.Icon,{className:u.icon,icon:E?c:s}))),n.createElement("div",{role:"group","aria-labelledby":g,id:x,className:i()(E&&u.wrapped)},!E&&r))}},902129:(e,t,o)=>{"use strict";o.d(t,{TitleSectionPopoverItem:()=>c});var n=o(50959),r=o(646538),i=o(344346),l=o(810410),a=o(126338);function c(e){const{id:t,role:o,onClick:c,rightSlot:s,rightSlotAriaLabelledby:u,focusable:d,ariaExpanded:p,ariaControls:m,ariaHaspopup:v,ariaLabel:h,children:f,className:b,...C}=e,E=C,{onKeyDown:k}=(0,r.useHorizontalMenuKeyboardNavigation)(),[g,x]=(0,n.useState)([]),w=(0,i.useWrappedIntoContextActionsContext)(s,x),I=(0,n.useId)();return n.createElement("div",{onKeyDown:k},n.createElement(l.CustomPopoverItem,{id:t,role:o,onClick:c,focusable:d||!!s,"aria-expanded":p??(!!s||void 0),"aria-controls":m??(s?I:void 0),"aria-haspopup":s?"menu":void 0,"aria-label":h,...E},n.createElement("div",{className:b},f,w)),n.createElement(a.ContextActionsSlot,{id:I,ariaLabelledby:u,ariaOwns:g}))}},404568:(e,t,o)=>{"use strict";o.d(t,{RadioButtonView:()=>a});var n=o(50959),r=o(497754),i=o.n(r),l=o(193139);function a(e){const{disabled:t,checked:o}=e;return n.createElement("span",{className:i()(l.radioButtonView,t&&l.disabled,o&&l.checked)})}},984950:(e,t,o)=>{"use strict";o.d(t,{Switch:()=>v,SwitchView:()=>s})
;var n,r=o(50959),i=o(497754),l=o.n(i),a=o(234539),c=o(845658);function s(e){const{size:t="small",checked:o,disabled:n}=e;return r.createElement("span",{className:l()(c.switchView,c[t],n&&c.disabled,o&&c.checked)},r.createElement("span",{className:c.track}),r.createElement("span",{className:c.thumb}))}!function(e){e.Small="small",e.Medium="medium",e.Large="large"}(n||(n={}));var u,d=o(930202),p=o(420071),m=o.n(p);function v(e){const t=(0,r.useContext)(a.CustomBehaviourContext),{size:o,intent:n="default",checked:l,className:c,enableActiveStateStyles:u=t.enableActiveStateStyles,disabled:p,onChange:v,title:h,id:f,name:b,value:C,tabIndex:E,role:k="switch",ariaDisabled:g,reference:x,ariaLabelledBy:w,ariaLabel:I,...O}=e;return r.createElement("span",{className:i(c,m().switcher)},r.createElement("input",{...O,type:"checkbox",className:i(m().input,u&&m().activeStylesEnabled,l&&m().checked,p&&m().disabled),role:k,"aria-checked":l,checked:l,onKeyDown:e=>{13===(0,d.hashFromEvent)(e)&&e.currentTarget?.click()},onChange:v,disabled:p,"aria-disabled":g,tabIndex:E,title:h,id:f,name:b,value:C,ref:x,"aria-label":I,"aria-labelledby":w}),r.createElement("span",{className:i(m().thumbWrapper,m()[n])},r.createElement(s,{checked:l,size:o,disabled:p})))}!function(e){e.Default="default",e.Select="select"}(u||(u={}))},314485:(e,t,o)=>{"use strict";function n(e,t){if(!t)return e;const o=255&e,n=-256&e;return 37===o?39+n:39===o?37+n:e}o.d(t,{flipArrowsOnRtl:()=>n})},928141:(e,t,o)=>{"use strict";function n(e,t){const o={};for(const r of Object.entries(t))o["camelCase"===e?"aria"+(n=r[0],""===n?"":n[0].toUpperCase()+n.substring(1)):"aria-"+r[0]]=r[1];var n;return o}o.d(t,{createAriaAttributesProps:()=>n})},937901:(e,t,o)=>{"use strict";function n(e,t){return{[e]:t}}o.d(t,{createRefProp:()=>n})},904925:(e,t,o)=>{"use strict";o.d(t,{PopupMenuDisclosureView:()=>u});var n=o(50959),r=o(624216),i=o(650151);const l={x:0,y:0};function a(e,t,o){return(0,n.useCallback)((()=>function(e,t,{x:o=l.x,y:n=l.y}=l){const r=(0,i.ensureNotNull)(e).getBoundingClientRect(),a={x:r.left+o,y:r.top+r.height+n,indentFromWindow:{top:4,bottom:4,left:4,right:4}};return t&&(a.overrideWidth=r.width),a}(e.current,t,o)),[e,t])}var c=o(586240);const s=parseInt(c["size-header-height"]);function u(e){const{button:t,popupChildren:o,buttonRef:i,listboxId:l,listboxClassName:c,listboxTabIndex:u,matchButtonAndListboxWidths:d,isOpened:p,scrollWrapReference:m,listboxReference:v,onClose:h,onOpen:f,onListboxFocus:b,onListboxBlur:C,onListboxKeyDown:E,listboxAria:k,repositionOnScroll:g=!0,closeOnHeaderOverlap:x=!1,popupPositionCorrection:w={x:0,y:0},popupPosition:I}=e,O=a(i,d,w),S=x?s:0;return n.createElement(n.Fragment,null,t,n.createElement(r.PopupMenu,{...k,id:l,className:c,tabIndex:u,isOpened:p,position:I||O,repositionOnScroll:g,onClose:h,onOpen:f,doNotCloseOn:i.current,reference:v,scrollWrapReference:m,onFocus:b,onBlur:C,onKeyDown:E,closeOnScrollOutsideOffset:S},o))}},297265:(e,t,o)=>{"use strict";o.d(t,{useWatchedValueReadonly:()=>r});var n=o(50959);const r=(e,t=!1,o=[])=>{
const r="watchedValue"in e?e.watchedValue:void 0,i="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[l,a]=(0,n.useState)(r?r.value():i);return(t?n.useLayoutEffect:n.useEffect)((()=>{if(r){a(r.value());const e=e=>a(e);return r.subscribe(e),()=>r.unsubscribe(e)}return()=>{}}),[r,...o]),l}},737563:(e,t,o)=>{"use strict";o.d(t,{IconQuestionInformation:()=>A});var n,r=o(50959),i=o(497754),l=o.n(i),a=o(800417),c=o(878112),s=o(482353),u=o(527941),d=o(499084),p=o(530162),m=o(946105),v=o.n(m);!function(e){e.Small="small",e.Medium="medium",e.Large="large"}(n||(n={}));const h="small";var f,b;!function(e){e.Default="default",e.Danger="danger",e.Warning="warning",e.Success="success",e.Neutral="neutral",e.NeutralLight="neutral-light"}(f||(f={})),function(e){e.Info="info",e.Question="question",e.Check="check",e.Exclamation="exclamation"}(b||(b={}));const C={info:u,question:s,check:d,exclamation:p},E=r.forwardRef(((e,t)=>r.createElement("span",{...e,ref:t,className:l()(e.className,v()["no-active-state"])}))),k=r.forwardRef(((e,t)=>{const{icon:o="exclamation",intent:n="default",ariaLabel:i,tooltip:s,className:u,renderComponent:d=E,tabIndex:p=0,size:m=h,onFocus:f,onBlur:b,onClick:k,...g}=e,x=C[o]??o;return r.createElement(d,{className:l()(u,v().iconWrapper,v()[n],v()[m]),title:s,"aria-label":i,ref:t,tabIndex:p,onFocus:f,onBlur:b,onClick:k,...(0,a.filterDataProps)(g)},r.createElement(c.Icon,{"aria-hidden":!0,icon:x,className:v().icon}))}));var g=o(139784),x=o(744471),w=o(541140),I=o.n(w);function O(){document.removeEventListener("scroll",O),document.removeEventListener("touchstart",O),document.removeEventListener("mouseout",O),(0,x.hide)()}const S=e=>{(0,x.showOnElement)(e.currentTarget,{tooltipDelay:0}),document.addEventListener("scroll",O),document.addEventListener("touchstart",O),document.addEventListener("mouseout",O)},A=(0,r.forwardRef)(((e,t)=>{const{className:o,onClick:n=S,doNotShowTooltipOnTouch:l,size:a,...c}=e,{tooltip:s,className:u,...d}=(0,g.useTooltip)({tooltip:e.tooltip,doNotShowTooltipOnTouch:!1,showTooltip:x.showOnElement,hideTooltip:x.hide,onClick:n},t);return r.createElement(k,{className:i(o,I()["icon-wrapper"],s&&I()["with-tooltip"],u),tooltip:s,size:a,...c,...d})}));(0,r.forwardRef)(((e,t)=>{const{className:o,href:n,rel:l,target:a,...c}=e,s=(0,r.useMemo)((()=>(0,r.forwardRef)(((e,t)=>r.createElement("a",{href:n,rel:l,target:a,ref:t,...e})))),[n,l,a]);return r.createElement(A,{...c,className:i(o,I()["with-link"]),renderComponent:s,ref:t,doNotShowTooltipOnTouch:!0})})),(0,r.forwardRef)(((e,t)=>{const{className:o,withActiveState:n,...l}=e,a=(0,r.useMemo)((()=>(0,r.forwardRef)(((e,t)=>r.createElement("button",{...e,ref:t,type:"button"})))),[]);return r.createElement(A,{...l,className:i(o,!n&&I()["no-active-state"]),renderComponent:a,ref:t})}))},834073:(e,t,o)=>{"use strict";o.d(t,{TradingInformer:()=>i});var n=o(50959);o(632227),o(661851);const r=n.lazy((async()=>({default:(await Promise.all([o.e(9642),o.e(4582),o.e(4010)]).then(o.bind(o,37624))).TradingInformerImpl})));function i(e){
const{informerMessage:t,className:o}=e;return n.createElement(n.Suspense,{fallback:null},n.createElement(r,{className:o,informerMessage:t}))}},347531:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="m4.67 7.38.66-.76L9 9.84l3.67-3.22.66.76L9 11.16 4.67 7.38Z"/></svg>'},615309:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9.84 9 6.62 5.33l.76-.66L11.16 9l-3.78 4.33-.76-.66L9.84 9Z"/></svg>'},368182:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="m4.67 10.62.66.76L9 8.16l3.67 3.22.66-.76L9 6.84l-4.33 3.78Z"/></svg>'},604244:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M4.5 3A1.5 1.5 0 0 0 3 4.5v9A1.5 1.5 0 0 0 4.5 15h9a1.5 1.5 0 0 0 1.5-1.5V10h-1v3.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5H8V3H4.5zM11 4h2.3L9.14 8.15l.7.7L14 4.71V7h1V3h-4v1z"/></svg>'},499084:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16Zm4-9.97L11.9 6 8.3 9.82 6.1 7.46 4.99 8.5 8.32 12 13 7.03Z"/></svg>'},530162:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16ZM9 4c-.79 0-1.38.7-1.25 1.48l.67 4.03a.59.59 0 0 0 1.16 0l.67-4.03A1.27 1.27 0 0 0 9 4Zm0 8a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"/></svg>'},527941:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16Zm1-12a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM8.5 9.5H7V8h3v6H8.5V9.5Z"/></svg>'},482353:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16Zm-1-4a1 1 0 1 0 2 0 1 1 0 0 0-2 0Zm2.83-3.52c-.49.43-.97.85-1.06 1.52H8.26c.08-1.18.74-1.69 1.32-2.13.49-.38.92-.71.92-1.37C10.5 6.67 9.82 6 9 6s-1.5.67-1.5 1.5V8H6v-.5a3 3 0 1 1 6 0c0 .96-.6 1.48-1.17 1.98Z"/></svg>'}}]);