(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4662,3334,7328],{697459:e=>{e.exports={en:["GS"]}},852506:e=>{e.exports={en:["Lmt"]}},66133:e=>{e.exports={en:["Mkt"]}},108701:e=>{e.exports={en:["Stp lmt"]}},54331:e=>{e.exports={en:["SL"]}},348473:e=>{e.exports={en:["Real-time data for {symbolName}"]}},684455:e=>{e.exports={en:["is provided by {exchange} exchange."]}},615678:e=>{e.exports={en:["TP"]}},454129:e=>{e.exports={en:["TRAIL"]}},433334:e=>{e.exports={en:["Close"]}},679953:e=>{e.exports={en:["Stop"]}},952240:e=>{e.exports={en:["Stp"]}},418083:e=>{e.exports={en:["Buy"]}},90737:e=>{e.exports={en:["Long"]}},327232:e=>{e.exports={en:["Sell"]}},193865:e=>{e.exports={en:["Short"]}},666643:e=>{e.exports={en:["% balance"]}},835519:e=>{e.exports={en:["Concurrent connection warning"]}},840225:e=>{e.exports={en:["Continuous futures contracts"]}},260657:e=>{e.exports={en:["Contract expired"]}},798702:e=>{e.exports={en:["Calculated boundaries do not match parent order side"]}},904543:e=>{e.exports={en:["Cancel"]}},719634:e=>{e.exports={en:["Cancel order"]}},830538:e=>{e.exports={en:["Cancel order on a non-tradable symbol {symbol}"]}},937129:e=>{e.exports={en:["Cancel bracket"]}},500864:e=>{e.exports={en:["Cannot Modify Order"]}},947962:e=>{e.exports={en:["Cannot cancel order on {symbol}"]}},466690:e=>{e.exports={en:["Cannot close position on {symbol}"]}},734567:e=>{e.exports={en:["Cannot protect position"]}},58796:e=>{e.exports={en:["Cboe One"]}},50659:e=>{e.exports={en:["Choosing the {riskTitle} will require you to set the stop loss price for further quantity calculation"]}},47742:e=>{e.exports={en:["Close menu"]}},383236:e=>{e.exports={en:["Close position"]}},182914:e=>{e.exports={en:["Close position on a non-tradable symbol {symbol}"]}},767325:e=>{e.exports={en:["Create new order"]}},381849:e=>{e.exports={en:["Currency"]}},772119:e=>{e.exports={en:["Currency risk"]}},316183:e=>{e.exports={en:["Amount"]}},605954:e=>{e.exports={en:["Accept"]}},510526:e=>{e.exports={en:["Alarm Clock"]}},639301:e=>{e.exports={en:["All good things must come to an end - this contract has reached expiry!"]}},492299:e=>{e.exports={en:["Are you confirming you'd like to restore order confirmations, which you've previously disabled?"]}},970322:e=>{e.exports={en:["Are you sure you want to cancel {orderId}bracket?"]}},569850:e=>{e.exports={en:["Are you sure you want to cancel {orderId}brackets?"]}},333739:e=>{e.exports={en:["Are you sure you want to cancel {qty} {side} orders?"]}},223615:e=>{e.exports={en:["Are you sure you want to cancel order {orderId}?"]}},431110:e=>{e.exports={en:["Are you sure you want to reverse {positionId} position?"]}},535928:e=>{e.exports={en:["Ask"]}},313518:e=>{e.exports={en:["Ask Size"]}},369635:e=>{e.exports={en:["Authorization Error"]}},846369:e=>{e.exports={en:["BUY"]}},395506:e=>{e.exports={en:["Bid"]}},912059:e=>{e.exports={en:["Bid Size"]}},443797:e=>{e.exports={en:["Bracket price is less than the instrument minimum of {min}."]}},625289:e=>{e.exports={
en:["Bracket price is more than the instrument maximum of {max}."]}},160523:e=>{e.exports={en:["Bracket price is out of range"]}},173069:e=>{e.exports={en:["Bracket price should be a multiple of {minTick}"]}},514236:e=>{e.exports={en:["Bracket price should be in the range of {min} and {max}"]}},169961:e=>{e.exports={en:["Buy"]}},99234:e=>{e.exports={en:["Buy Market"]}},570032:e=>{e.exports={en:["Buy real-time data"]}},913571:e=>{e.exports={en:["Buy {qty} {symbol} @ {value} limit"]}},253107:e=>{e.exports={en:["Buy {qty} {symbol} @ {value} stop"]}},278673:e=>{e.exports={en:["Buy {qty} {symbol} @ {value} stop {altValue} limit"]}},581591:e=>{e.exports={en:["Buy {quantity}"]}},379498:e=>{e.exports={en:["Buy {quantity}{separatorTag} at {limitPrice} (Limit) after {stopPrice} (Stop) crossed"]}},880512:e=>{e.exports={en:["Buy {quantity}{separatorTag} at {price}"]}},199452:e=>{e.exports={en:["Go to editor"]}},337539:e=>{e.exports={en:["Guaranteed Stop"]}},666391:e=>{e.exports={en:["Guaranteed Stop Loss order cancelled on {symbol}"]}},728481:e=>{e.exports={en:["Guaranteed Stop Loss order executed on {symbol}"]}},508106:e=>{e.exports={en:["Guaranteed Stop Loss order modified on {symbol}"]}},779598:e=>{e.exports={en:["Guaranteed Stop Loss order partially executed on {symbol}"]}},131629:e=>{e.exports={en:["Guaranteed Stop Loss order placed on {symbol}"]}},888894:e=>{e.exports={en:["Guaranteed Stop Loss order rejected on {symbol}"]}},611638:e=>{e.exports={en:["DOM"]}},442956:e=>{e.exports={en:["Dock to right"]}},127328:e=>{e.exports={en:["Don't show again"]}},932925:e=>{e.exports={en:["Data is updated once a day."]}},833039:e=>{e.exports={en:["Data is updated once per second, even if there are more updates on the market."]}},543348:e=>{e.exports={en:["Data is delayed"]}},138368:e=>{e.exports={en:["Data on our Basic plan is updated once per second, even if there are more updates on the market."]}},612869:e=>{e.exports={en:["Decline"]}},419679:e=>{e.exports={en:["Defaulted bond"]}},759158:e=>{e.exports={en:["Delayed data"]}},254602:e=>{e.exports={en:["Delisted"]}},331683:e=>{e.exports={en:["Delisted alert"]}},50035:e=>{e.exports={en:["Derived Data"]}},705805:e=>{e.exports={en:["End of day data"]}},627822:e=>{e.exports={en:["Estimated money impact is for main order only."]}},443383:e=>{e.exports={en:["Exchange disclaimer"]}},778837:e=>{e.exports={en:["Export data"]}},209877:e=>{e.exports={en:["Failed to modify Stop Loss / Take Profit"]}},663504:e=>{e.exports={en:["Failed to modify order"]}},83683:e=>{e.exports={en:["Failed to cancel brackets"]}},759469:e=>{e.exports={en:["Failed to cancel one or more orders"]}},919116:e=>{e.exports={en:["Failed to cancel order"]}},834737:e=>{e.exports={en:["Failed to close position"]}},139924:e=>{e.exports={en:["Failed to close the position"]}},679453:e=>{e.exports={en:["Failed to login"]}},48048:e=>{e.exports={en:["Failed to preview leverage"]}},628412:e=>{e.exports={en:["Failed to reverse position"]}},252888:e=>{e.exports={en:["Failed to set leverage"]}},759269:e=>{e.exports={
en:["From our family to yours, wishing you a merry festive season."]}},414181:e=>{e.exports={en:["HKEX and/or any subsidiaries endeavour to ensure the accuracy and reliability of the information provided but do not guarantee its accuracy or reliability and accept no liability (whether in tort or contract or otherwise) for any loss or damage arising from any inaccuracies or omissions."]}},675119:e=>{e.exports={en:["Halal symbol"]}},934987:e=>{e.exports={en:["Happy holidays, traders"]}},366025:e=>{e.exports={en:["I understand"]}},195400:e=>{e.exports={en:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"]}},248040:e=>{e.exports={en:["It looks like the order settings you've selected can't be used for this symbol."]}},991006:e=>{e.exports={en:["One update per second"]}},284937:e=>{e.exports={en:["Obligation not paid. Issuer payments are over a month late, this is due to coupon or principal debt."]}},819295:e=>{e.exports={en:["Ok"]}},172723:e=>{e.exports={en:["Opened in detached editor"]}},439664:e=>{e.exports={en:["Opened in editor"]}},512686:e=>{e.exports={en:["Order"]}},823887:e=>{e.exports={en:["Order Panel"]}},88346:e=>{e.exports={en:["Order confirmation"]}},845542:e=>{e.exports={en:["Order doesn't have a price."]}},405196:e=>{e.exports={en:["Order don't have price or pips."]}},330569:e=>{e.exports={en:["Order info"]}},348649:e=>{e.exports={en:["Order sent"]}},204395:e=>{e.exports={en:["Order preview"]}},10833:e=>{e.exports={en:["Order price is not appropriate for this order type/direction."]}},402043:e=>{e.exports={en:["Order price is too close to the market price. It can lead to placing wrong order type due to specifics of the order placement in {brokerTitle}"]}},806280:e=>{e.exports={en:["Order price should be a multiple of {minTick}"]}},775364:e=>{e.exports={en:["Order price should be in the range of {min} and {max}"]}},74390:e=>{e.exports={en:["Order rejected"]}},733144:e=>{e.exports={en:["Order {orderId} doesn't exist or has already been canceled"]}},574045:e=>{e.exports={en:["Lot size"]}},833526:e=>{e.exports={en:["Lots"]}},835617:e=>{e.exports={en:["Loading error. Try changing the parameters."]}},732114:e=>{e.exports={en:["Log Out"]}},672122:e=>{e.exports={en:["Log Out Confirmation"]}},166231:e=>{e.exports={en:["Log in warning"]}},728257:e=>{e.exports={en:["Long"]}},905301:e=>{e.exports={en:["Lost internet connection"]}},440846:e=>{e.exports={en:["Last"]}},274485:e=>{e.exports={en:["Last Size"]}},801356:e=>{e.exports={en:["Let it snow"]}},824813:e=>{e.exports={en:["Leverage"]}},327741:e=>{e.exports={en:["Learn more"]}},790258:e=>{e.exports={en:["Limit"]}},743087:e=>{e.exports={en:["Limit order cancelled on {symbol}"]}},586246:e=>{e.exports={en:["Limit order executed on {symbol}"]}},804352:e=>{e.exports={en:["Limit order modified on {symbol}"]}},133480:e=>{e.exports={en:["Limit order partially executed on {symbol}"]}},900493:e=>{e.exports={en:["Limit order placed on {symbol}"]}},570694:e=>{e.exports={
en:["Limit order rejected on {symbol}"]}},277646:e=>{e.exports={en:["Limit price"]}},561874:e=>{e.exports={en:["Modify"]}},224522:e=>{e.exports={en:["Modify order"]}},808249:e=>{e.exports={en:["Money"]}},437117:e=>{e.exports={en:["More"]}},544668:e=>{e.exports={en:["Max amount is {amount} {currency}."]}},9055:e=>{e.exports={en:["Margin currency"]}},207359:e=>{e.exports={en:["Margin {currency}"]}},490138:e=>{e.exports={en:["Market"]}},623851:e=>{e.exports={en:["Market order modified on {symbol}"]}},404107:e=>{e.exports={en:["Market order cancelled on {symbol}"]}},924681:e=>{e.exports={en:["Market order executed on {symbol}"]}},771010:e=>{e.exports={en:["Market order partially executed on {symbol}"]}},649642:e=>{e.exports={en:["Market order placed on {symbol}"]}},847816:e=>{e.exports={en:["Market order rejected on {symbol}"]}},38733:e=>{e.exports={en:["No"]}},606569:e=>{e.exports={en:["No asks"]}},44402:e=>{e.exports={en:["No bids"]}},838067:e=>{e.exports={en:["Non-applicable order preset"]}},645207:e=>{e.exports={en:["Non-tradable symbol"]}},784830:e=>{e.exports={en:["Number format is invalid"]}},424426:e=>{e.exports={en:["SELL"]}},419676:e=>{e.exports={en:["Select any field{line_break}to activate order placement"]}},168222:e=>{e.exports={en:["Sell"]}},132521:e=>{e.exports={en:["Sell {qty} {symbol} @ {value} limit"]}},219027:e=>{e.exports={en:["Sell {qty} {symbol} @ {value} stop"]}},873554:e=>{e.exports={en:["Sell {qty} {symbol} @ {value} stop {altValue} limit"]}},117112:e=>{e.exports={en:["Sell {quantity}"]}},754789:e=>{e.exports={en:["Sell {quantity}{separatorTag} at {limitPrice} (Limit) after {stopPrice} (Stop) crossed"]}},162734:e=>{e.exports={en:["Sell {quantity}{separatorTag} at {price}"]}},527953:e=>{e.exports={en:["Sell Market"]}},454674:e=>{e.exports={en:["Send"]}},288368:e=>{e.exports={en:["Shares"]}},13009:e=>{e.exports={en:["Short"]}},973353:e=>{e.exports={en:["Show Amount in {units} Risk"]}},411258:e=>{e.exports={en:["Show Buy/Sell buttons"]}},373447:e=>{e.exports={en:["Show Order Price in Ticks"]}},973085:e=>{e.exports={en:["Show Quantity in {units} Risk"]}},271854:e=>{e.exports={en:["Show TP/SL inputs in {units}"]}},74600:e=>{e.exports={en:["Show order confirmations"]}},777630:e=>{e.exports={en:["Side"]}},96248:e=>{e.exports={en:["Specified amount is less than the instrument minimum of {min}."]}},59195:e=>{e.exports={en:["Specified amount is more than the instrument maximum of {max}."]}},928316:e=>{e.exports={en:["Specified value is more than the % balance maximum of {max}."]}},60943:e=>{e.exports={en:["Specified value is more than the % risk maximum of {max}."]}},58451:e=>{e.exports={en:["Specified value is more than the currency maximum of {max}."]}},164335:e=>{e.exports={en:["Specified value is more than the currency risk maximum of {max}."]}},631331:e=>{e.exports={en:["Specified value is more than the instrument maximum of {max}."]}},324567:e=>{e.exports={en:["Specified value is less than the % balance minimum of {min}."]}},130315:e=>{e.exports={
en:["Specified value is less than the % risk minimum of {min}."]}},220509:e=>{e.exports={en:["Specified value is less than the currency minimum of {min}."]}},544265:e=>{e.exports={en:["Specified value is less than the currency risk minimum of {min}."]}},324216:e=>{e.exports={en:["Specified value is less than the instrument minimum of {min}."]}},659707:e=>{e.exports={en:["Spread"]}},898251:e=>{e.exports={en:["Stop"]}},396341:e=>{e.exports={en:["Stop the snow"]}},978369:e=>{e.exports={en:["Stop Limit"]}},672173:e=>{e.exports={en:["Stop Limit order cancelled on {symbol}"]}},347693:e=>{e.exports={en:["Stop Limit order executed on {symbol}"]}},976044:e=>{e.exports={en:["Stop Limit order modified on {symbol}"]}},875482:e=>{e.exports={en:["Stop Limit order partially executed on {symbol}"]}},531955:e=>{e.exports={en:["Stop Limit order placed on {symbol}"]}},407371:e=>{e.exports={en:["Stop Limit order rejected on {symbol}"]}},719702:e=>{e.exports={en:["Stop Loss"]}},258578:e=>{e.exports={en:["Stop Loss order modified on {symbol}"]}},25141:e=>{e.exports={en:["Stop Loss order cancelled on {symbol}"]}},256969:e=>{e.exports={en:["Stop Loss order executed on {symbol}"]}},465730:e=>{e.exports={en:["Stop Loss order partially executed on {symbol}"]}},533423:e=>{e.exports={en:["Stop Loss order placed on {symbol}"]}},141089:e=>{e.exports={en:["Stop Loss order rejected on {symbol}"]}},547602:e=>{e.exports={en:["Stop loss must be above current {value}."]}},833928:e=>{e.exports={en:["Stop loss must be below current {value}."]}},91189:e=>{e.exports={en:["Stop loss order must be below entry price."]}},753689:e=>{e.exports={en:["Stop loss order must be higher entry price."]}},543880:e=>{e.exports={en:["Stop order cancelled on {symbol}"]}},321105:e=>{e.exports={en:["Stop order executed on {symbol}"]}},897978:e=>{e.exports={en:["Stop order modified on {symbol}"]}},920175:e=>{e.exports={en:["Stop order partially executed on {symbol}"]}},447530:e=>{e.exports={en:["Stop order placed on {symbol}"]}},27267:e=>{e.exports={en:["Stop order rejected on {symbol}"]}},475299:e=>{e.exports={en:["Stop price"]}},264073:e=>{e.exports={en:["StopLimit"]}},370559:e=>{e.exports={en:["Summary row"]}},531410:e=>{e.exports={en:["Switch the symbol"]}},704707:e=>{e.exports={en:["Switch to {accountName}"]}},595481:e=>{e.exports={en:["Symbol"]}},491478:e=>{e.exports={en:["Symbol cannot be traded"]}},992655:e=>{e.exports={en:["Symbol changing timeout. Waiting for symbol {symbol}, but got {actualSymbol}"]}},871847:e=>{e.exports={en:["Synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."]}},944138:e=>{e.exports={en:["Synthetic symbol"]}},267539:e=>{e.exports={en:["Position"]}},792417:e=>{e.exports={en:["Position ID {id}"]}},798565:e=>{e.exports={en:["Position info"]}},526672:e=>{e.exports={en:["Position {positionId} doesn't exist or has already been closed"]}},23998:e=>{e.exports={en:["Paid plans feature faster data updates."]}},675236:e=>{e.exports={
en:["Parent order doesn't have a price."]}},207074:e=>{e.exports={en:["Pip value"]}},7953:e=>{e.exports={en:["Price"]}},994972:e=>{e.exports={en:["Primary listing"]}},116056:e=>{e.exports={en:["Proceed anyway"]}},598721:e=>{e.exports={en:["Quantity"]}},178170:e=>{e.exports={en:["Reverse {positionId} position?"]}},360196:e=>{e.exports={en:["Reverse position"]}},65183:e=>{e.exports={en:["Reward"]}},920987:e=>{e.exports={en:["Real-time data for this symbol is not supported right now. We may support it in the future."]}},131539:e=>{e.exports={en:["Real-time data for {symbolName} is provided by {exchange} exchange."]}},923642:e=>{e.exports={en:["Reset order preset"]}},18240:e=>{e.exports={en:["Restore confirmations"]}},663886:e=>{e.exports={en:["Risk"]}},136106:e=>{e.exports={en:["We were unable to check {brokerName} side maintenance status. Please, proceed at your own risk"]}},807281:e=>{e.exports={en:["To get real-time data for {description}, please buy the real-time data package."]}},926986:e=>{e.exports={en:["To learn more, please contact your broker."]}},408007:e=>{e.exports={en:["Total"]}},255739:e=>{e.exports={en:["Take Profit"]}},107364:e=>{e.exports={en:["Take Profit order cancelled on {symbol}"]}},735055:e=>{e.exports={en:["Take Profit order executed on {symbol}"]}},978630:e=>{e.exports={en:["Take Profit order modified on {symbol}"]}},184536:e=>{e.exports={en:["Take Profit order partially executed on {symbol}"]}},877889:e=>{e.exports={en:["Take Profit order placed on {symbol}"]}},425034:e=>{e.exports={en:["Take Profit order rejected on {symbol}"]}},327163:e=>{e.exports={en:["Take profit order must be above entry price."]}},482810:e=>{e.exports={en:["Take profit order must be lower entry price."]}},345417:e=>{e.exports={en:["Take profit must be above current {value}."]}},442403:e=>{e.exports={en:["Take profit must be below current {value}."]}},295246:e=>{e.exports={en:["The main, or first, stock exchange where a company's stock is listed and traded."]}},947870:e=>{e.exports={en:["The broker connection limit has been exceeded. Make sure you don't have a concurrent session and try to reconnect. If the issue persists, please, contact your broker."]}},985512:e=>{e.exports={en:["The broker is already selected in another tab. This connection can interrupt other connections to this broker."]}},488368:e=>{e.exports={en:["The cash quantity equivalent in the order is approximate, does not include a commission and depends on the actual order execution on the exchange"]}},949880:e=>{e.exports={en:["The connection attempt failed. Please try again later."]}},806786:e=>{e.exports={en:["The following settings need adjustment: {validationMessage}."]}},730086:e=>{e.exports={en:["The percentage quantity equivalent in the order is approximate, does not include a commission and depends on the actual order execution on the exchange"]}},445863:e=>{e.exports={en:["The position you are trying to close has been already closed."]}},819189:e=>{e.exports={en:["The position you are trying to close has been changed."]}},887411:e=>{e.exports={
en:["The position you are trying to reverse no longer exists."]}},925608:e=>{e.exports={en:["The source code of this script version is open in the Pine Editor."]}},733161:e=>{e.exports={en:["The source code of this script version is open in the detached Pine Editor."]}},3094:e=>{e.exports={en:["The specified quantity must be a multiple of the instrument's quantity step. In this case, it is {step}."]}},178224:e=>{e.exports={en:["This will also close all active orders"]}},352355:e=>{e.exports={en:["This will also close all active orders."]}},943288:e=>{e.exports={en:["This broker is currently carrying out maintenance. Please try again a little bit later or proceed at your own risk."]}},624669:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."]}},352668:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."]}},484484:e=>{e.exports={en:['This is a shariah-compliant bond, often referred to as a "sukuk," meaning that it complies with Islamic law that prohibits interest. Unlike conventional bonds, which involve paying interest, sukuk represent ownership in an underlying asset or project and investors earn returns based on profit-sharing or rental income.']}},667607:e=>{e.exports={en:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."]}},983556:e=>{e.exports={en:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."]}},344492:e=>{e.exports={en:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."]}},619481:e=>{e.exports={en:["This refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."]}},556445:e=>{e.exports={en:["This symbol can't be traded through this broker"]}},90589:e=>{e.exports={en:["This symbol is calculated by TradingView using the rate from other exchanges."]}},414285:e=>{e.exports={en:["Tick value"]}},248410:e=>{e.exports={en:["Trade value"]}},276495:e=>{e.exports={en:["Trading"]}},579646:e=>{e.exports={en:["Trading with this broker via multiple tabs is not possible. If you want to continue trading in this tab, please log out from the broker in another tab."]}},881352:e=>{e.exports={en:["Trading settings"]}},992201:e=>{e.exports={en:["Trailing Stop"]}},538030:e=>{e.exports={en:["Trailing Stop order cancelled on {symbol}"]}},543655:e=>{e.exports={en:["Trailing Stop order executed on {symbol}"]}},428941:e=>{e.exports={en:["Trailing Stop order modified on {symbol}"]}},490645:e=>{e.exports={en:["Trailing Stop order partially executed on {symbol}"]}},581861:e=>{
e.exports={en:["Trailing Stop order placed on {symbol}"]}},107497:e=>{e.exports={en:["Trailing Stop order rejected on {symbol}"]}},908940:e=>{e.exports={en:["Trailing Stops cannot be modified"]}},998413:e=>{e.exports={en:["Type"]}},746562:e=>{e.exports={en:["Unable to cancel an order on a non-tradable symbol"]}},495610:e=>{e.exports={en:["Unable to close a position on a non-tradable symbol"]}},702733:e=>{e.exports={en:["Undock"]}},811195:e=>{e.exports={en:["You need to select {customFields} in the order ticket."]}},913064:e=>{e.exports={en:["You already have at least one working order that can affect this position"]}},156910:e=>{e.exports={en:["You are trying to cancel the order on a non-tradable symbol {symbol}. Only position closing or order cancellation is available for this symbol. Once the order is cancelled, you will not be able to place a new order on {symbol}"]}},227617:e=>{e.exports={en:["You are trying to close the position on a non-tradable symbol {symbol}. Only position closing or order cancellation is available for this symbol. Once the position is closed, you will not be able to place a new order on {symbol}."]}},295271:e=>{e.exports={en:["You are currently seeing delayed data. All orders are executed at the delayed prices. Real-time data for this symbol is not supported right now. We may support it in the future."]}},668510:e=>{e.exports={en:["You are currently seeing delayed data. All orders are executed at the real-time prices which you cannot see. Please purchase real-time data package to see real-time data."]}},28117:e=>{e.exports={en:["You are seeing delayed data. You need to get the data from your broker, if you want to see it real-time."]}},41553:e=>{e.exports={en:["You can't trade the symbol {symbol} at TradingView via {broker}."]}},282478:e=>{e.exports={en:["You can't trade this market with this brokerage account. Please switch over to a different market and have another try."]}},880452:e=>{e.exports={en:["Your value exceeds the acceptable balance level."]}},955512:e=>{e.exports={en:["Yes"]}},542451:e=>{e.exports={en:["cancelled"]}},605598:e=>{e.exports={en:["change buy/sell buttons visibility"]}},115822:e=>{e.exports={en:["ask"]}},473220:e=>{e.exports={en:["bid"]}},417672:e=>{e.exports={en:["filled"]}},902456:e=>{e.exports={en:["in account currency"]}},270743:e=>{e.exports={en:["in symbol currency"]}},956995:e=>{e.exports={en:["inactive"]}},258063:e=>{e.exports={en:["order"]}},840882:e=>{e.exports={en:["placing"]}},218136:e=>{e.exports={en:["rejected"]}},654565:e=>{e.exports={en:["working"]}},637161:e=>{e.exports={en:["{brokerId} side maintenance"]}},655154:e=>{e.exports={en:["{exchange} by {originalExchange}"]}},651211:e=>{e.exports={en:["{listedExchange} real-time data is available for free to registered users."]}},416342:e=>{e.exports={en:["{qty} {symbol}{whitespaceNoBreak}@ {limitPrice}{whitespaceNoBreak}LMT"]}},925609:e=>{e.exports={en:["{qty} {symbol}{whitespaceNoBreak}@ {stopPrice}{whitespaceNoBreak}STP"]}},991313:e=>{e.exports={
en:["{qty} {symbol}{whitespaceNoBreak}@ {stopPrice}{whitespaceNoBreak}STP {limitPrice}{whitespaceNoBreak}LMT"]}},733740:e=>{e.exports={en:["{qty} {symbol}{whitespaceNoBreak}MKT"]}},881227:e=>{e.exports={en:["{symbolName} data is delayed by {time} minute because of exchange requirements.","{symbolName} data is delayed by {time} minutes because of exchange requirements."]}}}]);