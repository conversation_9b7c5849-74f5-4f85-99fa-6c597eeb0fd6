"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1713],{178950:(e,n,i)=>{i.r(n),i.d(n,{LineToolSineLine:()=>s});var t=i(792535),r=i(889868);class s extends r.LineDataSource{constructor(e,n,t,r){super(e,n??s.createProperties(e.backgroundTheme().spawnOwnership()),t,r),Promise.all([i.e(6290),i.e(6881),i.e(5579),i.e(1583)]).then(i.bind(i,583749)).then((e=>{this._setPaneViews([new e.SineLinePaneView(this,this._model)])}))}pointsCount(){return 2}name(){return"Sine Line"}static createProperties(e,n){const i=new t.DefaultProperty({defaultName:"linetoolsineline",state:n,theme:e});return this._configureProperties(i),i}async _getPropertyDefinitionsViewModelClass(){return(await Promise.all([i.e(6406),i.e(8511),i.e(5234),i.e(4590),i.e(8537)]).then(i.bind(i,921462))).CyclicAndSineLinesPatternDefinitionsViewModel}}}}]);