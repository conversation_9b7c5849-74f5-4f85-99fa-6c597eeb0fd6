<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Indicator Sources Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #1e1e1e;
            color: white;
        }
        
        #tv_chart_container {
            height: 100vh;
            width: 100%;
        }
        
        .demo-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            max-width: 300px;
        }
        
        .demo-panel h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        
        .demo-panel button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .demo-panel button:hover {
            background: #1976D2;
        }
        
        .demo-panel .info {
            font-size: 11px;
            color: #ccc;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div id="tv_chart_container"></div>
    
    <div class="demo-panel">
        <h3>📊 Indicator Sources Demo</h3>
        <div class="info">Add indicators and see them appear as sources!</div>
        
        <div style="margin: 10px 0;">
            <button onclick="addRSI()">Add RSI</button>
            <button onclick="addMACD()">Add MACD</button>
            <button onclick="addBollingerBands()">Add Bollinger Bands</button>
        </div>
        
        <div style="margin: 10px 0;">
            <button onclick="addMovingAverage(20, 'SMA')">Add SMA(20)</button>
            <button onclick="addMovingAverage(50, 'EMA')">Add EMA(50)</button>
        </div>
        
        <div style="margin: 10px 0;">
            <button onclick="addFlexibleSMA()">Add Flexible SMA</button>
            <button onclick="addFlexibleRSI()">Add Flexible RSI</button>
        </div>
        
        <div style="margin: 10px 0;">
            <button onclick="getSources()">Show Sources</button>
            <button onclick="refreshIndicatorSources()">Refresh Sources</button>
        </div>
        
        <div style="margin: 10px 0;">
            <button onclick="listIndicators()">List Indicators</button>
            <button onclick="showIndicatorManager()">Indicator Manager</button>
        </div>
        
        <div class="info">
            💡 After adding indicators, try adding a Flexible SMA or RSI and check the source dropdown!
        </div>
    </div>

    <!-- TradingView Charting Library -->
    <script type="text/javascript" src="charting_library/charting_library.standalone.js"></script>

    <!-- Custom Indicators -->
    <script type="text/javascript" src="custom_indicators.js"></script>
    <script type="text/javascript" src="open_interest_indicator.js"></script>
    <script type="text/javascript" src="simple_open_interest.js"></script>
    <script type="text/javascript" src="pine_indicators.js"></script>
    <script type="text/javascript" src="simple_candlestick_patterns.js"></script>

    <script type="text/javascript">
        window.studies = {};
        
        // Create a dynamic sources object that can be updated (same as index5.html)
        window.sourcesConfig = {
            _sources: ['open','high','low','close','hl2','hlc3','ohlc4','volume','oi'],
            _indicatorSources: new Map(),
            get sources() {
                return this._sources;
            },
            set sources(newSources) {
                console.log('Updating sources from:', this._sources, 'to:', newSources);
                this._sources = newSources;
                console.log('Sources updated to:', this._sources);
            },
            add: function(source) {
                if (!this._sources.includes(source)) {
                    this._sources.push(source);
                    console.log('Added source:', source, 'Current sources:', this._sources);
                }
            },
            remove: function(source) {
                const index = this._sources.indexOf(source);
                if (index > -1) {
                    this._sources.splice(index, 1);
                    console.log('Removed source:', source, 'Current sources:', this._sources);
                }
            },
            addIndicatorSource: function(indicatorId, indicatorName, outputs = []) {
                if (!outputs.length) {
                    outputs = this.getDefaultOutputs(indicatorName);
                }
                
                this._indicatorSources.set(indicatorId, {
                    name: indicatorName,
                    outputs: outputs
                });
                
                outputs.forEach(output => {
                    const sourceName = `${indicatorName}: ${output}`;
                    this.add(sourceName);
                });
                
                if (window.refreshFlexibleIndicators) {
                    window.refreshFlexibleIndicators();
                }
                
                console.log(`📊 Added indicator sources for ${indicatorName}:`, outputs);
            },
            removeIndicatorSource: function(indicatorId) {
                const indicator = this._indicatorSources.get(indicatorId);
                if (indicator) {
                    indicator.outputs.forEach(output => {
                        const sourceName = `${indicator.name}: ${output}`;
                        this.remove(sourceName);
                    });
                    
                    this._indicatorSources.delete(indicatorId);
                    
                    if (window.refreshFlexibleIndicators) {
                        window.refreshFlexibleIndicators();
                    }
                    
                    console.log(`📊 Removed indicator sources for ${indicator.name}`);
                }
            },
            getDefaultOutputs: function(indicatorName) {
                const outputMap = {
                    'RSI': ['RSI'],
                    'Relative Strength Index': ['RSI'],
                    'MACD': ['MACD', 'Signal', 'Histogram'],
                    'Moving Average': ['MA'],
                    'Moving Average Exponential': ['EMA'],
                    'Moving Average Weighted': ['WMA'],
                    'Bollinger Bands': ['Upper Band', 'Middle Band', 'Lower Band'],
                    'Stochastic': ['%K', '%D'],
                    'Williams %R': ['%R'],
                    'Volume': ['Volume'],
                    'ATR': ['ATR'],
                    'CCI': ['CCI'],
                    'Momentum': ['Momentum'],
                    'ROC': ['ROC'],
                    'Aroon': ['Aroon Up', 'Aroon Down'],
                    'ADX': ['ADX', '+DI', '-DI'],
                    'Parabolic SAR': ['SAR'],
                    'Ichimoku Cloud': ['Tenkan', 'Kijun', 'Senkou A', 'Senkou B'],
                    'VWAP': ['VWAP'],
                    'Awesome Oscillator': ['AO'],
                    'Chaikin Money Flow': ['CMF'],
                    'Money Flow Index': ['MFI'],
                    'On Balance Volume': ['OBV'],
                    'Price Volume Trend': ['PVT'],
                    'Accumulation/Distribution': ['A/D'],
                    'Force Index': ['FI'],
                    'Ease of Movement': ['EMV'],
                    'Trix': ['TRIX'],
                    'Ultimate Oscillator': ['UO'],
                    'Vortex Indicator': ['VI+', 'VI-'],
                    'Open Interest': ['OI'],
                    'Flexible SMA': ['SMA'],
                    'Flexible RSI': ['RSI']
                };
                
                return outputMap[indicatorName] || [indicatorName];
            }
        };

        // For backward compatibility
        window.sources = window.sourcesConfig.sources;

        function getParameterByName(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)'),
                results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Simple Datafeed for demo
        class SimpleDatafeed {
            constructor() {
                this.configuration = {
                    supports_marks: false,
                    supports_timescale_marks: true,
                    supported_resolutions: ['1', '5', '15', '30', '60', '1D', '1W', '1M'],
                    exchanges: [{ value: '', name: '', desc: '' }],
                    symbols_types: [
                        { name: 'All', value: '' },
                        { name: 'Stock', value: 'stock' },
                        { name: 'Index', value: 'index' }
                    ]
                };

                // Generate sample data
                this.generateSampleData();
            }

            onReady(callback) {
                console.log('[onReady]: Method call');
                setTimeout(() => callback(this.configuration), 0);
            }

            searchSymbols(userInput, exchange, symbolType, onResultReadyCallback) {
                console.log('[searchSymbols]: Method call');
                const symbols = [
                    {
                        symbol: 'NSE:RELIANCE-EQ',
                        full_name: 'NSE:RELIANCE-EQ',
                        description: 'Reliance Industries',
                        exchange: 'NSE',
                        ticker: 'NSE:RELIANCE-EQ',
                        type: 'stock'
                    }
                ];
                onResultReadyCallback(symbols);
            }

            resolveSymbol(symbolName, onSymbolResolvedCallback, onResolveErrorCallback) {
                console.log('[resolveSymbol]: Method call', symbolName);
                const symbolInfo = {
                    ticker: symbolName,
                    name: symbolName,
                    description: 'Demo Stock',
                    type: 'stock',
                    session: '0915-1530',
                    timezone: 'Asia/Kolkata',
                    exchange: 'NSE',
                    minmov: 1,
                    pricescale: 100,
                    has_intraday: true,
                    has_no_volume: false,
                    has_weekly_and_monthly: true,
                    supported_resolutions: this.configuration.supported_resolutions,
                    volume_precision: 0,
                    data_status: 'streaming'
                };
                setTimeout(() => onSymbolResolvedCallback(symbolInfo), 0);
            }

            getBars(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) {
                console.log('[getBars]: Method call', symbolInfo, resolution, periodParams);

                if (!this.sampleData || this.sampleData.length === 0) {
                    onHistoryCallback([], { noData: true });
                    return;
                }

                const rawBars = this.sampleData.map(candle => ({
                    time: candle[0],
                    open: candle[1],
                    high: candle[2],
                    low: candle[3],
                    close: candle[4],
                    volume: candle[5] || 1000
                }));

                const filteredBars = rawBars.filter(bar => {
                    return bar.time >= periodParams.from * 1000 && bar.time <= periodParams.to * 1000;
                });

                console.log(`[getBars]: Returning ${filteredBars.length} bars`);
                onHistoryCallback(filteredBars, { noData: false });
            }

            subscribeBars(symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) {
                console.log('[subscribeBars]: Method call', symbolInfo.ticker, subscriberUID);
            }

            unsubscribeBars(subscriberUID) {
                console.log('[unsubscribeBars]: Method call', subscriberUID);
            }

            generateSampleData() {
                const now = Date.now();
                const oneDay = 24 * 60 * 60 * 1000;
                const data = [];
                let price = 2500;

                for (let i = 100; i >= 0; i--) {
                    const time = now - (i * oneDay);
                    const change = (Math.random() - 0.5) * 50;
                    price += change;

                    const open = price;
                    const high = price + Math.random() * 30;
                    const low = price - Math.random() * 30;
                    const close = low + Math.random() * (high - low);
                    const volume = Math.floor(Math.random() * 1000000) + 100000;

                    data.push([time, open, high, low, close, volume]);
                    price = close;
                }

                this.sampleData = data;
                console.log(`Generated ${data.length} sample data points`);
            }
        }

        // Initialize datafeed
        const datafeed = new SimpleDatafeed();
        
        // Create TradingView widget
        const widget = new TradingView.widget({
            debug: false,
            fullscreen: true,
            symbol: 'NSE:RELIANCE-EQ',
            interval: '5',
            container: 'tv_chart_container',
            datafeed: datafeed,
            library_path: 'charting_library/',
            locale: getParameterByName('lang') || 'en',
            disabled_features: [
                'use_localstorage_for_settings'
            ],
            enabled_features: [
                'study_templates'
            ],
            custom_indicators_getter: function(PineJS) {
                return Promise.resolve([
                    ...(window.customIndicators || []),
                    ...(window.pineIndicators || [])
                ]);
            }
        });

        // Initialize when chart is ready
        widget.onChartReady(() => {
            console.log('📈 Chart is ready!');

            // Initialize Indicator Manager (simplified version)
            window.IndicatorManager = {
                widget: widget,
                chart: widget.chart(),
                indicators: new Map(),

                addIndicator: function(indicatorName, options = {}) {
                    if (!this.chart) {
                        console.error('❌ Chart not available');
                        return null;
                    }

                    try {
                        const {
                            forceOverlay = false,
                            inputs = [],
                            styles = {},
                            callback = null
                        } = options;

                        console.log(`🔧 Adding indicator: ${indicatorName}`, options);

                        const studyId = this.chart.createStudy(
                            indicatorName,
                            forceOverlay,
                            false,
                            inputs,
                            callback,
                            styles
                        );

                        if (studyId) {
                            this.indicators.set(studyId, {
                                id: studyId,
                                name: indicatorName,
                                forceOverlay: forceOverlay,
                                inputs: inputs,
                                styles: styles,
                                addedAt: new Date()
                            });

                            // Add indicator outputs as available sources
                            window.sourcesConfig.addIndicatorSource(studyId, indicatorName);

                            console.log(`✅ Indicator added successfully: ${indicatorName} (ID: ${studyId})`);
                            console.log(`📊 Updated sources:`, window.sourcesConfig.sources);
                            return studyId;
                        } else {
                            console.error(`❌ Failed to add indicator: ${indicatorName}`);
                            return null;
                        }
                    } catch (error) {
                        console.error(`❌ Error adding indicator ${indicatorName}:`, error);
                        return null;
                    }
                },

                removeIndicator: function(indicatorId) {
                    if (!this.chart) {
                        console.error('❌ Chart not available');
                        return false;
                    }

                    try {
                        window.sourcesConfig.removeIndicatorSource(indicatorId);
                        this.chart.removeEntity(indicatorId);
                        this.indicators.delete(indicatorId);
                        console.log(`✅ Indicator removed: ${indicatorId}`);
                        console.log(`📊 Updated sources:`, window.sourcesConfig.sources);
                        return true;
                    } catch (error) {
                        console.error(`❌ Error removing indicator ${indicatorId}:`, error);
                        return false;
                    }
                },

                getAllIndicators: function() {
                    return Array.from(this.indicators.values());
                }
            };

            // Global convenience functions
            window.addIndicator = function(name, options = {}) {
                return window.IndicatorManager.addIndicator(name, options);
            };

            window.removeIndicator = function(id) {
                return window.IndicatorManager.removeIndicator(id);
            };

            window.listIndicators = function() {
                const indicators = window.IndicatorManager.getAllIndicators();
                console.table(indicators);
                return indicators;
            };

            window.addRSI = function(period = 14, overlay = false) {
                return window.IndicatorManager.addIndicator('RSI', {
                    inputs: [period],
                    forceOverlay: overlay
                });
            };

            window.addMACD = function(fast = 12, slow = 26, signal = 9, overlay = false) {
                return window.IndicatorManager.addIndicator('MACD', {
                    inputs: [fast, slow, signal],
                    forceOverlay: overlay
                });
            };

            window.addBollingerBands = function(period = 20, stdDev = 2) {
                return window.IndicatorManager.addIndicator('Bollinger Bands', {
                    inputs: [period, stdDev],
                    forceOverlay: true
                });
            };

            window.addMovingAverage = function(period = 20, type = 'SMA') {
                const indicatorName = type === 'EMA' ? 'Moving Average Exponential' :
                                     type === 'WMA' ? 'Moving Average Weighted' : 'Moving Average';
                return window.IndicatorManager.addIndicator(indicatorName, {
                    inputs: [period],
                    forceOverlay: true
                });
            };

            window.addFlexibleSMA = function(period = 14, source = 'close') {
                window.refreshFlexibleIndicators();
                return window.IndicatorManager.addIndicator('Flexible SMA', {
                    inputs: [period, source],
                    forceOverlay: false
                });
            };

            window.addFlexibleRSI = function(period = 14, source = 'close', overbought = 70, oversold = 30) {
                window.refreshFlexibleIndicators();
                return window.IndicatorManager.addIndicator('Flexible RSI', {
                    inputs: [period, source, overbought, oversold],
                    forceOverlay: false
                });
            };

            window.refreshFlexibleIndicators = function() {
                console.log('📊 Refreshing indicator definitions with current sources...');
                console.log('Current sources for new indicators:', window.sourcesConfig.sources);

                if (window.studies && window.studies['Flexible SMA']) {
                    window.studies['Flexible SMA'].metainfo.inputs[1].options = window.sourcesConfig.sources;
                }
                if (window.studies && window.studies['Flexible RSI']) {
                    window.studies['Flexible RSI'].metainfo.inputs[1].options = window.sourcesConfig.sources;
                }

                console.log('✅ Indicator definitions updated with latest sources');
            };

            window.getSources = function() {
                console.log('📊 Current sources:', window.sourcesConfig.sources);
                console.log('📈 Basic sources:', ['open','high','low','close','hl2','hlc3','ohlc4','volume','oi']);
                console.log('🔧 Indicator sources:', Array.from(window.sourcesConfig._indicatorSources.values()));
                return window.sourcesConfig.sources;
            };

            window.refreshIndicatorSources = function() {
                console.log('🔄 Refreshing all indicator sources...');

                const indicators = window.IndicatorManager.getAllIndicators();
                window.sourcesConfig._indicatorSources.clear();
                window.sourcesConfig._sources = ['open','high','low','close','hl2','hlc3','ohlc4','volume','oi'];

                indicators.forEach(indicator => {
                    window.sourcesConfig.addIndicatorSource(indicator.id, indicator.name);
                });

                console.log('✅ All indicator sources refreshed');
                console.log('📊 Updated sources:', window.sourcesConfig.sources);
                return window.sourcesConfig.sources;
            };

            window.showIndicatorManager = function() {
                console.log('📊 Indicator Manager Demo');
                console.log('Available functions:');
                console.log('- addRSI(), addMACD(), addBollingerBands()');
                console.log('- addMovingAverage(period, type)');
                console.log('- addFlexibleSMA(), addFlexibleRSI()');
                console.log('- getSources(), refreshIndicatorSources()');
                console.log('- listIndicators()');
            };

            console.log('📊 TradingView Indicator Sources Demo');
            console.log('💡 This demo shows how indicators automatically become available as sources');
            console.log('🎯 Try: 1) Add some indicators, 2) Add a Flexible SMA/RSI, 3) Check the source dropdown!');
            console.log('🔧 Type showIndicatorManager() for available functions');
        });
    </script>
</body>
</html>
