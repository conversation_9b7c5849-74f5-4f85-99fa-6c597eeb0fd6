"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[78326],{158942:(e,t,n)=>{n.d(t,{WatchedValue:()=>r.WatchedValue});var r=n(597773);n(390251)},536794:(e,t,n)=>{function r(e){return"number"==typeof e&&isFinite(e)}function s(e){if(!e||"object"!=typeof e)return e;let t;t=Array.isArray(e)?[]:{};for(const n in e)if(e.hasOwnProperty(n)){const r=e[n];t[n]=r&&"object"==typeof r?s(r):r}return t}function i(e){return"object"==typeof e&&null!==e}n.d(t,{clone:()=>s,isArray:()=>o,isInteger:()=>a,isNumber:()=>r,isObject:()=>i});const o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function a(e){return"number"==typeof e&&e%1==0}Number.isNaN=Number.isNaN||function(e){return e!=e}},390251:(e,t,n)=>{n(536794),n(597773)},597773:(e,t,n)=>{n.d(t,{WatchedValue:()=>a});var r=n(735566),s=n(954992);const i=(0,r.getLogger)("Common.WatchedValue");function o(e){i.logError(`${e&&(e.stack||e.message)}`)}class a{constructor(...e){this._listeners=[],e.length>0?this._value=e[0]:delete this._value}destroy(){this.unsubscribe()}value(){return this._owner?this._owner._value:this._value}setValue(e,t){const n=this._owner?this._owner:this;if(n.writeLock)return;const r=n._value===e||Number.isNaN(n._value)&&Number.isNaN(e);if(!t&&r&&n.hasOwnProperty("_value"))return;n._value=e;const s=n._listeners.slice();let i=0;for(let t=0;t<s.length;t++){s[t].once&&(n._listeners.splice(t-i,1),i++);try{s[t].cb(e)}catch(e){o(e)}}}deleteValue(){this.setValue(void 0)}subscribe(e,t){if("function"!=typeof e)throw new TypeError("callback must be a function");const n=!!t&&!!t.once,r=!!t&&!!t.callWithLast,s=this._owner?this._owner:this;if(r&&s.hasOwnProperty("_value")){try{e(s._value)}catch(e){o(e)}if(n)return}s._listeners.push({cb:e,owner:this,once:!!t&&!!t.once})}unsubscribe(e){const t=this._owner?this._owner:this;void 0===e&&(e=null);const n=t._listeners;for(let r=n.length;r--;)n[r].owner!==this&&t!==this||n[r].cb!==e&&null!==e||n.splice(r,1)}readonly(){if(this._readonlyInstance)return this._readonlyInstance;const e={subscribe:this.subscribe.bind(this),unsubscribe:this.unsubscribe.bind(this),value:this.value.bind(this),when:this.when.bind(this),ownership:this.ownership.bind(this),spawnOwnership:this.spawnOwnership.bind(this),weakReference:this.weakReference.bind(this),spawn:e=>this.spawn(e).readonly(),destroy:this.destroy.bind(this)};return this._readonlyInstance=e,e}spawn(e){return this._spawn(e)}when(e){(0,s.callWhen)(this,(e=>Boolean(e)),(()=>{try{e(this.value())}catch(e){o(e)}}))}assertNoSubscriptions(){0}ownership(){return this}release(){this.destroy()}spawnOwnership(){return this._spawn()}weakReference(){return this._spawn(void 0,!0)}_spawn(e,t){return new c(this._owner||this,e,t)}}class c extends a{constructor(e,t,n){super(),delete this._listeners,this._owner=e,this._onDestroy=t,this._weakReference=!!n}destroy(){try{this._onDestroy?.()}catch(e){o(e)}super.destroy()}readonly(){return this._readonlySpawnInstance||(this._readonlySpawnInstance={...super.readonly(),destroy:()=>this.destroy(),readonly(){
return this}}),this._readonlySpawnInstance}release(){this._weakReference||super.release()}}},607423:(e,t,n)=>{n.d(t,{isNativeUIInteraction:()=>i});var r=n(591800);function s(e){if("INPUT"===e.tagName){const t=e.type;return"text"===t||"email"===t||"number"===t||"password"===t||"search"===t||"tel"===t||"url"===t}return"TEXTAREA"===e.tagName||e.isContentEditable}function i(e,t){if(!t)return!1;const n=255&e;if(27===n||n>>>4==7)return!1;switch(e^n){case r.Modifiers.Alt:return(38===n||40===n)&&"SELECT"===t.tagName||s(t);case r.Modifiers.Alt+r.Modifiers.Shift:return s(t);case r.Modifiers.Mod:if(67===n||!r.isMacKeyboard&&45===n){const e=t.ownerDocument&&t.ownerDocument.getSelection();if(e&&!e.isCollapsed)return!0}return s(t);case r.Modifiers.Mod+r.Modifiers.Shift:return n>=33&&n<=40&&s(t);case r.Modifiers.Shift:case 0:return 9===n?!(!t.ownerDocument||t===t.ownerDocument.body||t===t.ownerDocument.documentElement):(!function(e){if("BUTTON"===e.tagName)return!0;if("INPUT"===e.tagName){const t=e.type;if("submit"===t||"button"===t||"reset"===t||"checkbox"===t||"radio"===t)return!0}return!1}(t)||13===n||32===n||9===n)&&("form"in t||t.isContentEditable)}return!1}},336748:(e,t,n)=>{n.d(t,{colorsPalette:()=>h});var r=n(130714),s=n(410515),i=n(650151);const o={...r,...s},a={},c=Object.keys(o).length,u=/^#(([a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i;function l(e,t=[],n=o){const r=n[e];if(!r)return null;if(u.test(r))return r;const s=r;return t.push(e),-1!==t.indexOf(s)?(console.warn("Colors definitions cycled"),r):t.length>c?(console.warn("Too many variables-link in HEX-color search: "+t[0]),null):l(s,t,n)}Object.keys(o).forEach((e=>{const t=l(e);a[e]=(0,i.ensureNotNull)(t)}));const h=a},790188:(e,t,n)=>{n.d(t,{colorsPalette:()=>r.colorsPalette});var r=n(336748)},745269:(e,t,n)=>{n.d(t,{color:()=>r});const r={black70:"#4A4A4A",black80:"#535353"}},547465:(e,t,n)=>{n.d(t,{Delegate:()=>r.Delegate});var r=n(978956)},820028:(e,t,n)=>{n.d(t,{WatchedValue:()=>r.WatchedValue});var r=n(158942)}}]);