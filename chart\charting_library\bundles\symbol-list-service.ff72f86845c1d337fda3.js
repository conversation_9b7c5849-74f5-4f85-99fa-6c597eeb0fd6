"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1026],{605011:(t,e,n)=>{n.r(e),n.d(e,{activeSymbolListNavigator:()=>a});var o=n(746212),i=n(129885),s=n(926032),l=n(254931),r=n(688401),c=n(706474);function*a(t){const e=(0,o.eventChannel)((t=>{const e=(e,n)=>{u(e)&&t(n)},n=s.createGroup({desc:"Active Symbol List Navigation"});return n.add({desc:"Select previous symbol",hotkey:38,handler:t=>e(t,"previous"),isDisabled:t=>!u(t)}),n.add({desc:"Select previous symbol",hotkey:s.Modifiers.Shift+32,handler:t=>e(t,"previous"),isDisabled:t=>!u(t)}),n.add({desc:"Select next symbol",hotkey:32,handler:t=>e(t,"next"),isDisabled:t=>!u(t)}),n.add({desc:"Select next symbol",hotkey:40,handler:t=>e(t,"next"),isDisabled:t=>!u(t)}),()=>n.destroy()}));try{for(;;){const n=yield(0,i.take)(e),o=(0,l.getGlobalActiveSymbolList)(yield(0,i.select)());if(null===o)continue;const s=o.symbols;if(0===s.length)continue;const a=r.linking.proSymbol.value(),u=r.linking.symbolNamesList.value();let d=(0,c.getSymbolFromList)(a,s);!d&&u&&u.forEach((t=>{d=(0,c.getSymbolFromList)(t,s)}));const f=s.indexOf(d||"");switch(n){case"previous":{const e=-1===f?s.length-1:(f+s.length-1)%s.length;if((0,c.isValidSeparatorItem)(s[e])){const n=(0,c.findNextAvailableSymbol)(e,s,"previous");n&&r.linking.setSymbolAndLogInitiator(n,t);break}r.linking.setSymbolAndLogInitiator(s[e],t);break}case"next":{const e=-1===f?0:(f+s.length+1)%s.length;if((0,c.isValidSeparatorItem)(s[e])){const n=(0,c.findNextAvailableSymbol)(e,s,"next");n&&r.linking.setSymbolAndLogInitiator(n,t);break}r.linking.setSymbolAndLogInitiator(s[e],t);break}}}}finally{e.close()}}function u(t){return!t.defaultPrevented&&(null!==t.target.closest("[data-allow-watchlist-navigation]")||t.target===document.body)}},2595:(t,e,n)=>{n.r(e),n.d(e,{configureStore:()=>h});var o=n(891442),i=n(746212),s=n(440891),l=n(717866),r=n(924910),c=n(429498),a=n(406047),u=n(650279),d=n(70644);function f(t,e,n,o){return{id:t,tickerType:e,columns:n,options:o,selectedSymbols:[],sorting:null,highlightedSymbols:null,listId:null,isLoading:!1,symbolsBeforeSorting:null,sortingListId:null,scrollToId:null}}function g(t,e){if(t.length!==e.length)return!1;const n=[...t].sort(),o=[...e].sort();return(0,u.default)(n,o)}function m(t,e){return{...t,[e]:{...t[e],sorting:null,symbolsBeforeSorting:null,sortingListId:null}}}var y=n(706474);const b=(0,a.combineReducers)({positions:function(t={},e){switch(e.type){case d.UPDATE_POSITIONS:{const{symbol:n,position:o}=e;return(0,y.isEqualRecords)(t[n],o)?t:{...t,[n]:o}}case d.UPDATE_BULK_POSITIONS:{const{map:n}=e,o={...t};let i=!1;for(const[e,s]of Object.entries(n))(0,y.isEqualRecords)(t[e],s)||(i=!0,o[e]=s);return i?o:t}default:return t}},customLists:c.reducer,hotLists:(t,e)=>null,markedLists:(t,e)=>null,widgets:function(t={},e){if(c.setup.match(e)||c.insert.match(e)||c.exclude.match(e)||c.exact.match(e)||c.replace.match(e)){let n=t;for(const o of Object.values(t)){const{listId:i,sortingListId:s,symbolsBeforeSorting:l}=o;if(null!==i){if(i===s&&c.setup.match(e)){
const t=e.payload.lists.find((t=>t.id===s));if(void 0!==t&&null!==l&&g(t.symbols,l))continue}n=m(t,o.id)}}return n}return function(t={},e){switch(e.type){case d.INIT_WIDGET:{const{id:n,tickerType:o,columns:i,options:s}=e;return{...t,[n]:f(n,o,i,s)}}case d.UPDATE_WIDGET:{const{widgetId:n,widget:o}=e,i={...t,[n]:{...t[n],...o}},s=t[n].tickerType,l=o.tickerType,r="short_name"===t[n].sorting?.column;return void 0!==l&&s!==l&&r?m(i,n):i}case d.UPDATE_WIDGET_OPTIONS:{const{widgetId:n,options:o}=e;return{...t,[n]:{...t[n],options:{...t[n].options,...o}}}}default:return t}}(t,e)},isAuthenticated:()=>!1,activeSymbolList:function(t=null,e){return e.type===d.UPDATE_ACTIVE_LIST?e.token:t},collapsedSeparators:()=>({})});function h(){const t=(0,i.default)();return{store:(0,o.configureStore)({reducer:b,middleware:e=>[...e(),...p(),t,s.enabled("use_localstorage_for_settings")&&s.enabled("watchlist_cross_tab_sync")?(0,c.createCrossTabSyncMiddleware)((0,r.randomHash)(),l.TVXWindowEvents):null,null].filter((t=>null!==t))}),runner:t}}function p(){return[]}},215078:(t,e,n)=>{function o(t){return e=>t+"__"+e}n.d(e,{createActionTypeFactory:()=>o})}}]);