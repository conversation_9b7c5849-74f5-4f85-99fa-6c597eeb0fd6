(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[61997],{150335:(e,t)=>{"use strict";function n(e){return Math.round(1e10*e)/1e10}t.isNumber=void 0,t.isNumber=function(e){return"number"==typeof e&&isFinite(e)}},685459:function(e,t,n){var r;!function(t){"use strict";function i(){}var s=i.prototype,o=t.EventEmitter;function u(e,t){for(var n=e.length;n--;)if(e[n].listener===t)return n;return-1}function f(e){return function(){return this[e].apply(this,arguments)}}function c(e){return"function"==typeof e||e instanceof RegExp||!(!e||"object"!=typeof e)&&c(e.listener)}s.getListeners=function(e){var t,n,r=this._getEvents();if(e instanceof RegExp)for(n in t={},r)r.hasOwnProperty(n)&&e.test(n)&&(t[n]=r[n]);else t=r[e]||(r[e]=[]);return t},s.flattenListeners=function(e){var t,n=[];for(t=0;t<e.length;t+=1)n.push(e[t].listener);return n},s.getListenersAsObject=function(e){var t,n=this.getListeners(e);return n instanceof Array&&((t={})[e]=n),t||n},s.addListener=function(e,t){if(!c(t))throw new TypeError("listener must be a function");var n,r=this.getListenersAsObject(e),i="object"==typeof t;for(n in r)r.hasOwnProperty(n)&&-1===u(r[n],t)&&r[n].push(i?t:{listener:t,once:!1});return this},s.on=f("addListener"),s.addOnceListener=function(e,t){return this.addListener(e,{listener:t,once:!0})},s.once=f("addOnceListener"),s.defineEvent=function(e){return this.getListeners(e),this},s.defineEvents=function(e){for(var t=0;t<e.length;t+=1)this.defineEvent(e[t]);return this},s.removeListener=function(e,t){var n,r,i=this.getListenersAsObject(e);for(r in i)i.hasOwnProperty(r)&&-1!==(n=u(i[r],t))&&i[r].splice(n,1);return this},s.off=f("removeListener"),s.addListeners=function(e,t){return this.manipulateListeners(!1,e,t)},s.removeListeners=function(e,t){return this.manipulateListeners(!0,e,t)},s.manipulateListeners=function(e,t,n){var r,i,s=e?this.removeListener:this.addListener,o=e?this.removeListeners:this.addListeners;if("object"!=typeof t||t instanceof RegExp)for(r=n.length;r--;)s.call(this,t,n[r]);else for(r in t)t.hasOwnProperty(r)&&(i=t[r])&&("function"==typeof i?s.call(this,r,i):o.call(this,r,i));return this},s.removeEvent=function(e){var t,n=typeof e,r=this._getEvents();if("string"===n)delete r[e];else if(e instanceof RegExp)for(t in r)r.hasOwnProperty(t)&&e.test(t)&&delete r[t];else delete this._events;return this},s.removeAllListeners=f("removeEvent"),s.emitEvent=function(e,t){var n,r,i,s,o=this.getListenersAsObject(e);for(s in o)if(o.hasOwnProperty(s))for(n=o[s].slice(0),i=0;i<n.length;i++)!0===(r=n[i]).once&&this.removeListener(e,r.listener),r.listener.apply(this,t||[])===this._getOnceReturnValue()&&this.removeListener(e,r.listener);return this},s.trigger=f("emitEvent"),s.emit=function(e){var t=Array.prototype.slice.call(arguments,1);return this.emitEvent(e,t)},s.setOnceReturnValue=function(e){return this._onceReturnValue=e,this},s._getOnceReturnValue=function(){return!this.hasOwnProperty("_onceReturnValue")||this._onceReturnValue},s._getEvents=function(){return this._events||(this._events={})},
i.noConflict=function(){return t.EventEmitter=o,i},void 0===(r=function(){return i}.call(t,n,t,e))||(e.exports=r)}(this||{})}}]);