(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2878],{703845:e=>{e.exports={"default-drawer-min-top-distance":"100px"}},536718:e=>{e.exports={"default-drawer-min-top-distance":"100px",wrap:"wrap-_HnK0UIN",positionBottom:"positionBottom-_HnK0UIN",backdrop:"backdrop-_HnK0UIN",drawer:"drawer-_HnK0UIN",positionLeft:"positionLeft-_HnK0UIN"}},622413:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",focused:"focused-_FRQhM5Y",active:"active-_FRQhM5Y",checked:"checked-_FRQhM5Y"}},395214:e=>{e.exports={item:"item-zwyEh4hn",label:"label-zwyEh4hn",labelRow:"labelRow-zwyEh4hn",toolbox:"toolbox-zwyEh4hn"}},700238:e=>{e.exports={separator:"separator-QjUlCDId",small:"small-QjUlCDId",normal:"normal-QjUlCDId",large:"large-QjUlCDId"}},975228:(e,t,o)=>{"use strict";o.d(t,{hoverMouseEventFilter:()=>l,useAccurateHover:()=>a,useHover:()=>i});var n=o(50959);function i(){const[e,t]=(0,n.useState)(!1);return[e,{onMouseOver:function(e){l(e)&&t(!0)},onMouseOut:function(e){l(e)&&t(!1)}}]}function l(e){return!e.currentTarget.contains(e.relatedTarget)}function a(e){const[t,o]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{const t=t=>{if(null===e.current)return;const n=e.current.contains(t.target);o(n)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},664332:(e,t,o)=>{"use strict";o.d(t,{useResizeObserver:()=>a});var n=o(50959),i=o(855393),l=o(718736);function a(e,t=[]){const{callback:o,ref:a=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),s=(0,n.useRef)(null),r=(0,n.useRef)(o);r.current=o;const c=(0,l.useFunctionalRefObject)(a),u=(0,n.useCallback)((e=>{c(e),null!==s.current&&(s.current.disconnect(),null!==e&&s.current.observe(e))}),[c,s]);return(0,i.useIsomorphicLayoutEffect)((()=>(s.current=new ResizeObserver(((e,t)=>{r.current(e,t)})),c.current&&u(c.current),()=>{s.current?.disconnect()})),[c,...t]),u}},269842:(e,t,o)=>{"use strict";function n(...e){return t=>{for(const o of e)void 0!==o&&o(t)}}o.d(t,{createSafeMulticastEventHandler:()=>n})},438980:(e,t,o)=>{"use strict";o.d(t,{Measure:()=>i});var n=o(664332);function i(e){const{children:t,onResize:o}=e;return t((0,n.useResizeObserver)(o||(()=>{}),[null===o]))}},227570:(e,t,o)=>{"use strict";o.d(t,{useActiveDescendant:()=>l});var n=o(50959),i=o(718736);function l(e,t=[]){const[o,l]=(0,n.useState)(!1),a=(0,i.useFunctionalRefObject)(e);return(0,n.useLayoutEffect)((()=>{const e=a.current;if(null===e)return;const t=e=>{switch(e.type){case"active-descendant-focus":l(!0);break;case"active-descendant-blur":l(!1)}};return e.addEventListener("active-descendant-focus",t),e.addEventListener("active-descendant-blur",t),()=>{e.removeEventListener("active-descendant-focus",t),e.removeEventListener("active-descendant-blur",t)}}),t),[a,o]}},163694:(e,t,o)=>{"use strict";o.d(t,{DrawerContext:()=>a,DrawerManager:()=>l});var n=o(50959),i=o(285089);class l extends n.PureComponent{constructor(e){super(e),this._isBodyFixed=!1,this._addDrawer=e=>{this.setState((t=>({
stack:[...t.stack,e]})))},this._removeDrawer=e=>{this.setState((t=>({stack:t.stack.filter((t=>t!==e))})))},this.state={stack:[]}}componentDidUpdate(e,t){!t.stack.length&&this.state.stack.length&&((0,i.setFixedBodyState)(!0),this._isBodyFixed=!0),t.stack.length&&!this.state.stack.length&&this._isBodyFixed&&((0,i.setFixedBodyState)(!1),this._isBodyFixed=!1)}componentWillUnmount(){this.state.stack.length&&this._isBodyFixed&&(0,i.setFixedBodyState)(!1)}render(){return n.createElement(a.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.stack.length?this.state.stack[this.state.stack.length-1]:null}},this.props.children)}}const a=n.createContext(null)},759339:(e,t,o)=>{"use strict";o.d(t,{Drawer:()=>m});var n=o(50959),i=o(650151),l=o(497754),a=o(924910),s=o(8361),r=o(163694),c=o(28466),u=o(742554),d=o(536718);var h;function m(e){const{position:t="Bottom",onClose:o,children:u,reference:h,className:m,theme:p=d}=e,g=(0,i.ensureNotNull)((0,n.useContext)(r.DrawerContext)),[b]=(0,n.useState)((()=>(0,a.randomHash)())),f=(0,n.useRef)(null),T=(0,n.useContext)(c.CloseDelegateContext);return(0,n.useLayoutEffect)((()=>((0,i.ensureNotNull)(f.current).focus({preventScroll:!0}),T.subscribe(g,o),g.addDrawer(b),()=>{g.removeDrawer(b),T.unsubscribe(g,o)})),[]),n.createElement(s.Portal,null,n.createElement("div",{ref:h,className:l(d.wrap,d[`position${t}`])},b===g.currentDrawer&&n.createElement("div",{className:d.backdrop,onClick:o}),n.createElement(v,{className:l(p.drawer,d[`position${t}`],m),ref:f,"data-name":e["data-name"]},u)))}!function(e){e.Left="Left",e.Bottom="Bottom"}(h||(h={}));const v=(0,n.forwardRef)(((e,t)=>{const{className:o,...i}=e;return n.createElement(u.TouchScrollContainer,{className:l(d.drawer,o),tabIndex:-1,ref:t,...i})}))},598448:(e,t,o)=>{"use strict";o.d(t,{RemoveTitleType:()=>n,removeTitlesMap:()=>l});var n,i=o(609838);!function(e){e.Add="add",e.Remove="remove"}(n||(n={}));const l={[n.Add]:i.t(null,void 0,o(869207)),[n.Remove]:i.t(null,void 0,o(685106))}},577687:(e,t,o)=>{"use strict";o.d(t,{FavoriteButton:()=>h});var n=o(50959),i=o(497754),l=o.n(i),a=o(878112),s=o(598448),r=o(227570),c=o(239146),u=o(648010),d=o(622413);function h(e){const{className:t,isFilled:o,isActive:i,onClick:h,title:m,...v}=e,[p,g]=(0,r.useActiveDescendant)(null),b=m??(o?s.removeTitlesMap[s.RemoveTitleType.Remove]:s.removeTitlesMap[s.RemoveTitleType.Add]);return(0,n.useLayoutEffect)((()=>{const e=p.current;e instanceof HTMLElement&&b&&e.dispatchEvent(new CustomEvent("common-tooltip-update"))}),[b,p]),n.createElement(a.Icon,{...v,className:l()(d.favorite,"apply-common-tooltip",o&&d.checked,i&&d.active,g&&d.focused,t),onClick:h,icon:o?c:u,title:b,ariaLabel:b,ref:p})}},522224:(e,t,o)=>{"use strict";o.d(t,{hoverMouseEventFilter:()=>n.hoverMouseEventFilter,useAccurateHover:()=>n.useAccurateHover,useHover:()=>n.useHover});var n=o(975228)},901317:(e,t,o)=>{"use strict";o.d(t,{useWatchedValue:()=>i});var n=o(50959);const i=(e,t=[])=>{const[o,i]=(0,n.useState)(e.value());return(0,n.useEffect)((()=>{
const t=e=>i(e);return e.subscribe(t),()=>e.unsubscribe(t)}),[e,...t]),[o,t=>e.setValue(t)]}},614417:(e,t,o)=>{"use strict";o.d(t,{multilineLabelWithIconAndToolboxTheme:()=>a});var n=o(493173),i=o(509059),l=o(395214);const a=(0,n.mergeThemes)(i,l)},917850:(e,t,o)=>{"use strict";o.d(t,{PopupMenuSeparator:()=>r});var n,i=o(50959),l=o(497754),a=o.n(l),s=o(700238);function r(e){const{size:t="normal",className:o,ariaHidden:n=!1}=e;return i.createElement("div",{className:a()(s.separator,"small"===t&&s.small,"normal"===t&&s.normal,"large"===t&&s.large,o),role:"separator","aria-hidden":n})}!function(e){e.Small="small",e.Large="large",e.Normal="normal"}(n||(n={}))},742554:(e,t,o)=>{"use strict";o.d(t,{TouchScrollContainer:()=>c});var n=o(50959),i=o(259142),l=o(650151),a=o(601227);const s=CSS.supports("overscroll-behavior","none");let r=0;const c=(0,n.forwardRef)(((e,t)=>{const{children:o,...l}=e,c=(0,n.useRef)(null);return(0,n.useImperativeHandle)(t,(()=>c.current)),(0,n.useLayoutEffect)((()=>{if(a.CheckMobile.iOS())return r++,null!==c.current&&(s?1===r&&(document.body.style.overscrollBehavior="none"):(0,i.disableBodyScroll)(c.current,{allowTouchMove:u(c)})),()=>{r--,null!==c.current&&(s?0===r&&(document.body.style.overscrollBehavior=""):(0,i.enableBodyScroll)(c.current))}}),[]),n.createElement("div",{ref:c,...l},o)}));function u(e){return t=>{const o=(0,l.ensureNotNull)(e.current),n=document.activeElement;return!o.contains(t)||null!==n&&o.contains(n)&&n.contains(t)}}},189888:e=>{e.exports={button:"button-LkmyTVRc",active:"active-LkmyTVRc"}},677020:e=>{e.exports={dropdown:"dropdown-pbhJWNrt",buttonWrap:"buttonWrap-pbhJWNrt",control:"control-pbhJWNrt",arrow:"arrow-pbhJWNrt",arrowIcon:"arrowIcon-pbhJWNrt",isOpened:"isOpened-pbhJWNrt",hover:"hover-pbhJWNrt",isGrayed:"isGrayed-pbhJWNrt",accessible:"accessible-pbhJWNrt"}},155973:e=>{e.exports={title:"title-u3QJgF_p"}},903088:e=>{e.exports={container:"container-Wp9adlfh",mirror:"mirror-Wp9adlfh",background:"background-Wp9adlfh",arrow:"arrow-Wp9adlfh"}},211766:e=>{e.exports={item:"item-uxNfqe_g",label:"label-uxNfqe_g"}},563754:e=>{e.exports={drawingToolbar:"drawingToolbar-BfVZxb4b",isHidden:"isHidden-BfVZxb4b",inner:"inner-BfVZxb4b",group:"group-BfVZxb4b",lastGroup:"lastGroup-BfVZxb4b",fill:"fill-BfVZxb4b"}},555613:e=>{e.exports={toggleButton:"toggleButton-OhcB9eH7",collapsed:"collapsed-OhcB9eH7",background:"background-OhcB9eH7",arrow:"arrow-OhcB9eH7",accessible:"accessible-OhcB9eH7"}},474099:e=>{e.exports={item:"item-yfwdxbRo",hovered:"hovered-yfwdxbRo"}},989265:e=>{e.exports={desktopSize:"desktopSize-l1SzP6TV",smallSize:"smallSize-l1SzP6TV",tabs:"tabs-l1SzP6TV",categories:"categories-l1SzP6TV"}},133892:e=>{e.exports={sticker:"sticker-aZclaNCs"}},265171:e=>{e.exports={"tablet-small-breakpoint":"(max-width: 440px)",stickerRow:"stickerRow-KUOIljqV"}},559453:e=>{e.exports={wrapper:"wrapper-FNeSdxed"}},633430:e=>{e.exports={drawer:"drawer-PzCssz1z",menuBox:"menuBox-PzCssz1z"}},757503:e=>{e.exports={toolButtonMagnet:"toolButtonMagnet-wg76fIbD",
toolButtonMagnet__menuItem:"toolButtonMagnet__menuItem-wg76fIbD",toolButtonMagnet__hintPlaceholder:"toolButtonMagnet__hintPlaceholder-wg76fIbD"}},537620:e=>{e.exports={popupMenuItem:"popupMenuItem-KrnunDnk"}},473824:e=>{e.exports={sectionTitle:"sectionTitle-Srvnqigs",newBadge:"newBadge-Srvnqigs",label:"label-Srvnqigs"}},582374:e=>{e.exports={wrap:"wrap-Z4M3tWHb",scrollWrap:"scrollWrap-Z4M3tWHb",noScrollBar:"noScrollBar-Z4M3tWHb",content:"content-Z4M3tWHb",icon:"icon-Z4M3tWHb",scrollBot:"scrollBot-Z4M3tWHb",scrollTop:"scrollTop-Z4M3tWHb",isVisible:"isVisible-Z4M3tWHb",iconWrap:"iconWrap-Z4M3tWHb",fadeBot:"fadeBot-Z4M3tWHb",fadeTop:"fadeTop-Z4M3tWHb"}},229703:e=>{e.exports={iconContainer:"iconContainer-dmpvVypS"}},994567:(e,t,o)=>{"use strict";o.d(t,{focusFirstMenuItem:()=>u,handleAccessibleMenuFocus:()=>r,handleAccessibleMenuKeyDown:()=>c,queryMenuElements:()=>m});var n=o(442092),i=o(333086),l=o(180185),a=o(32556);const s=[37,39,38,40];function r(e,t){if(!e.target)return;const o=e.relatedTarget?.getAttribute("aria-activedescendant");if(e.relatedTarget!==t.current){const e=o&&document.getElementById(o);if(!e||e!==t.current)return}u(e.target)}function c(e){if(e.defaultPrevented)return;const t=(0,l.hashFromEvent)(e);if(!s.includes(t))return;const o=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const a=m(e.currentTarget).sort(n.navigationOrderComparator);if(0===a.length)return;const r=document.activeElement.closest('[data-role="menuitem"]')||document.activeElement.parentElement?.querySelector('[data-role="menuitem"]');if(!(r instanceof HTMLElement))return;const c=a.indexOf(r);if(-1===c)return;const u=v(r),p=u.indexOf(document.activeElement),g=-1!==p,b=e=>{o&&(0,i.becomeSecondaryElement)(o),(0,i.becomeMainElement)(e),e.focus()};switch((0,n.mapKeyCodeToDirection)(t)){case"inlinePrev":if(!u.length)return;e.preventDefault(),b(0===p?a[c]:g?d(u,p,-1):u[u.length-1]);break;case"inlineNext":if(!u.length)return;e.preventDefault(),p===u.length-1?b(a[c]):b(g?d(u,p,1):u[0]);break;case"blockPrev":{e.preventDefault();const t=d(a,c,-1);if(g){const e=h(t,p);b(e||t);break}b(t);break}case"blockNext":{e.preventDefault();const t=d(a,c,1);if(g){const e=h(t,p);b(e||t);break}b(t)}}}function u(e){const[t]=m(e);t&&((0,i.becomeMainElement)(t),t.focus())}function d(e,t,o){return e[(t+e.length+o)%e.length]}function h(e,t){const o=v(e);return o.length?o[(t+o.length)%o.length]:null}function m(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled]):not([aria-disabled])')).filter((0,a.createScopedVisibleElementFilter)(e))}function v(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled]):not([aria-disabled])")).filter((0,a.createScopedVisibleElementFilter)(e))}},840781:(e,t,o)=>{"use strict";o.d(t,{MenuFavoriteButton:()=>u});var n=o(50959),i=o(497754),l=o.n(i),a=o(865266),s=o(577687),r=o(598448),c=o(189888);function u(e){const{onClick:t,isFilled:o,isActive:i,...u}=e,[d,h]=(0,
a.useRovingTabindexElement)(null),m=o?r.removeTitlesMap[r.RemoveTitleType.Remove]:r.removeTitlesMap[r.RemoveTitleType.Add];return(0,n.useLayoutEffect)((()=>{const e=d.current;e instanceof HTMLElement&&e.dispatchEvent(new CustomEvent("common-tooltip-update"))}),[m,d]),n.createElement("button",{ref:d,tabIndex:h,onClick:t,className:l()(c.button,i&&c.active,"apply-common-tooltip"),type:"button","aria-label":m,"data-tooltip":m},n.createElement(s.FavoriteButton,{...u,isFilled:o,isActive:i,title:""}))}},722426:(e,t,o)=>{"use strict";o.d(t,{ToolWidgetMenuSummary:()=>a});var n=o(50959),i=o(497754),l=o(155973);function a(e){return n.createElement("div",{className:i(e.className,l.title)},e.children)}},167645:(e,t,o)=>{"use strict";o.d(t,{DEFAULT_VERTICAL_TOOLBAR_HIDER_THEME:()=>s,VerticalToolbarHider:()=>c});var n=o(50959),i=o(497754),l=o(409174),a=o(903088);const s=a,r="http://www.w3.org/2000/svg";function c(e){const{direction:t,theme:o=a}=e;return n.createElement("svg",{xmlns:r,width:"9",height:"27",viewBox:"0 0 9 27",className:i(o.container,"right"===t?o.mirror:null),onContextMenu:l.preventDefault},n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("path",{className:o.background,d:"M4.5.5a4 4 0 0 1 4 4v18a4 4 0 1 1-8 0v-18a4 4 0 0 1 4-4z"}),n.createElement("path",{className:o.arrow,d:"M5.5 10l-2 3.5 2 3.5"})))}},916076:(e,t,o)=>{"use strict";o.r(t),o.d(t,{DrawingToolbarRenderer:()=>fo});var n=o(50959),i=o(632227),l=o(650151),a=o(497754),s=o.n(a),r=o(972535),c=o(870122),u=o(440891),d=o(651407),h=o(559410),m=o(329452),v=o(601227),p=o(69111),g=o(16509);class b{constructor(e){this._drawingsAccess=e||{tools:[],type:"black"}}isToolEnabled(e){const t=this._findTool(e);return!(!t||!t.grayed)||("black"===this._drawingsAccess.type?!t:!!t)}isToolGrayed(e){const t=this._findTool(e);return Boolean(t&&t.grayed)}_findTool(e){return this._drawingsAccess.tools.find((t=>t.name===e))}}var f=o(488381),T=o(294905),w=o(341121),C=o(926032),_=o(370981),x=o(898828),E=o(865266);function k(e){const[t,o]=(0,E.useRovingTabindexElement)(null);return n.createElement(x.ToolButton,{...e,ref:t,tag:"button",tabIndex:o})}function F(e){const{id:t,action:o,isActive:i,isHidden:l,isTransparent:a,toolName:s}=e;return n.createElement(k,{id:t,icon:g.lineToolsInfo[s].icon,isActive:i,isHidden:l,isTransparent:a,onClick:o,tooltip:g.lineToolsInfo[s].localizedName,"data-name":s})}var S,y=o(609838),L=o(800417),M=o(901317),A=o(240534);!function(e){e.Icons="icons",e.Emojis="emojis",e.Stickers="stickers"}(S||(S={}));const D=c.getValue("ToolButtonIcons.LastCategory",S.Emojis),B=new A.WatchedValue(D);function N(){const[e,t]=(0,M.useWatchedValue)(B);return[e,(0,n.useCallback)((e=>{t(e),function(e){c.setValue("ToolButtonIcons.LastCategory",e)}(e)}),[t])]}var I=o(543454),W=o(559453);function R(e){return n.createElement("div",{className:W.wrapper},e.text)}var V=o(931907),H=o(151609),O=o(922976),P=o(670616),z=o(918042),j=o(44986),Z=o(683778),U=o(548748)
;const G=["0xF087","0xF088","0xF164","0xF165","0xF0A4","0xF0A5","0xF007","0xF0A6","0xF0A7","0xF118","0xF11A","0xF119","0xF183"],K=["0xF153","0xF154","0xF155","0xF156","0xF157","0xF158","0xF159","0xF195","0xF15A"],q=["0xF060","0xF061","0xF062","0xF063","0xF053","0xF054","0xF077","0xF078","0xF07D","0xF07E","0xF0A9","0xF0AA","0xF0AB","0xF0D9","0xF0DA","0xF0D7","0xF0D8","0xF102","0xF103","0xF104","0xF105","0xF106","0xF107","0xF137","0xF139","0xF13A","0xF112","0xF064","0xF148","0xF149","0xF177","0xF178","0xF175","0xF176","0xF01A","0xF01B","0xF065","0xF066"],J=["0xF11D","0xF11E","0xF024","0xF004","0xF005","0xF006","0xF046","0xF00C","0xF00D","0xF011","0xF012","0xF021","0xF01E","0xF192","0xF041","0xF14A","0xF055","0xF056","0xF057","0xF059","0xF058","0xF05A","0xF05B","0xF05C","0xF05D","0xF05E","0xF067","0xF068","0xF069","0xF06A","0xF071","0xF06E","0xF070","0xF075","0xF08A","0xF0A3","0xF0E5","0xF110","0xF111","0xF123","0xF124","0xF10C","0xF128","0xF129","0xF12A","0xF140","0xF113","0xF17C","0xF179"],Q=["0xF06C","0xF185","0xF186","0xF188","0xF0E7"],$=["0xF000","0xF002","0xF00E","0xF015","0xF017","0xF030","0xF013","0xF043","0xF06B","0xF072","0xF076","0xF080","0xF084","0xF040","0xF0A1","0xF0A2","0xF0D6","0xF0E3","0xF0EB","0xF0F3","0xF135","0xF13D","0xF2FE"],Y=[...G,...K,...q,...J,...Q,...$].map((e=>+e)),X=new Set(Y);const ee=[{title:y.t(null,{context:"emoji_group"},o(188906)),emojis:[],content:n.createElement(I.IconItem,{icon:j})},{title:y.t(null,{context:"emoji_group"},o(51853)),emojis:G,content:n.createElement(I.IconItem,{icon:Z})},{title:y.t(null,{context:"emoji_group"},o(633282)),emojis:J,content:n.createElement(I.IconItem,{icon:P})},{title:y.t(null,{context:"emoji_group"},o(231054)),emojis:Q,content:n.createElement(I.IconItem,{icon:U})},{title:y.t(null,{context:"emoji_group"},o(614143)),emojis:K,content:n.createElement(I.IconItem,{icon:O})},{title:y.t(null,{context:"emoji_group"},o(998355)),emojis:$,content:n.createElement(I.IconItem,{icon:z})},{title:y.t(null,{context:"emoji_group"},o(174245)),emojis:q,content:n.createElement(I.IconItem,{icon:H})}],te={[S.Icons]:V.drawingToolsIcons.heart,[S.Emojis]:V.drawingToolsIcons.smile,[S.Stickers]:V.drawingToolsIcons.sticker},oe=[{title:S.Emojis,content:n.createElement(R,{text:y.t(null,void 0,o(816290))})},{title:S.Stickers,content:n.createElement(R,{text:y.t(null,void 0,o(350428))})},{title:S.Icons,content:n.createElement(R,{text:y.t(null,void 0,o(573829))})}];var ne=o(878112),ie=o(930202),le=o(624216),ae=o(510618),se=o(111706),re=o(759339),ce=o(493173),ue=o(994567),de=o(214665);const he=o(677020),me=(0,n.forwardRef)(((e,t)=>{const{buttonActiveClass:o,buttonClass:i,buttonIcon:l,buttonTitle:s,buttonHotKey:c,dropdownTooltip:u,children:d,isActive:h,isGrayed:m,onClickWhenGrayed:v,checkable:p,isSmallTablet:g,theme:b=he,onClickButton:f,onArrowClick:T,openDropdownByClick:w,onMenuFocus:C=ue.handleAccessibleMenuFocus,onMenuKeyDown:_=ue.handleAccessibleMenuKeyDown,...k}=e,F=(0,ce.mergeThemes)(ae.DEFAULT_MENU_THEME,{menuBox:b.menuBox}),[S,y]=(0,n.useState)(!1),[L,M]=(0,n.useState)(!1),A=(0,
n.useRef)(null),D=(0,n.useRef)(null),B=(0,n.useRef)(null),N=(0,n.useRef)(0),I=(0,n.useRef)(0),[W,R]=(0,E.useRovingTabindexElement)(null),[V,H]=(0,E.useRovingTabindexElement)(null);return(0,n.useImperativeHandle)(t,(()=>({open:()=>y(!0)})),[]),n.createElement("div",{...k,className:a(b.dropdown,{[b.isGrayed]:m,[b.isActive]:h,[b.isOpened]:S}),onClick:m?v:void 0,onKeyDown:function(e){if(e.defaultPrevented||!(e.target instanceof Node))return;const t=(0,ie.hashFromEvent)(e);if(e.currentTarget.contains(e.target)||27!==t)return;e.preventDefault(),O(!1),L&&V?.current?.focus()},ref:A},n.createElement("div",{ref:D,className:b.control},n.createElement("div",{...function(){if(!m)return r.mobiletouch?p?{onTouchStart:j,onTouchEnd:U,onTouchMove:Z}:{onClick:z}:{onMouseDown:j,onMouseUp:G};return{}}(),className:a(b.buttonWrap,b.accessible)},n.createElement(x.ToolButton,{activeClass:o,className:a(i,b.button),icon:l,isActive:h,isGrayed:m,isTransparent:!p,ref:W,tag:"button",tabIndex:R,onClick:function(e){if(!(0,se.isKeyboardClick)(e))return;w?O(!0,!0):f?.()},tooltip:s,buttonHotKey:c,"data-tooltip-delay":1500,tooltipPosition:"vertical"})),!m&&!r.mobiletouch&&n.createElement("button",{className:a(b.arrow,u&&"apply-common-tooltip common-tooltip-vertical",b.accessible),onClick:function(e){T?.(),O(void 0,(0,se.isKeyboardClick)(e))},onKeyDown:function(e){if(e.defaultPrevented||!(e.target instanceof Node))return;const t=(0,ie.hashFromEvent)(e);if(e.currentTarget.contains(e.target))switch(t){case 39:if(S)return;e.preventDefault(),O(!0,!0);break;case 27:if(!S)return;e.preventDefault(),O(!1)}},type:"button",ref:V,tabIndex:H,"aria-pressed":h,"aria-label":u,"data-tooltip":u},n.createElement(ne.Icon,{className:b.arrowIcon,icon:de}))),!m&&(g?S&&n.createElement(re.Drawer,{className:b.drawer,onClose:P,position:"Bottom"},d):n.createElement(le.PopupMenu,{theme:F,doNotCloseOn:function(){if(null===A.current)return[];return[A.current]},isOpened:S,onClose:P,position:function(){if(!D||!D.current)return{x:0,y:0};const e=D.current.getBoundingClientRect();return{x:e.left+e.width+1,y:e.top-6}},onKeyDown:_,onFocus:e=>C(e,V),controller:B,onOpen:function(){B.current?.focus()},tabIndex:-1},d)));function O(e,t=!1){const o=void 0!==e?e:!S;y(o),M(!!o&&t)}function P(){O(!1)}function z(){f&&f(),O()}function j(){if(r.mobiletouch&&!p)!I.current&&f&&f();else{if(N.current)return clearTimeout(N.current),N.current=0,void O(!0);N.current=setTimeout((()=>{N.current=0,!I.current&&f&&f()}),175)}I.current=setTimeout((()=>{I.current=0,O(!0)}),300)}function Z(){clearTimeout(I.current),I.current=0,clearTimeout(N.current),N.current=0}function U(e){e.cancelable&&e.preventDefault(),G()}function G(){I.current&&(clearTimeout(I.current),I.current=0,S?O(!1):p||S||r.mobiletouch||!h&&!w?!N.current&&f&&f():O(!0))}}));var ve=o(499547),pe=o(65559),ge=o(891346),be=o(717866);class fe extends ge.CommonJsonStoreService{constructor(e,t,o,n,i=18){super(be.TVXWindowEvents,c,e,t,[]),this._onChangeDrawingState=()=>{const e=d[this._drawingType].value();this._promote(e)},this._sanitizer=o,this._drawingType=n,
this._maxRecentCount=i,d[this._drawingType].subscribe(this._onChangeDrawingState)}destroy(){d[this._drawingType].unsubscribe(this._onChangeDrawingState),super.destroy()}_deserialize(e){const t=this._sanitizer(e);return this._removeUnavailableRecents(e,t)}_removeUnavailableRecents(e,t){return Array.isArray(e)?e.length>this._maxRecentCount&&(t=e.slice(0,this._maxRecentCount)):t=[],t}_promote(e){let t=[...this.get()];const o=t.indexOf(e);-1!==o&&t.splice(o,1),t=[e,...t.slice(0,this._maxRecentCount-1)],this.set(t)}}const Te=new fe("RECENT_ICONS_CHANGED","linetoolicon.recenticons",(function(e){return e.filter((e=>X.has(e)))}),"iconTool");var we=o(229703);function Ce(e){const{fallback:t,...o}=e;return n.createElement(n.Suspense,{fallback:t??null},n.createElement(_e,{...o}))}const _e=n.lazy((async()=>{const{getSvgContentForCharCode:e}=await o.e(7987).then(o.bind(o,478754));return{default:t=>{const{charCode:o}=t,i=e(o)??void 0;return n.createElement(ne.Icon,{icon:i,className:we.iconContainer})}}}));var xe=o(474099);var Ee=o(611005);const ke=new fe("RECENT_EMOJIS_CHANGED","linetoolemoji.recents",Ee.removeUnavailableEmoji,"emojiTool");var Fe;!function(e){e.Elon="elon",e.Doge="doge",e.Dislike="dislike",e.Yolo="yolo",e.Whale="whale",e.Wagmi="wagmi",e.Tendies="tendies",e.Short="short",e.Rugged="rugged",e.Shill="shill",e.Rekt="rekt",e.Sell="sell",e.PaperHands="paper-hands",e.Og="og",e.Fud="fud",e.Gm="gm",e.Ngmi="ngmi",e.Moon="moon",e.Love="love",e.Lambo="lambo",e.Ethereum="ethereum",e.Look="look",e.DiamondHand="diamond-hand",e.Leap="leap",e.Like="like",e.Few="few",e.Bitcoin="bitcoin",e.BagHolder="bag-holder",e.BuyTheDip="buy-the-dip",e.Buy="buy",e.Hodl="hodl"}(Fe||(Fe={}));const Se=["elon","doge","dislike","yolo","whale","wagmi","tendies","short","rugged","shill","rekt","sell","paper-hands","og","fud","gm","ngmi","moon","love","lambo","ethereum","look","diamond-hand","leap","like","few","bitcoin","bag-holder","buy-the-dip","buy","hodl"];var ye=o(537603),Le=o(90624);const Me=new Set(Se);const Ae=[{title:y.t(null,{context:"emoji_group"},o(188906)),emojis:[],content:n.createElement(I.IconItem,{icon:ye})},{title:"TradingView",emojis:Se,content:n.createElement(I.IconItem,{icon:Le})}],De=new fe("RECENT_STICKERS_CHANGED","linetoolsticker.recents",(function(e){return e.filter((e=>Me.has(e)))}),"stickerTool",3);var Be=o(840976),Ne=o(168233),Ie=o(133892);var We,Re=o(723407),Ve=o(265171);!function(e){e.Icon="LineToolIcon",e.Emoji="LineToolEmoji",e.Sticker="LineToolSticker"}(We||(We={}));const He={[S.Icons]:{service:Te,toolName:"LineToolIcon",ItemComponent:function(e){const{emoji:t,className:o}=e;return n.createElement("div",{className:s()(xe.item,o)},n.createElement(Ce,{charCode:Number(t)}))},icons:ee,onEmojiSelect:e=>{d.iconTool.setValue(Number(e)),d.tool.setValue("LineToolIcon")}},[S.Emojis]:{service:ke,toolName:"LineToolEmoji",icons:(0,Ee.emojiGroups)(),onEmojiSelect:e=>{d.emojiTool.setValue(e),d.tool.setValue("LineToolEmoji")}},[S.Stickers]:{service:De,toolName:"LineToolSticker",ItemComponent:function(e){const{emoji:t}=e,{size:i}=(0,
Be.useEnsuredContext)(Ne.EmojiListContentContext),[l,a]=(0,n.useState)();return(0,n.useEffect)((()=>{o.e(5598).then(o.bind(o,443241)).then((({getSvgContentForSticker:e})=>{const o=e(t);o&&a(o)}))}),[]),n.createElement(ne.Icon,{className:Ie.sticker,icon:null!==l?l:void 0,style:{width:`${i}px`,height:`${i}px`}})},RowComponent:function(e){return n.createElement(Re.EmojisRow,{...e,className:Ve.stickerRow})},icons:Ae,onEmojiSelect:e=>{d.stickerTool.setValue(e),d.tool.setValue("LineToolSticker")},getEmojiSize:e=>e?78:112}};var Oe=o(989265);function Pe(e){const{isSmallTablet:t,maxHeight:o,activeTab:i,setActiveTab:l}=e,s=He[i],{service:r,ItemComponent:c,RowComponent:u,onEmojiSelect:d,getEmojiSize:h}=s,m=h&&h(t),[v,p]=(0,n.useState)(ze(s));return(0,n.useLayoutEffect)((()=>{const e={},t=()=>{const e=ze(s);p(e)};return t(),r.getOnChange().subscribe(e,t),()=>{r.getOnChange().unsubscribeAll(e)}}),[s]),n.createElement("div",{style:{maxHeight:o}},n.createElement(ve.EmojiList,{className:a(Oe.desktopSize,t&&Oe.smallSize),emojis:v,onSelect:function(e){d(e),(0,_.globalCloseMenu)()},ItemComponent:c,RowComponent:u,height:o,category:i,emojiSize:m}),n.createElement(pe.GroupTabs,{className:Oe.tabs,tabClassName:Oe.categories,tabs:oe,activeTab:i,onTabClick:function(e){l(e)}}))}function ze(e){const{icons:t,service:o}=e,n=[...t],i=o.get();return n[0].emojis=i.map((e=>String(e))),n.filter((e=>e.emojis.length))}var je=o(442092),Ze=o(180185),Ue=o(703845),Ge=o(633430);const Ke={icon:y.t(null,void 0,o(37913)),dropdownTooltip:y.t(null,void 0,o(573829))},qe=(0,ce.mergeThemes)(he,{menuBox:Ge.menuBox,drawer:Ge.drawer}),Je=parseInt(Ue["default-drawer-min-top-distance"]);function Qe(e){const{isGrayed:t,isSmallTablet:o}=e,i=(0,L.filterDataProps)(e),[l,a]=N(),[s]=(0,M.useWatchedValue)(d.tool),{toolName:r}=He[l];return n.createElement(me,{theme:qe,buttonIcon:te[l],buttonTitle:Ke.icon,dropdownTooltip:Ke.dropdownTooltip,isActive:s===r,isGrayed:t,isSmallTablet:o,onClickButton:function(){c()},onClickWhenGrayed:()=>(0,h.emit)("onGrayedObjectClicked",{type:"drawing",name:g.lineToolsInfo[r].localizedName}),onArrowClick:function(){c("menu")},openDropdownByClick:!0,onMenuFocus:$e,onMenuKeyDown:function(e){if(e.defaultPrevented)return;const t=(0,Ze.hashFromEvent)(e);9!==t&&t!==Ze.Modifiers.Shift+9||(0,je.updateTabIndexes)()},...i},n.createElement(Pe,{isSmallTablet:o,maxHeight:o?Math.min(679,window.innerHeight-Je):679,activeTab:l,setActiveTab:a}));function c(e){0}}function $e(e){if(!e.target)return;const t=e.currentTarget;e.target===t&&((0,je.updateTabIndexes)(),setTimeout((()=>{if(document.activeElement!==t)return;const[e]=(0,je.queryTabbableElements)(t).sort(je.navigationOrderComparator);e&&e.focus()})))}var Ye=o(792535);class Xe extends n.PureComponent{constructor(e){super(e),this._handleClick=()=>{this.props.saveDefaultOnChange&&(0,Ye.allowSavingDefaults)(!0);const e=!this.props.property.value();this.props.property.setValue(e),this.props.saveDefaultOnChange&&(0,Ye.allowSavingDefaults)(!1),this.props.onClick&&this.props.onClick(e)},this.state={
isActive:this.props.property.value()}}componentDidMount(){this.props.property.subscribe(this,this._onChange)}componentWillUnmount(){this.props.property.unsubscribe(this,this._onChange)}render(){const{toolName:e}=this.props,{isActive:t}=this.state,o=g.lineToolsInfo[e];return n.createElement(k,{icon:t&&o.iconActive?o.iconActive:o.icon,isActive:t,onClick:this._handleClick,tooltip:o.localizedName,buttonHotKey:o.hotKey,"data-name":e})}_onChange(e){this.setState({isActive:e.value()})}}class et extends n.PureComponent{constructor(e){super(e),this._handleClick=()=>{d.tool.setValue(this.props.toolName),this.props.onClick?.()},this._onChange=()=>{this.setState({isActive:d.tool.value()===this.props.toolName})},this.state={isActive:d.tool.value()===this.props.toolName}}componentDidMount(){d.tool.subscribe(this._onChange)}componentWillUnmount(){d.tool.unsubscribe(this._onChange)}render(){const{toolName:e}=this.props,{isActive:t}=this.state,o=g.lineToolsInfo[e];return n.createElement(k,{icon:g.lineToolsInfo[e].icon,isActive:t,isTransparent:!0,onClick:this._handleClick,tooltip:o.localizedName,buttonHotKey:o.hotKey,"data-name":e})}}class tt extends n.PureComponent{constructor(e){super(e),this._boundUndoModel=null,this._handleClick=()=>{const e=this._activeChartWidget();e.hasModel()&&e.model().zoomFromViewport()},this._syncUnzoomButton=()=>{const e=this._activeChartWidget();let t=!1;if(e.hasModel()){const o=e.model();this._boundUndoModel!==o&&(this._boundUndoModel&&this._boundUndoModel.zoomStack().onChange().unsubscribe(null,this._syncUnzoomButton),o.zoomStack().onChange().subscribe(null,this._syncUnzoomButton),this._boundUndoModel=o),t=!o.zoomStack().isEmpty()}else e.withModel(null,this._syncUnzoomButton);this.setState({isVisible:t})},this.state={isVisible:!1}}componentDidMount(){this.props.chartWidgetCollection.activeChartWidget.subscribe(this._syncUnzoomButton,{callWithLast:!0})}componentWillUnmount(){this.props.chartWidgetCollection.activeChartWidget.unsubscribe(this._syncUnzoomButton)}render(){return this.state.isVisible?n.createElement(F,{action:this._handleClick,isTransparent:!0,toolName:"zoom-out"}):n.createElement("div",null)}_activeChartWidget(){return this.props.chartWidgetCollection.activeChartWidget.value()}}var ot=o(917850),nt=o(909434),it=o(722426),lt=o(840781),at=o(46305),st=o(614417),rt=o(128492),ct=o(473824);function ut(e){return"name"in e}new Set(["LineToolTable"]);class dt extends n.PureComponent{constructor(e){super(e),this._onChangeDrawingState=()=>{const e=this._getActiveToolName();this.setState({current:e||this.state.current,isActive:!!e})},this._handleClickButton=()=>{this._trackClick();const{current:e}=this.state;!v.CheckMobile.any()&&e&&this._selectTool(e)},this._handleClickItem=e=>{this._selectTool(e)},this._handleGrayedClick=e=>{(0,h.emit)("onGrayedObjectClicked",{type:"drawing",name:g.lineToolsInfo[e].localizedName})},this._handleClickFavorite=e=>{this.state.favState&&this.state.favState[e]?nt.LinetoolsFavoritesStore.removeFavorite(e):nt.LinetoolsFavoritesStore.addFavorite(e)},this._onAddFavorite=e=>{
this.setState({favState:{...this.state.favState,[e]:!0}})},this._onRemoveFavorite=e=>{this.setState({favState:{...this.state.favState,[e]:!1}})},this._onSyncFavorites=()=>{this.setState({favState:this._composeFavState()})},this._handleArrowClick=()=>{this._trackClick("menu")},this._trackClick=e=>{const{trackLabel:t}=this.props};const t=this._getActiveToolName();this.state={current:t||this._firstNonGrayedTool(),favState:this._composeFavState(),isActive:!!t}}componentDidMount(){d.tool.subscribe(this._onChangeDrawingState),nt.LinetoolsFavoritesStore.favoriteAdded.subscribe(null,this._onAddFavorite),nt.LinetoolsFavoritesStore.favoriteRemoved.subscribe(null,this._onRemoveFavorite),nt.LinetoolsFavoritesStore.favoritesSynced.subscribe(null,this._onSyncFavorites)}componentWillUnmount(){d.tool.unsubscribe(this._onChangeDrawingState),nt.LinetoolsFavoritesStore.favoriteAdded.unsubscribe(null,this._onAddFavorite),nt.LinetoolsFavoritesStore.favoriteRemoved.unsubscribe(null,this._onRemoveFavorite),nt.LinetoolsFavoritesStore.favoritesSynced.unsubscribe(null,this._onSyncFavorites)}componentDidUpdate(e,t){e.lineTools!==this.props.lineTools&&this.setState({favState:this._composeFavState()})}render(){const{current:e,favState:t,isActive:o}=this.state;if(!e)return n.createElement(n.Fragment,null);const{favoriting:i,grayedTools:l,lineTools:a,dropdownTooltip:s,isSmallTablet:r}=this.props,c=this._showShortcuts(),u=g.lineToolsInfo[e],d=(0,L.filterDataProps)(this.props);return n.createElement("span",null,n.createElement(me,{buttonIcon:u.icon,buttonTitle:u.localizedName,buttonHotKey:u.hotKey,dropdownTooltip:s,isActive:o,onClickButton:this._handleClickButton,onArrowClick:this._handleArrowClick,isSmallTablet:r,...d},a.map(((a,s)=>{if("title"in a)return n.createElement(n.Fragment,{key:a.title??`separator${s}`},s>0?n.createElement(ot.PopupMenuSeparator,null):null,a.title&&n.createElement(it.ToolWidgetMenuSummary,{className:ct.sectionTitle},a.title));const{name:u}=a,d=g.lineToolsInfo[u]?.selectHotkey?.hash,h=g.lineToolsInfo[u],m=l[u];return n.createElement(at.AccessibleMenuItem,{key:u,"data-name":u,theme:r?st.multilineLabelWithIconAndToolboxTheme:void 0,dontClosePopup:m,forceShowShortcuts:c,shortcut:!r&&d?(0,Ze.humanReadableHash)(d):void 0,icon:h.icon,isActive:o&&e===u,appearAsDisabled:m,label:n.createElement("div",{className:ct.label},h.localizedName,!1),showToolboxOnFocus:!0,onClick:m?this._handleGrayedClick:this._handleClickItem,onClickArg:u,showToolboxOnHover:!t[u],toolbox:i&&!m?n.createElement(lt.MenuFavoriteButton,{isActive:o&&e===u,isFilled:t[u],onClick:()=>this._handleClickFavorite(u)}):void 0})}))))}_firstNonGrayedTool(){const{grayedTools:e,lineTools:t}=this.props;return t.find((t=>ut(t)&&!e[t.name]))?.name}_showShortcuts(){return this.props.lineTools.some((e=>"hotkeyHash"in e))}_getActiveToolName(){return this.props.lineTools.find((e=>ut(e)&&e.name===d.tool.value()))?.name}async _selectTool(e){await(0,rt.initLineTool)(e),d.tool.setValue(e)}_composeFavState(){const e={};return this.props.lineTools.forEach((t=>{
ut(t)&&(e[t.name]=nt.LinetoolsFavoritesStore.isFavorite(t.name))})),e}}var ht=o(32133),mt=o(192063),vt=o(211766);const pt=(0,ce.mergeThemes)(mt.DEFAULT_POPUP_MENU_ITEM_THEME,vt),gt=!1;class bt extends n.PureComponent{constructor(e){super(e),this._handleRemoveToolClick=()=>{r.mobiletouch||this._handleRemoveDrawings(),Tt()},this._handleRemoveDrawings=()=>{ft("remove drawing"),this.props.chartWidgetCollection.activeChartWidget.value().actions().paneRemoveAllDrawingTools.execute()},this._handleRemoveStudies=()=>{ft("remove indicator"),this.props.chartWidgetCollection.activeChartWidget.value().actions().paneRemoveAllStudies.execute()},this._handleRemoveAll=()=>{ft("remove all"),this.props.chartWidgetCollection.activeChartWidget.value().actions().paneRemoveAllStudiesDrawingTools.execute()},this._handleActiveChartWidgetChanged=e=>{this._activeChartWidget&&this._unsubscribeToModelChanges(this._activeChartWidget),e&&this._subscribeToModelChanges(e),this._activeChartWidget=e,this._handleCollectionChanged()},this._handleCollectionChanged=()=>{this.setState(this._getActualState())},this._getActualState=()=>{if(!this._activeChartWidget||!this._activeChartWidget.hasModel())return{removeAllDrawingsLabel:y.t(null,void 0,o(396374)),removeAllStudiesLabel:y.t(null,void 0,o(699984)),removeAllLabel:y.t(null,void 0,o(53981))};const e=this._activeChartWidget.actions();return{removeAllDrawingsLabel:e.paneRemoveAllDrawingTools.getState().label,removeAllStudiesLabel:e.paneRemoveAllStudies.getState().label,removeAllLabel:e.paneRemoveAllStudiesDrawingTools.getState().label}},this._activeChartWidget=this.props.chartWidgetCollection.activeChartWidget.value(),this.state=this._getActualState()}componentDidMount(){this.props.chartWidgetCollection.activeChartWidget.subscribe(this._handleActiveChartWidgetChanged,{callWithLast:!0})}componentWillUnmount(){this._activeChartWidget&&this._unsubscribeToModelChanges(this._activeChartWidget),this.props.chartWidgetCollection.activeChartWidget.unsubscribe(this._handleActiveChartWidgetChanged)}render(){const e=this.props.isSmallTablet?pt:void 0,{removeAllDrawingsLabel:t,removeAllStudiesLabel:i,removeAllLabel:l}=this.state;return n.createElement(me,{buttonIcon:g.lineToolsInfo[this.props.toolName].icon,buttonTitle:t,onClickButton:this._handleRemoveToolClick,dropdownTooltip:y.t(null,void 0,o(802671)),isSmallTablet:this.props.isSmallTablet,"data-name":this.props.toolName,onArrowClick:this._handleArrowClick,openDropdownByClick:gt},n.createElement(at.AccessibleMenuItem,{"data-name":"remove-drawing-tools",label:t,onClick:this._handleRemoveDrawings,theme:e}),n.createElement(at.AccessibleMenuItem,{"data-name":"remove-studies",label:i,onClick:this._handleRemoveStudies,theme:e}),n.createElement(at.AccessibleMenuItem,{"data-name":"remove-all",label:l,onClick:this._handleRemoveAll,theme:e}))}_handleArrowClick(){Tt("menu")}_subscribeToModelChanges(e){e.withModel(this,(()=>{this._handleCollectionChanged(),e.model().model().dataSourceCollectionChanged().subscribe(this,this._handleCollectionChanged)}))}
_unsubscribeToModelChanges(e){e.hasModel()&&e.model().model().dataSourceCollectionChanged().unsubscribe(this,this._handleCollectionChanged),e.modelCreated().unsubscribeAll(this)}}function ft(e){(0,ht.trackEvent)("GUI","Chart Left Toolbar",e)}function Tt(e){0}var wt=o(787382),Ct=o(812297),_t=o(126309);const xt=n.createContext({hideMode:"drawings",isActive:!1});function Et(e){const{hideMode:t,option:{label:o,dataName:i,getBoxedValue:l},isSmallTablet:a,onClick:s}=e,{hideMode:r,isActive:c}=(0,n.useContext)(xt),u=l?.();return"all"===t||u?n.createElement(at.AccessibleMenuItem,{label:o,isActive:r===t&&c,onClick:function(){s(t,(0,Ct.toggleHideMode)(t))},"data-name":i,theme:a?pt:void 0}):n.createElement(n.Fragment,null)}const kt={drawings:{active:V.drawingToolsIcons.hideAllDrawingToolsActive,inactive:V.drawingToolsIcons.hideAllDrawingTools},indicators:{active:V.drawingToolsIcons.hideAllIndicatorsActive,inactive:V.drawingToolsIcons.hideAllIndicators},positions:{active:V.drawingToolsIcons.hideAllPositionsToolsActive,inactive:V.drawingToolsIcons.hideAllPositionsTools},all:{active:V.drawingToolsIcons.hideAllDrawingsActive,inactive:V.drawingToolsIcons.hideAllDrawings}};function Ft(e){const{isSmallTablet:t}=e,[{isActive:i,hideMode:a},s]=(0,n.useState)((()=>({isActive:!1,hideMode:(0,Ct.getSavedHideMode)()})));(0,n.useEffect)((()=>(_t.hideStateChange.subscribe(null,s),()=>{_t.hideStateChange.unsubscribe(null,s)})),[]);const r=g.lineToolsInfo.hideAllDrawings,{trackLabel:c,tooltip:u,dataName:d}=(0,l.ensureDefined)((0,Ct.getHideOptions)().get(a)),h=kt[a][i?"active":"inactive"],m=i?u.active:u.inactive;return n.createElement(me,{buttonIcon:h,buttonTitle:m,buttonHotKey:r.hotKey,dropdownTooltip:wt.t(null,void 0,o(595343)),onClickButton:function(){(0,Ct.toggleHideMode)(a),St(c,!i),yt(i?"on":"off")},isSmallTablet:t,isActive:i,checkable:!0,"data-name":"hide-all","data-type":d,onArrowClick:function(){yt("menu")}},n.createElement(xt.Provider,{value:{isActive:i,hideMode:a}},Array.from((0,Ct.getHideOptions)()).map((([e,o])=>n.createElement(Et,{key:e,hideMode:e,option:o,isSmallTablet:t,onClick:v})))));function v(e,t){St((0,l.ensureDefined)((0,Ct.getHideOptions)().get(e)).trackLabel,t)}}function St(e,t){(0,ht.trackEvent)("GUI","Chart Left Toolbar",`${e} ${t?"on":"off"}`)}function yt(e){0}var Lt=o(793483),Mt=o(853573);const At=y.t(null,void 0,o(351465));class Dt extends n.PureComponent{constructor(){super(...arguments),this._instance=null,this._promise=null,this._bindedForceUpdate=()=>this.forceUpdate(),this._handleClick=()=>{null!==this._instance&&(this._instance.isVisible()?(this._instance.hideAndSaveSettingsValue(),this._trackClick(!1)):(this._instance.showAndSaveSettingsValue(),this._trackClick(!0)))}}componentDidMount(){const e=this._promise=(0,l.ensureNotNull)((0,Lt.getFavoriteDrawingToolbarPromise)());e.then((t=>{this._promise===e&&(this._instance=t,this._instance.canBeShown().subscribe(this._bindedForceUpdate),this._instance.visibility().subscribe(this._bindedForceUpdate),this.forceUpdate())}))}componentWillUnmount(){this._promise=null,
null!==this._instance&&(this._instance.canBeShown().unsubscribe(this._bindedForceUpdate),this._instance.visibility().unsubscribe(this._bindedForceUpdate),this._instance=null)}render(){return null!==this._instance&&this._instance.canBeShown().value()?n.createElement(k,{id:this.props.id,icon:Mt,isActive:this._instance.isVisible(),onClick:this._handleClick,tooltip:At}):null}_trackClick(e){0}}var Bt=o(297265),Nt=o(719680),It=o(405017),Wt=o(757503);const Rt={[Nt.MagnetMode.WeakMagnet]:{id:Nt.MagnetMode.WeakMagnet,name:"weakMagnet",icon:V.drawingToolsIcons.magnet,localizedName:y.t(null,void 0,o(3519))},[Nt.MagnetMode.StrongMagnet]:{id:Nt.MagnetMode.StrongMagnet,name:"strongMagnet",icon:V.drawingToolsIcons.strongMagnet,localizedName:y.t(null,void 0,o(494593))}};function Vt(e){const{isSmallTablet:t}=e,i=(0,Bt.useWatchedValueReadonly)({watchedValue:(0,It.magnetEnabled)()}),l=(0,Bt.useWatchedValueReadonly)({watchedValue:(0,It.magnetMode)()});return n.createElement("div",{className:Wt.toolButtonMagnet},n.createElement(me,{"data-name":"magnet-button",buttonIcon:Rt[l].icon,buttonTitle:g.lineToolsInfo.magnet.localizedName,dropdownTooltip:y.t(null,void 0,o(141964)),isActive:i,onClickButton:function(){const e=!i;(0,ht.trackEvent)("GUI","Chart Left Toolbar","magnet mode "+(e?"on":"off")),!1;(0,It.setIsMagnetEnabled)(e)},buttonHotKey:g.lineToolsInfo.magnet.hotKey,checkable:!0,isSmallTablet:t,onArrowClick:function(){0}},Object.values(Rt).map((({id:e,name:o,localizedName:s,icon:r})=>n.createElement(at.AccessibleMenuItem,{key:e,className:t?Wt.toolButtonMagnet__menuItem:void 0,"data-name":o,icon:r,isActive:i&&l===e,label:s,onClick:a,onClickArg:e})))),!1);function a(e){void 0!==e&&((0,ht.trackEvent)("GUI","Magnet mode",e===Nt.MagnetMode.WeakMagnet?"Weak":"Strong"),(0,It.setMagnetMode)(e))}}var Ht;!function(e){e.Screenshot="drawing-toolbar-screenshot",e.FavoriteDrawings="drawing-toolbar-favorite-drawings",e.ObjectTree="drawing-toolbar-object-tree"}(Ht||(Ht={}));var Ot=o(522224),Pt=o(409174),zt=o(474021),jt=o(28466),Zt=o(602069),Ut=(o(175855),o(439235)),Gt=o(53806);o(537620);y.t(null,void 0,o(893027)),y.t(null,void 0,o(859377)),y.t(null,void 0,o(403521)),y.t(null,void 0,o(417415));var Kt=o(571956);class qt extends n.PureComponent{constructor(e){super(e),this._syncVisibleState=()=>{this.setState({isVisible:this._isMultipleLayout()})},this.state={isVisible:this._isMultipleLayout()}}componentDidMount(){this.props.layout.subscribe(this._syncVisibleState)}componentWillUnmount(){this.props.layout.unsubscribe(this._syncVisibleState)}render(){return this.state.isVisible?this.props.children:n.createElement("div",null)}_isMultipleLayout(){return(0,Kt.isMultipleLayout)(this.props.layout.value())}}var Jt=o(167645),Qt=o(718736),$t=o(74336),Yt=o(555613);const Xt=(0,ce.mergeThemes)(Jt.DEFAULT_VERTICAL_TOOLBAR_HIDER_THEME,Yt),eo={hide:y.t(null,void 0,o(799838)),show:y.t(null,void 0,o(932579))},to=(0,n.forwardRef)(((e,t)=>{const{toolbarVisible:o,"data-name":i}=e,l=(0,Qt.useFunctionalRefObject)(t);return n.createElement("button",{
"data-tooltip-show-on-focus":"true",...$t.MouseClickAutoBlurHandler.attributes(),ref:l,type:"button","aria-label":o?eo.hide:eo.show,"data-tooltip":o?eo.hide:eo.show,className:a(Xt.toggleButton,"apply-common-tooltip common-tooltip-vertical",!o&&Xt.collapsed,Xt.accessible),onClick:function(){T.isDrawingToolbarVisible.setValue(!T.isDrawingToolbarVisible.value())},"data-name":i,"data-value":o?"visible":"collapsed"},n.createElement(Jt.VerticalToolbarHider,{direction:o?"left":"right",theme:o?void 0:Xt}))}));var oo=o(163694),no=o(996038),io=o(930052);const lo={chartWidgetCollection:o(719036).any.isRequired};var ao=o(244693),so=o(563754);const ro=u.enabled("right_toolbar"),co=u.enabled("keep_object_tree_widget_in_right_toolbar"),uo=u.enabled("saveload_separate_drawings_storage"),ho=(0,v.onWidget)(),mo=new m.Delegate,vo=ht.trackEvent.bind(null,"GUI","Chart Left Toolbar"),po=(e,t)=>vo(`${e} ${t?"on":"off"}`);class go extends n.PureComponent{constructor(e){super(e),this._grayedTools={},this._handleMeasureClick=()=>{bo("measure")},this._handleZoomInClick=()=>{bo("zoom in")},this._handleDrawingClick=e=>{po("drawing mode",e),bo("drawing mode",e?"on":"off")},this._handleLockClick=e=>{po("lock all drawing",e),bo("lock",e?"on":"off")},this._handleSyncClick=e=>{po("sync",e),bo("sync",e?"on":"off")},this._handleObjectsTreeClick=()=>{this._activeChartWidget().showObjectsTreeDialog(),bo("object tree")},this._handleMouseOver=e=>{(0,Ot.hoverMouseEventFilter)(e)&&this.setState({isHovered:!0})},this._handleMouseOut=e=>{(0,Ot.hoverMouseEventFilter)(e)&&this.setState({isHovered:!1})},this._handleChangeVisibility=e=>{this.setState({isVisible:e})},this._handleEsc=()=>{d.resetToCursor(!0)},this._handleWidgetbarSettled=e=>{this.setState({isWidgetbarVisible:Boolean(window.widgetbar?.visible().value()),widgetbarSettled:e})},this._handleWidgetbarVisible=e=>{this.setState({isWidgetbarVisible:e})},d.init(),this._toolsFilter=new b(this.props.drawingsAccess),this._filteredLineTools=f.lineTools.reduce(((e,t)=>{const{id:o,title:n,trackLabel:i}=t,l=e=>this._toolsFilter.isToolEnabled(g.lineToolsInfo[e.name].localizedName),a=[];return(0,f.isLineToolsGroupWithSections)(t)?t.sections.forEach((e=>{const t=e.items.filter(l);t.length&&a.push({title:e.title},...t)})):a.push(...t.items.filter(l)),a.length&&e.push({id:o,title:n,trackLabel:i,items:a}),e}),[]),this._filteredLineTools.forEach((e=>{e.items.forEach((e=>{"name"in e&&(this._grayedTools[e.name]=this._toolsFilter.isToolGrayed(g.lineToolsInfo[e.name].localizedName))}))})),this.state={isHovered:!1,isVisible:T.isDrawingToolbarVisible.value(),isWidgetbarVisible:Boolean(window.widgetbar?.visible().value()),widgetbarSettled:void 0!==window.widgetbar},this._features={favoriting:!this.props.readOnly&&!ho&&u.enabled("items_favoriting"),multicharts:u.enabled("support_multicharts"),tools:!ho||!0},this._registry={chartWidgetCollection:this.props.chartWidgetCollection},this._negotiateResizer()}componentDidMount(){T.isDrawingToolbarVisible.subscribe(this._handleChangeVisibility),
_.globalCloseDelegate.subscribe(this,this._handleGlobalClose),this._tool=d.tool.spawn(),this._tool.subscribe(this._updateHotkeys.bind(this)),this._initHotkeys(),this.props.widgetbarSettled&&(this.props.widgetbarSettled.subscribe(this,this._handleWidgetbarSettled),v.CheckMobile.any()&&window.widgetbar?.visible().subscribe(this._handleWidgetbarVisible))}componentWillUnmount(){window.widgetbar?.visible().unsubscribe(this._handleWidgetbarVisible),T.isDrawingToolbarVisible.unsubscribe(this._handleChangeVisibility),_.globalCloseDelegate.unsubscribe(this,this._handleGlobalClose),this._tool.destroy(),this._hotkeys.destroy()}componentDidUpdate(e,t){const{isVisible:o,widgetbarSettled:n}=this.state;o!==t.isVisible&&(h.emit("toggle_sidebar",!o),c.setValue("ChartDrawingToolbarWidget.visible",o),this._negotiateResizer()),t.widgetbarSettled!==n&&n&&v.CheckMobile.any()&&window.widgetbar?.visible().subscribe(this._handleWidgetbarVisible)}render(){const{bgColor:e,chartWidgetCollection:t,readOnly:o}=this.props,{isHovered:i,isVisible:l}=this.state,s={backgroundColor:e&&`#${e}`};let c;c=n.createElement(to,{toolbarVisible:l,"data-name":"toolbar-drawing-toggle-button"});const h=()=>!!this._features.tools&&(!(!u.enabled("show_object_tree")||co&&!ro)&&(!u.enabled("right_toolbar")||(!this.state.isWidgetbarVisible&&v.CheckMobile.any()||(0,p.isOnMobileAppPage)("new"))));return n.createElement(ao.RegistryProvider,{validation:lo,value:this._registry},n.createElement(jt.CloseDelegateContext.Provider,{value:mo},n.createElement(oo.DrawerManager,null,n.createElement(io.MatchMedia,{rule:no.DialogBreakpoints.TabletSmall},(e=>n.createElement(Zt.Toolbar,{id:"drawing-toolbar",className:a(so.drawingToolbar,{[so.isHidden]:!l}),style:s,onClick:this.props.onClick,onContextMenu:Pt.preventDefaultForContextMenu,orientation:"vertical"},n.createElement(w.VerticalScroll,{onScroll:this._handleGlobalClose,isVisibleFade:r.mobiletouch,isVisibleButtons:!r.mobiletouch&&i,isVisibleScrollbar:!1,onMouseOver:this._handleMouseOver,onMouseOut:this._handleMouseOut},n.createElement("div",{className:so.inner},!o&&n.createElement("div",{className:so.group,style:s},this._filteredLineTools.map((o=>n.createElement(dt,{key:o.id,"data-name":o.id,chartWidgetCollection:t,favoriting:this._features.favoriting&&!("linetool-group-cursors"===o.id&&(0,p.isOnMobileAppPage)("any")),grayedTools:this._grayedTools,dropdownTooltip:o.title,lineTools:o.items,isSmallTablet:e,trackLabel:o.trackLabel}))),this._toolsFilter.isToolEnabled("Font Icons")&&n.createElement(Qe,{"data-name":"linetool-group-font-icons",isGrayed:this._grayedTools["Font Icons"],isSmallTablet:e})),!o&&n.createElement("div",{className:so.group,style:s},n.createElement(et,{toolName:"measure",onClick:this._handleMeasureClick}),n.createElement(et,{toolName:"zoom",onClick:this._handleZoomInClick}),n.createElement(tt,{chartWidgetCollection:t})),!o&&n.createElement("div",{className:so.group,style:s},n.createElement(Vt,{isSmallTablet:e}),this._features.tools&&n.createElement(Xe,{property:d.properties().childs().stayInDrawingMode,
saveDefaultOnChange:!0,toolName:"drawginmode",onClick:this._handleDrawingClick}),this._features.tools&&n.createElement(Xe,{property:d.lockDrawings(),toolName:"lockAllDrawings",onClick:this._handleLockClick}),this._features.tools&&n.createElement(Ft,{isSmallTablet:e}),this._features.tools&&this._features.multicharts&&n.createElement(n.Fragment,null,uo?n.createElement(Xe,{property:d.drawOnAllCharts(),saveDefaultOnChange:!0,toolName:"SyncDrawing",onClick:this._handleSyncClick}):n.createElement(qt,{layout:t.layout},n.createElement(Xe,{property:d.drawOnAllCharts(),saveDefaultOnChange:!0,toolName:"SyncDrawing",onClick:this._handleSyncClick})))),!o&&this._features.tools&&n.createElement("div",{className:so.group,style:s},n.createElement(bt,{chartWidgetCollection:t,isSmallTablet:e,toolName:"removeAllDrawingTools"})),n.createElement("div",{className:so.fill,style:s}),!o&&(this._features.tools||!1)&&n.createElement("div",{className:a(so.group,so.lastGroup),style:s},!1,this._features.tools&&this._features.favoriting&&n.createElement(Dt,{id:Ht.FavoriteDrawings}),h()&&n.createElement(F,{id:Ht.ObjectTree,action:this._handleObjectsTreeClick,toolName:"showObjectsTree"}))))))),c)))}_activeChartWidget(){return this.props.chartWidgetCollection.activeChartWidget.value()}_negotiateResizer(){const e=zt.TOOLBAR_WIDTH_COLLAPSED;this.props.resizerBridge.negotiateWidth(this.state.isVisible?zt.TOOLBAR_WIDTH_EXPANDED:e)}_handleGlobalClose(){mo.fire()}_updateHotkeys(){this._hotkeys.promote()}_initHotkeys(){this._hotkeys=C.createGroup({desc:"Drawing Toolbar"}),this._hotkeys.add({desc:"Reset",hotkey:27,handler:()=>this._handleEsc(),isDisabled:()=>d.toolIsCursor(d.tool.value())})}}function bo(e,t){0}class fo{constructor(e,t){this._component=null,this._handleRef=e=>{this._component=e},this._container=e,i.render(n.createElement(go,{...t,ref:this._handleRef}),this._container)}destroy(){i.unmountComponentAtNode(this._container)}getComponent(){return(0,l.ensureNotNull)(this._component)}}},488381:(e,t,o)=>{"use strict";o.d(t,{isLineToolsGroupWithSections:()=>r,lineTools:()=>s,lineToolsFlat:()=>c});var n=o(609838),i=(o(601227),o(440891)),l=o(41899);const a=i.enabled("image_drawingtool"),s=[{id:"linetool-group-cursors",title:n.t(null,void 0,o(681578)),sections:[{items:[{name:"cursor"},{name:"dot"},{name:"arrow"},{name:"demonstration"},null].filter(l.isExistent)},{items:[{name:"eraser"}]}],trackLabel:null},{id:"linetool-group-trend-line",title:n.t(null,void 0,o(948773)),sections:[{title:n.t(null,void 0,o(156982)),items:[{name:"LineToolTrendLine"},{name:"LineToolRay"},{name:"LineToolInfoLine"},{name:"LineToolExtended"},{name:"LineToolTrendAngle"},{name:"LineToolHorzLine"},{name:"LineToolHorzRay"},{name:"LineToolVertLine"},{name:"LineToolCrossLine"}]},{title:n.t(null,void 0,o(159934)),items:[{name:"LineToolParallelChannel"},{name:"LineToolRegressionTrend"},{name:"LineToolFlatBottom"},{name:"LineToolDisjointAngle"}]},{title:n.t(null,void 0,o(536167)),items:[{name:"LineToolPitchfork"},{name:"LineToolSchiffPitchfork2"},{name:"LineToolSchiffPitchfork"},{
name:"LineToolInsidePitchfork"}]}],trackLabel:null},{id:"linetool-group-gann-and-fibonacci",title:n.t(null,void 0,o(602654)),sections:[{title:n.t(null,void 0,o(26578)),items:[{name:"LineToolFibRetracement"},{name:"LineToolTrendBasedFibExtension"},{name:"LineToolFibChannel"},{name:"LineToolFibTimeZone"},{name:"LineToolFibSpeedResistanceFan"},{name:"LineToolTrendBasedFibTime"},{name:"LineToolFibCircles"},{name:"LineToolFibSpiral"},{name:"LineToolFibSpeedResistanceArcs"},{name:"LineToolFibWedge"},{name:"LineToolPitchfan"}]},{title:n.t(null,void 0,o(451494)),items:[{name:"LineToolGannSquare"},{name:"LineToolGannFixed"},{name:"LineToolGannComplex"},{name:"LineToolGannFan"}]}],trackLabel:null},{id:"linetool-group-patterns",title:n.t(null,void 0,o(846417)),sections:[{title:n.t(null,void 0,o(846417)),items:[{name:"LineTool5PointsPattern"},{name:"LineToolCypherPattern"},{name:"LineToolHeadAndShoulders"},{name:"LineToolABCD"},{name:"LineToolTrianglePattern"},{name:"LineToolThreeDrivers"}]},{title:n.t(null,void 0,o(44255)),items:[{name:"LineToolElliottImpulse"},{name:"LineToolElliottCorrection"},{name:"LineToolElliottTriangle"},{name:"LineToolElliottDoubleCombo"},{name:"LineToolElliottTripleCombo"}]},{title:n.t(null,void 0,o(177915)),items:[{name:"LineToolCircleLines"},{name:"LineToolTimeCycles"},{name:"LineToolSineLine"}]}],trackLabel:null},{id:"linetool-group-prediction-and-measurement",title:n.t(null,void 0,o(501410)),sections:[{title:n.t(null,void 0,o(375747)),items:[{name:"LineToolRiskRewardLong"},{name:"LineToolRiskRewardShort"},{name:"LineToolPrediction"},{name:"LineToolBarsPattern"},{name:"LineToolGhostFeed"},{name:"LineToolProjection"}].filter(l.isExistent)},{title:n.t(null,void 0,o(669260)),items:[{name:"LineToolAnchoredVWAP"},{name:"LineToolFixedRangeVolumeProfile"},null].filter(l.isExistent)},{title:n.t(null,void 0,o(897050)),items:[{name:"LineToolPriceRange"},{name:"LineToolDateRange"},{name:"LineToolDateAndPriceRange"}]}],trackLabel:null},{id:"linetool-group-geometric-shapes",title:n.t(null,void 0,o(22145)),sections:[{title:n.t(null,void 0,o(565695)),items:[{name:"LineToolBrush"},{name:"LineToolHighlighter"}]},{title:n.t(null,void 0,o(819147)),items:[{name:"LineToolArrowMarker"},{name:"LineToolArrow"},{name:"LineToolArrowMarkUp"},{name:"LineToolArrowMarkDown"},{name:"LineToolArrowMarkLeft"},{name:"LineToolArrowMarkRight"}].filter(l.isExistent)},{title:n.t(null,void 0,o(65781)),items:[{name:"LineToolRectangle"},{name:"LineToolRotatedRectangle"},{name:"LineToolPath"},{name:"LineToolCircle"},{name:"LineToolEllipse"},{name:"LineToolPolyline"},{name:"LineToolTriangle"},{name:"LineToolArc"},{name:"LineToolBezierQuadro"},{name:"LineToolBezierCubic"}]}],trackLabel:null},{id:"linetool-group-annotation",title:n.t(null,void 0,o(432064)),sections:[{title:n.t(null,void 0,o(565831)),items:[{name:"LineToolText"},{name:"LineToolTextAbsolute"},{name:"LineToolTextNote"},{name:"LineToolPriceNote"},{name:"LineToolNote"},{name:"LineToolTable"},{name:"LineToolCallout"},{name:"LineToolComment"},{name:"LineToolPriceLabel"},{
name:"LineToolSignpost"},{name:"LineToolFlagMark"}].filter(l.isExistent)},{title:n.t(null,void 0,o(93111)),items:[a?{name:"LineToolImage"}:null,null,null].filter(l.isExistent)}],trackLabel:null}];function r(e){return"sections"in e}const c=s.map((function(e){return r(e)?e.sections.map((e=>e.items)).flat():e.items})).flat()},341121:(e,t,o)=>{"use strict";o.d(t,{VerticalScroll:()=>h});var n=o(50959),i=o(497754),l=o.n(i),a=o(878112),s=o(351329),r=o(176616),c=o(438980),u=o(582374),d=o(661380);class h extends n.PureComponent{constructor(e){super(e),this._scroll=null,this._handleScrollTop=()=>{this.animateTo(Math.max(0,this.currentPosition()-(this.state.heightWrap-50)))},this._handleScrollBot=()=>{this.animateTo(Math.min((this.state.heightContent||0)-(this.state.heightWrap||0),this.currentPosition()+(this.state.heightWrap-50)))},this._handleResizeWrap=([e])=>{this.setState({heightWrap:e.contentRect.height})},this._handleResizeContent=([e])=>{this.setState({heightContent:e.contentRect.height})},this._handleScroll=()=>{const{onScroll:e}=this.props;e&&e(this.currentPosition(),this.isAtTop(),this.isAtBot()),this._checkButtonsVisibility()},this._checkButtonsVisibility=()=>{const{isVisibleTopButton:e,isVisibleBotButton:t}=this.state,o=this.isAtTop(),n=this.isAtBot();o||e?o&&e&&this.setState({isVisibleTopButton:!1}):this.setState({isVisibleTopButton:!0}),n||t?n&&t&&this.setState({isVisibleBotButton:!1}):this.setState({isVisibleBotButton:!0})},this.state={heightContent:0,heightWrap:0,isVisibleBotButton:!1,isVisibleTopButton:!1}}componentDidMount(){this._checkButtonsVisibility()}componentDidUpdate(e,t){t.heightWrap===this.state.heightWrap&&t.heightContent===this.state.heightContent||this._handleScroll()}currentPosition(){return this._scroll?this._scroll.scrollTop:0}isAtTop(){return this.currentPosition()<=1}isAtBot(){return this.currentPosition()+this.state.heightWrap>=this.state.heightContent-1}animateTo(e,t=r.dur){const o=this._scroll;o&&(0,s.doAnimate)({onStep(e,t){o.scrollTop=t},from:o.scrollTop,to:Math.round(e),easing:r.easingFunc.easeInOutCubic,duration:t})}render(){const{children:e,isVisibleScrollbar:t,isVisibleFade:o,isVisibleButtons:i,onMouseOver:s,onMouseOut:r}=this.props,{heightContent:h,heightWrap:m,isVisibleBotButton:v,isVisibleTopButton:p}=this.state;return n.createElement(c.Measure,{onResize:this._handleResizeWrap},(g=>n.createElement("div",{className:u.wrap,onMouseOver:s,onMouseOut:r,ref:g},n.createElement("div",{className:l()(u.scrollWrap,{[u.noScrollBar]:!t}),onScroll:this._handleScroll,ref:e=>this._scroll=e},n.createElement(c.Measure,{onResize:this._handleResizeContent},(t=>n.createElement("div",{className:u.content,ref:t},e)))),o&&n.createElement("div",{className:l()(u.fadeTop,{[u.isVisible]:p&&h>m})}),o&&n.createElement("div",{className:l()(u.fadeBot,{[u.isVisible]:v&&h>m})}),i&&n.createElement("div",{className:l()(u.scrollTop,{[u.isVisible]:p&&h>m}),onClick:this._handleScrollTop},n.createElement("div",{className:u.iconWrap},n.createElement(a.Icon,{icon:d,className:u.icon}))),i&&n.createElement("div",{
className:l()(u.scrollBot,{[u.isVisible]:v&&h>m}),onClick:this._handleScrollBot},n.createElement("div",{className:u.iconWrap},n.createElement(a.Icon,{icon:d,className:u.icon}))))))}}h.defaultProps={isVisibleScrollbar:!0}},244693:(e,t,o)=>{"use strict";o.d(t,{RegistryProvider:()=>r,registryContextType:()=>c,validateRegistry:()=>s});var n=o(50959),i=o(719036),l=o.n(i);const a=n.createContext({});function s(e,t){l().checkPropTypes(t,e,"context","RegistryContext")}function r(e){const{validation:t,value:o}=e;return s(o,t),n.createElement(a.Provider,{value:o},e.children)}function c(){return a}},175855:(e,t,o)=>{"use strict";o.d(t,{setDrawingSyncMode:()=>l,toggleDrawingSync:()=>a});var n=o(651407),i=o(792535);function l(e){const{drawOnAllCharts:t,drawOnAllChartsMode:o}=(0,n.properties)().childs();(0,i.allowSavingDefaults)(!0),o.setValue(e),t.setValue(!0),(0,i.allowSavingDefaults)(!1)}function a(){const{drawOnAllCharts:e}=(0,n.properties)().childs();(0,i.allowSavingDefaults)(!0),e.setValue(!e.value()),(0,i.allowSavingDefaults)(!1)}},661380:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 10" width="20" height="10"><path fill="none" stroke="currentColor" stroke-width="1.5" d="M2 1l8 8 8-8"/></svg>'},53806:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M12.65 5.1a9.07 9.07 0 0 1 2.7 0 12.44 12.44 0 0 1 2.13 4.61c-1.06.2-2.24.29-3.48.29a18.6 18.6 0 0 1-3.48-.3 12.44 12.44 0 0 1 2.13-4.6Zm-.53-.92a10 10 0 0 0 0 19.65 10.05 10.05 0 0 0 3.76 0 10 10 0 0 0 0-19.65 10.05 10.05 0 0 0-3.76 0Zm4.68 1.26a14.4 14.4 0 0 1 1.66 4.05 7.06 7.06 0 0 0 2.5-1.2 9.01 9.01 0 0 0-4.16-2.85Zm4.75 3.66a8.12 8.12 0 0 1-2.88 1.37 18.93 18.93 0 0 1 0 7.06c1.1.3 2.08.74 2.88 1.37a8.96 8.96 0 0 0 0-9.8Zm-.59 10.6a7.05 7.05 0 0 0-2.5-1.19 14.1 14.1 0 0 1-1.65 4.04 9.01 9.01 0 0 0 4.15-2.85Zm-5.61 3.2a12.27 12.27 0 0 0 2.14-4.61c-1.07-.2-2.25-.29-3.49-.29-1.25 0-2.42.09-3.49.3a12.27 12.27 0 0 0 2.14 4.6 9.06 9.06 0 0 0 2.7 0Zm-4.16-.35a14.1 14.1 0 0 1-1.65-4.03c-.96.27-1.8.66-2.5 1.19a9.01 9.01 0 0 0 4.15 2.84ZM6.45 18.9a8.57 8.57 0 0 1 2.87-1.35 19.19 19.19 0 0 1 .01-7.1A8.58 8.58 0 0 1 6.45 9.1a8.96 8.96 0 0 0 0 9.8Zm.6-10.6a9.01 9.01 0 0 1 4.15-2.86 14.38 14.38 0 0 0-1.65 4.04 7.54 7.54 0 0 1-2.5-1.19ZM10 14c0-1.15.1-2.27.3-3.32 1.17.23 2.42.32 3.7.32 1.27 0 2.53-.09 3.7-.3a17.93 17.93 0 0 1 0 6.61A20 20 0 0 0 14 17c-1.29 0-2.54.09-3.7.32A18.2 18.2 0 0 1 10 14Z"/></svg>'},439235:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M15.08 5.75a5.07 5.07 0 0 1 7.17 7.17l-3.23 3.23-.7-.7 3.22-3.24a4.07 4.07 0 1 0-5.75-5.75l-3.23 3.23-.7-.71 3.22-3.23Zm-9.33 9.33a5.07 5.07 0 1 0 7.17 7.17l3.23-3.23-.7-.7-3.24 3.22a4.07 4.07 0 1 1-5.75-5.75l3.23-3.23-.71-.7-3.23 3.22Zm10.77-4.3-5.74 5.74.7.7 5.75-5.74-.71-.7Z"/></svg>'},151609:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M4.31 14.5a1.07 1.07 0 0 1 0-1.5L13 4.3c.42-.41 1.1-.41 1.52 0l.99 1c.42.42.41 1.11-.02 1.53l-5.38 5.12h12.83c.6 0 1.07.48 1.07 1.07v1.43c0 .6-.48 1.07-1.07 1.07H10.1l5.38 5.13c.44.41.45 1.1.02 1.53l-1 .99c-.41.42-1.1.42-1.5 0L4.3 14.5Zm7.97 9.38-8.67-8.67c-.81-.8-.82-2.12 0-2.93l8.68-8.67c.8-.81 2.12-.82 2.92 0l1 .99c.82.82.8 2.16-.04 2.96l-3.57 3.4h10.33c1.14 0 2.07.93 2.07 2.07v1.43c0 1.15-.93 2.07-2.07 2.07H12.6l3.57 3.4c.84.8.86 2.14.03 2.97l-.99.99c-.8.8-2.12.8-2.93 0Z"/></svg>'},922976:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M4.87 4.52a.5.5 0 0 1 .61.35L6.91 10h5.47l1.03-4.67c.14-.63 1.04-.63 1.18 0L15.62 10h5.47l1.43-5.13a.5.5 0 0 1 .96.26L22.13 10H25a.5.5 0 0 1 0 1h-3.15l-.83 3H25a.5.5 0 0 1 0 1h-4.26l-2.15 7.75c-.17.6-1.03.58-1.16-.03L15.7 15h-3.42l-1.72 7.72c-.13.6-1 .63-1.16.03L7.26 15H3a.5.5 0 1 1 0-1h3.98l-.83-3H3a.5.5 0 1 1 0-1h2.87L4.52 5.13a.5.5 0 0 1 .35-.61ZM7.19 11l.83 3h3.47l.66-3H7.2Zm5.99 0-.67 3h2.98l-.67-3h-1.64Zm1.42-1L14 7.3l-.6 2.7h1.2Zm1.25 1 .66 3h3.47l.83-3h-4.96Zm3.85 4h-2.97l1.32 5.94L19.7 15Zm-8.43 0H8.3l1.65 5.94L11.27 15Z"/></svg>'},670616:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M7.5 24v-5.5m0 0s2.7-1.1 4.5-1c2.1.12 2.9 1.88 5 2 1.8.1 4.5-1 4.5-1v-6m-14 6v-6m0 0v-6s2.7-1.1 4.5-1c2.1.12 2.9 1.88 5 2 1.8.1 4.5-1 4.5-1v6m-14 0s2.7-1.1 4.5-1c2.1.12 2.9 1.88 5 2 1.8.1 4.5-1 4.5-1"/></svg>'},548748:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14.08 3.73c.1.16.1.37 0 .54a9.4 9.4 0 0 0 3.35 13.26 9.9 9.9 0 0 0 6.49 1.18.5.5 0 0 1 .5.76 10.67 10.67 0 0 1-3.83 3.64 10.91 10.91 0 0 1-14.28-3.3A10.44 10.44 0 0 1 8.69 5.56a10.86 10.86 0 0 1 4.9-2.06.5.5 0 0 1 .49.22Zm8.3 15.61v.5c-1.91 0-3.8-.5-5.45-1.44a10.64 10.64 0 0 1-3.95-3.97 10.4 10.4 0 0 1-.3-9.72 9.6 9.6 0 0 0-6.37 5.39 9.39 9.39 0 0 0 .83 9.14 9.7 9.7 0 0 0 3.6 3.17 9.92 9.92 0 0 0 12.21-2.59c-.19.02-.38.02-.57.02v-.5Z"/></svg>'},918042:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M6 11.69C6 7.46 9.56 4 14 4c4.44 0 8 3.46 8 7.69 0 2.63-1.2 4.93-3.25 6.31H14.5v-5H18v-1h-8v1h3.5v5H9.14A8.06 8.06 0 0 1 6 11.69Zm2 6.67a9.1 9.1 0 0 1-3-6.67C5 6.87 9.05 3 14 3s9 3.87 9 8.69a8.51 8.51 0 0 1-3 6.62V22h-2v3h-8v-3H8v-3.64ZM11 22v2h6v-2h-6Zm-2-1v-2h10v2H9Z"/></svg>'},44986:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M6 14.5C6 9.78 9.78 6 14.5 6c4.72 0 8.5 3.78 8.5 8.5 0 4.72-3.78 8.5-8.5 8.5A8.46 8.46 0 0 1 6 14.5ZM14.5 5A9.46 9.46 0 0 0 5 14.5c0 5.28 4.22 9.5 9.5 9.5s9.5-4.22 9.5-9.5S19.78 5 14.5 5ZM14 16V9h1v6h4v1h-5Z"/></svg>'},683778:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M6 14.5C6 9.78 9.78 6 14.5 6c4.72 0 8.5 3.78 8.5 8.5 0 4.72-3.78 8.5-8.5 8.5A8.46 8.46 0 0 1 6 14.5ZM14.5 5A9.46 9.46 0 0 0 5 14.5c0 5.28 4.22 9.5 9.5 9.5s9.5-4.22 9.5-9.5S19.78 5 14.5 5ZM12 12a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm4 1a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm-6 4-.43.26v.01l.03.03a3.55 3.55 0 0 0 .3.4 5.7 5.7 0 0 0 9.22 0 5.42 5.42 0 0 0 .28-.4l.02-.03v-.01L19 17l-.43-.26v.02a2.45 2.45 0 0 1-.24.32c-.17.21-.43.5-.78.79a4.71 4.71 0 0 1-6.88-.8 4.32 4.32 0 0 1-.23-.31l-.01-.02L10 17Z"/></svg>'},90624:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 112 112" width="28" height="28"><path fill="#fff" d="M63.42 93.22a37.13 37.13 0 1 0 .01-74.27 37.13 37.13 0 0 0-.01 74.27Z"/><path fill="#fff" d="M45.48 48.85c-.71.04-1.96 0-3.17.2-2.36.41-4.72.85-7.03 1.51a30.65 30.65 0 0 0-4.87 2.02c-1.9.9-3.74 1.93-5.59 2.94-.66.36-.71.86-.16 1.39.53.53 1.1 1.01 1.7 1.44 2.43 1.63 4.91 3.15 7.3 4.85 2.77 1.95 5.86 3.03 8.95 4.03 3.5 1.14 7.15.85 10.72.38 4.05-.54 8.1-1.3 11.9-2.96 2.17-.95 4.21-2.22 6.27-3.44.88-.5.86-.86.08-1.5-1.59-1.28-3.16-2.6-4.82-3.78-3.73-2.66-7.65-4.85-12.05-6a29.47 29.47 0 0 0-9.23-1.08Zm6.56-21.95v8.8c0 1.1-.02 2.18-.03 3.27 0 .86.33 1.39 1.14 1.47.38.04.77.06 1.16.11 2.8.35 3.14.13 3.99-2.86.77-2.7 1.47-5.44 2.22-8.15.31-1.12.5-1.18 1.5-.79 1.98.78 3.95 1.58 5.94 2.32.77.29 1.03.6.7 1.56-.98 2.94-1.86 5.92-2.77 8.89-.09.28-.15.57-.21.86-.42 2.02-.37 2.12 1.37 2.8.25.1.5.21.74.34.51.3.91.26 1.38-.19 2.34-2.22 4.75-4.34 7.05-6.6.74-.73 1.57-.62 2.16-.04A83.06 83.06 0 0 1 82 42.52c.64.73.6 1.52-.04 2.3a273.4 273.4 0 0 1-4.69 5.62c-.46.53-.44.98-.02 1.44 1.46 1.55 2.93 3.1 4.4 4.63 1.1 1.13 2.21 2.24 3.3 3.37 1.05 1.07 1.12 1.67.06 2.77-1.44 1.5-2.86 3.08-4.51 4.23a87.09 87.09 0 0 1-10 6.28 32.38 32.38 0 0 1-12.28 3.5c-4.54.36-9.07.43-13.57-.15a59.04 59.04 0 0 1-9.69-2.07 38.4 38.4 0 0 1-8.35-3.83 51.59 51.59 0 0 1-5.8-4.13 73.78 73.78 0 0 1-6.18-5.38c-1.29-1.3-2.33-2.9-3.38-4.46-.58-.84-.06-1.55.59-2.1 1.14-.96 2.32-1.9 3.42-2.9.72-.65.95-.96 1.62-1.67.5-.53.43-1.02-.07-1.51-1.3-1.3-1.52-1.76-2.83-3.07-.6-.59-.74-1.1-.07-1.79 1.66-1.72 4.35-4.22 5.97-5.98.8-.86.9-.82 1.7.12 1.6 1.9 2.12 2.97 3.78 4.83.87.98 1.19 1.55 2.5 1.04 2.37-.95 1.76-.7 1.05-3.35-.64-2.37-1-2.96-1.72-5.3-.08-.26-.17-.5-.23-.75-.33-1.2-.3-1.33.8-1.7 2.06-.68 5.56-1.72 7.62-2.4.8-.27 1.16.18 1.39.93.73 2.55 1.01 3.38 1.77 5.92.2.72.48 1.41.84 2.05.7 1.18 1.13 1.4 2.27 1.36 1.96-.07 2.24-.3 2.24-2.45 0-3.1-.06-6.21-.14-9.32-.04-1.53-.07-1.62 1.34-1.66 2.3-.06 4.61-.02 6.96-.02"/><path fill="#2962FF" d="M63.42 90.92a34.26 34.26 0 1 0 .01-68.52 34.26 34.26 0 0 0-.01 68.52Z"/><path fill="#FF5200" d="M45.69 49.83c-.67.03-1.83 0-2.95.17-2.2.35-4.4.72-6.54 1.28-1.56.4-3.06 1.05-4.53 1.7-1.76.77-3.47 1.64-5.2 2.49-.6.3-.66.73-.15 1.17.5.45 1.03.86 1.59 1.22 2.26 1.37 4.56 2.66 6.79 4.1 2.57 1.64 5.45 2.55 8.31 3.4 3.26.96 6.65.72 9.98.32 3.76-.46 7.52-1.1 11.06-2.5 2.01-.8 3.92-1.88 5.82-2.9.82-.44.8-.74.08-1.27-1.48-1.09-2.94-2.2-4.48-3.2-3.47-2.25-7.11-4.1-11.2-5.06a30.03 30.03 0 0 0-8.59-.91v-.01Zm6.09-18.54v7.44l-.02 2.76c0 .72.3 1.17 1.05 1.24.36.03.73.05 1.08.1 2.6.29 2.92.1 3.71-2.43.72-2.28 1.37-4.59 2.07-6.88.29-.94.45-1 1.4-.66 1.84.66 3.66 1.33 5.52 1.95.7.25.95.52.64 1.32-.9 2.48-1.72 5-2.57 7.5-.08.25-.14.5-.2.74-.38 1.7-.34 1.79 1.28 2.37.23.08.47.17.7.28.47.26.84.22 1.27-.16 2.18-1.87 4.42-3.67 6.56-5.58.69-.61 1.46-.52 2-.03a73.41 73.41 0 0 1 3.37 3.24c.6.6.56 1.28-.03 1.94-1.44 1.6-2.89 3.18-4.37 4.74-.43.46-.4.83-.01 1.22a340.4 340.4 0 0 0 4.1 3.91c1 .96 2.04 1.9 3.06 2.85.97.9 1.03 1.41.05 2.34-1.34 1.26-2.66 2.6-4.2 3.57a82.59 82.59 0 0 1-9.29 5.3 32.44 32.44 0 0 1-11.42 2.97c-4.22.3-8.43.36-12.62-.13a59.71 59.71 0 0 1-9-1.75c-2.76-.77-5.3-1.91-7.77-3.24a48.2 48.2 0 0 1-5.39-3.49c-2-1.4-3.92-2.92-5.75-4.54-1.2-1.09-2.17-2.45-3.15-3.76-.53-.72-.05-1.31.55-1.78 1.06-.82 2.16-1.6 3.18-2.45.67-.55 1.27-1.17 1.9-1.77.46-.45.4-.86-.07-1.28l-3.64-3.32c-.55-.5-.68-.93-.05-1.51 1.53-1.46 3.01-2.98 4.52-4.46.74-.72.84-.7 1.58.1 1.5 1.61 2.98 3.24 4.51 4.8.82.84 1.75 1.09 2.96.65 2.21-.8 2.3-.73 1.63-2.97-.6-2-1.32-3.96-2-5.93-.07-.22-.16-.42-.21-.63-.3-1.02-.28-1.12.74-1.43 1.92-.59 3.85-1.11 5.77-1.69.75-.23 1.08.15 1.3.78.67 2.16 1.33 4.32 2.04 6.46.18.61.44 1.2.78 1.74.66 1 1.72.98 2.78.94 1.83-.06 2.09-.25 2.09-2.07 0-2.62-.06-5.25-.13-7.87-.04-1.3-.07-1.37 1.24-1.4 2.14-.06 4.29-.02 6.47-.02"/><path fill="#FDD600" d="m53.5 54.08.15-.32c-.5-.49-.91-1.15-1.5-1.44a9.83 9.83 0 0 0-6.84-.8c-1.95.5-3.23 1.92-4.14 3.57-.98 1.8-1.33 3.8-.09 5.64.54.8 1.38 1.44 2.16 2.04a6.98 6.98 0 0 0 10.61-2.68c.4-.87.27-1.18-.66-1.48-.98-.31-1.98-.59-2.96-.9-.65-.22-1.31-.44-1.31-1.3 0-.82.53-1.15 1.24-1.35 1.12-.3 2.23-.65 3.34-.97Zm-7.81-4.25c3.23-.15 5.9.29 8.58.92 4.08.96 7.73 2.8 11.21 5.06 1.54.99 3 2.1 4.48 3.2.72.53.74.82-.08 1.26-1.91 1.03-3.82 2.1-5.82 2.9-3.54 1.4-7.3 2.04-11.07 2.5-3.32.4-6.72.65-9.97-.31-2.87-.85-5.74-1.76-8.32-3.41-2.22-1.43-4.52-2.72-6.78-4.1a12 12 0 0 1-1.6-1.21c-.5-.45-.45-.86.17-1.18 1.72-.86 3.43-1.72 5.19-2.48 1.48-.65 2.97-1.3 4.52-1.7 2.16-.56 4.35-.93 6.55-1.28 1.12-.18 2.28-.14 2.94-.18"/><path fill="#1D1D1B" d="M53.5 54.08c-1.11.33-2.22.67-3.34.98-.71.19-1.24.52-1.24 1.34 0 .86.67 1.1 1.3 1.3.99.32 1.99.6 2.97.9.93.3 1.05.61.66 1.49a6.98 6.98 0 0 1-10.62 2.68 9.18 9.18 0 0 1-2.16-2.04c-1.24-1.85-.9-3.85.1-5.65.9-1.65 2.18-3.07 4.13-3.57a9.84 9.84 0 0 1 6.84.8c.6.3 1.01.95 1.5 1.44l-.15.33"/></svg>'
},214665:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 16" width="10" height="16"><path d="M.6 1.4l1.4-1.4 8 8-8 8-1.4-1.4 6.389-6.532-6.389-6.668z"/></svg>'},239146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},648010:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.116.236.26.038 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'}}]);