// Test script for indicator sources functionality
// Run this in the browser console after the chart loads

console.log('🧪 Testing Indicator Sources Functionality');
console.log('==========================================');

// Test 1: Check initial sources
console.log('\n📊 Test 1: Initial Sources');
console.log('Initial sources:', window.sourcesConfig.sources);

// Test 2: Add RSI and check sources
console.log('\n📊 Test 2: Adding RSI');
const rsiId = addRSI(14);
console.log('RSI added with ID:', rsiId);
console.log('Sources after RSI:', window.sourcesConfig.sources);

// Test 3: Add MACD and check sources
console.log('\n📊 Test 3: Adding MACD');
const macdId = addMACD(12, 26, 9);
console.log('MACD added with ID:', macdId);
console.log('Sources after MACD:', window.sourcesConfig.sources);

// Test 4: Add Bollinger Bands and check sources
console.log('\n📊 Test 4: Adding Bollinger Bands');
const bbId = addBollingerBands(20, 2);
console.log('Bollinger Bands added with ID:', bbId);
console.log('Sources after BB:', window.sourcesConfig.sources);

// Test 5: Check indicator sources map
console.log('\n📊 Test 5: Indicator Sources Map');
console.log('Indicator sources map:', window.sourcesConfig._indicatorSources);

// Test 6: List all indicators
console.log('\n📊 Test 6: All Indicators');
const indicators = listIndicators();

// Test 7: Add Flexible SMA using indicator source
console.log('\n📊 Test 7: Adding Flexible SMA with RSI source');
setTimeout(() => {
    try {
        const flexSmaId = addFlexibleSMA(10, 'RSI: RSI');
        console.log('Flexible SMA added with ID:', flexSmaId);
        console.log('Final sources:', window.sourcesConfig.sources);
    } catch (error) {
        console.log('Note: Flexible SMA might need manual addition through UI');
        console.log('Available sources for manual selection:', window.sourcesConfig.sources);
    }
}, 2000);

// Test 8: Verify sources contain indicator outputs
console.log('\n📊 Test 8: Verification');
setTimeout(() => {
    const sources = window.sourcesConfig.sources;
    const hasRSI = sources.some(s => s.includes('RSI'));
    const hasMACD = sources.some(s => s.includes('MACD'));
    const hasBB = sources.some(s => s.includes('Bollinger'));
    
    console.log('✅ Test Results:');
    console.log('- RSI sources found:', hasRSI);
    console.log('- MACD sources found:', hasMACD);
    console.log('- Bollinger Bands sources found:', hasBB);
    console.log('- Total sources:', sources.length);
    
    if (hasRSI && hasMACD && hasBB) {
        console.log('🎉 SUCCESS: All indicator sources are working correctly!');
        console.log('💡 Now try adding a Flexible SMA/RSI and check the source dropdown');
    } else {
        console.log('⚠️ Some indicator sources may not be detected');
    }
}, 3000);

// Helper function to test manual source refresh
window.testRefresh = function() {
    console.log('\n🔄 Testing manual refresh...');
    refreshIndicatorSources();
    console.log('Sources after refresh:', window.sourcesConfig.sources);
};

console.log('\n💡 Instructions:');
console.log('1. Wait for the tests to complete (3 seconds)');
console.log('2. Try adding a Flexible SMA: addFlexibleSMA(10, "RSI: RSI")');
console.log('3. Or use the UI to add indicators and check source dropdowns');
console.log('4. Run testRefresh() if sources seem out of sync');
