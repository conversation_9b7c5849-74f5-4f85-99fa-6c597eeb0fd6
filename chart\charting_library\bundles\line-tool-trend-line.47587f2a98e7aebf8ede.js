/**
 * TradingView Line Tool Trend Line Bundle
 * This module provides functionality for drawing and editing trend lines on charts
 * with in-place text editing capabilities.
 */
"use strict";

// Initialize TradingView webpack chunk
(self.webpackChunktradingview = self.webpackChunktradingview || []).push([[8673], {

    // Module 174906: InplaceTextLineDataSource and InplaceTextUndoCommand
    174906: (exports, module, require) => {

        // Export the main classes
        require.d(module, {
            InplaceTextLineDataSource: () => InplaceTextLineDataSource,
            InplaceTextUndoCommand: () => InplaceTextUndoCommand
        });

        // Import required dependencies
        var deepEqual = require(650279);
        var ensureNotNull = require(650151);
        var point = require(86441);
        var colorUtils = require(926048);
        var colorBlending = require(724377);
        var translations = require(609838);
        var translatedString = require(272047);
        var combine = require(658895);
        var watchedValue = require(240534);
        var undoCommand = require(887240);
        var lineDataSource = require(889868);
        var sourceChangeEvent = require(934135);
        var lineToolsConfig = require(979910);

        // Light theme text editing styles
        const LIGHT_THEME_STYLES = {
            selectionColor: colorUtils.getHexColorByName("color-tv-blue-500"),
            cursorColor: colorUtils.getHexColorByName("color-black")
        };

        // Dark theme text editing styles
        const DARK_THEME_STYLES = {
            selectionColor: colorUtils.getHexColorByName("color-white"),
            cursorColor: colorUtils.getHexColorByName("color-white")
        };

        /**
         * Undo command for in-place text editing operations
         * Handles undo/redo functionality when text is modified
         */
        class InplaceTextUndoCommand extends undoCommand.UndoCommand {

            constructor(model, source, oldValue, newValue) {
                // Create localized command description
                const title = new translatedString.TranslatedString(
                    "change {title} text",
                    translations.t(null, void 0, require(257122))
                ).format({
                    title: new translatedString.TranslatedString(source.name(), source.translatedType())
                });

                super(title, true, !lineToolsConfig.lineToolsDoNotAffectChartInvalidation);

                this._sourceId = source.id();
                this._model = model;
                this._oldValue = oldValue;
                this._newValue = newValue;
                this._changeVisibility = source.editableTextProperties().textVisible?.value() === false;
            }

            /**
             * Execute the text change (redo operation)
             */
            redo() {
                const source = this._getSource();
                this._getTextProperty(source).setValue(this._newValue);

                if (this._changeVisibility) {
                    this._getTextVisibilityProperty(source)?.setValue(true);
                }
            }

            /**
             * Revert the text change (undo operation)
             */
            undo() {
                const source = this._getSource();
                this._getTextProperty(source).setValue(this._oldValue);

                if (this._changeVisibility) {
                    this._getTextVisibilityProperty(source)?.setValue(false);
                }
            }

            /**
             * Get the text property from the source
             */
            _getTextProperty(source) {
                return source.editableTextProperties().text;
            }

            /**
             * Get the text visibility property from the source
             */
            _getTextVisibilityProperty(source) {
                return source.editableTextProperties().textVisible;
            }

            /**
             * Get the data source by ID
             */
            _getSource() {
                return ensureNotNull.ensureNotNull(this._model.dataSourceForId(this._sourceId));
            }
        }

        /**
         * Base class for line data sources with in-place text editing capabilities
         * Provides functionality for editing text directly on the chart
         */
        class InplaceTextLineDataSource extends lineDataSource.LineDataSource {

            constructor(model, properties, points, zOrder) {
                super(model, properties, points, zOrder);

                // Initialize text editing state
                this._container = null;
                this._editableText = new watchedValue.WatchedValue("");
                this._activateTextEditingElement = null;
                this._paneView = null;
                this._selectionData = {};
                this._cursorPaneView = null;
                this._cursorPosition = null;
                this._editingOnCreation = false;
                this._editingActivationTime = null;

                // Subscribe to text changes
                this._editableText.subscribe(() => {
                    this.updateAllViewsAndRedraw(sourceChangeEvent.sourceChangeEvent(this.id()));
                });

                // Determine if background is dark for styling
                this._isDarkBackground = combine.combine(
                    (backgroundColor, dataSourceBackgroundColor) => {
                        if (dataSourceBackgroundColor === null) {
                            return this._model.dark().value();
                        }

                        const blendedColor = colorBlending.blendRgba(
                            colorBlending.parseRgba(backgroundColor),
                            colorBlending.parseRgba(dataSourceBackgroundColor)
                        );

                        return colorBlending.rgbToBlackWhiteString(
                            [blendedColor[0], blendedColor[1], blendedColor[2]],
                            150
                        ) === "black";
                    },
                    this._model.backgroundColor().spawnOwnership(),
                    this._createDataSourceBackgroundColorWV()
                );

                // Load cursor pane view asynchronously
                Promise.all([
                    require.e(6290),
                    require.e(6881),
                    require.e(5579),
                    require.e(1583)
                ])
                .then(require.bind(require, 600733))
                .then((cursorModule) => {
                    this._cursorPaneView = new cursorModule.InplaceTextCursorPaneView(this, model);

                    if (this._additionalCursorDataGetters) {
                        this._cursorPaneView.setAdditionalCursorData(...this._additionalCursorDataGetters);

                        if (this._cursorPosition !== null) {
                            this._cursorPaneView.setCursorPosition(this._cursorPosition);
                            model.updateSource(this);
                        }
                    }
                });
            }

            /**
             * Clean up resources when destroying the data source
             */
            destroy() {
                this._isDarkBackground.destroy();
                this._editableText.unsubscribe();
                this._closeTextEditor();
                super.destroy();
            }

            /**
             * Get the current text editing style based on theme
             */
            editableTextStyle() {
                return {
                    ...(this._isDarkBackground.value() ? DARK_THEME_STYLES : LIGHT_THEME_STYLES)
                };
            }

            /**
             * Determine if the data source should be removed when text is empty
             */
            removeIfEditableTextIsEmpty() {
                return false;
            }

            /**
             * Determine if editing should be activated when the tool is created
             */
            activateEditingOnCreation() {
                return false;
            }

            /**
             * Get top pane views for rendering
             */
            topPaneViews(pane) {
                if (pane.hasDataSource(this) &&
                    !window.TradingView.printing &&
                    this._cursorPaneView) {

                    this._cursorPaneView.update(sourceChangeEvent.sourceChangeEvent(this.id()));
                    return [this._cursorPaneView];
                }
                return null;
            }

            /**
             * Check if data and views are ready for rendering
             */
            dataAndViewsReady() {
                return super.dataAndViewsReady() && this._cursorPaneView !== null;
            }

            /**
             * Get the editable text value
             */
            editableText() {
                return this._editableText;
            }

            /**
             * Get the text editing element
             */
            textEditingElement() {
                return this._activateTextEditingElement;
            }

            /**
             * Activate text editing on a specific element
             */
            activateTextEditingOn(element, isCreation) {
                this._activateTextEditingElement = element;
                this._editingOnCreation = !!isCreation;
                this._editingActivationTime = performance.now();
                this.updateAllViewsAndRedraw(sourceChangeEvent.sourceChangeEvent(this.id()));
            }

            /**
             * Deactivate text editing
             */
            deactivateTextEditing() {
                this._closeTextEditor();
            }

            /**
             * Get the time when text editing was activated
             */
            textEditingActivationTime() {
                return this._editingActivationTime;
            }

            /**
             * Handle text selection changes
             */
            onSelectionChange(selection) {
                const selectionData = {};

                if (selection !== undefined) {
                    const { start, end } = selection;

                    if (start === end) {
                        selectionData.cursorPosition = start;
                    } else {
                        selectionData.selectionRange = [Math.min(start, end), Math.max(start, end)];
                    }
                }

                // Only update if selection data has changed
                if (!deepEqual.default(selectionData, this._selectionData)) {
                    this._selectionData = selectionData;

                    // Update all pane views with new selection
                    this._paneViews.forEach((paneViewArray) => {
                        paneViewArray.forEach((paneView) => {
                            if ("setSelectionRange" in paneView) {
                                paneView.setSelectionRange(selectionData.selectionRange);
                            }
                        });
                    });

                    // Update cursor position
                    if (this._cursorPaneView) {
                        this._cursorPaneView.setCursorPosition(selectionData.cursorPosition);
                    } else {
                        this._cursorPosition = selectionData.cursorPosition ?? null;
                    }

                    this.updateAllViewsAndRedraw(sourceChangeEvent.sourceChangeEvent(this.id()));
                }
            }

            /**
             * Set additional cursor data for rendering
             */
            setAdditionalCursorData(getter1, getter2) {
                if (this._cursorPaneView) {
                    this._cursorPaneView.setAdditionalCursorData(getter1, getter2);
                } else {
                    this._additionalCursorDataGetters = [getter1, getter2];
                }
            }

            /**
             * Update all pane views with new data
             */
            _updateAllPaneViews(changeEvent) {
                super._updateAllPaneViews(changeEvent);
                this._cursorPaneView?.update(changeEvent);
            }

            /**
             * Open the text editor interface
             */
            async _openTextEditor(parentElement, textInfo, placeholder, onClose, onSelectionChange) {
                // Prevent multiple editors from opening
                if (this._container !== null) {
                    return;
                }

                // Set activation time if not already set
                if (this._editingActivationTime === null) {
                    this._editingActivationTime = performance.now();
                }

                // Reset state
                this._activateTextEditingElement = null;
                this._cursorPosition = null;

                // Create container for the text editor
                this._container = document.createElement("div");
                this._container.style.position = "absolute";
                this._container.style.top = "0";
                this._container.style.bottom = "0";
                this._container.style.left = "0";
                this._container.style.right = "0";
                this._container.style.overflow = "hidden";
                this._container.style.pointerEvents = "none";

                parentElement.appendChild(this._container);

                // Load text editor module
                const { updateChartEditorText, closeChartEditorText } = await Promise.all([
                    require.e(4556),
                    require.e(2227),
                    require.e(5592)
                ]).then(require.bind(require, 707040));

                // Check if container still exists and component not destroyed
                if (this._container === null || this._isDestroyed) {
                    return;
                }

                this._closeChartEditorText = closeChartEditorText;

                // Get text properties
                const { text, textColor, wordWrap } = this.editableTextProperties();
                const { forbidLineBreaks, maxLength } = this.editableTextStyle();

                // Set initial text value
                this._editableText.setValue(text.value());

                // Calculate position for the editor
                const position = this.isFixed()
                    ? ensureNotNull.ensureDefined(this.fixedPoint())
                    : ensureNotNull.ensureNotNull(this.pointToScreenPoint(this._points[0]));

                // Configure text editor options
                const editorOptions = {
                    position: point.point(position.x, position.y),
                    textInfo: textInfo,
                    placeholder: placeholder,
                    text: this._editableText,
                    textColor: textColor,
                    wordWrap: wordWrap,
                    forbidLineBreaks: forbidLineBreaks,
                    maxLength: maxLength,
                    onClose: onClose,
                    onSelectionChange: onSelectionChange,
                    onContextMenu: this.onContextMenu ? this.onContextMenu.bind(this) : undefined
                };

                // Initialize the text editor
                updateChartEditorText(this._container, editorOptions);
                this.updateAllViewsAndRedraw(sourceChangeEvent.sourceChangeEvent(this.id()));
            }

            /**
             * Close the text editor and save changes
             */
            _closeTextEditor() {
                if (this._container === null || this._isDestroyed) {
                    return;
                }

                // Reset editing state
                this._editingActivationTime = null;
                this._saveEditedText();
                this._editingOnCreation = false;
                this.onSelectionChange();

                // Close the editor
                this._closeChartEditorText?.(this._container);
                this._closeChartEditorText = undefined;

                // Remove container
                this._container.remove();
                this._container = null;

                this.updateAllViewsAndRedraw(sourceChangeEvent.sourceChangeEvent(this.id()));
            }

            /**
             * Save the edited text and create undo command
             */
            _saveEditedText() {
                const originalText = this.editableTextProperties().text.value();
                const newText = this._editableText.value();

                // Only save if text has changed
                if (originalText !== newText) {
                    // If editing on creation, set the text directly
                    if (this._editingOnCreation) {
                        this.editableTextProperties().text.setValue(newText);
                    }

                    // Add undo command to history
                    this._model.undoModel().undoHistory().pushUndoCommand(
                        this._createChangeEditableTextUndoCommand(originalText, newText)
                    );
                }
            }

            /**
             * Create an undo command for text changes
             */
            _createChangeEditableTextUndoCommand(oldValue, newValue) {
                return new InplaceTextUndoCommand(this._model, this, oldValue, newValue);
            }

            /**
             * Create a watched value for data source background color
             */
            _createDataSourceBackgroundColorWV() {
                return new watchedValue.WatchedValue(null).readonly().ownership();
            }
        }
    },

    // Module 434961: LineToolTrendLine implementation
    434961: (exports, module, require) => {

        require.r(module);
        require.d(module, {
            Consts: () => TrendLineConstants,
            LineToolTrendLine: () => LineToolTrendLine
        });

        // Import dependencies
        var defaultProperty = require(792535);
        var property = require(571772);
        var inplaceTextLineDataSource = require(174906);
        var lineToolPriceAxisView = require(884634);

        /**
         * Constants for the Trend Line tool
         */
        var TrendLineConstants;
        (function(TrendLineConstants) {
            TrendLineConstants[TrendLineConstants.PointsCount = 2] = "PointsCount";
            TrendLineConstants.Name = "Trend Line";
        })(TrendLineConstants || (TrendLineConstants = {}));

        /**
         * Trend Line tool implementation
         * Extends InplaceTextLineDataSource to provide trend line functionality
         * with text editing capabilities
         */
        class LineToolTrendLine extends inplaceTextLineDataSource.InplaceTextLineDataSource {

            constructor(model, properties, points, zOrder) {
                super(
                    model,
                    properties ?? LineToolTrendLine.createProperties(model.backgroundTheme().spawnOwnership()),
                    points,
                    zOrder
                );

                this._trendLinePaneView = null;

                // Load trend line pane view asynchronously
                Promise.all([
                    require.e(6290),
                    require.e(6881),
                    require.e(5579),
                    require.e(1583)
                ])
                .then(require.bind(require, 818134))
                .then(({ TrendLinePaneView }) => {
                    this._trendLinePaneView = new TrendLinePaneView(
                        this,
                        this._model,
                        this._openTextEditor.bind(this),
                        this._closeTextEditor.bind(this),
                        this.onSelectionChange.bind(this)
                    );

                    this._setPaneViews([this._trendLinePaneView]);
                });
            }

            /**
             * Check if data and views are ready for rendering
             */
            dataAndViewsReady() {
                return super.dataAndViewsReady() && this._trendLinePaneView !== null;
            }

            /**
             * Get the number of points required for this tool
             */
            pointsCount() {
                return 2;
            }

            /**
             * Get the display name of this tool
             */
            name() {
                return "Trend Line";
            }

            /**
             * Check if this tool can have alerts
             */
            canHasAlert() {
                return true;
            }

            /**
             * Check if price labels should be shown
             */
            showPriceLabels() {
                return this._properties.childs().showPriceLabels.value();
            }

            /**
             * Create a price axis view for the specified point
             */
            createPriceAxisView(pointIndex) {
                return new lineToolPriceAxisView.LineToolPriceAxisView(this, {
                    pointIndex: pointIndex,
                    backgroundPropertyGetter: () => {
                        return this.showPriceLabels()
                            ? this._properties.childs().linecolor.value()
                            : null;
                    }
                });
            }

            /**
             * Check if price axis labels should be forced to draw
             */
            isForcedDrawPriceAxisLabel() {
                return this.showPriceLabels();
            }

            /**
             * Get the template data for this tool
             */
            template() {
                const template = super.template();
                template.text = this.properties().childs().text.value();
                return template;
            }

            /**
             * Get the editable text properties
             */
            editableTextProperties() {
                const properties = this.properties().childs();
                return {
                    text: properties.text,
                    textColor: properties.textcolor,
                    textVisible: properties.showLabel
                };
            }

            /**
             * Check if snapping to 45-degree angles is available
             */
            snapTo45DegreesAvailable() {
                return true;
            }

            /**
             * Create default properties for the trend line
             */
            static createProperties(theme, state, useUserPreferences) {
                return LineToolTrendLine._createPropertiesImpl(
                    "linetooltrendline",
                    theme,
                    state,
                    useUserPreferences
                );
            }

            /**
             * Get alert plots for this trend line
             */
            _getAlertPlots() {
                const alertPlot = this._linePointsToAlertPlot(
                    this._points,
                    null,
                    this._properties.childs().extendLeft.value(),
                    this._properties.childs().extendRight.value()
                );

                return alertPlot === null ? [] : [alertPlot];
            }

            /**
             * Get the property definitions view model class
             */
            async _getPropertyDefinitionsViewModelClass() {
                const module = await Promise.all([
                    require.e(6406),
                    require.e(8511),
                    require.e(5234),
                    require.e(4590),
                    require.e(8537)
                ]).then(require.bind(require, 262060));

                return module.TrendLineDefinitionsViewModel;
            }

            /**
             * Apply template data to this tool
             */
            _applyTemplateImpl(template) {
                super._applyTemplateImpl(template);
                this.properties().childs().text.setValue(template.text || "");
            }

            /**
             * Create properties implementation
             */
            static _createPropertiesImpl(name, theme, state, useUserPreferences) {
                // Handle legacy property migration
                if (state && state.showPercentPriceRange === undefined) {
                    state.showPercentPriceRange = state.showPriceRange;
                    state.showPipsPriceRange = state.showPriceRange;
                }

                const properties = new defaultProperty.DefaultProperty({
                    theme: theme,
                    defaultName: name,
                    state: state,
                    useUserPreferences: useUserPreferences
                });

                this._configureProperties(properties);
                return properties;
            }

            /**
             * Configure the properties for the trend line
             */
            static _configureProperties(properties) {
                super._configureProperties(properties);

                // Add text property if it doesn't exist
                if (!properties.hasChild("text")) {
                    properties.addChild("text", new property.Property(""));
                }

                // Exclude text from certain operations
                properties.addExcludedKey("text", 1);
            }
        }
    }
}]);