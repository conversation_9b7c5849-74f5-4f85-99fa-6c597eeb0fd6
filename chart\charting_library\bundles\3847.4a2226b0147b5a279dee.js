(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3847],{259142:function(e,t){var n,r,o;r=[t],n=function(e){"use strict";function t(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var n=!1;if("undefined"!=typeof window){var r={get passive(){n=!0}};window.addEventListener("testPassive",null,r),window.removeEventListener("testPassive",null,r)}var o="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),c=[],u=!1,a=-1,i=void 0,s=void 0,f=function(e){return c.some((function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))}))},l=function(e){var t=e||window.event;return!!f(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},d=function(){setTimeout((function(){void 0!==s&&(document.body.style.paddingRight=s,s=void 0),void 0!==i&&(document.body.style.overflow=i,i=void 0)}))};e.disableBodyScroll=function(e,r){if(o){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!c.some((function(t){return t.targetElement===e}))){var d={targetElement:e,options:r||{}};c=[].concat(t(c),[d]),e.ontouchstart=function(e){1===e.targetTouches.length&&(a=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var n,r,o,c;1===t.targetTouches.length&&(r=e,c=(n=t).targetTouches[0].clientY-a,!f(n.target)&&(r&&0===r.scrollTop&&0<c||(o=r)&&o.scrollHeight-o.scrollTop<=o.clientHeight&&c<0?l(n):n.stopPropagation()))},u||(document.addEventListener("touchmove",l,n?{passive:!1}:void 0),u=!0)}}else{v=r,setTimeout((function(){if(void 0===s){var e=!!v&&!0===v.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(s=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===i&&(i=document.body.style.overflow,document.body.style.overflow="hidden")}));var p={targetElement:e,options:r||{}};c=[].concat(t(c),[p])}var v},e.clearAllBodyScrollLocks=function(){o?(c.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),u&&(document.removeEventListener("touchmove",l,n?{passive:!1}:void 0),u=!1),c=[],a=-1):(d(),c=[])},e.enableBodyScroll=function(e){if(o){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,c=c.filter((function(t){return t.targetElement!==e})),u&&0===c.length&&(document.removeEventListener("touchmove",l,n?{passive:!1}:void 0),u=!1)}else 1===c.length&&c[0].targetElement===e?(d(),c=[]):c=c.filter((function(t){return t.targetElement!==e}))}},void 0===(o="function"==typeof n?n.apply(t,r):n)||(e.exports=o)},472535:(e,t,n)=>{"use strict";var r=n(756237),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,
mixins:!0,propTypes:!0,type:!0},c={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},u={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function i(e){return r.isMemo(e)?u:a[e.$$typeof]||o}a[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[r.Memo]=u;var s=Object.defineProperty,f=Object.getOwnPropertyNames,l=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,v=Object.prototype;e.exports=function e(t,n,r){if("string"!=typeof n){if(v){var o=p(n);o&&o!==v&&e(t,o,r)}var u=f(n);l&&(u=u.concat(l(n)));for(var a=i(t),y=i(n),m=0;m<u.length;++m){var h=u[m];if(!(c[h]||r&&r[h]||y&&y[h]||a&&a[h])){var g=d(n,h);try{s(t,h,g)}catch(e){}}}}return t}},760198:(e,t)=>{"use strict";var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,c=n?Symbol.for("react.fragment"):60107,u=n?Symbol.for("react.strict_mode"):60108,a=n?Symbol.for("react.profiler"):60114,i=n?Symbol.for("react.provider"):60109,s=n?Symbol.for("react.context"):60110,f=n?Symbol.for("react.async_mode"):60111,l=n?Symbol.for("react.concurrent_mode"):60111,d=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,v=n?Symbol.for("react.suspense_list"):60120,y=n?Symbol.for("react.memo"):60115,m=n?Symbol.for("react.lazy"):60116,h=n?Symbol.for("react.block"):60121,g=n?Symbol.for("react.fundamental"):60117,S=n?Symbol.for("react.responder"):60118,b=n?Symbol.for("react.scope"):60119;function E(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case f:case l:case c:case a:case u:case p:return e;default:switch(e=e&&e.$$typeof){case s:case d:case m:case y:case i:return e;default:return t}}case o:return t}}}function w(e){return E(e)===l}t.AsyncMode=f,t.ConcurrentMode=l,t.ContextConsumer=s,t.ContextProvider=i,t.Element=r,t.ForwardRef=d,t.Fragment=c,t.Lazy=m,t.Memo=y,t.Portal=o,t.Profiler=a,t.StrictMode=u,t.Suspense=p,t.isAsyncMode=function(e){return w(e)||E(e)===f},t.isConcurrentMode=w,t.isContextConsumer=function(e){return E(e)===s},t.isContextProvider=function(e){return E(e)===i},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return E(e)===d},t.isFragment=function(e){return E(e)===c},t.isLazy=function(e){return E(e)===m},t.isMemo=function(e){return E(e)===y},t.isPortal=function(e){return E(e)===o},t.isProfiler=function(e){return E(e)===a},t.isStrictMode=function(e){return E(e)===u},t.isSuspense=function(e){return E(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===c||e===l||e===a||e===u||e===p||e===v||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===y||e.$$typeof===i||e.$$typeof===s||e.$$typeof===d||e.$$typeof===g||e.$$typeof===S||e.$$typeof===b||e.$$typeof===h)},t.typeOf=E},756237:(e,t,n)=>{"use strict";e.exports=n(760198)},254218:(e,t)=>{"use strict"
;var n,r=Symbol.for("react.element"),o=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),f=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),v=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),m=Symbol.for("react.offscreen");function h(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case c:case a:case u:case d:case p:return e;default:switch(e=e&&e.$$typeof){case f:case s:case l:case y:case v:case i:return e;default:return t}}case o:return t}}}n=Symbol.for("react.module.reference"),t.isContextConsumer=function(e){return h(e)===s}},361357:(e,t,n)=>{"use strict";e.exports=n(254218)},743766:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Provider:()=>B,ReactReduxContext:()=>s,batch:()=>c.unstable_batchedUpdates,connect:()=>q,createDispatchHook:()=>H,createSelectorHook:()=>v,createStoreHook:()=>F,shallowEqual:()=>N,useDispatch:()=>U,useSelector:()=>y,useStore:()=>I});var r=n(383363),o=n(34730),c=n(632227);let u=function(e){e()};const a=()=>u;var i=n(50959);const s=(0,i.createContext)(null);function f(){return(0,i.useContext)(s)}const l=()=>{throw new Error("uSES not initialized!")};let d=l;const p=(e,t)=>e===t;function v(e=s){const t=e===s?f:()=>(0,i.useContext)(e);return function(e,n=p){const{store:r,subscription:o,getServerState:c}=t(),u=d(o.addNestedSub,r.getState,c||r.getState,e,n);return(0,i.useDebugValue)(u),u}}const y=v();var m=n(315882),h=n(330950),g=n(472535),S=n.n(g),b=n(361357);const E=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function w(e,t,n,r,{areStatesEqual:o,areOwnPropsEqual:c,areStatePropsEqual:u}){let a,i,s,f,l,d=!1;function p(d,p){const v=!c(p,i),y=!o(d,a,p,i);return a=d,i=p,v&&y?(s=e(a,i),t.dependsOnOwnProps&&(f=t(r,i)),l=n(s,f,i),l):v?(e.dependsOnOwnProps&&(s=e(a,i)),t.dependsOnOwnProps&&(f=t(r,i)),l=n(s,f,i),l):y?function(){const t=e(a,i),r=!u(t,s);return s=t,r&&(l=n(s,f,i)),l}():l}return function(o,c){return d?p(o,c):(a=o,i=c,s=e(a,i),f=t(r,i),l=n(s,f,i),d=!0,l)}}function x(e){return function(t){const n=e(t);function r(){return n}return r.dependsOnOwnProps=!1,r}}function C(e){return e.dependsOnOwnProps?Boolean(e.dependsOnOwnProps):1!==e.length}function P(e,t){return function(t,{displayName:n}){const r=function(e,t){return r.dependsOnOwnProps?r.mapToProps(e,t):r.mapToProps(e,void 0)};return r.dependsOnOwnProps=!0,r.mapToProps=function(t,n){r.mapToProps=e,r.dependsOnOwnProps=C(e);let o=r(t,n);return"function"==typeof o&&(r.mapToProps=o,r.dependsOnOwnProps=C(o),o=r(t,n)),o},r}}function T(e,t){return(n,r)=>{throw new Error(`Invalid value of type ${typeof e} for ${t} argument when connecting component ${r.wrappedComponentName}.`)}}function M(e,t,n){return(0,m.default)({},n,e,t)}const O={notify(){},get:()=>[]};function k(e,t){let n,r=O;function o(){u.onStateChange&&u.onStateChange()}function c(){n||(n=t?t.addNestedSub(o):e.subscribe(o),
r=function(){const e=a();let t=null,n=null;return{clear(){t=null,n=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let r=!0,o=n={callback:e,next:null,prev:n};return o.prev?o.prev.next=o:t=o,function(){r&&null!==t&&(r=!1,o.next?o.next.prev=o.prev:n=o.prev,o.prev?o.prev.next=o.next:t=o.next)}}}}())}const u={addNestedSub:function(e){return c(),r.subscribe(e)},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:o,isSubscribed:function(){return Boolean(n)},trySubscribe:c,tryUnsubscribe:function(){n&&(n(),n=void 0,r.clear(),r=O)},getListeners:()=>r};return u}const R=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement)?i.useLayoutEffect:i.useEffect;function j(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function N(e,t){if(j(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let r=0;r<n.length;r++)if(!Object.prototype.hasOwnProperty.call(t,n[r])||!j(e[n[r]],t[n[r]]))return!1;return!0}const $=["reactReduxForwardedRef"];let A=l;const _=[null,null];function L(e,t,n,r,o,c){e.current=r,n.current=!1,o.current&&(o.current=null,c())}function D(e,t){return e===t}const q=function(e,t,n,{pure:r,areStatesEqual:o=D,areOwnPropsEqual:c=N,areStatePropsEqual:u=N,areMergedPropsEqual:a=N,forwardRef:f=!1,context:l=s}={}){const d=l,p=function(e){return e?"function"==typeof e?P(e):T(e,"mapStateToProps"):x((()=>({})))}(e),v=function(e){return e&&"object"==typeof e?x((t=>function(e,t){const n={};for(const r in e){const o=e[r];"function"==typeof o&&(n[r]=(...e)=>t(o(...e)))}return n}(e,t))):e?"function"==typeof e?P(e):T(e,"mapDispatchToProps"):x((e=>({dispatch:e})))}(t),y=function(e){return e?"function"==typeof e?function(e){return function(t,{displayName:n,areMergedPropsEqual:r}){let o,c=!1;return function(t,n,u){const a=e(t,n,u);return c?r(a,o)||(o=a):(c=!0,o=a),o}}}(e):T(e,"mergeProps"):()=>M}(n),g=Boolean(e);return e=>{const t=e.displayName||e.name||"Component",n=`Connect(${t})`,r={shouldHandleStateChanges:g,displayName:n,wrappedComponentName:t,WrappedComponent:e,initMapStateToProps:p,initMapDispatchToProps:v,initMergeProps:y,areStatesEqual:o,areStatePropsEqual:u,areOwnPropsEqual:c,areMergedPropsEqual:a};function s(t){const[n,o,c]=(0,i.useMemo)((()=>{const{reactReduxForwardedRef:e}=t,n=(0,h.default)(t,$);return[t.context,e,n]}),[t]),u=(0,i.useMemo)((()=>n&&n.Consumer&&(0,b.isContextConsumer)(i.createElement(n.Consumer,null))?n:d),[n,d]),a=(0,i.useContext)(u),s=Boolean(t.store)&&Boolean(t.store.getState)&&Boolean(t.store.dispatch),f=Boolean(a)&&Boolean(a.store);const l=s?t.store:a.store,p=f?a.getServerState:l.getState,v=(0,i.useMemo)((()=>function(e,t){let{initMapStateToProps:n,initMapDispatchToProps:r,initMergeProps:o}=t,c=(0,h.default)(t,E);return w(n(e,c),r(e,c),o(e,c),e,c)}(l.dispatch,r)),[l]),[y,S]=(0,i.useMemo)((()=>{if(!g)return _
;const e=k(l,s?void 0:a.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]}),[l,s,a]),x=(0,i.useMemo)((()=>s?a:(0,m.default)({},a,{subscription:y})),[s,a,y]),C=(0,i.useRef)(),P=(0,i.useRef)(c),T=(0,i.useRef)(),M=(0,i.useRef)(!1),O=((0,i.useRef)(!1),(0,i.useRef)(!1)),j=(0,i.useRef)();R((()=>(O.current=!0,()=>{O.current=!1})),[]);const N=(0,i.useMemo)((()=>()=>T.current&&c===P.current?T.current:v(l.getState(),c)),[l,c]),D=(0,i.useMemo)((()=>e=>y?function(e,t,n,r,o,c,u,a,i,s,f){if(!e)return()=>{};let l=!1,d=null;const p=()=>{if(l||!a.current)return;const e=t.getState();let n,p;try{n=r(e,o.current)}catch(e){p=e,d=e}p||(d=null),n===c.current?u.current||s():(c.current=n,i.current=n,u.current=!0,f())};return n.onStateChange=p,n.trySubscribe(),p(),()=>{if(l=!0,n.tryUnsubscribe(),n.onStateChange=null,d)throw d}}(g,l,y,v,P,C,M,O,T,S,e):()=>{}),[y]);var q,B,F;let I;q=L,B=[P,C,M,c,T,S],R((()=>q(...B)),F);try{I=A(D,N,p?()=>v(p(),c):N)}catch(e){throw j.current&&(e.message+=`\nThe error may be correlated with this previous error:\n${j.current.stack}\n\n`),e}R((()=>{j.current=void 0,T.current=void 0,C.current=I}));const H=(0,i.useMemo)((()=>i.createElement(e,(0,m.default)({},I,{ref:o}))),[o,e,I]);return(0,i.useMemo)((()=>g?i.createElement(u.Provider,{value:x},H):H),[u,H,x])}const l=i.memo(s);if(l.WrappedComponent=e,l.displayName=s.displayName=n,f){const t=i.forwardRef((function(e,t){return i.createElement(l,(0,m.default)({},e,{reactReduxForwardedRef:t}))}));return t.displayName=n,t.WrappedComponent=e,S()(t,e)}return S()(l,e)}};const B=function({store:e,context:t,children:n,serverState:r}){const o=(0,i.useMemo)((()=>{const t=k(e);return{store:e,subscription:t,getServerState:r?()=>r:void 0}}),[e,r]),c=(0,i.useMemo)((()=>e.getState()),[e]);R((()=>{const{subscription:t}=o;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),c!==e.getState()&&t.notifyNestedSubs(),()=>{t.tryUnsubscribe(),t.onStateChange=void 0}}),[o,c]);const u=t||s;return i.createElement(u.Provider,{value:o},n)};function F(e=s){const t=e===s?f:()=>(0,i.useContext)(e);return function(){const{store:e}=t();return e}}const I=F();function H(e=s){const t=e===s?I:F(e);return function(){return t().dispatch}}const U=H();var z,V;z=o.useSyncExternalStoreWithSelector,d=z,(e=>{A=e})(r.useSyncExternalStore),V=c.unstable_batchedUpdates,u=V},746212:(e,t,n)=>{"use strict";n.d(t,{buffers:()=>a.I,default:()=>Y,eventChannel:()=>M});var r=n(45807),o=n(315882),c=n(330950),u=n(617443),a=n(943705);function i(){var e={};return e.promise=new Promise((function(t,n){e.resolve=t,e.reject=n})),e}const s=i;var f=[],l=0;function d(e){try{y(),e()}finally{m()}}function p(e){f.push(e),l||(y(),h())}function v(e){try{return y(),e()}finally{h()}}function y(){l++}function m(){l--}function h(){var e;for(m();!l&&void 0!==(e=f.shift());)d(e)}var g=function(e){return function(t){return e.some((function(e){return x(e)(t)}))}},S=function(e){return function(t){return e(t)}},b=function(e){return function(t){return t.type===String(e)}},E=function(e){return function(t){return t.type===e}},w=function(){
return a.k};function x(e){var t="*"===e?w:(0,u.string)(e)?b:(0,u.array)(e)?g:(0,u.stringableFunc)(e)?b:(0,u.func)(e)?S:(0,u.symbol)(e)?E:null;if(null===t)throw new Error("invalid pattern: "+e);return t(e)}var C={type:r.CHANNEL_END_TYPE},P=function(e){return e&&e.type===r.CHANNEL_END_TYPE};function T(e){void 0===e&&(e=(0,a.e)());var t=!1,n=[];return{take:function(r){t&&e.isEmpty()?r(C):e.isEmpty()?(n.push(r),r.cancel=function(){(0,a.r)(n,r)}):r(e.take())},put:function(r){if(!t){if(0===n.length)return e.put(r);n.shift()(r)}},flush:function(n){t&&e.isEmpty()?n(C):n(e.flush())},close:function(){if(!t){t=!0;var e=n;n=[];for(var r=0,o=e.length;r<o;r++){(0,e[r])(C)}}}}}function M(e,t){void 0===t&&(t=(0,a.n)());var n,r=!1,o=T(t),c=function(){r||(r=!0,(0,u.func)(n)&&n(),o.close())};return n=e((function(e){P(e)?c():o.put(e)})),n=(0,a.o)(n),r&&n(),{take:o.take,flush:o.flush,close:c}}function O(){var e,t,n,o,c,u,i=(t=!1,o=n=[],c=function(){o===n&&(o=n.slice())},u=function(){t=!0;var e=n=o;o=[],e.forEach((function(e){e(C)}))},(e={})[r.MULTICAST]=!0,e.put=function(e){if(!t)if(P(e))u();else for(var c=n=o,a=0,i=c.length;a<i;a++){var s=c[a];s[r.MATCH](e)&&(s.cancel(),s(e))}},e.take=function(e,n){void 0===n&&(n=w),t?e(C):(e[r.MATCH]=n,c(),o.push(e),e.cancel=(0,a.o)((function(){c(),(0,a.r)(o,e)})))},e.close=u,e),s=i.put;return i.put=function(e){e[r.SAGA_ACTION]?s(e):p((function(){s(e)}))},i}function k(e,t){var n=e[r.CANCEL];(0,u.func)(n)&&(t.cancel=n),e.then(t,(function(e){t(e,!0)}))}var R,j=0,N=function(){return++j};function $(e){e.isRunning()&&e.cancel()}var A=((R={})[a.T]=function(e,t,n){var o=t.channel,c=void 0===o?e.channel:o,a=t.pattern,i=t.maybe,s=function(e){e instanceof Error?n(e,!0):!P(e)||i?n(e):n(r.TERMINATE)};try{c.take(s,(0,u.notUndef)(a)?x(a):null)}catch(e){return void n(e,!0)}n.cancel=s.cancel},R[a.P]=function(e,t,n){var r=t.channel,o=t.action,c=t.resolve;p((function(){var t;try{t=(r?r.put:e.dispatch)(o)}catch(e){return void n(e,!0)}c&&(0,u.promise)(t)?k(t,n):n(t)}))},R[a.A]=function(e,t,n,r){var o=r.digestEffect,c=j,i=Object.keys(t);if(0!==i.length){var s=(0,a.l)(t,n);i.forEach((function(e){o(t[e],c,s[e],e)}))}else n((0,u.array)(t)?[]:{})},R[a.R]=function(e,t,n,r){var o=r.digestEffect,c=j,i=Object.keys(t),s=(0,u.array)(t)?(0,a.m)(i.length):{},f={},l=!1;i.forEach((function(e){var t=function(t,r){l||(r||(0,a.s)(t)?(n.cancel(),n(t,r)):(n.cancel(),l=!0,s[e]=t,n(s)))};t.cancel=a.t,f[e]=t})),n.cancel=function(){l||(l=!0,i.forEach((function(e){return f[e].cancel()})))},i.forEach((function(e){l||o(t[e],c,f[e],e)}))},R[a.C]=function(e,t,n,r){var o=t.context,c=t.fn,i=t.args,s=r.task;try{var f=c.apply(o,i);if((0,u.promise)(f))return void k(f,n);if((0,u.iterator)(f))return void V(e,f,s.context,j,(0,a.j)(c),!1,n);n(f)}catch(e){n(e,!0)}},R[a.a]=function(e,t,n){var r=t.context,o=t.fn,c=t.args;try{var a=function(e,t){(0,u.undef)(e)?n(t):n(e,!0)};o.apply(r,c.concat(a)),a.cancel&&(n.cancel=a.cancel)}catch(e){n(e,!0)}},R[a.F]=function(e,t,n,r){var o=t.context,c=t.fn,i=t.args,s=t.detached,f=r.task,l=function(e){
var t=e.context,n=e.fn,r=e.args;try{var o=n.apply(t,r);if((0,u.iterator)(o))return o;var c=!1;return(0,a.q)((function(e){return c?{value:e,done:!0}:(c=!0,{value:o,done:!(0,u.promise)(o)})}))}catch(e){return(0,a.q)((function(){throw e}))}}({context:o,fn:c,args:i}),d=function(e,t){return e.isSagaIterator?{name:e.meta.name}:(0,a.j)(t)}(l,c);v((function(){var t=V(e,l,f.context,j,d,s,void 0);s?n(t):t.isRunning()?(f.queue.addTask(t),n(t)):t.isAborted()?f.queue.abort(t.error()):n(t)}))},R[a.J]=function(e,t,n,r){var o=r.task,c=function(e,t){if(e.isRunning()){var n={task:o,cb:t};t.cancel=function(){e.isRunning()&&(0,a.r)(e.joiners,n)},e.joiners.push(n)}else e.isAborted()?t(e.error(),!0):t(e.result())};if((0,u.array)(t)){if(0===t.length)return void n([]);var i=(0,a.l)(t,n);t.forEach((function(e,t){c(e,i[t])}))}else c(t,n)},R[a.b]=function(e,t,n,o){var c=o.task;t===r.SELF_CANCELLATION?$(c):(0,u.array)(t)?t.forEach($):$(t),n()},R[a.S]=function(e,t,n){var r=t.selector,o=t.args;try{n(r.apply(void 0,[e.getState()].concat(o)))}catch(e){n(e,!0)}},R[a.d]=function(e,t,n){var r=t.pattern,o=T(t.buffer),c=x(r),u=function t(n){P(n)||e.channel.take(t,c),o.put(n)},a=o.close;o.close=function(){u.cancel(),a()},e.channel.take(u,c),n(o)},R[a.f]=function(e,t,n,r){n(r.task.isCancelled())},R[a.g]=function(e,t,n){t.flush(n)},R[a.G]=function(e,t,n,r){n(r.task.context[t])},R[a.h]=function(e,t,n,r){var o=r.task;(0,a.p)(o.context,t),n()},R);function _(e,t){return e+"?"+t}function L(e){var t=e.name,n=e.location;return n?t+"  "+_(n.fileName,n.lineNumber):t}function D(e){var t=(0,a.u)((function(e){return e.cancelledTasks}),e);return t.length?["Tasks cancelled due to error:"].concat(t).join("\n"):""}var q=null,B=[],F=function(e){e.crashedEffect=q,B.push(e)},I=function(){q=null,B.length=0},H=function(e){q=e},U=function(){var e,t,n=B[0],r=B.slice(1),o=n.crashedEffect?(e=n.crashedEffect,(t=(0,a.v)(e))?t.code+"  "+_(t.fileName,t.lineNumber):""):null;return["The above error occurred in task "+L(n.meta)+(o?" \n when executing effect "+o:"")].concat(r.map((function(e){return"    created by "+L(e.meta)})),[D(B)]).join("\n")};function z(e,t,n,o,c,u,i){var f;void 0===i&&(i=a.t);var l,d,p=0,v=null,y=[],m=Object.create(n),h=function(e,t,n){var r,o=[],c=!1;function u(e){t(),s(),n(e,!0)}function i(t){o.push(t),t.cont=function(i,s){c||((0,a.r)(o,t),t.cont=a.t,s?u(i):(t===e&&(r=i),o.length||(c=!0,n(r))))}}function s(){c||(c=!0,o.forEach((function(e){e.cont=a.t,e.cancel()})),o=[])}return i(e),{addTask:i,cancelAll:s,abort:u,getTasks:function(){return o}}}(t,(function(){y.push.apply(y,h.getTasks().map((function(e){return e.meta.name})))}),g);function g(t,n){if(n){if(p=2,F({meta:c,cancelledTasks:y}),S.isRoot){var o=U();I(),e.onError(t,{sagaStack:o})}d=t,v&&v.reject(t)}else t===r.TASK_CANCEL?p=1:1!==p&&(p=3),l=t,v&&v.resolve(t);S.cont(t,n),S.joiners.forEach((function(e){e.cb(t,n)})),S.joiners=null}var S=((f={})[r.TASK]=!0,f.id=o,f.meta=c,f.isRoot=u,f.context=m,f.joiners=[],f.queue=h,f.cancel=function(){0===p&&(p=1,h.cancelAll(),g(r.TASK_CANCEL,!1))},f.cont=i,f.end=g,
f.setContext=function(e){(0,a.p)(m,e)},f.toPromise=function(){return v||(v=s(),2===p?v.reject(d):0!==p&&v.resolve(l)),v.promise},f.isRunning=function(){return 0===p},f.isCancelled=function(){return 1===p||0===p&&1===t.status},f.isAborted=function(){return 2===p},f.result=function(){return l},f.error=function(){return d},f);return S}function V(e,t,n,o,c,i,s){var f=e.finalizeRunEffect((function(t,n,o){if((0,u.promise)(t))k(t,o);else if((0,u.iterator)(t))V(e,t,d.context,n,c,!1,o);else if(t&&t[r.IO]){(0,A[t.type])(e,t.payload,o,p)}else o(t)}));v.cancel=a.t;var l={meta:c,cancel:function(){0===l.status&&(l.status=1,v(r.TASK_CANCEL))},status:0},d=z(e,l,n,o,c,i,s),p={task:d,digestEffect:y};return s&&(s.cancel=d.cancel),v(),d;function v(e,n){try{var c;n?(c=t.throw(e),I()):(0,a.y)(e)?(l.status=1,v.cancel(),c=(0,u.func)(t.return)?t.return(r.TASK_CANCEL):{done:!0,value:r.TASK_CANCEL}):c=(0,a.z)(e)?(0,u.func)(t.return)?t.return():{done:!0}:t.next(e),c.done?(1!==l.status&&(l.status=3),l.cont(c.value)):y(c.value,o,v)}catch(e){if(1===l.status)throw e;l.status=2,l.cont(e,!0)}}function y(t,n,r,o){void 0===o&&(o="");var c,u=N();function i(n,o){c||(c=!0,r.cancel=a.t,e.sagaMonitor&&(o?e.sagaMonitor.effectRejected(u,n):e.sagaMonitor.effectResolved(u,n)),o&&H(t),r(n,o))}e.sagaMonitor&&e.sagaMonitor.effectTriggered({effectId:u,parentEffectId:n,label:o,effect:t}),i.cancel=a.t,r.cancel=function(){c||(c=!0,i.cancel(),i.cancel=a.t,e.sagaMonitor&&e.sagaMonitor.effectCancelled(u))},f(t,u,i)}}function W(e,t){var n=e.channel,r=void 0===n?O():n,o=e.dispatch,c=e.getState,u=e.context,i=void 0===u?{}:u,s=e.sagaMonitor,f=e.effectMiddlewares,l=e.onError,d=void 0===l?a.D:l;for(var p=arguments.length,y=new Array(p>2?p-2:0),m=2;m<p;m++)y[m-2]=arguments[m];var h=t.apply(void 0,y);var g,S=N();if(s&&(s.rootSagaStarted=s.rootSagaStarted||a.t,s.effectTriggered=s.effectTriggered||a.t,s.effectResolved=s.effectResolved||a.t,s.effectRejected=s.effectRejected||a.t,s.effectCancelled=s.effectCancelled||a.t,s.actionDispatched=s.actionDispatched||a.t,s.rootSagaStarted({effectId:S,saga:t,args:y})),f){var b=a.B.apply(void 0,f);g=function(e){return function(t,n,r){return b((function(t){return e(t,n,r)}))(t)}}}else g=a.H;var E={channel:r,dispatch:(0,a.E)(o),getState:c,sagaMonitor:s,onError:d,finalizeRunEffect:g};return v((function(){var e=V(E,h,i,S,(0,a.j)(t),!0,void 0);return s&&s.effectResolved(S,e),e}))}const K=function(e){var t,n=void 0===e?{}:e,r=n.context,u=void 0===r?{}:r,i=n.channel,s=void 0===i?O():i,f=n.sagaMonitor,l=(0,c.default)(n,["context","channel","sagaMonitor"]);function d(e){var n=e.getState,r=e.dispatch;return t=W.bind(null,(0,o.default)({},l,{context:u,channel:s,dispatch:r,getState:n,sagaMonitor:f})),function(e){return function(t){f&&f.actionDispatched&&f.actionDispatched(t);var n=e(t);return s.put(t),n}}}return d.run=function(){return t.apply(void 0,arguments)},d.setContext=function(e){(0,a.p)(u,e)},d},Y=K},171052:(e,t,n)=>{"use strict";var r=n(50959);var o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t
},c=r.useState,u=r.useEffect,a=r.useLayoutEffect,i=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var f="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=c({inst:{value:n,getSnapshot:t}}),o=r[0].inst,f=r[1];return a((function(){o.value=n,o.getSnapshot=t,s(o)&&f({inst:o})}),[e,n,t]),u((function(){return s(o)&&f({inst:o}),e((function(){s(o)&&f({inst:o})}))}),[e]),i(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:f},328283:(e,t,n)=>{"use strict";var r=n(50959),o=n(383363);var c="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},u=o.useSyncExternalStore,a=r.useRef,i=r.useEffect,s=r.useMemo,f=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var l=a(null);if(null===l.current){var d={hasValue:!1,value:null};l.current=d}else d=l.current;l=s((function(){function e(e){if(!i){if(i=!0,u=e,e=r(e),void 0!==o&&d.hasValue){var t=d.value;if(o(t,e))return a=t}return a=e}if(t=a,c(u,e))return t;var n=r(e);return void 0!==o&&o(t,n)?t:(u=e,a=n)}var u,a,i=!1,s=void 0===n?null:n;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]}),[t,n,r,o]);var p=u(e,l[0],l[1]);return i((function(){d.hasValue=!0,d.value=p}),[p]),f(p),p}},383363:(e,t,n)=>{"use strict";e.exports=n(171052)},34730:(e,t,n)=>{"use strict";e.exports=n(328283)},330950:(e,t,n)=>{"use strict";function r(e,t){if(null==e)return{};var n,r,o={},c=Object.keys(e);for(r=0;r<c.length;r++)n=c[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}n.d(t,{default:()=>r})},925931:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>r});let r=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);