(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2538,9216],{497754:(e,t)=>{var n;!function(){"use strict";var i={}.hasOwnProperty;function s(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var o=typeof n;if("string"===o||"number"===o)e.push(n);else if(Array.isArray(n)&&n.length){var a=s.apply(null,n);a&&e.push(a)}else if("object"===o)for(var r in n)i.call(n,r)&&n[r]&&e.push(r)}}return e.join(" ")}e.exports?(s.default=s,e.exports=s):void 0===(n=function(){return s}.apply(t,[]))||(e.exports=n)}()},262453:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},34869:e=>{e.exports={hidden:"hidden-DgcIT6Uz",fadeInWrapper:"fadeInWrapper-DgcIT6Uz"}},584928:e=>{e.exports={dialog:"dialog-aRAWUDhF",rounded:"rounded-aRAWUDhF",shadowed:"shadowed-aRAWUDhF",fullscreen:"fullscreen-aRAWUDhF",darker:"darker-aRAWUDhF",backdrop:"backdrop-aRAWUDhF"}},485862:e=>{e.exports={disableSelfPositioning:"disableSelfPositioning-dYiqkKAE"}},896108:e=>{e.exports={"tablet-normal-breakpoint":"(max-width: 768px)","small-height-breakpoint":"(max-height: 360px)","tablet-small-breakpoint":"(max-width: 440px)"}},577508:e=>{e.exports={dialog:"dialog-b8SxMnzX",wrapper:"wrapper-b8SxMnzX",separator:"separator-b8SxMnzX",bounded:"bounded-b8SxMnzX"}},710326:e=>{e.exports={"small-height-breakpoint":"(max-height: 360px)",container:"container-BZKENkhT",unsetAlign:"unsetAlign-BZKENkhT",title:"title-BZKENkhT",subtitle:"subtitle-BZKENkhT",textWrap:"textWrap-BZKENkhT",ellipsis:"ellipsis-BZKENkhT",close:"close-BZKENkhT",icon:"icon-BZKENkhT"}},513100:e=>{e.exports={"tablet-normal-breakpoint":"(max-width: 768px)","tooltip-offset":"20px",dialog:"dialog-qyCw0PaN",dragging:"dragging-qyCw0PaN",mobile:"mobile-qyCw0PaN",fullscreen:"fullscreen-qyCw0PaN",dialogAnimatedAppearance:"dialogAnimatedAppearance-qyCw0PaN",dialogAnimation:"dialogAnimation-qyCw0PaN",dialogTooltip:"dialogTooltip-qyCw0PaN"}},536204:e=>{e.exports={separator:"separator-Pf4rIzEt"}},993524:e=>{e.exports={linkItem:"linkItem-zMVwkifW"}},590854:e=>{e.exports={roundTabButton:"roundTabButton-JbssaNvk",disableFocusOutline:"disableFocusOutline-JbssaNvk",enableCursorPointer:"enableCursorPointer-JbssaNvk",large:"large-JbssaNvk",withStartIcon:"withStartIcon-JbssaNvk",iconOnly:"iconOnly-JbssaNvk",withEndIcon:"withEndIcon-JbssaNvk",startIconWrap:"startIconWrap-JbssaNvk",endIconWrap:"endIconWrap-JbssaNvk",small:"small-JbssaNvk",xsmall:"xsmall-JbssaNvk",primary:"primary-JbssaNvk",selected:"selected-JbssaNvk",disableActiveStateStyles:"disableActiveStateStyles-JbssaNvk",ghost:"ghost-JbssaNvk",fake:"fake-JbssaNvk",caret:"caret-JbssaNvk",visuallyHidden:"visuallyHidden-JbssaNvk"}},476912:e=>{e.exports={
scrollWrap:"scrollWrap-vgCB17hK",overflowScroll:"overflowScroll-vgCB17hK",roundTabs:"roundTabs-vgCB17hK",center:"center-vgCB17hK",overflowWrap:"overflowWrap-vgCB17hK",start:"start-vgCB17hK"}},149128:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},389986:(e,t,n)=>{"use strict";n.d(t,{CloseButton:()=>u});var i=n(50959),s=n(270762),o=n(117105),a=n(315130),r=n(238822),l=n(663346),c=n(534983);function d(e="large"){switch(e){case"large":return o;case"medium":default:return a;case"small":return r;case"xsmall":return l;case"xxsmall":return c}}const u=i.forwardRef(((e,t)=>i.createElement(s.NavButton,{...e,ref:t,icon:d(e.size)})))},270762:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var i=n(50959),s=n(497754),o=n(878112),a=(n(15378),n(262453));function r(e){const{size:t="large",preservePaddings:n,isLink:i,flipIconOnRtl:o,className:r}=e;return s(a["nav-button"],a[`size-${t}`],n&&a["preserve-paddings"],o&&a["flip-icon"],i&&a.link,r)}function l(e){const{children:t,icon:n}=e;return i.createElement(i.Fragment,null,i.createElement("span",{className:a.background}),i.createElement(o.Icon,{icon:n,className:a.icon,"aria-hidden":!0}),t&&i.createElement("span",{className:a["visually-hidden"]},t))}const c=(0,i.forwardRef)(((e,t)=>{const{icon:n,type:s="button",preservePaddings:o,flipIconOnRtl:a,size:c,"aria-label":d,...u}=e;return i.createElement("button",{...u,className:r({...e,children:d}),ref:t,type:s},i.createElement(l,{icon:n},d))}));c.displayName="NavButton";var d=n(591365),u=n(273388);(0,i.forwardRef)(((e,t)=>{const{icon:n,renderComponent:s,"aria-label":o,...a}=e,c=s??d.CustomComponentDefaultLink;return i.createElement(c,{...a,className:r({...e,children:o,isLink:!0}),reference:(0,u.isomorphicRef)(t)},i.createElement(l,{icon:n},o))})).displayName="NavAnchorButton"},15378:(e,t,n)=>{"use strict";var i,s,o,a;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(i||(i={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(s||(s={})),function(e){e.Brand="brand",e.Blue="blue",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(o||(o={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(a||(a={}))},380327:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>i});const i=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},409245:(e,t,n)=>{"use strict";function i(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>i})},591365:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>o});var i=n(50959),s=n(409245);function o(e){return i.createElement("a",{...(0,s.renameRef)(e)})}i.PureComponent},234539:(e,t,n)=>{"use strict";n.d(t,{CustomBehaviourContext:()=>i});const i=(0,
n(50959).createContext)({enableActiveStateStyles:!0});i.displayName="CustomBehaviourContext"},612554:(e,t,n)=>{"use strict";n.d(t,{useFadeInContext:()=>o});var i=n(50959);n(497754),n(34869);const s=i.createContext({children:{},setIsReady:()=>{}});function o(){const{setIsReady:e,children:t}=(0,i.useContext)(s),n=(0,i.useRef)((0,i.useId)());t[n.current]||(t[n.current]={isReady:!1});return(0,i.useCallback)((()=>{t[n.current].isReady=!0,e(Object.values(t).every((e=>e.isReady)))}),[t,e])}},80137:(e,t,n)=>{"use strict";n.d(t,{Dialog:()=>c});var i=n(50959),s=n(497754),o=n(682925),a=n(801808),r=n(800417),l=n(584928);class c extends i.PureComponent{constructor(){super(...arguments),this._manager=new a.OverlapManager,this._handleSlot=e=>{this._manager.setContainer(e)}}render(){const{rounded:e=!0,shadowed:t=!0,fullscreen:n=!1,darker:a=!1,className:c,backdrop:d,containerTabIndex:u=-1}=this.props,h=s(c,l.dialog,e&&l.rounded,t&&l.shadowed,n&&l.fullscreen,a&&l.darker),p=(0,r.filterDataProps)(this.props),m=this.props.style?{...this._createStyles(),...this.props.style}:this._createStyles();return i.createElement(i.Fragment,null,i.createElement(o.SlotContext.Provider,{value:this._manager},d&&i.createElement("div",{onClick:this.props.onClickBackdrop,className:l.backdrop}),i.createElement("div",{...p,className:h,style:m,ref:this.props.reference,onFocus:this.props.onFocus,onMouseDown:this.props.onMouseDown,onMouseUp:this.props.onMouseUp,onClick:this.props.onClick,onKeyDown:this.props.onKeyDown,tabIndex:u,"aria-label":this.props.containerAriaLabel},this.props.children)),i.createElement(o.Slot,{reference:this._handleSlot}))}_createStyles(){const{bottom:e,left:t,width:n,right:i,top:s,zIndex:o,height:a}=this.props;return{bottom:e,left:t,right:i,top:s,zIndex:o,maxWidth:n,height:a}}}},35574:(e,t,n)=>{"use strict";n.d(t,{useIsFirstRender:()=>s,useIsNonFirstRender:()=>o});var i=n(50959);function s(){const[e,t]=(0,i.useState)(!0);return(0,i.useEffect)((()=>{t(!1)}),[]),e}function o(){return!s()}},813550:(e,t,n)=>{"use strict";n.d(t,{useForceUpdate:()=>s});var i=n(50959);const s=()=>{const[,e]=(0,i.useReducer)((e=>e+1),0);return e}},718736:(e,t,n)=>{"use strict";n.d(t,{useFunctionalRefObject:()=>o});var i=n(50959),s=n(855393);function o(e){const t=(0,i.useMemo)((()=>function(e){const t=n=>{e(n),t.current=n};return t.current=null,t}((e=>{r.current(e)}))),[]),n=(0,i.useRef)(null),o=t=>{if(null===t)return a(n.current,t),void(n.current=null);n.current!==e&&(n.current=e,a(n.current,t))},r=(0,i.useRef)(o);return r.current=o,(0,s.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return r.current(t.current),()=>r.current(null)}),[e]),t}function a(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},855393:(e,t,n)=>{"use strict";n.d(t,{useIsomorphicLayoutEffect:()=>s});var i=n(50959);function s(e,t){("undefined"==typeof window?i.useEffect:i.useLayoutEffect)(e,t)}},185727:(e,t,n)=>{"use strict";n.d(t,{usePrevious:()=>s});var i=n(50959);function s(e){const t=(0,i.useRef)(null);return(0,i.useEffect)((()=>{t.current=e}),[e]),t.current}},
183787:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>s});var i=n(50959);const s=i.forwardRef(((e,t)=>{const{icon:n="",title:s,ariaLabel:o,ariaLabelledby:a,ariaHidden:r,...l}=e,c=!!(s||o||a);return i.createElement("span",{role:"img",...l,ref:t,"aria-label":o,"aria-labelledby":a,"aria-hidden":r||!c,title:s,dangerouslySetInnerHTML:{__html:n}})}))},878112:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>i.Icon});var i=n(183787)},672511:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>l});var i=n(50959),s=n(497754),o=n(843442),a=(n(715216),n(485862)),r=n.n(a);function l(e){const{ariaLabel:t,ariaLabelledby:n,className:a,style:l,size:c,id:d,disableSelfPositioning:u}=e;return i.createElement("div",{className:s(a,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${o.spinnerSizeMap[c||o.DEFAULT_SIZE]}`,u&&r().disableSelfPositioning),style:l,role:"progressbar",id:d,"aria-label":t,"aria-labelledby":n})}},865679:(e,t,n)=>{"use strict";n.d(t,{useTabsMainHandlers:()=>v});var i=n(50959),s=n(269842),o=n(118965),a=n(586240),r=n(671129);var l=n(429510),c=n(984164),d=n(772069),u=n(525388),h=n(865968),p=n(612554),m=n(35574),g=n(664332),f=n(185727);function v(e,t,n){const{id:v,items:b,activationType:_,orientation:y,disabled:w,onActivate:x,isActive:C,overflowBehaviour:S,enableActiveStateStyles:E,tablistLabelId:k,tablistLabel:D,preventDefaultIfKeyboardActionHandled:I,stopPropagationIfKeyboardActionHandled:B,keyboardNavigationLoop:A,defaultKeyboardFocus:N,focusableItemAttributes:T}=t,M=(0,o.useMobileTouchState)(),O=function(e){const t=(0,r.useSafeMatchMedia)(a["media-mf-phone-landscape"],!0),n=(0,o.useMobileTouchState)();return e??(n||!t?"scroll":"collapse")}(S),R=(0,i.useRef)(!1),F=(0,i.useCallback)((e=>e.id),[]),P=E??!M,z=(0,p.useFadeInContext)(),{visibleItems:L,hiddenItems:H,containerRefCallback:W,innerContainerRefCallback:K,moreButtonRef:U,setItemRef:G,itemsMeasurements:Q}=(0,l.useCollapsible)(b,F,C),V=(0,f.usePrevious)(Q.current?.containerWidth)??0,$=(0,m.useIsNonFirstRender)(),X=Q.current?.containerWidth??0;let q=!1;Q.current&&$&&(q=function(e,t,n,i,s){if("collapse"!==i)return!0;const o=function(e,t,n){const i=e.filter((e=>t.find((t=>t.id===e[0]))));return t.length>0?i[0][1]+n:0}(Array.from(e.widthsMap.entries()),t,s),a=e.moreButtonWidth??0;let r=function(e,t){return e.reduce(((e,n)=>e+(t.get(n.id)??0)),0)}(n,e.widthsMap);return r+=t.length>0?a:0,function(e,t,n,i){return 0!==e?t-n<e&&t-n>i:n<t}(o,e.containerWidth,r,s)}(Q.current,H,L,O,n.gap??0)||0===X);const J=(0,g.useResizeObserver)((([e])=>{const t=$&&0===V&&0===H.length;(q&&e.contentRect.width===V||t)&&z()})),Y="collapse"===O?L:b,Z=(0,i.useMemo)((()=>"collapse"===O?H:[]),[O,H]),j=(0,i.useCallback)((e=>Z.includes(e)),[Z]),{isOpened:ee,open:te,close:ne,onButtonClick:ie}=(0,d.useDisclosure)({id:v,disabled:w}),{tabsBindings:se,tablistBinding:oe,scrollWrapBinding:ae,onActivate:re,onHighlight:le,isHighlighted:ce}=(0,c.useTabs)({id:v,items:[...Y,...Z],activationType:_,orientation:y,disabled:w,tablistLabelId:k,tablistLabel:D,preventDefaultIfKeyboardActionHandled:I,scrollIntoViewOptions:n.scrollIntoViewOptions,
onActivate:x,isActive:C,isCollapsed:j,isRtl:n.isRtl,isDisclosureOpened:ee,isRadioGroup:n.isRadioGroup,stopPropagationIfKeyboardActionHandled:B,keyboardNavigationLoop:A,defaultKeyboardFocus:N,focusableItemAttributes:T}),de=Z.find(ce),ue=(0,i.useCallback)((()=>{const e=b.find(C);e&&le(e)}),[le,C,b]),he=(0,i.useCallback)((e=>se.find((t=>t.id===e.id))),[se]),pe=(0,i.useCallback)((()=>{ne(),ue(),R.current=!0}),[ne,ue]),me=(0,i.useCallback)((()=>{de&&(re(de),le(de,250))}),[re,le,de]);ae.ref=(0,u.useMergedRefs)([J,ae.ref,W]),oe.ref=(0,u.useMergedRefs)([oe.ref,K]),oe.onKeyDown=(0,s.createSafeMulticastEventHandler)((0,h.useKeyboardEventHandler)([(0,h.useKeyboardClose)(ee,pe),(0,h.useKeyboardActionHandler)([13,32],me,(0,i.useCallback)((()=>Boolean(de)),[de]))],I),oe.onKeyDown);const ge=(0,i.useCallback)((e=>{R.current=!0,ie(e)}),[R,ie]),fe=(0,i.useCallback)((e=>{e&&re(e)}),[re]);return(0,i.useEffect)((()=>{R.current?R.current=!1:(de&&!ee&&te(),!de&&ee&&ne())}),[de,ee,te,ne]),{enableActiveStateStyles:P,moreButtonRef:U,setItemRef:G,getBindings:he,handleMoreButtonClick:ge,handleCollapsedItemClick:fe,scrollWrapBinding:ae,overflowBehaviour:O,tablistBinding:oe,visibleTabs:Y,hiddenTabs:Z,handleActivate:re,isMobileTouch:M,getItemId:F,isDisclosureOpened:ee,isHighlighted:ce,closeDisclosure:ne}}},273388:(e,t,n)=>{"use strict";function i(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function s(e){return i([e])}n.d(t,{isomorphicRef:()=>s,mergeRefs:()=>i})},738036:(e,t,n)=>{"use strict";n.d(t,{OutsideEvent:()=>s});var i=n(908783);function s(e){const{children:t,...n}=e;return t((0,i.useOutsideEvent)(n))}},651674:(e,t,n)=>{"use strict";n.d(t,{createReactRoot:()=>u});var i=n(50959),s=n(632227),o=n(904237);const a=(0,i.createContext)({isOnMobileAppPage:()=>!1,isRtl:!1,locale:"en"});var r=n(69111),l=n(431520);const c={iOs:"old",android:"new",old:"old",new:"new",any:"any"};function d(e){const[t]=(0,i.useState)({isOnMobileAppPage:e=>(0,r.isOnMobileAppPage)(c[e]),isRtl:(0,l.isRtl)(),locale:window.locale});return i.createElement(a.Provider,{value:t},e.children)}function u(e,t,n="legacy"){const a=i.createElement(d,null,e);if("modern"===n){const e=(0,o.createRoot)(t);return e.render(a),{render(t){e.render(i.createElement(d,null,t))},unmount(){e.unmount()}}}return s.render(a,t),{render(e){s.render(i.createElement(d,null,e),t)},unmount(){s.unmountComponentAtNode(t)}}}},996038:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>s});var i=n(896108);const s={SmallHeight:i["small-height-breakpoint"],TabletSmall:i["tablet-small-breakpoint"],TabletNormal:i["tablet-normal-breakpoint"]}},533408:(e,t,n)=>{"use strict";n.d(t,{AdaptivePopupDialog:()=>B});var i=n(50959),s=n(650151),o=n(497754),a=n.n(o),r=n(180185),l=n(431520),c=n(698043),d=n(59216),u=n(494707),h=n(996038),p=n(930052),m=n(910549);var g=n(206594),f=n(559410),v=n(609838),b=n(389986),_=n(190410),y=n(710326);function w(e){
const{titleId:t,title:s,titleTextWrap:o=!1,subtitle:r,showCloseIcon:l=!0,onClose:c,onCloseButtonKeyDown:d,renderBefore:u,renderAfter:h,draggable:p,className:m,unsetAlign:g,closeAriaLabel:f=v.t(null,void 0,n(47742)),closeButtonReference:w}=e,[x,C]=(0,i.useState)(!1);return i.createElement(_.DialogHeaderContext.Provider,{value:{setHideClose:C}},i.createElement("div",{className:a()(y.container,m,(r||g)&&y.unsetAlign)},u,i.createElement("div",{id:t,className:y.title,"data-dragg-area":p},i.createElement("div",{className:a()(o?y.textWrap:y.ellipsis)},s),r&&i.createElement("div",{className:a()(y.ellipsis,y.subtitle)},r)),h,l&&!x&&i.createElement(b.CloseButton,{className:y.close,"data-name":"close","aria-label":f,onClick:c,onKeyDown:d,ref:w,size:"medium",preservePaddings:!0})))}var x=n(273388),C=n(800417),S=n(924910),E=n(440891),k=n(577508);const D={vertical:20},I={vertical:0};class B extends i.PureComponent{constructor(){super(...arguments),this._controller=null,this._reference=null,this._orientationMediaQuery=null,this._embedResizerOverridesEnabled=E.enabled("embed_resizer_overrides"),this._titleId=`title_${(0,S.randomHash)()}`,this._renderChildren=(e,t)=>(this._controller=e,this.props.render({requestResize:this._requestResize,centerAndFit:this._centerAndFit,isSmallWidth:t})),this._handleReference=e=>this._reference=e,this._handleCloseBtnClick=()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleClose()},this._handleClose=()=>{this.props.onClose()},this._handleOpen=()=>{void 0!==this.props.onOpen&&this.props.isOpened&&this.props.onOpen(this.props.fullScreen||window.matchMedia(h.DialogBreakpoints.TabletSmall).matches)},this._handleKeyDown=e=>{if(!e.defaultPrevented){if(this.props.onKeyDown&&this.props.onKeyDown(e),27===(0,r.hashFromEvent)(e)){if(e.defaultPrevented)return;if(this.props.forceCloseOnEsc&&this.props.forceCloseOnEsc())return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose();const{activeElement:n}=document;if(null!==n){if(e.preventDefault(),"true"===(t=n).getAttribute("data-haspopup")&&"true"!==t.getAttribute("data-expanded"))return void this._handleClose();const i=this._reference;if(null!==i&&(0,c.isTextEditingField)(n))return void i.focus();if(i?.contains(n))return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose()}}var t,n;(function(e){if("function"==typeof e)return e();return Boolean(e)})(this.props.disableTabNavigationContainment)||(n=e,[9,r.Modifiers.Shift+9].includes((0,r.hashFromEvent)(n))&&n.stopPropagation())}},this._requestResize=()=>{null!==this._controller&&this._controller.recalculateBounds()},this._centerAndFit=()=>{null!==this._controller&&this._controller.centerAndFit()},this._calculatePositionWithOffsets=(e,t)=>{const n=(0,s.ensureDefined)(this.props.fullScreenViewOffsets).value();return{top:n.top,left:(0,l.isRtl)()?-n.right:n.left,width:t.clientWidth-n.left-n.right,height:t.clientHeight-n.top-n.bottom}}}componentDidMount(){
this.props.ignoreClosePopupsAndDialog||f.subscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),this._handleOpen(),void 0!==this.props.onOpen&&(this._orientationMediaQuery=window.matchMedia("(orientation: portrait)"),this._orientationMediaQuery.addEventListener("change",this._handleOpen)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.subscribe(this._requestResize);const{backdrop:e,draggable:t=!e,centerOnResize:n=!t}=this.props;n&&window.addEventListener("resize",this._centerAndFit)}componentWillUnmount(){this.props.ignoreClosePopupsAndDialog||f.unsubscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),null!==this._orientationMediaQuery&&this._orientationMediaQuery.removeEventListener("change",this._handleOpen),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.unsubscribe(this._requestResize),window.removeEventListener("resize",this._centerAndFit)}focus(){(0,s.ensureNotNull)(this._reference).focus()}getElement(){return this._reference}contains(e){return this._reference?.contains(e)??!1}render(){const{className:e,wrapperClassName:t,headerClassName:n,isOpened:s,title:o,titleTextWrap:r,dataName:l,onClickOutside:c,additionalElementPos:g,additionalHeaderElement:f,backdrop:v,shouldForceFocus:b=!0,shouldReturnFocus:_,onForceFocus:y,showSeparator:S,subtitle:E,draggable:B=!v,fullScreen:A=!1,showCloseIcon:N=!0,rounded:T=!0,isAnimationEnabled:M,growPoint:O,dialogTooltip:R,unsetHeaderAlign:F,onDragStart:P,dataDialogName:z,closeAriaLabel:L,containerAriaLabel:H,reference:W,containerTabIndex:K,closeButtonReference:U,onCloseButtonKeyDown:G,shadowed:Q,fullScreenViewOffsets:V,fixedBody:$,onClick:X}=this.props,q="after"!==g?f:void 0,J="after"===g?f:void 0,Y="string"==typeof o?o:z||"",Z=(0,C.filterDataProps)(this.props),j=(0,x.mergeRefs)([this._handleReference,W]);return i.createElement(p.MatchMedia,{rule:h.DialogBreakpoints.SmallHeight},(g=>i.createElement(p.MatchMedia,{rule:h.DialogBreakpoints.TabletSmall},(h=>i.createElement(d.PopupDialog,{rounded:!(h||A)&&T,className:a()(k.dialog,A&&V&&k.bounded,e),isOpened:s,reference:j,onKeyDown:this._handleKeyDown,onClickOutside:c,onClickBackdrop:c,fullscreen:h||A,guard:g?I:D,boundByScreen:h||A,shouldForceFocus:b,onForceFocus:y,shouldReturnFocus:_,backdrop:v,draggable:B,isAnimationEnabled:M,growPoint:O,name:this.props.dataName,dialogTooltip:R,onDragStart:P,containerAriaLabel:H,containerTabIndex:K,calculateDialogPosition:A&&V?this._calculatePositionWithOffsets:void 0,shadowed:Q,fixedBody:$,onClick:X,...Z},i.createElement("div",{role:"dialog","aria-labelledby":void 0!==o?this._titleId:void 0,className:a()(k.wrapper,t),"data-name":l,"data-dialog-name":Y},void 0!==o&&i.createElement(w,{draggable:B&&!(h||A),onClose:this._handleCloseBtnClick,renderAfter:J,renderBefore:q,subtitle:E,title:o,titleId:this._titleId,titleTextWrap:r,showCloseIcon:N,className:n,unsetAlign:F,closeAriaLabel:L,closeButtonReference:U,onCloseButtonKeyDown:G}),S&&i.createElement(u.Separator,{className:k.separator
}),i.createElement(m.PopupContext.Consumer,null,(e=>this._renderChildren(e,h||A)))))))))}}},190410:(e,t,n)=>{"use strict";n.d(t,{DialogHeaderContext:()=>i});const i=n(50959).createContext({setHideClose:()=>{}})},910549:(e,t,n)=>{"use strict";n.d(t,{PopupContext:()=>i});const i=n(50959).createContext(null)},59216:(e,t,n)=>{"use strict";n.d(t,{PopupDialog:()=>A});var i,s=n(50959),o=n(497754),a=n(650151),r=n(80137),l=n(874485),c=n(738036),d=n(822960),u=n(32556);function h(e,t,n,i){return e+t>i&&(e=i-t),e<n&&(e=n),e}function p(e){return{x:(0,d.clamp)(e.x,20,document.documentElement.clientWidth-20),y:(0,d.clamp)(e.y,20,window.innerHeight-20)}}function m(e){return{x:e.clientX,y:e.clientY}}function g(e){return{x:e.touches[0].clientX,y:e.touches[0].clientY}}!function(e){e[e.MouseGuardZone=20]="MouseGuardZone"}(i||(i={}));class f{constructor(e,t,n={boundByScreen:!0}){this._drag=null,this._canBeTouchClick=!1,this._frame=null,this._onMouseDragStart=e=>{if(0!==e.button||this._isTargetNoDraggable(e))return;e.preventDefault(),document.addEventListener("mousemove",this._onMouseDragMove),document.addEventListener("mouseup",this._onMouseDragEnd);const t=p(m(e));this._dragStart(t)},this._onTouchDragStart=e=>{if(this._isTargetNoDraggable(e))return;this._canBeTouchClick=!0,e.preventDefault(),this._header.addEventListener("touchmove",this._onTouchDragMove,{passive:!1});const t=p(g(e));this._dragStart(t)},this._onMouseDragEnd=e=>{e.target instanceof Node&&this._header.contains(e.target)&&e.preventDefault(),document.removeEventListener("mousemove",this._onMouseDragMove),document.removeEventListener("mouseup",this._onMouseDragEnd),this._onDragStop()},this._onTouchDragEnd=e=>{this._header.removeEventListener("touchmove",this._onTouchDragMove),this._onDragStop(),this._canBeTouchClick&&(this._canBeTouchClick=!1,function(e){if(e instanceof SVGElement){const t=document.createEvent("SVGEvents");t.initEvent("click",!0,!0),e.dispatchEvent(t)}e instanceof HTMLElement&&e.click()}(e.target))},this._onMouseDragMove=e=>{const t=p(m(e));this._dragMove(t)},this._onTouchDragMove=e=>{this._canBeTouchClick=!1,e.preventDefault();const t=p(g(e));this._dragMove(t)},this._onDragStop=()=>{this._drag=null,this._header.classList.remove("dragging"),this._options.onDragEnd&&this._options.onDragEnd()},this._dialog=e,this._header=t,this._options=n,this._header.addEventListener("mousedown",this._onMouseDragStart),this._header.addEventListener("touchstart",this._onTouchDragStart),this._header.addEventListener("touchend",this._onTouchDragEnd)}destroy(){null!==this._frame&&cancelAnimationFrame(this._frame),this._header.removeEventListener("mousedown",this._onMouseDragStart),document.removeEventListener("mouseup",this._onMouseDragEnd),this._header.removeEventListener("touchstart",this._onTouchDragStart),this._header.removeEventListener("touchend",this._onTouchDragEnd),document.removeEventListener("mouseleave",this._onMouseDragEnd)}updateOptions(e){this._options=e}_dragStart(e){const t=this._dialog.getBoundingClientRect();this._drag={startX:e.x,startY:e.y,finishX:e.x,
finishY:e.y,dialogX:t.left,dialogY:t.top};const n=Math.round(t.left),i=Math.round(t.top);this._dialog.style.transform=`translate(${n}px, ${i}px)`,this._header.classList.add("dragging"),this._options.onDragStart&&this._options.onDragStart()}_dragMove(e){if(this._drag){if(this._drag.finishX=e.x,this._drag.finishY=e.y,null!==this._frame)return;this._frame=requestAnimationFrame((()=>{if(this._drag){const t=e.x-this._drag.startX,n=e.y-this._drag.startY;this._moveDialog(this._drag.dialogX+t,this._drag.dialogY+n)}this._frame=null}))}}_moveDialog(e,t){const n=this._dialog.getBoundingClientRect(),{boundByScreen:i}=this._options,s=h(e,n.width,i?0:-1/0,i?window.innerWidth:1/0),o=h(t,n.height,i?0:-1/0,i?window.innerHeight:1/0);this._dialog.style.transform=`translate(${Math.round(s)}px, ${Math.round(o)}px)`}_isTargetNoDraggable(e){return e.target instanceof Element&&null!==e.target.closest("[data-disable-drag]")}}const v={vertical:0};var b,_=n(8361),y=n(910549),w=n(285089),x=n(671945),C=n(924910);!function(e){e.Open="dialog-open",e.Close="dialog-close",e.FullscreenOn="dialog-fullscreen-on",e.FullscreenOff="dialog-fullscreen-off"}(b||(b={}));const S=(0,x.getLogger)("DialogEventDispatcher");class E{constructor(){this._openSessionId=null}dispatch(e){if("dialog-open"===e){if(null!==this._openSessionId)return void S.logError("Multiple calls to open dialog");this._openSessionId=(0,C.randomHash)()}null!==this._openSessionId?(window.dispatchEvent(new CustomEvent(e,{bubbles:!0,detail:{dialogSessionId:this._openSessionId}})),"dialog-close"===e&&(this._openSessionId=null)):S.logError("Empty open dialog session id")}}var k=n(69111),D=(n(440891),n(513100));D["tooltip-offset"];const I=class{constructor(e,t){this._frame=null,this._isFullscreen=!1,this._handleResize=()=>{null===this._frame&&(this._frame=requestAnimationFrame((()=>{this.recalculateBounds(),this._frame=null})))},this._dialog=e,this._guard=t.guard||v,this._calculateDialogPosition=t.calculateDialogPosition,this._initialHeight=e.style.height,window.addEventListener("resize",this._handleResize)}updateOptions(e){this._guard=e.guard||v,this._calculateDialogPosition=e.calculateDialogPosition}setFullscreen(e){this._isFullscreen!==e&&(this._isFullscreen=e,this.recalculateBounds())}centerAndFit(){const{x:e,y:t}=this.getDialogsTopLeftCoordinates(),n=this._calcAvailableHeight(),i=this._calcDialogHeight();if(n===i)if(this._calculateDialogPosition){const{left:e,top:t}=this._calculateDialogPosition(this._dialog,document.documentElement,this._guard);this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else this._dialog.style.height=i+"px";this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${e}px, ${t}px)`}getDialogsTopLeftCoordinates(){const{clientWidth:e,clientHeight:t}=this._getClientDimensions(),n=this._calcDialogHeight(),i=e/2-this._dialog.clientWidth/2,s=t/2-n/2+this._getTopOffset();return{x:Math.round(i),y:Math.round(s)}}recalculateBounds(){
const{clientWidth:e,clientHeight:t}=this._getClientDimensions(),{vertical:n}=this._guard,i=this._calculateDialogPosition?.(this._dialog,{clientWidth:e,clientHeight:t},{vertical:n});if(this._isFullscreen){if(this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.width="100%",this._dialog.style.height="100%",this._dialog.style.transform="none",i){const{left:e,top:t,width:n,height:s}=i;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`,n&&(this._dialog.style.width=`${n}px`,this._dialog.style.minWidth="unset"),s&&(this._dialog.style.height=`${s}px`,this._dialog.style.minHeight="unset")}}else if(i){const{left:e,top:t}=i;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else{this._dialog.style.width="",this._dialog.style.height="";const i=this._dialog.getBoundingClientRect(),s=t-2*n,o=h(i.left,i.width,0,e),a=h(i.top,i.height,n,t);this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${Math.round(o)}px, ${Math.round(a)}px)`,this._dialog.style.height=s<i.height?s+"px":this._initialHeight}}destroy(){window.removeEventListener("resize",this._handleResize),null!==this._frame&&(cancelAnimationFrame(this._frame),this._frame=null)}_getClientDimensions(){return{clientHeight:document.documentElement.clientHeight,clientWidth:document.documentElement.clientWidth}}_getTopOffset(){return 0}_calcDialogHeight(){const e=this._calcAvailableHeight();return e<this._dialog.clientHeight?e:this._dialog.clientHeight}_calcAvailableHeight(){return this._getClientDimensions().clientHeight-2*this._guard.vertical}};class B extends s.PureComponent{constructor(e){super(e),this._dialog=null,this._cleanUpFunctions=[],this._prevActiveElement=null,this._eventDispatcher=new E,this._handleDialogRef=e=>{const{reference:t}=this.props;this._dialog=e,"function"==typeof t&&t(e)},this._handleFocus=()=>{this._moveToTop()},this._handleMouseDown=e=>{this._moveToTop()},this._handleTouchStart=e=>{this._moveToTop()},this.state={canFitTooltip:!1},this._prevActiveElement=document.activeElement}render(){return s.createElement(y.PopupContext.Provider,{value:this},s.createElement(c.OutsideEvent,{mouseDown:!0,touchStart:!0,handler:this.props.onClickOutside},(e=>s.createElement("div",{ref:e,"data-outside-boundary-for":this.props.name,onFocus:this._handleFocus,onMouseDown:this._handleMouseDown,onTouchStart:this._handleTouchStart,"data-dialog-name":this.props["data-dialog-name"],"data-tooltip-show-on-focus":"true"},s.createElement(r.Dialog,{style:this._applyAnimationCSSVariables(),...this.props,reference:this._handleDialogRef,className:o(D.dialog,(0,k.isOnMobileAppPage)("any")&&!this.props.noMobileAppShadows&&D.mobile,this.props.fullscreen&&D.fullscreen,this.props.className)},!1,this.props.children)))))}componentDidMount(){const{draggable:e,boundByScreen:t,onDragStart:n}=this.props,i=(0,a.ensureNotNull)(this._dialog);if(this._eventDispatcher.dispatch("dialog-open"),e){const e=i.querySelector("[data-dragg-area]");if(e&&e instanceof HTMLElement){
const s=new f(i,e,{boundByScreen:Boolean(t),onDragStart:n});this._cleanUpFunctions.push((()=>s.destroy())),this._drag=s}}this.props.autofocus&&!i.contains(document.activeElement)&&i.focus(),(this._isFullScreen()||this.props.fixedBody)&&(0,w.setFixedBodyState)(!0);const{guard:s,calculateDialogPosition:o}=this.props;if(this.props.resizeHandler)this._resize=this.props.resizeHandler;else{const e=new I(i,{guard:s,calculateDialogPosition:o});this._cleanUpFunctions.push((()=>e.destroy())),this._resize=e}if(this._isFullScreen()&&this._eventDispatcher.dispatch("dialog-fullscreen-on"),this.props.isAnimationEnabled&&this.props.growPoint&&this._applyAppearanceAnimation(this.props.growPoint),this.props.centeredOnMount&&this._resize.centerAndFit(),this._resize.setFullscreen(this._isFullScreen()),this.props.shouldForceFocus){if(this.props.onForceFocus)return void this.props.onForceFocus(i);i.focus()}if(!i.contains(document.activeElement)){const e=function(e){const t=e.querySelector("[autofocus]:not([disabled])");if(t)return t;if(e.tabIndex>=0)return e;const n=(0,u.getActiveElementSelectors)(),i=Array.from(e.querySelectorAll(n)).filter((0,u.createScopedVisibleElementFilter)(e));let s=Number.NEGATIVE_INFINITY,o=null;for(let e=0;e<i.length;e++){const t=i[e],n=t.getAttribute("tabindex");if(null!==n){const e=parseInt(n,10);!isNaN(e)&&e>s&&(s=e,o=t)}}return o}(i);e instanceof HTMLElement&&e.focus()}}componentDidUpdate(e){const t=e.fullscreen;if(this._resize){const{guard:e,calculateDialogPosition:t}=this.props;this._resize.updateOptions({guard:e,calculateDialogPosition:t}),this._resize.setFullscreen(this._isFullScreen())}if(this._drag&&this._drag.updateOptions({boundByScreen:Boolean(this.props.boundByScreen),onDragStart:this.props.onDragStart}),e.fullscreen!==this.props.fullscreen){const e=this.props.fullscreen;e&&!t?this._eventDispatcher.dispatch("dialog-fullscreen-on"):!e&&t&&this._eventDispatcher.dispatch("dialog-fullscreen-off")}}componentWillUnmount(){if(this.props.shouldReturnFocus&&this._prevActiveElement&&document.body.contains(this._prevActiveElement)&&(null===document.activeElement||document.activeElement===document.body||this._dialog?.contains(document.activeElement)))try{setTimeout((()=>{this._prevActiveElement.focus({preventScroll:!0})}))}catch{}for(const e of this._cleanUpFunctions)e();(this._isFullScreen()||this.props.fixedBody)&&(0,w.setFixedBodyState)(!1),this._isFullScreen()&&this._eventDispatcher.dispatch("dialog-fullscreen-off"),this._eventDispatcher.dispatch("dialog-close")}focus(){this._dialog&&this._dialog.focus()}centerAndFit(){this._resize&&this._resize.centerAndFit()}recalculateBounds(){this._resize&&this._resize.recalculateBounds()}_moveToTop(){this.props.isZIndexFixed||null!==this.context&&this.context.moveToTop()}_applyAnimationCSSVariables(){return{"--animationTranslateStartX":null,"--animationTranslateStartY":null,"--animationTranslateEndX":null,"--animationTranslateEndY":null}}_applyAppearanceAnimation(e){if(this._resize&&this._dialog){const{x:t,y:n}=e,{x:i,y:s}=this._resize.getDialogsTopLeftCoordinates()
;this._dialog.style.setProperty("--animationTranslateStartX",`${t}px`),this._dialog.style.setProperty("--animationTranslateStartY",`${n}px`),this._dialog.style.setProperty("--animationTranslateEndX",`${i}px`),this._dialog.style.setProperty("--animationTranslateEndY",`${s}px`),this._dialog.classList.add(D.dialogAnimatedAppearance)}}_handleTooltipFit(){0}_isFullScreen(){return Boolean(this.props.fullscreen)}}B.contextType=_.PortalContext,B.defaultProps={boundByScreen:!0,draggable:!0,centeredOnMount:!0,shouldReturnFocus:!0};const A=(0,l.makeOverlapable)(B,!0)},904925:(e,t,n)=>{"use strict";n.d(t,{PopupMenuDisclosureView:()=>d});var i=n(50959),s=n(624216),o=n(650151);const a={x:0,y:0};function r(e,t,n){return(0,i.useCallback)((()=>function(e,t,{x:n=a.x,y:i=a.y}=a){const s=(0,o.ensureNotNull)(e).getBoundingClientRect(),r={x:s.left+n,y:s.top+s.height+i,indentFromWindow:{top:4,bottom:4,left:4,right:4}};return t&&(r.overrideWidth=s.width),r}(e.current,t,n)),[e,t])}var l=n(586240);const c=parseInt(l["size-header-height"]);function d(e){const{button:t,popupChildren:n,buttonRef:o,listboxId:a,listboxClassName:l,listboxTabIndex:d,matchButtonAndListboxWidths:u,isOpened:h,scrollWrapReference:p,listboxReference:m,onClose:g,onOpen:f,onListboxFocus:v,onListboxBlur:b,onListboxKeyDown:_,listboxAria:y,repositionOnScroll:w=!0,closeOnHeaderOverlap:x=!1,popupPositionCorrection:C={x:0,y:0},popupPosition:S}=e,E=r(o,u,C),k=x?c:0;return i.createElement(i.Fragment,null,t,i.createElement(s.PopupMenu,{...y,id:a,className:l,tabIndex:d,isOpened:h,position:S||E,repositionOnScroll:w,onClose:g,onOpen:f,doNotCloseOn:o.current,reference:m,scrollWrapReference:p,onFocus:v,onBlur:b,onKeyDown:_,closeOnScrollOutsideOffset:k},n))}},585938:(e,t,n)=>{"use strict";n.d(t,{useForceUpdate:()=>i.useForceUpdate});var i=n(813550)},297265:(e,t,n)=>{"use strict";n.d(t,{useWatchedValueReadonly:()=>s});var i=n(50959);const s=(e,t=!1,n=[])=>{const s="watchedValue"in e?e.watchedValue:void 0,o="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[a,r]=(0,i.useState)(s?s.value():o);return(t?i.useLayoutEffect:i.useEffect)((()=>{if(s){r(s.value());const e=e=>r(e);return s.subscribe(e),()=>s.unsubscribe(e)}return()=>{}}),[s,...n]),a}},494707:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>a});var i=n(50959),s=n(497754),o=n(536204);function a(e){return i.createElement("div",{className:s(o.separator,e.className)})}},874485:(e,t,n)=>{"use strict";n.d(t,{makeOverlapable:()=>o});var i=n(50959),s=n(8361);function o(e,t){return class extends i.PureComponent{render(){const{isOpened:n,root:o}=this.props;if(!n)return null;const a=i.createElement(e,{...this.props,ref:this.props.componentRef,zIndex:150});return"parent"===o?a:i.createElement(s.Portal,{shouldTrapFocus:t},a)}}}},686870:(e,t,n)=>{"use strict";n.d(t,{RoundButtonTabs:()=>L});var i=n(50959),s=n(497754),o=n(609838),a=n(431520),r=n(865679),l=n(540801),c=n(273388),d=n(234539),u=n(878112),h=n(602948),p=n(590854);const m="xsmall",g="primary";function f(e){const t=(0,
i.useContext)(d.CustomBehaviourContext),{size:n="xsmall",variant:o="primary",active:a,fake:r,startIcon:l,endIcon:c,showCaret:u,iconOnly:h,anchor:m,enableActiveStateStyles:g=t.enableActiveStateStyles,disableFocusOutline:f=!1,tooltip:v}=e;return s(p.roundTabButton,p[n],p[o],l&&p.withStartIcon,(c||u)&&p.withEndIcon,h&&p.iconOnly,a&&p.selected,r&&p.fake,m&&p.enableCursorPointer,!g&&p.disableActiveStateStyles,f&&p.disableFocusOutline,v&&"apply-common-tooltip")}function v(e){const{startIcon:t,endIcon:n,showCaret:o,iconOnly:a,children:r}=e;return i.createElement(i.Fragment,null,t&&i.createElement(u.Icon,{icon:t,className:p.startIconWrap,"aria-hidden":!0}),r&&i.createElement("span",{className:s(p.content,a&&p.visuallyHidden)},r),(!a&&n||o)&&i.createElement(b,{icon:n,showCaret:o}))}function b(e){const{icon:t,showCaret:n}=e;return i.createElement(u.Icon,{className:s(p.endIconWrap,n&&p.caret),icon:n?h:t,"aria-hidden":!0})}const _=(0,i.forwardRef)(((e,t)=>{const{id:n,size:s,variant:o,active:a,fake:r,startIcon:l,endIcon:c,showCaret:d,iconOnly:u,children:h,enableActiveStateStyles:p,disableFocusOutline:m,tooltip:g,...b}=e;return i.createElement("button",{...b,id:n,ref:t,"data-tooltip":g,className:f({size:s,variant:o,active:a,fake:r,startIcon:l,endIcon:c,showCaret:d,iconOnly:u,enableActiveStateStyles:p,disableFocusOutline:m,tooltip:g})},i.createElement(v,{startIcon:l,endIcon:c,showCaret:d,iconOnly:u},h))}));_.displayName="RoundTabsBaseButton";const y=(0,i.createContext)({size:"small",variant:"primary",isHighlighted:!1,isCollapsed:!1,disabled:!1});function w(e){const{item:t,highlighted:n,handleItemRef:s,reference:o,onClick:a,"aria-disabled":r,...l}=e,d=(0,i.useCallback)((e=>{l.disabled&&e.preventDefault(),a&&a(t)}),[a,t,l.disabled]),u=(0,i.useCallback)((e=>{s&&s(t,e),(0,c.isomorphicRef)(o)(e)}),[t,s]),h={size:l.size??m,variant:l.variant??g,isHighlighted:Boolean(l.active),isCollapsed:!1,disabled:l.disabled??!1};return i.createElement(_,{...l,id:t.id,onClick:d,ref:u,startIcon:t.startIcon,endIcon:t.endIcon,tooltip:t.tooltip,"aria-label":"radio"===l.role?t.children:void 0},i.createElement(y.Provider,{value:h},t.children))}var x=n(525388),C=n(192063),S=n(904925),E=n(722426),k=n(789882),D=n(602057),I=n(993524);function B(e){const{disabled:t,isOpened:n,enableActiveStateStyles:s,disableFocusOutline:o,fake:a,items:r,buttonText:l,buttonPreset:c="text",buttonRef:d,size:h,variant:p,isAnchorTabs:m,isHighlighted:g,onButtonClick:f,onItemClick:v,onClose:b}=e,y=(0,i.useRef)(null),w=(0,x.useMergedRefs)([d,y]),B="text"===c?void 0:"xsmall"===h?k:D;return i.createElement(S.PopupMenuDisclosureView,{buttonRef:y,listboxTabIndex:-1,isOpened:n,onClose:b,listboxAria:{"aria-hidden":!0},button:i.createElement(_,{"aria-hidden":!0,disabled:t,active:n,onClick:f,ref:w,tabIndex:-1,size:h,variant:p,startIcon:B,showCaret:"text"===c,iconOnly:"meatballs"===c,enableActiveStateStyles:s,disableFocusOutline:o,fake:a},l),popupChildren:i.createElement(i.Fragment,null,"meatballs"===c&&i.createElement(E.ToolWidgetMenuSummary,null,l),r.map((e=>i.createElement(C.PopupMenuItem,{
key:e.id,className:m?I.linkItem:void 0,onClick:v,onClickArg:e,isActive:g(e),label:i.createElement(A,{isHighlighted:g(e),size:h,variant:p,disabled:e.disabled},e.children),isDisabled:e.disabled,link:"href"in e?e.href:void 0,rel:"rel"in e?e.rel:void 0,target:"target"in e?e.target:void 0,icon:e.startIcon,toolbox:e.endIcon&&i.createElement(u.Icon,{icon:e.endIcon}),renderComponent:"renderComponent"in e?e.renderComponent:void 0,dontClosePopup:!0}))))})}function A(e){const{isHighlighted:t,size:n,variant:s,children:o,disabled:a}=e,r={size:n??m,variant:s??g,isHighlighted:t,isCollapsed:!0,disabled:a??!1};return i.createElement(y.Provider,{value:r},o)}n(671129),n(118965),n(586240);var N,T,M,O,R=n(476912);function F(e){const{overflowBehaviour:t}=e;return s(R.scrollWrap,"scroll"===t&&R.overflowScroll,"wrap"===t&&R.overflowWrap)}function P(e){const{align:t="start"}=e;return s(R.roundTabs,R[t])}function z(e){const{children:t,disabled:c,moreButtonText:d=o.t(null,void 0,n(437117)),moreButtonPreset:u,className:h,size:p,variant:m,align:g,style:f={},"data-name":v,isRadioGroup:b,"aria-controls":_}=e,y=function(e="xsmall"){switch(e){case"small":return 8;case"xsmall":return 4;default:return 16}}(p),{enableActiveStateStyles:x,moreButtonRef:C,setItemRef:S,getBindings:E,handleMoreButtonClick:k,handleCollapsedItemClick:D,scrollWrapBinding:I,overflowBehaviour:A,tablistBinding:N,visibleTabs:T,hiddenTabs:M,handleActivate:O,isMobileTouch:R,getItemId:z,isDisclosureOpened:L,isHighlighted:H,closeDisclosure:W}=(0,r.useTabsMainHandlers)(l.TabNames.RoundButtonTabs,e,{isRtl:a.isRtl,scrollIntoViewOptions:{additionalScroll:y},isRadioGroup:b,gap:y});return i.createElement("div",{...I,className:s(F({overflowBehaviour:A}),h),style:{...f,"--ui-lib-round-tabs-gap":`${y}px`},"data-name":v},i.createElement("div",{...N,className:P({align:g,overflowBehaviour:A})},T.map((e=>i.createElement(w,{...E(e),key:e.id,item:e,onClick:()=>O(e),variant:m,size:p,enableActiveStateStyles:x,disableFocusOutline:R,reference:S(z(e)),...e.dataId&&{"data-id":e.dataId},"aria-controls":_}))),M.map((e=>i.createElement(w,{...E(e),key:e.id,item:e,variant:m,size:p,reference:S(z(e)),"aria-controls":_,fake:!0}))),i.createElement(B,{disabled:c,isOpened:L,items:M,buttonText:d,buttonPreset:u,buttonRef:C,isHighlighted:H,onButtonClick:k,onItemClick:D,onClose:W,variant:m,size:p,enableActiveStateStyles:x,disableFocusOutline:R,fake:0===M.length}),t))}function L(e){const{"data-name":t="round-tabs-buttons",...n}=e;return i.createElement(z,{...n,"data-name":t})}!function(e){e.Primary="primary",e.Ghost="ghost"}(N||(N={})),function(e){e.XSmall="xsmall",e.Small="small",e.Large="large"}(T||(T={})),function(e){e.Start="start",e.Center="center"}(M||(M={})),function(e){e.Text="text",e.Meatballs="meatballs"}(O||(O={}));n(591365)},132455:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>i.Spinner});var i=n(672511)},515783:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetCaret:()=>l});var i=n(50959),s=n(497754),o=n(878112),a=n(149128),r=n(100578);function l(e){const{dropped:t,className:n}=e;return i.createElement(o.Icon,{
className:s(n,a.icon,{[a.dropped]:t}),icon:r})}},904237:(e,t,n)=>{"use strict";var i=n(632227);t.createRoot=i.createRoot,i.hydrateRoot},438576:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},155973:e=>{e.exports={title:"title-u3QJgF_p"}},155352:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>r,ToolWidgetButton:()=>l});var i=n(50959),s=n(497754),o=n(878112),a=n(438576);const r=a,l=i.forwardRef(((e,t)=>{const{tag:n="div",icon:r,endIcon:l,isActive:c,isOpened:d,isDisabled:u,isGrouped:h,isHovered:p,isClicked:m,onClick:g,text:f,textBeforeIcon:v,title:b,theme:_=a,className:y,forceInteractive:w,inactive:x,"data-name":C,"data-tooltip":S,...E}=e,k=s(y,_.button,(b||S)&&"apply-common-tooltip",{[_.isActive]:c,[_.isOpened]:d,[_.isInteractive]:(w||Boolean(g))&&!u&&!x,[_.isDisabled]:Boolean(u||x),[_.isGrouped]:h,[_.hover]:p,[_.clicked]:m}),D=r&&("string"==typeof r?i.createElement(o.Icon,{className:_.icon,icon:r}):i.cloneElement(r,{className:s(_.icon,r.props.className)}));return"button"===n?i.createElement("button",{...E,ref:t,type:"button",className:s(k,_.accessible),disabled:u&&!x,onClick:g,title:b,"data-name":C,"data-tooltip":S},v&&f&&i.createElement("div",{className:s("js-button-text",_.text)},f),D,!v&&f&&i.createElement("div",{className:s("js-button-text",_.text)},f)):i.createElement("div",{...E,ref:t,"data-role":"button",className:k,onClick:u?void 0:g,title:b,"data-name":C,"data-tooltip":S},v&&f&&i.createElement("div",{className:s("js-button-text",_.text)},f),D,!v&&f&&i.createElement("div",{className:s("js-button-text",_.text)},f),l&&i.createElement(o.Icon,{icon:l,className:a.endIcon}))}))},722426:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetMenuSummary:()=>a});var i=n(50959),s=n(497754),o=n(155973);function a(e){return i.createElement("div",{className:s(e.className,o.title)},e.children)}},602948:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.92 7.83 9 12.29l5.08-4.46-1-1.13L9 10.29l-4.09-3.6-.99 1.14Z"/></svg>'},789882:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M5 9a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm6 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 2a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/></svg>'},602057:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M9 14a3 3 0 1 1-6 0 3 3 0 0 1 6 0zm8 0a3 3 0 1 1-6 0 3 3 0 0 1 6 0zm5 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/></svg>'},117105:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 21 21m0-21-21 21"/></svg>'},315130:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 15 15m0-15-15 15"/></svg>'},238822:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 11 11m0-11-11 11"/></svg>'},663346:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 9 9m0-9-9 9"/></svg>'},534983:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 7 7m0-7-7 7"/></svg>'},100578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},569533:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'}}]);