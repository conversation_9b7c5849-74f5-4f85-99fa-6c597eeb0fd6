(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9216],{584928:t=>{t.exports={dialog:"dialog-aRAWUDhF",rounded:"rounded-aRAWUDhF",shadowed:"shadowed-aRAWUDhF",fullscreen:"fullscreen-aRAWUDhF",darker:"darker-aRAWUDhF",backdrop:"backdrop-aRAWUDhF"}},513100:t=>{t.exports={"tablet-normal-breakpoint":"(max-width: 768px)","tooltip-offset":"20px",dialog:"dialog-qyCw0PaN",dragging:"dragging-qyCw0PaN",mobile:"mobile-qyCw0PaN",fullscreen:"fullscreen-qyCw0PaN",dialogAnimatedAppearance:"dialogAnimatedAppearance-qyCw0PaN",dialogAnimation:"dialogAnimation-qyCw0PaN",dialogTooltip:"dialogTooltip-qyCw0PaN"}},80137:(t,e,i)=>{"use strict";i.d(e,{Dialog:()=>h});var s=i(50959),o=i(497754),n=i(682925),a=i(801808),r=i(800417),l=i(584928);class h extends s.PureComponent{constructor(){super(...arguments),this._manager=new a.OverlapManager,this._handleSlot=t=>{this._manager.setContainer(t)}}render(){const{rounded:t=!0,shadowed:e=!0,fullscreen:i=!1,darker:a=!1,className:h,backdrop:c,containerTabIndex:d=-1}=this.props,u=o(h,l.dialog,t&&l.rounded,e&&l.shadowed,i&&l.fullscreen,a&&l.darker),p=(0,r.filterDataProps)(this.props),g=this.props.style?{...this._createStyles(),...this.props.style}:this._createStyles();return s.createElement(s.Fragment,null,s.createElement(n.SlotContext.Provider,{value:this._manager},c&&s.createElement("div",{onClick:this.props.onClickBackdrop,className:l.backdrop}),s.createElement("div",{...p,className:u,style:g,ref:this.props.reference,onFocus:this.props.onFocus,onMouseDown:this.props.onMouseDown,onMouseUp:this.props.onMouseUp,onClick:this.props.onClick,onKeyDown:this.props.onKeyDown,tabIndex:d,"aria-label":this.props.containerAriaLabel},this.props.children)),s.createElement(n.Slot,{reference:this._handleSlot}))}_createStyles(){const{bottom:t,left:e,width:i,right:s,top:o,zIndex:n,height:a}=this.props;return{bottom:t,left:e,right:s,top:o,zIndex:n,maxWidth:i,height:a}}}},738036:(t,e,i)=>{"use strict";i.d(e,{OutsideEvent:()=>o});var s=i(908783);function o(t){const{children:e,...i}=t;return e((0,s.useOutsideEvent)(i))}},910549:(t,e,i)=>{"use strict";i.d(e,{PopupContext:()=>s});const s=i(50959).createContext(null)},59216:(t,e,i)=>{"use strict";i.d(e,{PopupDialog:()=>C});var s,o=i(50959),n=i(497754),a=i(650151),r=i(80137),l=i(874485),h=i(738036),c=i(822960),d=i(32556);function u(t,e,i,s){return t+e>s&&(t=s-e),t<i&&(t=i),t}function p(t){return{x:(0,c.clamp)(t.x,20,document.documentElement.clientWidth-20),y:(0,c.clamp)(t.y,20,window.innerHeight-20)}}function g(t){return{x:t.clientX,y:t.clientY}}function _(t){return{x:t.touches[0].clientX,y:t.touches[0].clientY}}!function(t){t[t.MouseGuardZone=20]="MouseGuardZone"}(s||(s={}));class m{constructor(t,e,i={boundByScreen:!0}){this._drag=null,this._canBeTouchClick=!1,this._frame=null,this._onMouseDragStart=t=>{if(0!==t.button||this._isTargetNoDraggable(t))return;t.preventDefault(),document.addEventListener("mousemove",this._onMouseDragMove),document.addEventListener("mouseup",this._onMouseDragEnd);const e=p(g(t));this._dragStart(e)},this._onTouchDragStart=t=>{
if(this._isTargetNoDraggable(t))return;this._canBeTouchClick=!0,t.preventDefault(),this._header.addEventListener("touchmove",this._onTouchDragMove,{passive:!1});const e=p(_(t));this._dragStart(e)},this._onMouseDragEnd=t=>{t.target instanceof Node&&this._header.contains(t.target)&&t.preventDefault(),document.removeEventListener("mousemove",this._onMouseDragMove),document.removeEventListener("mouseup",this._onMouseDragEnd),this._onDragStop()},this._onTouchDragEnd=t=>{this._header.removeEventListener("touchmove",this._onTouchDragMove),this._onDragStop(),this._canBeTouchClick&&(this._canBeTouchClick=!1,function(t){if(t instanceof SVGElement){const e=document.createEvent("SVGEvents");e.initEvent("click",!0,!0),t.dispatchEvent(e)}t instanceof HTMLElement&&t.click()}(t.target))},this._onMouseDragMove=t=>{const e=p(g(t));this._dragMove(e)},this._onTouchDragMove=t=>{this._canBeTouchClick=!1,t.preventDefault();const e=p(_(t));this._dragMove(e)},this._onDragStop=()=>{this._drag=null,this._header.classList.remove("dragging"),this._options.onDragEnd&&this._options.onDragEnd()},this._dialog=t,this._header=e,this._options=i,this._header.addEventListener("mousedown",this._onMouseDragStart),this._header.addEventListener("touchstart",this._onTouchDragStart),this._header.addEventListener("touchend",this._onTouchDragEnd)}destroy(){null!==this._frame&&cancelAnimationFrame(this._frame),this._header.removeEventListener("mousedown",this._onMouseDragStart),document.removeEventListener("mouseup",this._onMouseDragEnd),this._header.removeEventListener("touchstart",this._onTouchDragStart),this._header.removeEventListener("touchend",this._onTouchDragEnd),document.removeEventListener("mouseleave",this._onMouseDragEnd)}updateOptions(t){this._options=t}_dragStart(t){const e=this._dialog.getBoundingClientRect();this._drag={startX:t.x,startY:t.y,finishX:t.x,finishY:t.y,dialogX:e.left,dialogY:e.top};const i=Math.round(e.left),s=Math.round(e.top);this._dialog.style.transform=`translate(${i}px, ${s}px)`,this._header.classList.add("dragging"),this._options.onDragStart&&this._options.onDragStart()}_dragMove(t){if(this._drag){if(this._drag.finishX=t.x,this._drag.finishY=t.y,null!==this._frame)return;this._frame=requestAnimationFrame((()=>{if(this._drag){const e=t.x-this._drag.startX,i=t.y-this._drag.startY;this._moveDialog(this._drag.dialogX+e,this._drag.dialogY+i)}this._frame=null}))}}_moveDialog(t,e){const i=this._dialog.getBoundingClientRect(),{boundByScreen:s}=this._options,o=u(t,i.width,s?0:-1/0,s?window.innerWidth:1/0),n=u(e,i.height,s?0:-1/0,s?window.innerHeight:1/0);this._dialog.style.transform=`translate(${Math.round(o)}px, ${Math.round(n)}px)`}_isTargetNoDraggable(t){return t.target instanceof Element&&null!==t.target.closest("[data-disable-drag]")}}const f={vertical:0};var v,y=i(8361),D=i(910549),E=i(285089),x=i(671945),S=i(924910);!function(t){t.Open="dialog-open",t.Close="dialog-close",t.FullscreenOn="dialog-fullscreen-on",t.FullscreenOff="dialog-fullscreen-off"}(v||(v={}));const F=(0,x.getLogger)("DialogEventDispatcher");class T{constructor(){
this._openSessionId=null}dispatch(t){if("dialog-open"===t){if(null!==this._openSessionId)return void F.logError("Multiple calls to open dialog");this._openSessionId=(0,S.randomHash)()}null!==this._openSessionId?(window.dispatchEvent(new CustomEvent(t,{bubbles:!0,detail:{dialogSessionId:this._openSessionId}})),"dialog-close"===t&&(this._openSessionId=null)):F.logError("Empty open dialog session id")}}var w=i(69111),b=(i(440891),i(513100));b["tooltip-offset"];const M=class{constructor(t,e){this._frame=null,this._isFullscreen=!1,this._handleResize=()=>{null===this._frame&&(this._frame=requestAnimationFrame((()=>{this.recalculateBounds(),this._frame=null})))},this._dialog=t,this._guard=e.guard||f,this._calculateDialogPosition=e.calculateDialogPosition,this._initialHeight=t.style.height,window.addEventListener("resize",this._handleResize)}updateOptions(t){this._guard=t.guard||f,this._calculateDialogPosition=t.calculateDialogPosition}setFullscreen(t){this._isFullscreen!==t&&(this._isFullscreen=t,this.recalculateBounds())}centerAndFit(){const{x:t,y:e}=this.getDialogsTopLeftCoordinates(),i=this._calcAvailableHeight(),s=this._calcDialogHeight();if(i===s)if(this._calculateDialogPosition){const{left:t,top:e}=this._calculateDialogPosition(this._dialog,document.documentElement,this._guard);this._dialog.style.transform=`translate(${Math.round(t)}px, ${Math.round(e)}px)`}else this._dialog.style.height=s+"px";this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${t}px, ${e}px)`}getDialogsTopLeftCoordinates(){const{clientWidth:t,clientHeight:e}=this._getClientDimensions(),i=this._calcDialogHeight(),s=t/2-this._dialog.clientWidth/2,o=e/2-i/2+this._getTopOffset();return{x:Math.round(s),y:Math.round(o)}}recalculateBounds(){const{clientWidth:t,clientHeight:e}=this._getClientDimensions(),{vertical:i}=this._guard,s=this._calculateDialogPosition?.(this._dialog,{clientWidth:t,clientHeight:e},{vertical:i});if(this._isFullscreen){if(this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.width="100%",this._dialog.style.height="100%",this._dialog.style.transform="none",s){const{left:t,top:e,width:i,height:o}=s;this._dialog.style.transform=`translate(${Math.round(t)}px, ${Math.round(e)}px)`,i&&(this._dialog.style.width=`${i}px`,this._dialog.style.minWidth="unset"),o&&(this._dialog.style.height=`${o}px`,this._dialog.style.minHeight="unset")}}else if(s){const{left:t,top:e}=s;this._dialog.style.transform=`translate(${Math.round(t)}px, ${Math.round(e)}px)`}else{this._dialog.style.width="",this._dialog.style.height="";const s=this._dialog.getBoundingClientRect(),o=e-2*i,n=u(s.left,s.width,0,t),a=u(s.top,s.height,i,e);this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${Math.round(n)}px, ${Math.round(a)}px)`,this._dialog.style.height=o<s.height?o+"px":this._initialHeight}}destroy(){window.removeEventListener("resize",this._handleResize),null!==this._frame&&(cancelAnimationFrame(this._frame),this._frame=null)}_getClientDimensions(){
return{clientHeight:document.documentElement.clientHeight,clientWidth:document.documentElement.clientWidth}}_getTopOffset(){return 0}_calcDialogHeight(){const t=this._calcAvailableHeight();return t<this._dialog.clientHeight?t:this._dialog.clientHeight}_calcAvailableHeight(){return this._getClientDimensions().clientHeight-2*this._guard.vertical}};class A extends o.PureComponent{constructor(t){super(t),this._dialog=null,this._cleanUpFunctions=[],this._prevActiveElement=null,this._eventDispatcher=new T,this._handleDialogRef=t=>{const{reference:e}=this.props;this._dialog=t,"function"==typeof e&&e(t)},this._handleFocus=()=>{this._moveToTop()},this._handleMouseDown=t=>{this._moveToTop()},this._handleTouchStart=t=>{this._moveToTop()},this.state={canFitTooltip:!1},this._prevActiveElement=document.activeElement}render(){return o.createElement(D.PopupContext.Provider,{value:this},o.createElement(h.OutsideEvent,{mouseDown:!0,touchStart:!0,handler:this.props.onClickOutside},(t=>o.createElement("div",{ref:t,"data-outside-boundary-for":this.props.name,onFocus:this._handleFocus,onMouseDown:this._handleMouseDown,onTouchStart:this._handleTouchStart,"data-dialog-name":this.props["data-dialog-name"],"data-tooltip-show-on-focus":"true"},o.createElement(r.Dialog,{style:this._applyAnimationCSSVariables(),...this.props,reference:this._handleDialogRef,className:n(b.dialog,(0,w.isOnMobileAppPage)("any")&&!this.props.noMobileAppShadows&&b.mobile,this.props.fullscreen&&b.fullscreen,this.props.className)},!1,this.props.children)))))}componentDidMount(){const{draggable:t,boundByScreen:e,onDragStart:i}=this.props,s=(0,a.ensureNotNull)(this._dialog);if(this._eventDispatcher.dispatch("dialog-open"),t){const t=s.querySelector("[data-dragg-area]");if(t&&t instanceof HTMLElement){const o=new m(s,t,{boundByScreen:Boolean(e),onDragStart:i});this._cleanUpFunctions.push((()=>o.destroy())),this._drag=o}}this.props.autofocus&&!s.contains(document.activeElement)&&s.focus(),(this._isFullScreen()||this.props.fixedBody)&&(0,E.setFixedBodyState)(!0);const{guard:o,calculateDialogPosition:n}=this.props;if(this.props.resizeHandler)this._resize=this.props.resizeHandler;else{const t=new M(s,{guard:o,calculateDialogPosition:n});this._cleanUpFunctions.push((()=>t.destroy())),this._resize=t}if(this._isFullScreen()&&this._eventDispatcher.dispatch("dialog-fullscreen-on"),this.props.isAnimationEnabled&&this.props.growPoint&&this._applyAppearanceAnimation(this.props.growPoint),this.props.centeredOnMount&&this._resize.centerAndFit(),this._resize.setFullscreen(this._isFullScreen()),this.props.shouldForceFocus){if(this.props.onForceFocus)return void this.props.onForceFocus(s);s.focus()}if(!s.contains(document.activeElement)){const t=function(t){const e=t.querySelector("[autofocus]:not([disabled])");if(e)return e;if(t.tabIndex>=0)return t;const i=(0,d.getActiveElementSelectors)(),s=Array.from(t.querySelectorAll(i)).filter((0,d.createScopedVisibleElementFilter)(t));let o=Number.NEGATIVE_INFINITY,n=null;for(let t=0;t<s.length;t++){const e=s[t],i=e.getAttribute("tabindex")
;if(null!==i){const t=parseInt(i,10);!isNaN(t)&&t>o&&(o=t,n=e)}}return n}(s);t instanceof HTMLElement&&t.focus()}}componentDidUpdate(t){const e=t.fullscreen;if(this._resize){const{guard:t,calculateDialogPosition:e}=this.props;this._resize.updateOptions({guard:t,calculateDialogPosition:e}),this._resize.setFullscreen(this._isFullScreen())}if(this._drag&&this._drag.updateOptions({boundByScreen:Boolean(this.props.boundByScreen),onDragStart:this.props.onDragStart}),t.fullscreen!==this.props.fullscreen){const t=this.props.fullscreen;t&&!e?this._eventDispatcher.dispatch("dialog-fullscreen-on"):!t&&e&&this._eventDispatcher.dispatch("dialog-fullscreen-off")}}componentWillUnmount(){if(this.props.shouldReturnFocus&&this._prevActiveElement&&document.body.contains(this._prevActiveElement)&&(null===document.activeElement||document.activeElement===document.body||this._dialog?.contains(document.activeElement)))try{setTimeout((()=>{this._prevActiveElement.focus({preventScroll:!0})}))}catch{}for(const t of this._cleanUpFunctions)t();(this._isFullScreen()||this.props.fixedBody)&&(0,E.setFixedBodyState)(!1),this._isFullScreen()&&this._eventDispatcher.dispatch("dialog-fullscreen-off"),this._eventDispatcher.dispatch("dialog-close")}focus(){this._dialog&&this._dialog.focus()}centerAndFit(){this._resize&&this._resize.centerAndFit()}recalculateBounds(){this._resize&&this._resize.recalculateBounds()}_moveToTop(){this.props.isZIndexFixed||null!==this.context&&this.context.moveToTop()}_applyAnimationCSSVariables(){return{"--animationTranslateStartX":null,"--animationTranslateStartY":null,"--animationTranslateEndX":null,"--animationTranslateEndY":null}}_applyAppearanceAnimation(t){if(this._resize&&this._dialog){const{x:e,y:i}=t,{x:s,y:o}=this._resize.getDialogsTopLeftCoordinates();this._dialog.style.setProperty("--animationTranslateStartX",`${e}px`),this._dialog.style.setProperty("--animationTranslateStartY",`${i}px`),this._dialog.style.setProperty("--animationTranslateEndX",`${s}px`),this._dialog.style.setProperty("--animationTranslateEndY",`${o}px`),this._dialog.classList.add(b.dialogAnimatedAppearance)}}_handleTooltipFit(){0}_isFullScreen(){return Boolean(this.props.fullscreen)}}A.contextType=y.PortalContext,A.defaultProps={boundByScreen:!0,draggable:!0,centeredOnMount:!0,shouldReturnFocus:!0};const C=(0,l.makeOverlapable)(A,!0)},874485:(t,e,i)=>{"use strict";i.d(e,{makeOverlapable:()=>n});var s=i(50959),o=i(8361);function n(t,e){return class extends s.PureComponent{render(){const{isOpened:i,root:n}=this.props;if(!i)return null;const a=s.createElement(t,{...this.props,ref:this.props.componentRef,zIndex:150});return"parent"===n?a:s.createElement(o.Portal,{shouldTrapFocus:e},a)}}}}}]);