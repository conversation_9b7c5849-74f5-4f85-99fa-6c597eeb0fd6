"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3054],{943705:(n,t,r)=>{r.d(t,{$:()=>$,A:()=>M,B:()=>T,C:()=>R,D:()=>b,E:()=>E,F:()=>D,G:()=>J,H:()=>a,I:()=>x,J:()=>F,L:()=>X,M:()=>tn,N:()=>en,O:()=>nn,P:()=>q,Q:()=>cn,R:()=>U,S:()=>H,T:()=>_,U:()=>un,V:()=>k,Z:()=>Z,a:()=>K,a3:()=>rn,a4:()=>on,b:()=>G,d:()=>B,e:()=>P,f:()=>W,g:()=>z,h:()=>Q,i:()=>w,j:()=>C,k:()=>c,l:()=>m,m:()=>g,n:()=>j,o:()=>s,p:()=>l,q:()=>h,r:()=>v,s:()=>S,t:()=>f,u:()=>p,v:()=>N,y:()=>O,z:()=>A});var e=r(45807),o=r(315882),u=r(617443);const i=function(n,t){var r;void 0===t&&(t=!0);var o=new Promise((function(e){r=setTimeout(e,Math.min(2147483647,n),t)}));return o[e.CANCEL]=function(){clearTimeout(r)},o};var c=function(n){return function(){return n}}(!0),f=function(){};var a=function(n){return n};"function"==typeof Symbol&&Symbol.asyncIterator&&Symbol.asyncIterator;var l=function(n,t){(0,o.default)(n,t),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(t).forEach((function(r){n[r]=t[r]}))},p=function(n,t){var r;return(r=[]).concat.apply(r,t.map(n))};function v(n,t){var r=n.indexOf(t);r>=0&&n.splice(r,1)}function s(n){var t=!1;return function(){t||(t=!0,n())}}var y=function(n){throw n},d=function(n){return{value:n,done:!0}};function h(n,t,r){void 0===t&&(t=y),void 0===r&&(r="iterator");var e={meta:{name:r},next:n,throw:t,return:d,isSagaIterator:!0};return"undefined"!=typeof Symbol&&(e[Symbol.iterator]=function(){return e}),e}function b(n,t){var r=t.sagaStack;console.error(n),console.error(r)}var w=function(n){return new Error("\n  redux-saga: Error checking hooks detected an inconsistent state. This is likely a bug\n  in redux-saga code and not yours. Thanks for reporting this in the project's github repo.\n  Error: "+n+"\n")},g=function(n){return Array.apply(null,new Array(n))},E=function(n){return function(t){return n(Object.defineProperty(t,e.SAGA_ACTION,{value:!0}))}},A=function(n){return n===e.TERMINATE},O=function(n){return n===e.TASK_CANCEL},S=function(n){return A(n)||O(n)};function m(n,t){var r=Object.keys(n),e=r.length;var o,i=0,c=(0,u.array)(n)?g(e):{},a={};return r.forEach((function(n){var r=function(r,u){o||(u||S(r)?(t.cancel(),t(r,u)):(c[n]=r,++i===e&&(o=!0,t(c))))};r.cancel=f,a[n]=r})),t.cancel=function(){o||(o=!0,r.forEach((function(n){return a[n].cancel()})))},a}function C(n){return{name:n.name||"anonymous",location:N(n)}}function N(n){return n[e.SAGA_LOCATION]}function T(){for(var n=arguments.length,t=new Array(n),r=0;r<n;r++)t[r]=arguments[r];return 0===t.length?function(n){return n}:1===t.length?t[0]:t.reduce((function(n,t){return function(){return n(t.apply(void 0,arguments))}}))}var L={isEmpty:c,put:f,take:f};function I(n,t){void 0===n&&(n=10);var r=new Array(n),e=0,o=0,u=0,i=function(t){r[o]=t,o=(o+1)%n,e++},c=function(){if(0!=e){var t=r[u];return r[u]=null,e--,u=(u+1)%n,t}},f=function(){for(var n=[];e;)n.push(c());return n};return{isEmpty:function(){return 0==e},put:function(c){var a;if(e<n)i(c);else switch(t){case 1:throw new Error("Channel's Buffer overflow!");case 3:r[o]=c,
u=o=(o+1)%n;break;case 4:a=2*n,r=f(),e=r.length,o=r.length,u=0,r.length=a,n=a,i(c)}},take:c,flush:f}}var j=function(){return L},k=function(n){return I(n,3)},P=function(n){return I(n,4)},x=Object.freeze({__proto__:null,none:j,fixed:function(n){return I(n,1)},dropping:function(n){return I(n,2)},sliding:k,expanding:P}),_="TAKE",q="PUT",M="ALL",U="RACE",R="CALL",K="CPS",D="FORK",F="JOIN",G="CANCEL",H="SELECT",B="ACTION_CHANNEL",W="CANCELLED",z="FLUSH",J="GET_CONTEXT",Q="SET_CONTEXT",V=function(n,t){var r;return(r={})[e.IO]=!0,r.combinator=!1,r.type=n,r.payload=t,r};function X(n,t){return void 0===n&&(n="*"),(0,u.pattern)(n)?((0,u.notUndef)(t)&&console.warn("take(pattern) takes one argument but two were provided. Consider passing an array for listening to several action types"),V(_,{pattern:n})):(0,u.multicast)(n)&&(0,u.notUndef)(t)&&(0,u.pattern)(t)?V(_,{channel:n,pattern:t}):(0,u.channel)(n)?((0,u.notUndef)(t)&&console.warn("take(channel) takes one argument but two were provided. Second argument is ignored."),V(_,{channel:n})):void 0}function Z(n,t){return(0,u.undef)(t)&&(t=n,n=void 0),V(q,{channel:n,action:t})}function $(n){var t=V(M,n);return t.combinator=!0,t}function Y(n,t){var r,e=null;return(0,u.func)(n)?r=n:((0,u.array)(n)?(e=n[0],r=n[1]):(e=n.context,r=n.fn),e&&(0,u.string)(r)&&(0,u.func)(e[r])&&(r=e[r])),{context:e,fn:r,args:t}}function nn(n){for(var t=arguments.length,r=new Array(t>1?t-1:0),e=1;e<t;e++)r[e-1]=arguments[e];return V(R,Y(n,r))}function tn(n){for(var t=arguments.length,r=new Array(t>1?t-1:0),e=1;e<t;e++)r[e-1]=arguments[e];return V(D,Y(n,r))}function rn(n){return V(F,n)}function en(n){return void 0===n&&(n=e.SELF_CANCELLATION),V(G,n)}function on(n){void 0===n&&(n=a);for(var t=arguments.length,r=new Array(t>1?t-1:0),e=1;e<t;e++)r[e-1]=arguments[e];return V(H,{selector:n,args:r})}function un(n,t){return V(B,{pattern:n,buffer:t})}var cn=nn.bind(null,i)},617443:(n,t,r)=>{r.d(t,{array:()=>f,channel:()=>v,func:()=>i,iterator:()=>l,multicast:()=>d,notUndef:()=>u,pattern:()=>p,promise:()=>a,string:()=>c,stringableFunc:()=>s,symbol:()=>y,undef:()=>o});var e=r(45807),o=function(n){return null==n},u=function(n){return null!=n},i=function(n){return"function"==typeof n},c=function(n){return"string"==typeof n},f=Array.isArray,a=function(n){return n&&i(n.then)},l=function(n){return n&&i(n.next)&&i(n.throw)},p=function n(t){return t&&(c(t)||y(t)||i(t)||f(t)&&t.every(n))},v=function(n){return n&&i(n.take)&&i(n.close)},s=function(n){return i(n)&&n.hasOwnProperty("toString")},y=function(n){return Boolean(n)&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype},d=function(n){return v(n)&&n[e.MULTICAST]}},45807:(n,t,r)=>{r.d(t,{CANCEL:()=>o,CHANNEL_END_TYPE:()=>u,IO:()=>i,MATCH:()=>c,MULTICAST:()=>f,SAGA_ACTION:()=>a,SAGA_LOCATION:()=>y,SELF_CANCELLATION:()=>l,TASK:()=>p,TASK_CANCEL:()=>v,TERMINATE:()=>s});var e=function(n){return"@@redux-saga/"+n
},o=e("CANCEL_PROMISE"),u=e("CHANNEL_END"),i=e("IO"),c=e("MATCH"),f=e("MULTICAST"),a=e("SAGA_ACTION"),l=e("SELF_CANCELLATION"),p=e("TASK"),v=e("TASK_CANCEL"),s=e("TERMINATE"),y=e("LOCATION")},129885:(n,t,r)=>{r.d(t,{all:()=>o.$,call:()=>o.O,cancel:()=>o.N,fork:()=>o.M,join:()=>o.a3,put:()=>o.Z,select:()=>o.a4,take:()=>o.L,takeEvery:()=>v,takeLatest:()=>s,throttle:()=>y});var e=r(617443),o=r(943705),u=function(n){return{done:!0,value:n}},i={};function c(n){return(0,e.channel)(n)?"channel":(0,e.stringableFunc)(n)?String(n):(0,e.func)(n)?n.name:String(n)}function f(n,t,r){var e,c,f,a=t;function l(t,r){if(a===i)return u(t);if(r&&!c)throw a=i,r;e&&e(t);var o=r?n[c](r):n[a]();return a=o.nextState,f=o.effect,e=o.stateUpdater,c=o.errorState,a===i?u(t):f}return(0,o.q)(l,(function(n){return l(null,n)}),r)}function a(n,t){for(var r=arguments.length,e=new Array(r>2?r-2:0),u=2;u<r;u++)e[u-2]=arguments[u];var i,a={done:!1,value:(0,o.L)(n)},l=function(n){return i=n};return f({q1:function(){return{nextState:"q2",effect:a,stateUpdater:l}},q2:function(){return{nextState:"q1",effect:(n=i,{done:!1,value:o.M.apply(void 0,[t].concat(e,[n]))})};var n}},"q1","takeEvery("+c(n)+", "+t.name+")")}function l(n,t){for(var r=arguments.length,e=new Array(r>2?r-2:0),u=2;u<r;u++)e[u-2]=arguments[u];var i,a,l={done:!1,value:(0,o.L)(n)},p=function(n){return{done:!1,value:o.M.apply(void 0,[t].concat(e,[n]))}},v=function(n){return{done:!1,value:(0,o.N)(n)}},s=function(n){return i=n},y=function(n){return a=n};return f({q1:function(){return{nextState:"q2",effect:l,stateUpdater:y}},q2:function(){return i?{nextState:"q3",effect:v(i)}:{nextState:"q1",effect:p(a),stateUpdater:s}},q3:function(){return{nextState:"q1",effect:p(a),stateUpdater:s}}},"q1","takeLatest("+c(n)+", "+t.name+")")}function p(n,t,r){for(var u=arguments.length,i=new Array(u>3?u-3:0),a=3;a<u;a++)i[a-3]=arguments[a];var l,p,v={done:!1,value:(0,o.Q)(n)},s=function(n){return l=n},y=function(n){return p=n},d=!(0,e.channel)(t);return d||y(t),f({q1:function(){return{nextState:"q2",effect:{done:!1,value:(0,o.U)(t,(0,o.V)(1))},stateUpdater:y}},q2:function(){return{nextState:"q3",effect:{done:!1,value:(0,o.L)(p)},stateUpdater:s}},q3:function(){return{nextState:"q4",effect:(n=l,{done:!1,value:o.M.apply(void 0,[r].concat(i,[n]))})};var n},q4:function(){return{nextState:"q2",effect:v}}},d?"q1":"q2","throttle("+c(t)+", "+r.name+")")}function v(n,t){for(var r=arguments.length,e=new Array(r>2?r-2:0),u=2;u<r;u++)e[u-2]=arguments[u];return o.M.apply(void 0,[a,n,t].concat(e))}function s(n,t){for(var r=arguments.length,e=new Array(r>2?r-2:0),u=2;u<r;u++)e[u-2]=arguments[u];return o.M.apply(void 0,[l,n,t].concat(e))}function y(n,t,r){for(var e=arguments.length,u=new Array(e>3?e-3:0),i=3;i<e;i++)u[i-3]=arguments[i];return o.M.apply(void 0,[p,n,t,r].concat(u))}},406047:(n,t,r)=>{function e(n){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},e(n)}
function o(n){var t=function(n,t){if("object"!==e(n)||null===n)return n;var r=n[Symbol.toPrimitive];if(void 0!==r){var o=r.call(n,t||"default");if("object"!==e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(n)}(n,"string");return"symbol"===e(t)?t:String(t)}function u(n,t,r){return(t=o(t))in n?Object.defineProperty(n,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[t]=r,n}function i(n,t){var r=Object.keys(n);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(n);t&&(e=e.filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),r.push.apply(r,e)}return r}function c(n){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){u(n,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(r,t))}))}return n}function f(n){return"Minified Redux error #"+n+"; visit https://redux.js.org/Errors?code="+n+" for the full message or use the non-minified dev environment for full errors. "}r.d(t,{applyMiddleware:()=>w,bindActionCreators:()=>h,combineReducers:()=>y,compose:()=>b,createStore:()=>s});var a="function"==typeof Symbol&&Symbol.observable||"@@observable",l=function(){return Math.random().toString(36).substring(7).split("").join(".")},p={INIT:"@@redux/INIT"+l(),REPLACE:"@@redux/REPLACE"+l(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+l()}};function v(n){if("object"!=typeof n||null===n)return!1;for(var t=n;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(n)===t}function s(n,t,r){var e;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(f(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(f(1));return r(s)(n,t)}if("function"!=typeof n)throw new Error(f(2));var o=n,u=t,i=[],c=i,l=!1;function y(){c===i&&(c=i.slice())}function d(){if(l)throw new Error(f(3));return u}function h(n){if("function"!=typeof n)throw new Error(f(4));if(l)throw new Error(f(5));var t=!0;return y(),c.push(n),function(){if(t){if(l)throw new Error(f(6));t=!1,y();var r=c.indexOf(n);c.splice(r,1),i=null}}}function b(n){if(!v(n))throw new Error(f(7));if(void 0===n.type)throw new Error(f(8));if(l)throw new Error(f(9));try{l=!0,u=o(u,n)}finally{l=!1}for(var t=i=c,r=0;r<t.length;r++){(0,t[r])()}return n}return b({type:p.INIT}),(e={dispatch:b,subscribe:h,getState:d,replaceReducer:function(n){if("function"!=typeof n)throw new Error(f(10));o=n,b({type:p.REPLACE})}})[a]=function(){var n,t=h;return(n={subscribe:function(n){if("object"!=typeof n||null===n)throw new Error(f(11));function r(){n.next&&n.next(d())}return r(),{unsubscribe:t(r)}}})[a]=function(){return this},n},e}function y(n){for(var t=Object.keys(n),r={},e=0;e<t.length;e++){var o=t[e];0,
"function"==typeof n[o]&&(r[o]=n[o])}var u,i=Object.keys(r);try{!function(n){Object.keys(n).forEach((function(t){var r=n[t];if(void 0===r(void 0,{type:p.INIT}))throw new Error(f(12));if(void 0===r(void 0,{type:p.PROBE_UNKNOWN_ACTION()}))throw new Error(f(13))}))}(r)}catch(n){u=n}return function(n,t){if(void 0===n&&(n={}),u)throw u;for(var e=!1,o={},c=0;c<i.length;c++){var a=i[c],l=r[a],p=n[a],v=l(p,t);if(void 0===v){t&&t.type;throw new Error(f(14))}o[a]=v,e=e||v!==p}return(e=e||i.length!==Object.keys(n).length)?o:n}}function d(n,t){return function(){return t(n.apply(this,arguments))}}function h(n,t){if("function"==typeof n)return d(n,t);if("object"!=typeof n||null===n)throw new Error(f(16));var r={};for(var e in n){var o=n[e];"function"==typeof o&&(r[e]=d(o,t))}return r}function b(){for(var n=arguments.length,t=new Array(n),r=0;r<n;r++)t[r]=arguments[r];return 0===t.length?function(n){return n}:1===t.length?t[0]:t.reduce((function(n,t){return function(){return n(t.apply(void 0,arguments))}}))}function w(){for(var n=arguments.length,t=new Array(n),r=0;r<n;r++)t[r]=arguments[r];return function(n){return function(){var r=n.apply(void 0,arguments),e=function(){throw new Error(f(15))},o={getState:r.getState,dispatch:function(){return e.apply(void 0,arguments)}},u=t.map((function(n){return n(o)}));return e=b.apply(void 0,u)(r.dispatch),c(c({},r),{},{dispatch:e})}}}}}]);