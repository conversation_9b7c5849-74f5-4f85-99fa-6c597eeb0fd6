"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[620],{56702:(e,n,t)=>{t.r(n),t.d(n,{tableActions:()=>r});var o=t(787382),a=t(737891),l=t(621327);const c=o.t(null,void 0,t(361702)),d=o.t(null,void 0,t(580473)),s=o.t(null,void 0,t(185069)),i=o.t(null,void 0,t(433500));function r(e,n){const t=[];t.push(new a.ActionWithStandardIcon({actionId:"Chart.SelectedObject.InsertColumnTable",options:{label:d,statName:"InsertColumnTableSelectedObject",iconId:"Chart.InsertColumnTable",checkable:!1,onExecute:()=>{const t=e.insertCellsUndoCommand(1);n.undoHistory().pushUndoCommand(t)}}})),t.push(new a.ActionWithStandardIcon({actionId:"Chart.SelectedObject.InsertRowTable",options:{label:c,iconId:"Chart.InsertRowTable",statName:"InsertRowTableSelectedObject",checkable:!1,onExecute:()=>{const t=e.insertCellsUndoCommand(0);n.undoHistory().pushUndoCommand(t)}}}));const[o,r]=e.inplaceEditableCellIndexes();return-1!==o&&-1!==r&&(t.length>0&&t.push(new l.Separator),t.push(new a.ActionWithStandardIcon({actionId:"Chart.SelectedObject.RemoveRowTable",options:{label:s,statName:"RemoveRowTableSelectedObject",iconId:"Chart.RemoveRowTable",checkable:!1,disabled:!e.isRemoveCellsAvailable(0),onExecute:()=>{const t=e.removeCellsUndoCommand(0);null!==t&&n.undoHistory().pushUndoCommand(t)}}})),t.push(new a.ActionWithStandardIcon({actionId:"Chart.SelectedObject.RemoveColumnTable",options:{label:i,statName:"RemoveColumnTableSelectedObject",iconId:"Chart.RemoveColumnTable",checkable:!1,disabled:!e.isRemoveCellsAvailable(1),onExecute:()=>{const t=e.removeCellsUndoCommand(1);null!==t&&n.undoHistory().pushUndoCommand(t)}}}))),t}},347098:(e,n,t)=>{t.r(n),t.d(n,{showTableContextMenu:()=>c});var o=t(311804),a=t(539442),l=t(529596);async function c(e,n,t){const c=(0,o.chartWidgetCollectionService)();if(null===c)throw new Error("ChartWidgetCollection should be defined");const d=c.activeChartWidget.value(),s=new a.ActionsProvider(d),i=await s.contextMenuActionsForSources([e]),r=await l.ContextMenuManager.createMenu(i,{},{menuName:"LineToolTableContextMenu"},n);return r.show(t),r}}}]);