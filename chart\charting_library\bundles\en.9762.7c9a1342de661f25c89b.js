(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9762],{753310:e=>{e.exports={en:["Re"]}},185119:e=>{e.exports={en:["Dark"]}},696870:e=>{e.exports={en:["Light"]}},585886:e=>{e.exports={en:["d"]}},244634:e=>{e.exports={en:["h"]}},105977:e=>{e.exports={en:["m"]}},21492:e=>{e.exports={en:["s"]}},297559:e=>{e.exports={en:["{title} copy"]}},38691:e=>{e.exports={en:["D"]}},977995:e=>{e.exports={en:["M"]}},993934:e=>{e.exports={en:["R"]}},882901:e=>{e.exports={en:["T"]}},307408:e=>{e.exports={en:["W"]}},438048:e=>{e.exports={en:["h"]}},768430:e=>{e.exports={en:["m"]}},968823:e=>{e.exports={en:["s"]}},102696:e=>{e.exports={en:["C"]}},943253:e=>{e.exports={en:["H"]}},661372:e=>{e.exports={en:["HL2"]}},255096:e=>{e.exports={en:["HLC3"]}},394174:e=>{e.exports={en:["OHLC4"]}},389923:e=>{e.exports={en:["L"]}},746728:e=>{e.exports={en:["O"]}},932856:e=>{e.exports=Object.create(null),e.exports["%D_input"]={en:["%D"]},e.exports["%K_input"]={en:["%K"]},e.exports["%R_input"]={en:["%R"]},e.exports["+DI_input"]={en:["+DI"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"]},e.exports["-DI_input"]={en:["-DI"]},e.exports["0 Level Color_input"]={en:["0 Level Color"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"]},e.exports["1 Level Color_input"]={en:["1 Level Color"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"]},e.exports["1st Period_input"]={en:["1st Period"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"]},e.exports["2nd Period_input"]={en:["2nd Period"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"]},e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"]},e.exports["3rd Period_input"]={en:["3rd Period"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"]},e.exports["4th Period_input"]={en:["4th Period"]},e.exports["5th Period_input"]={en:["5th Period"]},e.exports["6th Period_input"]={en:["6th Period"]},e.exports.ADR_B_input={en:["ADR_B"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"]},e.exports.ADX_input={en:["ADX"]},e.exports["ATR Mult_input"]={en:["ATR Mult"]},e.exports["ATR length_input"]={en:["ATR length"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"]},e.exports.ATR_input={en:["ATR"]},
e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"]},e.exports["All items_combobox_input"]={en:["All items"]},e.exports.All_input={en:["All"]},e.exports["Anchor Period_input"]={en:["Anchor Period"]},e.exports["Another symbol_input"]={en:["Another symbol"]},e.exports["Aroon Down_input"]={en:["Aroon Down"]},e.exports["Aroon Up_input"]={en:["Aroon Up"]},e.exports.Average_input={en:["Average"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"]},e.exports.Back_input={en:["Back"]},e.exports["Bands style_input"]={en:["Bands style"]},e.exports.Bar_input={en:["Bar"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"]},e.exports["Base Line_input"]={en:["Base Line"]},e.exports.Basis_input={en:["Basis"]},e.exports["Bollinger Bands %B_input"]={en:["Bollinger Bands %B"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"]},e.exports.Borders_input={en:["Borders"]},e.exports["Box size assignment method_input"]={en:["Box size assignment method"]},e.exports["Box size_input"]={en:["Box size"]},e.exports.CCI_input={en:["CCI"]},e.exports.CHOP_input={en:["CHOP"]},e.exports.Cancel_input={en:["Cancel"]},e.exports.Candles_input={en:["Candles"]},e.exports.Centered_input={en:["Centered"]},e.exports.Century_input={en:["Century"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"]},e.exports["Chande MO_input"]={en:["Chande MO"]},e.exports.Close_input={en:["Close"]},e.exports["Color 0_input"]={en:["Color 0"]},e.exports["Color 1_input"]={en:["Color 1"]},e.exports["Color 2_input"]={en:["Color 2"]},e.exports["Color 3_input"]={en:["Color 3"]},e.exports["Color 4_input"]={en:["Color 4"]},e.exports["Color 5_input"]={en:["Color 5"]},e.exports["Color 6_input"]={en:["Color 6"]},e.exports["Color 7_input"]={en:["Color 7"]},e.exports["Color 8_input"]={en:["Color 8"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"]},e.exports["Conversion Line_input"]={en:["Conversion Line"]},e.exports.Correlation_input={en:["Correlation"]},e.exports.Count_input={en:["Count"]},e.exports.Crosses_input={en:["Crosses"]},e.exports.Custom_input={en:["Custom"]},e.exports.DEMA_input={en:["DEMA"]},e.exports["DI Length_input"]={en:["DI Length"]},e.exports.DPO_input={en:["DPO"]},e.exports.D_input={en:["D"]},e.exports.Day_input={en:["Day"]},e.exports["Days Per Year_input"]={en:["Days Per Year"]},e.exports.Decade_input={en:["Decade"]},e.exports.Delta_input={en:["Delta"]},e.exports.Depth_input={en:["Depth"]},e.exports["Detrended Price Oscillator_input"]={en:["Detrended Price Oscillator"]},e.exports["Developing Poc_input"]={en:["Developing Poc"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"]},e.exports.Deviation_input={en:["Deviation"]},e.exports.Divisor_input={en:["Divisor"]},e.exports["Down Volume_input"]={en:["Down Volume"]},e.exports["Down bars_input"]={en:["Down bars"]},
e.exports["Down color_input"]={en:["Down color"]},e.exports["Down fractals_input"]={en:["Down fractals"]},e.exports.EOM_input={en:["EOM"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"]},e.exports["Equality Line_input"]={en:["Equality Line"]},e.exports.Exponential_input={en:["Exponential"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"]},e.exports["Extend Right_input"]={en:["Extend Right"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"]},e.exports["Extend to last bar_input"]={en:["Extend to last bar"]},e.exports.Falling_input={en:["Falling"]},e.exports["Fast Length_input"]={en:["Fast Length"]},e.exports["Fast length_input"]={en:["Fast length"]},e.exports.Fill_input={en:["Fill"]},e.exports.Fisher_input={en:["Fisher"]},e.exports.Growing_input={en:["Growing"]},e.exports["HLC bars_input"]={en:["HLC bars"]},e.exports.HV_input={en:["HV"]},e.exports["Histogram Box_input"]={en:["Histogram Box"]},e.exports.Histogram_input={en:["Histogram"]},e.exports["Hull MA_input"]={en:["Hull MA"]},e.exports.Increment_input={en:["Increment"]},e.exports.Indicator_input={en:["Indicator"]},e.exports["Instrument 1_input"]={en:["Instrument 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"]},e.exports["Jaw Length_input"]={en:["Jaw Length"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"]},e.exports.Jaw_input={en:["Jaw"]},e.exports.KST_input={en:["KST"]},e.exports.K_input={en:["K"]},e.exports["Labels Position_input"]={en:["Labels Position"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"]},e.exports["Lagging Span Periods_input"]={en:["Lagging Span Periods"]},e.exports["Lagging Span_input"]={en:["Lagging Span"]},e.exports["Leading Shift Periods_input"]={en:["Leading Shift Periods"]},e.exports["Leading Span A_input"]={en:["Leading Span A"]},e.exports["Leading Span B_input"]={en:["Leading Span B"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"]},e.exports["Length EMA_input"]={en:["Length EMA"]},e.exports["Length MA_input"]={en:["Length MA"]},e.exports.Length1_input={en:["Length1"]},e.exports.Length2_input={en:["Length2"]},e.exports.Length3_input={en:["Length3"]},e.exports.Length_input={en:["Length"]},e.exports.Level_input={en:["Level"]},e.exports["Levels Format_input"]={en:["Levels Format"]},e.exports.Limit_input={en:["Limit"]},e.exports.Line_input={en:["Line"]},e.exports["Lips Length_input"]={en:["Lips Length"]},e.exports["Lips Offset_input"]={en:["Lips Offset"]
},e.exports.Lips_input={en:["Lips"]},e.exports["Long Length_input"]={en:["Long Length"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"]},e.exports["Long length_input"]={en:["Long length"]},e.exports["Long period_input"]={en:["Long period"]},e.exports.Long_input={en:["Long"]},e.exports["Lower Band_input"]={en:["Lower Band"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"]},e.exports.LowerLimit_input={en:["LowerLimit"]},e.exports.Lower_input={en:["Lower"]},e.exports["MA Length_input"]={en:["MA Length"]},e.exports.MACD_input={en:["MACD"]},e.exports.MA_input={en:["MA"]},e.exports.MF_input={en:["MF"]},e.exports.MM_month_input={en:["MM"]},e.exports.MOM_input={en:["MOM"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"]},e.exports["Market only_input"]={en:["Market only"]},e.exports["Max value_input"]={en:["Max value"]},e.exports.Median_input={en:["Median"]},e.exports.Method_input={en:["Method"]},e.exports.Middle_input={en:["Middle"]},e.exports.Minimize_input={en:["Minimize"]},e.exports.Month_input={en:["Month"]},e.exports.Move_input={en:["Move"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"]},e.exports.Multiplier_input={en:["Multiplier"]},e.exports.NV_input={en:["NV"]},e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"]},e.exports["Number of line_input"]={en:["Number of line"]},e.exports.OSC_input={en:["OSC"]},e.exports.Offset_input={en:["Offset"]},e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"]},e.exports["One step back building_input"]={en:["One step back building"]},e.exports.Oscillator_input={en:["Oscillator"]},e.exports.Overbought_input={en:["Overbought"]},e.exports.Oversold_input={en:["Oversold"]},e.exports.POC_input={en:["POC"]},e.exports.PVT_input={en:["PVT"]},e.exports.P_input={en:["P"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"]},e.exports.Percent_input={en:["Percent"]},e.exports["Percentage LTP({percentageLTPValue}%)_input"]={en:["Percentage LTP({percentageLTPValue}%)"]},e.exports["Percentage LTP_input"]={en:["Percentage LTP"]},e.exports.Percentage_input={en:["Percentage"]},e.exports.Period_input={en:["Period"]},e.exports.Periods_input={en:["Periods"]},e.exports["Phantom bars_input"]={en:["Phantom bars"]},e.exports.Placement_input={en:["Placement"]},e.exports.Plot_input={en:["Plot"]},e.exports["Plots Background_input"]={en:["Plots Background"]},e.exports["Post-market only_input"]={en:["Post-market only"]},e.exports["Pre-market only_input"]={en:["Pre-market only"]},e.exports["Price source_input"]={en:["Price source"]},e.exports.Price_input={en:["Price"]},e.exports["Projection down bars_input"]={en:["Projection down bars"]},e.exports["Projection down color_input"]={en:["Projection down color"]},e.exports["Projection up bars_input"]={en:["Projection up bars"]},e.exports["Projection up color_input"]={en:["Projection up color"]},
e.exports.Q_input={en:["Q"]},e.exports["ROC Length_input"]={en:["ROC Length"]},e.exports.ROCLen1_input={en:["ROCLen1"]},e.exports.ROCLen2_input={en:["ROCLen2"]},e.exports.ROCLen3_input={en:["ROCLen3"]},e.exports.ROCLen4_input={en:["ROCLen4"]},e.exports.ROC_input={en:["ROC"]},e.exports["RSI Length_input"]={en:["RSI Length"]},e.exports["RSI Source_input"]={en:["RSI Source"]},e.exports.RSI_input={en:["RSI"]},e.exports.RVGI_input={en:["RVGI"]},e.exports.RVI_input={en:["RVI"]},e.exports.Range_input={en:["Range"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"]},e.exports["Reversal amount_input"]={en:["Reversal amount"]},e.exports["Rolling Period_input"]={en:["Rolling Period"]},e.exports["Row Size_input"]={en:["Row Size"]},e.exports["Rows Layout_input"]={en:["Rows Layout"]},e.exports.SMALen1_input={en:["SMALen1"]},e.exports.SMALen2_input={en:["SMALen2"]},e.exports.SMALen3_input={en:["SMALen3"]},e.exports.SMALen4_input={en:["SMALen4"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"]},e.exports.SMI_input={en:["SMI"]},e.exports.Session_input={en:["Session"]},e.exports.Sessions_input={en:["Sessions"]},e.exports.Shapes_input={en:["Shapes"]},e.exports["Short Length_input"]={en:["Short Length"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"]},e.exports["Short length_input"]={en:["Short length"]},e.exports["Short period_input"]={en:["Short period"]},e.exports.Short_input={en:["Short"]},e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={en:["Show real prices on price scale (instead of Heikin-Ashi price)"]},e.exports.SigLen_input={en:["SigLen"]},e.exports.Sig_input={en:["Sig"]},e.exports.Sigma_input={en:["Sigma"]},e.exports["Signal Length_input"]={en:["Signal Length"]},e.exports["Signal line period_input"]={en:["Signal line period"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"]},e.exports.Signal_input={en:["Signal"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"]},e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"]},e.exports.Simple_input={en:["Simple"]},e.exports["Slow Length_input"]={en:["Slow Length"]},e.exports["Slow length_input"]={en:["Slow length"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"]},e.exports.Smoothing_input={en:["Smoothing"]},e.exports.Source_input={en:["Source"]},e.exports["Standard Errors_input"]={en:["Standard Errors"]},e.exports.Start_input={en:["Start"]},e.exports.StdDev_input={en:["StdDev"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"]},e.exports.Style_input={en:["Style"]},e.exports.Symbol_input={en:["Symbol"]},e.exports.TEMA_input={en:["TEMA"]},e.exports.TRIX_input={en:["TRIX"]},e.exports["Teeth Length_input"]={en:["Teeth Length"]},e.exports["Teeth Offset_input"]={en:["Teeth Offset"]},e.exports.Teeth_input={en:["Teeth"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"]},e.exports.Timeframe_input={en:["Timeframe"]},e.exports.Total_input={en:["Total"]},
e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"]},e.exports.Traditional_input={en:["Traditional"]},e.exports.Trigger_input={en:["Trigger"]},e.exports.Type_input={en:["Type"]},e.exports.UO_input={en:["UO"]},e.exports["Up Volume_input"]={en:["Up Volume"]},e.exports["Up bars_input"]={en:["Up bars"]},e.exports["Up color_input"]={en:["Up color"]},e.exports["Up fractals_input"]={en:["Up fractals"]},e.exports["Up/Down_input"]={en:["Up/Down"]},e.exports["UpDown Length_input"]={en:["UpDown Length"]},e.exports["Upper Band_input"]={en:["Upper Band"]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"]},e.exports.UpperLimit_input={en:["UpperLimit"]},e.exports.Upper_input={en:["Upper"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"]},e.exports["VI +_input"]={en:["VI +"]},e.exports["VI -_input"]={en:["VI -"]},e.exports.VWAP_input={en:["VWAP"]},e.exports.VWMA_input={en:["VWMA"]},e.exports["Value Area Down_input"]={en:["Value Area Down"]},e.exports["Value Area Up_input"]={en:["Value Area Up"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"]},e.exports["Value Area volume_input"]={en:["Value Area volume"]},e.exports["Value Area_input"]={en:["Value Area"]},e.exports.Value_input={en:["Value"]},e.exports["Values in status line_input"]={en:["Values in status line"]},e.exports.Volume_input={en:["Volume"]},e.exports["WMA Length_input"]={en:["WMA Length"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"]},e.exports.Week_input={en:["Week"]},e.exports.Weighted_input={en:["Weighted"]},e.exports.Wick_input={en:["Wick"]},e.exports.Wicks_input={en:["Wicks"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"]},e.exports["Window Size_input"]={en:["Window Size"]},e.exports.X_input={en:["X"]},e.exports.YY_year_input={en:["YY"]},e.exports.Year_input={en:["Year"]},e.exports["Zero Line_input"]={en:["Zero Line"]},e.exports.Zero_input={en:["Zero"]},e.exports.fastLength_input={en:["fastLength"]},e.exports.from_input={en:["from"]},e.exports.increment_input={en:["increment"]},e.exports.isCentered_input={en:["isCentered"]},e.exports.len_input={en:["len"]},e.exports.length14_input={en:["length14"]},e.exports.length28_input={en:["length28"]},e.exports.length7_input={en:["length7"]},e.exports.lengthRSI_input={en:["lengthRSI"]},e.exports.lengthStoch_input={en:["lengthStoch"]},e.exports.length_input={en:["length"]},e.exports.long_input={en:["long"]},e.exports.longlen_input={en:["longlen"]},e.exports.maximum_input={en:["maximum"]},e.exports.mult_input={en:["mult"]},e.exports.p_input={en:["p"]},
e.exports.q_input={en:["q"]},e.exports.resolution_input={en:["resolution"]},e.exports.roclen1_input={en:["roclen1"]},e.exports.roclen2_input={en:["roclen2"]},e.exports.roclen3_input={en:["roclen3"]},e.exports.roclen4_input={en:["roclen4"]},e.exports.short_input={en:["short"]},e.exports.shortlen_input={en:["shortlen"]},e.exports["show MA_input"]={en:["show MA"]},e.exports.siglen_input={en:["siglen"]},e.exports.signalLength_input={en:["signalLength"]},e.exports.slowLength_input={en:["slowLength"]},e.exports.smalen1_input={en:["smalen1"]},e.exports.smalen2_input={en:["smalen2"]},e.exports.smalen3_input={en:["smalen3"]},e.exports.smalen4_input={en:["smalen4"]},e.exports.smoothD_input={en:["smoothD"]},e.exports.smoothK_input={en:["smoothK"]},e.exports.start_input={en:["start"]},e.exports.sym_input={en:["sym"]},e.exports.to_input={en:["to"]},e.exports.useTrueRange_input={en:["useTrueRange"]},e.exports.x_input={en:["x"]},e.exports["yay Color 0_input"]={en:["yay Color 0"]},e.exports["yay Color 1_input"]={en:["yay Color 1"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"]}},550873:e=>{e.exports={en:["ATR({atrValue})"]}},273425:e=>{e.exports={en:["Percentage LTP({percentageLTPValue}%)"]}},640566:e=>{e.exports={en:["Traditional"]}},35359:e=>{e.exports={en:["Post"]}},293866:e=>{e.exports={en:["Pre"]}},775163:e=>{e.exports={en:["Invert scale"]}},235210:e=>{e.exports={en:["Indexed to 100"]}},131340:e=>{e.exports={en:["Logarithmic"]}},19405:e=>{e.exports={en:["No overlapping labels"]}},34954:e=>{e.exports={en:["Percent"]}},655300:e=>{e.exports={en:["Regular"]}},8029:e=>{e.exports={en:["ETH"]}},34647:e=>{e.exports={en:["Electronic trading hours"]}},836862:e=>{e.exports={en:["Extended trading hours"]}},207807:e=>{e.exports={en:["POST"]}},346273:e=>{e.exports={en:["PRE"]}},450434:e=>{e.exports={en:["Postmarket"]}},659330:e=>{e.exports={en:["Premarket"]}},692158:e=>{e.exports={en:["RTH"]}},884246:e=>{e.exports={en:["Regular trading hours"]}},13132:e=>{e.exports={en:["May"]}},783477:e=>{e.exports=Object.create(null),e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"]},e.exports["52 Week High/Low_study"]={en:["52 Week High/Low"]},e.exports.ASI_study={en:["ASI"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"]},e.exports["Accelerator Oscillator_study"]={en:["Accelerator Oscillator"]},e.exports["Accounts payable_study"]={en:["Accounts payable"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"]},e.exports.Accruals_study={en:["Accruals"]},e.exports["Accrued payroll_study"]={en:["Accrued payroll"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"]},
e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"]},e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"]},e.exports["Advance/Decline_study"]={en:["Advance/Decline"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"]},e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"]},e.exports.Amortization_study={en:["Amortization"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"]},e.exports["Arnaud Legoux Moving Average_study"]={en:["Arnaud Legoux Moving Average"]},e.exports.Aroon_study={en:["Aroon"]},e.exports["Asset turnover_study"]={en:["Asset turnover"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"]},e.exports["Average Daily Range_study"]={en:["Average Daily Range"]},e.exports["Average Day Range_study"]={en:["Average Day Range"]},e.exports["Average Directional Index_study"]={en:["Average Directional Index"]},e.exports["Average Price_study"]={en:["Average Price"]},e.exports["Average True Range_study"]={en:["Average True Range"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"]},e.exports["Balance of Power_study"]={en:["Balance of Power"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"]},e.exports["Basic EPS_study"]={en:["Basic EPS"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"]},e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"]},e.exports["Book value per share_study"]={en:["Book value per share"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"]},
e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"]},e.exports["Buyback yield %_study"]={en:["Buyback yield %"]},e.exports["COGS to revenue ratio_study"]={en:["COGS to revenue ratio"]},e.exports.CRSI_study={en:["CRSI"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"]},e.exports["Capital expenditures - other assets_study"]={en:["Capital expenditures - other assets"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"]},e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"]},e.exports["Cash from operating activities_study"]={en:["Cash from operating activities"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"]},e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"]},e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"]},e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"]},e.exports["Change in accounts receivable_study"]={en:["Change in accounts receivable"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"]},e.exports["Change in inventories_study"]={en:["Change in inventories"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"]},e.exports["Changes in working capital_study"]={en:["Changes in working capital"]},e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"]},e.exports["Chop Zone_study"]={en:["Chop Zone"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"]},e.exports["Common equity, total_study"]={en:["Common equity, total"]},e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"]},e.exports.Compare_study={en:["Compare"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"]},e.exports["Connors RSI_study"]={en:["Connors RSI"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"]},e.exports["Consecutive Up/Down Strategy_study"]={
en:["Consecutive Up/Down Strategy"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"]},e.exports["Cost of goods_study"]={en:["Cost of goods"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"]},e.exports["Current ratio_study"]={en:["Current ratio"]},e.exports.DMI_study={en:["DMI"]},e.exports["Dark Cloud Cover - Bearish_study"]={en:["Dark Cloud Cover - Bearish"]},e.exports["Days inventory_study"]={en:["Days inventory"]},e.exports["Days payable_study"]={en:["Days payable"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"]},e.exports["Debt to revenue ratio_study"]={en:["Debt to revenue ratio"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"]},e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"]},e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"]},e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"]},e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"]},e.exports.Depreciation_study={en:["Depreciation"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"]},e.exports["Directional Movement_study"]={en:["Directional Movement"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"]},e.exports["Dividends payable_study"]={en:["Dividends payable"]},
e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"]},e.exports.Doji_study={en:["Doji"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"]},e.exports["Double EMA_study"]={en:["Double EMA"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"]},e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"]},e.exports.EBITDA_study={en:["EBITDA"]},e.exports.EBIT_study={en:["EBIT"]},e.exports["EMA Cross_study"]={en:["EMA Cross"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"]},e.exports["EPS estimates_study"]={en:["EPS estimates"]},e.exports["Earnings yield_study"]={en:["Earnings yield"]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"]},e.exports["Ease of Movement_study"]={en:["Ease of Movement"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"]},e.exports["Elder's Force Index_study"]={en:["Elder's Force Index"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"]},e.exports["Enterprise value_study"]={en:["Enterprise value"]},e.exports.Envelope_study={en:["Envelope"]},e.exports.Envelopes_study={en:["Envelopes"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"]},e.exports["Equity to assets ratio_study"]={en:["Equity to assets ratio"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"]},e.exports["Falling Window - Bearish_study"]={en:["Falling Window - Bearish"]},e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"]},e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"]},e.exports["Fisher Transform_study"]={
en:["Fisher Transform"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"]},e.exports["Fixed Range_study"]={en:["Fixed Range"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"]},e.exports["Free cash flow_study"]={en:["Free cash flow"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"]},e.exports["Funds from operations_study"]={en:["Funds from operations"]},e.exports.Gaps_study={en:["Gaps"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"]},e.exports["Goodwill, net_study"]={en:["Goodwill, net"]},e.exports["Graham's number_study"]={en:["Graham's number"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"]},e.exports["Gross margin %_study"]={en:["Gross margin %"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"]},e.exports["Gross profit_study"]={en:["Gross profit"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"]},e.exports["Historical Volatility_study"]={en:["Historical Volatility"]},e.exports["Hull Moving Average_study"]={en:["Hull Moving Average"]},e.exports["Ichimoku Cloud_study"]={en:["Ichimoku Cloud"]},e.exports.Ichimoku_study={en:["Ichimoku"]},e.exports.Impairments_study={en:["Impairments"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"]},e.exports["Income tax payable_study"]={en:["Income tax payable"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"]},e.exports["Income tax, current_study"]={en:["Income tax, current"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"]},e.exports["Interest coverage_study"]={en:["Interest coverage"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"]},
e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"]},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"]},e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"]},e.exports["Investments in unconsolidated subsidiaries_study"]={en:["Investments in unconsolidated subsidiaries"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"]},e.exports["Issuance/retirement of short term debt_study"]={en:["Issuance/retirement of short term debt"]},e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"]},e.exports["KZ index_study"]={en:["KZ index"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"]},e.exports["Keltner Channels_study"]={en:["Keltner Channels"]},e.exports["Key stats_study"]={en:["Key stats"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"]},e.exports["Kicking - Bullish_study"]={en:["Kicking - Bullish"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"]},e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"]},e.exports["Linear Regression Slope_study"]={en:["Linear Regression Slope"]},e.exports["Linear Regression_study"]={en:["Linear Regression"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"]},
e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"]},e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"]},e.exports["Long term debt_study"]={en:["Long term debt"]},e.exports["Long term investments_study"]={en:["Long term investments"]},e.exports["MA Cross_study"]={en:["MA Cross"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"]},e.exports.MACD_study={en:["MACD"]},e.exports["Majority Rule_study"]={en:["Majority Rule"]},e.exports["Market capitalization_study"]={en:["Market capitalization"]},e.exports["Marubozu Black - Bearish_study"]={en:["Marubozu Black - Bearish"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"]},e.exports["Mass Index_study"]={en:["Mass Index"]},e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"]},e.exports["Median Price_study"]={en:["Median Price"]},e.exports.Median_study={en:["Median"]},e.exports["Minority interest_study"]={en:["Minority interest"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"]},e.exports.Momentum_study={en:["Momentum"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"]},e.exports["Money Flow_study"]={en:["Money Flow"]},e.exports["Moon Phases_study"]={en:["Moon Phases"]},e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"]},e.exports["Moving Average Convergence/Divergence_study"]={en:["Moving Average Convergence/Divergence"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"]},e.exports["Moving Average Modified_study"]={en:["Moving Average Modified"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"]},e.exports["Moving Average_study"]={en:["Moving Average"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"]},e.exports["Net Volume_study"]={en:["Net Volume"]},e.exports["Net current asset value per share_study"]={
en:["Net current asset value per share"]},e.exports["Net debt_study"]={en:["Net debt"]},e.exports["Net income (cash flow)_study"]={en:["Net income (cash flow)"]},e.exports["Net income before discontinued operations_study"]={en:["Net income before discontinued operations"]},e.exports["Net income per employee_study"]={en:["Net income per employee"]},e.exports["Net income_study"]={en:["Net income"]},e.exports["Net intangible assets_study"]={en:["Net intangible assets"]},e.exports["Net margin %_study"]={en:["Net margin %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"]},e.exports["Non-cash items_study"]={en:["Non-cash items"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"]},e.exports["Note receivable - long term_study"]={en:["Note receivable - long term"]},e.exports["Notes payable_study"]={en:["Notes payable"]},e.exports["Number of employees_study"]={en:["Number of employees"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"]},e.exports["On Balance Volume_study"]={en:["On Balance Volume"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"]},e.exports["Open Interest_study"]={en:["Open Interest"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"]},e.exports["Operating income_study"]={en:["Operating income"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"]},e.exports["Operating margin %_study"]={en:["Operating margin %"]},e.exports["Other COGS_study"]={en:["Other COGS"]},e.exports["Other common equity_study"]={en:["Other common equity"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"]},e.exports["Other investing cash flow items, total_study"]={en:["Other investing cash flow items, total"]},e.exports["Other investments_study"]={en:["Other investments"]},e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"]},
e.exports["Other receivables_study"]={en:["Other receivables"]},e.exports["Other short term debt_study"]={en:["Other short term debt"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"]},e.exports.Overlay_study={en:["Overlay"]},e.exports["PEG ratio_study"]={en:["PEG ratio"]},e.exports["Paid in capital_study"]={en:["Paid in capital"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"]},e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"]},e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"]},e.exports["Pivot Extension Strategy_study"]={en:["Pivot Extension Strategy"]},e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"]},e.exports["Pivot Reversal Strategy_study"]={en:["Pivot Reversal Strategy"]},e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"]},e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"]},e.exports["Pretax income_study"]={en:["Pretax income"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"]},e.exports["Price Channel_study"]={en:["Price Channel"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"]},e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"]},e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"]},e.exports["Quality ratio_study"]={en:["Quality ratio"]},e.exports["Quick ratio_study"]={en:["Quick ratio"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"]},
e.exports["Rectangle Chart Pattern_study"]={en:["Rectangle Chart Pattern"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"]},e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"]},e.exports["Repurchase of common & preferred stock_study"]={en:["Repurchase of common & preferred stock"]},e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"]},e.exports["Research & development_study"]={en:["Research & development"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"]},e.exports["Retained earnings_study"]={en:["Retained earnings"]},e.exports["Return on assets %_study"]={en:["Return on assets %"]},e.exports["Return on equity %_study"]={en:["Return on equity %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"]},e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"]},e.exports["Revenue estimates_study"]={en:["Revenue estimates"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"]},e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"]},e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"]},e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"]},e.exports["Rob Booker - Missed Pivot Points_study"]={en:["Rob Booker - Missed Pivot Points"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={en:["Rob Booker - Ziv Ghost Pivots"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"]},e.exports["Selling/general/admin expenses, total_study"]={
en:["Selling/general/admin expenses, total"]},e.exports["Session Volume HD_study"]={en:["Session Volume HD"]},e.exports["Session Volume Profile HD_study"]={en:["Session Volume Profile HD"]},e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"]},e.exports["Session Volume_study"]={en:["Session Volume"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"]},e.exports["Short term debt_study"]={en:["Short term debt"]},e.exports["Short term investments_study"]={en:["Short term investments"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"]},e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"]},e.exports["Springate score_study"]={en:["Springate score"]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"]},e.exports["Standard Error_study"]={en:["Standard Error"]},e.exports.Stoch_study={en:["Stoch"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"]},e.exports.Stochastic_study={en:["Stochastic"]},e.exports.SuperTrend_study={en:["SuperTrend"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"]},e.exports.Supertrend_study={en:["Supertrend"]},e.exports["Sustainable growth rate_study"]={en:["Sustainable growth rate"]},e.exports.TRIX_study={en:["TRIX"]},e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"]},e.exports["Tangible common equity ratio_study"]={en:["Tangible common equity ratio"]},e.exports.Taxes_study={en:["Taxes"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"]},e.exports.Technicals_study={en:["Technicals"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"]},e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"]},e.exports["Total assets_study"]={en:["Total assets"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"]},e.exports["Total current assets_study"]={en:["Total current assets"]},e.exports["Total current liabilities_study"]={en:["Total current liabilities"]},
e.exports["Total debt_study"]={en:["Total debt"]},e.exports["Total equity_study"]={en:["Total equity"]},e.exports["Total inventory_study"]={en:["Total inventory"]},e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"]},e.exports["Total liabilities_study"]={en:["Total liabilities"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"]},e.exports["Total revenue_study"]={en:["Total revenue"]},e.exports["Trading Sessions_study"]={en:["Trading Sessions"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"]},e.exports["Triple Bottom Chart Pattern_study"]={en:["Triple Bottom Chart Pattern"]},e.exports["Triple EMA_study"]={en:["Triple EMA"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"]},e.exports["True Strength Index_study"]={en:["True Strength Index"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"]},e.exports["Typical Price_study"]={en:["Typical Price"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"]},e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"]},e.exports.VWAP_study={en:["VWAP"]},e.exports.VWMA_study={en:["VWMA"]},e.exports["Valuation ratios_study"]={en:["Valuation ratios"]},e.exports["Visible Average Price_study"]={en:["Visible Average Price"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"]},e.exports["Visible Range_study"]={en:["Visible Range"]},e.exports.Vol_study={en:["Vol"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"]},e.exports["Volatility Index_study"]={en:["Volatility Index"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"]},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"]},e.exports["Volume Weighted Average Price_study"]={en:["Volume Weighted Average Price"]},e.exports["Volume Weighted Moving Average_study"]={
en:["Volume Weighted Moving Average"]},e.exports.Volume_study={en:["Volume"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"]},e.exports["Williams %R_study"]={en:["Williams %R"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"]},e.exports["Williams Fractals_study"]={en:["Williams Fractals"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"]},e.exports["Zig Zag_study"]={en:["Zig Zag"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"]}},689633:e=>{e.exports={en:["Anchored Volume Profile"]}},925705:e=>{e.exports={en:["Fixed Range Volume Profile"]}},424261:e=>{e.exports={en:["Vol"]}},267736:e=>{e.exports={en:["Minor"]}},200922:e=>{e.exports={en:["Minute"]}},691405:e=>{e.exports={en:["Text"]}},978972:e=>{e.exports={en:["Couldn't copy"]}},410615:e=>{e.exports={en:["Couldn't cut"]}},981518:e=>{e.exports={en:["Couldn't paste"]}},583140:e=>{e.exports={en:["Countdown to bar close"]}},10871:e=>{e.exports={en:["Colombo"]}},655761:e=>{e.exports={en:["Columns"]}},309818:e=>{e.exports={en:["Comment"]}},253942:e=>{e.exports={en:["Compare or Add Symbol"]}},212086:e=>{e.exports={en:["Compilation error"]}},271692:e=>{e.exports={en:["Confirm to remove locked drawings"]}},648141:e=>{e.exports={en:["Confirm Inputs"]}},381605:e=>{e.exports={en:["Confirm Remove Study Tree"]}},538917:e=>{e.exports={en:["Copenhagen"]}},249680:e=>{e.exports={en:["Copy"]}},166134:e=>{e.exports={en:["Copy Chart Layout"]}},163553:e=>{e.exports={en:["Copy price"]}},365736:e=>{e.exports={en:["Cairo"]}},925381:e=>{e.exports={en:["Callout"]}},745054:e=>{e.exports={en:["Candles"]}},930948:e=>{e.exports={en:["Caracas"]}},770409:e=>{e.exports={en:["Casablanca"]}},537276:e=>{e.exports={en:["Change"]}},885124:e=>{e.exports={en:["Change Symbol"]}},102569:e=>{e.exports={en:["Change interval"]}},409687:e=>{e.exports={en:["Change interval. Press number or comma"]}},836332:e=>{e.exports={en:["Change symbol. Start typing symbol name"]}},548566:e=>{e.exports={en:["Change scale currency"]}},185110:e=>{e.exports={en:["Change scale unit"]}},556275:e=>{e.exports={en:["Chart #{index}"]}},639950:e=>{e.exports={en:["Chart Properties"]}},698856:e=>{e.exports={en:["Chart by TradingView"]}},501136:e=>{e.exports={en:["Chart for {symbol}, {interval}"]}},569804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"]}},506655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"]}},72452:e=>{e.exports={en:["Chicago"]}},850349:e=>{e.exports={en:["Chongqing"]}},191944:e=>{e.exports={en:["Circle"]}},414985:e=>{e.exports={en:["Click to set a point"]}},712537:e=>{e.exports={en:["Clone"]}},862578:e=>{e.exports={en:["Close"]}},800264:e=>{e.exports={en:["Create limit order"]}},506969:e=>{e.exports={en:["Cross"]}},974334:e=>{e.exports={en:["Cross Line"]}},259396:e=>{e.exports={en:["Currencies"]}},720177:e=>{e.exports={en:["Current interval and above"]}},
700494:e=>{e.exports={en:["Current interval and below"]}},960668:e=>{e.exports={en:["Current interval only"]}},678609:e=>{e.exports={en:["Curve"]}},787380:e=>{e.exports={en:["Cycle"]}},584031:e=>{e.exports={en:["Cyclic Lines"]}},193191:e=>{e.exports={en:["Cypher Pattern"]}},607219:e=>{e.exports={en:["A layout with that name already exists"]}},167635:e=>{e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"]}},246712:e=>{e.exports={en:["ABCD Pattern"]}},936485:e=>{e.exports={en:["Amsterdam"]}},24185:e=>{e.exports={en:["Anchor"]}},942630:e=>{e.exports={en:["Anchorage"]}},642669:e=>{e.exports={en:["Anchored Text"]}},584541:e=>{e.exports={en:["Anchored VWAP"]}},377401:e=>{e.exports={en:["Access error"]}},346501:e=>{e.exports={en:["Add Symbol"]}},702439:e=>{e.exports={en:["Add financial metric for {instrumentName}"]}},235088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"]}},535679:e=>{e.exports={en:["Add this financial metric to entire layout"]}},910996:e=>{e.exports={en:["Add this financial metric to favorites"]}},326090:e=>{e.exports={en:["Add this indicator to entire layout"]}},792957:e=>{e.exports={en:["Add this indicator to favorites"]}},995754:e=>{e.exports={en:["Add this strategy to entire layout"]}},239010:e=>{e.exports={en:["Add this symbol to entire layout"]}},295829:e=>{e.exports={en:["Add {symbol} to watchlist"]}},437265:e=>{e.exports={en:["Adelaide"]}},840452:e=>{e.exports={en:["Always invisible"]}},336299:e=>{e.exports={en:["Always visible"]}},858026:e=>{e.exports={en:["All intervals"]}},178358:e=>{e.exports={en:["Apply default"]}},222437:e=>{e.exports={en:["Apply these indicators to entire layout"]}},227072:e=>{e.exports={en:["Apr"]}},659324:e=>{e.exports={en:["Arc"]}},534456:e=>{e.exports={en:["Area"]}},511858:e=>{e.exports={en:["Arrow"]}},734247:e=>{e.exports={en:["Arrow Down"]}},936352:e=>{e.exports={en:["Arrow Marker"]}},673193:e=>{e.exports={en:["Arrow Mark Down"]}},401949:e=>{e.exports={en:["Arrow Mark Left"]}},886275:e=>{e.exports={en:["Arrow Mark Right"]}},162453:e=>{e.exports={en:["Arrow Mark Up"]}},777231:e=>{e.exports={en:["Arrow Up"]}},398128:e=>{e.exports={en:["Astana"]}},763627:e=>{e.exports={en:["Ashgabat"]}},872445:e=>{e.exports={en:["At close"]}},373702:e=>{e.exports={en:["Athens"]}},221469:e=>{e.exports={en:["Auto"]}},224157:e=>{e.exports={en:["Auto (fits data to screen)"]}},546450:e=>{e.exports={en:["Aug"]}},721841:e=>{e.exports={en:["Average close price label"]}},916138:e=>{e.exports={en:["Average close price line"]}},873025:e=>{e.exports={en:["Avg"]}},287580:e=>{e.exports={en:["Azores"]}},273905:e=>{e.exports={en:["Bogota"]}},139403:e=>{e.exports={en:["Back"]}},290594:e=>{e.exports={en:["Bahrain"]}},770540:e=>{e.exports={en:["Balloon"]}},947045:e=>{e.exports={en:["Bangkok"]}},892101:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Want to exit Bar Replay?"]}},432482:e=>{e.exports={en:["Bar Replay isn't available for this symbol. Want to exit Bar Replay?"]}},185902:e=>{e.exports={
en:["Bar Replay isn't available for this time interval. Want to exit Bar Replay?"]}},527377:e=>{e.exports={en:["Bars"]}},381994:e=>{e.exports={en:["Bars Pattern"]}},259213:e=>{e.exports={en:["Baseline"]}},271797:e=>{e.exports={en:["Belgrade"]}},764313:e=>{e.exports={en:["Berlin"]}},398810:e=>{e.exports={en:["Bid and ask labels"]}},292590:e=>{e.exports={en:["Bid and ask lines"]}},243539:e=>{e.exports={en:["Brush"]}},991499:e=>{e.exports={en:["Brussels"]}},270876:e=>{e.exports={en:["Bratislava"]}},55481:e=>{e.exports={en:["Bring forward"]}},417293:e=>{e.exports={en:["Bring to front"]}},579336:e=>{e.exports={en:["Brisbane"]}},633672:e=>{e.exports={en:["Bucharest"]}},20313:e=>{e.exports={en:["Budapest"]}},325282:e=>{e.exports={en:["Buenos Aires"]}},146768:e=>{e.exports={en:["By TradingView"]}},754280:e=>{e.exports={en:["Go to date"]}},774975:e=>{e.exports={en:["Go to {lineToolName}"]}},515462:e=>{e.exports={en:["Got it"]}},947460:e=>{e.exports={en:["Gann Box"]}},748683:e=>{e.exports={en:["Gann Fan"]}},844763:e=>{e.exports={en:["Gann Square"]}},960707:e=>{e.exports={en:["Gann Square Fixed"]}},546808:e=>{e.exports={en:["Ghost Feed"]}},357726:e=>{e.exports={en:["Grand supercycle"]}},715096:e=>{e.exports={en:["Do you really want to delete indicator template '{name}' ?"]}},377174:e=>{e.exports={en:["Do you really want to delete study and all of it's children?"]}},77125:e=>{e.exports={en:["Double Curve"]}},9430:e=>{e.exports={en:["Double-click any edge to reset layout grid"]}},275296:e=>{e.exports={en:["Double-click to finish Path"]}},317409:e=>{e.exports={en:["Double-click to finish Polyline"]}},436539:e=>{e.exports={en:["Double-tap any edge to reset layout grid"]}},957131:e=>{e.exports={en:["Data Provided by"]}},748741:e=>{e.exports={en:["Data Window"]}},722677:e=>{e.exports={en:["Date"]}},485444:e=>{e.exports={en:["Date Range"]}},247017:e=>{e.exports={en:["Date and Price Range"]}},532084:e=>{e.exports={en:["Dec"]}},623403:e=>{e.exports={en:["Degree"]}},327358:e=>{e.exports={en:["Denver"]}},724959:e=>{e.exports={en:["Dhaka"]}},415179:e=>{e.exports={en:["Diamond"]}},91544:e=>{e.exports={en:["Disjoint Channel"]}},970132:e=>{e.exports={en:["Displacement"]}},993864:e=>{e.exports={en:["Drawings toolbar"]}},856916:e=>{e.exports={en:["Draw Horizontal Line at {price}"]}},523650:e=>{e.exports={en:["Dubai"]}},479716:e=>{e.exports={en:["Dublin"]}},273456:e=>{e.exports={en:["Emoji"]}},609541:e=>{e.exports={en:["Enter a new chart layout name"]}},980943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"]}},861114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"]}},872359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"]}},978996:e=>{e.exports={en:["Ellipse"]}},452788:e=>{e.exports={en:["Extended Line"]}},786905:e=>{e.exports={en:["Exchange"]}},319271:e=>{e.exports={en:["Existing pane above"]}},546545:e=>{e.exports={en:["Existing pane below"]}},920138:e=>{e.exports={en:["Forecast"]}},797057:e=>{e.exports={
en:["Forward"]}},302507:e=>{e.exports={en:["Feb"]}},659005:e=>{e.exports={en:["Fib Channel"]}},182330:e=>{e.exports={en:["Fib Circles"]}},855986:e=>{e.exports={en:["Fib Retracement"]}},633880:e=>{e.exports={en:["Fib Speed Resistance Arcs"]}},202395:e=>{e.exports={en:["Fib Speed Resistance Fan"]}},239014:e=>{e.exports={en:["Fib Spiral"]}},930622:e=>{e.exports={en:["Fib Time Zone"]}},485042:e=>{e.exports={en:["Fib Wedge"]}},333885:e=>{e.exports={en:["Flag"]}},514600:e=>{e.exports={en:["Flag Mark"]}},745051:e=>{e.exports={en:["Flat Top/Bottom"]}},339643:e=>{e.exports={en:["Fraction part is invalid."]}},224077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"]}},31561:e=>{e.exports={en:["Kolkata"]}},454533:e=>{e.exports={en:["Kathmandu"]}},383490:e=>{e.exports={en:["Kagi"]}},70913:e=>{e.exports={en:["Karachi"]}},76614:e=>{e.exports={en:["Kuwait"]}},538561:e=>{e.exports={en:["Kuala Lumpur"]}},99906:e=>{e.exports={en:["HLC area"]}},800886:e=>{e.exports={en:["HLC bars"]}},334491:e=>{e.exports={en:["Ho Chi Minh"]}},913459:e=>{e.exports={en:["Hollow candles"]}},248861:e=>{e.exports={en:["Hong Kong"]}},879668:e=>{e.exports={en:["Honolulu"]}},121795:e=>{e.exports={en:["Horizontal Line"]}},325487:e=>{e.exports={en:["Horizontal Ray"]}},921928:e=>{e.exports={en:["Head and Shoulders"]}},863876:e=>{e.exports={en:["Heikin Ashi"]}},348203:e=>{e.exports={en:["Helsinki"]}},327298:e=>{e.exports={en:["Hide"]}},47074:e=>{e.exports={en:["Hide all"]}},752563:e=>{e.exports={en:["Hide all drawings"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"]}},418216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"]}},178525:e=>{e.exports={en:["Hide all indicators"]}},842164:e=>{e.exports={en:["Hide all positions & orders"]}},703217:e=>{e.exports={en:["Hide drawings"]}},897878:e=>{e.exports={en:["Hide events on chart"]}},72351:e=>{e.exports={en:["Hide indicators"]}},328345:e=>{e.exports={en:["Hide marks on bars"]}},492226:e=>{e.exports={en:["Hide positions & orders"]}},778254:e=>{e.exports={en:["High"]}},498236:e=>{e.exports={en:["High-low"]}},399479:e=>{e.exports={en:["High and low price labels"]}},433766:e=>{e.exports={en:["High and low price lines"]}},969476:e=>{e.exports={en:["Highlighter"]}},872819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},594966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."]}},566751:e=>{e.exports={en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},868065:e=>{e.exports={en:["Image"]}},997802:e=>{e.exports={en:["Intervals less than {resolution} are not supported for {ticker}."]}},410268:e=>{e.exports={en:["Intermediate"]}},314285:e=>{e.exports={en:["Invalid Symbol"]}},652969:e=>{e.exports={en:["Invalid symbol"]}},37189:e=>{e.exports={en:["Invert scale"]}},189999:e=>{e.exports={en:["Indexed to 100"]}},146850:e=>{e.exports={
en:["Indicators value labels"]}},554418:e=>{e.exports={en:["Indicators name labels"]}},840490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"]}},715992:e=>{e.exports={en:["Info Line"]}},287829:e=>{e.exports={en:["Insert indicator"]}},141686:e=>{e.exports={en:["Inside Pitchfork"]}},37913:e=>{e.exports={en:["Icon"]}},478326:e=>{e.exports={en:["Istanbul"]}},239585:e=>{e.exports={en:["Johannesburg"]}},414995:e=>{e.exports={en:["Jakarta"]}},562310:e=>{e.exports={en:["Jan"]}},436057:e=>{e.exports={en:["Jerusalem"]}},853786:e=>{e.exports={en:["Jul"]}},800429:e=>{e.exports={en:["Jun"]}},367560:e=>{e.exports={en:["Juneau"]}},562329:e=>{e.exports={en:["On the left"]}},755813:e=>{e.exports={en:["On the right"]}},864818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."]}},621064:e=>{e.exports={en:["Oops!"]}},251221:e=>{e.exports={en:["Object Tree"]}},912179:e=>{e.exports={en:["Oct"]}},16610:e=>{e.exports={en:["Open"]}},27884:e=>{e.exports={en:["Open layout. Press period"]}},375722:e=>{e.exports={en:["Oslo"]}},165318:e=>{e.exports={en:["Low"]}},651077:e=>{e.exports={en:["Lock"]}},279777:e=>{e.exports={en:["Lock/unlock"]}},566005:e=>{e.exports={en:["Lock vertical cursor line by time"]}},314017:e=>{e.exports={en:["Lock price to bar ratio"]}},116170:e=>{e.exports={en:["Logarithmic"]}},119439:e=>{e.exports={en:["London"]}},674832:e=>{e.exports={en:["Long Position"]}},28733:e=>{e.exports={en:["Los Angeles"]}},785924:e=>{e.exports={en:["Label Down"]}},649857:e=>{e.exports={en:["Label Up"]}},5119:e=>{e.exports={en:["Labels"]}},919931:e=>{e.exports={en:["Lagos"]}},463815:e=>{e.exports={en:["Last day change"]}},759444:e=>{e.exports={en:["Lima"]}},903554:e=>{e.exports={en:["Line"]}},409394:e=>{e.exports={en:["Line with markers"]}},469576:e=>{e.exports={en:["Line Break"]}},743588:e=>{e.exports={en:["Line break"]}},156982:e=>{e.exports={en:["Lines"]}},337367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"]}},53375:e=>{e.exports={en:["Lisbon"]}},681038:e=>{e.exports={en:["Luxembourg"]}},360663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"]}},635049:e=>{e.exports={en:["Move to"]}},287849:e=>{e.exports={en:["Move chart"]}},26493:e=>{e.exports={en:["Move scale to left"]}},140789:e=>{e.exports={en:["Move scale to right"]}},657681:e=>{e.exports={en:["Modified Schiff Pitchfork"]}},793907:e=>{e.exports={en:["More settings"]}},564039:e=>{e.exports={en:["Moscow"]}},52066:e=>{e.exports={en:["Madrid"]}},638365:e=>{e.exports={en:["Malta"]}},348991:e=>{e.exports={en:["Manila"]}},92767:e=>{e.exports={en:["Mar"]}},773332:e=>{e.exports={en:["Mexico City"]}},488314:e=>{e.exports={en:["Merge all scales into one"]}},454215:e=>{e.exports={en:["Mixed"]}},324866:e=>{e.exports={en:["Micro"]}},487957:e=>{e.exports={en:["Millennium"]}},214724:e=>{e.exports={en:["Minuette"]}},78273:e=>{e.exports={en:["Minuscule"]}},909865:e=>{e.exports={en:["Muscat"]}},696935:e=>{e.exports={en:["N/A"]}},836252:e=>{e.exports={en:["No data here"]}},411254:e=>{e.exports={
en:["No scale (fullscreen)"]}},709140:e=>{e.exports={en:["No sync"]}},150910:e=>{e.exports={en:["No volume data"]}},99024:e=>{e.exports={en:["No, keep them"]}},794389:e=>{e.exports={en:["Note"]}},526899:e=>{e.exports={en:["Nov"]}},467891:e=>{e.exports={en:["Norfolk Island"]}},740977:e=>{e.exports={en:["Nairobi"]}},940544:e=>{e.exports={en:["New York"]}},866103:e=>{e.exports={en:["New Zealand"]}},715512:e=>{e.exports={en:["New pane above"]}},752160:e=>{e.exports={en:["New pane below"]}},94600:e=>{e.exports={en:["Nicosia"]}},673013:e=>{e.exports={en:["Something went wrong"]}},83524:e=>{e.exports={en:["Something went wrong when creating the indicator."]}},504509:e=>{e.exports={en:["Something went wrong. Please try again later."]}},443047:e=>{e.exports={en:["Save New Chart Layout"]}},276266:e=>{e.exports={en:["Save as"]}},255502:e=>{e.exports={en:["San Salvador"]}},630231:e=>{e.exports={en:["Santiago"]}},991912:e=>{e.exports={en:["Sao Paulo"]}},243931:e=>{e.exports={en:["Scale currency"]}},243758:e=>{e.exports={en:["Scale price chart only"]}},540012:e=>{e.exports={en:["Scale unit"]}},942608:e=>{e.exports={en:["Schiff Pitchfork"]}},976078:e=>{e.exports={en:["Script may be not updated if you leave the page."]}},232514:e=>{e.exports={en:["Settings"]}},370784:e=>{e.exports={en:["Second fraction part is invalid."]}},975594:e=>{e.exports={en:["Security info"]}},21973:e=>{e.exports={en:["Send to back"]}},171179:e=>{e.exports={en:["Send backward"]}},326820:e=>{e.exports={en:["Seoul"]}},806816:e=>{e.exports={en:["Sep"]}},194031:e=>{e.exports={en:["Session"]}},383298:e=>{e.exports={en:["Session volume profile"]}},366707:e=>{e.exports={en:["Session breaks"]}},601852:e=>{e.exports={en:["Shanghai"]}},508075:e=>{e.exports={en:["Short Position"]}},698334:e=>{e.exports={en:["Show"]}},185891:e=>{e.exports={en:["Show all drawings"]}},225881:e=>{e.exports={en:["Show all drawings and indicators"]}},686738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"]}},598753:e=>{e.exports={en:["Show all indicators"]}},555418:e=>{e.exports={en:["Show all ideas"]}},420506:e=>{e.exports={en:["Show all positions & orders"]}},833158:e=>{e.exports={en:["Show continuous contract switch"]}},81465:e=>{e.exports={en:["Show contract expiration"]}},629449:e=>{e.exports={en:["Show dividends"]}},537113:e=>{e.exports={en:["Show earnings"]}},510261:e=>{e.exports={en:["Show ideas of followed users"]}},62986:e=>{e.exports={en:["Show latest news"]}},644020:e=>{e.exports={en:["Show my ideas only"]}},350849:e=>{e.exports={en:["Show splits"]}},467751:e=>{e.exports={en:["Signpost"]}},977377:e=>{e.exports={en:["Singapore"]}},839090:e=>{e.exports={en:["Sine Line"]}},66205:e=>{e.exports={en:["Square"]}},586146:e=>{e.exports={en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."]}},492516:e=>{e.exports={en:["Style"]}},561507:e=>{e.exports={en:["Stack on the left"]}},597800:e=>{e.exports={en:["Stack on the right"]}},485166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"]}},704035:e=>{e.exports={
en:["Stay in drawing mode"]}},869217:e=>{e.exports={en:["Step line"]}},443114:e=>{e.exports={en:["Sticker"]}},86716:e=>{e.exports={en:["Stockholm"]}},201145:e=>{e.exports={en:["Submicro"]}},863375:e=>{e.exports={en:["Submillennium"]}},230585:e=>{e.exports={en:["Subminuette"]}},467948:e=>{e.exports={en:["Supercycle"]}},203348:e=>{e.exports={en:["Supermillennium"]}},518905:e=>{e.exports={en:["Switch to {resolution}"]}},531622:e=>{e.exports={en:["Sydney"]}},270963:e=>{e.exports={en:["Symbol Error"]}},832390:e=>{e.exports={en:["Symbol name label"]}},410127:e=>{e.exports={en:["Symbol last price label"]}},522071:e=>{e.exports={en:["Symbol previous day close price label"]}},639079:e=>{e.exports={en:["Sync globally"]}},846607:e=>{e.exports={en:["Sync in layout"]}},560963:e=>{e.exports={en:["PnF"]}},276519:e=>{e.exports={en:["Point & figure"]}},239949:e=>{e.exports={en:["Polyline"]}},800371:e=>{e.exports={en:["Path"]}},559256:e=>{e.exports={en:["Parallel Channel"]}},161879:e=>{e.exports={en:["Paris"]}},735140:e=>{e.exports={en:["Paste"]}},806919:e=>{e.exports={en:["Percent"]}},724436:e=>{e.exports={en:["Perth"]}},114055:e=>{e.exports={en:["Phoenix"]}},234156:e=>{e.exports={en:["Pitchfan"]}},919634:e=>{e.exports={en:["Pitchfork"]}},986631:e=>{e.exports={en:["Pin"]}},233110:e=>{e.exports={en:["Pin to new left scale"]}},928280:e=>{e.exports={en:["Pin to new right scale"]}},14115:e=>{e.exports={en:["Pin to left scale"]}},172046:e=>{e.exports={en:["Pin to left scale (hidden)"]}},81054:e=>{e.exports={en:["Pin to right scale"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"]}},960035:e=>{e.exports={en:["Pin to scale (now left)"]}},694210:e=>{e.exports={en:["Pin to scale (now no scale)"]}},210761:e=>{e.exports={en:["Pin to scale (now right)"]}},676150:e=>{e.exports={en:["Pin to scale (now {label})"]}},429436:e=>{e.exports={en:["Pin to scale {label}"]}},702165:e=>{e.exports={en:["Pin to scale {label} (hidden)"]}},590095:e=>{e.exports={en:["Pinned to left scale"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"]}},544579:e=>{e.exports={en:["Pinned to right scale"]}},794559:e=>{e.exports={en:["Pinned to right scale (hidden)"]}},312645:e=>{e.exports={en:["Pinned to scale {label}"]}},703564:e=>{e.exports={en:["Pinned to scale {label} (hidden)"]}},371566:e=>{e.exports={en:["Plus button"]}},328298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"]}},81248:e=>{e.exports={en:["Prague"]}},578793:e=>{e.exports={en:["Pre/post market price label"]}},597915:e=>{e.exports={en:["Pre/post market price line"]}},681712:e=>{e.exports={en:[""]}},879366:e=>{e.exports={en:["Previous day close price line"]}},91282:e=>{e.exports={en:["Price Label"]}},297512:e=>{e.exports={en:["Price Note"]}},768941:e=>{e.exports={en:["Price Range"]}},366123:e=>{e.exports={en:["Price format is invalid."]}},72926:e=>{e.exports={en:["Price line"]}},259189:e=>{e.exports={en:["Primary"]}},375747:e=>{e.exports={en:["Projection"]}},955801:e=>{
e.exports={en:["Published on {customer}, {date}"]}},914568:e=>{e.exports={en:["Q1"]}},913534:e=>{e.exports={en:["Q2"]}},914530:e=>{e.exports={en:["Q3"]}},903762:e=>{e.exports={en:["Q4"]}},328756:e=>{e.exports={en:["Qatar"]}},57959:e=>{e.exports={en:["Quick search. Press {shortcut}"]}},56820:e=>{e.exports={en:["Rotated Rectangle"]}},152961:e=>{e.exports={en:["Rome"]}},550318:e=>{e.exports={en:["Ray"]}},855169:e=>{e.exports={en:["Range"]}},813386:e=>{e.exports={en:["Reykjavik"]}},426001:e=>{e.exports={en:["Rectangle"]}},148236:e=>{e.exports={en:["Redo"]}},802460:e=>{e.exports={en:["Regression Trend"]}},767410:e=>{e.exports={en:["Remove"]}},903061:e=>{e.exports={en:["Remove this financial metric from favorites"]}},858764:e=>{e.exports={en:["Remove this indicator from favorites"]}},886285:e=>{e.exports={en:["Remove {drawings}"]}},587796:e=>{e.exports={en:["Remove {drawings} & {indicators}"]}},287797:e=>{e.exports={en:["Remove {indicators}"]}},422584:e=>{e.exports={en:["Rename Chart Layout"]}},188130:e=>{e.exports={en:["Renko"]}},575246:e=>{e.exports={en:["Reset chart view"]}},288853:e=>{e.exports={en:["Reset points"]}},115332:e=>{e.exports={en:["Reset price scale"]}},354170:e=>{e.exports={en:["Reset time scale"]}},837974:e=>{e.exports={en:["Riyadh"]}},594022:e=>{e.exports={en:["Riga"]}},660630:e=>{e.exports={en:["Runtime error"]}},512504:e=>{e.exports={en:["Watchlist"]}},966719:e=>{e.exports={en:["Warning"]}},505959:e=>{e.exports={en:["Warsaw"]}},604545:e=>{e.exports={en:["TPO"]}},841446:e=>{e.exports={en:["To calculate the VWAP indicator, more data is needed. Zoom out or scroll left to load more historical data."]}},298549:e=>{e.exports={en:["Tokelau"]}},769122:e=>{e.exports={en:["Tokyo"]}},110095:e=>{e.exports={en:["Toronto"]}},17981:e=>{e.exports={en:["Table"]}},511034:e=>{e.exports={en:["Taipei"]}},779995:e=>{e.exports={en:["Tallinn"]}},806686:e=>{e.exports={en:["Tehran"]}},93553:e=>{e.exports={en:["Template"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."]}},249947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."]}},499274:e=>{e.exports={en:["The request took too long to process. Ensure you have a stable internet connection. If the issue persists, try decreasing the length of the requested time interval."]}},843716:e=>{e.exports={en:["There's no data for your selected period and chart timeframe."]}},793738:e=>{e.exports={en:["This file is too big. Max size is {value}."]}},359519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."]}},418260:e=>{e.exports={en:["This script contains an error. Please contact its author."]}},276989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."]}},747773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."]}},46982:e=>{e.exports={en:["Three Drives Pattern"]}},780254:e=>{e.exports={en:["Tick-based intervals are not available for {ticker}."]}},312806:e=>{e.exports={
en:["Time"]}},320909:e=>{e.exports={en:["Time zone"]}},746852:e=>{e.exports={en:["Time Cycles"]}},617809:e=>{e.exports={en:["Time Price Opportunity"]}},366823:e=>{e.exports={en:["Trade"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"]}},35757:e=>{e.exports={en:["Trend Angle"]}},197339:e=>{e.exports={en:["Trend Line"]}},680583:e=>{e.exports={en:["Trend-Based Fib Extension"]}},272159:e=>{e.exports={en:["Trend-Based Fib Time"]}},901671:e=>{e.exports={en:["Triangle"]}},676152:e=>{e.exports={en:["Triangle Down"]}},390148:e=>{e.exports={en:["Triangle Pattern"]}},21236:e=>{e.exports={en:["Triangle Up"]}},21007:e=>{e.exports={en:["Tunis"]}},901833:e=>{e.exports={en:["UTC"]}},314804:e=>{e.exports={en:["Undo"]}},356815:e=>{e.exports={en:["Unexpected error in Deep Backtesting mode. Contact support for more information."]}},715432:e=>{e.exports={en:["Units"]}},411768:e=>{e.exports={en:["Unknown error"]}},999894:e=>{e.exports={en:["Unlock"]}},975546:e=>{e.exports={en:["Unsupported interval"]}},708580:e=>{e.exports={en:["User-defined error"]}},781030:e=>{e.exports={en:["VWAP is waiting for more data"]}},840693:e=>{e.exports={en:["Volume Profile Fixed Range"]}},139903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."]}},193722:e=>{e.exports={en:["Volume candles"]}},469156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."]}},292763:e=>{e.exports={en:["Volume footprint"]}},632838:e=>{e.exports={en:["Vancouver"]}},529535:e=>{e.exports={en:["Vertical Line"]}},23160:e=>{e.exports={en:["Vienna"]}},960534:e=>{e.exports={en:["Vilnius"]}},640091:e=>{e.exports={en:["Visibility"]}},754853:e=>{e.exports={en:["Visibility on intervals"]}},458302:e=>{e.exports={en:["Visible on tap"]}},10309:e=>{e.exports={en:["Visible on mouse over"]}},804077:e=>{e.exports={en:["Visual order"]}},211316:e=>{e.exports={en:["X Cross"]}},942231:e=>{e.exports={en:["XABCD Pattern"]}},525059:e=>{e.exports={en:["You cannot see this pivot timeframe on this resolution"]}},941019:e=>{e.exports={en:["You have locked drawings on this symbol. Do you want to remove the locked drawings too?"]}},32776:e=>{e.exports={en:["Yahoo! Finance"]}},553168:e=>{e.exports={en:["Yangon"]}},93123:e=>{e.exports={en:["Yes, remove them"]}},362859:e=>{e.exports={en:["Zurich"]}},547977:e=>{e.exports={en:["change Elliott degree"]}},561557:e=>{e.exports={en:["change no overlapping labels"]}},876852:e=>{e.exports={en:["change average close price label visibility"]}},1022:e=>{e.exports={en:["change average close price line visibility"]}},669362:e=>{e.exports={en:["change bid and ask labels visibility"]}},952919:e=>{e.exports={en:["change bid and ask lines visibility"]}},832302:e=>{e.exports={en:["change currency"]}},768846:e=>{e.exports={en:["change chart layout to {title}"]}},818867:e=>{e.exports={en:["change continuous contract switch visibility"]}},839383:e=>{e.exports={
en:["change countdown to bar close visibility"]}},316979:e=>{e.exports={en:["change date range"]}},753929:e=>{e.exports={en:["change dividends visibility"]}},906119:e=>{e.exports={en:["change events visibility on chart"]}},706819:e=>{e.exports={en:["change earnings visibility"]}},585532:e=>{e.exports={en:["change futures contract expiration visibility"]}},524226:e=>{e.exports={en:["change high and low price labels visibility"]}},180692:e=>{e.exports={en:["change high and low price lines visibility"]}},824893:e=>{e.exports={en:["change indicators name labels visibility"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"]}},350243:e=>{e.exports={en:["change latest news and Minds visibility"]}},688849:e=>{e.exports={en:["change linking group"]}},314691:e=>{e.exports={en:["change pane height"]}},796379:e=>{e.exports={en:["change plus button visibility"]}},776660:e=>{e.exports={en:["change point"]}},530870:e=>{e.exports={en:["change pre/post market price label visibility"]}},511718:e=>{e.exports={en:["change pre/post market price line visibility"]}},958419:e=>{e.exports={en:["change previous close price line visibility"]}},108662:e=>{e.exports={en:["change price line visibility"]}},702509:e=>{e.exports={en:["change price to bar ratio"]}},932829:e=>{e.exports={en:["change resolution"]}},435400:e=>{e.exports={en:["change symbol"]}},147074:e=>{e.exports={en:["change symbol labels visibility"]}},467453:e=>{e.exports={en:["change symbol last value visibility"]}},904729:e=>{e.exports={en:["change symbol previous close value visibility"]}},887041:e=>{e.exports={en:["change session"]}},338413:e=>{e.exports={en:["change session breaks visibility"]}},549965:e=>{e.exports={en:["change series style"]}},347474:e=>{e.exports={en:["change splits visibility"]}},920137:e=>{e.exports={en:["change timezone"]}},185975:e=>{e.exports={en:["change unit"]}},201924:e=>{e.exports={en:["change visibility"]}},584331:e=>{e.exports={en:["change visibility at current interval"]}},545800:e=>{e.exports={en:["change visibility at current interval and above"]}},175645:e=>{e.exports={en:["change visibility at current interval and below"]}},557916:e=>{e.exports={en:["change visibility at all intervals"]}},294566:e=>{e.exports={en:["charts by TradingView"]}},132943:e=>{e.exports={en:["clone line tools"]}},746219:e=>{e.exports={en:["create line tools group"]}},695394:e=>{e.exports={en:["create line tools group from selection"]}},12898:e=>{e.exports={en:["create {tool}"]}},194227:e=>{e.exports={en:["cut sources"]}},411500:e=>{e.exports={en:["cut {title}"]}},663869:e=>{e.exports={en:["anchor objects"]}},612570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"]}},267608:e=>{e.exports={en:["add this financial metric to entire layout"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"]}},358156:e=>{e.exports={en:["add this strategy to entire layout"]}},679290:e=>{e.exports={en:["add this symbol to entire layout"]}},4128:e=>{e.exports={
en:["align to 45 degrees"]}},368231:e=>{e.exports={en:["apply chart theme"]}},299551:e=>{e.exports={en:["apply all chart properties"]}},89720:e=>{e.exports={en:["apply drawing template"]}},727851:e=>{e.exports={en:["apply factory defaults to selected sources"]}},870507:e=>{e.exports={en:["apply indicators to entire layout"]}},69604:e=>{e.exports={en:["apply study template {template}"]}},686708:e=>{e.exports={en:["apply toolbars theme"]}},601979:e=>{e.exports={en:["bring group {title} forward"]}},453159:e=>{e.exports={en:["bring {title} to front"]}},941966:e=>{e.exports={en:["bring {title} forward"]}},244676:e=>{e.exports={en:["by TradingView"]}},658850:e=>{e.exports={en:["date range lock"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"]}},713017:e=>{e.exports={en:["hide {title}"]}},62249:e=>{e.exports={en:["hide marks on bars"]}},456558:e=>{e.exports={en:["interval lock"]}},6830:e=>{e.exports={en:["invert scale"]}},848818:e=>{e.exports={en:["insert {title}"]}},956307:e=>{e.exports={en:["insert {title} after {targetTitle}"]}},432960:e=>{e.exports={en:["insert {title} after {target}"]}},557106:e=>{e.exports={en:["insert {title} before {target}"]}},946229:e=>{e.exports={en:["insert {title} before {targetTitle}"]}},743364:e=>{e.exports={en:["load default drawing template"]}},662011:e=>{e.exports={en:["loading..."]}},576104:e=>{e.exports={en:["lock {title}"]}},720453:e=>{e.exports={en:["lock group {group}"]}},18942:e=>{e.exports={en:["lock objects"]}},398277:e=>{e.exports={en:["move"]}},158228:e=>{e.exports={en:["move {title} to new left scale"]}},177482:e=>{e.exports={en:["move {title} to new right scale"]}},864077:e=>{e.exports={en:["move all scales to left"]}},519013:e=>{e.exports={en:["move all scales to right"]}},752510:e=>{e.exports={en:["move drawing(s)"]}},179209:e=>{e.exports={en:["move left"]}},360114:e=>{e.exports={en:["move right"]}},444854:e=>{e.exports={en:["move scale"]}},910625:e=>{e.exports={en:["make {title} no scale (Full screen)"]}},576709:e=>{e.exports={en:["make group {group} invisible"]}},645987:e=>{e.exports={en:["make group {group} visible"]}},778055:e=>{e.exports={en:["merge down"]}},341866:e=>{e.exports={en:["merge to pane"]}},452458:e=>{e.exports={en:["merge up"]}},690091:e=>{e.exports={en:["n/a"]}},394981:e=>{e.exports={en:["scale price"]}},763796:e=>{e.exports={en:["scale price chart only"]}},470771:e=>{e.exports={en:["scale time"]}},42070:e=>{e.exports={en:["scroll"]}},787840:e=>{e.exports={en:["scroll time"]}},782241:e=>{e.exports={en:["set price scale selection strategy to {title}"]}},540962:e=>{e.exports={en:["send {title} backward"]}},105005:e=>{e.exports={en:["send {title} to back"]}},969546:e=>{e.exports={en:["send group {title} backward"]}},263934:e=>{e.exports={en:["share line tools globally"]}},90221:e=>{e.exports={en:["share line tools in layout"]}},13336:e=>{e.exports={en:["show all ideas"]}},791395:e=>{e.exports={en:["show ideas of followed users"]}},757460:e=>{e.exports={en:["show my ideas only"]}},504114:e=>{e.exports={en:["stay in drawing mode"]}},
703350:e=>{e.exports={en:["stop syncing drawing"]}},922:e=>{e.exports={en:["stop syncing line tool(s)"]}},919209:e=>{e.exports={en:["swap charts in the Layout"]}},253278:e=>{e.exports={en:["symbol lock"]}},591677:e=>{e.exports={en:["sync time"]}},772159:e=>{e.exports={en:["powered by"]}},492800:e=>{e.exports={en:["powered by TradingView"]}},862192:e=>{e.exports={en:["paste drawing"]}},901064:e=>{e.exports={en:["paste indicator"]}},657010:e=>{e.exports={en:["paste {title}"]}},778690:e=>{e.exports={en:["pin to left scale"]}},607495:e=>{e.exports={en:["pin to right scale"]}},981566:e=>{e.exports={en:["pin to scale {label}"]}},202618:e=>{e.exports={en:["rearrange panes"]}},260806:e=>{e.exports={en:["remove all indicators"]}},29096:e=>{e.exports={en:["remove all indicators and drawing tools"]}},727171:e=>{e.exports={en:["remove deselected empty line tools"]}},530538:e=>{e.exports={en:["remove drawings"]}},501193:e=>{e.exports={en:["remove drawings group"]}},838199:e=>{e.exports={en:["remove line data sources"]}},693333:e=>{e.exports={en:["remove pane"]}},94543:e=>{e.exports={en:["remove {title}"]}},241430:e=>{e.exports={en:["removing line tools group {name}"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"]}},385366:e=>{e.exports={en:["reset layout sizes"]}},703323:e=>{e.exports={en:["reset scales"]}},17336:e=>{e.exports={en:["reset time scale"]}},847418:e=>{e.exports={en:["resize layout"]}},985815:e=>{e.exports={en:["restore defaults"]}},796881:e=>{e.exports={en:["restore study defaults"]}},763095:e=>{e.exports={en:["toggle maximized pane state"]}},642240:e=>{e.exports={en:["toggle auto scale"]}},846054:e=>{e.exports={en:["toggle collapsed pane state"]}},824736:e=>{e.exports={en:["toggle indexed to 100 scale"]}},949695:e=>{e.exports={en:["toggle lock scale"]}},149403:e=>{e.exports={en:["toggle log scale"]}},398994:e=>{e.exports={en:["toggle percentage scale"]}},680688:e=>{e.exports={en:["toggle regular scale"]}},946807:e=>{e.exports={en:["track time"]}},508040:e=>{e.exports={en:["turn line tools sharing off"]}},199234:e=>{e.exports={en:["unanchor objects"]}},823230:e=>{e.exports={en:["unlock objects"]}},974590:e=>{e.exports={en:["unlock group {group}"]}},212525:e=>{e.exports={en:["unlock {title}"]}},981576:e=>{e.exports={en:["unmerge to new bottom pane"]}},379443:e=>{e.exports={en:["unmerge up"]}},246453:e=>{e.exports={en:["unmerge down"]}},294656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."]}},841643:e=>{e.exports={en:["{count} bars"]}},283470:e=>{e.exports={en:["{symbol} financials by TradingView"]}},821489:e=>{e.exports={en:["{title} for {symbol}"]}},740947:e=>{e.exports={en:["{userName} published on {customer}, {date}"]}},991084:e=>{e.exports={en:["zoom"]}},49856:e=>{e.exports={en:["zoom in"]}},673638:e=>{e.exports={en:["zoom out"]}},641807:e=>{e.exports={en:["day","days"]}},942328:e=>{e.exports={en:["hour","hours"]}},898393:e=>{e.exports={en:["month","months"]}},378318:e=>{e.exports={en:["minute","minutes"]}},33232:e=>{e.exports={
en:["second","seconds"]}},189937:e=>{e.exports={en:["range","ranges"]}},448898:e=>{e.exports={en:["week","weeks"]}},111913:e=>{e.exports={en:["tick","ticks"]}},22299:e=>{e.exports={en:["{amount} drawing","{amount} drawings"]}},68984:e=>{e.exports={en:["{amount} indicator","{amount} indicators"]}},958590:e=>{e.exports={en:["{count}m","{count}m"]}},547801:e=>{e.exports={en:["{count}d","{count}d"]}},146766:e=>{e.exports={en:["{count}y","{count}y"]}},956316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"]},e.exports["#AUDJPY-symbol-description"]={en:["Australian Dollar / Japanese Yen"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"]},e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"]},e.exports["#BELEX:BELEX15-symbol-description"]={en:["BELEX 15 Index"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"]},e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"]},e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"]},e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"]},e.exports["#BTCKRW-symbol-description"]={
en:["Bitcoin / South Korean Won"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"]},e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"]},e.exports["#CADUSD-symbol-description"]={en:["Canadian Dollar / U.S. Dollar"]},e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"]},e.exports["#CBOT:ZB1!-symbol-description"]={en:["T-Bond Futures"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"]},e.exports["#CBOT:ZQ1!-symbol-description"]={en:["30 Day Federal Funds Interest Rate Futures"]},e.exports["#CBOT:ZR1!-symbol-description"]={en:["Rice Futures"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"]},e.exports["#CBOT:ZW1!-symbol-description"]={en:["Wheat Futures"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"]},e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"]},e.exports["#CME:HE1!-symbol-description"]={en:["Lean Hogs Futures"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"]},e.exports["#COMEX:AUP1!-symbol-description"]={en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"]},e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"]},
e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"]},e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"]},e.exports["#DJ:DJA-symbol-description"]={en:["Dow Jones Composite Average Index"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"]},e.exports["#DJ:DJCIKC-symbol-description"]={en:["Dow Jones Commodity Index Coffee"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"]},e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"]},e.exports["#EUBUND-symbol-description"]={en:["Euro Bund"]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"]},
e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"]},e.exports["#EURNOK-symbol-description"]={en:["Euro / Norwegian Krone"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"]},e.exports["#EURUSD-symbol-description"]={en:["Euro / U.S. Dollar"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"]},e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"]},e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={en:["FTSE Bursa Malaysia KLCI Index"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"]},e.exports["#GBPEUR-symbol-description"]={en:["British Pound / Euro"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"]},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"]},e.exports["#GBPRUB-symbol-description"]={en:["British Pound / Russian Ruble"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"]},e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"]},e.exports["#ICEUS:DX1!-symbol-description"]={en:["U.S. Dollar Index Futures"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"]},e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"]},
e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"]},e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"]},e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"]},e.exports["#JPN225-symbol-description"]={en:["Japan 225 Index"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"]},e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"]},e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"]},e.exports["#MOEX:RUAL-symbol-description"]={en:["United Company RUSAL PLC"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"]},e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."]},e.exports["#NASDAQ:LE-symbol-description"]={en:["Lands' End Inc"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"]},e.exports["#NASDAQ:SHY-symbol-description"]={
en:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"]},e.exports["#NASDAQ:TLT-symbol-description"]={en:["Ishares 20+ Year Treasury Bond ETF"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"]},e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Japan 225 Index"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={en:["E-mini Light Crude Oil Futures"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"]},e.exports["#NYSE:BABA-symbol-description"]={en:["Alibaba Group Holdings Ltd."]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"]},e.exports["#NYSE:XMI-symbol-description"]={en:["NYSE ARCA Major Market Index"]},e.exports["#NZDJPY-symbol-description"]={en:["New Zealand Dollar / Japanese Yen"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={en:["CFDs on Natural Gas"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"]},e.exports["#OANDA:XCUUSD-symbol-description"]={en:["CFDs on Copper (US$ / lb)"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={
en:["OMX Helsinki 25 Index"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"]},e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"]},e.exports["#SOLUSD-symbol-description"]={en:["Solana / U.S. Dollar"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"]},e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"]},e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"]},e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"]},e.exports["#TVC:CN10-symbol-description"]={en:["China Government Bonds 10 YR"]},e.exports["#TVC:CN10Y-symbol-description"]={en:["China Government Bonds 10 YR Yield"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"]},
e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"]},e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"]},e.exports["#TVC:ES10Y-symbol-description"]={en:["Spain Government Bonds 10 YR Yield"]},e.exports["#TVC:EUBUND-symbol-description"]={en:["Euro Bund"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"]},e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"]},e.exports["#TVC:KR10Y-symbol-description"]={en:["Korea Government Bonds 10 YR Yield"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"]},e.exports["#TVC:NDX-symbol-description"]={en:["US 100 Index"]},e.exports["#TVC:NI225-symbol-description"]={en:["Japan 225 Index"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"]},e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"]},
e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"]},e.exports["#TVC:SX5E-symbol-description"]={en:["Euro Stoxx 50 Index"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"]},e.exports["#TVC:UKX-symbol-description"]={en:["UK 100 Index"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"]},e.exports["#TVC:US02Y-symbol-description"]={en:["US Government Bonds 2 YR Yield"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"]},e.exports["#TWSE:TAIEX-symbol-description"]={en:["Taiwan Capitalization Weighted Stock Index"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"]},e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"]},e.exports["#USDBRL-symbol-description"]={en:["U.S. Dollar / Brazilian Real"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"]},e.exports["#USDCNY-symbol-description"]={en:["U.S. Dollar / Chinese Yuan"]},e.exports["#USDDKK-symbol-description"]={
en:["U.S. Dollar / Danish Krone"]},e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"]},e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"]},e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"]},e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"]},e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"]},e.exports["#XPTUSD-symbol-description"]={en:["Platinum / U.S. Dollar"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"]},e.exports["#XRPUSD-symbol-description"]={en:["XRP / U.S. Dollar"]},e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"]}}}]);