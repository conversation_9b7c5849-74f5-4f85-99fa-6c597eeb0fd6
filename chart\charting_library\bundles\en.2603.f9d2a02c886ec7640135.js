(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2603,4109,8541],{155517:e=>{e.exports={en:["Add"]}},329619:e=>{e.exports={en:["Add custom color"]}},980936:e=>{e.exports={en:["Opacity"]}},664974:e=>{e.exports={en:["line tool(s) line style"]}},747370:e=>{e.exports={en:["Color"]}},904543:e=>{e.exports={en:["Cancel"]}},174089:e=>{e.exports={en:["Anchor drawing"]}},869207:e=>{e.exports={en:["Add to favorites"]}},199919:e=>{e.exports={en:["Align to 45 degree"]}},114663:e=>{e.exports={en:["Apply Default Drawing Template"]}},369715:e=>{e.exports={en:["Background color"]}},307977:e=>{e.exports={en:["Background color 1"]}},908111:e=>{e.exports={en:["Background color 2"]}},57285:e=>{e.exports={en:["Do you really want to delete Drawing Template '{name}' ?"]}},957157:e=>{e.exports={en:["Dot"]}},27390:e=>{e.exports={en:["Dotted line"]}},488123:e=>{e.exports={en:["Dashed line"]}},814939:e=>{e.exports={en:["Demonstration"]}},631145:e=>{e.exports={en:["Disable align to 45 degrees mode"]}},623276:e=>{e.exports={en:["Drawing Template '{name}' already exists. Do you really want to replace it?"]}},383187:e=>{e.exports={en:["Enable align to 45 degrees mode"]}},508727:e=>{e.exports={en:["Eraser"]}},992409:e=>{e.exports={en:["Font Size"]}},702573:e=>{e.exports={en:["Font size"]}},42633:e=>{e.exports={en:["Hold {hotKey_0} for temporary drawing"]}},322688:e=>{e.exports={en:["Hide Favorite Drawing Tools Toolbar"]}},779451:e=>{e.exports={en:["Lock all drawings"]}},614097:e=>{e.exports={en:["Line tool backgrounds"]}},211989:e=>{e.exports={en:["Line tool colors"]}},681956:e=>{e.exports={en:["Line tool text colors"]}},353002:e=>{e.exports={en:["Line tool width"]}},475056:e=>{e.exports={en:["Line tool widths"]}},437117:e=>{e.exports={en:["More"]}},935553:e=>{e.exports={en:["Magic"]}},981396:e=>{e.exports={en:["Magnet Mode snaps drawings placed near price bars to the closest OHLC value"]}},346193:e=>{e.exports={en:["Marker color"]}},91563:e=>{e.exports={en:["Measure"]}},859377:e=>{e.exports={en:["New drawings are replicated to all charts in the layout and shown when the same ticker is selected"]}},776047:e=>{e.exports={en:["Save Drawing Template As"]}},903154:e=>{e.exports={en:["Show Hidden Tools"]}},685786:e=>{e.exports={en:["Show Object Tree"]}},362518:e=>{e.exports={en:["Stay in Drawing Mode"]}},265086:e=>{e.exports={en:["Stop background color"]}},128736:e=>{e.exports={en:["Profit background color"]}},357118:e=>{e.exports={en:["Remove Drawings"]}},989984:e=>{e.exports={en:["Remove color"]}},685106:e=>{e.exports={en:["Remove from favorites"]}},77753:e=>{e.exports={en:["Text color"]}},59233:e=>{e.exports={en:["Template name"]}},881152:e=>{e.exports={en:["Templates"]}},655774:e=>{e.exports={en:["Zoom In"]}},537310:e=>{e.exports={en:["Zoom Out"]}},196142:e=>{e.exports={en:["change line tool(s) text color"]}},150522:e=>{e.exports={en:["change line tool(s) background color"]}},578655:e=>{e.exports={en:["change line tool(s) color"]}},237453:e=>{e.exports={en:["change line tool(s) font size"]}},413423:e=>{e.exports={
en:["change line tool(s) line style"]}},81303:e=>{e.exports={en:["change line tool(s) line width"]}},925167:e=>{e.exports={en:["change {propertyName} property"]}},292949:e=>{e.exports={en:["{hotKey_0} + Click on the chart"]}},313798:e=>{e.exports={en:["{hotKey_0} — circle"]}},623369:e=>{e.exports={en:["{hotKey_0} — drawing a straight line at angles of 45"]}},583042:e=>{e.exports={en:["{hotKey_0} — fixed increments"]}},10539:e=>{e.exports={en:["{hotKey_0} — square"]}}}]);