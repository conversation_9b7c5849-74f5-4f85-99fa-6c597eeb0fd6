(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1904,2970],{167040:e=>{e.exports={en:["C"]}},800919:e=>{e.exports={en:["D"]}},933088:e=>{e.exports={en:["D"]}},918400:e=>{e.exports={en:["E"]}},514149:e=>{e.exports={en:["F"]}},650940:e=>{e.exports={en:["R"]}},745540:e=>{e.exports={en:["R"]}},756757:e=>{e.exports={en:["S"]}},653288:e=>{e.exports={en:["animals & nature"]}},821311:e=>{e.exports={en:["activity"]}},590678:e=>{e.exports={en:["food & drink"]}},506715:e=>{e.exports={en:["flags"]}},998355:e=>{e.exports={en:["objects"]}},843438:e=>{e.exports={en:["smiles & people"]}},140457:e=>{e.exports={en:["symbols"]}},188906:e=>{e.exports={en:["recently used"]}},628562:e=>{e.exports={en:["travel & places"]}},366891:e=>{e.exports={en:["Connecting"]}},923599:e=>{e.exports={en:["Change %"]}},762888:e=>{e.exports={en:["Chg"]}},729369:e=>{e.exports={en:["Chg%"]}},798450:e=>{e.exports={en:["Clear list"]}},739283:e=>{e.exports={en:["Click"]}},800074:e=>{e.exports={en:["Click to sort by {columnLabel}"]}},222556:e=>{e.exports={en:["Create new list"]}},439281:e=>{e.exports={en:["Create alert on the list"]}},918939:e=>{e.exports={en:["Customize columns"]}},133333:e=>{e.exports={en:["Add Text Note for {symbol}"]}},445095:e=>{e.exports={en:["Add all selected to"]}},687186:e=>{e.exports={en:["Add all selected to Compare"]}},904461:e=>{e.exports={en:["Add emoji"]}},218542:e=>{e.exports={en:["Add symbol"]}},598104:e=>{e.exports={en:["Add section"]}},577766:e=>{e.exports={en:["Add {symbol} to"]}},958471:e=>{e.exports={en:["Add {symbol} to Compare"]}},935639:e=>{e.exports={en:["Add {symbol} to Watchlist"]}},624439:e=>{e.exports={en:["Do you really want to clear all symbols?"]}},930058:e=>{e.exports={en:["Do you really want to delete Watchlist '{name}' ?"]}},173837:e=>{e.exports={en:["Do you really want to unflag all symbols in the system?"]}},739688:e=>{e.exports={en:["Delayed"]}},254602:e=>{e.exports={en:["Delisted"]}},178734:e=>{e.exports={en:["Description"]}},328304:e=>{e.exports={en:["End of Day"]}},57335:e=>{e.exports={en:["Error"]}},352321:e=>{e.exports={en:["Export list"]}},372163:e=>{e.exports={en:['File contains incorrectly formatted data. Please correct the format and try again. Examples: "NYSE:GE,NYSE:F,NASDAQ:MSFT" or "F,GE,MSFT"']}},317299:e=>{e.exports={en:["File limit exceeded"]}},856135:e=>{e.exports={en:["Financials"]}},160589:e=>{e.exports={en:["Flag {symbol}"]}},318387:e=>{e.exports={en:["Flag All Selected"]}},578799:e=>{e.exports={en:["Hmmm, seems that there are no symbols in this hotlist."]}},109415:e=>{e.exports={en:["Hmmm, seems that you didn’t have any symbols in watchlist. You can add symbol above."]}},987845:e=>{e.exports={en:["Holiday"]}},795754:e=>{e.exports={en:["Import list"]}},108492:e=>{e.exports={en:["Invalid data format"]}},909161:e=>{e.exports={en:["Instrument is not allowed"]}},991153:e=>{e.exports={en:["Open {symbol} Chart"]}},545362:e=>{e.exports={en:["Open list"]}},273645:e=>{e.exports={en:["Open symbol on chart"]}},786726:e=>{e.exports={en:["Loading"]}},728257:e=>{e.exports={
en:["Long"]}},440846:e=>{e.exports={en:["Last"]}},340115:e=>{e.exports={en:["Make a copy"]}},241410:e=>{e.exports={en:["Market open"]}},762464:e=>{e.exports={en:["Market closed"]}},988408:e=>{e.exports={en:["Snapshot"]}},977415:e=>{e.exports={en:["Save List As"]}},508573:e=>{e.exports={en:["Search"]}},322796:e=>{e.exports={en:["See {symbol} Overview"]}},984239:e=>{e.exports={en:["Share list"]}},13009:e=>{e.exports={en:["Short"]}},595481:e=>{e.exports={en:["Symbol"]}},563322:e=>{e.exports={en:["Symbol Details"]}},84151:e=>{e.exports={en:["Symbol group"]}},46187:e=>{e.exports={en:["Symbol list name"]}},673897:e=>{e.exports={en:["Post-market"]}},178104:e=>{e.exports={en:["Post market"]}},545265:e=>{e.exports={en:["Pre market"]}},236018:e=>{e.exports={en:["Pre-market"]}},167476:e=>{e.exports={en:["Quotes are delayed by {number} min"]}},369539:e=>{e.exports={en:["Quotes are delayed by {number} min and updated every 30 seconds"]}},667239:e=>{e.exports={en:["Return to custom watchlist order"]}},303058:e=>{e.exports={en:["Real-time"]}},907448:e=>{e.exports={en:["Recently used"]}},595888:e=>{e.exports={en:["Remove from Watchlist"]}},63706:e=>{e.exports={en:["Remove section"]}},647550:e=>{e.exports={en:["Remove selected emoji"]}},606321:e=>{e.exports={en:["Rename"]}},38822:e=>{e.exports={en:["Replay Mode"]}},931425:e=>{e.exports={en:["Watchlist cannot take anymore, Cap'n"]}},191056:e=>{e.exports={en:["Watchlist error"]}},918580:e=>{e.exports={en:["Whoa there, you can't go adding over {number} symbols and separators to a watchlist – we're engineers not miracle workers."]}},770542:e=>{e.exports={en:["Table view"]}},316750:e=>{e.exports={en:["This file is too big. Max size is {fileLimit} KB"]}},632512:e=>{e.exports={en:["Unflag All Selected"]}},429678:e=>{e.exports={en:["Unflag All Symbols in the System"]}},967047:e=>{e.exports={en:["Unflag {symbol}"]}},937644:e=>{e.exports={en:["Volume"]}},599137:e=>{e.exports={en:["or"]}},788402:e=>{e.exports={en:["to add symbol and close dialog"]}},54105:e=>{e.exports={en:["Do you really want to delete {count} selected symbol?","Do you really want to delete {count} selected symbols?"]}},464788:e=>{e.exports={en:["Do you really want to unflag {count} selected symbol?","Do you really want to unflag {count} selected symbol?"]}},170352:e=>{e.exports={en:["{count} symbol","{count} symbols"]}}}]);