simple_open_interest.js:225 Simple OI indicators loaded: 3
simple_candlestick_patterns.js:269 ðŸ•¯ï¸ Candlestick Pattern Detector loaded successfully!
simple_candlestick_patterns.js:270 ðŸ’¡ Usage:
simple_candlestick_patterns.js:271   analyzeCandlestickPatterns(ohlcData, sensitivity) - Analyze OHLC data
simple_candlestick_patterns.js:272   testCandlestickPatterns() - Test with sample data
simple_candlestick_patterns.js:273   candlestickDetector.setSensitivity(0.5) - Adjust sensitivity
index5.html:549  GET http://localhost/suilu/top/chart/data/logos.json 404 (Not Found)
loadAvailableLogos @ index5.html:549
SampleDatafeed @ index5.html:538
initOnReady @ index5.html:1777
content.js:40 Feature is disabled
library.529d5b8c7f545237e54b.js:16595 4 custom indicators loaded.
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvY8y net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
library.529d5b8c7f545237e54b.js:16620 2025-07-30T01:15:36.378Z:Property:The state with a data type: unknown does not match a schema
w @ library.529d5b8c7f545237e54b.js:16620
(anonymous) @ library.529d5b8c7f545237e54b.js:16620
m @ library.529d5b8c7f545237e54b.js:16107
addProperty @ library.529d5b8c7f545237e54b.js:16107
merge @ library.529d5b8c7f545237e54b.js:16107
Jg @ library.529d5b8c7f545237e54b.js:16511
_createChartWidget @ library.529d5b8c7f545237e54b.js:16556
_addNewChartWidget @ library.529d5b8c7f545237e54b.js:16556
setLayout @ library.529d5b8c7f545237e54b.js:16548
loadContent @ library.529d5b8c7f545237e54b.js:16550
ty @ library.529d5b8c7f545237e54b.js:16546
TS @ library.529d5b8c7f545237e54b.js:16598
setTimeout
(anonymous) @ library.529d5b8c7f545237e54b.js:16603
Promise.then
356186 @ library.529d5b8c7f545237e54b.js:16603
o @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
o.O @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
a @ runtime.ea0bf94eec0f51895e4d.js:11
(anonymous) @ library.529d5b8c7f545237e54b.js:1
library.529d5b8c7f545237e54b.js:16620 2025-07-30T01:15:36.535Z:Chart.Model.StudyPropertiesOverrider:There is no such study Open Interest
w @ library.529d5b8c7f545237e54b.js:16620
(anonymous) @ library.529d5b8c7f545237e54b.js:16620
u @ library.529d5b8c7f545237e54b.js:16216
overrideDefaults @ library.529d5b8c7f545237e54b.js:16270
_processLibraryMetaInfo @ library.529d5b8c7f545237e54b.js:16207
requestMetaInfo @ library.529d5b8c7f545237e54b.js:16207
await in requestMetaInfo
_requestMetadata @ library.529d5b8c7f545237e54b.js:16530
_requestMetadataAndProcessModel @ library.529d5b8c7f545237e54b.js:16530
_onConnection @ library.529d5b8c7f545237e54b.js:16530
Jg._onChartSessionIsConnectedChanged @ library.529d5b8c7f545237e54b.js:16510
setValue @ library.529d5b8c7f545237e54b.js:15941
_onChartApiConnected @ library.529d5b8c7f545237e54b.js:16498
onMessage @ library.529d5b8c7f545237e54b.js:16497
JSServer.ChartApi._notifySessions @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi.connect @ library.529d5b8c7f545237e54b.js:41
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._fireEvent @ library.529d5b8c7f545237e54b.js:40
JSServer.ChartApi.start @ library.529d5b8c7f545237e54b.js:40
(anonymous) @ library.529d5b8c7f545237e54b.js:16595
Promise.then
356186 @ library.529d5b8c7f545237e54b.js:16595
o @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
o.O @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
a @ runtime.ea0bf94eec0f51895e4d.js:11
(anonymous) @ library.529d5b8c7f545237e54b.js:1
library.529d5b8c7f545237e54b.js:16620 2025-07-30T01:15:36.536Z:Chart.Model.StudyPropertiesOverrider:There is no such study OI Volume Ratio
w @ library.529d5b8c7f545237e54b.js:16620
(anonymous) @ library.529d5b8c7f545237e54b.js:16620
u @ library.529d5b8c7f545237e54b.js:16216
overrideDefaults @ library.529d5b8c7f545237e54b.js:16270
_processLibraryMetaInfo @ library.529d5b8c7f545237e54b.js:16207
requestMetaInfo @ library.529d5b8c7f545237e54b.js:16207
await in requestMetaInfo
_requestMetadata @ library.529d5b8c7f545237e54b.js:16530
_requestMetadataAndProcessModel @ library.529d5b8c7f545237e54b.js:16530
_onConnection @ library.529d5b8c7f545237e54b.js:16530
Jg._onChartSessionIsConnectedChanged @ library.529d5b8c7f545237e54b.js:16510
setValue @ library.529d5b8c7f545237e54b.js:15941
_onChartApiConnected @ library.529d5b8c7f545237e54b.js:16498
onMessage @ library.529d5b8c7f545237e54b.js:16497
JSServer.ChartApi._notifySessions @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi.connect @ library.529d5b8c7f545237e54b.js:41
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._fireEvent @ library.529d5b8c7f545237e54b.js:40
JSServer.ChartApi.start @ library.529d5b8c7f545237e54b.js:40
(anonymous) @ library.529d5b8c7f545237e54b.js:16595
Promise.then
356186 @ library.529d5b8c7f545237e54b.js:16595
o @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
o.O @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
a @ runtime.ea0bf94eec0f51895e4d.js:11
(anonymous) @ library.529d5b8c7f545237e54b.js:1
library.529d5b8c7f545237e54b.js:16620 2025-07-30T01:15:36.538Z:Chart.Model.StudyPropertiesOverrider:Study 'Moving Average' has no plot or input 'plot_0'
w @ library.529d5b8c7f545237e54b.js:16620
(anonymous) @ library.529d5b8c7f545237e54b.js:16620
h @ library.529d5b8c7f545237e54b.js:16216
u @ library.529d5b8c7f545237e54b.js:16217
overrideDefaults @ library.529d5b8c7f545237e54b.js:16270
_processLibraryMetaInfo @ library.529d5b8c7f545237e54b.js:16207
requestMetaInfo @ library.529d5b8c7f545237e54b.js:16207
await in requestMetaInfo
_requestMetadata @ library.529d5b8c7f545237e54b.js:16530
_requestMetadataAndProcessModel @ library.529d5b8c7f545237e54b.js:16530
_onConnection @ library.529d5b8c7f545237e54b.js:16530
Jg._onChartSessionIsConnectedChanged @ library.529d5b8c7f545237e54b.js:16510
setValue @ library.529d5b8c7f545237e54b.js:15941
_onChartApiConnected @ library.529d5b8c7f545237e54b.js:16498
onMessage @ library.529d5b8c7f545237e54b.js:16497
JSServer.ChartApi._notifySessions @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi.connect @ library.529d5b8c7f545237e54b.js:41
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._fireEvent @ library.529d5b8c7f545237e54b.js:40
JSServer.ChartApi.start @ library.529d5b8c7f545237e54b.js:40
(anonymous) @ library.529d5b8c7f545237e54b.js:16595
Promise.then
356186 @ library.529d5b8c7f545237e54b.js:16595
o @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
o.O @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
a @ runtime.ea0bf94eec0f51895e4d.js:11
(anonymous) @ library.529d5b8c7f545237e54b.js:1
library.529d5b8c7f545237e54b.js:15870 2025-07-30T01:15:36.920Z SymbolInfo validation for NIFTY: name must be non-empty string
et @ library.529d5b8c7f545237e54b.js:15870
i @ library.529d5b8c7f545237e54b.js:15866
(anonymous) @ library.529d5b8c7f545237e54b.js:15867
(anonymous) @ library.529d5b8c7f545237e54b.js:15867
(anonymous) @ index5.html:1497
setTimeout
resolveSymbol @ index5.html:1497
(anonymous) @ library.529d5b8c7f545237e54b.js:15866
_resolveSymbolInternal @ library.529d5b8c7f545237e54b.js:15866
_resolveSymbolByName @ library.529d5b8c7f545237e54b.js:15866
(anonymous) @ library.529d5b8c7f545237e54b.js:15863
o @ library.529d5b8c7f545237e54b.js:15863
a @ library.529d5b8c7f545237e54b.js:15863
_startQuotesSubscription @ library.529d5b8c7f545237e54b.js:15863
_restartQuotesSubscription @ library.529d5b8c7f545237e54b.js:15862
quoteAddSymbols @ library.529d5b8c7f545237e54b.js:15862
JSServer.ChartApi.quoteAddSymbols @ library.529d5b8c7f545237e54b.js:44
quoteAddSymbols @ library.529d5b8c7f545237e54b.js:15874
m.subscribe @ library.529d5b8c7f545237e54b.js:15876
_subscribeQuoteSession @ library.529d5b8c7f545237e54b.js:16108
setQuotesSessionSymbol @ library.529d5b8c7f545237e54b.js:16108
_updateSymbolInfo @ library.529d5b8c7f545237e54b.js:16157
restart @ library.529d5b8c7f545237e54b.js:16147
restart @ library.529d5b8c7f545237e54b.js:16452
(anonymous) @ library.529d5b8c7f545237e54b.js:16526
_init @ library.529d5b8c7f545237e54b.js:16527
_requestMetadataAndProcessModel @ library.529d5b8c7f545237e54b.js:16530
await in _requestMetadataAndProcessModel
_onConnection @ library.529d5b8c7f545237e54b.js:16530
Jg._onChartSessionIsConnectedChanged @ library.529d5b8c7f545237e54b.js:16510
setValue @ library.529d5b8c7f545237e54b.js:15941
_onChartApiConnected @ library.529d5b8c7f545237e54b.js:16498
onMessage @ library.529d5b8c7f545237e54b.js:16497
JSServer.ChartApi._notifySessions @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi.connect @ library.529d5b8c7f545237e54b.js:41
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._fireEvent @ library.529d5b8c7f545237e54b.js:40
JSServer.ChartApi.start @ library.529d5b8c7f545237e54b.js:40
(anonymous) @ library.529d5b8c7f545237e54b.js:16595
Promise.then
356186 @ library.529d5b8c7f545237e54b.js:16595
o @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
o.O @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
a @ runtime.ea0bf94eec0f51895e4d.js:11
(anonymous) @ library.529d5b8c7f545237e54b.js:1
library.529d5b8c7f545237e54b.js:15870 2025-07-30T01:15:36.921Z SymbolInfo validation for NIFTY: field has_no_volume is deprecated, use visible_plots_set instead
et @ library.529d5b8c7f545237e54b.js:15870
i @ library.529d5b8c7f545237e54b.js:15866
(anonymous) @ library.529d5b8c7f545237e54b.js:15867
(anonymous) @ library.529d5b8c7f545237e54b.js:15867
(anonymous) @ index5.html:1497
setTimeout
resolveSymbol @ index5.html:1497
(anonymous) @ library.529d5b8c7f545237e54b.js:15866
_resolveSymbolInternal @ library.529d5b8c7f545237e54b.js:15866
_resolveSymbolByName @ library.529d5b8c7f545237e54b.js:15866
(anonymous) @ library.529d5b8c7f545237e54b.js:15863
o @ library.529d5b8c7f545237e54b.js:15863
a @ library.529d5b8c7f545237e54b.js:15863
_startQuotesSubscription @ library.529d5b8c7f545237e54b.js:15863
_restartQuotesSubscription @ library.529d5b8c7f545237e54b.js:15862
quoteAddSymbols @ library.529d5b8c7f545237e54b.js:15862
JSServer.ChartApi.quoteAddSymbols @ library.529d5b8c7f545237e54b.js:44
quoteAddSymbols @ library.529d5b8c7f545237e54b.js:15874
m.subscribe @ library.529d5b8c7f545237e54b.js:15876
_subscribeQuoteSession @ library.529d5b8c7f545237e54b.js:16108
setQuotesSessionSymbol @ library.529d5b8c7f545237e54b.js:16108
_updateSymbolInfo @ library.529d5b8c7f545237e54b.js:16157
restart @ library.529d5b8c7f545237e54b.js:16147
restart @ library.529d5b8c7f545237e54b.js:16452
(anonymous) @ library.529d5b8c7f545237e54b.js:16526
_init @ library.529d5b8c7f545237e54b.js:16527
_requestMetadataAndProcessModel @ library.529d5b8c7f545237e54b.js:16530
await in _requestMetadataAndProcessModel
_onConnection @ library.529d5b8c7f545237e54b.js:16530
Jg._onChartSessionIsConnectedChanged @ library.529d5b8c7f545237e54b.js:16510
setValue @ library.529d5b8c7f545237e54b.js:15941
_onChartApiConnected @ library.529d5b8c7f545237e54b.js:16498
onMessage @ library.529d5b8c7f545237e54b.js:16497
JSServer.ChartApi._notifySessions @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi.connect @ library.529d5b8c7f545237e54b.js:41
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._fireEvent @ library.529d5b8c7f545237e54b.js:40
JSServer.ChartApi.start @ library.529d5b8c7f545237e54b.js:40
(anonymous) @ library.529d5b8c7f545237e54b.js:16595
Promise.then
356186 @ library.529d5b8c7f545237e54b.js:16595
o @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
o.O @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
a @ runtime.ea0bf94eec0f51895e4d.js:11
(anonymous) @ library.529d5b8c7f545237e54b.js:1
charting_library/data/svg/nse_nifty.svg:1  GET http://localhost/suilu/top/chart/charting_library/data/svg/nse_nifty.svg 404 (Not Found)
Image
(anonymous) @ library.529d5b8c7f545237e54b.js:15884
n @ library.529d5b8c7f545237e54b.js:15884
(anonymous) @ chart-widget-gui.373398f680e71823f0f1.js:16
R._updateSymbolLogo @ chart-widget-gui.373398f680e71823f0f1.js:16
subscribe @ library.529d5b8c7f545237e54b.js:15941
R @ chart-widget-gui.373398f680e71823f0f1.js:16
Z @ chart-widget-gui.373398f680e71823f0f1.js:18
_addMainDataSource @ chart-widget-gui.373398f680e71823f0f1.js:26
_onLayoutChanged @ chart-widget-gui.373398f680e71823f0f1.js:25
_fireImpl @ library.529d5b8c7f545237e54b.js:15883
updateLayout @ chart-widget-gui.373398f680e71823f0f1.js:55
(anonymous) @ library.529d5b8c7f545237e54b.js:16352
Promise.then
_loadAndCreateLegendWidget @ library.529d5b8c7f545237e54b.js:16352
Zo @ library.529d5b8c7f545237e54b.js:16329
_makePaneWidgetsAndSeparators @ library.529d5b8c7f545237e54b.js:16523
_updateGui @ library.529d5b8c7f545237e54b.js:16525
(anonymous) @ library.529d5b8c7f545237e54b.js:16527
_makeDefaultModel @ library.529d5b8c7f545237e54b.js:16528
_init @ library.529d5b8c7f545237e54b.js:16526
_requestMetadataAndProcessModel @ library.529d5b8c7f545237e54b.js:16530
await in _requestMetadataAndProcessModel
_onConnection @ library.529d5b8c7f545237e54b.js:16530
Jg._onChartSessionIsConnectedChanged @ library.529d5b8c7f545237e54b.js:16510
setValue @ library.529d5b8c7f545237e54b.js:15941
_onChartApiConnected @ library.529d5b8c7f545237e54b.js:16498
onMessage @ library.529d5b8c7f545237e54b.js:16497
JSServer.ChartApi._notifySessions @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi.connect @ library.529d5b8c7f545237e54b.js:41
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._fireEvent @ library.529d5b8c7f545237e54b.js:40
JSServer.ChartApi.start @ library.529d5b8c7f545237e54b.js:40
(anonymous) @ library.529d5b8c7f545237e54b.js:16595
Promise.then
356186 @ library.529d5b8c7f545237e54b.js:16595
o @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
o.O @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
a @ runtime.ea0bf94eec0f51895e4d.js:11
(anonymous) @ library.529d5b8c7f545237e54b.js:1
index5.html:4828 ðŸ”§ Indicator Manager initialized
index5.html:5154 ðŸ”§ Indicator Manager ready!
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvZAG net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
value @ manager.js:95
o @ manager.js:127
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
library.529d5b8c7f545237e54b.js:16270 Uncaught (in promise) Error: unexpected study id:open interest
    at h.findStudyMetaInfoByDescription (library.529d5b8c7f545237e54b.js:16270:1275)
    at Uy.createStudy (library.529d5b8c7f545237e54b.js:16570:3015)
findStudyMetaInfoByDescription @ library.529d5b8c7f545237e54b.js:16270
createStudy @ library.529d5b8c7f545237e54b.js:16570
await in createStudy
(anonymous) @ index5.html:5160
setTimeout
(anonymous) @ index5.html:5158
(anonymous) @ charting_library.standalone.js:782
_fireImpl @ library.529d5b8c7f545237e54b.js:15883
u @ library.529d5b8c7f545237e54b.js:14
_ @ library.529d5b8c7f545237e54b.js:14
s @ library.529d5b8c7f545237e54b.js:16546
i @ library.529d5b8c7f545237e54b.js:16546
_fireImpl @ library.529d5b8c7f545237e54b.js:15883
fireBarReceived @ library.529d5b8c7f545237e54b.js:16134
_onDataUnpacked @ library.529d5b8c7f545237e54b.js:16137
_onDataUpdate @ library.529d5b8c7f545237e54b.js:16137
await in _onDataUpdate
_onMessageImpl @ library.529d5b8c7f545237e54b.js:16136
(anonymous) @ library.529d5b8c7f545237e54b.js:16136
Promise.then
_enqueueUpdate @ library.529d5b8c7f545237e54b.js:16137
_onMessage @ library.529d5b8c7f545237e54b.js:16136
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._invokeHandler @ library.529d5b8c7f545237e54b.js:43
JSServer.ChartApi._invokeNotificationHandler @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi._dispatchNotification @ library.529d5b8c7f545237e54b.js:43
JSServer.ChartApi.receiveLocalResponse @ library.529d5b8c7f545237e54b.js:41
r.onDataUpdate @ library.529d5b8c7f545237e54b.js:45
g @ library.529d5b8c7f545237e54b.js:15860
onDataReadyCallback @ library.529d5b8c7f545237e54b.js:15860
recalc @ library.529d5b8c7f545237e54b.js:15868
recalc @ library.529d5b8c7f545237e54b.js:15840
update @ library.529d5b8c7f545237e54b.js:15840
a @ library.529d5b8c7f545237e54b.js:15840
(anonymous) @ library.529d5b8c7f545237e54b.js:15840
fire @ library.529d5b8c7f545237e54b.js:15837
_recalc @ library.529d5b8c7f545237e54b.js:15841
recalc @ library.529d5b8c7f545237e54b.js:15840
recalc @ library.529d5b8c7f545237e54b.js:15840
update @ library.529d5b8c7f545237e54b.js:15840
a @ library.529d5b8c7f545237e54b.js:15840
(anonymous) @ library.529d5b8c7f545237e54b.js:15840
fire @ library.529d5b8c7f545237e54b.js:15837
(anonymous) @ library.529d5b8c7f545237e54b.js:15846
(anonymous) @ library.529d5b8c7f545237e54b.js:15857
_returnHistoryDataToSubscriber @ library.529d5b8c7f545237e54b.js:15834
(anonymous) @ library.529d5b8c7f545237e54b.js:15833
_processPendingSubscribers @ library.529d5b8c7f545237e54b.js:15833
_processBars @ library.529d5b8c7f545237e54b.js:15835
(anonymous) @ library.529d5b8c7f545237e54b.js:15835
(anonymous) @ index5.html:1515
Promise.then
getBars @ index5.html:1506
_ensureRequestedTo @ library.529d5b8c7f545237e54b.js:15835
_processPendingSubscribers @ library.529d5b8c7f545237e54b.js:15834
(anonymous) @ library.529d5b8c7f545237e54b.js:15832
setTimeout
addSubscription @ library.529d5b8c7f545237e54b.js:15832
subscribe @ library.529d5b8c7f545237e54b.js:15836
subscribe @ library.529d5b8c7f545237e54b.js:15857
(anonymous) @ library.529d5b8c7f545237e54b.js:15846
(anonymous) @ library.529d5b8c7f545237e54b.js:15857
Promise.then
_resolveSymbolByName @ library.529d5b8c7f545237e54b.js:15866
resolve @ library.529d5b8c7f545237e54b.js:15857
X @ library.529d5b8c7f545237e54b.js:15846
_createItem @ library.529d5b8c7f545237e54b.js:15847
subscribe @ library.529d5b8c7f545237e54b.js:15846
o @ library.529d5b8c7f545237e54b.js:15840
start @ library.529d5b8c7f545237e54b.js:15840
N @ library.529d5b8c7f545237e54b.js:15839
O @ library.529d5b8c7f545237e54b.js:15840
F @ library.529d5b8c7f545237e54b.js:15840
_createItem @ library.529d5b8c7f545237e54b.js:15847
subscribe @ library.529d5b8c7f545237e54b.js:15846
o @ library.529d5b8c7f545237e54b.js:15840
start @ library.529d5b8c7f545237e54b.js:15840
N @ library.529d5b8c7f545237e54b.js:15839
O @ library.529d5b8c7f545237e54b.js:15840
P @ library.529d5b8c7f545237e54b.js:15867
_computeStudy @ library.529d5b8c7f545237e54b.js:15868
await in _computeStudy
createSeries @ library.529d5b8c7f545237e54b.js:15860
JSServer.ChartApi.createSeries @ library.529d5b8c7f545237e54b.js:41
createSeries @ library.529d5b8c7f545237e54b.js:16498
_createSeries @ library.529d5b8c7f545237e54b.js:16135
start @ library.529d5b8c7f545237e54b.js:16135
restart @ library.529d5b8c7f545237e54b.js:16148
restart @ library.529d5b8c7f545237e54b.js:16452
(anonymous) @ library.529d5b8c7f545237e54b.js:16526
_init @ library.529d5b8c7f545237e54b.js:16527
_requestMetadataAndProcessModel @ library.529d5b8c7f545237e54b.js:16530
await in _requestMetadataAndProcessModel
_onConnection @ library.529d5b8c7f545237e54b.js:16530
Jg._onChartSessionIsConnectedChanged @ library.529d5b8c7f545237e54b.js:16510
setValue @ library.529d5b8c7f545237e54b.js:15941
_onChartApiConnected @ library.529d5b8c7f545237e54b.js:16498
onMessage @ library.529d5b8c7f545237e54b.js:16497
JSServer.ChartApi._notifySessions @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi.connect @ library.529d5b8c7f545237e54b.js:41
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._fireEvent @ library.529d5b8c7f545237e54b.js:40
JSServer.ChartApi.start @ library.529d5b8c7f545237e54b.js:40
(anonymous) @ library.529d5b8c7f545237e54b.js:16595
Promise.then
356186 @ library.529d5b8c7f545237e54b.js:16595
o @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
o.O @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
a @ runtime.ea0bf94eec0f51895e4d.js:11
(anonymous) @ library.529d5b8c7f545237e54b.js:1
library.529d5b8c7f545237e54b.js:16270 Uncaught (in promise) Error: unexpected study id:oi volume ratio
    at h.findStudyMetaInfoByDescription (library.529d5b8c7f545237e54b.js:16270:1275)
    at Uy.createStudy (library.529d5b8c7f545237e54b.js:16570:3015)
findStudyMetaInfoByDescription @ library.529d5b8c7f545237e54b.js:16270
createStudy @ library.529d5b8c7f545237e54b.js:16570
await in createStudy
(anonymous) @ index5.html:5167
setTimeout
(anonymous) @ index5.html:5158
(anonymous) @ charting_library.standalone.js:782
_fireImpl @ library.529d5b8c7f545237e54b.js:15883
u @ library.529d5b8c7f545237e54b.js:14
_ @ library.529d5b8c7f545237e54b.js:14
s @ library.529d5b8c7f545237e54b.js:16546
i @ library.529d5b8c7f545237e54b.js:16546
_fireImpl @ library.529d5b8c7f545237e54b.js:15883
fireBarReceived @ library.529d5b8c7f545237e54b.js:16134
_onDataUnpacked @ library.529d5b8c7f545237e54b.js:16137
_onDataUpdate @ library.529d5b8c7f545237e54b.js:16137
await in _onDataUpdate
_onMessageImpl @ library.529d5b8c7f545237e54b.js:16136
(anonymous) @ library.529d5b8c7f545237e54b.js:16136
Promise.then
_enqueueUpdate @ library.529d5b8c7f545237e54b.js:16137
_onMessage @ library.529d5b8c7f545237e54b.js:16136
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._invokeHandler @ library.529d5b8c7f545237e54b.js:43
JSServer.ChartApi._invokeNotificationHandler @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi._dispatchNotification @ library.529d5b8c7f545237e54b.js:43
JSServer.ChartApi.receiveLocalResponse @ library.529d5b8c7f545237e54b.js:41
r.onDataUpdate @ library.529d5b8c7f545237e54b.js:45
g @ library.529d5b8c7f545237e54b.js:15860
onDataReadyCallback @ library.529d5b8c7f545237e54b.js:15860
recalc @ library.529d5b8c7f545237e54b.js:15868
recalc @ library.529d5b8c7f545237e54b.js:15840
update @ library.529d5b8c7f545237e54b.js:15840
a @ library.529d5b8c7f545237e54b.js:15840
(anonymous) @ library.529d5b8c7f545237e54b.js:15840
fire @ library.529d5b8c7f545237e54b.js:15837
_recalc @ library.529d5b8c7f545237e54b.js:15841
recalc @ library.529d5b8c7f545237e54b.js:15840
recalc @ library.529d5b8c7f545237e54b.js:15840
update @ library.529d5b8c7f545237e54b.js:15840
a @ library.529d5b8c7f545237e54b.js:15840
(anonymous) @ library.529d5b8c7f545237e54b.js:15840
fire @ library.529d5b8c7f545237e54b.js:15837
(anonymous) @ library.529d5b8c7f545237e54b.js:15846
(anonymous) @ library.529d5b8c7f545237e54b.js:15857
_returnHistoryDataToSubscriber @ library.529d5b8c7f545237e54b.js:15834
(anonymous) @ library.529d5b8c7f545237e54b.js:15833
_processPendingSubscribers @ library.529d5b8c7f545237e54b.js:15833
_processBars @ library.529d5b8c7f545237e54b.js:15835
(anonymous) @ library.529d5b8c7f545237e54b.js:15835
(anonymous) @ index5.html:1515
Promise.then
getBars @ index5.html:1506
_ensureRequestedTo @ library.529d5b8c7f545237e54b.js:15835
_processPendingSubscribers @ library.529d5b8c7f545237e54b.js:15834
(anonymous) @ library.529d5b8c7f545237e54b.js:15832
setTimeout
addSubscription @ library.529d5b8c7f545237e54b.js:15832
subscribe @ library.529d5b8c7f545237e54b.js:15836
subscribe @ library.529d5b8c7f545237e54b.js:15857
(anonymous) @ library.529d5b8c7f545237e54b.js:15846
(anonymous) @ library.529d5b8c7f545237e54b.js:15857
Promise.then
_resolveSymbolByName @ library.529d5b8c7f545237e54b.js:15866
resolve @ library.529d5b8c7f545237e54b.js:15857
X @ library.529d5b8c7f545237e54b.js:15846
_createItem @ library.529d5b8c7f545237e54b.js:15847
subscribe @ library.529d5b8c7f545237e54b.js:15846
o @ library.529d5b8c7f545237e54b.js:15840
start @ library.529d5b8c7f545237e54b.js:15840
N @ library.529d5b8c7f545237e54b.js:15839
O @ library.529d5b8c7f545237e54b.js:15840
F @ library.529d5b8c7f545237e54b.js:15840
_createItem @ library.529d5b8c7f545237e54b.js:15847
subscribe @ library.529d5b8c7f545237e54b.js:15846
o @ library.529d5b8c7f545237e54b.js:15840
start @ library.529d5b8c7f545237e54b.js:15840
N @ library.529d5b8c7f545237e54b.js:15839
O @ library.529d5b8c7f545237e54b.js:15840
P @ library.529d5b8c7f545237e54b.js:15867
_computeStudy @ library.529d5b8c7f545237e54b.js:15868
await in _computeStudy
createSeries @ library.529d5b8c7f545237e54b.js:15860
JSServer.ChartApi.createSeries @ library.529d5b8c7f545237e54b.js:41
createSeries @ library.529d5b8c7f545237e54b.js:16498
_createSeries @ library.529d5b8c7f545237e54b.js:16135
start @ library.529d5b8c7f545237e54b.js:16135
restart @ library.529d5b8c7f545237e54b.js:16148
restart @ library.529d5b8c7f545237e54b.js:16452
(anonymous) @ library.529d5b8c7f545237e54b.js:16526
_init @ library.529d5b8c7f545237e54b.js:16527
_requestMetadataAndProcessModel @ library.529d5b8c7f545237e54b.js:16530
await in _requestMetadataAndProcessModel
_onConnection @ library.529d5b8c7f545237e54b.js:16530
Jg._onChartSessionIsConnectedChanged @ library.529d5b8c7f545237e54b.js:16510
setValue @ library.529d5b8c7f545237e54b.js:15941
_onChartApiConnected @ library.529d5b8c7f545237e54b.js:16498
onMessage @ library.529d5b8c7f545237e54b.js:16497
JSServer.ChartApi._notifySessions @ library.529d5b8c7f545237e54b.js:44
JSServer.ChartApi.connect @ library.529d5b8c7f545237e54b.js:41
(anonymous) @ library.529d5b8c7f545237e54b.js:41
JSServer.ChartApi._fireEvent @ library.529d5b8c7f545237e54b.js:40
JSServer.ChartApi.start @ library.529d5b8c7f545237e54b.js:40
(anonymous) @ library.529d5b8c7f545237e54b.js:16595
Promise.then
356186 @ library.529d5b8c7f545237e54b.js:16595
o @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
o.O @ runtime.ea0bf94eec0f51895e4d.js:1
(anonymous) @ library.529d5b8c7f545237e54b.js:16627
a @ runtime.ea0bf94eec0f51895e4d.js:11
(anonymous) @ library.529d5b8c7f545237e54b.js:1
index5.html:5412 ðŸ“Š Indicator Manager ready! Click the ðŸ“Š button or use showIndicatorManager()
index5.html:5413 ðŸ’¡ Type indicatorHelp() for available commands
index5.html:5414 ðŸŽ¯ Key Features:
index5.html:5415   â€¢ Add indicators to new panes or overlay on existing ones
index5.html:5416   â€¢ Use forceOverlay: true to overlay any indicator
index5.html:5417   â€¢ Quick functions: addRSI(), addMACD(), addBollingerBands(), etc.
index5.html:5418   â€¢ Full UI for managing all indicators
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvaTt net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
value @ manager.js:95
o @ manager.js:127
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
// Test script for indicator sources functionality
// Run this in the browser console after the chart loads

console.log('🧪 Testing Indicator Sources Functionality');
console.log('==========================================');

// Test 1: Check initial sources
console.log('\n📊 Test 1: Initial Sources');
console.log('Initial sources:', window.sourcesConfig.sources);

// Test 2: Add RSI and check sources
console.log('\n📊 Test 2: Adding RSI');
const rsiId = addRSI(14);
console.log('RSI added with ID:', rsiId);
console.log('Sources after RSI:', window.sourcesConfig.sources);

// Test 3: Add MACD and check sources
console.log('\n📊 Test 3: Adding MACD');
const macdId = addMACD(12, 26, 9);
console.log('MACD added with ID:', macdId);
console.log('Sources after MACD:', window.sourcesConfig.sources);

// Test 4: Add Bollinger Bands and check sources
console.log('\n📊 Test 4: Adding Bollinger Bands');
const bbId = addBollingerBands(20, 2);
console.log('Bollinger Bands added with ID:', bbId);
console.log('Sources after BB:', window.sourcesConfig.sources);

// Test 5: Check indicator sources map
console.log('\n📊 Test 5: Indicator Sources Map');
console.log('Indicator sources map:', window.sourcesConfig._indicatorSources);

// Test 6: List all indicators
console.log('\n📊 Test 6: All Indicators');
const indicators = listIndicators();

// Test 7: Add Flexible SMA using indicator source
console.log('\n📊 Test 7: Adding Flexible SMA with RSI source');
setTimeout(() => {
    try {
        const flexSmaId = addFlexibleSMA(10, 'RSI: RSI');
        console.log('Flexible SMA added with ID:', flexSmaId);
        console.log('Final sources:', window.sourcesConfig.sources);
    } catch (error) {
        console.log('Note: Flexible SMA might need manual addition through UI');
        console.log('Available sources for manual selection:', window.sourcesConfig.sources);
    }
}, 2000);

// Test 8: Verify sources contain indicator outputs
console.log('\n📊 Test 8: Verification');
setTimeout(() => {
    const sources = window.sourcesConfig.sources;
    const hasRSI = sources.some(s => s.includes('RSI'));
    const hasMACD = sources.some(s => s.includes('MACD'));
    const hasBB = sources.some(s => s.includes('Bollinger'));
    
    console.log('✅ Test Results:');
    console.log('- RSI sources found:', hasRSI);
    console.log('- MACD sources found:', hasMACD);
    console.log('- Bollinger Bands sources found:', hasBB);
    console.log('- Total sources:', sources.length);
    
    if (hasRSI && hasMACD && hasBB) {
        console.log('🎉 SUCCESS: All indicator sources are working correctly!');
        console.log('💡 Now try adding a Flexible SMA/RSI and check the source dropdown');
    } else {
        console.log('⚠️ Some indicator sources may not be detected');
    }
}, 3000);

// Helper function to test manual source refresh
window.testRefresh = function() {
    console.log('\n🔄 Testing manual refresh...');
    refreshIndicatorSources();
    console.log('Sources after refresh:', window.sourcesConfig.sources);
};

console.log('\n💡 Instructions:');
console.log('1. Wait for the tests to complete (3 seconds)');
console.log('2. Try adding a Flexible SMA: addFlexibleSMA(10, "RSI: RSI")');
console.log('3. Or use the UI to add indicators and check source dropdowns');
console.log('4. Run testRefresh() if sources seem out of sync');

VM213:4 🧪 Testing Indicator Sources Functionality
VM213:5 ==========================================
VM213:8 
📊 Test 1: Initial Sources
VM213:9 Initial sources: (9) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi']
VM213:12 
📊 Test 2: Adding RSI
index5.html:4862 ðŸ”§ Adding indicator: RSI {inputs: Array(1), forceOverlay: false}
index5.html:46 Added source: RSI: RSI Current sources: (10) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI']
index5.html:5290 ðŸ“Š Refreshing indicator definitions with current sources...
index5.html:5291 Current sources for new indicators: (10) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI']
index5.html:5301 âœ… Indicator definitions updated with latest sources
index5.html:79 ðŸ“Š Added indicator sources for RSI: ['RSI']
index5.html:4888 âœ… Indicator added successfully: RSI (ID: [object Promise])
index5.html:4889 ðŸ“Š Updated sources: (10) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI']
VM213:14 RSI added with ID: Promise {<pending>}
VM213:15 Sources after RSI: (10) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI']
VM213:18 
📊 Test 3: Adding MACD
index5.html:4862 ðŸ”§ Adding indicator: MACD {inputs: Array(3), forceOverlay: false}
 Added source: MACD: MACD Current sources: (11) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD']
 Added source: MACD: Signal Current sources: (12) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal']
index5.html:46 Added source: MACD: Histogram Current sources: (13) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram']
index5.html:5290 ðŸ“Š Refreshing indicator definitions with current sources...
index5.html:5291 Current sources for new indicators: (13) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram']
index5.html:5301 âœ… Indicator definitions updated with latest sources
index5.html:79 ðŸ“Š Added indicator sources for MACD: (3) ['MACD', 'Signal', 'Histogram']
index5.html:4888 âœ… Indicator added successfully: MACD (ID: [object Promise])
index5.html:4889 ðŸ“Š Updated sources: (13) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram']
VM213:20 MACD added with ID: Promise {<pending>}
VM213:21 Sources after MACD: (13) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram']
VM213:24 
📊 Test 4: Adding Bollinger Bands
index5.html:4862 ðŸ”§ Adding indicator: Bollinger Bands {inputs: Array(2), forceOverlay: true}
index5.html:46 Added source: Bollinger Bands: Upper Band Current sources: (14) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band']
index5.html:46 Added source: Bollinger Bands: Middle Band Current sources: (15) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band']
index5.html:46 Added source: Bollinger Bands: Lower Band Current sources: (16) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band', 'Bollinger Bands: Lower Band']
index5.html:5290 ðŸ“Š Refreshing indicator definitions with current sources...
index5.html:5291 Current sources for new indicators: (16) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band', 'Bollinger Bands: Lower Band']
index5.html:5301 âœ… Indicator definitions updated with latest sources
index5.html:79 ðŸ“Š Added indicator sources for Bollinger Bands: (3) ['Upper Band', 'Middle Band', 'Lower Band']
index5.html:4888 âœ… Indicator added successfully: Bollinger Bands (ID: [object Promise])
index5.html:4889 ðŸ“Š Updated sources: (16) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band', 'Bollinger Bands: Lower Band']
VM213:26 Bollinger Bands added with ID: Promise {<pending>}
VM213:27 Sources after BB: (16) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band', 'Bollinger Bands: Lower Band']
VM213:30 
📊 Test 5: Indicator Sources Map
VM213:31 Indicator sources map: Map(3) {Promise => {…}, Promise => {…}, Promise => {…}}
VM213:34 
📊 Test 6: All Indicators
index5.html:4968 ðŸ“Š Current indicators: (2) [{…}, {…}]
index5.html:5218 (index)idnametype(index)idnametype0'cwzq0C''Candlestick Patterns''indicator'1'PYtMmX''Volume''indicator'Array(2)
VM213:38 
📊 Test 7: Adding Flexible SMA with RSI source
VM213:51 
📊 Test 8: Verification
VM213:79 
💡 Instructions:
VM213:80 1. Wait for the tests to complete (3 seconds)
VM213:81 2. Try adding a Flexible SMA: addFlexibleSMA(10, "RSI: RSI")
VM213:82 3. Or use the UI to add indicators and check source dropdowns
VM213:83 4. Run testRefresh() if sources seem out of sync
library.529d5b8c7f545237e54b.js:16571 Passing study inputs as an ordered array is now deprecated. Please use an object where keys correspond to the inputs of your study instead.
createStudy @ library.529d5b8c7f545237e54b.js:16571
await in createStudy
addIndicator @ index5.html:4865
window.addMACD @ index5.html:5245
(anonymous) @ VM213:19
library.529d5b8c7f545237e54b.js:16571 Passing study inputs as an ordered array is now deprecated. Please use an object where keys correspond to the inputs of your study instead.
createStudy @ library.529d5b8c7f545237e54b.js:16571
await in createStudy
addIndicator @ index5.html:4865
window.addBollingerBands @ index5.html:5252
(anonymous) @ VM213:25
undefined
library.529d5b8c7f545237e54b.js:16270 Uncaught (in promise) Error: unexpected study id:rsi
    at h.findStudyMetaInfoByDescription (library.529d5b8c7f545237e54b.js:16270:1275)
    at Uy.createStudy (library.529d5b8c7f545237e54b.js:16570:3015)
findStudyMetaInfoByDescription @ library.529d5b8c7f545237e54b.js:16270
createStudy @ library.529d5b8c7f545237e54b.js:16570
await in createStudy
addIndicator @ index5.html:4865
window.addRSI @ index5.html:5238
(anonymous) @ VM213:13
library.529d5b8c7f545237e54b.js:15870 Uncaught (in promise) TypeError: Cannot use 'in' operator to search for 'currency-id' in 12
    at tt (library.529d5b8c7f545237e54b.js:15870:2313)
    at Ke._resolveSymbolImpl (library.529d5b8c7f545237e54b.js:15865:1676)
    at library.529d5b8c7f545237e54b.js:15862:736
    at new Promise (<anonymous>)
    at Ke.resolveSymbol (library.529d5b8c7f545237e54b.js:15862:710)
    at JSServer.ChartApi.resolveSymbol (library.529d5b8c7f545237e54b.js:41:1643)
    at Km.resolveSymbol (library.529d5b8c7f545237e54b.js:16498:1738)
    at library.529d5b8c7f545237e54b.js:16254:255
    at new Promise (<anonymous>)
    at Kt._resolveSymbol (library.529d5b8c7f545237e54b.js:16254:185)
tt @ library.529d5b8c7f545237e54b.js:15870
_resolveSymbolImpl @ library.529d5b8c7f545237e54b.js:15865
(anonymous) @ library.529d5b8c7f545237e54b.js:15862
resolveSymbol @ library.529d5b8c7f545237e54b.js:15862
JSServer.ChartApi.resolveSymbol @ library.529d5b8c7f545237e54b.js:41
resolveSymbol @ library.529d5b8c7f545237e54b.js:16498
(anonymous) @ library.529d5b8c7f545237e54b.js:16254
_resolveSymbol @ library.529d5b8c7f545237e54b.js:16254
_allSymbolsAreResolved @ library.529d5b8c7f545237e54b.js:16253
start @ library.529d5b8c7f545237e54b.js:16242
await in start
(anonymous) @ library.529d5b8c7f545237e54b.js:16455
Promise.then
insertStudyWithParams @ library.529d5b8c7f545237e54b.js:16455
redo @ library.529d5b8c7f545237e54b.js:16377
a @ library.529d5b8c7f545237e54b.js:16544
_pushUndoCommand @ library.529d5b8c7f545237e54b.js:16495
_insertStudy @ library.529d5b8c7f545237e54b.js:16495
createStudy @ library.529d5b8c7f545237e54b.js:16488
_insertStudy @ library.529d5b8c7f545237e54b.js:16444
insert @ library.529d5b8c7f545237e54b.js:16444
await in insert
createStudy @ library.529d5b8c7f545237e54b.js:16571
await in createStudy
addIndicator @ index5.html:4865
window.addMACD @ index5.html:5245
(anonymous) @ VM213:19
index5.html:5290 ðŸ“Š Refreshing indicator definitions with current sources...
index5.html:5291 Current sources for new indicators: (16) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band', 'Bollinger Bands: Lower Band']
index5.html:5301 âœ… Indicator definitions updated with latest sources
index5.html:4862 ðŸ”§ Adding indicator: Flexible SMA {inputs: Array(2), forceOverlay: false}
index5.html:46 Added source: Flexible SMA: SMA Current sources: (17) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band', 'Bollinger Bands: Lower Band', 'Flexible SMA: SMA']
index5.html:5290 ðŸ“Š Refreshing indicator definitions with current sources...
index5.html:5291 Current sources for new indicators: (17) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band', 'Bollinger Bands: Lower Band', 'Flexible SMA: SMA']
index5.html:5301 âœ… Indicator definitions updated with latest sources
index5.html:79 ðŸ“Š Added indicator sources for Flexible SMA: ['SMA']
index5.html:4888 âœ… Indicator added successfully: Flexible SMA (ID: [object Promise])
index5.html:4889 ðŸ“Š Updated sources: (17) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band', 'Bollinger Bands: Lower Band', 'Flexible SMA: SMA']
VM213:42 Flexible SMA added with ID: Promise {<pending>}
VM213:43 Final sources: (17) ['open', 'high', 'low', 'close', 'hl2', 'hlc3', 'ohlc4', 'volume', 'oi', 'RSI: RSI', 'MACD: MACD', 'MACD: Signal', 'MACD: Histogram', 'Bollinger Bands: Upper Band', 'Bollinger Bands: Middle Band', 'Bollinger Bands: Lower Band', 'Flexible SMA: SMA']
library.529d5b8c7f545237e54b.js:16270 Uncaught (in promise) Error: unexpected study id:flexible sma
    at h.findStudyMetaInfoByDescription (library.529d5b8c7f545237e54b.js:16270:1275)
    at Uy.createStudy (library.529d5b8c7f545237e54b.js:16570:3015)
findStudyMetaInfoByDescription @ library.529d5b8c7f545237e54b.js:16270
createStudy @ library.529d5b8c7f545237e54b.js:16570
await in createStudy
addIndicator @ index5.html:4865
window.addFlexibleSMA @ index5.html:5274
(anonymous) @ VM213:41
setTimeout
(anonymous) @ VM213:39
VM213:58 ✅ Test Results:
VM213:59 - RSI sources found: true
VM213:60 - MACD sources found: true
VM213:61 - Bollinger Bands sources found: true
VM213:62 - Total sources: 17
VM213:65 🎉 SUCCESS: All indicator sources are working correctly!
VM213:66 💡 Now try adding a Flexible SMA/RSI and check the source dropdown
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvcGN net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
value @ manager.js:95
o @ manager.js:127
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvdxa net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
value @ manager.js:95
o @ manager.js:127
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvfkL net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
value @ manager.js:95
o @ manager.js:127
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvhXp net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
value @ manager.js:95
o @ manager.js:127
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvjKb net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
value @ manager.js:95
o @ manager.js:127
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvl7G net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
value @ manager.js:95
o @ manager.js:127
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvmwA net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
value @ manager.js:95
o @ manager.js:127
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
s @ manager.js:39
Vt @ index.js:29
initLiveDataWebSocket @ index5.html:460
createWidgetWithWatchlist @ index5.html:4812
initOnReady @ index5.html:5710
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvoir net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvqVc net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvsIo net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
manager.js:108  GET http://localhost:5001/socket.io/?EIO=4&transport=polling&t=PXOvu5l net::ERR_CONNECTION_REFUSED
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
setTimeout
value @ manager.js:321
(anonymous) @ manager.js:331
o @ manager.js:123
U.emit @ index.mjs:136
value @ socket.js:541
U.emit @ index.mjs:136
value @ transport.js:38
(anonymous) @ polling.js:218
U.emit @ index.mjs:136
value @ polling.js:320
(anonymous) @ polling.js:294
setTimeout
r.onreadystatechange @ polling.js:293
XMLHttpRequest.send
value @ polling.js:298
i @ polling.js:237
value @ polling.js:190
value @ polling.js:215
value @ polling.js:96
value @ polling.js:56
value @ transport.js:46
value @ socket.js:170
a @ socket.js:111
value @ manager.js:108
(anonymous) @ manager.js:328
