(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9754,9685,9937],{662435:e=>{e.exports={wrap:"wrap-HAxAr6QG",image:"image-HAxAr6QG",text:"text-HAxAr6QG"}},421466:e=>{e.exports={section:"section-Og4Rg_SK",heading:"heading-Og4Rg_SK"}},194587:e=>{e.exports={item:"item-nuuDM7vP",normal:"normal-nuuDM7vP",big:"big-nuuDM7vP",selected:"selected-nuuDM7vP",contentCell:"contentCell-nuuDM7vP",content:"content-nuuDM7vP",favourite:"favourite-nuuDM7vP",favoriteActionCell:"favoriteActionCell-nuuDM7vP",iconCell:"iconCell-nuuDM7vP",icon:"icon-nuuDM7vP",checkboxInput:"checkboxInput-nuuDM7vP",label:"label-nuuDM7vP"}},227027:e=>{e.exports={dialog:"dialog-UAy2ZKyS",wrap:"wrap-UAy2ZKyS",empty:"empty-UAy2ZKyS",image:"image-UAy2ZKyS",emptyState:"emptyState-UAy2ZKyS"}},997375:e=>{e.exports={dialog:"dialog-LfNchNNG",tabletDialog:"tabletDialog-LfNchNNG",desktopDialog:"desktopDialog-LfNchNNG"}},8510:e=>{e.exports={button:"button-w6lVe_oI",hovered:"hovered-w6lVe_oI",disabled:"disabled-w6lVe_oI"}},667797:e=>{e.exports={menuWrap:"menuWrap-Kq3ruQo8",isMeasuring:"isMeasuring-Kq3ruQo8",scrollWrap:"scrollWrap-Kq3ruQo8",momentumBased:"momentumBased-Kq3ruQo8",menuBox:"menuBox-Kq3ruQo8",isHidden:"isHidden-Kq3ruQo8"}},908783:(e,t,o)=>{"use strict";o.d(t,{useOutsideEvent:()=>s});var n=o(50959),i=o(855393),l=o(778199);function s(e){const{click:t,mouseDown:o,touchEnd:s,touchStart:a,handler:r,reference:c}=e,h=(0,n.useRef)(null),d=(0,n.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,i.useIsomorphicLayoutEffect)((()=>{const e={click:t,mouseDown:o,touchEnd:s,touchStart:a},n=c?c.current:h.current;return(0,l.addOutsideEventListener)(d.current,n,r,document,e)}),[t,o,s,a,r]),c||h}},823030:(e,t,o)=>{"use strict";o.d(t,{SubmenuContext:()=>i,SubmenuHandler:()=>l});var n=o(50959);const i=n.createContext(null);function l(e){const[t,o]=(0,n.useState)(null),l=(0,n.useRef)(null),s=(0,n.useRef)(new Map);return(0,n.useEffect)((()=>()=>{null!==l.current&&clearTimeout(l.current)}),[]),n.createElement(i.Provider,{value:{current:t,setCurrent:function(e){null!==l.current&&(clearTimeout(l.current),l.current=null);null===t?o(e):l.current=setTimeout((()=>{l.current=null,o(e)}),100)},registerSubmenu:function(e,t){return s.current.set(e,t),()=>{s.current.delete(e)}},isSubmenuNode:function(e){return Array.from(s.current.values()).some((t=>t(e)))}}},e.children)}},730654:(e,t,o)=>{"use strict";o.d(t,{Portal:()=>c,PortalContext:()=>h});var n=o(50959),i=o(632227),l=o(925931),s=o(801808),a=o(481564),r=o(682925);class c extends n.PureComponent{constructor(){super(...arguments),this._uuid=(0,l.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"";const t=this.props.className
;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE,"true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),i.createPortal(n.createElement(h.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,s.getRootOverlapManager)():this.context}}c.contextType=r.SlotContext;const h=n.createContext(null)},682925:(e,t,o)=>{"use strict";o.d(t,{Slot:()=>i,SlotContext:()=>l});var n=o(50959);class i extends n.Component{shouldComponentUpdate(){return!1}render(){return n.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const l=n.createContext(null)},801808:(e,t,o)=>{"use strict";o.d(t,{OverlapManager:()=>s,getRootOverlapManager:()=>r});var n=o(650151),i=o(481564);class l{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class s{constructor(e=document){this._storage=new l,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,o=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,o),this._container=o}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const o=this._windows.get(e);if(void 0!==o)return o;this.registerWindow(e);const n=this._document.createElement("div");if(n.style.position=t.position,n.style.zIndex=this._index.toString(),n.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(n);else if(t.index<=0)this._container.insertBefore(n,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(n,e)}}else"reverse"===t.direction?this._container.insertBefore(n,this._container.firstChild):this._container.appendChild(n);return this._windows.set(e,n),++this._index,n}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveLastWindowToTop(){const e=this._storage.getItems(),t=e[e.length-1];t&&this.moveToTop(t)}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,o)=>{e.hasAttribute(i.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(i.FOCUS_TRAP_DATA_ATTRIBUTE,e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const a=new WeakMap;function r(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,n.ensureDefined)(a.get(t));{
const t=new s(e),o=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return a.set(o,t),t.setContainer(o),e.body.appendChild(o),t}}var c;!function(e){e[e.BaseZindex=150]="BaseZindex"}(c||(c={}))},285089:(e,t,o)=>{"use strict";o.d(t,{setFixedBodyState:()=>r});var n=o(735922);const i=()=>!window.matchMedia("(min-width: 768px)").matches,l=()=>!window.matchMedia("(min-width: 1280px)").matches;let s=0,a=!1;function r(e){const{body:t}=document,o=t.querySelector(".widgetbar-wrap");if(e&&1==++s){const e=(0,n.getCSSProperty)(t,"overflow"),i=(0,n.getCSSPropertyNumericValue)(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&((0,n.setStyle)(o,"right",`${(0,n.getScrollbarWidth)()}px`),t.style.paddingRight=`${i+(0,n.getScrollbarWidth)()}px`,a=!0),t.classList.add("i-no-scroll")}else if(!e&&s>0&&0==--s&&(t.classList.remove("i-no-scroll"),a)){(0,n.setStyle)(o,"right","0px");let e=0;e=o?(r=(0,n.getContentWidth)(o),i()?0:l()?45:Math.min(Math.max(r,45),450)):0,t.scrollHeight<=t.clientHeight&&(e-=(0,n.getScrollbarWidth)()),t.style.paddingRight=(e<0?0:e)+"px",a=!1}var r}},912015:(e,t,o)=>{"use strict";o.d(t,{isPlatformMobile:()=>i});var n=o(69111);o(440891),o(601227);function i(){return!(0,n.isOnMobileAppPage)("any")&&(window.matchMedia("(min-width: 602px) and (min-height: 445px)").matches,!1)}},723698:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Components:()=>z,showDefaultSearchDialog:()=>E,showSymbolSearchItemsDialog:()=>a.showSymbolSearchItemsDialog});var n=o(688401),i=o(972535),l=o(992835),s=o(284741),a=o(558323),r=o(240534),c=o(50959),h=o(650151),d=o(56871),u=o(113060),v=o(944080),g=o(497754),m=o.n(g),p=o(609838),w=o(800296),f=o(993544),b=o(398120),y=o(177042),S=o(292069);function x(e){const{isSelected:t,existInWatchlist:n,findInWatchlist:i,addToWatchlist:l,removeFromWatchlist:s}=e,{selectedAction:a}=(0,h.ensureNotNull)((0,c.useContext)(u.SymbolSearchWatchlistContext));return c.createElement(c.Fragment,null,n?c.createElement(c.Fragment,null,c.createElement(w.ListItemButton,{className:m()(S.action,S.removeAction,t&&2===a&&S.selected,"apply-common-tooltip"),onClick:s,icon:f,title:p.t(null,void 0,o(595888))}),c.createElement(w.ListItemButton,{className:m()(S.action,S.targetAction,t&&1===a&&S.selected,"apply-common-tooltip"),onClick:i,icon:y,title:p.t(null,void 0,o(23653))})):c.createElement(w.ListItemButton,{className:m()(S.action,S.addAction,t&&0===a&&S.selected,"apply-common-tooltip"),onClick:l,icon:b,title:p.t(null,void 0,o(77957))}))}var T=o(180185),L=o(32133),C=o(979359),M=o(190266);var A=o(533408),_=o(442092),k=o(997375);(0,o(912015).isPlatformMobile)();function E(e){new r.WatchedValue({});const t=(0,s.getSymbolSearchCompleteOverrideFunction)(),{defaultValue:o,showSpreadActions:i,source:c,onSearchComplete:h,trackResultsOptions:d,...u}=e,v={...u,showSpreadActions:i??(0,l.canShowSpreadActions)(),onSymbolFiltersParamsChange:void 0,onSearchComplete:(e,o)=>{
t(e[0].symbol,e[0].result).then((e=>{n.linking.setSymbolAndLogInitiator(e.symbol,"symbol search"),h?.(e.symbol)}))},onEmptyResults:void 0};(0,a.showSymbolSearchItemsDialog)({...v,defaultValue:o})}const z={SymbolSearchWatchlistDialogContentItem:function(e){const{addToWatchlist:t,removeFromWatchlist:o,findInWatchlist:n,existInWatchlist:l,isSelected:s,fullSymbolName:a,...r}=e,{onClose:g,searchRef:m,searchSpreads:p}=(0,h.ensureNotNull)((0,c.useContext)(v.SymbolSearchItemsDialogContext)),{setSelectedAction:w,isSpreadOrMultipleMode:f,addAfter:b,clearTargetSymbol:y,highlighted:S,highlight:A}=(0,h.ensureNotNull)((0,c.useContext)(u.SymbolSearchWatchlistContext)),_=f(p,m);return(0,c.useLayoutEffect)((()=>{s&&w(void 0!==l?l?2:0:null)}),[s,l]),c.createElement(d.SymbolSearchDialogContentItem,{...r,fullSymbolName:a,onClick:_?e.onClick:function(n){if(void 0===a)return;if(void 0===l)return void(0,h.ensureDefined)(e.onClick)(n);l?(o(C.WATCHLIST_WIDGET_ID,[a]),E("watchlist remove click",n),b===a&&y()):((0,M.runOrSignInWithPromo)("watchList",{source:"add symbol to watchlist"},(()=>{t(C.WATCHLIST_WIDGET_ID,[a],b),e.id&&A(e.id)})),E("watchlist add click",n));k(n)},isHighlighted:S===e.id,isSelected:s,actions:void 0===l||_?void 0:c.createElement(x,{isSelected:s,existInWatchlist:l,addToWatchlist:function(o){if(o.stopPropagation(),void 0===a)return;(0,M.runOrSignInWithPromo)("watchList",{source:"add symbol to watchlist"},(()=>{t(C.WATCHLIST_WIDGET_ID,[a],b),e.id&&A(e.id)})),k(o),E("watchlist add button",o)},removeFromWatchlist:function(e){if(e.stopPropagation(),void 0===a)return;o(C.WATCHLIST_WIDGET_ID,[a]),k(e),E("watchlist remove button",e),b===a&&y()},findInWatchlist:function(e){if(e.stopPropagation(),void 0===a)return;n(C.WATCHLIST_WIDGET_ID,a),g(),E("watchlist goto button")}})});function k(e){e&&(0,T.modifiersFromEvent)(e)===T.Modifiers.Shift?g():i.mobiletouch||m.current?.select()}function E(e,t){let o=e;t&&(0,T.modifiersFromEvent)(t)===T.Modifiers.Shift&&(o+=" shift"),(0,L.trackEvent)("GUI","SS",o)}},SymbolSearchWatchlistDialog:function(e){const{addToWatchlist:t,removeFromWatchlist:o,findInWatchlist:n,...i}=e,{feedItems:l,searchRef:s,searchSpreads:a,selectedIndex:r,onSubmit:d,setSelectedIndex:g,onClose:p,isMobile:w,isTablet:f,mode:b,setMode:y,symbolSearchState:S}=(0,h.ensureNotNull)((0,c.useContext)(v.SymbolSearchItemsDialogContext)),{selectedAction:x,setSelectedAction:L,isSpreadOrMultipleMode:E,addAfter:z,clearTargetSymbol:P,highlight:H}=(0,h.ensureNotNull)((0,c.useContext)(u.SymbolSearchWatchlistContext)),I=l[r],D="exchange"===b;return c.createElement(A.AdaptivePopupDialog,{...i,className:m()(k.dialog,!w&&(f?k.tabletDialog:k.desktopDialog)),dataName:"watchlist-symbol-search-dialog",onKeyDown:function(e){if(e.target&&e.target!==s.current)return;const t=(0,T.hashFromEvent)(e);switch(t){case 13:return E(a,s)?void d(!0):(I?R(!1):d(!1),void s.current?.select());case 13+T.Modifiers.Shift:return E(a,s)?void d(!0):void(I?R(!0):d(!0));case 27:return e.preventDefault(),D?void y("symbolSearch"):void p()}switch((0,_.mapKeyCodeToDirection)(t)){
case"blockPrev":if(e.preventDefault(),0===r||"good"!==S)return;if(-1===r)return void g(0);g(r-1);break;case"blockNext":if(e.preventDefault(),r===l.length-1||"good"!==S)return;g(r+1);break;case"inlinePrev":if(!I)return;1===x&&(e.preventDefault(),L(2));break;case"inlineNext":if(!I)return;2===x&&(e.preventDefault(),L(1))}},backdrop:!0,draggable:!1});function R(e){if(!I||void 0===I.fullSymbolName)return;const{fullSymbolName:i}=I;switch(x){case 0:(0,M.runOrSignInWithPromo)("watchList",{source:"add symbol to watchlist"},(()=>{t(C.WATCHLIST_WIDGET_ID,[i],z),I.id&&H(I.id)}));break;case 1:return n(C.WATCHLIST_WIDGET_ID,i),void p();case 2:o(C.WATCHLIST_WIDGET_ID,[i]),z===i&&P()}e&&p()}}}},558323:(e,t,o)=>{"use strict";o.d(t,{showSymbolSearchItemsDialog:()=>r});var n=o(50959),i=o(753327),l=o(63192),s=o(798154),a=o(651674);function r(e){const{symbolTypeFilter:t,initialMode:o="symbolSearch",autofocus:r=!0,defaultValue:c,showSpreadActions:h,selectSearchOnInit:d,onSearchComplete:u,dialogTitle:v,placeholder:g,fullscreen:m,initialScreen:p,wrapper:w,dialog:f,contentItem:b,onClose:y,onOpen:S,footer:x,symbolTypes:T,searchInput:L,emptyState:C,hideMarkedListFlag:M,dialogWidth:A="auto",manager:_,shouldReturnFocus:k,onSymbolFiltersParamsChange:E,onEmptyResults:z,customSearchSymbols:P,enableOptionsChain:H,searchInitiationPoint:I}=e;if(l.dialogsOpenerManager.isOpened("SymbolSearch")||l.dialogsOpenerManager.isOpened("ChangeIntervalDialog"))return;const D=document.createElement("div"),R=n.createElement(i.SlotContext.Provider,{value:_??null},n.createElement(s.SymbolSearchItemsDialog,{symbolTypeFilter:t,onClose:F,initialMode:o,defaultValue:c,showSpreadActions:h,hideMarkedListFlag:M,selectSearchOnInit:d,onSearchComplete:u,dialogTitle:v,placeholder:g,fullscreen:m,initialScreen:p,wrapper:w,dialog:f,contentItem:b,footer:x,symbolTypes:T,searchInput:L,emptyState:C,autofocus:r,dialogWidth:A,shouldReturnFocus:k,onSymbolFiltersParamsChange:E,onEmptyResults:z,customSearchSymbols:P,enableOptionsChain:H,searchInitiationPoint:I})),V=(0,a.createReactRoot)(R,D);function F(){V.unmount(),l.dialogsOpenerManager.setAsClosed("SymbolSearch"),y&&y()}return l.dialogsOpenerManager.setAsOpened("SymbolSearch"),S&&S(),{close:F}}},219601:(e,t,o)=>{"use strict";o.r(t),o.d(t,{GlobalSearchDialogRenderer:()=>ft});var n=o(50959),i=o(311804),l=o(995553),s=o(290484),a=o(609838),r=o(870122),c=o(897811),h=o(41899),d=o(231862),u=o(497754),v=o.n(u),g=o(878112),m=o(111982),p=o(702054),w=o(267562),f=o(366619),b=o(662435);function y(e){const{text:t,showIcon:o=!0,className:i}=e,l=p.watchedTheme.value()===m.StdTheme.Dark?f:w;return n.createElement("div",{className:u(b.wrap,i)},o&&n.createElement(g.Icon,{icon:l,className:b.image}),n.createElement("span",{className:b.text},t))}var S=o(533408),x=o(755883),T=o(493173),L=o(871923),C=o(879091),M=o(227570),A=o(194587);const _=(0,T.mergeThemes)(L.DEFAUL_CONTEXT_MENU_ITEM_THEME,A);function k(e){const{action:t,onExecute:o}=e,[i,l]=(0,M.useActiveDescendant)(null);return n.createElement(C.ContextMenuAction,{theme:_,onShowSubMenu:x.default,
isSubMenuOpened:!1,checkboxInput:!0,reference:i,selected:l,action:t,onExecute:()=>o(t),className:v()("apply-overflow-tooltip","apply-overflow-tooltip-focus","apply-overflow-tooltip--direction_y","apply-overflow-tooltip--check-children-recursively"),"data-overflow-tooltip-text":t.getLabel()})}var E=o(421466);function z(e){const{heading:t,items:o,onExecute:i,getActionIndex:l}=e;return n.createElement("table",{className:E.section},n.createElement("tbody",null,n.createElement("tr",null,n.createElement("td",{className:E.heading},t)),o.map((e=>n.createElement(k,{key:e.id,action:e,onExecute:i,index:l(e)})))))}var P=o(227027);const H=[{name:"drawingsActions",label:a.t(null,void 0,o(674385))},{name:"functionActions",label:a.t(null,void 0,o(83576))},{name:"settingsActions",label:a.t(null,void 0,o(232514))}];function I(e){const{dialogId:t,items:i,onClose:l,shouldReturnFocus:u}=e,[v,g]=(0,n.useState)(""),[m,p]=(0,n.useState)([]),w=(0,n.useRef)(null),f=(0,n.useRef)(null),b=(0,n.useRef)(null);(0,n.useEffect)((()=>{w.current?.focus()}),[]),(0,n.useEffect)((()=>{const e=w.current;if(e)return e.addEventListener("input",D),D(),()=>{e&&e.removeEventListener("input",D)}}),[]);const x=(0,n.useCallback)((0,s.default)((e=>{}),1e3),[]),T=(0,n.useCallback)((0,s.default)(l,200),[]);(0,n.useEffect)((()=>()=>{x.flush(),T.cancel()}),[]);const L=(0,n.useMemo)((()=>{const e=new Set(r.getJSON("GlobalSearchDialog.recent",[])),t=[];for(const o of e){const e=i.find((e=>e.getState().id===o));e&&t.push(e)}return t.reverse(),t}),[]),C=(0,n.useMemo)((()=>H.reduce(((e,t)=>(e.set(t.name,m.filter((e=>e.getState().category===t.name))),e)),new Map)),[m]),{handleKeyDown:M,handleForceFocus:A,handleSearchRefBlur:_,onDialogClick:k,resetFocusState:E}=(0,c.useSearchDialogKeyboardNavigation)({dialogRef:f,searchInputRef:w,contentContainerRef:b,getNextFocusedItemIndex:function(e,t){const o=null===e?1===t?-1:0:e;for(let e=1;e<=m.length;e++){const n=(m.length+e*t+o)%m.length;if(!m[n].isDisabled())return n}return null},isNavigationDisabled:!m.length,scrollToFocusedItem:function(e){e?.scrollIntoView({block:"nearest"})},onEscapeClick:l,getElementIdByIndex:function(e,t){let o=m[e].id;null!=t&&(o+="-favorite");return CSS.escape(o)}});return n.createElement(S.AdaptivePopupDialog,{ref:f,dataName:t,title:a.t(null,void 0,o(679354)),onClose:l,onClickOutside:l,shouldReturnFocus:u,render:()=>n.createElement(n.Fragment,null,n.createElement(d.DialogSearch,{reference:w,onBlur:_}),n.createElement("div",{className:P.wrap,ref:b},v?n.createElement(n.Fragment,null,m.length?H.map((e=>{const t=C.get(e.name);return t&&t.length?n.createElement(z,{key:e.name,heading:e.label,items:t,onExecute:I,getActionIndex:V}):null})):n.createElement(y,{text:a.t(null,void 0,o(647202)),className:P.emptyState})):n.createElement(n.Fragment,null,Boolean(m.length)?n.createElement(z,{heading:a.t(null,void 0,o(676753)),items:m,onExecute:I,getActionIndex:V}):n.createElement(y,{text:a.t(null,void 0,o(227463)),showIcon:!1,className:P.emptyState})))),onKeyDown:M,className:P.dialog,onForceFocus:A,onClick:k,isOpened:!0})
;function I(e){const t=new Set(r.getJSON("GlobalSearchDialog.recent",[])),o=e.getState().id;t.has(o)&&t.delete(o),t.add(o),r.setJSON("GlobalSearchDialog.recent",Array.from(t).slice(-10)),e.getState().checkable?T():l()}function D(){E();const e=w.current?w.current.value.toLocaleLowerCase().trim():"";if(g(e),e){const t=i.filter((t=>R(t).includes(e)||function(e,t){const{aliases:o}=t.getState();if(o)return o.some((t=>t.toLowerCase().includes(e)));return!1}(e,t))).sort((t=>R(t)===e?-1:0));p(t),t.length||x(e)}else p(L)}function R(e){const{label:t}=e.getState();return(0,h.isString)(t)?t.toLocaleLowerCase():""}function V(e){return m.findIndex((t=>t.id===e.id))}}var D,R=o(180185),V=o(688401),F=o(704999),W=o(669874),Z=o(743717),B=o(677299),N=o(793483),O=o(964099),K=o(621327);!function(e){e.Drawings="drawingsActions",e.Function="functionActions",e.Settings="settingsActions"}(D||(D={}));class U extends K.Action{constructor({id:e,category:t,favourite:o,onFavouriteClick:n,hotkeyGroup:i,hotkeyHash:l,aliases:s,optionsLoader:a,onStartListening:r,...c}){super({actionId:"UnknownAction",options:{...c,doNotCloseOnClick:!0},id:e,optionsLoader:a,onStartListening:r}),this.execute=()=>{super.execute()},this.getState=()=>({...super.getState(),id:this._searchOptions.id,category:this._searchOptions.category,favourite:this._searchOptions.favourite,onFavouriteClick:this._onFavouriteClick,aliases:this._searchOptions.aliases}),this.update=e=>{this._searchOptions=Object.assign(this._searchOptions,e),super.update(e)},this._onFavouriteClick=e=>{this._searchOptions.onFavouriteClick&&(this.update({favourite:!this._searchOptions.favourite}),this._searchOptions.onFavouriteClick(e))},this._searchOptions={id:e,category:t,favourite:o,onFavouriteClick:n,aliases:s}}}class G extends U{constructor(e){super(e),this._controller=new AbortController}destroy(){super.destroy(),this._controller.abort()}async _load(){const e=await(0,Z.initSymbolListService)();if(this._controller.signal.aborted)return;const{activeSymbolList:t}=e.store.getState();this.update({disabled:"hot"===t?.type})}}var q,j=o(619130),$=o(82498),Q=o(440891),J=o(321303),Y=o(272047),X=o(601227),ee=o(293029);!function(e){e.None="all",e.Following="following",e.Private="private"}(q||(q={}));var te=o(341991),oe=o(344399),ne=o(50296);function ie(e){return e instanceof K.Action}function le(e,t){const o=e.getState().category,n=t.getState().category;return o===n?0:"drawingsActions"===n?1:"drawingsActions"===o||"functionActions"===o?-1:1}var se=o(344557),ae=o(932117),re=o(28033),ce=o(509715),he=o(865211);const de=async e=>{const t=(0,se.tradingService)(),n=e.model().mainSeries();n.symbolInfo();if(null===t||0===(await t.brokersMetainfo()).length)return[];const i=[];if(Q.enabled("buy_sell_buttons")){const t=(0,ce.getBuySellButtonsVisibility)(e.model().model());i.push(new U({id:"ShowBuySellButtons",category:"settingsActions",label:a.t(null,void 0,o(411258)),checkable:!0,checked:t.value(),onExecute:()=>(0,he.setBuySellButtonsVisibility)(e.model(),!t.value())}))}return(0,re.checkIsDOMAvailable)()&&i.push(new U({
label:a.t(null,void 0,o(875635)),icon:o(528009),id:"OpenDOMWidget",category:"functionActions",disabled:!t.tradingPanel.isOpeningAvailable.value(),shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Shift+68),onExecute:()=>{t.setDOMPanelVisibility(!0)}})),(0,ae.createTradeContext)(n).then((e=>{try{return t.chartContextMenuActions(e)}catch(e){return[]}})).then((e=>(i.push(...e.filter(ie).map((e=>new U({...e.options(),id:e.getState().name||e.getLabel(),category:"functionActions",aliases:"trade-new-order"===e.getState().name?[a.t(null,void 0,o(169961)),a.t(null,void 0,o(168222))]:void 0,disabled:e.options().disabled||"trade-new-order"===e.getState().name&&(!Q.enabled("right_toolbar")||!Q.enabled("order_panel"))})))),i)))};var ue=o(747830),ve=o(743003),ge=o(153055),me=o(948711);function pe(e){const t=e.match(/^(\d+).(\d+).(\d+)/);if(!t)return null;const[,o,n,i]=t;return[parseInt(o),parseInt(n),parseInt(i)]}function we(e){const t=(0,X.desktopAppVersion)();return!!t&&function(e,t){const o=pe(e),n=pe(t);if(!o||!n)return!1;const[i,l,s]=o,[a,r,c]=n;return i!==a?i<a:l!==r?l<r:s!==c&&s<c}(t,e)}const fe=e=>{const t=t=>{const n=[];if(t&&t.length&&window.is_authenticated&&t.forEach((t=>{n.push(new U({id:t,category:"settingsActions",label:`${a.t(null,void 0,o(453438))} ${ue.translateStdThemeName(t)}`,onExecute:()=>{(0,me.loadTheme)(e.chartWidgetCollection(),{themeName:t,standardTheme:!1}).then((()=>{e.readOnly()||window.saver.saveChartSilently()}))}}))})),!(0,X.isDesktopApp)()||we("1.0.10")){const[,t]=ue.getStdThemeNames();n.push(new U({id:"DarkColorTheme",category:"settingsActions",label:a.t(null,void 0,o(925848)),checkable:!0,checked:ue.getCurrentTheme().name===t,onExecute:()=>{!function(e){const t=(0,ue.getCurrentTheme)().name===m.StdTheme.Dark?m.StdTheme.Light:m.StdTheme.Dark,n=e.chartModels().value().every((e=>null!==e.model().getThemeNameIfStdTheme())),i=()=>{(0,me.loadTheme)(e,{themeName:t,standardTheme:!0}).then((()=>{e.readOnly()||window.saver.saveChartSilently()}))};if(n)i();else{const e=a.t(null,void 0,o(378659)).format({name:(0,ve.capitalizeFirstLetter)(t)});(0,ge.showConfirm)({text:e,onConfirm:({dialogClose:e})=>{i(),e()},onCancel:({dialogClose:e})=>{(0,p.setTheme)(t),(0,ue.syncTheme)(),e()}})}}(e.chartWidgetCollection())}}))}return n};return window.is_authenticated?ue.getThemeNames().then(t):Promise.resolve(t())};var be=o(651407),ye=o(405017),Se=(o(175855),o(719680)),xe=o(931907),Te=o(812297);const{DrawingSyncMode:Le}=be,Ce={drawings:"ToggleHideAllDrawingTools",indicators:"ToggleHideAllIndicators",positions:"ToggleHideAllPositions",all:"ToggleHideAll"},Me=new Y.TranslatedString("stay in drawing mode",a.t(null,void 0,o(504114))),Ae=new Y.TranslatedString("sync drawings",a.t(null,void 0,o(94521))),_e=a.t(null,void 0,o(362518)),ke=a.t(null,void 0,o(823391)),Ee=(a.t(null,void 0,o(893027)),a.t(null,void 0,o(403521)),a.t(null,void 0,o(779451))),ze=a.t(null,void 0,o(3519)),Pe=a.t(null,void 0,o(494593));var He=o(878845),Ie=o(947552),De=o(723698);class Re extends U{constructor(e){super({label:a.t(null,void 0,o(37189)),
id:"InvertScale",category:"settingsActions",checkable:!0,onExecute:()=>{this._model.invertPriceScale(this._model.mainSeries().priceScale())},shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Alt+73)}),this._model=e;(this._props=this._model.mainSeries().priceScale().properties().childs().isInverted).subscribe(this,(()=>{this._onUpdate.fire(this)}))}destroy(){super.destroy(),this._props.unsubscribeAll(this)}isChecked(){return this._model.mainSeries().priceScale().isInverted()}}class Ve extends U{constructor(e){super({label:a.t(null,void 0,o(806919)),checkable:!0,id:"TogglePercantage",category:"settingsActions",onExecute:()=>{this.isChecked()?this._model.setPriceScaleRegularScaleMode(this._model.mainSeries().priceScale()):this._model.togglePriceScalePercentageScaleMode(this._model.mainSeries().priceScale())},shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Alt+80),disabled:e.mainSeries().priceScale().isLockScale()||6===e.mainSeries().properties().childs().style.value(),checked:e.mainSeries().priceScale().isPercentage()}),this._model=e;(this._props=this._model.mainSeries().priceScale().properties().childs().percentage).subscribe(this,(()=>{this._onUpdate.fire(this)}))}destroy(){super.destroy(),this._props.unsubscribeAll(this)}isChecked(){return this._model.mainSeries().priceScale().isPercentage()}}class Fe extends U{constructor(e){super({label:a.t(null,void 0,o(116170)),id:"ToggleLogScale",category:"settingsActions",checkable:!0,onExecute:()=>{this.isChecked()?this._model.setPriceScaleRegularScaleMode(this._model.mainSeries().priceScale()):this._model.togglePriceScaleLogScaleMode(this._model.mainSeries().priceScale())},shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Alt+76),disabled:e.mainSeries().priceScale().isLockScale()||6===e.mainSeries().properties().childs().style.value(),checked:e.mainSeries().priceScale().isLog()}),this._model=e;(this._props=this._model.mainSeries().priceScale().properties().childs().log).subscribe(this,(()=>{this._onUpdate.fire(this)}))}destroy(){super.destroy(),this._props.unsubscribeAll(this)}isChecked(){return this._model.mainSeries().priceScale().isLog()}}var We=o(686759)
;const Ze=Q.enabled("show_average_close_price_line_and_label"),Be=new Y.TranslatedString("change session",a.t(null,void 0,o(887041))),Ne=new Y.TranslatedString("change plus button visibility",a.t(null,void 0,o(796379))),Oe=new Y.TranslatedString("change countdown to bar close visibility",a.t(null,void 0,o(839383))),Ke=new Y.TranslatedString("scale price chart only",a.t(null,void 0,o(763796))),Ue=new Y.TranslatedString("change symbol last value visibility",a.t(null,void 0,o(467453))),Ge=new Y.TranslatedString("change high and low price labels visibility",a.t(null,void 0,o(524226))),qe=new Y.TranslatedString("change average close price label visibility",a.t(null,void 0,o(876852))),je=new Y.TranslatedString("change indicators and financials value labels visibility",a.t(null,void 0,o(171161))),$e=new Y.TranslatedString("change indicators and financials name labels visibility",a.t(null,void 0,o(735111))),Qe=new Y.TranslatedString("change high and low price lines visibility",a.t(null,void 0,o(180692))),Je=new Y.TranslatedString("change average close price line visibility",a.t(null,void 0,o(1022))),Ye=new Y.TranslatedString("change symbol labels visibility",a.t(null,void 0,o(147074))),Xe=new Y.TranslatedString("change pre/post market price label visibility",a.t(null,void 0,o(530870))),et=new Y.TranslatedString("change symbol previous close value visibility",a.t(null,void 0,o(904729))),tt=new Y.TranslatedString("change previous close price line visibility",a.t(null,void 0,o(958419))),ot=new Y.TranslatedString("change bid and ask labels visibility",a.t(null,void 0,o(669362))),nt=new Y.TranslatedString("change bid and ask lines visibility",a.t(null,void 0,o(952919))),it=new Y.TranslatedString("change pre/post market price lines visibility",a.t(null,void 0,o(791978))),lt=new Y.TranslatedString("change price line visibility",a.t(null,void 0,o(108662))),st=new Y.TranslatedString("change session breaks visibility",a.t(null,void 0,o(338413))),at=(new Y.TranslatedString("change ideas visibility on chart",a.t(null,void 0,o(713489))),new Y.TranslatedString("show all ideas",a.t(null,void 0,o(13336))),new Y.TranslatedString("show ideas of followed users",a.t(null,void 0,o(791395))),new Y.TranslatedString("show my ideas only",a.t(null,void 0,o(757460))),new Y.TranslatedString("change events visibility on chart",a.t(null,void 0,o(906119))),new Y.TranslatedString("change earnings visibility",a.t(null,void 0,o(706819))),new Y.TranslatedString("change dividends visibility",a.t(null,void 0,o(753929))),new Y.TranslatedString("change splits visibility",a.t(null,void 0,o(347474))),{0:a.t(null,void 0,o(919265)),1:a.t(null,void 0,o(264526)),9:a.t(null,void 0,o(944958)),2:a.t(null,void 0,o(92911)),14:a.t(null,void 0,o(935458)),15:a.t(null,void 0,o(140447)),3:a.t(null,void 0,o(209537)),16:a.t(null,void 0,o(739883)),4:a.t(null,void 0,o(880082)),7:a.t(null,void 0,o(411604)),5:a.t(null,void 0,o(747189)),6:a.t(null,void 0,o(522027)),8:a.t(null,void 0,o(462423)),10:a.t(null,void 0,o(319579)),11:a.t(null,void 0,o(349942)),12:a.t(null,void 0,o(470405)),
13:a.t(null,void 0,o(756281)),17:a.t(null,void 0,o(521190)),18:a.t(null,void 0,o(794861)),19:a.t(null,void 0,o(613618)),20:a.t(null,void 0,o(867346)),21:a.t(null,void 0,o(767579))});async function rt(e){const t=[],[n,i]=await Promise.all([fe(e),de(e)]),l=(e=>{const t=[],{stayInDrawingMode:o,drawOnAllCharts:n,drawOnAllChartsMode:i}=be.properties().childs();t.push(new U({label:_e,checkable:!0,checked:o.value(),id:"ToggleStayInDrawingMode",category:"settingsActions",onExecute:()=>{e.model().setProperty(o,!o.value(),Me)}})),t.push(new U({label:ke,checkable:!0,id:"ToggleSyncDrawings",category:"settingsActions",checked:n.value(),disabled:!e.isMultipleLayout().value(),onExecute:()=>{e.model().setProperty(n,!n.value(),Ae)}}));const l=be.lockDrawings();t.push(new U({label:Ee,checkable:!0,id:"ToggleLockDrawings",category:"settingsActions",checked:l.value(),onExecute:()=>{be.lockDrawings().setValue(!be.lockDrawings().value())}}));const s=(0,Te.getSavedHideMode)();t.push(...Array.from((0,Te.getHideOptions)()).map((([e,t])=>new U({label:t.tooltip.inactive,checkable:!0,id:Ce[e],category:"settingsActions",checked:s===e&&(0,Te.getHideModeStateValue)(e),onExecute:()=>(0,Te.toggleHideMode)(e)}))));const{magnet:a,magnetMode:r}=be.properties().childs();return t.push(new U({label:ze,checkable:!0,id:"WeakMagnet",category:"functionActions",checked:a.value()&&r.value()===Se.MagnetMode.WeakMagnet,icon:xe.drawingToolsIcons.magnet,onExecute:()=>{a.value()&&r.value()===Se.MagnetMode.WeakMagnet?(0,ye.setIsMagnetEnabled)(!1):(0,ye.setMagnetMode)(Se.MagnetMode.WeakMagnet)}})),t.push(new U({label:Pe,checkable:!0,id:"StrongMagnet",category:"functionActions",checked:a.value()&&r.value()===Se.MagnetMode.StrongMagnet,icon:xe.drawingToolsIcons.strongMagnet,onExecute:()=>{a.value()&&r.value()===Se.MagnetMode.StrongMagnet?(0,ye.setIsMagnetEnabled)(!1):(0,ye.setMagnetMode)(Se.MagnetMode.StrongMagnet)}})),t})(e),s=function(e){const t=[];return Q.enabled("header_widget")&&Q.enabled("header_compare")&&t.push(new U({icon:o(301393),label:(0,W.appendEllipsis)(a.t(null,void 0,o(657e3))),id:"Compare",category:"functionActions",onExecute:()=>e.toggleCompareOrAdd()})),Q.enabled("header_widget")&&Q.enabled("header_indicators")&&t.push(new U({icon:o(139681),label:(0,W.appendEllipsis)(a.t(null,void 0,o(287829))),id:"InsertIndicator",category:"functionActions",onExecute:()=>{e.showIndicators([])},shortcutHint:e.options().indicatorsDialogShortcutEnabled?(0,R.humanReadableHash)(47):void 0})),Q.enabled("show_object_tree")&&t.push(new U({icon:o(430192),label:a.t(null,void 0,o(427077)),id:"OpenObjectsTreeInRightPanel",category:"functionActions",onExecute:()=>e.showObjectsTreePanelOrDialog()})),Q.enabled("header_widget")&&Q.enabled("header_settings")&&t.push(new U({label:(0,W.appendEllipsis)(a.t(null,void 0,o(232514))),icon:o(634369),id:"ChartProperties",category:"functionActions",onExecute:()=>{e.showGeneralChartProperties()}})),Q.enabled("header_widget")&&Q.enabled("header_symbol_search")&&t.push(new U({icon:o(606347),label:(0,W.appendEllipsis)(a.t(null,void 0,o(963245))),
id:"ChangeSymbol",category:"functionActions",onExecute:()=>{(0,De.showDefaultSearchDialog)({defaultValue:"",trackResultsOptions:void 0,enableOptionsChain:Q.enabled("symbol_search_option_chain_selector")})}})),Q.enabled("symbol_info")&&t.push(new U({label:(0,W.appendEllipsis)(a.t(null,void 0,o(975594))),icon:o(437924),id:"SymbolInfo",category:"functionActions",onExecute:()=>{{const t=e.model().model(),o=t.mainSeries().symbolInfo(),n=t.availableUnits(),i={symbolInfo:o,showUnit:t.unitConversionEnabled(),unitDescription:e=>e?n.description(e):"",dateFormatter:t.dateFormatter()};return void(0,He.showSymbolInfoDialog)(i)}}})),e.options().goToDateEnabled&&t.push(new U({label:(0,W.appendEllipsis)(a.t(null,void 0,o(754280))),icon:o(290752),id:"GoToDate",category:"functionActions",onExecute:()=>{(0,Ie.showGoToDateDialog)(e)},shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Alt+71)})),t.filter((e=>null!==e))}(e);t.push(...n,...l,...s),i&&t.push(...i);const r=e.model().mainSeries(),c=r.priceScale(),h=r.properties().childs(),d=e.model().paneForSource?.(r);t.push(new U({id:"ResetPriceScale",category:"functionActions",label:a.t(null,void 0,o(115332)),icon:o(139267),onExecute:()=>{d&&e.model().resetPriceScale(d,c)},shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Alt+82)})),t.push(new Re(e.model())),t.push(new Ve(e.model())),t.push(new Fe(e.model()));const u=c.isLockScale(),v=6===h.style.value();t.push(new U({label:a.t(null,void 0,o(628051)),checkable:!0,id:"SetRegularSessionId",category:"functionActions",disabled:Boolean("regular"===r.symbolInfo()?.subsession_id),onExecute:()=>{e.model().setProperty(h.sessionId,"regular",Be)},checked:Boolean("regular"===r.symbolInfo()?.subsession_id)})),t.push(new U({label:a.t(null,void 0,o(293308)),checkable:!0,id:"SetExtendedSessionId",category:"functionActions",disabled:!r.symbolInfo()?.subsessions?.some((e=>!e.private&&"extended"===e.id)),onExecute:()=>{const t="extended"===r.symbolInfo()?.subsession_id?"regular":"extended";e.model().setProperty(h.sessionId,t,Be)},checked:Boolean("extended"===r.symbolInfo()?.subsession_id)})),t.push(new U({label:a.t(null,void 0,o(314017)),checkable:!0,id:"ToggleLockScale",category:"settingsActions",onExecute:()=>{e.model().togglePriceScaleLockScaleMode(e.model().mainSeries().priceScale())},checked:c.isLockScale()})),t.push(new U({label:a.t(null,void 0,o(189999)),checkable:!0,id:"ToggleIndexedTo100",category:"settingsActions",onExecute:()=>{c.isIndexedTo100()?e.model().setPriceScaleRegularScaleMode(e.model().mainSeries().priceScale()):e.model().togglePriceScaleIndexedTo100ScaleMode(e.model().mainSeries().priceScale())},disabled:u||v,checked:c.isIndexedTo100()})),t.push(new U({id:"AutoFitsToScreen",category:"settingsActions",label:a.t(null,void 0,o(224157)),checkable:!0,onExecute:()=>{e.model().togglePriceScaleAutoScaleMode(c)},checked:c.isAutoScale(),disabled:c.properties().childs().autoScaleDisabled.value()})),t.push(new U({label:a.t(null,{context:"scale_menu"},o(655300)),checkable:!0,id:"ToggleRegularScale",category:"settingsActions",onExecute:()=>{
e.model().setPriceScaleRegularScaleMode(c)},disabled:u||v||c.isRegular(),checked:c.isRegular()}));const g=e.model().model().priceScaleSlotsCount(),m=0===g.left;t.push(new U({label:m?a.t(null,void 0,o(26493)):a.t(null,void 0,o(140789)),id:"MoveScaleToSide",category:"functionActions",disabled:g.left+g.right!==1,onExecute:()=>{e.model().mergeAllScales(m?"left":"right")}})),t.push(new U({label:a.t(null,void 0,o(107276)),id:"MergeAllScalesToLeft",category:"functionActions",disabled:g.left+g.right===1,onExecute:()=>{e.model().mergeAllScales("left")}})),t.push(new U({label:a.t(null,void 0,o(380219)),id:"MergeAllScalesToRight",category:"functionActions",disabled:g.left+g.right===1,onExecute:()=>{e.model().mergeAllScales("right")}})),t.push(new U({label:a.t(null,void 0,o(371566)),checkable:!0,checked:ee.addPlusButtonProperty.value(),id:"ToggleAddOrderPlusButton",category:"settingsActions",onExecute:()=>{e.model().setProperty(ee.addPlusButtonProperty,!ee.addPlusButtonProperty.value(),Ne)}}));const p=e.properties().childs().scalesProperties.childs(),w=h.showCountdown;t.push(new U({label:a.t(null,void 0,o(583140)),checkable:!0,id:"ToggleCountdown",category:"settingsActions",checked:w.value(),onExecute:()=>{e.model().setProperty(w,!w.value(),Oe)}}));const f=p.scaleSeriesOnly;t.push(new U({label:a.t(null,void 0,o(243758)),checkable:!0,id:"ScalePriceChartOnly",category:"settingsActions",checked:f.value(),onExecute:()=>{e.model().setProperty(f,!f.value(),Ke)}}));const b=p.showSeriesLastValue;t.push(new U({label:a.t(null,void 0,o(410127)),checkable:!0,id:"ToggleSymbolLastValue",category:"settingsActions",checked:b.value(),onExecute:()=>{e.model().setProperty(b,!b.value(),Ue)}}));const y=h.highLowAvgPrice.childs();t.push(new U({label:a.t(null,void 0,o(399479)),checkable:!0,id:"ToggleHighLowPriceLabels",category:"settingsActions",checked:y.highLowPriceLabelsVisible.value(),onExecute:()=>{e.model().setProperty(y.highLowPriceLabelsVisible,!y.highLowPriceLabelsVisible.value(),Ge)}})),Ze&&t.push(new U({label:a.t(null,void 0,o(721841)),checkable:!0,id:"ToggleAverageClosePriceLabel",category:"settingsActions",checked:y.averageClosePriceLabelVisible.value(),onExecute:()=>{const t=!y.averageClosePriceLabelVisible.value();e.model().setProperty(y.averageClosePriceLabelVisible,t,qe)}}));const S=p.showSymbolLabels;t.push(new U({label:a.t(null,void 0,o(832390)),checkable:!0,id:"ToggleSymbolLabels",category:"settingsActions",checked:S.value(),onExecute:()=>{e.model().setProperty(S,!S.value(),Ye)}}));const x=(0,te.combineProperty)(((e,t)=>e||t),p.showStudyLastValue.weakReference(),p.showFundamentalLastValue.weakReference());t.push(new U({label:a.t(null,void 0,o(146850)),checkable:!0,id:"ToggleStudyLastValue",category:"settingsActions",checked:x.value(),onExecute:()=>{const t=!x.value();e.model().beginUndoMacro(je),e.model().setProperty(p.showStudyLastValue,t,null),e.model().setProperty(p.showFundamentalLastValue,t,null),e.model().endUndoMacro()},onDestroy:()=>{x.destroy()}}));const T=(0,
te.combineProperty)(((e,t)=>e||t),p.showStudyPlotLabels.weakReference(),p.showFundamentalNameLabel.weakReference());if(t.push(new U({label:a.t(null,void 0,o(554418)),checkable:!0,id:"ToggleIndicatorsLabels",category:"settingsActions",checked:T.value(),onExecute:()=>{e.model().beginUndoMacro($e);const t=!T.value();e.model().setProperty(p.showStudyPlotLabels,t,null),e.model().setProperty(p.showFundamentalNameLabel,t,null),e.model().endUndoMacro()},onDestroy:()=>{T.destroy()}})),Q.enabled("pre_post_market_price_line")){const n=p.showPrePostMarketPriceLabel;t.push(new U({label:a.t(null,void 0,o(578793)),checkable:!0,id:"TogglePrePostMarketPriceLabel",category:"settingsActions",checked:n.value(),disabled:!r.isPrePostMarketPricesAvailableProperty().value(),onExecute:()=>{e.model().setProperty(n,!n.value(),Xe)}}));const i=h.prePostMarket.childs().visible;t.push(new U({label:a.t(null,void 0,o(597915)),checkable:!0,id:"TogglePrePostMarketPriceLine",category:"settingsActions",checked:i.value(),disabled:!r.isPrePostMarketPricesAvailableProperty().value(),onExecute:()=>{e.model().setProperty(i,!i.value(),it)}}))}{const n=p.showSeriesPrevCloseValue;t.push(new U({label:a.t(null,void 0,o(522071)),checkable:!0,id:"ToggleSymbolPrevCloseValue",category:"settingsActions",checked:n.value(),disabled:e.model().mainSeries().isDWM(),onExecute:()=>{e.model().setProperty(n,!n.value(),et)}}));const i=p.showBidAskLabels;t.push(new U({label:a.t(null,void 0,o(398810)),checkable:!0,id:"ToggleBidAskLabels",category:"settingsActions",checked:i.value(),onExecute:()=>{e.model().setProperty(i,!i.value(),ot)}}));const l=h.bidAsk.childs().visible;t.push(new U({label:a.t(null,void 0,o(292590)),checkable:!0,id:"ToggleBidAskLines",category:"settingsActions",checked:l.value(),onExecute:()=>{e.model().setProperty(l,!l.value(),nt)}}))}t.push(new U({label:a.t(null,void 0,o(433766)),checkable:!0,id:"ToggleHighLowPriceLines",category:"settingsActions",checked:y.highLowPriceLinesVisible.value(),onExecute:()=>{e.model().setProperty(y.highLowPriceLinesVisible,!y.highLowPriceLinesVisible.value(),Qe)}})),Ze&&t.push(new U({label:a.t(null,void 0,o(916138)),checkable:!0,id:"ToggleAverageClosePriceLine",category:"settingsActions",checked:y.averageClosePriceLineVisible.value(),onExecute:()=>{const t=!y.averageClosePriceLineVisible.value();e.model().setProperty(y.averageClosePriceLineVisible,t,Je)}}));const L=h.showPriceLine;t.push(new U({label:a.t(null,void 0,o(72926)),checkable:!0,id:"TogglePriceLine",category:"settingsActions",checked:L.value(),onExecute:()=>{e.model().setProperty(L,!L.value(),lt)}}));const C=h.showPrevClosePriceLine;t.push(new U({label:a.t(null,void 0,o(879366)),checkable:!0,id:"ToggleSymbolPrevCloseLine",disabled:e.model().mainSeries().isDWM(),category:"settingsActions",checked:C.value(),onExecute:()=>{e.model().setProperty(C,!C.value(),tt)}})),t.push(new U({label:a.t(null,void 0,o(354170)),icon:o(139267),id:"ResetTimeScale",category:"functionActions",onExecute:()=>{e.model().resetTimeScale()},shortcutHint:(0,
R.humanReadableHash)(R.Modifiers.Mod+R.Modifiers.Alt+81)}));const M=e.model().model().sessions().properties().childs().sessionHighlight.childs().vertlines.childs().sessBreaks.childs().visible,A=(0,te.createWVFromProperty)(e.model().mainSeries().isDWMProperty()),_=new U({label:a.t(null,void 0,o(366707)),checkable:!0,id:"ToggleSessionBreaks",category:"settingsActions",disabled:A.value(),checked:M.value(),onExecute:()=>{e.model().setProperty(M,!M.value(),st)},onDestroy:()=>A.destroy()});if(A.subscribe((()=>_.update({disabled:A.value()}))),t.push(_),t.push(new U({label:a.t(null,void 0,o(131789)),icon:o(139267),id:"ResetChart",category:"functionActions",onExecute:()=>e.GUIResetScales(),shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Alt+82)})),t.push(new U({icon:o(993544),label:a.t(null,void 0,o(699984)),id:"RemoveAllIndicators",category:"functionActions",onExecute:()=>e.removeAllStudies()})),t.push(new U({icon:o(993544),label:a.t(null,void 0,o(396374)),id:"RemoveAllDrawingTools",category:"functionActions",onExecute:()=>e.removeAllDrawingTools()})),t.push(new U({icon:o(993544),label:a.t(null,void 0,o(804474)),id:"RemoveAllIndicatorsAndDrawingTools",category:"functionActions",onExecute:()=>e.removeAllStudiesDrawingTools()})),t.push(new U({label:a.t(null,void 0,o(222437)),id:"ApplyIndicatorsToAllCharts",category:"functionActions",disabled:!e.applyIndicatorsToAllChartsAvailable(),onExecute:()=>{e.chartWidgetCollection().applyIndicatorsToAllCharts(e)}})),Q.enabled("header_widget")&&Q.enabled("header_undo_redo")&&(t.push(new U({id:"Undo",category:"functionActions",icon:o(377665),label:a.t(null,void 0,o(314804)),onExecute:()=>{e.model().undoHistory().undo()},disabled:e.model().undoHistory().undoStack().isEmpty(),shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Mod+90)})),t.push(new U({id:"Redo",category:"functionActions",icon:o(796052),label:a.t(null,void 0,o(148236)),onExecute:()=>{e.model().undoHistory().redo()},disabled:e.model().undoHistory().redoStack().isEmpty(),shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Mod+89)}))),t.push(new U({label:a.t(null,void 0,o(812754)),id:"MoveChartRight",category:"functionActions",disabled:!e.chartWidgetCollection().activeChartCanBeMoved().value(),onExecute:()=>{e.chartWidgetCollection().moveActiveChartWithUndo(!1)}})),t.push(new U({label:a.t(null,void 0,o(935112)),id:"MoveChartLeft",category:"functionActions",disabled:!e.chartWidgetCollection().activeChartCanBeMoved().value(),onExecute:()=>{e.chartWidgetCollection().moveActiveChartWithUndo(!0)}})),Q.enabled("header_widget")&&Q.enabled("header_chart_type")){const o=(0,oe.allChartStyles)();for(const n of o)t.push(new U({id:`ChartStyle_${n}`,category:"functionActions",disabled:!V.linking.supportedChartStyles.value()?.includes(n),onExecute:()=>{e.chartWidgetCollection().setChartStyleToWidget(n)},icon:ne.SERIES_ICONS[n],label:at[n]}))}return Q.enabled("header_widget")&&(0,We.shouldShowFullscreen)()&&t.push(new U({label:a.t(null,void 0,o(967092)),id:"Fullscreen mode",icon:o(849697),category:"functionActions",checkable:!0,
checked:e.chartWidgetCollection().fullscreen().value(),disabled:!e.chartWidgetCollection().fullscreenable().value(),onExecute:()=>{const t=e.chartWidgetCollection();t.fullscreen().value()?t.exitFullscreen():t.startFullscreen()},shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Shift+70)})),t}var ct=o(650151),ht=o(909434),dt=o(16509),ut=o(488381);function vt(){return ut.lineToolsFlat.map((e=>function(e){const t=dt.lineToolsInfo[e],n=t.selectHotkey?.hash,i={id:e,category:"drawingsActions",label:t.localizedName,icon:t.icon,shortcutHint:n?(0,R.humanReadableHash)(n):void 0,payload:e,onExecute:()=>be.tool.setValue(e),favourite:ht.LinetoolsFavoritesStore.isFavorite(e),onFavouriteClick:t=>{t.preventDefault(),ht.LinetoolsFavoritesStore.isFavorite(e)?ht.LinetoolsFavoritesStore.removeFavorite(e):ht.LinetoolsFavoritesStore.addFavorite(e)}};return e.toLowerCase().includes("fib")&&(i.aliases=[a.t(null,void 0,o(26578))]),new U(i)}(e.name)))}var gt=o(344955);o(930202);const mt=()=>{const e=new U({id:"ManageLayoutDrawings",category:"functionActions",icon:o(881111),label:(0,W.appendEllipsis)(a.t(null,void 0,o(281031))),onExecute:()=>(0,gt.showManageDrawingsDialog)()}),t=Q.enabled("right_toolbar")&&Q.enabled("multiple_watchlists")?new U({id:"CreateNewWatchlist",category:"functionActions",label:(0,W.appendEllipsis)(a.t(null,void 0,o(465034))),icon:o(845437),onExecute:()=>{Q.enabled("multiple_watchlists")&&(0,O.initWatchlistWidget)((e=>{e?.createNewList()}))}}):null,n=Q.enabled("left_toolbar")?[...vt(),e]:[];return Q.enabled("right_toolbar")&&Q.enabled("multiple_watchlists")&&n.push((0,ct.ensureNotNull)(t)),n};var pt=o(33290),wt=o(651674);class ft extends l.DialogRenderer{constructor(e){super(),this._actions=[],this.show=e=>{this.visible().value()||async function(e,t){const n=[],[i,l,s,r,c]=await Promise.all([rt(e),Q.enabled("items_favoriting")?(0,N.getFavoriteDrawingToolbarPromise)():Promise.resolve(null),Promise.resolve(null),Promise.resolve(null),Promise.resolve(null)]);if(n.push(...i),l&&n.push(new U({id:"ToggleFavoriteDrawingsToolbar",category:"settingsActions",checkable:!0,disabled:!l.canBeShown().value(),checked:l.isVisible(),label:a.t(null,void 0,o(351465)),onExecute:()=>{l.isVisible()?l.hide():l.show()}})),Q.enabled("watchlist_import_export")){const e=(0,Z.initSymbolListService)();Q.enabled("watchlist_import_export")&&n.push(new U({label:(0,W.appendEllipsis)(a.t(null,void 0,o(795754))),icon:o(323595),id:"ImportList",category:"functionActions",onExecute:async()=>{!async function(){try{const t=await(0,$.importSymbolsFromFile)();if(!t)return;const{store:o,actions:n}=await e;o.dispatch(n.createNewWatchList(null,t))}catch(e){return}}()}}))}const h=e.chartWidgetCollection();if(Q.enabled("header_widget")&&Q.enabled("header_resolutions")){const t={label:(0,W.appendEllipsis)(a.t(null,void 0,o(102569))),id:"ChangeInterval",category:"functionActions",onExecute:()=>{(0,F.showChangeIntervalDialogAsync)({initVal:V.linking.interval.value(),selectOnInit:!0})}}
;!Q.enabled("show_interval_dialog_on_key_press")||e.readOnly()||e.options().hideSymbolSearch||(t.shortcutHint=(0,R.humanReadableHash)(188)),n.push(new U(t))}if(t&&Q.enabled("header_widget")&&Q.enabled("header_saveload")){n.push(new U({id:"LoadChartLayout",category:"functionActions",label:(0,W.appendEllipsis)(a.t(null,void 0,o(207386))),onExecute:()=>{t.showLoadDialog()},shortcutHint:a.t(null,{context:"hotkey"},o(23821))}));const i=e.getSaveChartService();i&&(n.push(new U({id:"RenameChartLayout",category:"functionActions",label:(0,W.appendEllipsis)(a.t(null,void 0,o(438206))),onExecute:()=>{i.renameChart()}})),n.push(new U({id:"SaveChartLayout",category:"functionActions",icon:o(53707),label:(0,W.appendEllipsis)(a.t(null,void 0,o(841569))),disabled:!i.hasChanges(),onExecute:()=>{i.saveChartOrShowTitleDialog()},shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Mod+83)})))}return n.push(new U({id:"TakeSnapshot",category:"functionActions",icon:o(272644),label:a.t(null,void 0,o(108270)),onExecute:()=>h.takeServerScreenshot(),shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Alt+83)})),Q.enabled("right_toolbar")&&n.push(new U({label:a.t(null,void 0,o(154679)),icon:o(152493),id:"OpenWatchlistInRightPanel",category:"functionActions",onExecute:()=>{window.widgetbar&&window.widgetbar.isVisible()&&window.widgetbar.setPage("base")}})),Q.enabled("right_toolbar")&&Q.enabled("add_to_watchlist")&&n.push(new G({label:a.t(null,{replace:{symbol:(0,J.splitSymbolName)(e.symbolWV().value())[1]}},o(903067)),icon:o(833366),id:"AddToWatchlist",category:"functionActions",onExecute:()=>{(0,B.runOrSigninWithFeature)((()=>{(0,O.initWatchlistWidget)((t=>{t?.addSymbols([e.symbolWV().value()])}))}),{feature:"watchList",source:"add symbol to watchlist"})},shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Alt+87),disabled:!1})),n.push(new U({icon:o(709210),label:a.t(null,void 0,o(719689)),shortcutHint:(0,R.humanReadableHash)(R.Modifiers.Alt+68),id:"OpenDataWindowInRightPanel",category:"functionActions",disabled:!window.widgetbar?.isVisible(),onExecute:j.showChartObjectsWidget})),n}(this._activeChartWidget,this._loadChartService).then((t=>{this._actions=t.concat(mt()).sort(le),this._rootInstance=(0,wt.createReactRoot)(n.createElement(I,{shouldReturnFocus:e?.shouldReturnFocus,dialogId:"globalSearch",items:this._actions,onClose:this.hide}),this._container),this._setVisibility(!0)}))},this.hide=()=>{this._rootInstance?.unmount(),this._setVisibility(!1);for(const e of this._actions)e.destroy()};const t=(0,pt.service)(i.CHART_WIDGET_COLLECTION_SERVICE);this._activeChartWidget=t.activeChartWidget.value(),this._loadChartService=e}}},897811:(e,t,o)=>{"use strict";o.d(t,{getElementId:()=>d,useSearchDialogKeyboardNavigation:()=>h});var n=o(50959),i=o(150335),l=o(930202),s=o(111706),a=o(442092),r=o(822960),c=o(601227);function h(e){const{contentContainerRef:t,searchInputRef:o,dialogRef:i,getNextFocusedItemIndex:h,isNavigationDisabled:u,onEscapeClick:v,scrollToFocusedItem:g,getElementIdByIndex:m=d}=e,[p,w]=(0,n.useState)(null),[f,b]=(0,n.useState)(null),y=(0,
n.useRef)({itemIndex:null,actionIndex:null}),[S,x]=(0,n.useState)(!1);function T(){x(!1),b(null),w(null)}return(0,n.useLayoutEffect)((()=>{if(null===p||!S)return;const e=m(p,f),o=t.current?.querySelector(`#${e}`);return o?.dispatchEvent(new CustomEvent("active-descendant-focus")),()=>{o?.dispatchEvent(new CustomEvent("active-descendant-blur"))}}),[p,S,f]),{handleKeyDown:function(e){const n=(0,l.hashFromEvent)(e);if(27===n&&v)return void v(e);const i=t.current;if(!i||e.target!==o.current)return;if(32===n||13===n){if(null===p)return;e.preventDefault();const t=i.querySelector(`#${m(p,f)}`);if(!(t instanceof HTMLElement))return;return void t.click()}const s=(0,a.mapKeyCodeToDirection)(n);if(!s||u)return;const c=null!==p?i.querySelector(`#${m(p)}`):null;switch(s){case"blockNext":case"blockPrev":{e.preventDefault();const t=h(p,"blockNext"===s?1:-1);if(null===t)return;w(t),x(!0),y.current.itemIndex=t;const o=i.querySelector(`#${m(t)}`);if(g(o,t),null!==f&&o instanceof HTMLElement){const e=Array.from(o.querySelectorAll('[data-role="list-item-action"]'));if(!e.length)return b(null),void(y.current.actionIndex=null);const t=(0,r.clamp)(f,0,e.length-1);b(t),y.current.actionIndex=t}return}case"inlineNext":{if(!c)return;e.preventDefault();const t=Array.from(c.querySelectorAll('[data-role="list-item-action"]'));if(!t||!t.length)return;return null===f?(b(0),void(y.current.actionIndex=0)):f===t.length-1?(b(null),void(y.current.actionIndex=null)):(b(f+1),void(y.current.actionIndex=f+1))}case"inlinePrev":{if(!c)return;e.preventDefault();const t=Array.from(c.querySelectorAll('[data-role="list-item-action"]'));if(!t||!t.length)return;return null===f?(b(t.length-1),void(y.current.actionIndex=t.length-1)):0===f?(b(null),void(y.current.actionIndex=null)):(b(f-1),void(y.current.actionIndex=f-1))}}},handleForceFocus:function(){(0,a.updateTabIndexes)()},handleSearchRefBlur:function(e){T(),w(p),c.CheckMobile.any()||e.relatedTarget!==i.current?.getElement()||e.target.focus()},resetFocusState:T,restoreFocusState:function(){b(y.current.actionIndex),w(y.current.itemIndex),x(!0)},focusVisible:S,focusedItemIndex:p,focusedActionIndex:f,setFocusedItemIndex:w,setFocusedActionIndex:b,onDialogClick:function(e){(0,s.isKeyboardClick)(e)||(x(!1),b(null))}}}function d(e,t){return(0,i.isNumber)(t)&&-1!==t?`list-item-${e}-action-${t}`:`list-item-${e}`}},344955:(e,t,o)=>{"use strict";o.d(t,{showManageDrawingsDialog:()=>i});let n=null;function i(e){return Promise.all([o.e(4166),o.e(8692),o.e(3693),o.e(7159),o.e(1702)]).then(o.bind(o,305624)).then((t=>{const o=new(0,t.ManageDrawingsDialogRenderer)(e);return null!==n&&n.hide(),o.show(),n=o,o}))}},82498:(e,t,o)=>{"use strict";o.d(t,{importSymbolsFromFile:()=>s});var n=o(787382),i=o(440891),l=o(153055);async function s(){if(!i.enabled("watchlist_import_export"))return null;const e=await async function(){const e=document.createElement("input");return e.type="file",e.accept="text/plain",new Promise(((t,o)=>{e.addEventListener("change",(()=>{t(e.files?e.files[0]:null),function(e){if(!e)return;e.value=""}(e)}),{once:!0
}),e.addEventListener("cancel",(()=>o())),e.click()}))}();return e?async function(e){return new Promise((t=>{const i=250;if(e.size/1024>i)return(0,l.showWarning)({title:n.t(null,void 0,o(317299)),text:n.t(null,{replace:{fileLimit:String(i)}},o(316750))}),t(null);const s=new FileReader;s.onload=()=>{var i;"string"!=typeof(i=s.result)||/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(i)||/^[\r\n\t,]*$/.test(i)?(0,l.showWarning)({title:n.t(null,void 0,o(108492)),text:n.t(null,void 0,o(372163))}):t({name:e.name.replace(/^(.+)\..+$/,"$1"),symbols:String(s.result).toUpperCase().split(/[\t\r\n,]+/).filter(Boolean)})},s.readAsText(e)}))}(e):null}},113060:(e,t,o)=>{"use strict";o.d(t,{SymbolSearchWatchlistContext:()=>l});var n,i=o(50959);!function(e){e[e.Add=0]="Add",e[e.Find=1]="Find",e[e.Remove=2]="Remove"}(n||(n={}));const l=i.createContext(null)},63192:(e,t,o)=>{"use strict";o.d(t,{DialogsOpenerManager:()=>n,dialogsOpenerManager:()=>i});class n{constructor(){this._storage=new Map}setAsOpened(e,t){this._storage.set(e,t)}setAsClosed(e){this._storage.delete(e)}isOpened(e){return this._storage.has(e)}getDialogPayload(e){return this._storage.get(e)}}const i=new n},190266:(e,t,o)=>{"use strict";o.d(t,{runOrSignIn:()=>n,runOrSignInWithPromo:()=>i});function n(e,t){e()}function i(e,t,o){o()}},840976:(e,t,o)=>{"use strict";o.d(t,{useEnsuredContext:()=>l});var n=o(50959),i=o(650151);function l(e){return(0,i.ensureNotNull)((0,n.useContext)(e))}},102478:(e,t,o)=>{"use strict";o.d(t,{useResizeObserver:()=>n.useResizeObserver});var n=o(664332)},800296:(e,t,o)=>{"use strict";o.d(t,{ListItemButton:()=>r});var n=o(50959),i=o(497754),l=o.n(i),s=o(878112),a=o(8510);function r(e){const{className:t,disabled:o,...i}=e;return n.createElement(s.Icon,{className:l()(a.button,o&&a.disabled,t),...i})}},930052:(e,t,o)=>{"use strict";o.d(t,{MatchMedia:()=>i});var n=o(50959);class i extends n.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addEventListener("change",this._handleChange)}_unsubscribe(e){e.removeEventListener("change",this._handleChange)}}},230553:(e,t,o)=>{"use strict";o.d(t,{MenuContext:()=>n});const n=o(50959).createContext(null)},510618:(e,t,o)=>{"use strict";o.d(t,{DEFAULT_MENU_THEME:()=>p,Menu:()=>f});var n=o(50959),i=o(497754),l=o.n(i),s=o(650151),a=o(822960),r=o(409174),c=o(753327),h=o(370981),d=o(801808),u=o(926032),v=o(823030),g=o(230553),m=o(667797);const p=m;var w;!function(e){e[e.IndentFromWindow=0]="IndentFromWindow"}(w||(w={}));class f extends n.PureComponent{constructor(e){super(e),this._containerRef=null,this._scrollWrapRef=null,
this._raf=null,this._scrollRaf=null,this._scrollTimeout=void 0,this._manager=new d.OverlapManager,this._hotkeys=null,this._scroll=0,this._handleContainerRef=e=>{this._containerRef=e,this.props.reference&&("function"==typeof this.props.reference&&this.props.reference(e),"object"==typeof this.props.reference&&(this.props.reference.current=e))},this._handleScrollWrapRef=e=>{this._scrollWrapRef=e,"function"==typeof this.props.scrollWrapReference&&this.props.scrollWrapReference(e),"object"==typeof this.props.scrollWrapReference&&(this.props.scrollWrapReference.current=e)},this._handleCustomRemeasureDelegate=()=>{this._resizeForced(),this._handleMeasure()},this._handleMeasure=({callback:e,forceRecalcPosition:t}={})=>{if(this.state.isMeasureValid&&!t)return;const{position:o}=this.props,n=(0,s.ensureNotNull)(this._containerRef);let i=n.getBoundingClientRect();const l=document.documentElement.clientHeight,r=document.documentElement.clientWidth,c=this.props.closeOnScrollOutsideOffset??0;let h=l-0-c;const d=i.height>h;if(d){(0,s.ensureNotNull)(this._scrollWrapRef).style.overflowY="scroll",i=n.getBoundingClientRect()}const{width:u,height:v}=i,g="function"==typeof o?o({contentWidth:u,contentHeight:v,availableWidth:r,availableHeight:l}):o,m=g?.indentFromWindow?.left??0,p=r-(g.overrideWidth??u)-(g?.indentFromWindow?.right??0),w=(0,a.clamp)(g.x,m,Math.max(m,p)),f=(g?.indentFromWindow?.top??0)+c,b=l-(g.overrideHeight??v)-(g?.indentFromWindow?.bottom??0);let y=(0,a.clamp)(g.y,f,Math.max(f,b));if(g.forbidCorrectYCoord&&y<g.y&&(h-=g.y-y,y=g.y),t&&void 0!==this.props.closeOnScrollOutsideOffset&&g.y<=this.props.closeOnScrollOutsideOffset)return void this._handleGlobalClose(!0);const S=g.overrideHeight??(d?h:void 0);this.setState({appearingMenuHeight:t?this.state.appearingMenuHeight:S,appearingMenuWidth:t?this.state.appearingMenuWidth:g.overrideWidth,appearingPosition:{x:w,y},isMeasureValid:!0},(()=>{this.props.doNotRestorePosition||this._restoreScrollPosition(),e&&e()}))},this._restoreScrollPosition=()=>{const e=document.activeElement,t=(0,s.ensureNotNull)(this._containerRef);if(null!==e&&t.contains(e))try{e.scrollIntoView()}catch(e){}else(0,s.ensureNotNull)(this._scrollWrapRef).scrollTop=this._scroll},this._resizeForced=()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0})},this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null})))},this._handleGlobalClose=e=>{this.props.onClose(e)},this._handleSlot=e=>{this._manager.setContainer(e)},this._handleScroll=()=>{this._scroll=(0,s.ensureNotNull)(this._scrollWrapRef).scrollTop},this._handleScrollOutsideEnd=()=>{clearTimeout(this._scrollTimeout),this._scrollTimeout=setTimeout((()=>{this._handleMeasure({forceRecalcPosition:!0})}),80)},this._handleScrollOutside=e=>{e.target!==this._scrollWrapRef&&(this._handleScrollOutsideEnd(),
null===this._scrollRaf&&(this._scrollRaf=requestAnimationFrame((()=>{this._handleMeasure({forceRecalcPosition:!0}),this._scrollRaf=null}))))},this.state={}}componentDidMount(){this._handleMeasure({callback:this.props.onOpen});const{customCloseDelegate:e=h.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.subscribe(this,this._handleGlobalClose),t?.subscribe(null,this._handleCustomRemeasureDelegate),window.addEventListener("resize",this._resize);const o=null!==this.context;this._hotkeys||o||(this._hotkeys=u.createGroup({desc:"Popup menu"}),this._hotkeys.add({desc:"Close",hotkey:27,handler:()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleGlobalClose()}})),this.props.repositionOnScroll&&window.addEventListener("scroll",this._handleScrollOutside,{capture:!0})}componentDidUpdate(){this._handleMeasure()}componentWillUnmount(){const{customCloseDelegate:e=h.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.unsubscribe(this,this._handleGlobalClose),t?.unsubscribe(null,this._handleCustomRemeasureDelegate),window.removeEventListener("resize",this._resize),window.removeEventListener("scroll",this._handleScrollOutside,{capture:!0}),this._hotkeys&&(this._hotkeys.destroy(),this._hotkeys=null),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),null!==this._scrollRaf&&(cancelAnimationFrame(this._scrollRaf),this._scrollRaf=null),this._scrollTimeout&&clearTimeout(this._scrollTimeout)}render(){const{id:e,role:t,"aria-label":o,"aria-labelledby":i,"aria-activedescendant":s,"aria-hidden":a,"aria-describedby":h,"aria-invalid":d,children:u,minWidth:p,theme:w=m,className:f,maxHeight:y,onMouseOver:S,onMouseOut:x,onKeyDown:T,onFocus:L,onBlur:C}=this.props,{appearingMenuHeight:M,appearingMenuWidth:A,appearingPosition:_,isMeasureValid:k}=this.state,E={"--ui-kit-menu-max-width":`${_&&_.x}px`,maxWidth:"calc(100vw - var(--ui-kit-menu-max-width) - 6px)"};return n.createElement(g.MenuContext.Provider,{value:this},n.createElement(v.SubmenuHandler,null,n.createElement(c.SlotContext.Provider,{value:this._manager},n.createElement("div",{id:e,role:t,"aria-label":o,"aria-labelledby":i,"aria-activedescendant":s,"aria-hidden":a,"aria-describedby":h,"aria-invalid":d,className:l()(f,w.menuWrap,!k&&w.isMeasuring),style:{height:M,left:_&&_.x,minWidth:p,position:"fixed",top:_&&_.y,width:A,...this.props.limitMaxWidth&&E},"data-name":this.props["data-name"],"data-tooltip-show-on-focus":this.props["data-tooltip-show-on-focus"],ref:this._handleContainerRef,onScrollCapture:this.props.onScroll,onContextMenu:r.preventDefaultForContextMenu,tabIndex:this.props.tabIndex,onMouseOver:S,onMouseOut:x,onKeyDown:T,onFocus:L,onBlur:C},n.createElement("div",{className:l()(w.scrollWrap,!this.props.noMomentumBasedScroll&&w.momentumBased),style:{overflowY:void 0!==M?"scroll":"auto",maxHeight:y},onScrollCapture:this._handleScroll,ref:this._handleScrollWrapRef},n.createElement(b,{className:w.menuBox},u)))),n.createElement(c.Slot,{reference:this._handleSlot})))}update(e){e?this._resizeForced():this._resize()}focus(e){
this._containerRef?.focus(e)}blur(){this._containerRef?.blur()}}function b(e){const t=(0,s.ensureNotNull)((0,n.useContext)(v.SubmenuContext)),o=n.useRef(null);return n.createElement("div",{ref:o,className:e.className,onMouseOver:function(e){if(!(null!==t.current&&e.target instanceof Node&&(n=e.target,o.current?.contains(n))))return;var n;t.isSubmenuNode(e.target)||t.setCurrent(null)},"data-name":"menu-inner"},e.children)}f.contextType=v.SubmenuContext},28466:(e,t,o)=>{"use strict";o.d(t,{CloseDelegateContext:()=>l});var n=o(50959),i=o(370981);const l=n.createContext(i.globalCloseDelegate)},8361:(e,t,o)=>{"use strict";o.d(t,{Portal:()=>n.Portal,PortalContext:()=>n.PortalContext});var n=o(730654)},753327:(e,t,o)=>{"use strict";o.d(t,{Slot:()=>n.Slot,SlotContext:()=>n.SlotContext});var n=o(682925)},493173:(e,t,o)=>{"use strict";function n(e,t,o={}){return Object.assign({},e,function(e,t,o={}){const n=Object.assign({},t);for(const i of Object.keys(t)){const l=o[i]||i;l in e&&(n[i]=[e[l],t[i]].join(" "))}return n}(e,t,o))}o.d(t,{mergeThemes:()=>n})},321303:(e,t,o)=>{"use strict";function n(e){return e.includes(":")?e.split(":"):["",e]}o.d(t,{splitSymbolName:()=>n})},931907:(e,t,o)=>{"use strict";o.d(t,{drawingToolsIcons:()=>n});const n={SyncDrawing:o(299088),arrow:o(363743),cursor:o(418953),dot:o(372196),demonstration:o(454780),performance:"",drawginmode:o(452459),drawginmodeActive:o(963975),eraser:o(927999),group:o(134059),hideAllDrawings:o(245820),hideAllDrawingsActive:o(484959),hideAllIndicators:o(842321),hideAllIndicatorsActive:o(875895),hideAllDrawingTools:o(193756),hideAllDrawingToolsActive:o(842650),hideAllPositionsTools:o(157313),hideAllPositionsToolsActive:o(265162),lockAllDrawings:o(691244),lockAllDrawingsActive:o(665186),magnet:o(268385),heart:o(10862),smile:o(507636),sticker:o(662567),strongMagnet:o(146049),measure:o(688518),removeAllDrawingTools:o(993544),showObjectsTree:o(36515),zoom:o(6894),"zoom-out":o(745360)}},488381:(e,t,o)=>{"use strict";o.d(t,{isLineToolsGroupWithSections:()=>r,lineTools:()=>a,lineToolsFlat:()=>c});var n=o(609838),i=(o(601227),o(440891)),l=o(41899);const s=i.enabled("image_drawingtool"),a=[{id:"linetool-group-cursors",title:n.t(null,void 0,o(681578)),sections:[{items:[{name:"cursor"},{name:"dot"},{name:"arrow"},{name:"demonstration"},null].filter(l.isExistent)},{items:[{name:"eraser"}]}],trackLabel:null},{id:"linetool-group-trend-line",title:n.t(null,void 0,o(948773)),sections:[{title:n.t(null,void 0,o(156982)),items:[{name:"LineToolTrendLine"},{name:"LineToolRay"},{name:"LineToolInfoLine"},{name:"LineToolExtended"},{name:"LineToolTrendAngle"},{name:"LineToolHorzLine"},{name:"LineToolHorzRay"},{name:"LineToolVertLine"},{name:"LineToolCrossLine"}]},{title:n.t(null,void 0,o(159934)),items:[{name:"LineToolParallelChannel"},{name:"LineToolRegressionTrend"},{name:"LineToolFlatBottom"},{name:"LineToolDisjointAngle"}]},{title:n.t(null,void 0,o(536167)),items:[{name:"LineToolPitchfork"},{name:"LineToolSchiffPitchfork2"},{name:"LineToolSchiffPitchfork"},{name:"LineToolInsidePitchfork"}]}],
trackLabel:null},{id:"linetool-group-gann-and-fibonacci",title:n.t(null,void 0,o(602654)),sections:[{title:n.t(null,void 0,o(26578)),items:[{name:"LineToolFibRetracement"},{name:"LineToolTrendBasedFibExtension"},{name:"LineToolFibChannel"},{name:"LineToolFibTimeZone"},{name:"LineToolFibSpeedResistanceFan"},{name:"LineToolTrendBasedFibTime"},{name:"LineToolFibCircles"},{name:"LineToolFibSpiral"},{name:"LineToolFibSpeedResistanceArcs"},{name:"LineToolFibWedge"},{name:"LineToolPitchfan"}]},{title:n.t(null,void 0,o(451494)),items:[{name:"LineToolGannSquare"},{name:"LineToolGannFixed"},{name:"LineToolGannComplex"},{name:"LineToolGannFan"}]}],trackLabel:null},{id:"linetool-group-patterns",title:n.t(null,void 0,o(846417)),sections:[{title:n.t(null,void 0,o(846417)),items:[{name:"LineTool5PointsPattern"},{name:"LineToolCypherPattern"},{name:"LineToolHeadAndShoulders"},{name:"LineToolABCD"},{name:"LineToolTrianglePattern"},{name:"LineToolThreeDrivers"}]},{title:n.t(null,void 0,o(44255)),items:[{name:"LineToolElliottImpulse"},{name:"LineToolElliottCorrection"},{name:"LineToolElliottTriangle"},{name:"LineToolElliottDoubleCombo"},{name:"LineToolElliottTripleCombo"}]},{title:n.t(null,void 0,o(177915)),items:[{name:"LineToolCircleLines"},{name:"LineToolTimeCycles"},{name:"LineToolSineLine"}]}],trackLabel:null},{id:"linetool-group-prediction-and-measurement",title:n.t(null,void 0,o(501410)),sections:[{title:n.t(null,void 0,o(375747)),items:[{name:"LineToolRiskRewardLong"},{name:"LineToolRiskRewardShort"},{name:"LineToolPrediction"},{name:"LineToolBarsPattern"},{name:"LineToolGhostFeed"},{name:"LineToolProjection"}].filter(l.isExistent)},{title:n.t(null,void 0,o(669260)),items:[{name:"LineToolAnchoredVWAP"},{name:"LineToolFixedRangeVolumeProfile"},null].filter(l.isExistent)},{title:n.t(null,void 0,o(897050)),items:[{name:"LineToolPriceRange"},{name:"LineToolDateRange"},{name:"LineToolDateAndPriceRange"}]}],trackLabel:null},{id:"linetool-group-geometric-shapes",title:n.t(null,void 0,o(22145)),sections:[{title:n.t(null,void 0,o(565695)),items:[{name:"LineToolBrush"},{name:"LineToolHighlighter"}]},{title:n.t(null,void 0,o(819147)),items:[{name:"LineToolArrowMarker"},{name:"LineToolArrow"},{name:"LineToolArrowMarkUp"},{name:"LineToolArrowMarkDown"},{name:"LineToolArrowMarkLeft"},{name:"LineToolArrowMarkRight"}].filter(l.isExistent)},{title:n.t(null,void 0,o(65781)),items:[{name:"LineToolRectangle"},{name:"LineToolRotatedRectangle"},{name:"LineToolPath"},{name:"LineToolCircle"},{name:"LineToolEllipse"},{name:"LineToolPolyline"},{name:"LineToolTriangle"},{name:"LineToolArc"},{name:"LineToolBezierQuadro"},{name:"LineToolBezierCubic"}]}],trackLabel:null},{id:"linetool-group-annotation",title:n.t(null,void 0,o(432064)),sections:[{title:n.t(null,void 0,o(565831)),items:[{name:"LineToolText"},{name:"LineToolTextAbsolute"},{name:"LineToolTextNote"},{name:"LineToolPriceNote"},{name:"LineToolNote"},{name:"LineToolTable"},{name:"LineToolCallout"},{name:"LineToolComment"},{name:"LineToolPriceLabel"},{name:"LineToolSignpost"},{
name:"LineToolFlagMark"}].filter(l.isExistent)},{title:n.t(null,void 0,o(93111)),items:[s?{name:"LineToolImage"}:null,null,null].filter(l.isExistent)}],trackLabel:null}];function r(e){return"sections"in e}const c=a.map((function(e){return r(e)?e.sections.map((e=>e.items)).flat():e.items})).flat()},686759:(e,t,o)=>{"use strict";o.d(t,{shouldShowFullscreen:()=>i});var n=o(440891);function i(){return n.enabled("header_fullscreen_button")}},16509:(e,t,o)=>{"use strict";o.d(t,{lineToolsInfo:()=>b});var n=o(650151),i=o(609838),l=o(725784),s=(o(68212),o(89935)),a=o(931907);const r={SyncDrawing:i.t(null,void 0,o(859377)),arrow:i.t(null,void 0,o(511858)),cursor:i.t(null,void 0,o(506969)),demonstration:i.t(null,void 0,o(814939)),dot:i.t(null,void 0,o(957157)),performance:i.t(null,void 0,o(935553)),drawginmode:i.t(null,void 0,o(362518)),eraser:i.t(null,void 0,o(508727)),group:i.t(null,void 0,o(903154)),hideAllDrawings:i.t(null,void 0,o(752563)),lockAllDrawings:i.t(null,void 0,o(779451)),magnet:i.t(null,void 0,o(981396)),measure:i.t(null,void 0,o(91563)),removeAllDrawingTools:i.t(null,void 0,o(357118)),showObjectsTree:i.t(null,void 0,o(685786)),zoom:i.t(null,void 0,o(655774)),"zoom-out":i.t(null,void 0,o(537310))};var c=o(271805),h=o(180185),d=o(868424);const u=(0,h.humanReadableModifiers)(h.Modifiers.Shift,!1).trim(),v=(0,h.humanReadableModifiers)(h.Modifiers.Alt,!1).trim(),g=(0,h.humanReadableModifiers)(h.Modifiers.Mod,!1).trim(),m={keys:[u],text:i.t(null,void 0,o(623369))},p={keys:[u],text:i.t(null,void 0,o(313798))},w={keys:[u],text:i.t(null,void 0,o(10539))},f={LineTool5PointsPattern:{},LineToolABCD:{},LineToolArc:{},LineToolArrow:{},LineToolArrowMarkDown:{},LineToolArrowMarkLeft:{},LineToolArrowMarkRight:{},LineToolArrowMarkUp:{},LineToolComment:{},LineToolBarsPattern:{},LineToolBezierCubic:{},LineToolBezierQuadro:{},LineToolBrush:{},LineToolCallout:{},LineToolCircleLines:{},LineToolCypherPattern:{},LineToolDateAndPriceRange:{},LineToolDateRange:{},LineToolDisjointAngle:{hotKey:(0,l.hotKeySerialize)(m)},LineToolElliottCorrection:{},LineToolElliottDoubleCombo:{},LineToolElliottImpulse:{},LineToolElliottTriangle:{},LineToolElliottTripleCombo:{},LineToolEllipse:{hotKey:(0,l.hotKeySerialize)(p)},LineToolExtended:{},LineToolFibChannel:{},LineToolFibCircles:{hotKey:(0,l.hotKeySerialize)(p)},LineToolFibRetracement:{},LineToolFibSpeedResistanceArcs:{},LineToolFibSpeedResistanceFan:{hotKey:(0,l.hotKeySerialize)(w)},LineToolFibSpiral:{},LineToolFibTimeZone:{},LineToolFibWedge:{},LineToolFlagMark:{},LineToolFlatBottom:{hotKey:(0,l.hotKeySerialize)(m)},LineToolAnchoredVWAP:{},LineToolGannComplex:{},LineToolGannFixed:{},LineToolGannFan:{},LineToolGannSquare:{hotKey:(0,l.hotKeySerialize)({keys:[u],text:i.t(null,void 0,o(583042))})},LineToolHeadAndShoulders:{},LineToolHorzLine:{hotKey:(0,l.hotKeySerialize)({keys:[v,"H"],text:"{0} + {1}"})},LineToolHorzRay:{},LineToolIcon:{},LineToolImage:{},LineToolEmoji:{},LineToolInsidePitchfork:{},LineToolNote:{},LineToolSignpost:{},LineToolParallelChannel:{hotKey:(0,l.hotKeySerialize)(m)},
LineToolPitchfan:{},LineToolPitchfork:{},LineToolPolyline:{},LineToolPath:{},LineToolPrediction:{},LineToolPriceLabel:{},LineToolPriceNote:{hotKey:(0,l.hotKeySerialize)(m)},LineToolTextNote:{},LineToolArrowMarker:{},LineToolPriceRange:{},LineToolProjection:{},LineToolRay:{},LineToolRectangle:{hotKey:(0,l.hotKeySerialize)({keys:[u],text:i.t(null,void 0,o(10539))})},LineToolCircle:{},LineToolRegressionTrend:{},LineToolRiskRewardLong:{},LineToolRiskRewardShort:{},LineToolFixedRangeVolumeProfile:{},LineToolRotatedRectangle:{hotKey:(0,l.hotKeySerialize)(m)},LineToolSchiffPitchfork:{},LineToolSchiffPitchfork2:{},LineToolSineLine:{},LineToolText:{},LineToolTextAbsolute:{},LineToolThreeDrivers:{},LineToolTimeCycles:{},LineToolTrendAngle:{hotKey:(0,l.hotKeySerialize)(m)},LineToolTrendBasedFibExtension:{},LineToolTrendBasedFibTime:{},LineToolTrendLine:{hotKey:(0,l.hotKeySerialize)(m)},LineToolInfoLine:{},LineToolTriangle:{},LineToolTrianglePattern:{},LineToolVertLine:{hotKey:(0,l.hotKeySerialize)({keys:[v,"V"],text:"{0} + {1}"})},LineToolCrossLine:{},LineToolHighlighter:{},LineToolGhostFeed:{},LineToolTable:{},SyncDrawing:{iconActive:a.drawingToolsIcons.SyncDrawingActive},arrow:{},cursor:{},dot:{},demonstration:{hotKey:(0,l.hotKeySerialize)({keys:[v],text:i.t(null,void 0,o(42633))})},drawginmode:{iconActive:a.drawingToolsIcons.drawginmodeActive},eraser:{},group:{},hideAllDrawings:{iconActive:a.drawingToolsIcons.hideAllDrawingsActive,hotKey:(0,l.hotKeySerialize)({keys:[g,v,"H"],text:"{0} + {1} + {2}"})},lockAllDrawings:{iconActive:a.drawingToolsIcons.lockAllDrawingsActive},magnet:{hotKey:(0,l.hotKeySerialize)({keys:[g],text:"{0}"})},measure:{hotKey:(0,l.hotKeySerialize)({keys:[u],text:i.t(null,void 0,o(292949))})},removeAllDrawingTools:{},showObjectsTree:{},zoom:{},"zoom-out":{}};const b={};Object.entries(f).map((([e,t])=>{const o=s.lineToolsIcons[e]??a.drawingToolsIcons[e];(0,n.assert)(!!o,`Icon is not defined for drawing "${e}"`);const i=c.lineToolsLocalizedNames[e]??r[e];(0,n.assert)(!!i,`Localized name is not defined for drawing "${e}"`);return{...t,name:e,icon:o,localizedName:i,selectHotkey:d.lineToolsSelectHotkeys[e]}})).forEach((e=>{b[e.name]=e}))},50296:(e,t,o)=>{"use strict";o.r(t),o.d(t,{SERIES_ICONS:()=>S});var n=o(149387),i=o(893316),l=o(173149),s=o(943031),a=o(283617),r=o(704475),c=o(94670),h=o(832162),d=o(539956),u=o(14083),v=o(45504),g=o(352867),m=o(241473),p=o(831246),w=o(715726),f=o(724464),b=o(671705),y=o(309450);const S={3:c,16:h,0:d,1:u,8:v,9:g,2:m,14:p,15:w,10:f,12:b,13:y};S[4]=n,S[6]=i,S[7]=l,S[5]=s,S[19]=a,S[21]=r},28033:(e,t,o)=>{"use strict";o.d(t,{checkIsDOMAvailable:()=>i});var n=o(440891);function i(){return n.enabled("dom_widget")&&!0}},509715:(e,t,o)=>{"use strict";function n(e){return e.properties().childs().paneProperties.childs().legendProperties.childs().showTradingButtons}o.d(t,{getBuySellButtonsVisibility:()=>n})},865211:(e,t,o)=>{"use strict";o.d(t,{setBuySellButtonsVisibility:()=>a,undoShowBuySellButtonsVisibility:()=>s});var n=o(609838),i=o(272047),l=o(509715)
;const s=new i.TranslatedString("change buy/sell buttons visibility",n.t(null,void 0,o(605598)));function a(e,t){e.setProperty((0,l.getBuySellButtonsVisibility)(e.model()),t,s)}},175855:(e,t,o)=>{"use strict";o.d(t,{setDrawingSyncMode:()=>l,toggleDrawingSync:()=>s});var n=o(651407),i=o(792535);function l(e){const{drawOnAllCharts:t,drawOnAllChartsMode:o}=(0,n.properties)().childs();(0,i.allowSavingDefaults)(!0),o.setValue(e),t.setValue(!0),(0,i.allowSavingDefaults)(!1)}function s(){const{drawOnAllCharts:e}=(0,n.properties)().childs();(0,i.allowSavingDefaults)(!0),e.setValue(!e.value()),(0,i.allowSavingDefaults)(!1)}},909434:(e,t,o)=>{"use strict";o.d(t,{LinetoolsFavoritesStore:()=>c});var n=o(329452),i=o(41899),l=o(870122);const s=["LineToolBalloon","LineToolNoteAbsolute",null,null].filter(i.isExistent),a=!1;var r,c;!function(e){function t(){e.favorites=[];let t=!1;const n=Boolean(void 0===(0,l.getValue)("chart.favoriteDrawings")),r=(0,l.getJSON)("chart.favoriteDrawings",[]);if(0===r.length&&n&&"undefined"!=typeof window){const e=JSON.parse(window.urlParams?.favorites??"{}").drawingTools;e&&Array.isArray(e)&&r.push(...e)}r.forEach(((n,i)=>{const l=n.tool||n;o(l)?s.includes(l)?t=!0:e.favorites.push(l):a&&a.includes(l)&&e.hiddenToolsPositions.set(l,i)})),t&&i(),e.favoritesSynced.fire()}function o(e){return"string"==typeof e&&""!==e&&!(a&&a.includes(e))}function i(t){const o=e.favorites.slice();e.hiddenToolsPositions.forEach(((e,t)=>{o.splice(e,0,t)})),(0,l.setJSON)("chart.favoriteDrawings",o,t)}e.favorites=[],e.favoritesSynced=new n.Delegate,e.hiddenToolsPositions=new Map,e.favoriteIndex=function(t){return e.favorites.indexOf(t)},e.isValidLineToolName=o,e.saveFavorites=i,t(),l.onSync.subscribe(null,t)}(r||(r={})),function(e){function t(e){return r.isValidLineToolName(e)}function o(){return r.favorites.length}function i(e){return-1!==r.favoriteIndex(e)}e.favoriteAdded=new n.Delegate,e.favoriteRemoved=new n.Delegate,e.favoriteMoved=new n.Delegate,e.favoritesSynced=r.favoritesSynced,e.favorites=function(){return r.favorites.slice()},e.isValidLineToolName=t,e.favoritesCount=o,e.favorite=function(e){return e<0||e>=o()?"":r.favorites[e]},e.addFavorite=function(o,n){return!(i(o)||!t(o)||"performance"===o)&&(r.favorites.push(o),r.saveFavorites(n),e.favoriteAdded.fire(o),!0)},e.removeFavorite=function(t,o){const n=r.favoriteIndex(t);if(-1===n)return!1;r.favorites.splice(n,1);const i=r.hiddenToolsPositions;return i.forEach(((e,t)=>{e>n&&i.set(t,e-1)})),r.saveFavorites(o),e.favoriteRemoved.fire(t),!0},e.isFavorite=i,e.moveFavorite=function(n,i,l){if(i<0||i>=o()||!t(n))return!1;const s=r.favoriteIndex(n);if(-1===s||i===s)return!1;const a=r.hiddenToolsPositions;return a.forEach(((e,t)=>{s<e&&i>e?e--:i<e&&s>e&&e++,a.set(t,e)})),r.favorites.splice(s,1),r.favorites.splice(i,0,n),r.saveFavorites(l),e.favoriteMoved.fire(n,s,i),!0}}(c||(c={}))},743003:(e,t,o)=>{"use strict";function n(e){return e.charAt(0).toUpperCase()+e.substring(1)}o.d(t,{capitalizeFirstLetter:()=>n})},709210:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M5 2H4v3H1v1h3v3h1V6h3V5H5V2Z"/><path fill="currentColor" fill-rule="evenodd" d="M17.5 11h-7a1.5 1.5 0 0 0 0 3h7a1.5 1.5 0 0 0 0-3Zm-7-1a2.5 2.5 0 0 0 0 5h7a2.5 2.5 0 0 0 0-5h-7Z"/><path fill="currentColor" d="M8 18h12v1H8v-1Z"/><path fill="currentColor" d="M21.5 6H10V5h11.5A2.5 2.5 0 0 1 24 7.5v14a2.5 2.5 0 0 1-2.5 2.5h-15A2.5 2.5 0 0 1 4 21.5V11h1v10.5c0 .83.67 1.5 1.5 1.5h15c.83 0 1.5-.67 1.5-1.5v-14c0-.83-.67-1.5-1.5-1.5Z"/></svg>'},454780:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="m11.26 21 3.65-4.78 6.09-.66L10 8zm3.09-5.71-2.33 3.05-.8-8.3 7.02 4.82z"/><path fill-rule="evenodd" d="M25 14a11 11 0 1 1-22 0 11 11 0 0 1 22 0m-1 0a10 10 0 1 1-20 0 10 10 0 0 1 20 0"/></svg>'},452459:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M17.27 4.56a2.5 2.5 0 0 0-3.54 0l-.58.59-9 9-1 1-.15.14V20h4.7l.15-.15 1-1 9-9 .59-.58a2.5 2.5 0 0 0 0-3.54l-1.17-1.17Zm-2.83.7a1.5 1.5 0 0 1 2.12 0l1.17 1.18a1.5 1.5 0 0 1 0 2.12l-.23.23-3.3-3.29.24-.23Zm-.94.95 3.3 3.29-8.3 8.3-3.3-3.3 8.3-8.3Zm-9 9 3.3 3.29-.5.5H4v-3.3l.5-.5Zm16.5.29a1.5 1.5 0 0 0-3 0V18h4.5c.83 0 1.5.67 1.5 1.5v4c0 .83-.67 1.5-1.5 1.5h-6a1.5 1.5 0 0 1-1.5-1.5v-4c0-.83.67-1.5 1.5-1.5h.5v-2.5a2.5 2.5 0 0 1 5 0v.5h-1v-.5ZM16.5 19a.5.5 0 0 0-.5.5v4c0 .*********.5h6a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 0-.5-.5h-6Zm2.5 4v-2h1v2h-1Z"/></svg>'},963975:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M17.27 4.56a2.5 2.5 0 0 0-3.54 0l-.58.59-9 9-1 1-.15.14V20h4.7l.15-.15 1-1 9-9 .59-.58a2.5 2.5 0 0 0 0-3.54l-1.17-1.17Zm-2.83.7a1.5 1.5 0 0 1 2.12 0l1.17 1.18a1.5 1.5 0 0 1 0 2.12l-.23.23-3.3-3.29.24-.23Zm-.94.95 3.3 3.29-8.3 8.3-3.3-3.3 8.3-8.3Zm-9 9 3.3 3.29-.5.5H4v-3.3l.5-.5Zm16.5.29a1.5 1.5 0 0 0-3 0V18h3v-2.5Zm1 0V18h.5c.83 0 1.5.67 1.5 1.5v4c0 .83-.67 1.5-1.5 1.5h-6a1.5 1.5 0 0 1-1.5-1.5v-4c0-.83.67-1.5 1.5-1.5h.5v-2.5a2.5 2.5 0 0 1 5 0ZM16.5 19a.5.5 0 0 0-.5.5v4c0 .*********.5h6a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 0-.5-.5h-6Zm2.5 4v-2h1v2h-1Z"/></svg>'},528009:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14 13C15.1046 13 16 12.1046 16 11C16 9.89543 15.1046 9 14 9C12.8954 9 12 9.89543 12 11C12 12.1046 12.8954 13 14 13ZM15 11C15 11.5523 14.5523 12 14 12C13.4477 12 13 11.5523 13 11C13 10.4477 13.4477 10 14 10C14.5523 10 15 10.4477 15 11ZM14 19C15.1046 19 16 18.1046 16 17C16 15.8954 15.1046 15 14 15C12.8954 15 12 15.8954 12 17C12 18.1046 12.8954 19 14 19ZM15 17C15 17.5523 14.5523 18 14 18C13.4477 18 13 17.5523 13 17C13 16.4477 13.4477 16 14 16C14.5523 16 15 16.4477 15 17ZM24 10H20C19.4477 10 19 10.4477 19 11C19 11.5523 19.4477 12 20 12H24C24.5523 12 25 11.5523 25 11C25 10.4477 24.5523 10 24 10ZM20 9C18.8954 9 18 9.89543 18 11C18 12.1046 18.8954 13 20 13H24C25.1046 13 26 12.1046 26 11C26 9.89543 25.1046 9 24 9H20ZM4 16H8C8.55228 16 9 16.4477 9 17C9 17.5523 8.55228 18 8 18H4C3.44772 18 3 17.5523 3 17C3 16.4477 3.44772 16 4 16ZM2 17C2 15.8954 2.89543 15 4 15H8C9.10457 15 10 15.8954 10 17C10 18.1046 9.10457 19 8 19H4C2.89543 19 2 18.1046 2 17Z"/></svg>'},94670:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="m25.35 5.35-9.5 9.5-.35.36-.35-.36-4.65-4.64-8.15 8.14-.7-.7 8.5-8.5.35-.36.35.36 4.65 4.64 9.15-9.14.7.7ZM2 21h1v1H2v-1Zm2-1H3v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1V9h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1H8v1H7v1H6v1H5v1H4v1Zm1 0v1H4v-1h1Zm1 0H5v-1h1v1Zm1 0v1H6v-1h1Zm0-1H6v-1h1v1Zm1 0H7v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1H8v1H7v1h1v1Zm1 0v1H8v-1h1Zm0-1H8v-1h1v1Zm1 0H9v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1h1v1Zm1 0v1h-1v-1h1Zm0-1v-1h-1v1h1Zm0 0v1h1v1h1v-1h-1v-1h-1Zm6 2v-1h1v1h-1Zm2 0v1h-1v-1h1Zm0-1h-1v-1h1v1Zm1 0h-1v1h1v1h1v-1h1v1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h1v1Zm1 0h-1v1h1v-1Zm0-1h1v1h-1v-1Zm0-1h1v-1h-1v1Zm0 0v1h-1v-1h1Zm-4 3v1h-1v-1h1Z"/></svg>'},539956:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M19 6h-1v7h-3v1h3v8h1v-3h3v-1h-3V6ZM11 7h-1v13H7v1h3v2h1V10h3V9h-3V7Z"/></svg>'},724464:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m10.49 7.55-.42.7-2.1 3.5.86.5 1.68-2.8 1.8 2.82.84-.54-2.23-3.5-.43-.68Zm12.32 4.72-.84-.54 2.61-4 .84.54-2.61 4Zm-5.3 6.3 1.2-1.84.84.54-1.63 2.5-.43.65-.41-.65-1.6-2.5.85-.54 1.17 1.85ZM4.96 16.75l.86.52-2.4 4-.86-.52 2.4-4ZM3 14v1h1v-1H3Zm2 0h1v1H5v-1Zm2 0v1h1v-1H7Zm2 0h1v1H9v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Z"/></svg>'},14083:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 11v6h3v-6h-3zm-.5-1h4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5z"/><path d="M18 7h1v3.5h-1zm0 10.5h1V21h-1z"/><path d="M9 8v12h3V8H9zm-.5-1h4a.5.5 0 0 1 .5.5v13a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-13a.5.5 0 0 1 .5-.5z"/><path d="M10 4h1v3.5h-1zm0 16.5h1V24h-1z"/></svg>'},53707:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><g fill="none"><path stroke="currentColor" d="M11 20.5H7.5a5 5 0 1 1 .42-9.98 7.5 7.5 0 0 1 14.57 2.1 4 4 0 0 1-1 7.877H18"/><path stroke="currentColor" d="M14.5 24V12.5M11 16l3.5-3.5L18 16"/></g></svg>'},309450:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M12 7v14h5V7h-5Zm4 1h-3v12h3V8ZM19 15v6h5v-6h-5Zm4 1h-3v4h3v-4ZM5 12h5v9H5v-9Zm1 1h3v7H6v-7Z"/></svg>'},301393:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.5 6a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17zM4 14.5a9.5 9.5 0 1 1 19 0 9.5 9.5 0 0 1-19 0z"/><path fill="currentColor" d="M9 14h4v-4h1v4h4v1h-4v4h-1v-4H9v-1z"/></svg>'},849697:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor"><path d="M21 7v4h1V6h-5v1z"/><path d="M16.854 11.854l5-5-.708-.708-5 5zM7 7v4H6V6h5v1z"/><path d="M11.146 11.854l-5-5 .708-.708 5 5zM21 21v-4h1v5h-5v-1z"/><path d="M16.854 16.146l5 5-.708.708-5-5z"/><g><path d="M7 21v-4H6v5h5v-1z"/><path d="M11.146 16.146l-5 5 .708.708 5-5z"/></g></g></svg>'},134059:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="30" height="30"><path fill="currentColor" d="M5.5 13A2.5 2.5 0 0 0 3 15.5 2.5 2.5 0 0 0 5.5 18 2.5 2.5 0 0 0 8 15.5 2.5 2.5 0 0 0 5.5 13zm9.5 0a2.5 2.5 0 0 0-2.5 2.5A2.5 2.5 0 0 0 15 18a2.5 2.5 0 0 0 2.5-2.5A2.5 2.5 0 0 0 15 13zm9.5 0a2.5 2.5 0 0 0-2.5 2.5 2.5 2.5 0 0 0 2.5 2.5 2.5 2.5 0 0 0 2.5-2.5 2.5 2.5 0 0 0-2.5-2.5z"/></svg>'},45504:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M9 8v12h3V8H9zm-1-.502C8 7.223 8.215 7 8.498 7h4.004c.275 0 .498.22.498.498v13.004a.493.493 0 0 1-.498.498H8.498A.496.496 0 0 1 8 20.502V7.498z"/><path d="M10 4h1v3.5h-1z"/><path d="M17 6v6h3V6h-3zm-1-.5c0-.276.215-.5.498-.5h4.004c.275 0 .498.23.498.5v7c0 .276-.215.5-.498.5h-4.004a.503.503 0 0 1-.498-.5v-7z"/><path d="M18 2h1v3.5h-1z"/></svg>'},671705:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M7.5 7H7v14h5V7H7.5zM8 20V8h3v12H8zm7.5-11H15v10h5V9h-4.5zm.5 9v-8h3v8h-3z"/></svg>'},832162:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" d="M22 3h1v1h-1V3Zm0 2V4h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1V9h-1V8h-1V7h-1V6h-1V5h-1v1H9v1H8v1H7v1H6v1H5v1H4v1h1v1H4v1h1v-1h1v-1h1v-1h1v-1h1V9h1V8h1v1h1v1h1v1h1v1h1v1h1v-1h1v-1h1v-1h1V9h1V8h1V7h1V6h1V5h-1Zm-1 1V5h1v1h-1Zm-1 1V6h1v1h-1Zm-1 1V7h1v1h-1Zm-1 1V8h1v1h-1Zm-1 1V9h1v1h-1Zm-1 1v-1h1v1h-1Zm-1 0v-1h-1V9h-1V8h-1V7h-1V6h-1v1H9v1H8v1H7v1H6v1H5v1h1v-1h1v-1h1V9h1V8h1V7h1v1h1v1h1v1h1v1h1Zm0 0h1v1h-1v-1Zm.84 6.37 7.5-7-.68-.74-7.15 6.67-4.66-4.65-.33-.34-.36.32-5.5 5 .68.74 5.14-4.68 4.67 **********.35-.33ZM6 23H5v1h1v-1Zm0-1H5v-1h1v1Zm1 0v1H6v-1h1Zm0-1H6v-1h1v1Zm1 0v1H7v-1h1Zm0-1H7v-1h1v1Zm1 0v1H8v-1h1Zm0-1H8v-1h1v1Zm1 0v1H9v-1h1Zm0-1H9v-1h1v1Zm1 0h-1v1h1v1h1v1h1v1h1v1h1v1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1h1v1Zm0 0h1v1h-1v-1Zm2 2v1h1v1h1v1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1Zm0 0v-1h-1v1h1Z"/></svg>'},704475:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M19 6h-1v7v9h1v-3h3v-1h-3V6ZM11 7h-1v16h1V10h3V9h-3V7Z"/></svg>'},352867:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 11v6h3v-6h-3zm-.5-1h4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5z"/><path d="M18 7h1v3.5h-1zm0 10.5h1V21h-1z"/><path d="M9 8v11h3V8H9zm-.5-1h4a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-12a.5.5 0 0 1 .5-.5z"/><path d="M10 4h1v5h-1zm0 14h1v5h-1zM8.5 9H10v1H8.5zM11 9h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11z"/></svg>'},139681:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M20 17l-5 5M15 17l5 5M9 11.5h7M17.5 8a2.5 2.5 0 0 0-5 0v11a2.5 2.5 0 0 1-5 0"/></svg>'},943031:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M11 5h3v12h5V8h3v13h1V7h-5v9h-3V4h-5v18H7v-5H6v6h5z"/></svg>'},831246:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" d="m18.43 15.91 6.96-8.6-.78-.62-6.96 8.6a2.49 2.49 0 0 0-2.63.2l-2.21-2.02A2.5 2.5 0 0 0 10.5 10a2.5 2.5 0 1 0 1.73 4.3l2.12 1.92a2.5 2.5 0 1 0 4.08-.31ZM10.5 14a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm7.5 3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"/><path d="M8.37 13.8c.17.3.4.54.68.74l-5.67 6.78-.76-.64 5.75-6.88Z"/></svg>'},241473:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m25.39 7.31-8.83 10.92-6.02-5.47-7.16 8.56-.76-.64 7.82-9.36 6 5.45L24.61 6.7l.78.62Z"/></svg>'},173149:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M18 24h3V12h-3v12zm-1-13h5v14h-5V11zm-4-8v7h3V3h-3zm-1-1h5v9h-5V2zM8 19h3v-7H8v7zm-1-8h5v9H7v-9z"/></svg>'},893316:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M14.5 16a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5zm0-1a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3zm0 7a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5zm0-1a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3zm3.293-15.5l4.707 4.707.707-.707L18.5 4.793z"/><path d="M18.5 10.207L23.207 5.5l-.707-.707L17.793 9.5zm-.707 1.293l4.707 4.707.707-.707-4.707-4.707z"/><path d="M18.5 16.207l4.707-4.707-.707-.707-4.707 4.707zM5.793 17.5l4.707 4.707.707-.707L6.5 16.793z"/><path d="M6.5 22.207l4.707-4.707-.707-.707L5.793 21.5zM5.793 5.5l4.707 4.707.707-.707L6.5 4.793z"/><path d="M6.5 10.207L11.207 5.5l-.707-.707L5.793 9.5zM5.793 11.5l4.707 4.707.707-.707L6.5 10.793z"/><path d="M6.5 16.207l4.707-4.707-.707-.707L5.793 15.5z"/></svg>'},796052:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M18.293 13l-2.647 2.646.707.708 3.854-3.854-3.854-3.854-.707.708L18.293 12H12.5A5.5 5.5 0 0 0 7 17.5V19h1v-1.5a4.5 4.5 0 0 1 4.5-4.5h5.793z"/></svg>'},149387:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M18 5v5h3V5h-3zm-1-1h5v7h-5V4zm-4 13h3v-5h-3v5zm-1-6h5v7h-5v-7zM8 24h3v-5H8v5zm-1-6h5v7H7v-7z"/></svg>'},272644:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.118 6a.5.5 0 0 0-.447.276L9.809 8H5.5A1.5 1.5 0 0 0 4 9.5v10A1.5 1.5 0 0 0 5.5 21h16a1.5 1.5 0 0 0 1.5-1.5v-10A1.5 1.5 0 0 0 21.5 8h-4.309l-.862-1.724A.5.5 0 0 0 15.882 6h-4.764zm-1.342-.17A1.5 1.5 0 0 1 11.118 5h4.764a1.5 1.5 0 0 1 1.342.83L17.809 7H21.5A2.5 2.5 0 0 1 24 9.5v10a2.5 2.5 0 0 1-2.5 2.5h-16A2.5 2.5 0 0 1 3 19.5v-10A2.5 2.5 0 0 1 5.5 7h3.691l.585-1.17z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5 18a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7zm0 1a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9z"/></svg>'},715726:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M19 5h5v1h-4v13h-6v-7h-4v12H5v-1h4V11h6v7h4V5Z"/></svg>'},363743:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M11.682 16.09l3.504 6.068 1.732-1-3.497-6.057 3.595-2.1L8 7.74v10.512l3.682-2.163zm-.362 1.372L7 20V6l12 7-4.216 2.462 3.5 6.062-3.464 2-3.5-6.062z"/></svg>'},418953:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor"><path d="M18 15h8v-1h-8z"/><path d="M14 18v8h1v-8zM14 3v8h1v-8zM3 15h8v-1h-8z"/></g></svg>'},372196:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><circle fill="currentColor" cx="14" cy="14" r="3"/></svg>'},927999:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29 31" width="29" height="31"><g fill="currentColor" fill-rule="nonzero"><path d="M15.3 22l8.187-8.187c.394-.394.395-1.028.004-1.418l-4.243-4.243c-.394-.394-1.019-.395-1.407-.006l-11.325 11.325c-.383.383-.383 1.018.007 1.407l1.121 1.121h7.656zm-9.484-.414c-.781-.781-.779-2.049-.007-2.821l11.325-11.325c.777-.777 2.035-.78 2.821.006l4.243 4.243c.781.781.78 2.048-.004 2.832l-8.48 8.48h-8.484l-1.414-1.414z"/><path d="M13.011 22.999h7.999v-1h-7.999zM13.501 11.294l6.717 6.717.707-.707-6.717-6.717z"/></g></svg>'},10862:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M24.13 14.65a6.2 6.2 0 0 0-.46-9.28c-2.57-2.09-6.39-1.71-8.75.6l-.92.91-.92-.9c-2.36-2.32-6.18-2.7-8.75-.61a6.2 6.2 0 0 0-.46 9.28l9.07 8.92c.58.57 1.53.57 2.12 0l9.07-8.92Zm-9.77 8.2 9.07-8.91a5.2 5.2 0 0 0-.39-7.8c-2.13-1.73-5.38-1.45-7.42.55L14 8.29l-1.62-1.6c-2.03-2-5.29-2.28-7.42-.55a5.2 5.2 0 0 0-.4 7.8l9.08 8.91c.2.2.52.2.72 0Z"/></svg>'},268385:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor" fill-rule="evenodd"><path fill-rule="nonzero" d="M14 10a2 2 0 0 0-2 2v11H6V12c0-4.416 3.584-8 8-8s8 3.584 8 8v11h-6V12a2 2 0 0 0-2-2zm-3 2a3 3 0 0 1 6 0v10h4V12c0-3.864-3.136-7-7-7s-7 3.136-7 7v10h4V12z"/><path d="M6.5 18h5v1h-5zm10 0h5v1h-5z"/></g></svg>'},688518:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M2 9.75a1.5 1.5 0 0 0-1.5 1.5v5.5a1.5 1.5 0 0 0 1.5 1.5h24a1.5 1.5 0 0 0 1.5-1.5v-5.5a1.5 1.5 0 0 0-1.5-1.5zm0 1h3v2.5h1v-2.5h3.25v3.9h1v-3.9h3.25v2.5h1v-2.5h3.25v3.9h1v-3.9H22v2.5h1v-2.5h3a.5.5 0 0 1 .5.5v5.5a.5.5 0 0 1-.5.5H2a.5.5 0 0 1-.5-.5v-5.5a.5.5 0 0 1 .5-.5z" transform="rotate(-45 14 14)"/></svg>'},36515:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor"><path fill-rule="nonzero" d="M14 18.634l-.307-.239-7.37-5.73-2.137-1.665 9.814-7.633 9.816 7.634-.509.394-1.639 1.269-7.667 5.969zm7.054-6.759l1.131-.876-8.184-6.366-8.186 6.367 1.123.875 7.063 5.491 7.054-5.492z"/><path d="M7 14.5l-1 .57 8 6.43 8-6.5-1-.5-7 5.5z"/><path d="M7 17.5l-1 .57 8 6.43 8-6.5-1-.5-7 5.5z"/></g></svg>'},507636:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M4.05 14a9.95 9.95 0 1 1 19.9 0 9.95 9.95 0 0 1-19.9 0ZM14 3a11 11 0 1 0 0 22 11 11 0 0 0 0-22Zm-3 13.03a.5.5 0 0 1 .64.3 2.5 2.5 0 0 0 4.72 0 .5.5 0 0 1 .94.34 3.5 3.5 0 0 1-6.6 0 .5.5 0 0 1 .3-.64Zm.5-4.53a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm5 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"/></svg>'},662567:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M7 4h14a3 3 0 0 1 3 3v11c0 .34-.03.67-.08 1H20.3c-1.28 0-2.31.97-2.31 2.24V24H7a3 3 0 0 1-3-3V7a3 3 0 0 1 3-3Zm12 19.92A6 6 0 0 0 23.66 20H20.3c-.77 0-1.31.48-1.31 1.24v2.68ZM3 7a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4v11a7 7 0 0 1-7 7H7a4 4 0 0 1-4-4V7Zm8 9.03a.5.5 0 0 1 .64.3 2.5 2.5 0 0 0 4.72 0 .5.5 0 0 1 .94.34 3.5 3.5 0 0 1-6.6 0 .5.5 0 0 1 .3-.64Zm.5-4.53a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm5 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"/></svg>'},146049:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="nonzero" d="M14 5a7 7 0 0 0-7 7v3h4v-3a3 3 0 1 1 6 0v3h4v-3a7 7 0 0 0-7-7zm7 11h-4v3h4v-3zm-10 0H7v3h4v-3zm-5-4a8 8 0 1 1 16 0v8h-6v-8a2 2 0 1 0-4 0v8H6v-8zm3.293 11.294l-1.222-2.037.858-.514 1.777 2.963-2 1 1.223 2.037-.858.514-1.778-2.963 2-1zm9.778-2.551l.858.514-1.223 2.037 2 1-1.777 2.963-.858-.514 1.223-2.037-2-1 1.777-2.963z"/></svg>'},299088:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor"><path fill-rule="nonzero" d="M15.039 5.969l-.019-.019-2.828 2.828.707.707 2.474-2.474c1.367-1.367 3.582-1.367 4.949 0s1.367 3.582 0 4.949l-2.474 2.474.707.707 2.828-2.828-.019-.019c1.415-1.767 1.304-4.352-.334-5.99-1.638-1.638-4.224-1.749-5.99-.334zM5.97 15.038l-.019-.019 2.828-2.828.707.707-2.475 2.475c-1.367 1.367-1.367 3.582 0 4.949s3.582 1.367 4.949 0l2.474-2.474.707.707-2.828 2.828-.019-.019c-1.767 1.415-4.352 1.304-5.99-.334-1.638-1.638-1.749-4.224-.334-5.99z"/><path d="M10.485 16.141l5.656-5.656.707.707-5.656 5.656z"/></g></svg>'},842650:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M19.76 6.07l-.7.7a13.4 13.4 0 011.93 2.47c.19.3.33.55.42.72l.03.04-.03.04a15 15 0 01-2.09 2.9c-1.47 1.6-3.6 3.12-6.32 3.12-.98 0-1.88-.2-2.7-.52l-.77.76c1.03.47 2.18.76 3.47.76 3.12 0 5.5-1.75 7.06-3.44a16 16 0 002.38-3.38v-.02h.01L22 10l.45.22.1-.22-.1-.22L22 10l.45-.22-.01-.02a5.1 5.1 0 00-.15-.28 16 16 0 00-2.53-3.41zM6.24 13.93l.7-.7-.27-.29a15 15 0 01-2.08-2.9L4.56 10l.03-.04a15 15 0 012.09-2.9c1.47-1.6 3.6-3.12 6.32-3.12.98 0 1.88.2 2.7.52l.77-.76A8.32 8.32 0 0013 2.94c-3.12 0-5.5 1.75-7.06 3.44a16 16 0 00-2.38 3.38v.02h-.01L4 10l-.45-.22-.1.22.1.22L4 10l-.45.22.01.02a5.5 5.5 0 00.15.28 16 16 0 002.53 3.41zm6.09-.43a3.6 3.6 0 004.24-4.24l-.93.93a2.6 2.6 0 01-2.36 2.36l-.95.95zm-1.97-3.69l-.93.93a3.6 3.6 0 014.24-4.24l-.95.95a2.6 2.6 0 00-2.36 2.36zm11.29 7.84l-.8.79a1.5 1.5 0 000 2.12l.59.59a1.5 1.5 0 002.12 0l1.8-1.8-.71-.7-1.8 1.79a.5.5 0 01-.7 0l-.59-.59a.5.5 0 010-.7l.8-.8-.71-.7zm-5.5 3.5l.35.35-.35-.35.01-.02.02-.02.02-.02a4.68 4.68 0 01.65-.5c.4-.27 1-.59 1.65-.59.66 0 1.28.33 1.73.77.44.45.77 1.07.77 1.73a2.5 2.5 0 01-.77 1.73 2.5 2.5 0 01-1.73.77h-4a.5.5 0 01-.42-.78l1-1.5 1-1.5a.5.5 0 01.07-.07zm.74.67a3.46 3.46 0 01.51-.4c.35-.24.75-.42 1.1-.42.34 0 .72.17 1.02.48.3.3.48.68.48 1.02 0 .34-.17.72-.48 1.02-.3.3-.68.48-1.02.48h-3.07l.49-.72.97-1.46zM21.2 2.5L5.5 18.2l-.7-.7L20.5 1.8l.7.7z"/></svg>'},875895:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M16.47 3.7A8.32 8.32 0 0013 2.94c-3.12 0-5.5 1.75-7.06 3.44a16 16 0 00-2.38 3.38v.02h-.01L4 10l-.45-.22-.1.22.1.22L4 10l-.45.22.01.02a5.5 5.5 0 00.15.28 16 16 0 002.53 3.41l.7-.7-.27-.29a15 15 0 01-2.08-2.9L4.56 10l.03-.04a15 15 0 012.09-2.9c1.47-1.6 3.6-3.12 6.32-3.12.98 0 1.88.2 2.7.52l.77-.76zm-7.04 7.04l.93-.93a2.6 2.6 0 012.36-2.36l.95-.95a3.6 3.6 0 00-4.24 4.24zm.1 5.56c1.03.47 2.18.76 3.47.76 3.12 0 5.5-1.75 7.06-3.44a16 16 0 002.38-3.38v-.02h.01L22 10l.45.22.1-.22-.1-.22L22 10l.45-.22-.01-.02-.02-.03-.01-.03a9.5 9.5 0 00-.57-1 16 16 0 00-2.08-2.63l-.7.7.27.29a15.01 15.01 0 012.08 2.9l.03.04-.03.04a15 15 0 01-2.09 2.9c-1.47 1.6-3.6 3.12-6.32 3.12-.98 0-1.88-.2-2.7-.52l-.77.76zm2.8-2.8a3.6 3.6 0 004.24-4.24l-.93.93a2.6 2.6 0 01-2.36 2.36l-.95.95zm7.9 3.73c-.12.12-.23.35-.23.77v2h1v1h-1v2c0 .58-.14 1.1-.52 1.48-.38.38-.9.52-1.48.52s-1.1-.14-1.48-.52c-.38-.38-.52-.9-.52-1.48h1c0 .42.1.65.23.77.12.12.35.23.77.23.42 0 .65-.1.77-.23.12-.12.23-.35.23-.77v-2h-1v-1h1v-2c0-.58.14-1.1.52-1.48.38-.38.9-.52 1.48-.52s1.1.14 1.48.52c.38.38.52.9.52 1.48h-1c0-.42-.1-.65-.23-.77-.12-.12-.35-.23-.77-.23-.42 0-.65.1-.77.23zm2.56 6.27l-1.14-1.15.7-.7 1.15 1.14 1.15-1.14.7.7-1.14 1.15 1.14 1.15-.7.7-1.15-1.14-1.15 1.14-.7-.7 1.14-1.15zM21.2 2.5L5.5 18.2l-.7-.7L20.5 1.8l.7.7z"/></svg>'},265162:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M5.5 18.2L21.2 2.5l-.7-.7L4.8 17.5l.7.7zM19.05 6.78l.71-.7a14.26 14.26 0 0 1 2.08 2.64 14.26 14.26 0 0 1 .6 1.05v.02h.01L22 10l.45.22-.01.02a5.18 5.18 0 0 1-.15.28 16 16 0 0 1-2.23 3.1c-1.56 1.69-3.94 3.44-7.06 3.44-1.29 0-2.44-.3-3.47-.76l.76-.76c.83.32 1.73.52 2.71.52 2.73 0 4.85-1.53 6.33-3.12a15.01 15.01 0 0 0 2.08-2.9l.03-.04-.03-.04a15 15 0 0 0-2.36-3.18zM22 10l.45-.22.1.22-.1.22L22 10zM6.94 13.23l-.7.7a14.24 14.24 0 0 1-2.08-2.64 14.28 14.28 0 0 1-.6-1.05v-.02h-.01L4 10l-.45-.22.01-.02a5.55 5.55 0 0 1 .15-.28 16 16 0 0 1 2.23-3.1C7.5 4.69 9.88 2.94 13 2.94c1.29 0 2.44.3 3.47.76l-.76.76A7.27 7.27 0 0 0 13 3.94c-2.73 0-4.85 1.53-6.33 3.12a15 15 0 0 0-2.08 2.9l-.03.04.03.04a15.01 15.01 0 0 0 2.36 3.18zM4 10l-.45.22-.1-.22.1-.22L4 10zm9 3.56c-.23 0-.46-.02-.67-.06l.95-.95a2.6 2.6 0 0 0 2.36-2.36l.93-.93a3.6 3.6 0 0 1-3.57 4.3zm-3.57-2.82l.93-.93a2.6 2.6 0 0 1 2.36-2.36l.95-.95a3.6 3.6 0 0 0-4.24 4.24zM17.5 21.9l3.28 2.18a.5.5 0 1 1-.56.84L17.5 23.1l-2.72 1.82a.5.5 0 1 1-.56-.84l3.28-2.18zM18.58 19.22a.5.5 0 0 1 .7-.14L22 20.9l2.72-1.82a.5.5 0 0 1 .56.84L22 22.1l-3.28-2.18a.5.5 0 0 1-.14-.7z"/></svg>'},665186:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M14 6a3 3 0 0 0-3 3v3h6V9a3 3 0 0 0-3-3zm4 6V9a4 4 0 0 0-8 0v3H8.5A2.5 2.5 0 0 0 6 14.5v7A2.5 2.5 0 0 0 8.5 24h11a2.5 2.5 0 0 0 2.5-2.5v-7a2.5 2.5 0 0 0-2.5-2.5H18zm-5 5a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0v-2zm-6-2.5c0-.83.67-1.5 1.5-1.5h11c.83 0 1.5.67 1.5 1.5v7c0 .83-.67 1.5-1.5 1.5h-11A1.5 1.5 0 0 1 7 21.5v-7z"/></svg>'},691244:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M14 6a3 3 0 0 0-3 3v3h8.5a2.5 2.5 0 0 1 2.5 2.5v7a2.5 2.5 0 0 1-2.5 2.5h-11A2.5 2.5 0 0 1 6 21.5v-7A2.5 2.5 0 0 1 8.5 12H10V9a4 4 0 0 1 8 0h-1a3 3 0 0 0-3-3zm-1 11a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0v-2zm-6-2.5c0-.83.67-1.5 1.5-1.5h11c.83 0 1.5.67 1.5 1.5v7c0 .83-.67 1.5-1.5 1.5h-11A1.5 1.5 0 0 1 7 21.5v-7z"/></svg>'},245820:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M4.56 14a10.05 10.05 0 00.52.91c.41.69 1.04 1.6 1.85 2.5C8.58 19.25 10.95 21 14 21c3.05 0 5.42-1.76 7.07-3.58A17.18 17.18 0 0023.44 14a9.47 9.47 0 00-.52-.91c-.41-.69-1.04-1.6-1.85-2.5C19.42 8.75 17.05 7 14 7c-3.05 0-5.42 1.76-7.07 3.58A17.18 17.18 0 004.56 14zM24 14l.45-.21-.01-.03a7.03 7.03 0 00-.16-.32c-.11-.2-.28-.51-.5-.87-.44-.72-1.1-1.69-1.97-2.65C20.08 7.99 17.45 6 14 6c-3.45 0-6.08 2-7.8 3.92a18.18 18.18 0 00-2.64 3.84v.02h-.01L4 14l-.45-.21-.1.21.1.21L4 14l-.45.21.01.03a5.85 5.85 0 00.16.32c.11.2.28.51.5.87.44.72 1.1 1.69 1.97 2.65C7.92 20.01 10.55 22 14 22c3.45 0 6.08-2 7.8-3.92a18.18 18.18 0 002.64-3.84v-.02h.01L24 14zm0 0l.45.21.1-.21-.1-.21L24 14zm-10-3a3 3 0 100 6 3 3 0 000-6zm-4 3a4 4 0 118 0 4 4 0 01-8 0z"/></svg>'},
193756:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M5 10.76l-.41-.72-.03-.04.03-.04a15 15 0 012.09-2.9c1.47-1.6 3.6-3.12 6.32-3.12 2.73 0 4.85 1.53 6.33 3.12a15.01 15.01 0 012.08 2.9l.03.04-.03.04a15 15 0 01-2.09 2.9c-1.47 1.6-3.6 3.12-6.32 3.12-2.73 0-4.85-1.53-6.33-3.12a15 15 0 01-1.66-2.18zm17.45-.98L22 10l.45.22-.01.02a5.04 5.04 0 01-.15.28 16.01 16.01 0 01-2.23 3.1c-1.56 1.69-3.94 3.44-7.06 3.44-3.12 0-5.5-1.75-7.06-3.44a16 16 0 01-2.38-3.38v-.02h-.01L4 10l-.45-.22.01-.02a5.4 5.4 0 01.15-.28 16 16 0 012.23-3.1C7.5 4.69 9.88 2.94 13 2.94c3.12 0 5.5 1.75 7.06 3.44a16.01 16.01 0 012.38 3.38v.02h.01zM22 10l.45-.22.1.22-.1.22L22 10zM3.55 9.78L4 10l-.45.22-.1-.22.1-.22zm6.8.22A2.6 2.6 0 0113 7.44 2.6 2.6 0 0115.65 10 2.6 2.6 0 0113 12.56 2.6 2.6 0 0110.35 10zM13 6.44A3.6 3.6 0 009.35 10 3.6 3.6 0 0013 13.56c2 0 3.65-1.58 3.65-3.56A3.6 3.6 0 0013 6.44zm7.85 12l.8-.8.7.71-.79.8a.5.5 0 000 .7l.59.59c.2.2.5.2.7 0l1.8-1.8.7.71-1.79 1.8a1.5 1.5 0 01-2.12 0l-.59-.59a1.5 1.5 0 010-2.12zM16.5 21.5l-.35-.35a.5.5 0 00-.07.07l-1 1.5-1 1.5a.5.5 0 00.42.78h4a2.5 2.5 0 001.73-.77A2.5 2.5 0 0021 22.5a2.5 2.5 0 00-.77-1.73A2.5 2.5 0 0018.5 20a3.1 3.1 0 00-1.65.58 5.28 5.28 0 00-.69.55v.01h-.01l.35.36zm.39.32l-.97 1.46-.49.72h3.07c.34 0 .72-.17 1.02-.48.3-.3.48-.68.48-1.02 0-.34-.17-.72-.48-1.02-.3-.3-.68-.48-1.02-.48-.35 0-.75.18-1.1.42a4.27 4.27 0 00-.51.4z"/></svg>'},842321:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M5 10.76a13.27 13.27 0 01-.41-.72L4.56 10l.03-.04a15 15 0 012.08-2.9c1.48-1.6 3.6-3.12 6.33-3.12s4.85 1.53 6.33 3.12a15.01 15.01 0 012.08 2.9l.03.04-.03.04a15 15 0 01-2.08 2.9c-1.48 1.6-3.6 3.12-6.33 3.12s-4.85-1.53-6.33-3.12a15 15 0 01-1.66-2.18zm17.45-.98L22 10l.45.22-.01.02a14.3 14.3 0 01-.6 1.05c-.4.64-1 1.48-1.78 2.33-1.56 1.7-3.94 3.44-7.06 3.44s-5.5-1.75-7.06-3.44a16 16 0 01-2.23-3.1 9.39 9.39 0 01-.15-.28v-.02h-.01L4 10l-.45-.22.01-.02a5.59 5.59 0 01.15-.28 16 16 0 012.23-3.1C7.5 4.69 9.87 2.94 13 2.94c3.12 0 5.5 1.75 7.06 3.44a16 16 0 012.23 3.1 9.5 9.5 0 01.15.28v.01l.01.01zM22 10l.45-.22.1.22-.1.22L22 10zM3.55 9.78L4 10l-.45.22-.1-.22.1-.22zm6.8.22A2.6 2.6 0 0113 7.44 2.6 2.6 0 0115.65 10 2.6 2.6 0 0113 12.56 2.6 2.6 0 0110.35 10zM13 6.44A3.6 3.6 0 009.35 10c0 1.98 1.65 3.56 3.65 3.56s3.65-1.58 3.65-3.56A3.6 3.6 0 0013 6.44zM20 18c0-.42.1-.65.23-.77.12-.13.35-.23.77-.23.42 0 .65.1.77.23.13.12.23.35.23.77h1c0-.58-.14-1.1-.52-1.48-.38-.38-.9-.52-1.48-.52s-1.1.14-1.48.52c-.37.38-.52.9-.52 1.48v2h-1v1h1v2c0 .42-.1.65-.23.77-.12.13-.35.23-.77.23-.42 0-.65-.1-.77-.23-.13-.12-.23-.35-.23-.77h-1c0 .58.14 1.1.52 1.48.38.37.9.52 1.48.52s1.1-.14 1.48-.52c.37-.38.52-.9.52-1.48v-2h1v-1h-1v-2zm1.65 4.35l1.14 1.15-1.14 1.15.7.7 1.15-1.14 1.15 1.14.7-.7-1.14-1.15 1.14-1.15-.7-.7-1.15 1.14-1.15-1.14-.7.7z"/></svg>'},157313:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M4.5 10a8.46 8.46 0 0 0 .46.8c.38.6.94 1.4 1.68 2.19 1.48 1.6 3.62 3.13 6.36 3.13s4.88-1.53 6.36-3.13A15.07 15.07 0 0 0 21.5 10a7.41 7.41 0 0 0-.46-.8c-.38-.6-.94-1.4-1.68-2.19-1.48-1.6-3.62-3.13-6.36-3.13S8.12 5.4 6.64 7A15.07 15.07 0 0 0 4.5 10zM22 10l.41-.19-.4.19zm0 0l.41.19-.4-.19zm.41.19l.09-.19-.09-.19-.01-.02a6.86 6.86 0 0 0-.15-.28c-.1-.18-.25-.45-.45-.76-.4-.64-.99-1.48-1.77-2.32C18.47 4.74 16.11 3 13 3 9.89 3 7.53 4.74 5.97 6.43A15.94 15.94 0 0 0 3.6 9.79v.02h-.01L3.5 10l.09.19.01.02a6.59 6.59 0 0 0 .15.28c.1.18.25.45.45.76.4.64.99 1.48 1.77 2.32C7.53 15.26 9.89 17 13 17c3.11 0 5.47-1.74 7.03-3.43a15.94 15.94 0 0 0 2.37-3.36v-.02h.01zM4 10l-.41-.19.4.19zm9-2.63c-1.5 0-2.7 1.18-2.7 2.63s1.2 2.63 2.7 2.63c1.5 0 2.7-1.18 2.7-2.63S14.5 7.37 13 7.37zM9.4 10C9.4 8.07 11 6.5 13 6.5s3.6 1.57 3.6 3.5S15 13.5 13 13.5A3.55 3.55 0 0 1 9.4 10zm8.1 11.9l3.28 2.18a.5.5 0 1 1-.56.84L17.5 23.1l-2.72 1.82a.5.5 0 1 1-.56-.84l3.28-2.18zm1.78-2.82a.5.5 0 0 0-.56.84L22 22.1l3.28-2.18a.5.5 0 1 0-.56-.84L22 20.9l-2.72-1.82z"/></svg>'},6894:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17.646 18.354l4 4 .708-.708-4-4z"/><path d="M12.5 21a8.5 8.5 0 1 1 0-17 8.5 8.5 0 0 1 0 17zm0-1a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15z"/><path d="M9 13h7v-1H9z"/><path d="M13 16V9h-1v7z"/></svg>'},745360:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17.646 18.354l4 4 .708-.708-4-4z"/><path d="M12.5 21a8.5 8.5 0 1 1 0-17 8.5 8.5 0 0 1 0 17zm0-1a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15z"/><path d="M9 13h7v-1H9z"/></svg>'},377665:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M8.707 13l2.647 2.646-.707.708L6.792 12.5l3.853-3.854.708.708L8.707 12H14.5a5.5 5.5 0 0 1 5.5 5.5V19h-1v-1.5a4.5 4.5 0 0 0-4.5-4.5H8.707z"/></svg>'},283617:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M18 4h1v3h3.5c.28 0 .5.22.5.5v13a.5.5 0 0 1-.5.5H19v3h-1v-3h-3.5a.5.5 0 0 1-.5-.5v-13c0-.28.22-.5.5-.5H18V4Zm-3 16h7V8h-7v12ZM10 7H9v3H7.5a.5.5 0 0 0-.5.5v8c0 .*********.5H9v3h1v-3h1.5a.5.5 0 0 0 .5-.5v-8a.5.5 0 0 0-.5-.5H10V7Zm-1 4H8v7h3v-7H9Z"/></svg>'},833366:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" d="M7 13h7V6h1v7h7v1h-7v7h-1v-7H7v-1z"/></svg>'},290752:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 13v-2.5m8.5 11h6.5a2 2 0 0 0 2-2v-9m-17 0v-2c0-1.1.9-2 2-2h13a2 2 0 0 1 2 2v2m-17 0h17"/><path fill="currentColor" d="M10 4h1v4h-1V4zM17 4h1v4h-1V4z"/><path stroke="currentColor" d="M4 18.5h7.5m0 0L8 22m3.5-3.5L8 15"/></svg>'},323595:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 16v4.5a1 1 0 001 1h14a1 1 0 001-1V16M14.5 5V17m-4-3.5l4 4l4-4"/></svg>'},881111:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M4 6.5C4 5.67 4.67 5 5.5 5h4.2l.15.15L11.71 7h8.79c.83 0 1.5.67 1.5 1.5V11H5V20.5c0 .*********.5H9v1H5.5A1.5 1.5 0 0 1 4 20.5V6.5zM5 10h16V8.5a.5.5 0 0 0-.5-.5h-9.2l-.15-.15L9.29 6H5.5a.5.5 0 0 0-.5.5V10z"/><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14.85 16.85l3.5-3.5-.7-.7-3.5 3.5a1.5 1.5 0 1 0 0 2.7l1.64 1.65-1.64 1.65a1.5 1.5 0 1 0 .7.7l1.65-1.64 1.65 1.64a1.5 1.5 0 1 0 2.7 0l3.5-3.5-.7-.7-3.5 3.5a1.5 1.5 0 0 0-1.3 0l-1.64-1.65 4.14-4.15-.7-.7-4.15 4.14-1.65-1.64a1.5 1.5 0 0 0 0-1.3zm-.85.65a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm6 6a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm-6.5.5a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1z"/></svg>'},430192:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.39 3.84a1 1 0 0 1 1.22 0l8.19 6.37a1 1 0 0 1 0 1.58l-8.19 6.37a1 1 0 0 1-1.22 0L5.2 11.79a1 1 0 0 1 0-1.58l8.19-6.37zm.61.8L5.81 11 14 17.37 22.19 11 14 4.63zM5.3 13.6l8.7 6.76 8.7-*********-8.69 6.77a1 1 0 0 1-1.22 0l-8.7-6.77.62-.78zm8.09 10.55l-8.7-6.77.62-.78L14 23.37l8.7-*********-8.69 6.77a1 1 0 0 1-1.22 0z"/></svg>'},398120:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M13.9 14.1V22h1.2v-7.9H23v-1.2h-7.9V5h-1.2v7.9H6v1.2h7.9z"/></svg>'},177042:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14 9.5a.5.5 0 0 0 1 0V7.02A6.5 6.5 0 0 1 20.98 13H18.5a.5.5 0 0 0 0 1h2.48A6.5 6.5 0 0 1 15 19.98V17.5a.5.5 0 0 0-1 0v2.48A6.5 6.5 0 0 1 8.02 14h2.48a.5.5 0 0 0 0-1H8.02A6.5 6.5 0 0 1 14 7.02V9.5zm1-3.48V4.5a.5.5 0 0 0-1 0v1.52A7.5 7.5 0 0 0 7.02 13H5.5a.5.5 0 0 0 0 1h1.52A7.5 7.5 0 0 0 14 20.98v1.52a.5.5 0 0 0 1 0v-1.52A7.5 7.5 0 0 0 21.98 14h1.52a.5.5 0 0 0 0-1h-1.52A7.5 7.5 0 0 0 15 6.02z"/></svg>'},152493:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M7.5 4.5h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-14a2 2 0 0 1-2-2v-14c0-1.1.9-2 2-2zM9 9.5h11M9 13.5h11M9 17.5h11"/></svg>'},755883:(e,t,o)=>{"use strict";o.d(t,{default:()=>n});const n=function(){}}}]);