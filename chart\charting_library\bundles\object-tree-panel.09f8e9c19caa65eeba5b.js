(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[5031],{456057:e=>{e.exports={logo:"logo-PsAlMQQF",hidden:"hidden-PsAlMQQF",xxceptionallysmalldonotusebrv1023:"xxceptionallysmalldonotusebrv1023-PsAlMQQF",xxxsmall:"xxxsmall-PsAlMQQF",xxsmall:"xxsmall-PsAlMQQF",xsmall:"xsmall-PsAlMQQF",small:"small-PsAlMQQF",medium:"medium-PsAlMQQF",large:"large-PsAlMQQF",xlarge:"xlarge-PsAlMQQF",xxlarge:"xxlarge-PsAlMQQF",xxxlarge:"xxxlarge-PsAlMQQF",skeleton:"skeleton-PsAlMQQF",letter:"letter-PsAlMQQF"}},55679:e=>{e.exports={wrapper:"wrapper-TJ9ObuLF",animated:"animated-TJ9ObuLF",pulsation:"pulsation-TJ9ObuLF"}},509059:e=>{e.exports={"tablet-small-breakpoint":"(max-width: 440px)",item:"item-jFqVJoPk",hovered:"hovered-jFqVJoPk",isDisabled:"isDisabled-jFqVJoPk",isActive:"isActive-jFqVJoPk",shortcut:"shortcut-jFqVJoPk",toolbox:"toolbox-jFqVJoPk",withIcon:"withIcon-jFqVJoPk","round-icon":"round-icon-jFqVJoPk",icon:"icon-jFqVJoPk",labelRow:"labelRow-jFqVJoPk",label:"label-jFqVJoPk",showOnHover:"showOnHover-jFqVJoPk","disclosure-item-circle-logo":"disclosure-item-circle-logo-jFqVJoPk",showOnFocus:"showOnFocus-jFqVJoPk"}},149128:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},185934:(e,t,o)=>{"use strict";o.d(t,{getStyleClasses:()=>a,isCircleLogoWithUrlProps:()=>i});var n=o(497754),s=o(92318),l=o(456057),r=o.n(l);function a(e,t=2,o){return n(r().logo,r()[e],o,0===t||1===t?n(s.skeletonTheme.wrapper,r().skeleton):r().letter,1===t&&s.skeletonTheme.animated)}function i(e){return"logoUrl"in e&&null!==e.logoUrl&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},380327:(e,t,o)=>{"use strict";o.d(t,{ControlGroupContext:()=>n});const n=o(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},383836:(e,t,o)=>{"use strict";o.d(t,{useFocus:()=>s});var n=o(50959);function s(e,t){const[o,s]=(0,n.useState)(!1);(0,n.useEffect)((()=>{t&&o&&s(!1)}),[t,o]);const l={onFocus:(0,n.useCallback)((function(t){void 0!==e&&e.current!==t.target||s(!0)}),[e]),onBlur:(0,n.useCallback)((function(t){void 0!==e&&e.current!==t.target||s(!1)}),[e])};return[o,l]}},813550:(e,t,o)=>{"use strict";o.d(t,{useForceUpdate:()=>s});var n=o(50959);const s=()=>{const[,e]=(0,n.useReducer)((e=>e+1),0);return e}},525388:(e,t,o)=>{"use strict";o.d(t,{useMergedRefs:()=>l});var n=o(50959),s=o(273388);function l(e){return(0,n.useCallback)((0,s.mergeRefs)(e),e)}},778199:(e,t,o)=>{"use strict";function n(e,t,o,n,s){function l(s){if(e>s.timeStamp)return;const l=s.target;void 0!==o&&null!==t&&null!==l&&l.ownerDocument===n&&(t.contains(l)||o(s))}return s.click&&n.addEventListener("click",l,!1),s.mouseDown&&n.addEventListener("mousedown",l,!1),s.touchEnd&&n.addEventListener("touchend",l,!1),s.touchStart&&n.addEventListener("touchstart",l,!1),()=>{n.removeEventListener("click",l,!1),n.removeEventListener("mousedown",l,!1),n.removeEventListener("touchend",l,!1),n.removeEventListener("touchstart",l,!1)}}o.d(t,{addOutsideEventListener:()=>n})},92318:(e,t,o)=>{"use strict";o.d(t,{skeletonTheme:()=>s});var n=o(55679);const s=n},
800417:(e,t,o)=>{"use strict";function n(e){return l(e,r)}function s(e){return l(e,a)}function l(e,t){const o=Object.entries(e).filter(t),n={};for(const[e,t]of o)n[e]=t;return n}function r(e){const[t,o]=e;return 0===t.indexOf("data-")&&"string"==typeof o}function a(e){return 0===e[0].indexOf("aria-")}o.d(t,{filterAriaProps:()=>s,filterDataProps:()=>n,filterProps:()=>l,isAriaAttribute:()=>a,isDataAttribute:()=>r})},361701:(e,t,o)=>{"use strict";o.d(t,{CircleLogo:()=>a,hiddenCircleLogoClass:()=>r});var n=o(50959),s=o(185934),l=o(456057);const r=o.n(l)().hidden;function a(e){const t=(0,s.isCircleLogoWithUrlProps)(e),[o,l]=(0,n.useState)(0),r=(0,n.useRef)(null),a=(0,s.getStyleClasses)(e.size,o,e.className),i=e.alt??e.title??"",c=t?i[0]:e.placeholderLetter;return(0,n.useEffect)((()=>l(r.current?.complete??!t?2:1)),[t]),t&&3!==o?n.createElement("img",{ref:r,className:a,crossOrigin:"",src:e.logoUrl,alt:i,title:e.title,loading:e.loading,onLoad:()=>l(2),onError:()=>l(3),"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):n.createElement("span",{className:a,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},c)}},585938:(e,t,o)=>{"use strict";o.d(t,{useForceUpdate:()=>n.useForceUpdate});var n=o(813550)},297265:(e,t,o)=>{"use strict";o.d(t,{useWatchedValueReadonly:()=>s});var n=o(50959);const s=(e,t=!1,o=[])=>{const s="watchedValue"in e?e.watchedValue:void 0,l="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[r,a]=(0,n.useState)(s?s.value():l);return(t?n.useLayoutEffect:n.useEffect)((()=>{if(s){a(s.value());const e=e=>a(e);return s.subscribe(e),()=>s.unsubscribe(e)}return()=>{}}),[s,...o]),r}},192063:(e,t,o)=>{"use strict";o.d(t,{DEFAULT_POPUP_MENU_ITEM_THEME:()=>u,PopupMenuItem:()=>m});var n=o(50959),s=o(497754),l=o(32133),r=o(370981),a=o(361701),i=o(111706),c=o(509059);const u=c;function d(e){e.stopPropagation()}function m(e){const{id:t,role:o,className:u,title:m,labelRowClassName:p,labelClassName:v,toolboxClassName:f,shortcut:h,forceShowShortcuts:b,icon:g,iconClassname:x,isActive:w,isDisabled:k,isHovered:C,appearAsDisabled:E,label:Q,link:P,showToolboxOnHover:F,showToolboxOnFocus:M,target:O,rel:N,toolbox:L,toolboxRole:A,reference:U,onMouseOut:y,onMouseOver:S,onKeyDown:D,suppressToolboxClick:T=!0,theme:j=c,tabIndex:I,tagName:V,renderComponent:G,roundedIcon:J,iconAriaProps:R,circleLogo:q,dontClosePopup:W,onClick:_,onClickArg:B,trackEventObject:H,trackMouseWheelClick:K,trackRightClick:z,...$}=e,X=(0,n.useRef)(null),Y=(0,n.useMemo)((()=>function(e){function t(t){const{reference:o,...s}=t,l=e??(s.href?"a":"div"),r="a"===l?s:function(e){const{download:t,href:o,hrefLang:n,media:s,ping:l,rel:r,target:a,type:i,referrerPolicy:c,...u}=e;return u}(s);return n.createElement(l,{...r,ref:o})}return t.displayName=`DefaultComponent(${e})`,t}(V)),[V]),Z=G??Y;return n.createElement(Z,{...$,id:t,role:o,className:s(u,j.item,g&&j.withIcon,{[j.isActive]:w,[j.isDisabled]:k||E,[j.hovered]:C}),title:m,href:P,target:O,rel:N,reference:function(e){X.current=e,"function"==typeof U&&U(e)
;"object"==typeof U&&(U.current=e)},onClick:function(e){if(k)return;H&&(0,l.trackEvent)(H.category,H.event,H.label);_&&_(B,e);W||(e.currentTarget.dispatchEvent(new CustomEvent("popup-menu-close-event",{bubbles:!0,detail:{clickType:(0,i.isKeyboardClick)(e)?"keyboard":"mouse"}})),(0,r.globalCloseMenu)())},onContextMenu:function(e){H&&z&&(0,l.trackEvent)(H.category,H.event,`${H.label}_rightClick`)},onMouseUp:function(e){if(1===e.button&&P&&H){let e=H.label;K&&(e+="_mouseWheelClick"),(0,l.trackEvent)(H.category,H.event,e)}},onMouseOver:S,onMouseOut:y,onKeyDown:D,tabIndex:I},q&&n.createElement(a.CircleLogo,{...R,className:c["disclosure-item-circle-logo"],size:"xxxsmall",logoUrl:q.logoUrl,placeholderLetter:"placeholderLetter"in q?q.placeholderLetter:void 0}),g&&n.createElement("span",{"aria-label":R&&R["aria-label"],"aria-hidden":R&&Boolean(R["aria-hidden"]),className:s(j.icon,J&&c["round-icon"],x),dangerouslySetInnerHTML:{__html:g}}),n.createElement("span",{className:s(j.labelRow,p)},n.createElement("span",{className:s(j.label,v)},Q)),(void 0!==h||b)&&n.createElement("span",{className:j.shortcut},(ee=h)&&ee.split("+").join(" + ")),void 0!==L&&n.createElement("span",{role:A,onClick:T?d:void 0,className:s(f,j.toolbox,{[j.showOnHover]:F,[j.showOnFocus]:M})},L));var ee}},624216:(e,t,o)=>{"use strict";o.d(t,{PopupMenu:()=>m});var n=o(50959),s=o(632227),l=o(688987),r=o(8361),a=o(510618),i=o(28466);const c=n.createContext(void 0);var u=o(908783);const d=n.createContext({setMenuMaxWidth:!1});function m(e){const{controller:t,children:o,isOpened:m,closeOnClickOutside:p=!0,doNotCloseOn:v,onClickOutside:f,onClose:h,onKeyboardClose:b,"data-name":g="popup-menu-container",...x}=e,w=(0,n.useContext)(i.CloseDelegateContext),k=n.useContext(d),C=(0,n.useContext)(c),E=(0,u.useOutsideEvent)({handler:function(e){f&&f(e);if(!p)return;const t=(0,l.default)(v)?v():null==v?[]:[v];if(t.length>0&&e.target instanceof Node)for(const o of t){const t=s.findDOMNode(o);if(t instanceof Node&&t.contains(e.target))return}h()},mouseDown:!0,touchStart:!0});return m?n.createElement(r.Portal,{top:"0",left:"0",right:"0",bottom:"0",pointerEvents:"none"},n.createElement("span",{ref:E,style:{pointerEvents:"auto"}},n.createElement(a.Menu,{...x,onClose:h,onKeyboardClose:b,onScroll:function(t){const{onScroll:o}=e;o&&o(t)},customCloseDelegate:w,customRemeasureDelegate:C,ref:t,"data-name":g,limitMaxWidth:k.setMenuMaxWidth,"data-tooltip-show-on-focus":"true"},o))):null}},515783:(e,t,o)=>{"use strict";o.d(t,{ToolWidgetCaret:()=>i});var n=o(50959),s=o(497754),l=o(878112),r=o(149128),a=o(100578);function i(e){const{dropped:t,className:o}=e;return n.createElement(l.Icon,{className:s(o,r.icon,{[r.dropped]:t}),icon:a})}},438576:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},
155352:(e,t,o)=>{"use strict";o.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>a,ToolWidgetButton:()=>i});var n=o(50959),s=o(497754),l=o(878112),r=o(438576);const a=r,i=n.forwardRef(((e,t)=>{const{tag:o="div",icon:a,endIcon:i,isActive:c,isOpened:u,isDisabled:d,isGrouped:m,isHovered:p,isClicked:v,onClick:f,text:h,textBeforeIcon:b,title:g,theme:x=r,className:w,forceInteractive:k,inactive:C,"data-name":E,"data-tooltip":Q,...P}=e,F=s(w,x.button,(g||Q)&&"apply-common-tooltip",{[x.isActive]:c,[x.isOpened]:u,[x.isInteractive]:(k||Boolean(f))&&!d&&!C,[x.isDisabled]:Boolean(d||C),[x.isGrouped]:m,[x.hover]:p,[x.clicked]:v}),M=a&&("string"==typeof a?n.createElement(l.Icon,{className:x.icon,icon:a}):n.cloneElement(a,{className:s(x.icon,a.props.className)}));return"button"===o?n.createElement("button",{...P,ref:t,type:"button",className:s(F,x.accessible),disabled:d&&!C,onClick:f,title:g,"data-name":E,"data-tooltip":Q},b&&h&&n.createElement("div",{className:s("js-button-text",x.text)},h),M,!b&&h&&n.createElement("div",{className:s("js-button-text",x.text)},h)):n.createElement("div",{...P,ref:t,"data-role":"button",className:F,onClick:d?void 0:f,title:g,"data-name":E,"data-tooltip":Q},b&&h&&n.createElement("div",{className:s("js-button-text",x.text)},h),M,!b&&h&&n.createElement("div",{className:s("js-button-text",x.text)},h),i&&n.createElement(l.Icon,{icon:i,className:r.endIcon}))}))},100578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},569533:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'}}]);