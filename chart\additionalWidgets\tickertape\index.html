<!DOCTYPE html>
<html lang="en" dir="ltr">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
        <title>Ticker Tape Widget</title>
        <link rel="preconnect" href="https://s3-symbol-logo.tradingview.com/" crossorigin>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/64828.59dee6d26369b4538a0a.css" rel="stylesheet" type="text/css"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/83439.13b61a3f3b048821ed59.css" rel="stylesheet" type="text/css"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/38752.72594d7096e07bd415b3.css" rel="stylesheet" type="text/css"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/98681.47fd08d3c923c63dd4d1.css" rel="stylesheet" type="text/css"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/80259.5f40fb33e178b007bfc4.css" rel="stylesheet" type="text/css"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/3579.053cede951b9d64dea44.css" rel="stylesheet" type="text/css"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/55822.72338a326684b4a6e9b2.css" rel="stylesheet" type="text/css"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/44883.1f12e32e6d4c4b83fe10.css" rel="stylesheet" type="text/css"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/94562.0ebe36b95f80e21ab50c.css" rel="stylesheet" type="text/css"/>
        <script nonce="iYgQxM1C7oIMUoQa+uM2/g==">
            window.locale = 'en';
            window.language = 'en';
        </script>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/runtime-embed_ticker_tape_widget.923a78c5893b8e06a5ee.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/en.56316.0415d4cdd1336c3b0a45.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/en.37132.084b1a90ed1c0f0ed90b.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/68558.89ecd29cbd04297613e8.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/69022.08529a2673f0ee6c9802.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/45953.1e9975e506018dd4e3f2.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/61997.9b223907748904842eec.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/99198.b8dbef9a8bdce95bb5a8.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/63985.621169e8f49b1cabac32.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/36511.7fa685aecfbad7d20a17.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/42824.1faaa9e60d8363c1ae34.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/33786.b3416778ce2f0689e600.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/78326.15ab06fd74f73508b54c.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/84707.acc6d70696b2b2c352c1.js" rel="preload" as="script"/>
        <link crossorigin="anonymous" href="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/embed_ticker_tape_widget.669686d4b6a2c9f7d6b8.js" rel="preload" as="script"/>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/runtime-embed_ticker_tape_widget.923a78c5893b8e06a5ee.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/en.56316.0415d4cdd1336c3b0a45.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/en.37132.084b1a90ed1c0f0ed90b.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/68558.89ecd29cbd04297613e8.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/69022.08529a2673f0ee6c9802.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/45953.1e9975e506018dd4e3f2.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/61997.9b223907748904842eec.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/99198.b8dbef9a8bdce95bb5a8.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/63985.621169e8f49b1cabac32.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/36511.7fa685aecfbad7d20a17.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/42824.1faaa9e60d8363c1ae34.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/33786.b3416778ce2f0689e600.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/78326.15ab06fd74f73508b54c.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/84707.acc6d70696b2b2c352c1.js" defer></script>
        <script crossorigin="anonymous" src="http://localhost/top/chart/additionalWidgets/tickertape/static/bundles/embed_ticker_tape_widget.669686d4b6a2c9f7d6b8.js" defer></script>
        <script nonce="iYgQxM1C7oIMUoQa+uM2/g==">
            var environment = "battle";
            window.BUILD_TIME = "2025-07-25T09:00:15";
            window.WEBSOCKET_HOST = "widgetdata.tradingview.com";
            window.WEBSOCKET_HOST_FOR_RECONNECT = "widgetdata-backup.tradingview.com";
            window.user = {
                'username': 'Guest',
                'following': '0',
                'followers': '0',
                'ignore_list': {},
                'do_not_track': true
            };
        </script>
        <script nonce="iYgQxM1C7oIMUoQa+uM2/g==">
            let params = {};
            const httpReg = new RegExp('^http(s)?://');

            try {
                params = JSON.parse(decodeURIComponent(location.hash.substring(1)));
            } catch (e) {
                params = {};
            }

            window.WEBSOCKET_PARAMS_ANALYTICS = {
                'ws_ancestor_origin': window.location.ancestorOrigins && window.location.ancestorOrigins.length ? window.location.ancestorOrigins[window.location.ancestorOrigins.length - 1].replace(httpReg, '') : null,
                'ws_page_uri': params['page-uri'],
            };
            ( () => {
                "use strict";
                function e(e, t=!1) {
                    const {searchParams: n} = new URL(String(location));
                    let s = "true" === n.get("mobileapp_new")
                      , o = "true" === n.get("mobileapp");
                    if (!t) {
                        const e = function(e) {
                            const t = e + "="
                              , n = document.cookie.split(";");
                            for (let e = 0; e < n.length; e++) {
                                let s = n[e];
                                for (; " " === s.charAt(0); )
                                    s = s.substring(1, s.length);
                                if (0 === s.indexOf(t))
                                    return s.substring(t.length, s.length)
                            }
                            return null
                        }("tv_app") || "";
                        s ||= ["android", "android_nps"].includes(e),
                        o ||= "ios" === e
                    }
                    return !("new" !== e && "any" !== e || !s) || !("new" === e || !o)
                }
                const t = () => {}
                  , n = "~m~";
                class s {
                    constructor(e, t={}) {
                        this.sessionid = null,
                        this.connected = !1,
                        this._timeout = null,
                        this._base = e,
                        this._options = {
                            timeout: t.timeout || 2e4,
                            connectionType: t.connectionType
                        }
                    }
                    connect() {
                        this._socket = new WebSocket(this._prepareUrl()),
                        this._socket.onmessage = e => {
                            if ("string" != typeof e.data)
                                throw new TypeError(`The WebSocket message should be a string. Recieved ${Object.prototype.toString.call(e.data)}`);
                            this._onData(e.data)
                        }
                        ,
                        this._socket.onclose = this._onClose.bind(this),
                        this._socket.onerror = this._onError.bind(this)
                    }
                    send(e) {
                        this._socket && this._socket.send(this._encode(e))
                    }
                    disconnect() {
                        this._clearIdleTimeout(),
                        this._socket && (this._socket.onmessage = t,
                        this._socket.onclose = t,
                        this._socket.onerror = t,
                        this._socket.close())
                    }
                    _clearIdleTimeout() {
                        null !== this._timeout && (clearTimeout(this._timeout),
                        this._timeout = null)
                    }
                    _encode(e) {
                        let t, o = "";
                        const i = Array.isArray(e) ? e : [e]
                          , c = i.length;
                        for (let e = 0; e < c; e++)
                            t = null === i[e] || void 0 === i[e] ? "" : s._stringify(i[e]),
                            o += n + t.length + n + t;
                        return o
                    }
                    _decode(e) {
                        const t = [];
                        let s, o;
                        do {
                            if (e.substring(0, 3) !== n)
                                return t;
                            s = "",
                            o = "";
                            const i = (e = e.substring(3)).length;
                            for (let t = 0; t < i; t++) {
                                if (o = Number(e.substring(t, t + 1)),
                                Number(e.substring(t, t + 1)) !== o) {
                                    e = e.substring(s.length + 3),
                                    s = Number(s);
                                    break
                                }
                                s += o
                            }
                            t.push(e.substring(0, s)),
                            e = e.substring(s)
                        } while ("" !== e);
                        return t
                    }
                    _onData(e) {
                        this._setTimeout();
                        const t = this._decode(e)
                          , n = t.length;
                        for (let e = 0; e < n; e++)
                            this._onMessage(t[e])
                    }
                    _setTimeout() {
                        this._clearIdleTimeout(),
                        this._timeout = setTimeout(this._onTimeout.bind(this), this._options.timeout)
                    }
                    _onTimeout() {
                        this.disconnect(),
                        this._onDisconnect({
                            code: 4e3,
                            reason: "socket.io timeout",
                            wasClean: !1
                        })
                    }
                    _onMessage(e) {
                        this.sessionid ? this._checkIfHeartbeat(e) ? this._onHeartbeat(e.slice(3)) : this._checkIfJson(e) ? this._base.onMessage(JSON.parse(e.slice(3))) : this._base.onMessage(e) : (this.sessionid = e,
                        this._onConnect())
                    }
                    _checkIfHeartbeat(e) {
                        return this._checkMessageType(e, "h")
                    }
                    _checkIfJson(e) {
                        return this._checkMessageType(e, "j")
                    }
                    _checkMessageType(e, t) {
                        return e.substring(0, 3) === "~" + t + "~"
                    }
                    _onHeartbeat(e) {
                        this.send("~h~" + e)
                    }
                    _onConnect() {
                        this.connected = !0,
                        this._base.onConnect()
                    }
                    _onDisconnect(e) {
                        this._clear(),
                        this._base.onDisconnect(e),
                        this.sessionid = null
                    }
                    _clear() {
                        this.connected = !1
                    }
                    _prepareUrl() {
                        const t = i(this._base.host);
                        if (t.pathname += "socket.io/websocket",
                        t.protocol = "wss:",
                        t.searchParams.append("from", window.location.pathname.slice(1, 50)),
                        t.searchParams.append("date", window.BUILD_TIME || ""),
                        e("any") && t.searchParams.append("client", "mobile"),
                        this._options.connectionType && t.searchParams.append("type", this._options.connectionType),
                        window.WEBSOCKET_PARAMS_ANALYTICS) {
                            const {ws_page_uri: e, ws_ancestor_origin: n} = window.WEBSOCKET_PARAMS_ANALYTICS;
                            e && t.searchParams.append("page-uri", e),
                            n && t.searchParams.append("ancestor-origin", n)
                        }
                        return t.href
                    }
                    _onClose(e) {
                        this._clearIdleTimeout(),
                        this._onDisconnect(e)
                    }
                    _onError(e) {
                        this._clearIdleTimeout(),
                        this._clear(),
                        this._base.emit("error", [e]),
                        this.sessionid = null
                    }
                    static _stringify(e) {
                        return "[object Object]" === Object.prototype.toString.call(e) ? "~j~" + JSON.stringify(e) : String(e)
                    }
                }
                class o {
                    constructor(e, t) {
                        this.host = e,
                        this._connecting = !1,
                        this._events = {},
                        this.transport = this._getTransport(t)
                    }
                    isConnected() {
                        return !!this.transport && this.transport.connected
                    }
                    isConnecting() {
                        return this._connecting
                    }
                    connect() {
                        this.isConnected() || (this._connecting && this.disconnect(),
                        this._connecting = !0,
                        this.transport.connect())
                    }
                    send(e) {
                        this.transport && this.transport.connected && this.transport.send(e)
                    }
                    disconnect() {
                        this.transport && this.transport.disconnect()
                    }
                    on(e, t) {
                        e in this._events || (this._events[e] = []),
                        this._events[e].push(t)
                    }
                    offAll() {
                        this._events = {}
                    }
                    onMessage(e) {
                        this.emit("message", [e])
                    }
                    emit(e, t=[]) {
                        if (e in this._events) {
                            const n = this._events[e].concat()
                              , s = n.length;
                            for (let e = 0; e < s; e++)
                                n[e].apply(this, t)
                        }
                    }
                    onConnect() {
                        this.clear(),
                        this.emit("connect")
                    }
                    onDisconnect(e) {
                        this.emit("disconnect", [e])
                    }
                    clear() {
                        this._connecting = !1
                    }
                    _getTransport(e) {
                        return new s(this,e)
                    }
                }
                function i(e) {
                    const t = -1 !== e.indexOf("/") ? new URL(e) : new URL("wss://" + e);
                    if ("wss:" !== t.protocol && "https:" !== t.protocol)
                        throw new Error("Invalid websocket base " + e);
                    return t.pathname.endsWith("/") || (t.pathname += "/"),
                    t.search = "",
                    t.username = "",
                    t.password = "",
                    t
                }
                const c = "undefined" != typeof window && Number(window.TELEMETRY_WS_ERROR_LOGS_THRESHOLD) || 0;
                class r {
                    constructor(e, t={}) {
                        this._queueStack = [],
                        this._logsQueue = [],
                        this._telemetryObjectsQueue = [],
                        this._reconnectCount = 0,
                        this._redirectCount = 0,
                        this._errorsCount = 0,
                        this._errorsInfoSent = !1,
                        this._connectionStart = null,
                        this._connectionEstablished = null,
                        this._reconnectTimeout = null,
                        this._onlineCancellationToken = null,
                        this._isConnectionForbidden = !1,
                        this._initialHost = t.initialHost || null,
                        this._suggestedHost = e,
                        this._proHost = t.proHost,
                        this._reconnectHost = t.reconnectHost,
                        this._noReconnectAfterTimeout = !0 === t.noReconnectAfterTimeout,
                        this._dataRequestTimeout = t.dataRequestTimeout,
                        this._connectionType = t.connectionType,
                        this._doConnect(),
                        t.pingRequired && -1 === window.location.search.indexOf("noping") && this._startPing()
                    }
                    connect() {
                        this._tryConnect()
                    }
                    resetCounters() {
                        this._reconnectCount = 0,
                        this._redirectCount = 0
                    }
                    setLogger(e, t) {
                        this._logger = e,
                        this._getLogHistory = t,
                        this._flushLogs()
                    }
                    setTelemetry(e) {
                        this._telemetry = e,
                        this._telemetry.reportSent.subscribe(this, this._onTelemetrySent),
                        this._flushTelemetry()
                    }
                    onReconnect(e) {
                        this._onReconnect = e
                    }
                    isConnected() {
                        return !!this._socket && this._socket.isConnected()
                    }
                    isConnecting() {
                        return !!this._socket && this._socket.isConnecting()
                    }
                    on(e, t) {
                        return !!this._socket && ("connect" === e && this._socket.isConnected() ? t() : "disconnect" === e ? this._disconnectCallbacks.push(t) : this._socket.on(e, t),
                        !0)
                    }
                    getSessionId() {
                        return this._socket && this._socket.transport ? this._socket.transport.sessionid : null
                    }
                    send(e) {
                        return this.isConnected() ? (this._socket.send(e),
                        !0) : (this._queueMessage(e),
                        !1)
                    }
                    getConnectionEstablished() {
                        return this._connectionEstablished
                    }
                    getHost() {
                        const e = this._tryGetProHost();
                        return null !== e ? e : this._reconnectHost && this._reconnectCount > 3 ? this._reconnectHost : this._suggestedHost
                    }
                    getReconnectCount() {
                        return this._reconnectCount
                    }
                    getRedirectCount() {
                        return this._redirectCount
                    }
                    getConnectionStart() {
                        return this._connectionStart
                    }
                    disconnect() {
                        this._clearReconnectTimeout(),
                        (this.isConnected() || this.isConnecting()) && (this._propagateDisconnect(),
                        this._disconnectCallbacks = [],
                        this._closeSocket())
                    }
                    forbidConnection() {
                        this._isConnectionForbidden = !0,
                        this.disconnect()
                    }
                    allowConnection() {
                        this._isConnectionForbidden = !1,
                        this.connect()
                    }
                    isMaxRedirects() {
                        return this._redirectCount >= 20
                    }
                    isMaxReconnects() {
                        return this._reconnectCount >= 20
                    }
                    getPingInfo() {
                        return this._pingInfo || null
                    }
                    _tryGetProHost() {
                        return window.TradingView && window.TradingView.onChartPage && "battle" === window.environment && !this._redirectCount && -1 === window.location.href.indexOf("ws_host") ? this._initialHost ? this._initialHost : void 0 !== window.user && window.user.pro_plan ? this._proHost || this._suggestedHost : null : null
                    }
                    _queueMessage(e) {
                        0 === this._queueStack.length && this._logMessage(0, "Socket is not connected. Queued a message"),
                        this._queueStack.push(e)
                    }
                    _processMessageQueue() {
                        0 !== this._queueStack.length && (this._logMessage(0, "Processing queued messages"),
                        this._queueStack.forEach(this.send.bind(this)),
                        this._logMessage(0, "Processed " + this._queueStack.length + " messages"),
                        this._queueStack = [])
                    }
                    _onDisconnect(e) {
                        this._noReconnectAfterTimeout || null !== this._reconnectTimeout || (this._reconnectTimeout = setTimeout(this._tryReconnect.bind(this), 5e3)),
                        this._clearOnlineCancellationToken();
                        let t = "disconnect session:" + this.getSessionId();
                        e && (t += ", code:" + e.code + ", reason:" + e.reason,
                        1005 === e.code && this._sendTelemetry("websocket_code_1005")),
                        this._logMessage(0, t),
                        this._propagateDisconnect(e),
                        this._closeSocket(),
                        this._queueStack = []
                    }
                    _closeSocket() {
                        null !== this._socket && (this._socket.offAll(),
                        this._socket.disconnect(),
                        this._socket = null)
                    }
                    _logMessage(e, t) {
                        const n = {
                            method: e,
                            message: t
                        };
                        this._logger ? this._flushLogMessage(n) : (n.message = `[${(new Date).toISOString()}] ${n.message}`,
                        this._logsQueue.push(n))
                    }
                    _flushLogMessage(e) {
                        switch (e.method) {
                        case 2:
                            this._logger.logDebug(e.message);
                            break;
                        case 3:
                            this._logger.logError(e.message);
                            break;
                        case 0:
                            this._logger.logInfo(e.message);
                            break;
                        case 1:
                            this._logger.logNormal(e.message)
                        }
                    }
                    _flushLogs() {
                        this._flushLogMessage({
                            method: 1,
                            message: "messages from queue. Start."
                        }),
                        this._logsQueue.forEach((e => {
                            this._flushLogMessage(e)
                        }
                        )),
                        this._flushLogMessage({
                            method: 1,
                            message: "messages from queue. End."
                        }),
                        this._logsQueue = []
                    }
                    _sendTelemetry(e, t) {
                        const n = {
                            event: e,
                            params: t
                        };
                        this._telemetry ? this._flushTelemetryObject(n) : this._telemetryObjectsQueue.push(n)
                    }
                    _flushTelemetryObject(e) {
                        this._telemetry.sendChartReport(e.event, e.params, !1)
                    }
                    _flushTelemetry() {
                        this._telemetryObjectsQueue.forEach((e => {
                            this._flushTelemetryObject(e)
                        }
                        )),
                        this._telemetryObjectsQueue = []
                    }
                    _doConnect() {
                        this._socket && (this._socket.isConnected() || this._socket.isConnecting()) || (this._clearOnlineCancellationToken(),
                        this._host = this.getHost(),
                        this._socket = new o(this._host,{
                            timeout: this._dataRequestTimeout,
                            connectionType: this._connectionType
                        }),
                        this._logMessage(0, "Connecting to " + this._host),
                        this._bindEvents(),
                        this._disconnectCallbacks = [],
                        this._connectionStart = performance.now(),
                        this._connectionEstablished = null,
                        this._socket.connect(),
                        performance.mark("SWSC", {
                            detail: "Start WebSocket connection"
                        }),
                        this._socket.on("connect", ( () => {
                            performance.mark("EWSC", {
                                detail: "End WebSocket connection"
                            }),
                            performance.measure("WebSocket connection delay", "SWSC", "EWSC")
                        }
                        )))
                    }
                    _propagateDisconnect(e) {
                        const t = this._disconnectCallbacks.length;
                        for (let n = 0; n < t; n++)
                            this._disconnectCallbacks[n](e || {})
                    }
                    _bindEvents() {
                        this._socket && (this._socket.on("connect", ( () => {
                            const e = this.getSessionId();
                            if ("string" == typeof e) {
                                const t = JSON.parse(e);
                                if (t.redirect)
                                    return this._redirectCount += 1,
                                    this._suggestedHost = t.redirect,
                                    this.isMaxRedirects() && this._sendTelemetry("redirect_bailout"),
                                    void this._redirect()
                            }
                            this._connectionEstablished = performance.now(),
                            this._processMessageQueue(),
                            this._logMessage(0, "connect session:" + e)
                        }
                        )),
                        this._socket.on("disconnect", this._onDisconnect.bind(this)),
                        this._socket.on("close", this._onDisconnect.bind(this)),
                        this._socket.on("error", (e => {
                            this._logMessage(0, new Date + " session:" + this.getSessionId() + " websocket error:" + JSON.stringify(e)),
                            this._sendTelemetry("websocket_error"),
                            this._errorsCount++,
                            !this._errorsInfoSent && this._errorsCount >= c && (void 0 !== this._lastConnectCallStack && (this._sendTelemetry("websocket_error_connect_stack", {
                                text: this._lastConnectCallStack
                            }),
                            delete this._lastConnectCallStack),
                            void 0 !== this._getLogHistory && this._sendTelemetry("websocket_error_log", {
                                text: this._getLogHistory(50).join("\n")
                            }),
                            this._errorsInfoSent = !0)
                        }
                        )))
                    }
                    _redirect() {
                        this.disconnect(),
                        this._reconnectWhenOnline()
                    }
                    _tryReconnect() {
                        this._tryConnect() && (this._reconnectCount += 1)
                    }
                    _tryConnect() {
                        return !this._isConnectionForbidden && (this._clearReconnectTimeout(),
                        this._lastConnectCallStack = new Error(`WebSocket connect stack. Is connected: ${this.isConnected()}.`).stack || "",
                        !this.isConnected() && (this.disconnect(),
                        this._reconnectWhenOnline(),
                        !0))
                    }
                    _clearOnlineCancellationToken() {
                        this._onlineCancellationToken && (this._onlineCancellationToken(),
                        this._onlineCancellationToken = null)
                    }
                    _clearReconnectTimeout() {
                        null !== this._reconnectTimeout && (clearTimeout(this._reconnectTimeout),
                        this._reconnectTimeout = null)
                    }
                    _reconnectWhenOnline() {
                        if (navigator.onLine)
                            return this._logMessage(0, "Network status: online - trying to connect"),
                            this._doConnect(),
                            void (this._onReconnect && this._onReconnect());
                        this._logMessage(0, "Network status: offline - wait until online"),
                        this._onlineCancellationToken = function(e) {
                            let t = e;
                            const n = () => {
                                window.removeEventListener("online", n),
                                t && t()
                            }
                            ;
                            return window.addEventListener("online", n),
                            () => {
                                t = null
                            }
                        }(( () => {
                            this._logMessage(0, "Network status changed to online - trying to connect"),
                            this._doConnect(),
                            this._onReconnect && this._onReconnect()
                        }
                        ))
                    }
                    _onTelemetrySent(e) {
                        "websocket_error"in e && (this._errorsCount = 0,
                        this._errorsInfoSent = !1)
                    }
                    _startPing() {
                        if (this._pingIntervalId)
                            return;
                        const e = i(this.getHost());
                        e.pathname += "ping",
                        e.protocol = "https:";
                        let t = 0
                          , n = 0;
                        const s = e => {
                            this._pingInfo = this._pingInfo || {
                                max: 0,
                                min: 1 / 0,
                                avg: 0
                            };
                            const s = (new Date).getTime() - e;
                            s > this._pingInfo.max && (this._pingInfo.max = s),
                            s < this._pingInfo.min && (this._pingInfo.min = s),
                            t += s,
                            n++,
                            this._pingInfo.avg = t / n,
                            n >= 10 && this._pingIntervalId && (clearInterval(this._pingIntervalId),
                            delete this._pingIntervalId)
                        }
                        ;
                        this._pingIntervalId = setInterval(( () => {
                            const t = (new Date).getTime()
                              , n = new XMLHttpRequest;
                            n.open("GET", e, !0),
                            n.send(),
                            n.onreadystatechange = () => {
                                n.readyState === XMLHttpRequest.DONE && 200 === n.status && s(t)
                            }
                        }
                        ), 1e4)
                    }
                }
                window.WSBackendConnection = new r(window.WEBSOCKET_HOST,{
                    pingRequired: window.WS_HOST_PING_REQUIRED,
                    proHost: window.WEBSOCKET_PRO_HOST,
                    reconnectHost: window.WEBSOCKET_HOST_FOR_RECONNECT,
                    initialHost: window.WEBSOCKET_INITIAL_HOST,
                    connectionType: window.WEBSOCKET_CONNECTION_TYPE
                }),
                window.WSBackendConnectionCtor = r
            }
            )();
        </script>
        <script nonce="iYgQxM1C7oIMUoQa+uM2/g==">
            window.featureToggleState = {
                "black_friday_popup": 1.0,
                "black_friday_mainpage": 1.0,
                "black_friday_extend_limitation": 1.0,
                "chart_autosave_30min": 1.0,
                "remove_symbol_related_items_from_bg_menu": 1.0,
                "performance_test_mode": 1.0,
                "chart_storage_hibernation_delay_60min": 1.0,
                "multichart_replay": 1.0,
                "log_replay_to_persistent_logger": 1.0,
                "enable_select_date_replay_mobile": 1.0,
                "support_persistent_logs": 1.0,
                "activate_selected_strategy": 1.0,
                "fundamental_graphs": 0.1,
                "shift_chart_only_after_right_margin": 1.0,
                "long_press_floating_tooltip": 1.0,
                "adjust_drawings_on_splits": 0.1,
                "watermark_in_replay": 1.0,
                "heikin_ashi_supported_for_replay": 1.0,
                "enable_step_by_step_hints_for_drawings": 1.0,
                "enable_new_indicators_templates_view": 1.0,
                "allow_brackets_profit_loss": 1.0,
                "enable_traded_context_linking": 1.0,
                "order_context_validation_in_instant_mode": 1.0,
                "internal_fullscreen_api": 1.0,
                "alerts-update-collections-on-reconnection": 1.0,
                "alerts-use-drawing-text-in-alert-message": 1.0,
                "enable_symbol_change_restriction_on_widgets": 1.0,
                "enable_asx_symbol_restriction": 1.0,
                "symphony_allow_non_partitioned_cookie_on_old_symphony": 1.0,
                "symphony_notification_badges": 1.0,
                "symphony_use_adk_for_upgrade_request": 1.0,
                "telegram_mini_app_reduce_snapshot_quality": 1.0,
                "news_enable_filtering_by_user": 1.0,
                "timeout_django_db": 0.15,
                "timeout_django_usersettings_db": 0.15,
                "timeout_django_charts_db": 0.25,
                "timeout_django_symbols_lists_db": 0.25,
                "timeout_django_minds_db": 0.05,
                "timeout_django_logging_db": 0.25,
                "disable_services_monitor_metrics": 1.0,
                "enable_signin_recaptcha": 1.0,
                "enable_idea_spamdetectorml": 1.0,
                "enable_idea_spamdetectorml_comments": 1.0,
                "enable_spamdetectorml_chat": 1.0,
                "enable_minds_spamdetectorml": 1.0,
                "backend-connections-in-token": 1.0,
                "one_connection_with_exchanges": 1.0,
                "mobile-ads-ios": 1.0,
                "mobile-ads-android": 1.0,
                "google-one-tap-signin": 1.0,
                "braintree-gopro-in-order-dialog": 1.0,
                "braintree-apple-pay": 1.0,
                "braintree-google-pay": 1.0,
                "braintree-apple-pay-trial": 1.0,
                "triplea-payments": 1.0,
                "braintree-google-pay-trial": 1.0,
                "braintree-one-usd-verification": 1.0,
                "braintree-3ds-enabled": 1.0,
                "braintree-3ds-status-check": 1.0,
                "braintree-additional-card-checks-for-trial": 1.0,
                "braintree-instant-settlement": 1.0,
                "checkout_black_friday_downgrade": 1.0,
                "checkout-subscriptions": 1.0,
                "checkout_additional_card_checks_for_trial": 1.0,
                "checkout_fail_on_duplicate_payment_methods_for_trial": 1.0,
                "checkout_fail_on_banned_countries_for_trial": 1.0,
                "checkout_fail_on_banned_bank_for_trial": 1.0,
                "checkout_fail_on_banned_bank_for_premium_trial": 1.0,
                "checkout_fail_on_close_to_expiration_card_trial": 1.0,
                "checkout_fail_on_prepaid_card_trial": 1.0,
                "checkout_fail_on_commercial_card_trial": 1.0,
                "checkout_show_instead_of_braintree": 1.0,
                "checkout-3ds": 1.0,
                "checkout-3ds-us": 1.0,
                "checkout-paypal": 1.0,
                "checkout_include_account_holder": 1.0,
                "checkout-paypal-trial": 1.0,
                "annual_to_monthly_downgrade_attempt": 1.0,
                "razorpay_trial_10_inr": 1.0,
                "razorpay_black_friday_downgrade": 1.0,
                "razorpay-card-order-token-section": 1.0,
                "razorpay-card-subscriptions": 1.0,
                "razorpay-upi-subscriptions": 1.0,
                "razorpay-s3-invoice-upload": 1.0,
                "dlocal-payments": 1.0,
                "braintree_transaction_source": 1.0,
                "braintree_commercial_card_check": 1.0,
                "vertex-tax": 1.0,
                "receipt_in_emails": 1.0,
                "adwords-analytics": 1.0,
                "disable_mobile_upsell_ios": 1.0,
                "disable_mobile_upsell_android": 1.0,
                "minds_widget_enabled": 1.0,
                "minds_on_symbol_page_enabled": 1.0,
                "required_agreement_for_rt": 1.0,
                "check_market_data_limits": 1.0,
                "force_to_complete_data": 1.0,
                "force_to_upgrade_to_expert": 1.0,
                "send_tradevan_invoice": 1.0,
                "show_pepe_animation": 1.0,
                "screener-alerts-read-only": 1.0,
                "screener_send_touch_request": 1.0,
                "screener_standalone_load_recently_used_screen": 1.0,
                "screener_widgetbar_load_recently_used_screen": 1.0,
                "ses_tracking": 1.0,
                "send_financial_notifications": 1.0,
                "show_new_broker_header": 1.0,
                "active_auto_scroll_table_on_hover_cart_yield": 1.0,
                "spark_translations": 1.0,
                "spark_category_translations": 1.0,
                "spark_tags_translations": 1.0,
                "pro_plan_initial_refunds_disabled": 1.0,
                "enable_ideas_recommendations": 1.0,
                "enable_ideas_recommendations_feed": 1.0,
                "fail_on_duplicate_payment_methods_for_trial": 1.0,
                "ethoca_alert_notification_webhook": 1.0,
                "hide_suspicious_users_ideas": 1.0,
                "disable_publish_strategy_range_based_chart": 1.0,
                "restrict_simultaneous_requests": 1.0,
                "login_from_new_device_email": 1.0,
                "ssr_worker_nowait": 1.0,
                "broker_A1CAPITAL": 1.0,
                "broker_ACTIVTRADES": 1.0,
                "static_dom": 1.0,
                "hide_ALCHEMYMARKETS_on_ios": 1.0,
                "hide_ALCHEMYMARKETS_on_android": 1.0,
                "hide_ALCHEMYMARKETS_on_mobile_web": 1.0,
                "ALCHEMYMARKETS_beta": 1.0,
                "broker_ALICEBLUE": 1.0,
                "broker_ALOR": 1.0,
                "broker_ALPACA": 1.0,
                "broker_AMP": 1.0,
                "hide_ANGELONE_on_ios": 1.0,
                "hide_ANGELONE_on_android": 1.0,
                "hide_ANGELONE_on_mobile_web": 1.0,
                "ANGELONE_beta": 1.0,
                "broker_AVA_FUTURES": 1.0,
                "AVA_FUTURES_oauth_authorization": 1.0,
                "AVA_FUTURES_beta": 1.0,
                "hide_BAJAJ_on_ios": 1.0,
                "hide_BAJAJ_on_android": 1.0,
                "hide_BAJAJ_on_mobile_web": 1.0,
                "BAJAJ_beta": 1.0,
                "broker_BEYOND": 1.0,
                "broker_BINANCE": 1.0,
                "enable_binanceapis_base_url": 1.0,
                "broker_BINGBON": 1.0,
                "broker_BITAZZA": 1.0,
                "broker_BITGET": 1.0,
                "broker_BITMEX": 1.0,
                "broker_BITSTAMP": 1.0,
                "broker_BLACKBULL": 1.0,
                "broker_BLUEBERRYMARKETS": 1.0,
                "broker_BYBIT": 1.0,
                "broker_CAPITALCOM": 1.0,
                "broker_CFI": 1.0,
                "broker_CITYINDEX": 1.0,
                "cityindex_spreadbetting": 1.0,
                "broker_CMCMARKETS": 1.0,
                "broker_COBRATRADING": 1.0,
                "broker_COINBASE": 1.0,
                "coinbase_request_server_logger": 1.0,
                "coinbase_cancel_position_brackets": 1.0,
                "hide_COINW_on_ios": 1.0,
                "hide_COINW_on_android": 1.0,
                "hide_COINW_on_mobile_web": 1.0,
                "COINW_beta": 1.0,
                "broker_COLMEX": 1.0,
                "hide_CQG_on_ios": 1.0,
                "hide_CQG_on_android": 1.0,
                "hide_CQG_on_mobile_web": 1.0,
                "cqg-realtime-bandwidth-limit": 1.0,
                "hide_CURRENCYCOM_on_ios": 1.0,
                "hide_CURRENCYCOM_on_android": 1.0,
                "hide_CURRENCYCOM_on_mobile_web": 1.0,
                "broker_DERAYAH": 1.0,
                "broker_DHAN": 1.0,
                "broker_DNSE": 1.0,
                "broker_DORMAN": 1.0,
                "hide_DUMMY_on_ios": 1.0,
                "hide_DUMMY_on_android": 1.0,
                "hide_DUMMY_on_mobile_web": 1.0,
                "broker_EASYMARKETS": 1.0,
                "broker_EDGECLEAR": 1.0,
                "edgeclear_oauth_authorization": 1.0,
                "broker_EIGHTCAP": 1.0,
                "broker_ERRANTE": 1.0,
                "broker_ESAFX": 1.0,
                "hide_FIDELITY_on_ios": 1.0,
                "hide_FIDELITY_on_android": 1.0,
                "hide_FIDELITY_on_mobile_web": 1.0,
                "FIDELITY_beta": 1.0,
                "broker_FOREXCOM": 1.0,
                "forexcom_session_v2": 1.0,
                "broker_FPMARKETS": 1.0,
                "hide_FTX_on_ios": 1.0,
                "hide_FTX_on_android": 1.0,
                "hide_FTX_on_mobile_web": 1.0,
                "ftx_request_server_logger": 1.0,
                "broker_FUSIONMARKETS": 1.0,
                "fxcm_server_logger": 1.0,
                "broker_FXCM": 1.0,
                "broker_FXOPEN": 1.0,
                "broker_FYERS": 1.0,
                "broker_GBEBROKERS": 1.0,
                "broker_GEMINI": 1.0,
                "hide_GLOBALPRIME_on_ios": 1.0,
                "hide_GLOBALPRIME_on_android": 1.0,
                "hide_GLOBALPRIME_on_mobile_web": 1.0,
                "globalprime-brokers-side-maintenance": 1.0,
                "broker_GOMARKETS": 1.0,
                "broker_GOTRADE": 1.0,
                "broker_HERENYA": 1.0,
                "broker_HTX": 1.0,
                "broker_IBKR": 1.0,
                "check_ibkr_side_maintenance": 1.0,
                "ibkr_request_server_logger": 1.0,
                "ibkr_parallel_provider_initialization": 1.0,
                "ibkr_ws_account_summary": 1.0,
                "ibkr_ws_server_logger": 1.0,
                "ibkr_subscribe_to_order_updates_first": 1.0,
                "ibkr_ws_account_ledger": 1.0,
                "broker_IBROKER": 1.0,
                "broker_ICMARKETS": 1.0,
                "broker_IG": 1.0,
                "broker_INFOYATIRIM": 1.0,
                "broker_INNOVESTX": 1.0,
                "broker_INTERACTIVEIL": 1.0,
                "broker_IRONBEAM": 1.0,
                "hide_IRONBEAM_CQG_on_ios": 1.0,
                "hide_IRONBEAM_CQG_on_android": 1.0,
                "hide_IRONBEAM_CQG_on_mobile_web": 1.0,
                "hide_KSECURITIES_on_ios": 1.0,
                "hide_KSECURITIES_on_android": 1.0,
                "hide_KSECURITIES_on_mobile_web": 1.0,
                "KSECURITIES_beta": 1.0,
                "broker_LIBERATOR": 1.0,
                "broker_MARKETSCOM": 1.0,
                "broker_MEXEM": 1.0,
                "broker_MIDAS": 1.0,
                "hide_MOCKBROKER_on_ios": 1.0,
                "hide_MOCKBROKER_on_android": 1.0,
                "hide_MOCKBROKER_on_mobile_web": 1.0,
                "broker_MOOMOO": 1.0,
                "broker_MOTILALOSWAL": 1.0,
                "broker_NINJATRADER": 1.0,
                "broker_OANDA": 1.0,
                "oanda_rest_api": 1.0,
                "oanda_rest_server_logging": 1.0,
                "launch-oanda-country-group-1": 1.0,
                "launch-oanda-country-group-2": 1.0,
                "launch-oanda-country-group-3": 1.0,
                "launch-oanda-country-group-4": 1.0,
                "oanda_oauth_multiplexing": 1.0,
                "broker_OKX": 1.0,
                "broker_OPTIMUS": 1.0,
                "broker_OSMANLI": 1.0,
                "paper_force_connect_pushstream": 1.0,
                "paper_subaccount_custom_currency": 1.0,
                "paper_outside_rth": 1.0,
                "broker_PEPPERSTONE": 1.0,
                "broker_PAYTM": 1.0,
                "broker_PHEMEX": 1.0,
                "broker_PHILLIPCAPITAL_TR": 1.0,
                "broker_PHILLIPNOVA": 1.0,
                "broker_PLUS500": 1.0,
                "plus500_oauth_authorization": 1.0,
                "broker_QUESTRADE": 1.0,
                "hide_RIYADCAPITAL_on_ios": 1.0,
                "hide_RIYADCAPITAL_on_android": 1.0,
                "hide_RIYADCAPITAL_on_mobile_web": 1.0,
                "RIYADCAPITAL_beta": 1.0,
                "broker_ROBOMARKETS": 1.0,
                "broker_SAMUEL": 1.0,
                "broker_SAXOBANK": 1.0,
                "hide_SKILLING_on_ios": 1.0,
                "hide_SKILLING_on_android": 1.0,
                "hide_SKILLING_on_mobile_web": 1.0,
                "skilling-brokers-side-maintenance": 1.0,
                "broker_SPREADEX": 1.0,
                "broker_SWISSQUOTE": 1.0,
                "broker_STONEX": 1.0,
                "broker_TASTYFX": 1.0,
                "broker_TASTYTRADE": 1.0,
                "broker_THINKMARKETS": 1.0,
                "broker_TICKMILL": 1.0,
                "hide_TIGER_on_ios": 1.0,
                "hide_TIGER_on_android": 1.0,
                "hide_TIGER_on_mobile_web": 1.0,
                "broker_TOKENIZE": 1.0,
                "broker_TRADENATION": 1.0,
                "hide_TRADESMART_on_ios": 1.0,
                "hide_TRADESMART_on_android": 1.0,
                "hide_TRADESMART_on_mobile_web": 1.0,
                "TRADESMART_beta": 1.0,
                "broker_TRADESTATION": 1.0,
                "tradestation_request_server_logger": 1.0,
                "tradestation_account_data_streaming": 1.0,
                "broker_TRADEZERO": 1.0,
                "broker_TRADIER": 1.0,
                "broker_TRADIER_FUTURES": 1.0,
                "tradier_futures_oauth_authorization": 1.0,
                "broker_TRADOVATE": 1.0,
                "tradu_spread_bet": 1.0,
                "broker_TRADU": 1.0,
                "broker_VANTAGE": 1.0,
                "broker_VELOCITY": 1.0,
                "broker_WEBULL": 1.0,
                "broker_WEBULLJAPAN": 1.0,
                "broker_WEBULLPAY": 1.0,
                "hide_WEBULLPAY_on_ios": 1.0,
                "hide_WEBULLPAY_on_android": 1.0,
                "hide_WEBULLPAY_on_mobile_web": 1.0,
                "broker_WEBULLUK": 1.0,
                "broker_WHITEBIT": 1.0,
                "broker_WHSELFINVEST": 1.0,
                "broker_WHSELFINVEST_FUTURES": 1.0,
                "WHSELFINVEST_FUTURES_oauth_authorization": 1.0,
                "broker_XCUBE": 1.0,
                "broker_YLG": 1.0,
                "broker_id_session": 1.0,
                "disallow_concurrent_sessions": 1.0,
                "mobile_trading_web": 1.0,
                "mobile_trading_ios": 1.0,
                "mobile_trading_android": 1.0,
                "continuous_front_contract_trading": 1.0,
                "trading_request_server_logger": 1.0,
                "rest_request_server_logger": 1.0,
                "oauth2_code_flow_provider_server_logger": 1.0,
                "rest_logout_on_429": 1.0,
                "review_popup_on_chart": 1.0,
                "show_concurrent_connection_warning": 1.0,
                "enable_trading_server_logger": 1.0,
                "order_presets": 1.0,
                "order_ticket_resizable_drawer_on": 1.0,
                "rest_use_async_mapper": 1.0,
                "paper_competition_leaderboard": 1.0,
                "paper_competition_link_community": 1.0,
                "paper_competition_landing": 1.0,
                "paper_competition_leaderboard_user_stats": 1.0,
                "paper_competition_previous_competitions": 1.0,
                "amp_oauth_authorization": 1.0,
                "blueline_oauth_authorization": 1.0,
                "dorman_oauth_authorization": 1.0,
                "ironbeam_oauth_authorization": 1.0,
                "optimus_oauth_authorization": 1.0,
                "stonex_oauth_authorization": 1.0,
                "ylg_oauth_authorization": 1.0,
                "trading_general_events_ga_tracking": 1.0,
                "replay_result_sharing": 1.0,
                "replay_trading_brackets": 1.0,
                "hide_all_brokers_button_in_ios_app": 1.0,
                "force_max_allowed_pulling_intervals": 1.0,
                "paper_delay_trading": 1.0,
                "enable_first_touch_is_selection": 1.0,
                "enable_order_moving_by_price_line": 1.0,
                "renew_token_preemption_30": 1.0,
                "do_not_open_ot_from_plus_button": 1.0,
                "broker_side_promotion": 1.0,
                "enable_new_trading_menu_structure": 1.0,
                "paper_order_confirmation_dialog": 0.5,
                "paper_place_order_preview": 0.5,
                "enable_symbols_popularity_showing": 1.0,
                "enable_translations_s3_upload": 1.0,
                "etf_fund_flows_only_days_resolutions": 1.0,
                "disable_snowplow_platform_events": 1.0,
                "notify_idea_mods_about_first_publication": 1.0,
                "enable_waf_tracking": 1.0,
                "backtesting_report": 1.0,
                "new_errors_flow": 1.0,
                "new_backtesting_chart": 0.4,
                "hide_save_indicator": 1.0,
                "symbol_search_country_sources": 1.0,
                "symbol_search_bond_type_filter": 1.0,
                "watchlists_dialog_scroll_to_active": 1.0,
                "bottom_panel_track_events": 1.0,
                "snowplow_beacon_feature": 1.0,
                "show_data_problems_in_help_center": 1.0,
                "enable_apple_device_check": 1.0,
                "enable_apple_promo_signature": 1.0,
                "should_charge_full_price_on_upgrade_if_google_payment": 1.0,
                "update_availability_for_std": 1.0,
                "enable_push_notifications_android": 1.0,
                "enable_push_notifications_ios": 1.0,
                "enable_manticore_cluster": 1.0,
                "enable_envoy_proxy": 1.0,
                "enable_envoy_proxy_papertrading": 1.0,
                "enable_envoy_proxy_screener": 1.0,
                "hide_ca_us_isin": 1.0,
                "options_reduce_polling_interval": 1.0,
                "options_chain_use_quote_session": 1.0,
                "show_toast_about_unread_message": 1.0,
                "enable_partner_payout": 1.0,
                "enable_email_change_logging": 1.0,
                "set_limit_to_1000_for_colored_lists": 1.0,
                "enable_email_on_partner_status_change": 1.0,
                "enable_partner_program_apply": 1.0,
                "enable_partner_program": 1.0,
                "compress_cache_data": 1.0,
                "news_enable_streaming": 1.0,
                "news_screener_page_client": 1.0,
                "enable_declaration_popup_on_load": 1.0,
                "move_ideas_and_minds_into_news": 1.0,
                "hide_data_window_tab": 1.0,
                "show_futures_front_contract": 1.0,
                "enable_prof_popup_free": 1.0,
                "ios_app_news_and_minds": 1.0,
                "increase_metainfo_version": 1.0,
                "do_not_disclose_phone_occupancy": 1.0,
                "enable_redirect_to_widget_documentation_of_any_localization": 1.0,
                "news_enable_streaming_hibernation": 1.0,
                "news_streaming_hibernation_delay_10min": 1.0,
                "pass_recovery_search_hide_info": 1.0,
                "news_enable_streaming_on_screener": 1.0,
                "enable_forced_email_confirmation": 1.0,
                "enable_support_assistant": 1.0,
                "show_gift_button": 1.0,
                "address_validation_enabled": 1.0,
                "generate_invoice_number_by_country": 1.0,
                "show_summary_over_the_chart": 1.0,
                "show_favorite_layouts": 1.0,
                "enable_social_auth_confirmation": 1.0,
                "enable_ad_block_detect": 1.0,
                "hide_public_chats_for_new_users": 1.0,
                "tvd_last_tab_close_button": 1.0,
                "enable_validation_for_prohibited_scripts": 1.0,
                "hide_embed_this_chart": 1.0,
                "enable_new_settings": 1.0,
                "move_watchlist_actions": 1.0,
                "enable_lingua_lang_check": 1.0,
                "get_saved_active_list_before_getting_all_lists": 1.0,
                "enable_alternative_twitter_api": 1.0,
                "show_referral_notification_dialog": 0.5,
                "set_new_black_color": 1.0,
                "use_symbol_search_options_exchanges_list": 1.0,
                "hide_right_toolbar_button": 1.0,
                "news_use_news_mediator": 1.0,
                "allow_trailing_whitespace_in_number_token": 1.0,
                "restrict_pwned_password_set": 1.0,
                "notif_settings_enable_new_store": 1.0,
                "notif_settings_disable_old_store_write": 1.0,
                "notif_settings_disable_old_store_read": 1.0,
                "checkout-enable-risksdk": 1.0,
                "checkout-enable-risksdk-for-initial-purchase": 1.0,
                "enable_metadefender_check_for_agreement": 1.0,
                "show_download_yield_curves_data": 1.0,
                "disable_widgetbar_in_apps": 1.0,
                "hide_publications_of_banned_users": 1.0,
                "portfolios_page": 1.0,
                "save_chart_log_too_often_changes_sentry": 0.2,
                "show_news_flow_tool_right_bar": 1.0,
                "enable_chart_saving_stats": 1.0,
                "enable_saving_same_chart_rate_limit": 1.0,
                "refund_unvoidable_coupons_enabled": 1.0,
                "enable_snowplow_email_tracking": 1.0
            };
            window.initData = {};
            window.S3_LOGO_SERVICE_BASE_URL = "https://s3-symbol-logo.tradingview.com/";

            window.initData = {
                "widgetDefaults": {
                    "isTransparent": false,
                    "displayMode": "adaptive",
                    "showSymbolLogo": false,
                    "symbols": [{
                        "proName": "FOREXCOM:SPXUSD",
                        "title": "S&P 500 Index"
                    }, {
                        "proName": "FOREXCOM:NSXUSD",
                        "title": "US 100 Cash CFD"
                    }, {
                        "proName": "FX_IDC:EURUSD",
                        "title": "EUR to USD"
                    }, {
                        "proName": "BITSTAMP:BTCUSD",
                        "title": "Bitcoin"
                    }, {
                        "proName": "BITSTAMP:ETHUSD",
                        "title": "Ethereum"
                    }],
                    "colorTheme": "light",
                    "locale": "en"
                }
            };
            window.initData.currentLocaleInfo = {
                "language": "en",
                "language_name": "English",
                "flag": "us",
                "geoip_code": "us",
                "iso": "en",
                "iso_639_3": "eng",
                "global_name": "English",
                "is_only_recommended_tw_autorepost": true
            };
            window.initData.settings = {
                'S3_LOGO_SERVICE_BASE_URL': 'https://s3-symbol-logo.tradingview.com/',
                'S3_NEWS_IMAGE_SERVICE_BASE_URL': 'https://s3.tradingview.com/news/',
                'WEBPACK_STATIC_PATH': 'https://static.tradingview.com/static/bundles/',
                'TRADING_URL': 'https://papertrading.tradingview.com',
                'TRADING_COMPETITION_ID': 'THE_LEAP_TRADESTATION'
            };
        </script>
        <script nonce="iYgQxM1C7oIMUoQa+uM2/g==">
            ( () => {
                "use strict";
                const e = {
                    whitelabel: !0,
                    permissionOverrides: !0
                };
                const t = window;
                t.initData = t.initData || {};
                const n = /\+/g
                  , a = /<\/?[^>]+(>|$)/g
                  , i = e => decodeURIComponent(e.replace(n, " ")).replace(a, "")
                  , o = t.location.search.substring(1)
                  , s = /([^&=]+)=?([^&]*)/g
                  , c = {};
                let r, l = s.exec(o);
                for (; null !== l; )
                    c[i(l[1])] = i(l[2]),
                    l = s.exec(o);
                try {
                    r = JSON.parse(decodeURIComponent(t.location.hash.substring(1))),
                    "object" == typeof r && null !== r || (r = {})
                } catch (e) {
                    r = {}
                }
                var h;
                t.initData.querySettings = c,
                t.initData.hashSettings = (h = r,
                Object.keys(h).forEach((t => {
                    e[t] && delete h[t]
                }
                )),
                h)
            }
            )();
        </script>
        <script nonce="iYgQxM1C7oIMUoQa+uM2/g==">
            window.SS_HOST = "symbol-search.tradingview.com";
            window.WIDGET_SHERIFF_HOST = "https://widget-sheriff.tradingview-widget.com";
            window.DEFAULT_SYMBOL = "NASDAQ:AAPL";
            window.is_authenticated = false;

            window.locale_domains = {
                "en": "www.tradingview.com",
                "ar_AE": "ar.tradingview.com",
                "br": "br.tradingview.com",
                "de_DE": "de.tradingview.com",
                "es": "es.tradingview.com",
                "fr": "fr.tradingview.com",
                "he_IL": "il.tradingview.com",
                "id": "id.tradingview.com",
                "in": "in.tradingview.com",
                "it": "it.tradingview.com",
                "ja": "jp.tradingview.com",
                "kr": "kr.tradingview.com",
                "ms_MY": "my.tradingview.com",
                "pl": "pl.tradingview.com",
                "ru": "ru.tradingview.com",
                "th_TH": "th.tradingview.com",
                "tr": "tr.tradingview.com",
                "vi_VN": "vn.tradingview.com",
                "zh_CN": "cn.tradingview.com",
                "zh_TW": "tw.tradingview.com",
                "ca_ES": "es.tradingview.com"
            };

            window.__initialEnabledFeaturesets = window.__initialEnabledFeaturesets || [];
            window.__initialEnabledFeaturesets.push('widget');

            window.TradingView = window.TradingView || {};
        </script>
    </head>
    <body>
        <script nonce="iYgQxM1C7oIMUoQa+uM2/g==">
            ( () => {
                "use strict";
                const t = 1019;
                var e, s, o;
                !function(t) {
                    t.Adaptive = "adaptive",
                    t.Regular = "regular",
                    t.Compact = "compact"
                }(e || (e = {})),
                function(t) {
                    t.AllSymbols = "all_symbols",
                    t.Market = "market",
                    t.Symbol = "symbol"
                }(s || (s = {})),
                function(t) {
                    t.Cryptocurrencies = "crypto",
                    t.Currencies = "forex",
                    t.Stocks = "stock",
                    t.Indices = "index",
                    t.Futures = "futures",
                    t.Bonds = "cfd"
                }(o || (o = {}));
                const a = window;
                a.initData = a.initData || {};
                let d = a.initData.hashSettings;
                if (a.initData.widgetDefaults && (d = {
                    ...a.initData.widgetDefaults,
                    ...d
                }),
                void 0 !== d) {
                    if (void 0 !== d.colorTheme && function(t, e=window) {
                        const s = "theme-" + t
                          , o = e.document.documentElement.classList;
                        for (const t of Array.from(o))
                            t.startsWith("theme-") && t !== s && o.remove(t);
                        o.add(s),
                        e.document.documentElement.dataset.theme = t
                    }(d.colorTheme),
                    (d.isTransparent || d.transparency) && document.documentElement.classList.add("is-transparent"),
                    void 0 !== d.displayMode && function(s, o) {
                        if (o === e.Adaptive) {
                            const e = window.matchMedia(`(max-width: ${t}px)`)
                              , o = () => {
                                s.classList.toggle("tv-display-mode-regular", !e.matches),
                                s.classList.toggle("tv-display-mode-compact", e.matches)
                            }
                            ;
                            o(),
                            e.addEventListener("change", o)
                        } else
                            s.classList.toggle("tv-display-mode-regular", o === e.Regular),
                            s.classList.toggle("tv-display-mode-compact", o === e.Compact)
                    }(document.body, d.displayMode),
                    d.bodyClasses)
                        for (const t of d.bodyClasses)
                            document.body.classList.add(t);
                    d.body_class && document.body.classList.add(d.body_class),
                    d.bodyId && (document.body.id = d.bodyId)
                }
            }
            )();
        </script>
        <div id="widget-ticker-tape-container"></div>
        <script nonce="iYgQxM1C7oIMUoQa+uM2/g==">
            window.initData = window.initData || {};

            window.initData.snowplowSettings = {
                collectorId: 'tv_cf',
                url: 'snowplow-pixel.tradingview.com',
                params: {
                    appId: 'tradingview',
                    postPath: '/com.tradingview/track',
                },
                enabled: true,
            }
        </script>
    </body>
</html>
