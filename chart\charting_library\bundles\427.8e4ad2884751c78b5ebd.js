(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[427],{61791:e=>{e.exports={iconButton:"iconButton-RAiBjVep",square:"square-RAiBjVep",round:"round-RAiBjVep",primary:"primary-RAiBjVep",icon:"icon-RAiBjVep",secondary:"secondary-RAiBjVep",tertiary:"tertiary-RAiBjVep","primary-special":"primary-special-RAiBjVep"}},626040:e=>{e.exports={divider:"divider-pzdcWv_c","orientation-horizontal":"orientation-horizontal-pzdcWv_c","orientation-vertical":"orientation-vertical-pzdcWv_c","size-xsmall":"size-xsmall-pzdcWv_c","size-small":"size-small-pzdcWv_c","size-medium":"size-medium-pzdcWv_c","size-large":"size-large-pzdcWv_c","size-xlarge":"size-xlarge-pzdcWv_c","size-xxlarge":"size-xxlarge-pzdcWv_c","type-primary":"type-primary-pzdcWv_c","type-secondary":"type-secondary-pzdcWv_c"}},34869:e=>{e.exports={hidden:"hidden-DgcIT6Uz",fadeInWrapper:"fadeInWrapper-DgcIT6Uz"}},524614:e=>{e.exports={blockIcon:"blockIcon-JMh4y6KH"}},824442:e=>{e.exports={button:"button-hQBvpmqM",xsmall:"xsmall-hQBvpmqM",small:"small-hQBvpmqM",medium:"medium-hQBvpmqM",large:"large-hQBvpmqM",content:"content-hQBvpmqM",disabled:"disabled-hQBvpmqM"}},964178:e=>{e.exports={wrapper:"wrapper-qs1EOhiv",iconUrl:"iconUrl-qs1EOhiv",small:"small-qs1EOhiv",big:"big-qs1EOhiv"}},663355:e=>{e.exports={wrap:"wrap-mlpAzgv0"}},515145:e=>{e.exports={background:"background-CRAOSRte",medium:"medium-CRAOSRte",large:"large-CRAOSRte",disabled:"disabled-CRAOSRte",open:"open-CRAOSRte",selected:"selected-CRAOSRte",neutral:"neutral-CRAOSRte",danger:"danger-CRAOSRte"}},508316:e=>{e.exports={ellipsis:"ellipsis-K3hWbfcy",withGaps:"withGaps-K3hWbfcy"}},450271:e=>{e.exports={hotkey:"hotkey-inADekSg"}},387982:e=>{e.exports={buttonContent:"buttonContent-Us7jWCYh",left:"left-Us7jWCYh"}},518410:e=>{e.exports={button:"button-Lsy3A2H8"}},907767:e=>{e.exports={middle:"middle-LSK1huUA",hasNoEndSlot:"hasNoEndSlot-LSK1huUA",title:"title-LSK1huUA",description:"description-LSK1huUA",hasTitle:"hasTitle-LSK1huUA",hasDescription:"hasDescription-LSK1huUA"}},956866:e=>{e.exports={right:"right-gJMI494Y"}},658100:e=>{e.exports={bar:"bar-clPa1LSn",thumb:"thumb-clPa1LSn",hidden:"hidden-clPa1LSn"}},972354:e=>{e.exports={popoverKeyboardNavigator:"popoverKeyboardNavigator-L2EaU7m_",contentDefaultAppearanceMobile:"contentDefaultAppearanceMobile-L2EaU7m_",contentDefaultAppearance:"contentDefaultAppearance-L2EaU7m_",begin:"begin-L2EaU7m_",end:"end-L2EaU7m_",top:"top-L2EaU7m_",bottom:"bottom-L2EaU7m_",eventWrapper:"eventWrapper-L2EaU7m_"}},127512:e=>{e.exports={invisible:"invisible-lATuqHRX",positioner:"positioner-lATuqHRX",contentWrapper:"contentWrapper-lATuqHRX",allowShrinkWidth:"allowShrinkWidth-lATuqHRX",notAllowShrinkWidth:"notAllowShrinkWidth-lATuqHRX",content:"content-lATuqHRX"}},927963:e=>{e.exports={content:"content-sCtoIJXr",stretch:"stretch-sCtoIJXr",begin:"begin-sCtoIJXr",end:"end-sCtoIJXr",top:"top-sCtoIJXr",bottom:"bottom-sCtoIJXr",backdrop:"backdrop-sCtoIJXr"}},281005:e=>{e.exports={hotkeyBox:"hotkeyBox-zwrHyYaw",contentWrapper:"contentWrapper-zwrHyYaw",content:"content-zwrHyYaw",
tooltip:"tooltip-zwrHyYaw",tooltipBase:"tooltipBase-zwrHyYaw",contentRtl:"contentRtl-zwrHyYaw",endSlotRtl:"endSlotRtl-zwrHyYaw",endSlotWrapper:"endSlotWrapper-zwrHyYaw",endSlotContentRtl:"endSlotContentRtl-zwrHyYaw",endSlotContent:"endSlotContent-zwrHyYaw",divider:"divider-zwrHyYaw",tooltipBaseWrapper:"tooltipBaseWrapper-zwrHyYaw",disableInteractive:"disableInteractive-zwrHyYaw",floatingArrow:"floatingArrow-zwrHyYaw",top:"top-zwrHyYaw","top-left":"top-left-zwrHyYaw","top-right":"top-right-zwrHyYaw",bottom:"bottom-zwrHyYaw","bottom-left":"bottom-left-zwrHyYaw","bottom-right":"bottom-right-zwrHyYaw",right:"right-zwrHyYaw","right-top":"right-top-zwrHyYaw","right-bottom":"right-bottom-zwrHyYaw",isSafari:"isSafari-zwrHyYaw",left:"left-zwrHyYaw","left-top":"left-top-zwrHyYaw","left-bottom":"left-bottom-zwrHyYaw"}},372764:e=>{e.exports={iconButton:"iconButton-ENbqg56K",square:"square-ENbqg56K",round:"round-ENbqg56K",primary:"primary-ENbqg56K",icon:"icon-ENbqg56K",secondary:"secondary-ENbqg56K",tertiary:"tertiary-ENbqg56K","primary-special":"primary-special-ENbqg56K"}},536718:e=>{e.exports={"default-drawer-min-top-distance":"100px",wrap:"wrap-_HnK0UIN",positionBottom:"positionBottom-_HnK0UIN",backdrop:"backdrop-_HnK0UIN",drawer:"drawer-_HnK0UIN",positionLeft:"positionLeft-_HnK0UIN"}},840638:e=>{e.exports={checkbox:"checkbox-aOSYFxuH"}},642739:e=>{e.exports={icon:"icon-vDz9Zwc6",toolboxLabel:"toolboxLabel-vDz9Zwc6"}},149128:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},80503:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_ICON_BUTTON_VARIANT:()=>r,IconButtonVariantContext:()=>i,IconButtonVariantContextProvider:()=>a});var o=n(50959);const r="primary",i=(0,o.createContext)({variant:void 0});function a(e){return o.createElement(i.Provider,{value:{variant:e.variant}},e.children)}},584811:(e,t,n)=>{"use strict";n.d(t,{Divider:()=>u});var o,r,i,a=n(50959),s=n(497754),l=n(626040),c=n.n(l);function u(e){const{className:t,type:n="secondary",size:o="small",orientation:r="horizontal"}=e,i=s(t,c().divider,c()[`size-${o}`],c()[`type-${n}`],c()[`orientation-${r}`]);return a.createElement("hr",{className:i,"aria-orientation":r})}!function(e){e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(o||(o={})),function(e){e.Primary="primary",e.Secondary="secondary"}(r||(r={})),function(e){e.Horizontal="horizontal",e.Vertical="vertical"}(i||(i={}))},612554:(e,t,n)=>{"use strict";n.d(t,{useFadeInContext:()=>i});var o=n(50959);n(497754),n(34869);const r=o.createContext({children:{},setIsReady:()=>{}});function i(){const{setIsReady:e,children:t}=(0,o.useContext)(r),n=(0,o.useRef)((0,o.useId)());t[n.current]||(t[n.current]={isReady:!1});return(0,o.useCallback)((()=>{t[n.current].isReady=!0,e(Object.values(t).every((e=>e.isReady)))}),[t,e])}},35574:(e,t,n)=>{"use strict";n.d(t,{useIsFirstRender:()=>r,useIsNonFirstRender:()=>i});var o=n(50959);function r(){const[e,t]=(0,o.useState)(!0);return(0,o.useEffect)((()=>{t(!1)}),[]),e}function i(){return!r()}},185727:(e,t,n)=>{"use strict";n.d(t,{
usePrevious:()=>r});var o=n(50959);function r(e){const t=(0,o.useRef)(null);return(0,o.useEffect)((()=>{t.current=e}),[e]),t.current}},592790:(e,t,n)=>{"use strict";n.d(t,{BlockIcon:()=>l});var o=n(497754),r=n.n(o),i=n(50959);const a=i.forwardRef(((e,t)=>{const{className:n,ariaLabel:o,ariaLabelledby:r,title:a,icon:s=""}=e,l=!(!o&&!r);return i.createElement("span",{"aria-label":o,"aria-labelledby":r,"aria-hidden":!l,ref:t,role:"img",dangerouslySetInnerHTML:{__html:s},className:n,title:a})}));var s=n(524614);const l=i.forwardRef(((e,t)=>{const{className:n,...o}=e;return i.createElement(a,{...o,className:r()(n,s.blockIcon),ref:t})}))},319937:(e,t,n)=>{"use strict";n.d(t,{ButtonAnchor:()=>h,useDefaultButtonAnchorProps:()=>b});var o,r=n(50959),i=n(497754),a=n.n(i),s=n(34735),l=n(102691),c=n(148982),u=n(930202),d=n(718736),p=n(528457),m=n(824442);function h(e){const{role:t,children:n,isOpen:o,onClick:i,onKeyDown:u,id:d,reference:p,focusedElement:h,stretch:f,anchorButtonClassname:E,disabled:b,ariaHaspopup:g,ariaControls:v,ariaLabelledby:C,size:y="medium",...O}=e,T=O,w=r.createElement(l.MiddleSlot,null,r.createElement("div",{className:m.content},n)),R=r.createElement(l.EndSlot,null,r.createElement(c.Caret,{isDropped:o})),[P,_]=(0,r.useState)(!1),A=P||o;return r.createElement(s.ControlSkeleton,{tag:"button",type:"button",role:t,"aria-controls":v,"aria-expanded":o,"aria-haspopup":g,"aria-activedescendant":h,"aria-disabled":b,"aria-labelledby":C,tabIndex:b?-1:0,disabled:b,size:"medium",ref:p,middleSlot:w,endSlot:R,onClick:b?void 0:i,onKeyDown:b?void 0:u,id:d,key:d,intent:A?"primary":"default",isFocused:P||o,highlight:P||o,onFocus:b?void 0:()=>_(!0),onBlur:()=>_(!1),stretch:f,className:a()(m.button,E,b&&m.disabled,m[y]),...T})}!function(e){e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large"}(o||(o={}));const f=new Set([32,40,38]),E=new Set([...f,13]);function b(e){const t=(0,r.useRef)(),n=(0,d.useFunctionalRefObject)(t),{isOpen:o,onOpen:i,onClose:a}=(0,p.useRootPopoverOpenState)(n),s=(0,r.useId)(),l=e?.anchorId??s,c=(0,r.useId)(),m=function(e){const{isOpen:t,onOpen:n,openOnEnter:o=!0}=e;return(0,r.useCallback)((e=>{const r=(0,u.hashFromEvent)(e);!t&&(o?E:f).has(r)&&(n(),e.preventDefault(),e.stopPropagation())}),[t,n,o])}({isOpen:o,onOpen:i,openOnEnter:e?.openOnEnter}),h=(0,r.useCallback)((()=>a(!0,"allLevels")),[a]);return{anchorButtonRef:n,isOpen:o,onOpen:i,onClose:a,anchorId:l,contentId:c,handleAnchorClick:o?h:i,handleAnchorKeyDown:m}}},241032:(e,t,n)=>{"use strict";n.d(t,{DATA_IS_CONTEXT_ACTION:()=>i,DATA_IS_POPOVER_ITEM_BUTTON:()=>r,DATA_NESTED_POPOVER_OPEN:()=>o,DATA_POSITIONER_FIXED_PART:()=>a,DATA_POSITIONER_SCROLLABLE_PART:()=>s});const o="data-nested-popover-open",r="data-is-popover-item-button",i="data-is-popover-context-action",a="data-positioner-fixed-part",s="data-positioner-scrollable-part"},656613:(e,t,n)=>{"use strict";n.d(t,{POPOVER_DEFAULT_CLOSE_ON_CLICK_AWAY:()=>c,POPOVER_DEFAULT_CLOSE_ON_ESCAPE:()=>l,POPOVER_DEFAULT_CLOSE_ON_RESIZE_WINDOW:()=>d,POPOVER_DEFAULT_CLOSE_ON_SCROLL_OUTSIDE:()=>u,
POPOVER_DEFAULT_CLOSE_ON_TAB:()=>p,POPOVER_DEFAULT_HEIGHT:()=>a,POPOVER_DEFAULT_IF_NONE_PLACEMENT_RULE_MATCHED:()=>r,POPOVER_DEFAULT_PLACEMENT_RULES:()=>s,POPOVER_DEFAULT_WIDTH:()=>i});var o=n(19952);const r={pickRule:{by:"smallest-overlap",criteria:"area"},horizontal:"keepNonFitting",vertical:"cropSlideToFit"},i=[{by:"anchor",type:"min"},{by:"content"}],a=[],s=[o.PLACEMENT_BOTTOM_ALIGN_LEFT,o.PLACEMENT_TOP_ALIGN_LEFT,o.PLACEMENT_BOTTOM_ALIGN_RIGHT,o.PLACEMENT_TOP_ALIGN_RIGHT],l=!0,c="pointerdown",u=!0,d=!0,p=!0},393728:(e,t,n)=>{"use strict";n.d(t,{PopoverContextProvider:()=>i,usePopoverContext:()=>a});var o=n(50959);const r=(0,o.createContext)({isAnchorPositioner:!1,isMobilePositioner:!1,closePopover:()=>{},isRtl:!1});function i(e){const{positioner:t,onClose:n,isRtl:i,children:a}=e,s=(0,o.useMemo)((()=>({isAnchorPositioner:"anchor"===t,isMobilePositioner:"mobile"===t,closePopover:n,isRtl:i})),[t,n,i]);return o.createElement(r.Provider,{value:s},a)}function a(){return(0,o.useContext)(r)}},354648:(e,t,n)=>{"use strict";n.d(t,{SvgIconPopoverItemPart:()=>c,iconPart:()=>m});var o=n(50959),r=n(497754),i=n.n(r),a=n(592790),s=n(991323),l=n(964178);function c(e){const{icon:t}=e;return o.createElement("div",{className:l.wrapper},o.createElement(a.BlockIcon,{icon:t}))}function u(e){const t=(0,o.useContext)(s.PopoverItemHasDescriptionContext),{icon:n}=e;return o.createElement("div",{className:l.wrapper},o.createElement("img",{alt:"",src:n,className:i()(l.iconUrl,!t&&l.small,t&&l.big)}))}function d(e){return o.createElement("div",{className:l.wrapper})}function p(e){const{children:t}=e;return o.createElement("div",{inert:"true",className:l.wrapper},t)}function m(e){return e.iconJsx?o.createElement(p,null,e.iconJsx):e.iconSvg?o.createElement(c,{icon:e.iconSvg}):e.iconUrl?o.createElement(u,{icon:e.iconUrl}):e.iconSpace?o.createElement(d,null):void 0}},685075:(e,t,n)=>{"use strict";n.d(t,{CheckboxPopoverItem:()=>l});var o=n(50959),r=n(723991),i=n(302946);function a(e){return null!=e}var s=n(663355);function l(e){const{popoverItemRef:t,popoverItemButtonRef:n,popoverItemButtonId:l,role:c,title:u,description:d,checked:p,onClick:m,ariaChecked:h,leftSlot:f,disabled:E,tabIndex:b,...g}=e,v=g,C=(0,o.useCallback)((()=>{m?.(!p)}),[p,m]);return o.createElement(r.PopoverItem,{popoverItemRef:t,popoverItemButtonRef:n,popoverItemButtonId:l,ariaChecked:h,role:c,title:u,description:d,disabled:E,leftSlot:[o.createElement("div",{className:s.wrap,key:"checkbox"},o.createElement(i.CheckboxView,{checked:p,disableActiveStyles:!0})),...Array.isArray(f)?f:[f].filter(a)],onClick:C,tabIndex:b,...v})}},991323:(e,t,n)=>{"use strict";n.d(t,{PopoverItemHasDescriptionContext:()=>o});const o=(0,n(50959).createContext)(!1)},723991:(e,t,n)=>{"use strict";n.d(t,{PopoverItem:()=>x});var o=n(50959),r=n(497754),i=n(515145);function a(e){const t=(0,o.useRef)(null),{children:n,popoverItemRef:a=t,isOpen:s,selected:l,disabled:c,onMouseEnter:u,size:d="medium",ariaBusy:p,intent:m="neutral",...h}=e,f=h;return o.createElement("div",{...f,
className:r(i.background,l&&i.selected,s&&i.open,c&&i.disabled,i[d],i[m]),ref:a,onMouseEnter:u,"aria-busy":p},n)}var s=n(930202),l=n(241032),c=n(718736),u=n(518410);function d(e){const t=(0,o.useRef)(null),{children:n,popoverItemButtonRef:i=t,popoverItemButtonId:a,onKeyDownPopoverItemButton:d,href:p,target:m="_blank",hotkey:h,ariaChecked:f,ariaHaspopup:E,ariaControls:b,ariaExpanded:g,ariaLabel:v,role:C,disabled:y,isOpen:O,selected:T,onClick:w,tabIndex:R=0,rel:P,..._}=e,A=_,N=(0,c.useFunctionalRefObject)(i),x=(0,o.useCallback)((e=>{if(N.current!==e.target)return;d?.(e);const t=(0,s.hashFromEvent)(e);32!==t&&13!==t||(void 0!==p&&window.open(p,m),w?.(e),e.preventDefault(),e.stopPropagation())}),[w,p,m,d,N]),I={role:C,"aria-keyshortcuts":h?(L=h,Array.isArray(L)?L.map(s.hotkeyHashToAriaKeyshortcuts).join(" "):(0,s.hotkeyHashToAriaKeyshortcuts)(L)):void 0,"aria-expanded":g,"aria-haspopup":E,"aria-controls":b,"aria-checked":f,"aria-disabled":y,"aria-label":v,[l.DATA_IS_POPOVER_ITEM_BUTTON]:!0,[l.DATA_NESTED_POPOVER_OPEN]:O,className:r(u.button,T&&u.selected),tabIndex:R,ref:N,onKeyDown:y?void 0:x,onClick:y?void 0:w,id:a,...A};var L;return o.createElement(o.Fragment,null,!p&&o.createElement("div",{...I}," ",n," "),p&&o.createElement("a",{href:p,target:m,rel:P,...I},n))}var p=n(80503),m=n(592790),h=n(61791);(0,o.forwardRef)(((e,t)=>{const n=(0,o.useContext)(p.IconButtonVariantContext).variant,{title:i,onClick:a,colorClassName:s,icon:l,variant:c=n??p.DEFAULT_ICON_BUTTON_VARIANT,shape:u="square"}=e;return o.createElement("button",{ref:t,title:i,"aria-label":i,onClick:a,className:r(h.iconButton,h[c],h[u],s,"apply-common-tooltip")},o.createElement(m.BlockIcon,{icon:l,className:h.icon,"aria-hidden":"true"}))}));var f;!function(e){e.Square="square",e.Round="round"}(f||(f={}));var E=n(450271);function b(e){const{hotkey:t}=e;return o.createElement("div",{className:E.hotkey},(n=t,Array.isArray(n)?n.map(s.humanReadableHash).join(", "):(0,s.humanReadableHash)(n)));var n}var g=n(956866),v=n.n(g);function C(e){const{rightSlot:t,hotkey:n,selected:r}=e;return o.createElement("div",{className:v().right},o.createElement(p.IconButtonVariantContextProvider,{variant:r?"primary-special":"primary"},n&&o.createElement(b,{hotkey:n}),t))}var y=n(508316);function O(e){const{ellipsis:t,text:n,className:i}=e;return n?o.createElement("div",{className:r(i,"string"==typeof n&&t&&y.ellipsis,"string"!=typeof n&&y.withGaps,t&&"apply-overflow-tooltip")},n):null}var T=n(907767);function w(e){const{ellipsis:t=!0,title:n,description:i,hasEndSlot:a}=e;return o.createElement("div",{className:r(T.middle,n&&T.hasTitle,i&&T.hasDescription,!a&&T.hasNoEndSlot)},o.createElement(O,{key:"title",className:T.title,text:n,ellipsis:t}),o.createElement(O,{key:"description",className:T.description,text:i,ellipsis:t}))}var R=n(387982);function P(e){const{leftSlot:t,rightSlot:n,hotkey:r,selected:i,ellipsis:a,title:s,description:l}=e,c=void 0!==t&&o.createElement("div",{className:R.left,style:{"--ui-lib-popoverItem-leftSlotItemsNumber":Array.isArray(t)?t.length:1}
},t),u=n||r?o.createElement(C,{rightSlot:n,hotkey:r,selected:i}):null,d=o.createElement(w,{ellipsis:a,title:s,description:l,hasEndSlot:!!u});return o.createElement(o.Fragment,null,o.createElement("div",{className:R.buttonContent},c,d,u))}var _,A,N=n(991323);function x(e){const{title:t,description:n,popoverItemRef:r,popoverItemButtonRef:i,isOpen:s,selected:l,disabled:c,onClick:u,href:p,leftSlot:m,rightSlot:h,hotkey:f,intent:E,onMouseEnter:b,onKeyDownPopoverItemButton:g,popoverItemButtonId:v,ariaChecked:C,ariaHaspopup:y,ariaControls:O,ariaExpanded:T,ariaBusy:w,ariaLabel:R,role:_,size:A,ellipsis:x,target:I,outsideOfButtonSlot:L,tabIndex:S,rel:k,...D}=e,M=D;return o.createElement(N.PopoverItemHasDescriptionContext.Provider,{value:!!n},o.createElement(a,{size:A,selected:l,isOpen:s,disabled:c,popoverItemRef:r,onMouseEnter:b,ariaBusy:w,intent:E},o.createElement(d,{popoverItemButtonRef:i,popoverItemButtonId:v,onKeyDownPopoverItemButton:g,href:p,target:I,hotkey:f,ariaChecked:C,ariaHaspopup:y,ariaControls:O,ariaExpanded:T,ariaLabel:R,role:_,disabled:c,isOpen:s,selected:l,onClick:u,...M,tabIndex:S,rel:k},o.createElement(P,{leftSlot:m,rightSlot:h,hotkey:f,selected:l,ellipsis:x,title:t,description:n})),L))}!function(e){e.Medium="medium",e.Large="large"}(_||(_={})),function(e){e.Neutral="neutral",e.Danger="danger"}(A||(A={}))},879413:(e,t,n)=>{"use strict";n.d(t,{FixedHeaderFooterHeightContext:()=>o});const o=(0,n(50959).createContext)({setFixedHeaderHeight:()=>{},setFixedFooterHeight:()=>{}})},679864:(e,t,n)=>{"use strict";n.d(t,{PopoverKeyboardNavigator:()=>J});var o=n(50959),r=n(718736),i=n(656613),a=n(608753),s=n(990390);const l=(0,a.windowEvent)("resize");var c=n(312754),u=n(914996);function d(e,t,n){const o=(0,u.useWindowResizeTrigger)(!n.closeOnResizeWindow&&e),r=(0,u.useWindowScrollTrigger)(!n.closeOnScrollOutside&&e);return function(e,t){(0,s.useConditionalEffect)(e,(()=>l((()=>t(!0,"currentLevel")))),[t])}(!!n.closeOnResizeWindow&&e,t),(0,c.useCloseOnScrollOutside)(!!n.closeOnScrollOutside&&e,t,n.ref),[o,r]}var p=n(756036),m=n(603949);var h=n(525388),f=n(930202);const E=(0,a.windowEvent)("keydown");function b(e){const t=(0,o.useRef)(null),{onClose:n,isOpen:l,refToContentWrapperElement:c=t,closeOnClickAway:u=i.POPOVER_DEFAULT_CLOSE_ON_CLICK_AWAY,closeOnEscape:b=i.POPOVER_DEFAULT_CLOSE_ON_ESCAPE,closeOnScrollOutside:g=i.POPOVER_DEFAULT_CLOSE_ON_SCROLL_OUTSIDE,closeOnResizeWindow:v=i.POPOVER_DEFAULT_CLOSE_ON_RESIZE_WINDOW,closeOnTab:C=i.POPOVER_DEFAULT_CLOSE_ON_TAB,excludeArea:y}=e;let O=(0,r.useFunctionalRefObject)(c);const T=d(l,n,{closeOnResizeWindow:v,closeOnScrollOutside:g,ref:O});return O=function(e,t,n){const i=(0,o.useCallback)((o=>{e&&o&&o.addEventListener("keydown",(e=>{if(27!==(0,f.hashFromEvent)(e))return;const o=n.current;if(!o)return;const r=e.target;r instanceof HTMLElement&&o.contains(r)&&(e.preventDefault(),e.stopPropagation(),t(!0,"currentLevel"))}))}),[e]);return(0,r.useFunctionalRefObject)((0,h.useMergedRefs)([n,i]))}(b&&l,n,O),function(e,t,n){(0,s.useConditionalEffect)(!!e,(()=>E((e=>{const n=(0,
f.hashFromEvent)(e);9!==n&&n!==9+f.Modifiers.Shift||t(!0,"currentLevel")}),!0)),[t,n])}(C&&l,n,O),function(e,t,n,o,r){const l="boolean"==typeof o?i.POPOVER_DEFAULT_CLOSE_ON_CLICK_AWAY:o;(0,s.useConditionalEffect)(!!e,(()=>(0,a.windowEvent)(l)((e=>{const o={x:e.clientX,y:e.clientY},i=n.current;(0,m.isInExcludedArea)(o,r)||i&&(0,p.isXYInsideElementThroughPortals)(i,o.x,o.y)||t(!1,"currentLevel")}))),[t,r,n,l])}(u&&l,n,O,u,y),{repositionTriggers:T,refToContentWrapperElement:O}}var g=n(241032),v=n(911794),C=n(865081);function y(e){const{focusPrevLoop:t,focusNextLoop:n,focusFirst:r,focusLast:i}=(0,o.useMemo)((()=>(0,C.getSelectorKeyboardNavigation)(`[${g.DATA_IS_POPOVER_ITEM_BUTTON}]`)),[]);return{onKeyDown:function(e){const o=(0,f.hashFromEvent)(e),a=e.currentTarget,s=function(e){switch(e){case 38:return t;case 40:return n;case 36:return r;case 35:return i}}(o);if(s){e.stopPropagation(),e.preventDefault();const t=s?.(a,{preventScroll:!0});t&&(0,v.scrollPopoverItemIntoView)(t)}},refToContentWrapperElement:(0,o.useCallback)((t=>{e&&t&&r(t)}),[r,e])}}var O=n(497754),T=n.n(O),w=n(879413),R=n(664332),P=n(325414),_=n(658100);const A=24,N=2,x=4;function I(e){const{scrollableRef:t,isOpen:n,needsRerender:r,headerHeight:i,footerHeight:a}=e,s=(0,o.useCallback)((e=>{const n=t.current;if(!n)return;const o=(r=0,i=e,a=1,Math.min(Math.max(r,i),a));var r,i,a;n.scrollTo({top:o*(n.scrollHeight-n.clientHeight)})}),[t]);(0,o.useEffect)((()=>{if(n)return()=>{u(0)}}),[n]);const l=i+a,[c,u]=(0,o.useState)(0),[d,p]=(0,o.useState)(0),[m,h]=(0,o.useState)(0),f=(0,o.useCallback)((()=>{const e=t.current;if(!e)return;const n=e.scrollTop,o=e.offsetHeight,r=e.scrollHeight-o,i=o-l;u(n/r),p(i),h(i/(e.scrollHeight-l))}),[l,t]);(0,o.useLayoutEffect)(f,[f]),(0,o.useEffect)((()=>(r.subscribe(f),()=>{r.unsubscribe(f)})),[r,f]);const E=(0,o.useRef)(null),b=Math.max(A,m*d),g=d-b;function v(e){return e/g}const C=c*g+i;const y=Math.ceil(b),O=t.current?.getAttribute("id")??"";function w(e){return 0===e?x:N}const R=w(i),P=w(a),I=y>=d;return(0,o.useEffect)((()=>{if(!I)return;const e=t.current;if(!e)return;const n=e=>{e.preventDefault(),e.stopPropagation()};return e.addEventListener("wheel",n),()=>e.removeEventListener("wheel",n)}),[I,t]),o.createElement("div",{role:"scrollbar","aria-controls":O,"aria-valuenow":Math.round(100*c),"aria-hidden":I,ref:E,className:_.bar,style:{height:d+"px"},onMouseDown:e=>{const t=E.current;if(!t)return;const n=e.clientY-t.getBoundingClientRect().y;let o=n-C;if(o<0||o>i+b-C){o=.5*b;const e=v(n-o);s(e)}const r=e=>{e.preventDefault(),e.stopPropagation(),(e=>{const t=E.current;if(!t)return;const n=v(e.clientY-t.getBoundingClientRect().y-o-i);s(n)})(e)},a=e=>{e.preventDefault(),e.stopPropagation()},l=e=>{e.preventDefault(),e.stopPropagation(),window.removeEventListener("selectstart",a),window.removeEventListener("mousemove",r),window.removeEventListener("mouseup",l)};window.addEventListener("selectstart",a),window.addEventListener("mousemove",r),window.addEventListener("mouseup",l)}},o.createElement("div",{
className:T()(_.thumb,I&&_.hidden),style:{top:C+R+"px",height:b-R-P+"px"}}))}var L,S=n(730654),k=n(625956),D=n(927963),M=n.n(D);function B(e){const t=(0,o.useRef)(null),{isOpen:n,onClose:i,children:a,cardAppearanceClassname:s,anchoredAt:l,refToContentWrapperElement:c=t,side:u="bottom",idOfContentWrapperElement:d,ariaLabelledby:m,ariaMultiselectable:h,role:f,stretch:E,...b}=e,g=b,v=(0,r.useFunctionalRefObject)(c);return(0,p.useAnchorPortalPair)(l,v),n?o.createElement(S.Portal,null,o.createElement("div",{...(0,k.contentProps)(d,m,h,f),...g,ref:v,className:T()(M().content,M()[u],s,E&&M().stretch)},a),o.createElement("div",{role:"presentation",className:M().backdrop,onClick:()=>{i(!1)},onPointerDown:e=>{e.stopPropagation()}})):null}!function(e){e.SideBegin="begin",e.SideEnd="end",e.Top="top",e.Bottom="bottom"}(L||(L={}));var F=n(671129),W=n(586240),H=n(985098),z=n(133375);function U(e){const t=(0,o.useRef)(null),{anchoredAt:n,isOpen:r,refObjectToContentWrapperElement:i=t,width:a,allowShrinkWidth:s,height:l,isRtl:c,placementRules:u,ifNonePlacementRuleMatched:d,anchorPositionerCardAppearanceClassname:m,children:h,fixedChildren:f,role:E,ariaLabelledby:b,ariaMultiselectable:g,idOfContentWrapperElement:v,onPlacementRuleSelected:C,getRootRect:y,repositionTriggers:O,...T}=e,w=T,R=(0,z.useRefRect)(n,r,O);return(0,p.useAnchorPortalPair)(n,i),o.createElement(H.RectanglePositioner,{width:a,allowShrinkWidth:s,height:l,isRtl:c,isOpen:r,placementRules:u,ifNonePlacementRuleMatched:d,anchorPositionerCardAppearanceClassname:m,children:h,fixedChildren:f,role:E,ariaLabelledby:b,ariaMultiselectable:g,idOfContentWrapperElement:v,refObjectToContentWrapperElement:i,anchoredAt:R,onPlacementRuleSelected:C,getRootRect:y,...w})}const V=!1;function G(e){const t=(0,o.useRef)(null),{anchored:n,placementRules:i,ifNonePlacementRuleMatched:a,width:s,allowShrinkWidth:l=V,height:c,isOpen:u,isRtl:d,anchorPositionerCardAppearanceClassname:p,refToContentWrapperElement:m=t,idOfContentWrapperElement:h,ariaLabelledby:f,ariaMultiselectable:E,role:b,children:g,fixedChildren:v,getRootRect:C,repositionTriggers:y,...O}=e,T=O,w=(0,r.useFunctionalRefObject)(m);switch(n.type){case"element":return o.createElement(U,{placementRules:i,ifNonePlacementRuleMatched:a,width:s,allowShrinkWidth:l,height:c,isOpen:u,isRtl:d,anchorPositionerCardAppearanceClassname:p,refObjectToContentWrapperElement:w,idOfContentWrapperElement:h,ariaLabelledby:f,ariaMultiselectable:E,role:b,children:g,fixedChildren:v,anchoredAt:n.at,getRootRect:C,repositionTriggers:y,...T});case"rectangle":return o.createElement(H.RectanglePositioner,{placementRules:i,ifNonePlacementRuleMatched:a,width:s,height:c,isOpen:u,isRtl:d,anchorPositionerCardAppearanceClassname:p,refObjectToContentWrapperElement:w,idOfContentWrapperElement:h,ariaLabelledby:f,ariaMultiselectable:E,role:b,children:g,fixedChildren:v,anchoredAt:n.at,getRootRect:C,...T})}}var K=n(393728),Y=n(459993);const j=W["media-phone"],q=(0,Y.ignoreOnSSR)((function(e){const{isRtl:t}=(0,
K.usePopoverContext)(),{isOpen:n,onClose:r,containerClassname:i,mobilePositionerCardAppearanceClassname:a,refToContentWrapperElement:s,isRtl:l=t,children:c,fixedChildren:u,anchorPositionerCardAppearanceClassname:d,role:p,ariaLabelledby:m,ariaMultiselectable:h,idOfContentWrapperElement:f,placementRules:E,ifNonePlacementRuleMatched:b,width:g,allowShrinkWidth:v,height:C,anchored:y,mobilePositionerSide:O,mobilePositionerStretch:T,repositionTriggers:w,...R}=e,P=R;return(0,F.useMatchMedia)(j)?o.createElement(B,{isOpen:n,onClose:r,cardAppearanceClassname:a,refToContentWrapperElement:s,idOfContentWrapperElement:f,anchoredAt:"element"===y.type?y.at:void 0,children:o.createElement("div",{className:i},o.createElement(K.PopoverContextProvider,{positioner:"mobile",onClose:r,isRtl:l},c)),role:p,ariaLabelledby:m,ariaMultiselectable:h,side:O,stretch:T,...P}):o.createElement(G,{anchored:y,isRtl:l,isOpen:n,anchorPositionerCardAppearanceClassname:d,role:p,ariaLabelledby:m,ariaMultiselectable:h,idOfContentWrapperElement:f,refToContentWrapperElement:s,placementRules:E,ifNonePlacementRuleMatched:b,width:g,allowShrinkWidth:v,height:C,children:o.createElement("div",{className:i},o.createElement(K.PopoverContextProvider,{positioner:"anchor",onClose:r,isRtl:l},c)),fixedChildren:u,repositionTriggers:w,...P})}));var Q=n(972354),$=n.n(Q);function X(e){const t=(0,o.useRef)(null),{width:n=i.POPOVER_DEFAULT_WIDTH,height:r=i.POPOVER_DEFAULT_HEIGHT,placementRules:a=i.POPOVER_DEFAULT_PLACEMENT_RULES,ifNonePlacementRuleMatched:s=i.POPOVER_DEFAULT_IF_NONE_PLACEMENT_RULE_MATCHED,onClose:l,isOpen:c,closeOnClickAway:u,closeOnEscape:d,closeOnScrollOutside:p,closeOnHoverAwayDelayMs:m,closeOnResizeWindow:f,closeOnTab:E,anchorPositionerCardAppearanceClassname:b=Q.contentDefaultAppearance,children:g,role:v,ariaLabelledby:C,idOfContentWrapperElement:y,anchored:O,ariaMultiselectable:_,mobilePositionerSide:A="bottom",mobilePositionerCardAppearanceClassname:N=T()(Q.contentDefaultAppearanceMobile,Q[A]),mobilePositionerStretch:x,refObjectToContentWrapperElement:L=t,isRtl:S,repositionTriggers:k,scrollUpdateTriggers:D,...M}=e,B=M,F=(0,o.useId)(),W=y??F,[H,z]=(0,o.useState)(0),[U,V]=(0,o.useState)(0),G=(0,o.useMemo)((()=>({setFixedFooterHeight:V,setFixedHeaderHeight:z})),[]),{scrollbar:K,additionalContentWrapperElementRef:Y}=function(e,t,n,r=[]){const i=(0,o.useRef)(null),a=(0,o.useMemo)(P.createSignal,[]);(0,o.useEffect)((()=>(r.forEach((e=>e.subscribe(a.fire))),()=>{r.forEach((e=>e.unsubscribe(a.fire)))})),[...r,a]);const s=(0,R.useResizeObserver)({callback:a.fire}),l=(0,o.useCallback)((e=>{e?.addEventListener("scroll",a.fire)}),[a.fire]);return{additionalContentWrapperElementRef:(0,h.useMergedRefs)([s,l,i]),scrollbar:(0,o.useMemo)((()=>o.createElement(I,{scrollableRef:i,isOpen:e,needsRerender:a,headerHeight:t,footerHeight:n})),[i,e,a,t,n])}}(c,H,U,D),j=(0,h.useMergedRefs)([L,Y]);return o.createElement(w.FixedHeaderFooterHeightContext.Provider,{value:G},o.createElement(q,{anchored:O,isRtl:S,onClose:l,isOpen:c,anchorPositionerCardAppearanceClassname:b,role:v,
ariaLabelledby:C,ariaMultiselectable:_,idOfContentWrapperElement:W,refToContentWrapperElement:j,placementRules:a,ifNonePlacementRuleMatched:s,width:n,height:r,containerClassname:Q.popoverKeyboardNavigator,children:g,fixedChildren:K,allowShrinkWidth:!1,mobilePositionerSide:A,mobilePositionerCardAppearanceClassname:N,mobilePositionerStretch:x,repositionTriggers:k,...B}))}function J(e){const t=(0,o.useRef)(null),{width:n=i.POPOVER_DEFAULT_WIDTH,height:a=i.POPOVER_DEFAULT_HEIGHT,placementRules:s=i.POPOVER_DEFAULT_PLACEMENT_RULES,ifNonePlacementRuleMatched:l=i.POPOVER_DEFAULT_IF_NONE_PLACEMENT_RULE_MATCHED,onClose:c,isOpen:u,closeOnClickAway:d=i.POPOVER_DEFAULT_CLOSE_ON_CLICK_AWAY,closeOnEscape:p=i.POPOVER_DEFAULT_CLOSE_ON_ESCAPE,closeOnScrollOutside:m=i.POPOVER_DEFAULT_CLOSE_ON_SCROLL_OUTSIDE,closeOnResizeWindow:f=i.POPOVER_DEFAULT_CLOSE_ON_RESIZE_WINDOW,closeOnTab:E=i.POPOVER_DEFAULT_CLOSE_ON_TAB,anchorPositionerCardAppearanceClassname:g,children:v,role:C,ariaLabelledby:O,idOfContentWrapperElement:T,anchored:w,ariaMultiselectable:R,mobilePositionerSide:P,mobilePositionerCardAppearanceClassname:_,refToContentWrapperElement:A=t,isRtl:N,focusFirstItemOnOpen:x=!0,repositionTriggers:I=[],...L}=e,S=L,k=(0,r.useFunctionalRefObject)(A),D=(0,o.useId)(),M=T??D,{repositionTriggers:B,refToContentWrapperElement:F}=b({onClose:c,isOpen:u,closeOnClickAway:d,closeOnEscape:p,closeOnResizeWindow:f,closeOnScrollOutside:m,closeOnTab:E,refToContentWrapperElement:k,excludeArea:w.at}),{onKeyDown:W,refToContentWrapperElement:H}=y(x),z=(0,h.useMergedRefs)([H,F]);return o.createElement(X,{anchored:w,isRtl:N,onClose:c,isOpen:u,anchorPositionerCardAppearanceClassname:g,role:C,ariaLabelledby:O,ariaMultiselectable:R,idOfContentWrapperElement:M,refObjectToContentWrapperElement:z,placementRules:s,ifNonePlacementRuleMatched:l,width:n,height:a,children:o.createElement("div",{onKeyDown:W,className:$().eventWrapper},v),mobilePositionerSide:P,mobilePositionerCardAppearanceClassname:_,repositionTriggers:[...B,...I],...S})}},911794:(e,t,n)=>{"use strict";n.d(t,{scrollPopoverItemIntoView:()=>r});var o=n(241032);function r(e,t){if(e.closest(`[${o.DATA_POSITIONER_FIXED_PART}]`))return;const n=e.closest(`[${o.DATA_POSITIONER_SCROLLABLE_PART}]`);if(!n)return;const r=n.getBoundingClientRect(),i=e.getBoundingClientRect();function a(e){return e.y+e.height/2}const s=a(r),l=a(i)-s;n.scrollBy({top:l,behavior:t?"smooth":void 0})}},133375:(e,t,n)=>{"use strict";n.d(t,{useRefRect:()=>s});var o=n(50959),r=n(701928);const i={x:0,y:0,width:0,height:0};function a(e){return function(e){if(!e)return i;const{x:t,y:n,width:o,height:r}=e.getBoundingClientRect();return{x:t,y:n,width:o,height:r}}(e.current)}function s(e,t,n=[]){const[s,l]=(0,o.useState)(i);return(0,o.useEffect)((()=>{const t=()=>{l(a(e))},o=n.map((e=>(e.subscribe(t),()=>e.unsubscribe(t))));return(0,r.forkFn)(...o)}),[...n,e]),(0,o.useLayoutEffect)((()=>{t&&l(a(e))}),[e,t]),s}},510695:(e,t,n)=>{"use strict";function o(e,t){return t.x<=e.x&&e.x<=t.x+t.width&&t.y<=e.y&&e.y<=t.y+t.height}n.d(t,{isInRect:()=>o})},
985098:(e,t,n)=>{"use strict";n.d(t,{RectanglePositioner:()=>P});var o=n(50959),r=n(497754),i=n(730654),a=n(625956),s=n(718736),l=n(664332);function c(e,t){return{x:t?-e.x:1-e.x,y:e.y}}function u(e,t){return e.map((e=>function(e,t){return{name:t&&e.rtlName||e.name,anchorPoint:c(e.anchorPoint),contentPoint:c(e.contentPoint),offset:e.offset?c(e.offset,!0):void 0}}(e,t)))}function d(e,t){return{start:e,length:t}}function p(e){return d(e.x,e.width)}function m(e){return d(e.y,e.height)}function h(e,t,n,o){const r=o.horizontal(p(e),p(t),p(n)),i=o.vertical(m(e),m(t),m(n));return{x:r.start,y:i.start,width:r.length,height:i.length}}function f(e,t){const n=e.start+e.length,o=t.start+t.length;return e.start<t.start?{overflow:t.start-e.start,correction:t.start-e.start}:n>o?{overflow:n-o,correction:o-n}:{overflow:0,correction:0}}function E(e,t,n,o){const r=function(e,t,n){const{anchorPoint:o,contentPoint:r,offset:i}=n;return{x:e.x+e.width*o.x-t.width*r.x+(i?.x??0),y:e.y+e.height*o.y-t.height*r.y+(i?.y??0)}}(e,t,n),i=d(r.x,t.width),a=p(o),{overflow:s}=f(i,a),l=d(r.y,t.height),c=m(o),{overflow:u}=f(l,c);return{contentRect:{x:r.x,y:r.y,...t},screenRect:o,overflow:{x:s,y:u}}}const b={horizontal:e=>e[0].overflow.x,vertical:e=>e[0].overflow.y,area:function(e){const t=e[0].contentRect,n=function(e,t){const n=Math.max(e.x,t.x),o=Math.max(e.y,t.y),r=Math.min(e.x+e.width,t.x+t.width),i=Math.min(e.y+e.height,t.y+t.height);if(!(n>r||o>i))return{x:n,y:o,width:r-n,height:i-o}}(t,{...e[0].screenRect});return n?t.width*t.height-n.width*n.height:Number.POSITIVE_INFINITY}};function g(e){return{pickRule:function(e){switch(e?.by){case"smallest-overlap":return n=e.criteria,t=b[n],e=>function(e,t){return function(e,t){let n=e[0],o=t(n);for(let r=1;r<e.length;r++){const i=e[r],a=t(i);a<o&&(o=a,n=i)}return n}(e,t)}(e,t);case"name":return t=>function(e,t){return e.find((e=>e[1].name===t.name))??e[0]}(t,e);default:return e=>function(e){return e[0]}(e)}var t;var n}(e.pickRule),vertical:y(e.vertical),horizontal:y(e.horizontal)}}const v={keepNonFitting:function(e){return d(e.start,e.length)},cropSlideToFit:function(e,t,n){if(t.start+t.length>=n.start&&t.start<=n.start+n.length){if(e.start<n.start)return d(n.start,e.length+e.start-n.start);const t=e.start+e.length>n.start+n.length;return d(e.start,t?n.start+n.length-e.start:e.length)}{const o=t.start+t.length<=n.start,r=Math.min(e.length,n.length);return d(o?n.start:n.length-r,r)}},slideToFit:function(e,t,n){return e.start<n.start?d(n.start,e.length):d(e.start+e.length>n.start+n.length?n.length-e.length:e.start,e.length)}},C="keepNonFitting";function y(e){return v[e??C]}function O(){return{x:0,y:0,width:document.documentElement.clientWidth,height:document.documentElement.clientHeight}}var T=n(127512),w=n.n(T);const R={width:0,height:0};function P(e){
const{isRtl:t,anchoredAt:n,isOpen:c,placementRules:d,ifNonePlacementRuleMatched:p,allowShrinkWidth:m,anchorPositionerCardAppearanceClassname:f,refObjectToContentWrapperElement:b,children:v,onPlacementRuleSelected:C,fixedChildren:y,getRootRect:T=O,idOfContentWrapperElement:P,ariaLabelledby:_,ariaMultiselectable:A,role:N,width:x,height:I,...L}=e,S=L,[k,D]=(0,o.useState)(void 0),M=g(p),B=(0,o.useCallback)((e=>{const t=Math.ceil(e.width),n=Math.ceil(e.height);D((e=>e?.width!==t||e?.height!==n?{width:t,height:n}:e))}),[D]),F=(0,o.useCallback)((([e])=>{const t=e.target;c&&k&&t&&B({width:e.borderBoxSize[0].inlineSize,height:e.borderBoxSize[0].blockSize})}),[c,k,B]),W=(0,l.useResizeObserver)(F),H=(0,s.useFunctionalRefObject)(W);(0,o.useLayoutEffect)((()=>{const e=H.current;if(!c||k||!e)return;const t=e.getBoundingClientRect();B({width:t.width,height:t.height})}),[c,k,H,B]),(0,o.useEffect)((()=>{c||D(void 0)}),[c]);const[z,U]=(0,o.useMemo)((()=>function(e,t,n,o,r,i=!1){const a=i?u(o,i):o,s=[];for(const o of a){const r=E(e,t,o,n);if(0===r.overflow.x&&0===r.overflow.y)return[r.contentRect,o];s.push([r,o])}const l=r.pickRule(s);return[h(l[0].contentRect,e,n,r),l[1]]}(n,k??R,T(),d,M,t)),[n,k,T,d,M,t]);if((0,o.useEffect)((()=>{if(U&&C)return C(U)}),[U,C]),!c)return null;const V=[],G={},K={};for(const t of["width","height"]){const o=e[t];(Array.isArray(o)?o:[o]).filter((e=>!!e)).forEach((e=>{if("content"===e.by)return;if("class"===e.by)return void V.push(e.className);const o=e.type??"exact";("exact"===o?G:K)["--ui-lib-positioner-anchor-"+o+"-"+t]=n[t]+"px"}))}const Y=[f,w().positioner],j={...G,...K,"--ui-lib-positioner-anchor-width":n.width+"px","--ui-lib-positioner-anchor-height":n.height+"px","--ui-lib-positioner-anchor-top":n.y+"px","--ui-lib-positioner-anchor-left":n.x+"px"},q=o.createElement("div",{...(0,a.contentProps)(P,_,A,N),...S,className:w().contentWrapper,ref:b},o.createElement("div",{className:r(w().content,m?w().allowShrinkWidth:w().notAllowShrinkWidth,...V),style:K,ref:H},v));return void 0===k?o.createElement(i.Portal,null,o.createElement("div",{className:r(w().invisible,...Y),style:j},q)):o.createElement(i.Portal,null,o.createElement("div",{className:r(...Y,...V),style:{"--ui-lib-positioner-calculated-left":z.x+"px","--ui-lib-positioner-calculated-top":z.y+"px","--ui-lib-positioner-calculated-height":z.height+"px","--ui-lib-positioner-calculated-width":z.width+"px",...j}},q,y))}},528457:(e,t,n)=>{"use strict";n.d(t,{useNestedPopoverOpenState:()=>i,useRootPopoverOpenState:()=>r});var o=n(50959);function r(e){const[t,n]=(0,o.useState)(!1),r=(0,o.useCallback)((()=>{n(!1)}),[]),i=(0,o.useCallback)((()=>{n(!0)}),[]),a=function(e,t){return(0,o.useCallback)((n=>{t(),n&&e.current?.focus()}),[e,t])}(e,r);return{isOpen:t,onOpen:i,onClose:a}}function i(e,t){const[n,r]=(0,o.useState)(!1);return{isOpen:n,onOpen:()=>r(!0),onClose:(0,o.useCallback)(((n,o="currentLevel")=>{r(!1),"allLevels"===o?t(n,o):n&&e.current?.focus()}),[e,t])}}},625956:(e,t,n)=>{"use strict";n.d(t,{contentProps:()=>r});var o=n(241032);function r(e,t,n,r){
return{role:r,"aria-labelledby":t,"aria-multiselectable":n,id:e,[o.DATA_POSITIONER_SCROLLABLE_PART]:!0}}},19952:(e,t,n)=>{"use strict";n.d(t,{PLACEMENT_BOTTOM_ALIGN_CENTER:()=>i,PLACEMENT_BOTTOM_ALIGN_LEFT:()=>o,PLACEMENT_BOTTOM_ALIGN_RIGHT:()=>r,PLACEMENT_LEFT_ALIGN_BOTTOM:()=>m,PLACEMENT_LEFT_ALIGN_CENTER:()=>h,PLACEMENT_LEFT_ALIGN_TOP:()=>p,PLACEMENT_RIGHT_ALIGN_BOTTOM:()=>u,PLACEMENT_RIGHT_ALIGN_CENTER:()=>d,PLACEMENT_RIGHT_ALIGN_TOP:()=>c,PLACEMENT_TOP_ALIGN_CENTER:()=>l,PLACEMENT_TOP_ALIGN_LEFT:()=>a,PLACEMENT_TOP_ALIGN_RIGHT:()=>s});const o={name:"bottom-left",rtlName:"bottom-right",anchorPoint:{x:0,y:1},contentPoint:{x:0,y:0}},r={name:"bottom-right",rtlName:"bottom-left",anchorPoint:{x:1,y:1},contentPoint:{x:1,y:0}},i={name:"bottom",anchorPoint:{x:.5,y:1},contentPoint:{x:.5,y:0}},a={name:"top-left",rtlName:"top-right",anchorPoint:{x:0,y:0},contentPoint:{x:0,y:1}},s={name:"top-right",rtlName:"top-left",anchorPoint:{x:1,y:0},contentPoint:{x:1,y:1}},l={name:"top",anchorPoint:{x:.5,y:0},contentPoint:{x:.5,y:1}},c={name:"right-top",rtlName:"left-top",anchorPoint:{x:1,y:0},contentPoint:{x:0,y:0}},u={name:"right-bottom",rtlName:"left-bottom",anchorPoint:{x:1,y:1},contentPoint:{x:0,y:1}},d={name:"right",rtlName:"left",anchorPoint:{x:1,y:.5},contentPoint:{x:0,y:.5}},p={name:"left-top",rtlName:"right-top",anchorPoint:{x:0,y:0},contentPoint:{x:1,y:0}},m={name:"left-bottom",rtlName:"right-bottom",anchorPoint:{x:0,y:1},contentPoint:{x:1,y:1}},h={name:"left",rtlName:"right",anchorPoint:{x:0,y:.5},contentPoint:{x:1,y:.5}}},756036:(e,t,n)=>{"use strict";n.d(t,{isElementContainsElementThroughPortals:()=>d,isXYInsideElementThroughPortals:()=>u,useAnchorPortalPair:()=>c});var o=n(50959),r=n(650151),i=n(510695);const a=new Map;let s=0;function l(e){const t=(0,o.useCallback)((()=>{const t=(0,r.ensure)(e);return"current"in t?t.current:t()}),[e]);if(e)return t}function c(e,t){const n=l(e),r=l(t);(0,o.useEffect)((()=>{if(n&&r)return function(e,t){const n=s++;return a.set(n,[e,t]),()=>{a.delete(n)}}(n,r)}),[n,r])}function u(e,t,n){if(function(e,t,n){return(0,i.isInRect)({x:t,y:n},e.getBoundingClientRect())}(e,t,n))return!0;for(const o of a.values()){const r=o[0](),i=o[1]();if(r&&i&&e.contains(r)&&u(i,t,n))return!0}return!1}function d(e,t){if(e.contains(t))return!0;for(const n of a.values()){const o=n[0](),r=n[1]();if(o&&r&&e.contains(o)&&d(r,t))return!0}return!1}},608753:(e,t,n)=>{"use strict";function o(e){return(t,n)=>(window.addEventListener(e,t,n),()=>{window.removeEventListener(e,t,n)})}n.d(t,{windowEvent:()=>o})},312754:(e,t,n)=>{"use strict";n.d(t,{useCloseOnScrollOutside:()=>a});var o=n(990390),r=n(756036);const i=(0,n(608753).windowEvent)("scroll");function a(e,t,n){(0,o.useConditionalEffect)(e,(()=>i((e=>{const o=n.current;if(!o)return;const i=e.target;if(!i)return;if((0,r.isElementContainsElementThroughPortals)(o,i))return;(0,r.isElementContainsElementThroughPortals)(i,o)&&t(!1,"currentLevel")}),!0)),[t,n])}},603949:(e,t,n)=>{"use strict";n.d(t,{isInExcludedArea:()=>r});var o=n(510695);function r(e,t){if(!t)return!1
;const n="current"in t?t.current?.getBoundingClientRect():t;return!!n&&(0,o.isInRect)(e,n)}},914996:(e,t,n)=>{"use strict";n.d(t,{useWindowResizeTrigger:()=>s,useWindowScrollTrigger:()=>l});var o=n(50959),r=n(325414),i=n(990390);function a(e,t,n){const a=(0,o.useMemo)((()=>(0,r.createSignal)()),[]);return(0,i.useConditionalEffect)(e,(()=>(window.addEventListener(t,a.fire,n),()=>{window.removeEventListener(t,a.fire,n)})),[a]),a}const s=(e=!0)=>a(e,"resize",!0),l=(e=!0)=>a(e,"scroll",!0)},990390:(e,t,n)=>{"use strict";n.d(t,{useConditionalEffect:()=>r});var o=n(50959);function r(e,t,n){(0,o.useEffect)((()=>{if(e)return t()}),[...n,e])}},865679:(e,t,n)=>{"use strict";n.d(t,{useTabsMainHandlers:()=>b});var o=n(50959),r=n(269842),i=n(118965),a=n(586240),s=n(671129);var l=n(429510),c=n(984164),u=n(772069),d=n(525388),p=n(865968),m=n(612554),h=n(35574),f=n(664332),E=n(185727);function b(e,t,n){const{id:b,items:g,activationType:v,orientation:C,disabled:y,onActivate:O,isActive:T,overflowBehaviour:w,enableActiveStateStyles:R,tablistLabelId:P,tablistLabel:_,preventDefaultIfKeyboardActionHandled:A,stopPropagationIfKeyboardActionHandled:N,keyboardNavigationLoop:x,defaultKeyboardFocus:I,focusableItemAttributes:L}=t,S=(0,i.useMobileTouchState)(),k=function(e){const t=(0,s.useSafeMatchMedia)(a["media-mf-phone-landscape"],!0),n=(0,i.useMobileTouchState)();return e??(n||!t?"scroll":"collapse")}(w),D=(0,o.useRef)(!1),M=(0,o.useCallback)((e=>e.id),[]),B=R??!S,F=(0,m.useFadeInContext)(),{visibleItems:W,hiddenItems:H,containerRefCallback:z,innerContainerRefCallback:U,moreButtonRef:V,setItemRef:G,itemsMeasurements:K}=(0,l.useCollapsible)(g,M,T),Y=(0,E.usePrevious)(K.current?.containerWidth)??0,j=(0,h.useIsNonFirstRender)(),q=K.current?.containerWidth??0;let Q=!1;K.current&&j&&(Q=function(e,t,n,o,r){if("collapse"!==o)return!0;const i=function(e,t,n){const o=e.filter((e=>t.find((t=>t.id===e[0]))));return t.length>0?o[0][1]+n:0}(Array.from(e.widthsMap.entries()),t,r),a=e.moreButtonWidth??0;let s=function(e,t){return e.reduce(((e,n)=>e+(t.get(n.id)??0)),0)}(n,e.widthsMap);return s+=t.length>0?a:0,function(e,t,n,o){return 0!==e?t-n<e&&t-n>o:n<t}(i,e.containerWidth,s,r)}(K.current,H,W,k,n.gap??0)||0===q);const $=(0,f.useResizeObserver)((([e])=>{const t=j&&0===Y&&0===H.length;(Q&&e.contentRect.width===Y||t)&&F()})),X="collapse"===k?W:g,J=(0,o.useMemo)((()=>"collapse"===k?H:[]),[k,H]),Z=(0,o.useCallback)((e=>J.includes(e)),[J]),{isOpened:ee,open:te,close:ne,onButtonClick:oe}=(0,u.useDisclosure)({id:b,disabled:y}),{tabsBindings:re,tablistBinding:ie,scrollWrapBinding:ae,onActivate:se,onHighlight:le,isHighlighted:ce}=(0,c.useTabs)({id:b,items:[...X,...J],activationType:v,orientation:C,disabled:y,tablistLabelId:P,tablistLabel:_,preventDefaultIfKeyboardActionHandled:A,scrollIntoViewOptions:n.scrollIntoViewOptions,onActivate:O,isActive:T,isCollapsed:Z,isRtl:n.isRtl,isDisclosureOpened:ee,isRadioGroup:n.isRadioGroup,stopPropagationIfKeyboardActionHandled:N,keyboardNavigationLoop:x,defaultKeyboardFocus:I,focusableItemAttributes:L}),ue=J.find(ce),de=(0,
o.useCallback)((()=>{const e=g.find(T);e&&le(e)}),[le,T,g]),pe=(0,o.useCallback)((e=>re.find((t=>t.id===e.id))),[re]),me=(0,o.useCallback)((()=>{ne(),de(),D.current=!0}),[ne,de]),he=(0,o.useCallback)((()=>{ue&&(se(ue),le(ue,250))}),[se,le,ue]);ae.ref=(0,d.useMergedRefs)([$,ae.ref,z]),ie.ref=(0,d.useMergedRefs)([ie.ref,U]),ie.onKeyDown=(0,r.createSafeMulticastEventHandler)((0,p.useKeyboardEventHandler)([(0,p.useKeyboardClose)(ee,me),(0,p.useKeyboardActionHandler)([13,32],he,(0,o.useCallback)((()=>Boolean(ue)),[ue]))],A),ie.onKeyDown);const fe=(0,o.useCallback)((e=>{D.current=!0,oe(e)}),[D,oe]),Ee=(0,o.useCallback)((e=>{e&&se(e)}),[se]);return(0,o.useEffect)((()=>{D.current?D.current=!1:(ue&&!ee&&te(),!ue&&ee&&ne())}),[ue,ee,te,ne]),{enableActiveStateStyles:B,moreButtonRef:V,setItemRef:G,getBindings:pe,handleMoreButtonClick:fe,handleCollapsedItemClick:Ee,scrollWrapBinding:ae,overflowBehaviour:k,tablistBinding:ie,visibleTabs:X,hiddenTabs:J,handleActivate:se,isMobileTouch:S,getItemId:M,isDisclosureOpened:ee,isHighlighted:ce,closeDisclosure:ne}}},224567:(e,t,n)=>{"use strict";n.d(t,{HotkeyBox:()=>i,Tooltip:()=>N,TooltipStateless:()=>w,Trigger:()=>R});var o=n(50959),r=n(281005);function i({keyboardKey:e}){return o.createElement("span",{className:r.hotkeyBox},e)}var a=n(497754),s=n.n(a),l=n(584811),c=n(799573);const u=e=>o.createElement("svg",{width:"16",height:"5",viewBox:"0 0 16 5",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{d:"M.5 5h15L8 0 .5 5Z",fill:"#363A45"})),d=({placement:e,...t})=>o.createElement("div",{className:s()(r.floatingArrow,e&&r[e],c.isSafari&&r.isSafari),...t},o.createElement(u,null));var p;function m({content:e,isRtl:t}){return o.createElement("span",{className:s()(r.endSlotWrapper,t&&r.endSlotRtl)},o.createElement(l.Divider,{className:r.divider,orientation:"vertical",type:"secondary",size:"xsmall"}),o.createElement("span",{className:s()(r.endSlotContent,t&&r.endSlotContentRtl)},e))}function h({id:e,content:t,endSlot:n,hasArrow:i=!0,isRtl:a=!1,placement:l="top",disableInteractive:c=!1,...u}){return o.createElement("div",{id:e,role:"tooltip",className:s()(r.tooltipBaseWrapper,c&&r.disableInteractive)},i&&o.createElement(d,{...u,placement:l}),o.createElement("div",{className:r.tooltipBase},o.createElement("div",{className:s()(r.contentWrapper,a&&r.contentRtl)},o.createElement("div",{className:r.content},t),n&&o.createElement(m,{content:n,isRtl:a}))))}!function(e){e.TopLeft="top-left",e.Top="top",e.TopRight="top-right",e.BottomLeft="bottom-left",e.Bottom="bottom",e.BottomRight="bottom-right",e.RightTop="right-top",e.Right="right",e.RightBottom="right-bottom",e.LeftTop="left-top",e.Left="left",e.LeftBottom="left-bottom"}(p||(p={}));var f=n(19952),E=n(459993),b=n(312754),g=n(914996),v=n(985098),C=n(133375),y=n(718736);const O={"bottom-left":[f.PLACEMENT_BOTTOM_ALIGN_LEFT],bottom:[f.PLACEMENT_BOTTOM_ALIGN_CENTER],"bottom-right":[f.PLACEMENT_BOTTOM_ALIGN_RIGHT],"top-left":[f.PLACEMENT_TOP_ALIGN_LEFT],top:[f.PLACEMENT_TOP_ALIGN_CENTER],"top-right":[f.PLACEMENT_TOP_ALIGN_RIGHT],
"right-top":[f.PLACEMENT_RIGHT_ALIGN_TOP],right:[f.PLACEMENT_RIGHT_ALIGN_CENTER],"right-bottom":[f.PLACEMENT_RIGHT_ALIGN_BOTTOM],"left-top":[f.PLACEMENT_LEFT_ALIGN_TOP],left:[f.PLACEMENT_LEFT_ALIGN_CENTER],"left-bottom":[f.PLACEMENT_LEFT_ALIGN_BOTTOM]},T={pickRule:{by:"smallest-overlap",criteria:"area"},horizontal:"slideToFit",vertical:"slideToFit"},w=(0,E.ignoreOnSSR)((function(e){const{anchor:t,open:n,id:i,content:a,endSlot:s,hasArrow:l=!0,isRtl:c=!1,onClose:u,placement:d=["top","bottom","left","right"],offset:p=0,closeOnScrollOutside:m=!1,disableInteractive:f}=e,E=l?4:p,w=(0,y.useFunctionalRefObject)((0,o.useRef)(null)),R=((e,t)=>(Array.isArray(e)?e:[e]).flatMap((e=>((e,t)=>e.map((e=>{const n={top:{x:0,y:-t},right:{x:t,y:0},bottom:{x:0,y:t},left:{x:-t,y:0}};return e.name?.split("-")[0]?{...e,offset:n[e.name.split("-")[0]]}:e})))(O[e],t))))(d,E),[P,_]=o.useState(R[0]),A=(0,o.useCallback)((e=>{P?.name!==e?.name&&n&&_(e)}),[n,P?.name]),N=(0,g.useWindowResizeTrigger)(n),x=(0,g.useWindowScrollTrigger)(!m&&n);(0,b.useCloseOnScrollOutside)(m&&n,u,t);const I=(0,o.useMemo)((()=>[N,x]),[x,N]),L=(0,C.useRefRect)(t,n,I);return o.createElement(v.RectanglePositioner,{placementRules:R,ifNonePlacementRuleMatched:T,isOpen:n,width:[],height:[],anchoredAt:L,isRtl:c,refObjectToContentWrapperElement:w,onPlacementRuleSelected:A,anchorPositionerCardAppearanceClassname:r.tooltip},o.createElement(h,{id:i,content:a,endSlot:s,hasArrow:l,isRtl:c,placement:P?.name,disableInteractive:f,"data-tooltip-inside-positioner":!0}))}));var R,P=n(953757),_=n(273388),A=n(701928);function N({children:e,id:t,trigger:n=R.Hover,delay:r=500,content:i,isRtl:a=!1,endSlot:s,placement:l,hasArrow:c=!0,closeOnScrollOutside:u,offset:d,disableInteractive:p}){const m=(0,o.useId)(),h=Array.isArray(n)?n:[n],[f,E]=Array.isArray(r)?r:[r,r],{visible:b,open:g,close:v,toggle:C}=(0,P.useTooltipState)({openDelay:f,closeDelay:E}),y=(0,o.useRef)(null),O=o.Children.only(e),T=h.reduce(((e,t)=>{switch(t){case R.Click:e.onClick=(0,A.forkFn)(O.props.onClick,C);break;case R.Focus:e.onFocus=(0,A.forkFn)(O.props.onFocus,(e=>{e.currentTarget.matches(":focus-visible")&&g()})),e.onBlur=(0,A.forkFn)(O.props.onBlur,(e=>{e.currentTarget.matches(":hover")||v()}));break;case R.Hover:e.onMouseEnter=(0,A.forkFn)(O.props.onMouseEnter,g),e.onMouseLeave=(0,A.forkFn)(O.props.onMouseLeave,(e=>{y.current?.contains(document.activeElement)&&e.currentTarget.matches(":focus-visible")||v()}))}return e}),{}),N="string"==typeof O.type?"ref":"reference",x="ref"in O&&null!=O.ref?O.ref:O.props[N],I=(0,_.mergeRefs)([x,y]);return o.createElement(o.Fragment,null,(0,o.cloneElement)(O,{...T,[N]:I,"aria-describedby":t}),o.createElement(w,{id:t||m,placement:l,content:i,endSlot:s,open:b,anchor:y,onClose:v,isRtl:a,hasArrow:c,closeOnScrollOutside:u,offset:d,disableInteractive:p}))}!function(e){e.Hover="hover",e.Click="click",e.Focus="focus"}(R||(R={}))},953757:(e,t,n)=>{"use strict";n.d(t,{useTooltipState:()=>r});var o=n(50959);function r(e){const{openDelay:t=0,closeDelay:n=0}=e,[r,i]=(0,o.useState)(!1),a=(0,
o.useRef)(),s=(0,o.useRef)(),l=e=>{e.current&&(clearTimeout(e.current),e.current=void 0)},c=(0,o.useCallback)((e=>{l(e?s:a);const o=e?t:n,r=e?a:s;o>0?r.current=window.setTimeout((()=>{i(e),r.current=void 0}),o):i(e)}),[t,n]),u=(0,o.useCallback)((()=>c(!0)),[c]),d=(0,o.useCallback)((()=>c(!1)),[c]),p=(0,o.useCallback)((()=>c(!r)),[r,c]),m=(0,o.useCallback)((()=>{l(s),i(!1)}),[]);return(0,o.useEffect)((()=>()=>{l(a),l(s)}),[]),{visible:r,open:u,close:d,toggle:p,closeInstantly:m}}},701928:(e,t,n)=>{"use strict";function o(...e){return(...t)=>{e.forEach((e=>e?.(...t)))}}n.d(t,{forkFn:()=>o})},459993:(e,t,n)=>{"use strict";n.d(t,{ignoreOnSSR:()=>i});var o=n(50959),r=n(35574);function i(e){return t=>(0,r.useIsNonFirstRender)()?o.createElement(e,{...t}):null}},865081:(e,t,n)=>{"use strict";n.d(t,{getSelectorKeyboardNavigation:()=>i});var o=n(745845),r=n(32556);function i(e,t){const n=t=>(n,i)=>{const a=document.activeElement||n,s=(0,o.getSelectorTreeWalker)(n,e);s.currentNode=a;const l=t(s);return l&&(0,r.isHTMLOrSVGElement)(l)&&l!==a?(l.focus(i),l):null},i=t?e=>e.previousNode():o.getPreviousNonParent,a=e=>e.nextNode();return{focusPrev:n(i),focusNext:n(a),focusFirst:n(o.getFirstDescendant),focusLast:n(o.getLastDescendant),focusPrevLoop:n((e=>{const t=i(e);return t||(0,o.getLastDescendant)(e)})),focusNextLoop:n((e=>{const t=a(e);return t||(0,o.getFirstDescendant)(e)})),focusIndex:(e,t,o)=>n((e=>{e.currentNode=e.root;for(let n=0;n<t;n++)e.nextNode();return e.nextNode()}))(e,o)}}},325414:(e,t,n)=>{"use strict";function o(){const e=new Set;return{subscribe:t=>{e.add(t)},unsubscribe:t=>{e.delete(t)},fire:()=>{e.forEach((e=>e()))}}}n.d(t,{createSignal:()=>o})},588714:(e,t,n)=>{"use strict";n.d(t,{ClickableIconButton:()=>l});var o=n(50959),r=n(497754),i=n(80503),a=n(592790),s=n(372764);const l=(0,o.forwardRef)(((e,t)=>{const{title:n,colorClassName:l,iconClassName:c,icon:u,variant:d=i.DEFAULT_ICON_BUTTON_VARIANT,shape:p="square",...m}=e;return o.createElement("button",{...m,ref:t,role:"button",title:n,"aria-label":n,className:r(s.iconButton,s[d],s[p],l,"apply-common-tooltip")},o.createElement(a.BlockIcon,{icon:u,className:r(s.icon,c),"aria-hidden":"true"}))}))},163694:(e,t,n)=>{"use strict";n.d(t,{DrawerContext:()=>a,DrawerManager:()=>i});var o=n(50959),r=n(285089);class i extends o.PureComponent{constructor(e){super(e),this._isBodyFixed=!1,this._addDrawer=e=>{this.setState((t=>({stack:[...t.stack,e]})))},this._removeDrawer=e=>{this.setState((t=>({stack:t.stack.filter((t=>t!==e))})))},this.state={stack:[]}}componentDidUpdate(e,t){!t.stack.length&&this.state.stack.length&&((0,r.setFixedBodyState)(!0),this._isBodyFixed=!0),t.stack.length&&!this.state.stack.length&&this._isBodyFixed&&((0,r.setFixedBodyState)(!1),this._isBodyFixed=!1)}componentWillUnmount(){this.state.stack.length&&this._isBodyFixed&&(0,r.setFixedBodyState)(!1)}render(){return o.createElement(a.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.stack.length?this.state.stack[this.state.stack.length-1]:null}
},this.props.children)}}const a=o.createContext(null)},759339:(e,t,n)=>{"use strict";n.d(t,{Drawer:()=>m});var o=n(50959),r=n(650151),i=n(497754),a=n(924910),s=n(8361),l=n(163694),c=n(28466),u=n(742554),d=n(536718);var p;function m(e){const{position:t="Bottom",onClose:n,children:u,reference:p,className:m,theme:f=d}=e,E=(0,r.ensureNotNull)((0,o.useContext)(l.DrawerContext)),[b]=(0,o.useState)((()=>(0,a.randomHash)())),g=(0,o.useRef)(null),v=(0,o.useContext)(c.CloseDelegateContext);return(0,o.useLayoutEffect)((()=>((0,r.ensureNotNull)(g.current).focus({preventScroll:!0}),v.subscribe(E,n),E.addDrawer(b),()=>{E.removeDrawer(b),v.unsubscribe(E,n)})),[]),o.createElement(s.Portal,null,o.createElement("div",{ref:p,className:i(d.wrap,d[`position${t}`])},b===E.currentDrawer&&o.createElement("div",{className:d.backdrop,onClick:n}),o.createElement(h,{className:i(f.drawer,d[`position${t}`],m),ref:g,"data-name":e["data-name"]},u)))}!function(e){e.Left="Left",e.Bottom="Bottom"}(p||(p={}));const h=(0,o.forwardRef)(((e,t)=>{const{className:n,...r}=e;return o.createElement(u.TouchScrollContainer,{className:i(d.drawer,n),tabIndex:-1,ref:t,...r})}))},661851:(e,t,n)=>{"use strict";n.d(t,{useObservable:()=>i,useObservableSubscription:()=>r});var o=n(50959);function r(e,t){(0,o.useEffect)((()=>{const n=e.subscribe(t);return()=>n.unsubscribe()}),[e,t])}function i(e,t){const n=(0,o.useCallback)(void 0!==t?()=>t:()=>{let t;return e.subscribe((e=>{t=e})).unsubscribe(),t},[t,e]),[i,a]=(0,o.useState)(n),s=(0,o.useCallback)((e=>a((()=>e))),[a]);return r(e,s),i}},379266:(e,t,n)=>{"use strict";n.d(t,{PopupMenuItemToggle:()=>l});var o=n(50959),r=n(497754),i=n(192063),a=n(302946),s=n(840638);function l(e){const{isDisabled:t,hint:n,label:l,isChecked:c,checkboxClassName:u,labelClassName:d,indeterminate:p,isActive:m,checkboxTabIndex:h,checkboxReference:f,checkboxDataRole:E,checkboxDataName:b,...g}=e;return o.createElement(i.PopupMenuItem,{...g,isDisabled:t,shortcut:n,dontClosePopup:!0,labelRowClassName:d,label:o.createElement(a.Checkbox,{reference:f,disabled:t,label:l,checked:c,indeterminate:p,className:r(s.checkbox,u),tabIndex:h,"data-role":E,"data-name":b})})}},730743:(e,t,n)=>{"use strict";n.d(t,{calcSubMenuPos:()=>r});var o=n(431520);function r(e,t,n={x:0,y:10}){if(t){const{left:n,right:r,top:i}=t.getBoundingClientRect(),a=document.documentElement.clientWidth,s={x:n-e,y:i},l={x:r,y:i};return(0,o.isRtl)()?n<=e?l:s:a-r>=e?l:s}return n}},515783:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetCaret:()=>l});var o=n(50959),r=n(497754),i=n(878112),a=n(149128),s=n(100578);function l(e){const{dropped:t,className:n}=e;return o.createElement(i.Icon,{className:r(n,a.icon,{[a.dropped]:t}),icon:s})}},438576:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},295389:e=>{
e.exports={button:"button-merBkM5y",hover:"hover-merBkM5y",clicked:"clicked-merBkM5y",accessible:"accessible-merBkM5y",arrow:"arrow-merBkM5y",arrowWrap:"arrowWrap-merBkM5y",isOpened:"isOpened-merBkM5y"}},992308:e=>{e.exports={toggle:"toggle-NYDT3E70",label:"label-NYDT3E70"}},55859:e=>{e.exports={dropdownButton:"dropdownButton-NgHNzdZC",bigIcon:"bigIcon-NgHNzdZC"}},139535:e=>{e.exports={mobile:"(max-width: 567px)"}},162458:(e,t,n)=>{"use strict";n.d(t,{HorizontalAttachEdge:()=>r,HorizontalDropDirection:()=>a,VerticalAttachEdge:()=>o,VerticalDropDirection:()=>i,getPopupPositioner:()=>c});var o,r,i,a,s=n(650151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(o||(o={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(r||(r={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(i||(i={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(a||(a={}));const l={verticalAttachEdge:o.Bottom,horizontalAttachEdge:r.Left,verticalDropDirection:i.FromTopToBottom,horizontalDropDirection:a.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(e,t){return n=>{const{contentWidth:c,contentHeight:u,availableHeight:d}=n,p=(0,s.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:m=l.horizontalAttachEdge,horizontalDropDirection:h=l.horizontalDropDirection,horizontalMargin:f=l.horizontalMargin,verticalMargin:E=l.verticalMargin,matchButtonAndListboxWidths:b=l.matchButtonAndListboxWidths}=t;let g=t.verticalAttachEdge??l.verticalAttachEdge,v=t.verticalDropDirection??l.verticalDropDirection;g===o.AutoStrict&&(d<p.y+p.height+E+u?(g=o.Top,v=i.FromBottomToTop):(g=o.Bottom,v=i.FromTopToBottom));const C=g===o.Top?-1*E:E,y=m===r.Right?p.right:p.left,O=g===o.Top?p.top:p.bottom,T={x:y-(h===a.FromRightToLeft?c:0)+f,y:O-(v===i.FromBottomToTop?u:0)+C};return b&&(T.overrideWidth=p.width),T}}},155352:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>s,ToolWidgetButton:()=>l});var o=n(50959),r=n(497754),i=n(878112),a=n(438576);const s=a,l=o.forwardRef(((e,t)=>{const{tag:n="div",icon:s,endIcon:l,isActive:c,isOpened:u,isDisabled:d,isGrouped:p,isHovered:m,isClicked:h,onClick:f,text:E,textBeforeIcon:b,title:g,theme:v=a,className:C,forceInteractive:y,inactive:O,"data-name":T,"data-tooltip":w,...R}=e,P=r(C,v.button,(g||w)&&"apply-common-tooltip",{[v.isActive]:c,[v.isOpened]:u,[v.isInteractive]:(y||Boolean(f))&&!d&&!O,[v.isDisabled]:Boolean(d||O),[v.isGrouped]:p,[v.hover]:m,[v.clicked]:h}),_=s&&("string"==typeof s?o.createElement(i.Icon,{className:v.icon,icon:s}):o.cloneElement(s,{className:r(v.icon,s.props.className)}));return"button"===n?o.createElement("button",{...R,ref:t,type:"button",className:r(P,v.accessible),disabled:d&&!O,onClick:f,title:g,"data-name":T,"data-tooltip":w},b&&E&&o.createElement("div",{className:r("js-button-text",v.text)},E),_,!b&&E&&o.createElement("div",{className:r("js-button-text",v.text)},E)):o.createElement("div",{...R,ref:t,
"data-role":"button",className:P,onClick:d?void 0:f,title:g,"data-name":T,"data-tooltip":w},b&&E&&o.createElement("div",{className:r("js-button-text",v.text)},E),_,!b&&E&&o.createElement("div",{className:r("js-button-text",v.text)},E),l&&o.createElement(i.Icon,{icon:l,className:a.endIcon}))}))},679458:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_TOOL_WIDGET_MENU_THEME:()=>b,ToolWidgetMenu:()=>v});var o=n(50959),r=n(497754),i=n.n(r),a=n(930202),s=n(624216),l=n(515783),c=n(800417),u=n(163694),d=n(759339),p=n(162458),m=n(930052),h=n(440891),f=n(111706),E=n(295389);const b=E;var g;!function(e){e[e.Vertical=2]="Vertical",e[e.Horizontal=0]="Horizontal"}(g||(g={}));class v extends o.PureComponent{constructor(e){super(e),this._wrapperRef=null,this._controller=o.createRef(),this._onPopupCloseOnClick=e=>{"keyboard"===e.detail.clickType&&this.focus()},this._handleMenuFocus=e=>{e.relatedTarget===this._wrapperRef&&this.setState((e=>({...e,isOpenedByButton:!0}))),this.props.onMenuFocus?.(e)},this._handleWrapperRef=e=>{this._wrapperRef=e,this.props.reference&&this.props.reference(e)},this._handleOpen=()=>{"div"!==this.props.tag&&(this.setState((e=>({...e,isOpenedByButton:!1}))),this.props.menuReference?.current?.addEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._controller.current?.focus())},this._handleClick=e=>{(h.enabled("skip_event_target_check")||e.target instanceof Node)&&e.currentTarget.contains(e.target)&&(this._handleToggleDropdown(void 0,(0,f.isKeyboardClick)(e)),this.props.onClick&&this.props.onClick(e,!this.state.isOpened))},this._handleToggleDropdown=(e,t=!1)=>{const{onClose:n,onOpen:o}=this.props,{isOpened:r}=this.state,i="boolean"==typeof e?e:!r;this.setState({isOpened:i,shouldReturnFocus:!!i&&t}),i&&o&&o(),!i&&n&&n()},this._handleClose=()=>{this.close()},this._handleKeyDown=e=>{const{orientation:t="horizontal"}=this.props;if(e.defaultPrevented)return;if(!(e.target instanceof Node))return;const n=(0,a.hashFromEvent)(e);if(e.currentTarget.contains(e.target))switch(n){case 40:if("div"===this.props.tag||"horizontal"!==t)return;if(this.state.isOpened)return;e.preventDefault(),this._handleToggleDropdown(!0,!0);break;case 27:if(!this.state.isOpened||!this.props.closeOnEsc)return;e.preventDefault(),e.stopPropagation(),this._handleToggleDropdown(!1)}else{if("div"===this.props.tag)return;switch(n){case 27:{e.preventDefault();const{shouldReturnFocus:t,isOpenedByButton:n}=this.state;this._handleToggleDropdown(!1),t&&n&&this._wrapperRef?.focus();break}}}},this.state={isOpened:!1,shouldReturnFocus:!1,isOpenedByButton:!1}}render(){const{tag:e="div",id:t,arrow:n,content:r,isDisabled:a,isDrawer:s,isShowTooltip:u,title:d,className:p,hotKey:h,theme:f,drawerBreakpoint:E,tabIndex:b,isClicked:g}=this.props,{isOpened:v}=this.state,y=i()(p,f.button,{"apply-common-tooltip":u||!a,[f.isDisabled]:a,[f.isOpened]:v,[f.clicked]:g}),O=C(r)?r({isOpened:v}):r;return"button"===e?o.createElement("button",{type:"button",id:t,className:i()(y,f.accessible),disabled:a,onClick:this._handleClick,title:d,"data-tooltip-hotkey":h,
ref:this._handleWrapperRef,onKeyDown:this._handleKeyDown,tabIndex:b,...(0,c.filterDataProps)(this.props),...(0,c.filterAriaProps)(this.props)},O,n&&o.createElement("div",{className:f.arrow},o.createElement("div",{className:f.arrowWrap},o.createElement(l.ToolWidgetCaret,{dropped:v}))),this.state.isOpened&&(E?o.createElement(m.MatchMedia,{rule:E},(e=>this._renderContent(e))):this._renderContent(s))):o.createElement("div",{id:t,className:y,onClick:a?void 0:this._handleClick,title:d,"data-tooltip-hotkey":h,ref:this._handleWrapperRef,"data-role":"button",tabIndex:b,onKeyDown:this._handleKeyDown,"aria-haspopup":this.props["aria-haspopup"],...(0,c.filterDataProps)(this.props)},O,n&&o.createElement("div",{className:f.arrow},o.createElement("div",{className:f.arrowWrap},o.createElement(l.ToolWidgetCaret,{dropped:v}))),this.state.isOpened&&(E?o.createElement(m.MatchMedia,{rule:E},(e=>this._renderContent(e))):this._renderContent(s)))}close(){this.props.menuReference?.current?.removeEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._handleToggleDropdown(!1)}focus(){this._wrapperRef?.focus()}update(){null!==this._controller.current&&this._controller.current.update()}_renderContent(e){const{menuDataName:t,minWidth:n,menuClassName:r,menuRole:i,maxHeight:a,drawerPosition:l="Bottom",children:c,noMomentumBasedScroll:m}=this.props,{isOpened:h}=this.state,f={horizontalMargin:this.props.horizontalMargin||0,verticalMargin:this.props.verticalMargin||2,verticalAttachEdge:this.props.verticalAttachEdge,horizontalAttachEdge:this.props.horizontalAttachEdge,verticalDropDirection:this.props.verticalDropDirection,horizontalDropDirection:this.props.horizontalDropDirection,matchButtonAndListboxWidths:this.props.matchButtonAndListboxWidths},E=Boolean(h&&e&&l),b=C(c)?c({isDrawer:E}):c;return E?o.createElement(u.DrawerManager,null,o.createElement(d.Drawer,{reference:this.props.drawerReference,onClose:this._handleClose,position:l,"data-name":t},b)):o.createElement(s.PopupMenu,{reference:this.props.menuReference,role:i,controller:this._controller,closeOnClickOutside:this.props.closeOnClickOutside,doNotCloseOn:this,isOpened:h,minWidth:n,onClose:this._handleClose,position:(0,p.getPopupPositioner)(this._wrapperRef,f),className:r,maxHeight:a,"data-name":t,tabIndex:"div"!==this.props.tag?-1:void 0,onOpen:this._handleOpen,onKeyDown:this.props.onMenuKeyDown,onFocus:this._handleMenuFocus,noMomentumBasedScroll:m},b)}}function C(e){return"function"==typeof e}v.defaultProps={arrow:!0,closeOnClickOutside:!0,theme:E}},628640:(e,t,n)=>{"use strict";n.d(t,{TerminalDropdown:()=>D});var o=n(50959),r=n(609838),i=n(497754),a=n.n(i),s=n(601227),l=n(878112),c=n(679458),u=n(155352),d=n(192063),p=n(669874);const m=o.lazy((async()=>({default:(await Promise.all([n.e(1419),n.e(4524),n.e(5816),n.e(1356),n.e(9258),n.e(5387),n.e(6445),n.e(6489),n.e(8222),n.e(2106),n.e(422),n.e(3368),n.e(9216),n.e(2985)]).then(n.bind(n,224511))).ExportDataDialogImpl})));function h(e){return o.createElement(o.Suspense,{fallback:null},o.createElement(m,{...e}))}
var f=n(661851),E=n(379266),b=n(624216),g=n(759339),v=n(924910),C=n(972535),y=n(823030),O=n(522224),T=n(730743);var w=n(930052),R=n(642739),P=n(379978);function _(e){return o.createElement(w.MatchMedia,{rule:"(max-width: 440px)"},(t=>t||e.forceUseDrawer?o.createElement(N,{...e}):o.createElement(x,{...e})))}function A(e){const{toolboxLabel:t,...n}=e;return o.createElement(d.PopupMenuItem,{toolbox:o.createElement(o.Fragment,null,t&&o.createElement("span",{className:R.toolboxLabel},t),o.createElement(l.Icon,{className:R.icon,icon:P})),suppressToolboxClick:!1,dontClosePopup:!0,...n})}function N(e){const{children:t,className:n,drawerClassName:r,menuItemTheme:i,label:a,icon:s,labelClassName:l,toolboxLabel:c}=e,[u,d]=(0,o.useState)(!1);return o.createElement(o.Fragment,null,o.createElement(A,{theme:i,label:a,toolboxLabel:c,icon:s,labelClassName:l,className:n,onClick:function(){d(!u)}}),u&&o.createElement(g.Drawer,{className:r,position:"Bottom",onClose:function(){d(!1)}},t))}function x(e){const{children:t,className:n,menuClassName:r,menuItemTheme:i,label:a,icon:s,labelClassName:l,toolboxLabel:c}=e,u=(0,o.useRef)((0,v.randomHash)()),d=(0,o.useRef)(null),{isMenuOpened:p,onTriggerClick:m,onTriggerMouseOver:h,onMenuClose:f,getPosition:E,onMenuMouseOver:g}=function(e,t,n={}){const r=o.useContext(y.SubmenuContext);o.useEffect((()=>r?.registerSubmenu(e,s)),[e]);const i=Boolean(r?.current===e);return{isMenuOpened:i,onTriggerClick:function(){(C.mobiletouch||n.forceOpenOnClick)&&r?.setCurrent(i?null:e)},onMenuClose:function(){r?.setCurrent(null)},getPosition:function(e){return(0,T.calcSubMenuPos)(e.contentWidth,t.current)},onMenuMouseOver:a,onTriggerMouseOver:a};function a(t){!C.mobiletouch&&(0,O.hoverMouseEventFilter)(t)&&r?.setCurrent(e)}function s(e){return Boolean(t.current?.contains(e))}}(u.current,d);return o.createElement(o.Fragment,null,o.createElement(A,{theme:i,label:a,toolboxLabel:c,icon:s,labelClassName:l,className:n,onClick:m,onMouseOver:h,reference:d,isHovered:p}),o.createElement(b.PopupMenu,{className:r,position:E,onClose:f,isOpened:p,doNotCloseOn:d.current,onMouseOver:g},t))}var I=n(992308);function L(e){const{labelClassName:t,summaryFieldsVisibilityInfo$:i,summaryFieldToggler:a,initialSummaryFieldsVisibilityInfo:l}=e,c=(0,f.useObservable)(i,l);return 0===c.size?o.createElement(o.Fragment,null):o.createElement(_,{label:r.t(null,void 0,n(370559)),labelClassName:t,forceUseDrawer:s.CheckMobile.any()},[...c.values()].map((e=>o.createElement(E.PopupMenuItemToggle,{key:e.id,className:I.toggle,label:o.createElement("span",{className:I.label},e.id),isChecked:e.visible,onClick:()=>a(e.id)}))))}var S=n(55859),k=n(48008);function D(e){const{dataExportController:t,initialSummaryFieldsVisibilityInfo:i,summaryFieldToggler:m,summaryFieldsVisibilityInfo$:f,iconSize:E}=e,[b,g]=function(e){const{dataExportController:t,trackEvent:n}=e,[r,i]=(0,o.useState)(!1);return void 0===t?[()=>{},null]:[()=>i(!0),o.createElement(h,{isOpened:r,onClose:()=>i(!1),trackEvent:n,dataExportController:t})]}({dataExportController:t})
;return o.createElement(o.Fragment,null,o.createElement(c.ToolWidgetMenu,{arrow:!1,isDrawer:s.CheckMobile.any(),className:S.dropdownButton,content:o.createElement(u.ToolWidgetButton,{title:r.t(null,void 0,n(437117)),icon:o.createElement(l.Icon,{className:a()("big"===E&&S.bigIcon),icon:k})}),"data-name":"terminal-dropdown"},o.createElement(L,{labelClassName:S.label,summaryFieldToggler:m,summaryFieldsVisibilityInfo$:f,initialSummaryFieldsVisibilityInfo:i}),null!==g&&o.createElement(d.PopupMenuItem,{labelClassName:S.label,label:(0,p.appendEllipsis)(r.t(null,void 0,n(778837))),onClick:b})),g)}},628370:(e,t,n)=>{"use strict";n.d(t,{makeAccountManagerHeaderDropdownsProps:()=>h});var o=n(275734),r=n(757604),i=n(650151),a=n(391431),s=n(671945),l=n(674765),c=n(752275),u=n(404357);const d=(0,s.getLogger)("Trading.DataExport");class p{constructor(e,t){this._prefix=t,this._getDataExporters=e}tabs(){return[...this._getDataExporters()].map((([e,t])=>({value:e,content:t.title})))}async exportData(e){const{exporters:t,title:n}=(0,i.ensureDefined)(this._getDataExporters().get(e),"data exporter");try{const e=await Promise.all(t.map((({exportData:e})=>e())));e.forEach(((o,r)=>{const i=t[r].name,a=void 0===i||""===i?`${(0,l.default)(n)}${e.length>1?`-${r+1}`:""}`:(0,l.default)(i);let s="";if(0!==o.length){const e=[m(Object.keys(o[0]))];for(const t of o)e.push(m(Object.values(t)));s=e.join("\n")}(0,c.saveTextFile)(`${(0,l.default)(this._prefix)}-${a}-${(new Date).toISOString()}.csv`,s,"text/csv")}))}catch(e){d.logError((0,u.getLoggerMessage)(e))}}}function m(e){return e.map((e=>"number"==typeof e?e:(0,c.escapeCSVValue)(e))).join(",")}async function h(e,t,n,s){const l=(await e.brokersMetainfo()).filter((e=>!e.configFlags.isSuspended)),c=Promise.resolve(void 0),u=(0,i.ensureNotNull)(e.activeBroker()),d=await c,m=await u.accountsMetainfo(),h=u.accountManagerInfo();if(0===m.length)return;const f=m.map((e=>({id:e.id,name:e.name,currency:""===e.currency?void 0:e.currency??void 0,isDefault:e.isDefault,label:e.label,labelLink:e.labelLink,labelTooltipText:e.labelTooltipText,selectAccount:async()=>{e.id!==u.currentAccount()&&u.setCurrentAccount(e.id)}}))),E=e=>(0,i.ensureDefined)(f.find((t=>t.id===e))),b=(0,o.fromEventPattern)((e=>u.currentAccountUpdate.subscribe(null,e)),(e=>u.currentAccountUpdate.unsubscribe(null,e)),E).pipe((0,r.startWith)(E(u.currentAccount()))),g=u.metainfo(),v=void 0!==d?(0,a.brokersListFromPlans)(l,d):void 0,C=void 0!==v?{title:h.accountTitle,brokerName:g.title,brokerId:g.id,logo:g.logoMiniUrl,logoBlack:g.logoMiniBlackUrl,actions:u.buttonDropdownActions(),trading:e,brokers:v,mode:n,isBeta:g.isBeta,realtimeDataPermissionsConfig:g.realtimeDataPermissionsToggleConfig}:void 0,y={dataExportController:void 0!==s?new p(s,g.title):void 0,initialSummaryFieldsVisibilityInfo:t.fieldsVisibilityInfo(),summaryFieldsVisibilityInfo$:t.fieldsVisibilityInfo$,summaryFieldToggler:t.toggleField};return{brokerDropdownProps:C,accountDropdownProps:{currentAccount$:b,accountsList:f,removeMargins:void 0===C,trackEvent:e.trackEvent,
showErrorNotification:e.showErrorNotification,mode:n,createAccount:u.config.supportCreateAccount?u.createAccount:void 0,deleteAccount:u.config.supportDeleteAccount?u.deleteAccount:void 0,resetAccount:u.config.supportResetAccount?u.resetAccount:void 0,changeAccountSettings:u.config.supportChangeAccountSettings?u.changeAccountSettings:void 0,accountSettingsInfo:u.config.supportCreateAccount||u.config.supportResetAccount||u.config.supportChangeAccountSettings?u.accountSettingsInfo:void 0,accountHintOptions:h.accountHintOptions},commonDropdownProps:y}}},332510:(e,t,n)=>{"use strict";n.r(t),n.d(t,{SummaryFieldsVisibilityManager:()=>r});var o=n(947488);class r{constructor(e,t){this.toggleField=e=>{const t=this._fieldsVisibilityInfo$.getValue(),n=new Map(t),o=n.get(e);void 0!==o&&(o.visible=!o.visible,this._fieldsVisibilityInfo$.next(n),this._settingsGetter()?.setSummaryFieldsVisibilityInfo(n))},this._settingsGetter=t;const n=this._settingsGetter()?.summaryFieldsVisibilityInfo();this._fieldsVisibilityInfo$=new o.BehaviorSubject(new Map(e.map((({text:e,isDefault:t})=>[e,{id:e,visible:n?.get(e)?.visible??t??!0}])))),this.fieldsVisibilityInfo$=this._fieldsVisibilityInfo$.asObservable()}fieldsVisibilityInfo(){return this._fieldsVisibilityInfo$.getValue()}}},904664:(e,t,n)=>{"use strict";n.d(t,{BracketType:()=>r.BracketType,OrderOrPositionMessageType:()=>r.OrderOrPositionMessageType});n(46415);var o,r=n(599016);!function(e){e.Date="date",e.DateOrDateTime="dateOrDateTime",e.Default="default",e.Fixed="fixed",e.FixedInCurrency="fixedInCurrency",e.VariablePrecision="variablePrecision",e.FormatQuantity="formatQuantity",e.FormatPrice="formatPrice",e.FormatPriceForexSup="formatPriceForexSup",e.FormatPriceInCurrency="formatPriceInCurrency",e.IntegerSeparated="integerSeparated",e.LocalDate="localDate",e.LocalDateOrDateTime="localDateOrDateTime",e.Percentage="percentage",e.Pips="pips",e.Profit="profit",e.ProfitInInstrumentCurrency="profitInInstrumentCurrency",e.Side="side",e.PositionSide="positionSide",e.Status="status",e.Symbol="symbol",e.Text="text",e.Type="type",e.MarginPercent="marginPercent",e.Empty="empty"}(o||(o={}))},403933:(e,t,n)=>{"use strict";n.d(t,{TradingLayoutBreakpoint:()=>r});var o=n(139535);const r={Mobile:o.mobile}},743003:(e,t,n)=>{"use strict";function o(e){return e.charAt(0).toUpperCase()+e.substring(1)}n.d(t,{capitalizeFirstLetter:()=>o})},48008:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M3 10a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm0 1a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm6-1a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm0 1a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm7-2a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm1 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"/></svg>'},100578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},379978:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'}}]);