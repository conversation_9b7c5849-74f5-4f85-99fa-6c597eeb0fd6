(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7502],{654767:e=>{e.exports={wrap:"wrap-Un5MtNUE",list:"list-Un5MtNUE",item:"item-Un5MtNUE",selected:"selected-Un5MtNUE",bluishItem:"bluishItem-Un5MtNUE",noPadding:"noPadding-Un5MtNUE"}},276833:e=>{e.exports={badge:"badge-PlSmolIm",hasChildren:"hasChildren-PlSmolIm",content:"content-PlSmolIm",leftSlot:"leftSlot-PlSmolIm",xsmall:"xsmall-PlSmolIm",small:"small-PlSmolIm",medium:"medium-PlSmolIm",large:"large-PlSmolIm",xlarge:"xlarge-PlSmolIm",xxlarge:"xxlarge-PlSmolIm"}},747553:e=>{e.exports={label:"label-oHMFhnC8"}},516473:e=>{e.exports={priceLabel:"priceLabel-mpa2WWtU",list:"list-mpa2WWtU",item:"item-mpa2WWtU",selected:"selected-mpa2WWtU",input:"input-mpa2WWtU",wait:"wait-mpa2WWtU"}},603216:e=>{e.exports={absolutePriceDropdownItem:"absolutePriceDropdownItem-QDwbzjCO",price:"price-QDwbzjCO",labelsGroup:"labelsGroup-QDwbzjCO"}},927186:e=>{e.exports={wrapper:"wrapper-RoaZjf69",title:"title-RoaZjf69"}},25596:e=>{e.exports={dateTimeWrapper:"dateTimeWrapper-Dg2Jb5Mv",containerDatePicker:"containerDatePicker-Dg2Jb5Mv",dateInputContainer:"dateInputContainer-Dg2Jb5Mv",dateInput:"dateInput-Dg2Jb5Mv",dateInputIcon:"dateInputIcon-Dg2Jb5Mv"}},664863:e=>{e.exports={container:"container-JQC67cD9",leverageButton:"leverageButton-JQC67cD9",disabledLeverageButton:"disabledLeverageButton-JQC67cD9"}},583221:e=>{e.exports={hiddenLabel:"hiddenLabel-XFowpoet"}},866562:e=>{e.exports={dialog:"dialog-mAVKt9Z3",dialogBody:"dialogBody-mAVKt9Z3"}},888074:e=>{e.exports={orderInfo:"orderInfo-ZCVq_FIR",scrollable:"scrollable-ZCVq_FIR",scrollWrapper:"scrollWrapper-ZCVq_FIR",title:"title-ZCVq_FIR",contentWrapper:"contentWrapper-ZCVq_FIR",right:"right-ZCVq_FIR",disclaimer:"disclaimer-ZCVq_FIR",disclaimerLink:"disclaimerLink-ZCVq_FIR"}},799591:e=>{e.exports={panelContainer:"panelContainer-qPQHH_hi",orderPanel:"orderPanel-qPQHH_hi"}},915609:e=>{e.exports={scrollWrapper:"scrollWrapper-XYi0nst9",content:"content-XYi0nst9",separator:"separator-XYi0nst9",controls:"controls-XYi0nst9",informer:"informer-XYi0nst9"}},44006:e=>{e.exports={controlGroup:"controlGroup-HcMnXcBP",absolutePriceControl:"absolutePriceControl-HcMnXcBP",withRelative:"withRelative-HcMnXcBP",relativePriceControl:"relativePriceControl-HcMnXcBP"}},411471:e=>{e.exports={"min-order-ticket-height":"320px","min-order-info-height":"46px",orderWidget:"orderWidget-F15FKlrH",header:"header-F15FKlrH",side:"side-F15FKlrH",doneButton:"doneButton-F15FKlrH",customField:"customField-F15FKlrH",duration:"duration-F15FKlrH",customFieldsWrapper:"customFieldsWrapper-F15FKlrH",separator:"separator-F15FKlrH",orderInfoWrapper:"orderInfoWrapper-F15FKlrH",resizeHandler:"resizeHandler-F15FKlrH",orderTicket:"orderTicket-F15FKlrH",orderInfoAndSeparator:"orderInfoAndSeparator-F15FKlrH",topWrapper:"topWrapper-F15FKlrH",centerBlock:"centerBlock-F15FKlrH","brackets-wrapper":"brackets-wrapper-F15FKlrH",brackets:"brackets-F15FKlrH",quantityWrapper:"quantityWrapper-F15FKlrH",leverage:"leverage-F15FKlrH",screenWithKeyholeIcon:"screenWithKeyholeIcon-F15FKlrH",
sadScreenIcon:"sadScreenIcon-F15FKlrH",emptyStateBlockWrapper:"emptyStateBlockWrapper-F15FKlrH",scrollWrapper:"scrollWrapper-F15FKlrH"}},282342:e=>{e.exports={priceLabel:"priceLabel-s5LgTmXf",ask:"ask-s5LgTmXf",bid:"bid-s5LgTmXf",last:"last-s5LgTmXf"}},724551:e=>{e.exports={progressBarContainer:"progressBarContainer-Zcxs1Uvi",marginsContainer:"marginsContainer-Zcxs1Uvi",margin:"margin-Zcxs1Uvi",title:"title-Zcxs1Uvi",price:"price-Zcxs1Uvi",usedMargin:"usedMargin-Zcxs1Uvi",progressBar:"progressBar-Zcxs1Uvi",track:"track-Zcxs1Uvi",progress:"progress-Zcxs1Uvi",fullFilled:"fullFilled-Zcxs1Uvi",exceededMargin:"exceededMargin-Zcxs1Uvi",wait:"wait-Zcxs1Uvi",description:"description-Zcxs1Uvi",link:"link-Zcxs1Uvi"}},283879:e=>{e.exports={calculator:"calculator-vLRZh3q9",calculatorWithInput:"calculatorWithInput-vLRZh3q9",input:"input-vLRZh3q9",buttonsWrapper:"buttonsWrapper-vLRZh3q9",buttonWithOperation:"buttonWithOperation-vLRZh3q9",buttonIcon:"buttonIcon-vLRZh3q9"}},183927:e=>{e.exports={menuBox:"menuBox-rm7KgfIT"}},478987:e=>{e.exports={wrapper:"wrapper-IWgXIGoE",label:"label-IWgXIGoE",wait:"wait-IWgXIGoE",title:"title-IWgXIGoE",popupMenuItem:"popupMenuItem-IWgXIGoE",value:"value-IWgXIGoE"}},971045:e=>{e.exports={wrapper:"wrapper-Gt0vOpyz",control:"control-Gt0vOpyz",label:"label-Gt0vOpyz",units:"units-Gt0vOpyz",single:"single-Gt0vOpyz",calculatorIcon:"calculatorIcon-Gt0vOpyz",isOpened:"isOpened-Gt0vOpyz",risk:"risk-Gt0vOpyz",wait:"wait-Gt0vOpyz"}},583874:e=>{e.exports={pipsLabel:"pipsLabel-oAJwOoJe",openButton:"openButton-oAJwOoJe",iconWrapper:"iconWrapper-oAJwOoJe",openIcon:"openIcon-oAJwOoJe",wait:"wait-oAJwOoJe",opened:"opened-oAJwOoJe",list:"list-oAJwOoJe",item:"item-oAJwOoJe",selected:"selected-oAJwOoJe",input:"input-oAJwOoJe"}},249065:e=>{e.exports={relativePriceDropdownItem:"relativePriceDropdownItem-L1mdgTgI",price:"price-L1mdgTgI"}},519762:e=>{e.exports={restrictions:"restrictions-kbYnGQgu",restriction:"restriction-kbYnGQgu",hardToBorrow:"hardToBorrow-kbYnGQgu",halted:"halted-kbYnGQgu"}},288342:e=>{e.exports={restrictions:"restrictions-B5GOsH7j",smallPaddings:"smallPaddings-B5GOsH7j",sideControl:"sideControl-B5GOsH7j",sell:"sell-B5GOsH7j",active:"active-B5GOsH7j",buy:"buy-B5GOsH7j",section:"section-B5GOsH7j",title:"title-B5GOsH7j",typeSide:"typeSide-B5GOsH7j",value:"value-B5GOsH7j",valueNoData:"valueNoData-B5GOsH7j",spread:"spread-B5GOsH7j",raisedTop:"raisedTop-B5GOsH7j",disable:"disable-B5GOsH7j"}},435148:e=>{e.exports={tabsContainer:"tabsContainer-zC5M0yfs"}},421978:e=>{e.exports={informer:"informer-iBPUmjfh"}},375390:e=>{e.exports={wrapper:"wrapper-eYAfVbpw"}},515931:e=>{e.exports={wrap:"wrap-ne5qGlZh",icon:"icon-ne5qGlZh",text:"text-ne5qGlZh",disabled:"disabled-ne5qGlZh"}},518561:e=>{e.exports={scrollWrap:"scrollWrap-FaOvTD2r"}},131987:e=>{e.exports={container:"container-Tv7LSjUz",overlayScrollWrap:"overlayScrollWrap-Tv7LSjUz",wrapper:"wrapper-Tv7LSjUz"}},151810:e=>{e.exports={wrap:"wrap-vSb6C0Bj","wrap--horizontal":"wrap--horizontal-vSb6C0Bj",bar:"bar-vSb6C0Bj",barInner:"barInner-vSb6C0Bj",
"barInner--horizontal":"barInner--horizontal-vSb6C0Bj","bar--horizontal":"bar--horizontal-vSb6C0Bj"}},269006:e=>{e.exports={container:"container-HTBJo9ba",top:"top-HTBJo9ba"}},698110:e=>{e.exports={container:"container-yrIMi47q",title:"title-yrIMi47q",title_normal:"title_normal-yrIMi47q",icon:"icon-yrIMi47q",text:"text-yrIMi47q",text_large:"text_large-yrIMi47q",action:"action-yrIMi47q"}},29493:e=>{e.exports={list:"list-HGwPo2aR",item:"item-HGwPo2aR"}},333636:e=>{e.exports={"link-item":"link-item-eIA09f0e"}},632498:e=>{e.exports={"arrow-icon":"arrow-icon-NIrWNOPk",dropped:"dropped-NIrWNOPk","size-xsmall":"size-xsmall-NIrWNOPk","size-small":"size-small-NIrWNOPk","size-medium":"size-medium-NIrWNOPk","size-large":"size-large-NIrWNOPk","size-xlarge":"size-xlarge-NIrWNOPk"}},956406:e=>{e.exports={"underline-tab":"underline-tab-cfYYXvwA","disable-focus-outline":"disable-focus-outline-cfYYXvwA","enable-cursor-pointer":"enable-cursor-pointer-cfYYXvwA",selected:"selected-cfYYXvwA","disable-active-state-styles":"disable-active-state-styles-cfYYXvwA","size-xsmall":"size-xsmall-cfYYXvwA","size-small":"size-small-cfYYXvwA","size-medium":"size-medium-cfYYXvwA","size-large":"size-large-cfYYXvwA","size-xlarge":"size-xlarge-cfYYXvwA",fake:"fake-cfYYXvwA","margin-xsmall":"margin-xsmall-cfYYXvwA","margin-small":"margin-small-cfYYXvwA","margin-medium":"margin-medium-cfYYXvwA","margin-large":"margin-large-cfYYXvwA","margin-xlarge":"margin-xlarge-cfYYXvwA",collapse:"collapse-cfYYXvwA","ellipsis-children":"ellipsis-children-cfYYXvwA"}},898163:e=>{e.exports={"scroll-wrap":"scroll-wrap-SmxgjhBJ","size-xlarge":"size-xlarge-SmxgjhBJ","enable-scroll":"enable-scroll-SmxgjhBJ","underline-tabs":"underline-tabs-SmxgjhBJ","size-large":"size-large-SmxgjhBJ","size-medium":"size-medium-SmxgjhBJ","size-small":"size-small-SmxgjhBJ","size-xsmall":"size-xsmall-SmxgjhBJ","make-grid-column":"make-grid-column-SmxgjhBJ","stretch-tabs":"stretch-tabs-SmxgjhBJ","equal-tab-size":"equal-tab-size-SmxgjhBJ"}},150368:e=>{e.exports={underline:"underline-Pun8HxCz",center:"center-Pun8HxCz",corner:"corner-Pun8HxCz",disabled:"disabled-Pun8HxCz"}},805184:(e,t,r)=>{"use strict";var o,s,n;function i(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function a(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function l(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}r.d(t,{Button:()=>p}),function(e){e.Primary="primary",e.Success="success",e.Default="default",e.Danger="danger"}(o||(o={})),function(e){e.Small="s",e.Medium="m",e.Large="l"}(s||(s={})),function(e){e.Default="default",e.Stroke="stroke"}(n||(n={}));var c=r(50959),u=r(228837);function d(e){const{intent:t,size:r,appearance:o,useFullWidth:s,icon:n,...c}=e;return{...c,color:a(t),size:l(r),variant:i(o),stretch:s}}function p(e){return c.createElement(u.SquareButton,{...d(e)})}},463039:(e,t,r)=>{"use strict";r.d(t,{Label:()=>l})
;var o=r(50959),s=r(497754),n=r.n(s),i=r(102691),a=r(747553);function l(e){const{htmlFor:t,children:r,className:s,id:l,...c}=e;return o.createElement(i.BeforeSlot,{...c,className:n()(a.label,s)},o.createElement("label",{id:l,htmlFor:t},r))}},252130:(e,t,r)=>{"use strict";r.d(t,{useIsMounted:()=>s});var o=r(50959);const s=()=>{const e=(0,o.useRef)(!1);return(0,o.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}},314266:(e,t,r)=>{"use strict";r.d(t,{DurationControl:()=>B});var o=r(609838),s=r(50959),n=r(319937),i=r(679864),a=r(723991),l=r(354648),c=r(911794);function u(e){return e.flatMap((e=>"items"in e?e.items:e))}var d=r(167554);function p(e){const{isOpen:t,onClose:r,anchorId:o,anchorButtonRef:n,contentId:a,items:l,value:p,onChange:m,placementRules:v,width:f,height:g,closeOnClickAway:b,...C}=e,E=(0,s.useCallback)((e=>{r(!0,"currentLevel"),m(e)}),[r,m]),y=(0,s.useId)(),S=(0,s.useMemo)((()=>u(l)),[l]),w=(0,s.useMemo)((()=>S.findIndex((e=>e.value===p))),[S,p]);(0,s.useEffect)((()=>{if(!t)return;if(-1===w)return;const e=`item_${y}_${w}`,r=document.getElementById(e);var o;r&&(o=()=>{r.focus({preventScroll:!0}),(0,c.scrollPopoverItemIntoView)(r,!1)},window.requestIdleCallback?window.requestIdleCallback(o):setTimeout(o))}),[y,w,t]);const _=(0,s.useCallback)((()=>{let e=0;function t(t){return s.createElement(h,{id:`item_${y}_${e++}`,key:`${t.title}:${t.description}`,value:p,item:t,onSelectItem:E})}return s.createElement(s.Fragment,null,l.map(((e,r)=>"items"in e?s.createElement(d.SectionPopoverItem,{key:`group_${y}_${r}`,title:e.title},e.items.map(t)):t(e))))}),[l,E,y,p]),k=(0,s.useMemo)(_,[_]),P=C;return s.createElement(i.PopoverKeyboardNavigator,{role:"listbox",ariaLabelledby:o,isOpen:t,onClose:r,anchored:{type:"element",at:n},idOfContentWrapperElement:a,placementRules:v,width:f,height:g,focusFirstItemOnOpen:!1,closeOnClickAway:b,...P},k)}function h(e){const{id:t,value:r,item:o,onSelectItem:n}=e,i=(0,s.useCallback)((()=>n(o.value)),[n,o.value]),c=r===o.value;return s.createElement(a.PopoverItem,{role:"option",selected:c,ariaChecked:c,title:o.title,leftSlot:(0,l.iconPart)(o),key:o.title,popoverItemButtonId:t,onClick:i,disabled:o.disabled})}var m=r(928141),v=r(937901);function f(e){return function(e){const{isOpen:t,onClose:r,contentId:o,anchorButtonRef:s,anchorId:i,handleAnchorClick:a,handleAnchorKeyDown:l}=(0,n.useDefaultButtonAnchorProps)({anchorId:e.anchorId,openOnEnter:e.openOnEnter});return{isOpen:t,anchorProps:{id:i,role:"combobox",onClick:a,onKeyDown:l,...(0,v.createRefProp)(e.refStyle,s),...(0,m.createAriaAttributesProps)(e.ariaStyle,{expanded:t,controls:o,haspopup:"listbox"})},popoverProps:{isOpen:t,anchorId:i,anchorButtonRef:s,contentId:o,onClose:r,items:e.items,value:e.value,onChange:e.onChange}}}({items:e.items,value:e.value,onChange:e.onChange,refStyle:e?.refStyle??"reference",ariaStyle:e?.ariaStyle??"kebab-case",openOnEnter:e.openOnEnter,anchorId:e?.anchorId})}function g(e){
const{title:t,items:r,onChange:o,value:i,noneSelectedTitle:a,popoverPlacementRules:l,popoverWidth:c,popoverHeight:d,stretch:h,anchorButtonId:m,disabled:v,anchorButtonSize:g,anchorButtonSizeClassname:b,openOnEnter:C}=e,E=(0,s.useMemo)((()=>u(r).find((e=>e.value===i))),[r,i]),{isOpen:y,anchorProps:S,popoverProps:w}=f({items:r,value:i,onChange:o,openOnEnter:C,ariaStyle:"camelCase",anchorId:m}),_=t??E?.title??a;return s.createElement(s.Fragment,null,s.createElement(n.ButtonAnchor,{...S,children:_,isOpen:y,stretch:h,anchorButtonClassname:b,disabled:v,size:g}),s.createElement(p,{...w,placementRules:l,width:c,height:d}))}var b=r(391431),C=r(930202),E=r(350324),y=r(493173),S=r(44807),w=r(508550),_=r(614793),k=r(25596);const P=new Date((new Date).setHours(0,0,0,0));const I=(0,y.mergeThemes)(E.DEFAULT_INPUT_DATE_THEME,{container:k.dateInputContainer,date:k.dateInput,icon:k.dateInputIcon});function N(e){return s.createElement(E.DateInput,{...e,theme:I})}function O(e){const{initDateTime:t,onDateSelect:r,onTimeSelect:o,onControlFocused:n,hasDatePicker:i,hasTimePicker:a,showErrorMessages:l,onError:c,revertInvalidData:u,keepFocusOnKeyBoardTimeNavigation:d}=e;if((0,s.useEffect)((()=>()=>c?.(!1)),[]),!i&&!a)return s.createElement(s.Fragment,null);const p=()=>{n?.()};return s.createElement("div",{className:k.dateTimeWrapper,onKeyDown:e=>{13===(0,C.hashFromEvent)(e)&&e.stopPropagation()}},i&&s.createElement(_.DatePicker,{name:"duration-date-input",containerClassName:k.containerDatePicker,minDate:P,initial:t,onPick:e=>{if(null===e)return c?.(!0);r(e),c?.(!1)},onFocus:p,InputComponent:N,showErrorMessages:l,revertInvalidData:u,showOnFocus:!1}),a&&s.createElement(S.TimeInput,{name:"duration-time-input",value:(h=t,(0,w.twoDigitsFormat)(h.getHours())+":"+(0,w.twoDigitsFormat)(h.getMinutes())),onChange:e=>{o(function(e){const[t,r]=e.split(":"),o=new Date;return o.setHours(Number(t),Number(r)),o}(e))},onFocus:p,keepFocusOnKeyBoardNavigation:d}));var h}var x=r(927186);function M(e,t){return Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),t.getUTCHours(),t.getUTCMinutes())}const D="duration-select-input";function B(e){const{currentDuration:t,durationMetaInfoList:n,onDurationChanged:i,onControlFocused:a,showErrorMessages:l,revertInvalidData:c,onError:u}=e,d=(0,b.findDurationMetaInfo)(n,t.type);(0,s.useEffect)((()=>{if(void 0===a)return;const e=document.getElementById(D);return e?.addEventListener("focus",a),()=>e?.removeEventListener("focus",a)}),[a]);const p=(0,s.useMemo)((()=>void 0!==t.datetime?new Date(t.datetime):["GTT","GTD"].includes(t.type)?(0,b.makeDatePlus24UTCHours)():new Date),[t.datetime,t.type]),h=(0,s.useMemo)((()=>n.map((e=>({title:e.name,value:e.value})))),[n]);if(void 0===d)return s.createElement(s.Fragment,null);const m=h.length<2;return s.createElement("div",{className:x.wrapper},s.createElement("span",{className:x.title},o.t(null,void 0,r(296344))),s.createElement(g,{anchorButtonId:D,value:d?.value,items:h,disabled:m,onChange:function(r){const o={type:r},s=(0,b.findDurationMetaInfo)(n,r)
;void 0!==s&&(s.hasDatePicker||s.hasTimePicker)&&(o.datetime=t.datetime||(0,b.getTimestamp)((0,b.makeDatePlus24UTCHours)()));i(o),void 0!==e.onClose&&e.onClose()},stretch:!0}),s.createElement(O,{initDateTime:p,hasDatePicker:d.hasDatePicker,hasTimePicker:d.hasTimePicker,onControlFocused:a,onDateSelect:function(t){const r=M(t,p);e.onDurationChanged({type:e.currentDuration.type,datetime:r})},onTimeSelect:function(t){const r=M(p,t);e.onDurationChanged({type:e.currentDuration.type,datetime:r})},revertInvalidData:c,showErrorMessages:l,onError:u,keepFocusOnKeyBoardTimeNavigation:!0}))}},729354:(e,t,r)=>{"use strict";r.d(t,{QuantityInput:()=>m});var o=r(50959),s=r(609838),n=r(463039),i=r(499665);var a=r(587125),l=r(341086),c=r(50771),u=r(848062),d=r(863582),p=r(391431),h=r(583221);function m(e){const{id:t,qty:m,quantityMetainfo:v,onValueChange:f,className:g,customTitle:b,hasLotSize:C,onClick:E,inputReference:y,hideLabel:S,button:w,customErrorsAttachment:_}=e,{error:k,errorMessage:P,setError:I,setErrorMessage:N,handleInputError:O}=function(e,t){const[r,s]=(0,o.useState)(!1),[n,a]=(0,o.useState)(void 0),[l,c]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{const{res:r,msg:o}=(0,i.checkQtyError)(t,e,!0),n=l?void 0:o;s(l||r),a(n)}),[e,t,l]),{error:r,errorMessage:n,setError:s,setErrorMessage:a,handleInputError:e=>{c(e)}}}(m,v),x=(0,o.useMemo)((()=>(0,l.numberFormattersParsersChain)({chain:[(0,c.prohibitNanOnParseStep)(),(0,u.decimalNumberMagnitudeStep)({standardNotationOnly:!0}),(0,d.signumStep)({negativeSign:d.longMinusSign})]},{ignoreLocaleNumberFormat:!0})),[v]),M=b??(C?s.t(null,void 0,r(833526)):s.t(null,void 0,r(715432)));return o.createElement(o.Fragment,null,o.createElement(n.Label,{className:S&&h.hiddenLabel,htmlFor:t},M),o.createElement(a.NumberInput,{...v,button:w,id:t,className:g,value:(0,p.applyRounding)(m,x),onValueChange:e=>{const{res:t,msg:r}=(0,i.checkQtyError)(v,e,!0);t||f(e),I(t),N(r)},error:k,errorMessage:P,errorHandler:O,onBlur:()=>{I(!1),N(void 0)},onClick:E,inputReference:y,useFormatter:!1,mode:"float",customErrorsAttachment:_}))}},129018:(e,t,r)=>{"use strict";r.d(t,{QuantityCalculator:()=>I});var o=r(50959),s=r(960521),n=r(624216),i=r(510618),a=r(493173),l=r(567704),c=r(609838),u=r(497754),d=r.n(u),p=r(926032),h=r(180185),m=r(878112),v=r(380327),f=r(729354),g=r(600297),b=r(218666),C=r(96634),E=r(115589),y=r(672197),S=r(283879);const w={isGrouped:!1,cellState:{isTop:!1,isRight:!1,isBottom:!1,isLeft:!1}};function _(e){const{min:t,max:s,step:n,uiStep:i,reference:a,withInput:l,valueInput:u,onChangeValueInput:_,onClick:k,onClose:P,onInputClick:I}=e,N=(0,o.useRef)(null),O=(0,o.useRef)(null),x={min:t,max:s,step:n,uiStep:i};function M(e,t){N.current?.blur(),k(e,t)}return(0,o.useEffect)((()=>{const e=setTimeout((()=>{N.current&&(N.current.focus(),N.current.selectionStart=N.current.value.length)}),0);return O.current=(0,p.createGroup)({desc:"Qty calculator"}),O.current?.add({desc:"Press Enter in qty calculator",hotkey:13,handler:P}),()=>{clearTimeout(e),O.current?.destroy(),O.current=null}}),[]),
o.createElement("div",{className:d()(S.calculator,l&&S.calculatorWithInput),tabIndex:-1,ref:a,onKeyDown:e=>{13===(0,h.hashFromEvent)(e)&&P()}},l&&o.createElement(f.QuantityInput,{id:"calculator-input",qty:u,quantityMetainfo:x,className:S.input,customTitle:c.t(null,void 0,r(598721)),onValueChange:_,onClick:I,inputReference:e=>N.current=e}),o.createElement(v.ControlGroupContext.Provider,{value:w},o.createElement("div",{className:S.buttonsWrapper},o.createElement(g.Button,{value:-n,buttonType:g.ButtonType.IncDec,icon:o.createElement(m.Icon,{className:S.buttonIcon,icon:C}),onClick:M,className:S.buttonWithOperation}),o.createElement(g.Button,{value:n,buttonType:g.ButtonType.IncDec,icon:o.createElement(m.Icon,{className:S.buttonIcon,icon:b}),onClick:M,className:S.buttonWithOperation}),o.createElement(g.Button,{value:1,buttonType:g.ButtonType.PlusValue,onClick:M}),o.createElement(g.Button,{value:5,buttonType:g.ButtonType.PlusValue,onClick:M}),o.createElement(g.Button,{value:25,buttonType:g.ButtonType.PlusValue,onClick:M}),o.createElement(g.Button,{value:100,buttonType:g.ButtonType.PlusValue,onClick:M}),o.createElement(g.Button,{value:500,buttonType:g.ButtonType.PlusValue,onClick:M}),o.createElement(g.Button,{value:1e3,buttonType:g.ButtonType.PlusValue,onClick:M}),o.createElement(g.Button,{value:0,buttonType:g.ButtonType.Clear,icon:o.createElement(m.Icon,{className:S.buttonIcon,icon:E}),onClick:M}),o.createElement(g.Button,{value:t||n,buttonType:g.ButtonType.Default,icon:o.createElement(m.Icon,{className:S.buttonIcon,icon:y}),onClick:M}))))}var k=r(183927);const P=(0,a.mergeThemes)(i.DEFAULT_MENU_THEME,{menuBox:k.menuBox});class I extends o.PureComponent{constructor(e){super(e),this._setCalcRef=e=>{this.props.calcReference&&this.props.calcReference(e)},this._calculatorStepHandler=(e,t)=>{const{min:r,max:o,uiStep:n,trackEventTarget:i,trackEvent:a}=this.props;if(void 0!==a&&void 0!==i&&a(i,"Calculator Button",(0,l.prepareCalculatorEventText)(e,t)),t===g.ButtonType.Clear||t===g.ButtonType.Default)return this._updateValueByCalc(e);const c=this.props.valueGetter();if(null===c)return;let u=Number((0,s.Big)(c).plus(e));if((u<r||o<u)&&t!==g.ButtonType.IncDec)return this._updateValueByCalc(c);if(t===g.ButtonType.IncDec){if(u=this._calcByStep(c,e,r,n),o<u)return this._updateValueByCalc(o);if(u<r)return this._updateValueByCalc(r)}return this._updateValueByCalc(u)},this._updateValueByCalc=e=>{this._handlerValueInput(e),this.props.onValueChange(e,!0),void 0!==this.props.errorHandler&&this.props.errorHandler(!1),this.props.calculatorUsedStat&&this.props.calculatorUsedStat(),this.props.onFocus?.()},this._onInputClick=()=>{const{trackEventTarget:e,trackEvent:t}=this.props;void 0!==t&&void 0!==e&&t(e,"Calculator Input","Quantity input field")},this.state={valueInput:e.valueGetter()},this._handlerValueInput=this._handlerValueInput.bind(this)}render(){const{min:e,max:t,step:r,uiStep:s,withInput:i,targetEl:a,position:l,onClose:c,onClickOutside:u,onKeyboardClose:d}=this.props;return o.createElement(n.PopupMenu,{isOpened:!0,onClose:c,
onClickOutside:u,onKeyboardClose:d,position:l,doNotCloseOn:a,theme:P},o.createElement(_,{min:e,max:t,step:r,uiStep:s,withInput:i,reference:this._setCalcRef,valueInput:this.state.valueInput,onChangeValueInput:this._updateValueByCalc,onClick:this._calculatorStepHandler,onClose:c,onInputClick:this._onInputClick}))}_calcByStep(e,t,r,o){const n=Math.sign(t),i=Math.abs(o??t),a=new s.Big(e),l=a.minus(r).mod(t);let c=a.plus(n*i);return l.eq(0)||(c=c.plus((n>0?0:1)*i).minus(l)),c.toNumber()}_handlerValueInput(e){this.setState({valueInput:e})}}},853177:(e,t,r)=>{"use strict";r.d(t,{TradingPanelWarningInformersContainer:()=>m});var o=r(50959),s=r(815544),n=r(38323);var i=r(586639),a=r(609838),l=r(918460);const c=e=>{const[t,r]=(0,o.useState)(e);return[t,(0,o.useCallback)((()=>r(!1)),[]),(0,o.useCallback)((()=>r(!0)),[])]};var u=r(661851),d=r(421978);function p(e){const{warningMessageSource:t,isCloseButtonShown:p=!0}=e,[h,m]=(0,o.useState)(),[v,f,g]=c(!0),b=(0,o.useCallback)((e=>{m(e),g()}),[]),C=(0,o.useMemo)((()=>{return(e=t)&&(e instanceof s.Observable||(0,n.isFunction)(e.lift)&&(0,n.isFunction)(e.subscribe))?t:(0,i.of)(t);var e}),[t]);return(0,u.useObservableSubscription)(C,b),v&&void 0!==h&&""!==h?o.createElement(l.Informer,{content:h,header:a.t(null,void 0,r(392298)),informerIntent:"warning",isCloseButtonShown:p,className:d.informer,onCloseClick:f}):o.createElement(o.Fragment,null)}var h=r(375390);function m(e){const{warningInformersItems:t}=e;return 0===t.length?o.createElement(o.Fragment,null):o.createElement("div",{className:h.wrapper},t.map((e=>o.createElement(p,{key:e.id,...e}))))}},27950:(e,t,r)=>{"use strict";r.d(t,{EditButton:()=>l});var o=r(50959),s=r(497754),n=r(878112),i=r(610600),a=r(515931);function l(e){const{value:t,onClick:r,className:l,startSlot:c,disabled:u=!1}=e;return o.createElement("div",{className:s(a.wrap,u&&a.disabled,l),onClick:r,"data-name":"edit-button"},o.createElement("div",{className:s(a.text,"apply-overflow-tooltip")},void 0!==c&&c,o.createElement("span",null,t)),o.createElement(n.Icon,{icon:i,className:a.icon}))}},438980:(e,t,r)=>{"use strict";r.d(t,{Measure:()=>s});var o=r(664332);function s(e){const{children:t,onResize:r}=e;return t((0,o.useResizeObserver)(r||(()=>{}),[null===r]))}},73288:(e,t,r)=>{"use strict";r.d(t,{OverlayScrollContainer:()=>g});var o=r(50959),s=r(497754),n=r.n(s),i=r(431520),a=r(650151),l=r(822960);const c=r(151810);var u;!function(e){e[e.Vertical=0]="Vertical",e[e.Horizontal=1]="Horizontal",e[e.HorizontalRtl=2]="HorizontalRtl"}(u||(u={}));const d={0:{isHorizontal:!1,isNegative:!1,sizePropName:"height",minSizePropName:"minHeight",startPointPropName:"top",currentMousePointPropName:"clientY",progressBarTransform:"translateY"},1:{isHorizontal:!0,isNegative:!1,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"left",currentMousePointPropName:"clientX",progressBarTransform:"translateX"},2:{isHorizontal:!0,isNegative:!0,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"right",currentMousePointPropName:"clientX",
progressBarTransform:"translateX"}},p=40;function h(e){const{size:t,scrollSize:r,clientSize:s,scrollProgress:i,onScrollProgressChange:u,scrollMode:h,theme:m=c,onDragStart:v,onDragEnd:f,minBarSize:g=p}=e,b=(0,o.useRef)(null),C=(0,o.useRef)(null),[E,y]=(0,o.useState)(!1),S=(0,o.useRef)(0),{isHorizontal:w,isNegative:_,sizePropName:k,minSizePropName:P,startPointPropName:I,currentMousePointPropName:N,progressBarTransform:O}=d[h];(0,o.useEffect)((()=>{const e=(0,a.ensureNotNull)(b.current).ownerDocument;return E?(v&&v(),e&&(e.addEventListener("mousemove",L),e.addEventListener("mouseup",z))):f&&f(),()=>{e&&(e.removeEventListener("mousemove",L),e.removeEventListener("mouseup",z))}}),[E]);const x=t/r||0,M=s*x||0,D=Math.max(M,g),B=(t-D)/(t-M),T=r-t,F=_?-T:0,R=_?0:T,H=A((0,l.clamp)(i,F,R))||0;return o.createElement("div",{ref:b,className:n()(m.wrap,w&&m["wrap--horizontal"]),style:{[k]:t},onMouseDown:function(e){if(e.isDefaultPrevented())return;e.preventDefault();const t=W(e.nativeEvent,(0,a.ensureNotNull)(b.current)),r=Math.sign(t),o=(0,a.ensureNotNull)(C.current).getBoundingClientRect();S.current=r*o[k]/2;let s=Math.abs(t)-Math.abs(S.current);const n=A(T);s<0?(s=0,S.current=t):s>n&&(s=n,S.current=t-r*n);u(V(r*s)),y(!0)}},o.createElement("div",{ref:C,className:n()(m.bar,w&&m["bar--horizontal"]),style:{[P]:g,[k]:D,transform:`${O}(${H}px)`},onMouseDown:function(e){e.preventDefault(),S.current=W(e.nativeEvent,(0,a.ensureNotNull)(C.current)),y(!0)}},o.createElement("div",{className:n()(m.barInner,w&&m["barInner--horizontal"])})));function L(e){const t=W(e,(0,a.ensureNotNull)(b.current))-S.current;u(V(t))}function z(){y(!1)}function W(e,t){const r=t.getBoundingClientRect()[I];return e[N]-r}function A(e){return e*x*B}function V(e){return e/x/B}}var m=r(522224),v=r(518561);const f=8;function g(e){const{reference:t,className:r,containerHeight:n=0,containerWidth:a=0,contentHeight:l=0,contentWidth:c=0,scrollPosTop:u=0,scrollPosLeft:d=0,onVerticalChange:p,onHorizontalChange:g,visible:b}=e,[C,E]=(0,m.useHover)(),[y,S]=(0,o.useState)(!1),w=n<l,_=a<c,k=w&&_?f:0;return o.createElement("div",{...E,ref:t,className:s(r,v.scrollWrap),style:{visibility:b||C||y?"visible":"hidden"}},w&&o.createElement(h,{size:n-k,scrollSize:l-k,clientSize:n-k,scrollProgress:u,onScrollProgressChange:function(e){p&&p(e)},onDragStart:P,onDragEnd:I,scrollMode:0}),_&&o.createElement(h,{size:a-k,scrollSize:c-k,clientSize:a-k,scrollProgress:d,onScrollProgressChange:function(e){g&&g(e)},onDragStart:P,onDragEnd:I,scrollMode:(0,i.isRtl)()?2:1}));function P(){S(!0)}function I(){S(!1)}}},109955:(e,t,r)=>{"use strict";r.d(t,{OverlayScrollWrapper:()=>c});var o=r(50959),s=r(497754),n=r.n(s),i=r(73288),a=r(139043),l=r(131987);function c(e){const{children:t,className:r,wrapperClassName:s,reference:c,hasCustomTouchScrollAnimation:u,scrollContainerRef:d,isForceVisible:p,tabIndex:h,...m}=e,[v,f,g,b]=(0,a.useOverlayScroll)(d,u);return(0,o.useImperativeHandle)(c,(()=>({updateScrollState:b}))),o.createElement("div",{...m,...f,className:n()(l.container,r)
},o.createElement(i.OverlayScrollContainer,{...v,visible:p??v.visible,className:l.overlayScrollWrap}),o.createElement("div",{className:n()(l.wrapper,s),tabIndex:h,ref:g,onScroll:b},t))}},662941:(e,t,r)=>{"use strict";r.d(t,{EmptyStateBlock:()=>l});var o=r(50959),s=r(497754),n=r.n(s),i=r(698110);function a(e){const{title:t,tagName:r,titleSize:s}=e,a=r||"strong";return o.createElement(a,{className:n()(i.title,i[`title_${s}`])},t)}const l=(0,o.forwardRef)((function(e,t){return o.createElement("div",{className:s(i.container,e.className),ref:t},o.createElement("div",{className:s(i.icon,e.iconClass)}),e.title&&o.createElement(a,{title:e.title,titleSize:e.titleSize,tagName:e.titleTagName}),e.text&&o.createElement("div",{className:s(i.text,"large"===e.textSize&&i.text_large)},e.text),e.action&&o.createElement("div",{className:i.action},e.action))}))},737402:(e,t,r)=>{"use strict";r.d(t,{useSafeMatchMedia:()=>o.useSafeMatchMedia});var o=r(671129)},139043:(e,t,r)=>{"use strict";r.d(t,{useOverlayScroll:()=>l});var o=r(50959),s=r(650151),n=r(522224),i=r(601227);const a={onMouseOver:()=>{},onMouseOut:()=>{}};function l(e,t=i.CheckMobile.any()){const r=(0,o.useRef)(null),l=e||(0,o.useRef)(null),[c,u]=(0,n.useHover)(),[d,p]=(0,o.useState)({reference:r,containerHeight:0,containerWidth:0,contentHeight:0,contentWidth:0,scrollPosTop:0,scrollPosLeft:0,onVerticalChange:function(e){p((t=>({...t,scrollPosTop:e}))),(0,s.ensureNotNull)(l.current).scrollTop=e},onHorizontalChange:function(e){p((t=>({...t,scrollPosLeft:e}))),(0,s.ensureNotNull)(l.current).scrollLeft=e},visible:c}),h=(0,o.useCallback)((()=>{if(!l.current)return;const{clientHeight:e,scrollHeight:t,scrollTop:o,clientWidth:s,scrollWidth:n,scrollLeft:i}=l.current,a=r.current?r.current.offsetTop:0;p((r=>({...r,containerHeight:e-a,contentHeight:t-a,scrollPosTop:o,containerWidth:s,contentWidth:n,scrollPosLeft:i})))}),[]);function m(){p((e=>({...e,scrollPosTop:(0,s.ensureNotNull)(l.current).scrollTop,scrollPosLeft:(0,s.ensureNotNull)(l.current).scrollLeft})))}return(0,o.useEffect)((()=>{c&&h(),p((e=>({...e,visible:c})))}),[c]),(0,o.useEffect)((()=>{const e=l.current;return e&&e.addEventListener("scroll",m),()=>{e&&e.removeEventListener("scroll",m)}}),[l]),[d,t?a:u,l,h]}},327871:(e,t,r)=>{"use strict";r.d(t,{KeyCode:()=>o,makeKeyboardListener:()=>i});var o,s=r(50959);!function(e){e[e.Enter=13]="Enter",e[e.Space=32]="Space",e[e.Backspace=8]="Backspace",e[e.DownArrow=40]="DownArrow",e[e.UpArrow=38]="UpArrow",e[e.RightArrow=39]="RightArrow",e[e.LeftArrow=37]="LeftArrow",e[e.Escape=27]="Escape",e[e.Tab=9]="Tab"}(o||(o={}));class n{constructor(){this._handlers=new Map}registerHandlers(e){Object.keys(e).forEach((t=>{const r=parseInt(t);let o=e[r];if(Array.isArray(o)||(o=[o]),this._handlers.has(r)){const e=this._handlers.get(r);e&&o.forEach((t=>e.add(t)))}else this._handlers.set(r,new Set(o))}))}unregisterHandlers(e){Object.keys(e).forEach((t=>{const r=parseInt(t);let o=e[r];if(Array.isArray(o)||(o=[o]),this._handlers.has(r)){const e=this._handlers.get(r);e&&o.forEach((t=>e.delete(t)))}}))}
deleteAllHandlers(){this._handlers=new Map}registerHandler(e,t){if(this._handlers.has(e)){const r=this._handlers.get(e);r&&r.add(t)}else this._handlers.set(e,new Set([t]))}unregisterHandler(e,t){if(this._handlers.has(e)){const r=this._handlers.get(e);r&&r.delete(t)}}listen(e){if(this._handlers.has(e.keyCode)){const t=this._handlers.get(e.keyCode);t&&t.forEach((t=>t(e)))}}}function i(e){var t;return(t=class extends s.PureComponent{constructor(e){super(e),this._keyboardListener=new n,this._listener=this._keyboardListener.listen.bind(this._keyboardListener)}componentDidMount(){this._registerHandlers(this.props.keyboardEventHandlers)}componentDidUpdate(e){e.keyboardEventHandlers!==this.props.keyboardEventHandlers&&this._registerHandlers(this.props.keyboardEventHandlers)}render(){const{keyboardEventHandlers:t,...r}=this.props;return s.createElement(e,{...r,onKeyDown:this._listener})}_registerHandlers(e){e&&(this._keyboardListener.deleteAllHandlers(),this._keyboardListener.registerHandlers(e))}}).displayName=`KeyboardListener(${e.displayName??e.name??"Component"})`,t}},488831:(e,t,r)=>{"use strict";r.d(t,{UnderlineButtonTabs:()=>Y});var o,s=r(50959),n=r(497754),i=r.n(n),a=r(609838),l=r(429510),c=r(525388),u=r(269842),d=r(772069),p=r(984164),h=r(953517);!function(e){e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge"}(o||(o={}));const m=(0,s.createContext)({size:"small",overflowBehaviour:void 0});var v=r(234539),f=r(956406);function g(e){const{size:t="xsmall",active:r,fake:o,enableActiveStateStyles:s,anchor:i=!1,hideFocusOutline:a=!1,equalTabSize:l,className:c,overflowBehaviour:u}=e;return n(f["underline-tab"],f[`size-${t}`],r&&f.selected,!s&&f["disable-active-state-styles"],a&&f["disable-focus-outline"],o&&f.fake,i&&f["enable-cursor-pointer"],l&&f[`margin-${t}`],"collapse"===u&&f.collapse,c)}const b=(0,s.forwardRef)(((e,t)=>{const{size:r,overflowBehaviour:o}=(0,s.useContext)(m),n=(0,s.useContext)(v.CustomBehaviourContext),{active:a,fake:l,className:c,enableActiveStateStyles:u=n.enableActiveStateStyles,hideFocusOutline:d=!1,equalTabSize:p,children:h,...b}=e;return s.createElement("button",{...b,ref:t,className:g({size:r,active:a,fake:l,enableActiveStateStyles:u,hideFocusOutline:d,equalTabSize:p,className:c,overflowBehaviour:o})},p&&"string"==typeof h?s.createElement("span",{className:i()(f["ellipsis-children"],"apply-overflow-tooltip")},h):h)}));b.displayName="UnderlineTabsBaseButton";const C=(0,s.forwardRef)(((e,t)=>{const{item:r,highlighted:o,handleItemRef:n,onClick:i,"aria-disabled":a,...l}=e,c=(0,s.useCallback)((()=>{i&&i(r)}),[i,r]),u=(0,s.useCallback)((e=>{n&&n(r,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[r,n,t]);return s.createElement(b,{...l,id:r.id,onClick:c,ref:u},r.label)}));C.displayName="UnderlineButtonTab";var E=r(650151),y=r(192063),S=r(904925),w=r(878112),_=r(347531),k=r(602948),P=r(863509),I=r(168874),N=r(632498);function O(e){switch(e){case"xsmall":return _;case"small":return k;case"medium":case"large":return P;case"xlarge":return I}}function x(e){
const{size:t,isDropped:r=!1}=e;return s.createElement(w.Icon,{icon:O(t),className:n(N["arrow-icon"],N[`size-${t}`],r&&N.dropped)})}var M=r(333636);const D=4,B=4;function T(e){const{size:t,disabled:r,isOpened:o,enableActiveStateStyles:n,hideFocusOutline:i,fake:a,items:l,buttonContent:u,buttonRef:d,isAnchorTabs:p,isHighlighted:h,onButtonClick:m,onItemClick:v,onClose:f}=e,g=(0,s.useRef)(null),C=(0,c.useMergedRefs)([d,g]),w=function(e,t){const r=(0,s.useRef)(R);return(0,s.useEffect)((()=>{const e=getComputedStyle((0,E.ensureNotNull)(t.current));r.current={xsmall:F(e,"xsmall"),small:F(e,"small"),medium:F(e,"medium"),large:F(e,"large"),xlarge:F(e,"xlarge")}}),[t]),(0,s.useCallback)((()=>{const o=(0,E.ensureNotNull)(t.current).getBoundingClientRect(),s=r.current[e];return{x:o.left,y:o.top+o.height+s+D,indentFromWindow:{top:B,bottom:B,left:B,right:B}}}),[t,e])}(t,g);return s.createElement(S.PopupMenuDisclosureView,{buttonRef:g,listboxTabIndex:-1,isOpened:o,onClose:f,listboxAria:{"aria-hidden":!0},popupPosition:w,button:s.createElement(b,{"aria-hidden":!0,disabled:r,active:o,onClick:m,ref:C,tabIndex:-1,enableActiveStateStyles:n,hideFocusOutline:i,fake:a},u,s.createElement(x,{size:t,isDropped:o})),popupChildren:l.map((e=>s.createElement(y.PopupMenuItem,{key:e.id,className:p?M["link-item"]:void 0,onClick:v,onClickArg:e,isActive:h(e),label:e.label,isDisabled:e.disabled,link:"href"in e?e.href:void 0,rel:"rel"in e?e.rel:void 0,target:"target"in e?e.target:void 0,renderComponent:"renderComponent"in e?e.renderComponent:void 0,dontClosePopup:!0})))})}function F(e,t){return parseInt(e.getPropertyValue(`--ui-lib-underline-tabs-tab-margin-bottom-${t}`),10)}const R={xsmall:0,small:0,medium:0,large:0,xlarge:0};var H=r(799573),L=r(737402),z=r(586240),W=r(898163);function A(e){const{size:t,overflowBehaviour:r,className:o}=e;return n(W["scroll-wrap"],W[`size-${t}`],"scroll"===r&&W["enable-scroll"],o)}function V(){const[e,t]=(0,s.useState)(!1);return(0,s.useEffect)((()=>{t(H.mobiletouch)}),[]),e}var K=r(290484),Z=r(431520),$=r(150368),U=r.n($);const q=100;function G(e){const{disabled:t,translateX:r,transitionDuration:o}=e,i=e.scale/100;return s.createElement("div",{className:n(U().underline,t&&U().disabled),style:{transform:`translateX(${r}px) scaleX(${i})`,transitionDuration:`${o}ms`}},s.createElement("div",{className:U().corner,style:{transform:`scaleX(${1/i})`}}),s.createElement("div",{className:U().center,style:{transform:`scaleX(${1-30/e.scale})`}}),s.createElement("div",{className:U().corner,style:{transform:`scaleX(${1/i})`}}))}function Y(e){const{id:t,items:o,activationType:n,orientation:v,disabled:f,moreButtonContent:g=a.t(null,void 0,r(437117)),size:b="small",onActivate:E,isActive:y,className:S,style:w,overflowBehaviour:_,enableActiveStateStyles:k,tablistLabelId:P,tablistLabel:I,"data-name":N="underline-tabs-buttons",stretchTabs:O,equalTabSize:x,defaultKeyboardFocus:M,stopPropagationIfKeyboardActionHandled:D,keyboardNavigationLoop:B,focusableItemAttributes:F}=e,R=V(),H=function(e){const t=(0,
L.useSafeMatchMedia)(z["media-mf-phone-landscape"],!0),r=V();return e??(r||!t?"scroll":"collapse")}(_),$=(0,s.useRef)(!1),U=(0,s.useCallback)((e=>e.id),[]),Y="none"===H&&O,X="none"===H&&x,j=k??!R,{visibleItems:J,hiddenItems:Q,containerRefCallback:ee,innerContainerRefCallback:te,moreButtonRef:re,setItemRef:oe}=(0,l.useCollapsible)(o,U,y),se="collapse"===H?J:o,ne="collapse"===H?Q:[],ie=(0,s.useCallback)((e=>ne.includes(e)),[ne]),ae=(0,s.useRef)(new Map),{isOpened:le,open:ce,close:ue,onButtonClick:de}=(0,d.useDisclosure)({id:t,disabled:f}),pe=function(e="xsmall"){switch(e){case"xsmall":case"small":return 12;case"medium":return 16;case"large":case"xlarge":return 20}}(b),{tabsBindings:he,tablistBinding:me,scrollWrapBinding:ve,onActivate:fe,onHighlight:ge,isHighlighted:be}=(0,p.useTabs)({id:t,items:[...se,...ne],activationType:n,orientation:v,disabled:f,tablistLabelId:P,tablistLabel:I,onActivate:E,isActive:y,isCollapsed:ie,isRtl:Z.isRtl,itemsRefs:ae,isDisclosureOpened:le,defaultKeyboardFocus:M,stopPropagationIfKeyboardActionHandled:D,keyboardNavigationLoop:B,focusableItemAttributes:F,scrollIntoViewOptions:{additionalScroll:pe}}),Ce=o.find(y),Ee=ne.find(be),ye=(0,s.useCallback)((()=>{Ce&&ge(Ce)}),[ge,Ce]),Se=(0,s.useCallback)((e=>he.find((t=>t.id===e.id))??{}),[he]),we=(0,s.useCallback)((()=>{ue(),ye(),$.current=!0}),[ue,ye]),_e=(0,s.useCallback)((()=>{Ee&&(fe(Ee),ge(Ee,200))}),[fe,ge,Ee]);ve.ref=(0,c.useMergedRefs)([ve.ref,ee]),me.ref=(0,c.useMergedRefs)([me.ref,te]),me.onKeyDown=(0,u.createSafeMulticastEventHandler)((0,h.useKeyboardEventHandler)([(0,h.useKeyboardClose)(le,we),(0,h.useKeyboardActionHandler)([13,32],_e,(0,s.useCallback)((()=>Boolean(Ee)),[Ee]))]),me.onKeyDown);const ke=(0,s.useCallback)((e=>{$.current=!0,de(e)}),[$,de]),Pe=(0,s.useCallback)((e=>{e&&fe(e)}),[fe]);(0,s.useEffect)((()=>{$.current?$.current=!1:(Ee&&!le&&ce(),!Ee&&le&&ue())}),[Ee,le,ce,ue]);const Ie=function(e,t,r=[]){const[o,n]=(0,s.useState)(),i=(0,s.useRef)(),a=(0,s.useRef)(),l=e=>{const t=e.parentElement??void 0;if(void 0===t)return;const r=void 0===a.current||a.current===e?0:q;a.current=e;const{left:o,right:s,width:i}=e.getBoundingClientRect(),{left:l,right:c}=t.getBoundingClientRect(),u=(0,Z.isRtl)()?s-c:o-l;n({translateX:u,scale:i,transitionDuration:r})};return(0,s.useEffect)((()=>{const e=(0,K.default)((e=>{const t=e[0].target;void 0!==t&&l(t)}),50);i.current=new ResizeObserver(e)}),[]),(0,s.useEffect)((()=>{if(void 0===t)return;const r=e.get(t);return void 0!==r?(l(r),i.current?.observe(r),()=>i.current?.disconnect()):void 0}),r),o}(ae.current,Ce??Ee,[Ce??Ee,se,b,Y,H]);return s.createElement(m.Provider,{value:{size:b,overflowBehaviour:H}},s.createElement("div",{...ve,className:A({size:b,overflowBehaviour:H,className:S}),style:w,"data-name":N},s.createElement("div",{...me,className:i()(W["underline-tabs"],{[W["make-grid-column"]]:Y||X,[W["stretch-tabs"]]:Y,[W["equal-tab-size"]]:X})},se.map((e=>s.createElement(C,{...Se(e),key:e.id,item:e,onClick:fe,enableActiveStateStyles:j,hideFocusOutline:R,ref:oe(U(e)),...e.dataId&&{"data-id":e.dataId
},equalTabSize:X}))),ne.map((e=>s.createElement(C,{...Se(e),ref:oe(U(e)),key:e.id,item:e,fake:!0}))),"collapse"===H&&s.createElement(T,{size:b,disabled:f,isOpened:le,items:ne,buttonContent:g,buttonRef:re,isHighlighted:be,onButtonClick:ke,onItemClick:Pe,onClose:ue,enableActiveStateStyles:j,hideFocusOutline:R,fake:0===ne.length}),Ie?s.createElement(G,{...Ie,disabled:f}):s.createElement("div",null))))}var X=r(409245);function j(e){return s.createElement("a",{...(0,X.renameRef)(e)})}(0,s.forwardRef)(((e,t)=>{const{size:r,overflowBehaviour:o}=(0,s.useContext)(m),n=(0,s.useContext)(v.CustomBehaviourContext),{item:i,highlighted:a,handleItemRef:l,onClick:c,active:u,fake:d,className:p,enableActiveStateStyles:h=n.enableActiveStateStyles,hideFocusOutline:f=!1,disabled:b,"aria-disabled":C,...E}=e,y=(0,s.useCallback)((e=>{C?e.preventDefault():c&&c(i)}),[c,C,i]),S=(0,s.useCallback)((e=>{l&&l(i,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[i,l,t]),w=i.renderComponent??j;return s.createElement(w,{...E,id:i.id,"aria-disabled":C,onClick:y,reference:S,href:i.href,rel:i.rel,target:i.target,className:g({size:r,active:u,fake:d,enableActiveStateStyles:h,anchor:!0,hideFocusOutline:f,className:p,overflowBehaviour:o})},i.label)})).displayName="UnderlineAnchorTab"},493173:(e,t,r)=>{"use strict";function o(e,t,r={}){return Object.assign({},e,function(e,t,r={}){const o=Object.assign({},t);for(const s of Object.keys(t)){const n=r[s]||s;n in e&&(o[s]=[e[n],t[s]].join(" "))}return o}(e,t,r))}r.d(t,{mergeThemes:()=>o})},214651:(e,t,r)=>{"use strict";r.r(t),r.d(t,{closeOrderDialog:()=>xr,closeOrderDrawer:()=>Br,mountOrderPanel:()=>Tr,showOrderDialog:()=>Or,showOrderDrawer:()=>Dr,unmountOrderPanel:()=>Fr});var o,s=r(50959),n=r(632227),i=r(801808),a=r(651674),l=r(497754),c=r.n(l),u=r(870122),d=r(586639),p=r(601227),h=r(228837),m=r(273388),v=r(650151),f=r(671945),g=r(662941),b=r(661851),C=r(180185),E=r(609838),y=r(440891),S=r(148296),w=r(930202),_=r(276833),k=r.n(_);function P(e){const{size:t,leftSlot:r,children:o,className:n,reference:i,href:a,onClick:l,...u}=e,d=a?"a":l?"button":"span";return s.createElement(d,{...u,ref:i,className:c()(n,k().badge,t&&k()[t],r&&k().hasLeftSlot,o&&k().hasChildren),onClick:l,...a?{target:"_blank",href:a}:{}},r&&s.createElement("div",{className:k().leftSlot},r),o&&s.createElement("span",{className:k().content},o))}!function(e){e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge"}(o||(o={}));var I=r(599016),N=r(519762);function O(e){return e===I.RestrictionType.HardToBorrow?N.hardToBorrow:e===I.RestrictionType.Halted?N.halted:null}function x(e){return s.createElement("div",{className:N.restrictions},e.restrictionTypes.map(((e,t)=>s.createElement(P,{key:t,size:o.XSmall,className:c()(N.restriction,O(e))},e))))}var M=r(363111),D=r(288342);function B(e,t){return r=>{const o=(0,w.hashFromEvent)(r);13!==o&&32!==o||(r.preventDefault(),r.stopPropagation(),e(t))}}
const T=E.t(null,void 0,r(168222)),F=E.t(null,void 0,r(169961)),R=E.t(null,void 0,r(883629)),H=e=>Math.max(e.ask?.length??0,e.bid?.length??0,e.spread?.length??0)>7,L=e=>[M.OrderPanelStatus.Editing,M.OrderPanelStatus.Preview].includes(e);function z({value:e}){return s.createElement("div",{className:D.value},e||s.createElement("div",{className:D.valueNoData},R))}const W=e=>{const{side:t,status:r,quotes:o,onSideChanged:n,restrictionTypes:i,currencyForCrypto:a}=e;function c(e){return r!==M.OrderPanelStatus.Wait&&t===e}const{handleBuyClick:u,handleSellClick:d,handleBuyKeyDown:p,handleSellKeyDown:h}=(0,s.useMemo)((()=>({handleBuyClick:()=>n(1),handleSellClick:()=>n(-1),handleBuyKeyDown:B(n,1),handleSellKeyDown:B(n,-1)})),[n]);return s.createElement(s.Fragment,null,0!==i.length&&s.createElement("div",{className:l(D.restrictions,D.smallPaddings)},s.createElement(x,{restrictionTypes:i})),s.createElement("div",{className:l(D.sideControl,L(r)&&D.disable)},s.createElement("div",{role:"button",tabIndex:0,className:l(D.sell,c(-1)&&D.active),"data-name":"side-control-sell",onClick:d,onKeyDown:h},s.createElement("div",{className:D.title},s.createElement("span",{className:a&&D.typeSide},T),a&&s.createElement("span",null,a)),s.createElement(z,{value:o.bid})),s.createElement("div",{role:"button",tabIndex:0,className:l(D.buy,c(1)&&D.active),"data-name":"side-control-buy",onClick:u,onKeyDown:p},s.createElement("div",{className:D.title},s.createElement("span",{className:a&&D.typeSide},F),a&&s.createElement("span",{className:D.typeSide},a)),s.createElement(z,{value:o.ask})),o.spread&&s.createElement("div",{className:l(D.spread,H(o)&&D.raisedTop),"data-name":"side-control-spread"},o.spread)))};var A=r(763281);function V(e){const{model:t,disabled:r}=e,{orderPanelStatus:o}=(0,s.useContext)(A.WidgetContext),n=(0,b.useObservable)(t.value$,t.getValue()),i=(0,b.useObservable)(t.formattedQuotes$,t.getFormattedQuotes()),a=(0,b.useObservable)(t.restrictionTypes$,t.getRestrictionTypes());return s.createElement(W,{quotes:i,side:n,status:o,restrictionTypes:a,currencyForCrypto:t.baseCurrency,onSideChanged:e=>{r||(t.setValue(e),t.onControlFocused.fire())}})}var K=r(918460),Z=r(805184),$=r(204926),U=r(975228),q=r(109955);function G(e){const{scrollContainerRef:t,...r}=e,o=(0,U.useAccurateHover)(t);return s.createElement(q.OverlayScrollWrapper,{...r,scrollContainerRef:t,isForceVisible:o})}var Y=r(915609);const X={cancel:E.t(null,void 0,r(904543)),sendOrder:E.t(null,void 0,r(224602))};function j(e){if(!Array.isArray(e))return s.createElement("div",{key:e},e);const t=[];for(const r of e)"string"!=typeof r?void 0!==r.url&&t.push(s.createElement("a",{key:r.url,href:r.url,target:"_blank"},r.text)):t.push(r);const r=t.map((e=>"string"==typeof e?e:e.key)).join("");return s.createElement("div",{key:r},t)}function J(e){const{model:t,side:r,isMobile:o,loading$:n,shouldShowActionButton:i,...a}=e,l=(0,s.useRef)(null),c=(0,s.useRef)(null),u=(0,b.useObservable)(n,!1);(0,s.useEffect)((()=>l.current?.focus()),[u]);const d=0!==t.errors.length
;return s.createElement(s.Fragment,null,o?s.createElement(Q,{...a,ref:c,model:t,hasErrors:d}):s.createElement(G,{className:Y.scrollWrapper,scrollContainerRef:c},s.createElement(Q,{...a,ref:c,model:t,hasErrors:d})),i&&s.createElement("div",{className:Y.controls},s.createElement(Z.Button,{useFullWidth:!0,size:"l",intent:-1===r?"danger":"primary",onClick:t.onPlaceClick,reference:l,disabled:d||u},X.sendOrder),s.createElement(Z.Button,{useFullWidth:!0,size:"l",intent:"default",appearance:"stroke",onClick:t.onCancelClick},X.cancel)))}const Q=(0,s.forwardRef)(((e,t)=>{const{model:r,hasErrors:o,isPopupMode:n,requestResize:i}=e,a=(0,b.useObservable)(r.infoTableQuotesData$,{rows:[]}),l=r.infoTableCustomData(),c=r.infoTableOrderData();return(0,s.useEffect)((()=>{n&&i?.()}),[n,a.rows.length]),s.createElement("div",{className:Y.content,ref:t},o&&s.createElement(K.Informer,{className:Y.informer,informerIntent:"danger",content:r.errors.map(j)}),0!==r.warnings.length&&s.createElement(K.Informer,{className:Y.informer,informerIntent:"warning",content:r.warnings.map(j)}),s.createElement($.InfoTable,{rows:a.rows,header:a.header}),s.createElement("div",{className:Y.separator}),s.createElement($.InfoTable,{rows:c.rows,header:c.header}),0!==l.length&&s.createElement(s.Fragment,null,s.createElement("div",{className:Y.separator}),l.map(((e,t)=>s.createElement($.InfoTable,{key:`custom-${t}`,rows:e.rows,header:e.header})))))}));var ee=r(488831),te=r(435148);const re=E.t(null,void 0,r(490138)),oe=E.t(null,void 0,r(790258)),se=E.t(null,void 0,r(898251)),ne=E.t(null,void 0,r(978369));var ie;!function(e){e.Market="Market",e.Limit="Limit",e.Stop="Stop",e.StopLimit="StopLimit"}(ie||(ie={}));const ae={Market:2,Limit:1,Stop:3,StopLimit:4};function le(e){const{orderType:t,setOrderType:r,disabled:o,supportMarketOrders:n,supportLimitOrders:i,supportStopOrders:a,supportStopLimitOrders:l}=e,c=(0,s.useMemo)((()=>function(e){const{supportMarketOrders:t,supportLimitOrders:r,supportStopOrders:o,supportStopLimitOrders:s}=e,n=[];return t&&n.push({id:"Market",label:re}),r&&n.push({id:"Limit",label:oe}),o&&n.push({id:"Stop",label:se}),s&&n.push({id:"StopLimit",label:ne}),n}({supportMarketOrders:n,supportLimitOrders:i,supportStopOrders:a,supportStopLimitOrders:l})),[n,i,a,l]),u=(0,s.useCallback)((e=>ae[e.id]===t),[t]);return s.createElement("div",{className:te.tabsContainer,tabIndex:-1},s.createElement(ee.UnderlineButtonTabs,{id:"order-type-tabs",items:c,isActive:u,onActivate:e=>r(ae[e.id]),size:"xsmall",disabled:o,overflowBehaviour:"none",stretchTabs:!0,equalTabSize:!0}))}var ce=r(308825),ue=r(380327),de=r(587125),pe=r(878112),he=r(162458),me=r(738036),ve=r(129018),fe=r(972535),ge=r(914987),be=r(971045);class Ce extends s.PureComponent{constructor(e){super(e),this._control=null,this._input=null,this._calc=null,this._getCurrentValue=()=>this.props.value||0,this._getDropdownPosition=()=>(0,he.getPopupPositioner)(this._control,{horizontalAttachEdge:he.HorizontalAttachEdge.Right,horizontalDropDirection:he.HorizontalDropDirection.FromRightToLeft}),
this._handleCalculatorFocus=()=>{fe.mobiletouch||null===this._input||this._input.focus()},this._setRef=e=>{this._control=e},this._setCalcRef=e=>{this._calc=e},this._setInputRef=e=>{this._input=e},this._onKeyDown=e=>{!this.state.isOpened||13!==e.keyCode&&27!==e.keyCode||(e.stopPropagation(),this.setState({isOpened:!1})),Object.values(M.CalculatorDecKeyCodes).includes(e.keyCode)||Object.values(M.CalculatorIncKeyCodes).includes(e.keyCode)||this.setState({isOpened:!1}),this.props.onKeyDown&&this.props.onKeyDown(e)},this._onClickOutside=e=>{this._calc&&!this._calc.contains(e.target)&&this.setState({isOpened:!1})},this._updateValue=e=>{this.props.onValueChange(e)},this._toggle=()=>{this.setState((e=>({isOpened:!e.isOpened})))},this._setInputFocus=e=>{null!==e&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))},this.state={isOpened:!1}}componentDidMount(){5===this.props.focus&&this._setInputFocus(this._input)}render(){const{min:e,max:t,step:r,uiStep:o,errorHandler:n,...i}=this.props,a=s.createElement(pe.Icon,{icon:ge,className:l(be.calculatorIcon,this.state.isOpened&&be.isOpened),onClick:this._toggle});return s.createElement(me.OutsideEvent,{click:!0,mouseDown:!0,touchStart:!0,handler:this._onClickOutside},(l=>s.createElement("span",{ref:l},s.createElement(de.NumberInput,{...i,value:this.props.value||0,alwaysUpdateValueFromProps:!0,shouldApplyValueOnBlur:!1,containerReference:this._setRef,inputReference:this._setInputRef,button:a,onValueChange:this._updateValue,onKeyDown:this._onKeyDown,errorHandler:n,min:e,max:t,step:r,uiStep:o,controlDecKeyCodes:this.state.isOpened?[M.CalculatorDecKeyCodes.Minus,M.CalculatorDecKeyCodes.NumMinus,M.CalculatorDecKeyCodes.FirefoxMinus]:void 0,controlIncKeyCodes:this.state.isOpened?[M.CalculatorIncKeyCodes.Plus,M.CalculatorIncKeyCodes.NumPlus,M.CalculatorIncKeyCodes.FirefoxPlus]:void 0}),this.state.isOpened&&s.createElement(ve.QuantityCalculator,{...i,min:e,max:t,step:r,uiStep:o,valueGetter:this._getCurrentValue,position:this._getDropdownPosition(),targetEl:this._control,calcReference:this._setCalcRef,onClose:this._toggle,onFocus:this._handleCalculatorFocus,errorHandler:n,trackEventTarget:"Order Ticket"}))))}}function Ee(e){const{control:t,focus:r,min:o,max:n,step:i,uiStep:a,type:c,error:u,errorMessage:d,errorHandler:p,label:h,className:m,status:v,trackEvent:f,highlight:g,highlightRemoveRoundBorder:C,intent:E,fontSizeStyle:y}=e,S=(0,b.useObservable)(t.value$,t.getValue()),w=()=>{e.onFocus&&e.onFocus(c)},_={value:S||0,step:i,uiStep:a,className:l(be.input,v===M.OrderPanelStatus.Wait&&be.wait),error:u,errorMessage:d,errorHandler:p,onFocus:w,useFormatter:!1,onValueChange:e=>{w(),t.setValue(e),t.onModifiedCallback()},calculatorUsedStat:()=>t.calculatorUsedStat(),highlight:g,highlightRemoveRoundBorder:C,intent:E,fontSizeStyle:y,mode:"float"},k=n||9e15,P=0===c?s.createElement(Ce,{..._,focus:r,min:void 0===o?i:o,max:k,trackEvent:f}):s.createElement(de.NumberInput,{..._,step:.01,min:o,max:k});return s.createElement("div",{className:l(m,be.control)},s.createElement("div",{className:be.label
}," ",h," "),P)}const ye=E.t(null,void 0,r(663886)),Se=E.t(null,void 0,r(715432)),we=E.t(null,void 0,r(833526));class _e extends s.PureComponent{constructor(e){super(e),this._subscriptions=[],this._onFocus=e=>{e!==this.props.model.getFocusedControl()&&this.props.model.setFocusedControl(e),this.props.model.onControlFocused.fire()},this._errorHandler=e=>{this.props.model.setControlError(e)},this._callback=()=>this.forceUpdate(),this._useContextValidation=e.useContextValidation}componentDidMount(){this._subscribeToModel(this.props.model)}componentWillUnmount(){this._unsubscribeFromModel()}render(){const e=this.context,{model:t,symbolHasLotSize:r,status:o,trackEvent:n,focus:i}=this.props,a=t.getError(),c={errorHandler:this._errorHandler,error:a,status:o,onFocus:this._onFocus,trackEvent:n,fontSizeStyle:"medium"},u=Boolean(a&&a.res),d=a&&a.msg,p=t.showRiskControlsAndInfo&&t.stopLossAvailable()&&(e.showPercentRiskInQty||e.showCurrencyRiskInQty);return s.createElement(s.Fragment,null,s.createElement(ue.ControlGroupContext.Provider,{value:this._getGroupContextValue(!0,!p)},s.createElement(Ee,{...c,...this._generateHighlightProps(0),control:t.quantity,max:this._useContextValidation?void 0:t.qty.max,min:this._useContextValidation?0:t.qty.min,step:t.qty.step,uiStep:t.qty.uiStep,type:0,label:t.units??(r?we:Se),className:l(!p&&be.single,be.units),focus:i,error:u&&this._isSelectedControl(0),errorMessage:d})),p&&s.createElement("div",{className:be.risk},t.riskInCurrency&&e.showCurrencyRiskInQty&&s.createElement(ue.ControlGroupContext.Provider,{value:this._getGroupContextValue(!1,!e.showPercentRiskInQty)},s.createElement(Ee,{...c,...this._generateHighlightProps(1),control:t.riskInCurrency,step:t.riskStep,type:1,label:t.currency+" "+ye,error:u&&this._isSelectedControl(1),errorMessage:d})),t.riskInPercent&&e.showPercentRiskInQty&&s.createElement(ue.ControlGroupContext.Provider,{value:this._getGroupContextValue(!1,!0)},s.createElement(Ee,{...c,...this._generateHighlightProps(2),control:t.riskInPercent,step:t.riskStep,type:2,label:"% "+ye,error:u&&this._isSelectedControl(2),errorMessage:d}))))}_generateHighlightProps(e){const t=this.props.model.getError(),r=this.props.model.stopLossAvailable()?this._isSelectedControl(e):void 0;return{highlight:(this.context.showCurrencyRiskInQty||this.context.showPercentRiskInQty)&&(0===e?r:this._isSelectedControl(e)),intent:Boolean(t&&t.res)&&this._isSelectedControl(e)?"danger":void 0}}_isSelectedControl(e){return this.props.status!==M.OrderPanelStatus.Wait&&this.props.model.getFocusedControl()===e}_subscribeToModel(e){this._subscriptions=[e.focusedControl$.subscribe(this._callback),e.stopLossAvailable$.subscribe(this._callback),e.error$.subscribe(this._callback)]}_unsubscribeFromModel(){this._subscriptions.forEach((e=>e.unsubscribe()))}_getGroupContextValue(e,t){return{isGrouped:!0,disablePositionAdjustment:!0,cellState:{isTop:!0,isRight:t,isBottom:!0,isLeft:e}}}}_e.contextType=A.SettingsContext;var ke=r(493455),Pe=r(960521),Ie=r(624216),Ne=r(192063),Oe=r(763802),xe=r(478987)
;const Me=[1,5,10,25,50,75,100];function De(e){const{control:t,controlType:r,error:o,side:n,focusedControl:i,setFocusedControl:a}=e,c=(0,s.useRef)(null),u=(0,s.useRef)(null),d=(0,b.useObservable)(t.value$,t.getValue()),p=(0,b.useObservable)(e.balanceValue$,null),[h,m]=(0,s.useState)(!1),[f,g]=(0,s.useState)(0),E=Boolean(o&&o.res),y=o&&o.msg,S=null!==p&&p>0&&(-1===n?3===r:4===r);return(0,s.useEffect)((()=>{var t;5===e.initialFocus&&null!==(t=u.current)&&(t.focus(),t.setSelectionRange(t.value.length,t.value.length))}),[]),s.createElement(s.Fragment,null,s.createElement(de.NumberInput,{...function(e){const t={};_(e)&&(t.highlight=!0);Boolean(o&&o.res)&&_(e)&&(t.intent="danger");return t}(r),value:d,onBlur:e.forceRoundByStep?function(){if(null!==d&&void 0!==e.step){const t=new Pe.Big(d),r=t.mod(e.step);if(!r.eq(0)){w(t.minus(r).toNumber())}}}:void 0,onValueChange:w,min:e.min,step:e.step,useFormatter:!1,errorMessage:y,errorHandler:e.setControlError,error:E&&_(r),inputReference:function(e){u.current=e},innerLabel:h?void 0:s.createElement("span",{className:xe.label},e.label),onFocus:()=>(a(r),void e.onControlFocused.fire()),onKeyDown:function(e){if(27===(0,C.hashFromEvent)(e))h&&(e.stopPropagation(),e.nativeEvent.stopImmediatePropagation(),k())},className:l(xe.input,e.status===M.OrderPanelStatus.Wait&&xe.wait),button:S?s.createElement(Oe.CaretButton,{isDropped:h,onFocus:function(){u.current?.focus()},onClick:function(){m(!h)}}):s.createElement(s.Fragment,null),containerReference:function(e){c.current=e},alwaysUpdateValueFromProps:e.forceRoundByStep,fontSizeStyle:e.fontSizeStyle,shouldApplyValueOnBlur:!1}),S&&s.createElement(Ie.PopupMenu,{position:function(){const e=(0,v.ensureNotNull)(c.current).getBoundingClientRect();return g(e.width),{x:e.left,y:e.top+e.height+1}},onClose:k,isOpened:h,doNotCloseOn:c.current,minWidth:f},Me.map((r=>{const o=function(e){return(0,Pe.Big)((0,v.ensureNotNull)(p)).mul(.01).mul(e).toNumber()}(r),n=s.createElement(s.Fragment,null,r+"%  ",s.createElement("span",{className:xe.value},"("+e.formatter.format(o)+" "+e.label+")"));return s.createElement(Ne.PopupMenuItem,{key:r.toString(),className:xe.popupMenuItem,label:n,onClick:()=>t.setValue(o)})}))));function w(r){t.setValue(r),e.onModifiedCallback()}function _(t){return e.status!==M.OrderPanelStatus.Wait&&i===t}function k(){m(!1)}}function Be(e){const{model:t,className:o,status:n,useContextValidation:i}=e,a=(0,b.useObservable)(t.quoteCurrencyUiParams$),c=(0,b.useObservable)(t.error$,t.getError()),u=(0,b.useObservable)(t.focusedControl$,t.getFocusedControl()),d={side:(0,b.useObservable)(t.side$,t.getSide()),status:n,formatter:t.formatter,error:c,focusedControl:u,setFocusedControl:t.setFocusedControl,onControlFocused:t.onControlFocused,setControlError:t.setControlError,fontSizeStyle:"medium"};return s.createElement(s.Fragment,null,s.createElement("div",{className:xe.title},E.t(null,void 0,r(715432))),s.createElement(ke.ControlGroup,{cols:1,rows:2,className:l(o,xe.wrapper)},s.createElement(De,{...d,control:t.quantity,
onModifiedCallback:t.quantity.onModifiedCallback,label:t.info.baseCurrency,controlType:3,balanceValue$:t.baseCurrencyCryptoBalanceValue$,initialFocus:e.focus,forceRoundByStep:!0,step:t.info.qty.step,min:i?0:t.info.qty.min}),s.createElement(De,{...d,control:t.quoteCurrencyQuantity,onModifiedCallback:t.quoteCurrencyQuantity.onModifiedCallback,label:t.info.quoteCurrency,controlType:4,balanceValue$:t.quoteCurrencyCryptoBalanceValue$,step:a?.step,min:i?0:a?.min})))}var Te=r(391431),Fe=r(327871),Re=r(654767),He=r(29493);class Le extends s.PureComponent{constructor(e){super(e)}render(){return s.createElement("div",{className:this.props.className,onClick:this.props.onClick,ref:this.props.reference},this.props.children)}}var ze=r(438980);class We extends s.PureComponent{constructor(e){super(e),this._handleResize=([e])=>{this.props.onMeasure?.(e.target.getBoundingClientRect())}}render(){const{theme:e=He}=this.props,t=l(e.list,{[this.props.className]:Boolean(this.props.className)}),{fontSize:r=13}=this.props,o={bottom:this.props.bottom,fontSize:r,left:this.props.left,height:this.props.height||"auto",right:this.props.right,top:this.props.top,width:this.props.width,zIndex:this.props.zIndex};return s.createElement(ze.Measure,{onResize:Boolean(this.props.onMeasure)?this._handleResize:null},(e=>s.createElement("div",{className:t,style:o,ref:(0,m.mergeRefs)([this.props.reference,e])},this._wrapItems(this.props.items,this.props.selected))))}componentDidMount(){if(void 0!==this.props.selected&&this.props.shouldScrollIfNotVisible){const e=this._items[this.props.selected];e&&this._scrollToItem(e)}}componentDidUpdate(){if(void 0!==this.props.selected&&this.props.shouldScrollIfNotVisible){const e=this._items[this.props.selected];e&&this._scrollToItem(e)}}_wrapItems(e=[],t){this._items=[];const{itemWrap:r=Le,theme:o=He}=this.props,n=r;return e.map(((e,r)=>{const i=l(o.item,{[this.props.itemsClassName]:Boolean(this.props.itemsClassName),[this.props.selectedClassName]:t===r});return s.createElement(n,{reference:t=>{t&&this._items.push({elem:t,index:r,value:e})},key:r,onClick:()=>this._onSelect(r),className:i},e.elem)}))}_onSelect(e){this.props.onSelect&&this.props.onSelect(e,this._items[e].value)}_scrollToItem(e){e.elem.scrollIntoView({block:"center"})}}We.defaultProps={shouldScrollIfNotVisible:!0};var Ae=r(841037),Ve=r(874485);class Ke extends s.PureComponent{render(){const e={position:"absolute",top:this.props.top,width:this.props.width,height:this.props.height,bottom:this.props.bottom,right:this.props.right,left:this.props.left,zIndex:this.props.zIndex};return s.createElement("div",{className:this.props.className,style:e,ref:this.props.reference},this.props.children)}}Ke.displayName="Dropdown Container";const Ze=(0,Ve.makeOverlapable)((0,Ae.makeAttachable)(Ke));var $e,Ue=r(269006);!function(e){e.Top="top",e.Bottom="bottom"}($e||($e={}));class qe extends s.PureComponent{constructor(){super(...arguments),this._container=null,this._setContainerRef=e=>{"function"==typeof this.props.reference&&this.props.reference(e),
"object"==typeof this.props.reference&&(this.props.reference.current=e),this._container=e}}componentDidMount(){this.props.onDropdownWheelNoPassive&&this._addPassiveListenerOnWheel(this.props.onDropdownWheelNoPassive)}componentDidUpdate(e){this.props.onDropdownWheelNoPassive!==e.onDropdownWheelNoPassive&&this._updatePassiveListenerOnWheel(e.onDropdownWheelNoPassive)}componentWillUnmount(){this.props.onDropdownWheelNoPassive&&this._removePassiveListenerOnWheel(this.props.onDropdownWheelNoPassive)}render(){const{shadow:e="bottom",children:t,className:r}=this.props,o=l(Ue.container,Ue[e],r),n={maxHeight:this.props.maxHeight};return s.createElement("div",{ref:this._setContainerRef,style:n,className:o,onTouchStart:this.props.onDropdownTouchStart,onTouchMove:this.props.onDropdownTouchMove,onTouchEnd:this.props.onDropdownTouchEnd,onWheel:this.props.onDropdownWheel},t)}_updatePassiveListenerOnWheel(e){e&&this._removePassiveListenerOnWheel(e),this.props.onDropdownWheelNoPassive&&this._addPassiveListenerOnWheel(this.props.onDropdownWheelNoPassive)}_addPassiveListenerOnWheel(e){(0,v.ensureNotNull)(this._container).addEventListener("wheel",e,{passive:!1})}_removePassiveListenerOnWheel(e){(0,v.ensureNotNull)(this._container).removeEventListener("wheel",e)}}var Ge=r(579184);const Ye=(Xe=We,(je=class extends s.PureComponent{constructor(e){super(e),this._items=this.props.items}componentDidUpdate(e){if(e.command!==this.props.command&&this.props.command)switch(this.props.command.name){case"next":this._next();break;case"prev":this._prev()}e.items!==this.props.items&&(this._items=this.props.items)}render(){return s.createElement(Xe,{...this.props},this.props.children)}_next(){const{selected:e=-1}=this.props,t=e+1;this._items.length-1>=t?this._navigateTo(t):this._navigateTo(0)}_prev(){const{selected:e=-1}=this.props,t=e-1,r=this._items.length-1;0<=t?this._navigateTo(t):this._navigateTo(r)}_navigateTo(e){this.props.onNavigate&&this.props.onNavigate(e,this._items[e])}}).displayName="Navigateable Component",je);var Xe,je;const Je=(0,Ge.makeAnchorable)(Ze),Qe={top:"top",bottom:"bottom",topRight:"top"};class et extends s.PureComponent{render(){const{anchor:e="bottom",fontSize:t=14,root:r="parent"}=this.props,o=l(Re.list,Re[e]),{dropdownClassName:n,height:i,...a}=this.props;return s.createElement(Je,{...a,className:n,root:r},s.createElement(qe,{className:this.props.dropdownContainerClassName,shadow:Qe[e],maxHeight:this.props.maxHeight,onDropdownTouchStart:this.props.onDropdownTouchStart?this.props.onDropdownTouchStart:void 0,onDropdownTouchMove:this.props.onDropdownTouchMove?this.props.onDropdownTouchMove:void 0,onDropdownTouchEnd:this.props.onDropdownTouchEnd?this.props.onDropdownTouchEnd:void 0,onDropdownWheelNoPassive:this.props.onDropdownWheelNoPassive?this.props.onDropdownWheelNoPassive:void 0},s.createElement(Ye,{...a,width:this.props.width,height:i,className:this.props.className||o,itemsClassName:this.props.itemsClassName||Re.item,selectedClassName:this.props.selectedClassName||Re.selected,fontSize:t,reference:this.props.reference})))}}
var tt,rt=r(282342);function ot(e){const t=l(rt.priceLabel,rt[e.modifier.toLowerCase()]);return s.createElement("span",{className:l(t)},e.modifier)}!function(e){e.Ask="Ask",e.Bid="Bid",e.Last="Last"}(tt||(tt={}));var st=r(603216);function nt(e){const t=e.price===e.ask,r=e.price===e.bid,o=e.price===e.last,n=(t||r)&&e.ask===e.bid;let i,a;return e.displayPriceLabel&&(n&&1===e.side?i=s.createElement(ot,{modifier:"Ask"}):n&&-1===e.side?i=s.createElement(ot,{modifier:"Bid"}):t?i=s.createElement(ot,{modifier:"Ask"}):r&&(i=s.createElement(ot,{modifier:"Bid"})),a=s.createElement("div",{className:st.labelsGroup},i,o&&s.createElement(ot,{modifier:"Last"}))),s.createElement("div",{className:st.absolutePriceDropdownItem},s.createElement("div",{className:st.price},e.price),a)}var it,at=r(794087),lt=r(431520),ct=r(296258);function ut(e,t){return e.findIndex((e=>e.priceIndex===t))}function dt(e,t,r){const o=function(){0;return"phone"!==ct.mediaState.device&&"phone-vertical"!==ct.mediaState.device?ht:mt}(),s=[pt(e,t,r)];for(let n=1;n<=o;n++)s.push(pt(e-n,t,r)),s.unshift(pt(e+n,t,r));return s}function pt(e,t,r){return{priceIndex:e,displayPrice:t.format(r(e))}}!function(e){e[e.Up=1]="Up",e[e.Down=-1]="Down"}(it||(it={}));const ht=6,mt=4;var vt=r(459409),ft=r(516473);const gt=(0,Fe.makeKeyboardListener)(at.InputWithError);class bt extends s.PureComponent{constructor(e){super(e),this._priceControl=null,this._priceInput=null,this._dropdown=null,this._validValueRegExp=/^([-−]?([0-9]+\.?[0-9]*)|([-−]?[0-9]*))$/,this._setDropdownRef=e=>{this._dropdown=e},this._setInitialFocus=()=>{!fe.mobiletouch&&this.props.isSelected&&this._priceInput&&(1===this.props.priceType&&1===this.props.focus||2===this.props.priceType&&2===this.props.focus)&&(this._priceInput.setSelectionRange(this.props.displayPrice.length,this.props.displayPrice.length),this._priceInput.focus())},this._onCaretButtonFocus=()=>{this._priceInput?.focus()},this._onFocus=()=>{this.props.onControlFocused&&this.props.onControlFocused(0)},this._onOutsideClick=e=>{if(document.activeElement===this._priceInput&&this.props.status!==M.OrderPanelStatus.Wait&&!this.props.error){const e=this.props.priceFormatter.parse(this.props.displayPrice);if(e.res){const t=this.props.priceFormatter.format(e.value);this._onPriceSelected(t)}}this.state.isOpened&&this._dropdown&&!this._dropdown.contains(e.target)&&this._closeDropdown()},this._onInputChange=e=>{const t=e.target.value;this._validValueRegExp.test((0,lt.stripLTRMarks)(t))&&(!this.props.isSelected&&this.props.onControlFocused&&this.props.onControlFocused(0),this._onModifiedCallback(),this._onPriceSelected(t))},this._onTouchStart=e=>{this._swipeState.y=e.touches[0].clientY},this._onTouchMove=e=>{if(e.changedTouches&&e.changedTouches.length&&null!==this._swipeState.y){const t=e.changedTouches[0].clientY-this._swipeState.y,r=this._swipeState.prevDiff??0;if(Math.abs(t-r)<10)return;this._swipeState.direction=t>r?"up":"down",this._swipeState.prevDiff=t,
"up"===this._swipeState.direction?this._prependPriceOnDropdown():"down"===this._swipeState.direction&&this._appendPriceOnDropdown()}},this._onTouchEnd=e=>{this._resetSwipeState()},this._onDropdownWheelNoPassive=e=>{e.preventDefault(),e.stopPropagation(),e.deltaY<0?this._prependPriceOnDropdown():this._appendPriceOnDropdown()},this._onInputMouseWheel=e=>{e.preventDefault(),e.stopPropagation(),this.props.isSelected&&(this._onModifiedCallback(),e.deltaY<0?this._changeValueByStep(1):this._changeValueByStep(-1))},this._onSelect=(e,t)=>{this.setState({selected:e},(()=>{this._onModifiedCallback(),this._closeDropdown(),this._onPriceSelected(t.displayPrice)}))},this._onNavigate=()=>{const e=this.state.command&&"prev"===this.state.command.name,t=this.state.command&&"next"===this.state.command.name;e?this._changeValueByStep(1):t&&this._changeValueByStep(-1)},this._onMouseOver=()=>{this.setState({mouseOver:!0})},this._onMouseOut=()=>{this.setState({mouseOver:!1})},this._onCaretButtonClick=e=>{e.stopPropagation(),this.state.isOpened?this._closeDropdown():(this._openDropdown(),fe.mobiletouch||this._priceInput?.setSelectionRange(this.props.displayPrice.length,this.props.displayPrice.length))},this._setPriceInput=e=>{this._priceInput=e},this._setPriceControl=e=>{this._priceControl=e},this._scrollPriceByArrow=e=>{e.preventDefault(),this._onModifiedCallback();const t=e.keyCode===Fe.KeyCode.UpArrow?1:-1;!this.props.isSelected&&this.props.onControlFocused&&this.props.onControlFocused(0),this._changeValueByStep(t)},this._selectPrev=e=>{e.preventDefault(),this._onModifiedCallback(),this.setState({command:{name:"prev"}})},this._selectNext=e=>{e.preventDefault(),this._onModifiedCallback(),this.setState({command:{name:"next"}})},this._openDropdown=()=>{this.setState({isOpened:!0,keyboardEventHandlers:this._getKeyboardEventHandlers("open")})},this._closeDropdownWithStopPropagation=e=>{e&&(e.stopPropagation(),e.nativeEvent.stopImmediatePropagation()),this._closeDropdown()},this._closeDropdown=()=>{const e=dt(this.props.priceIndex,this.props.priceFormatter,this.props.indexToPrice);this.setState({isOpened:!1,keyboardEventHandlers:this._getKeyboardEventHandlers("close"),priceList:e,selected:ut(e,this.props.priceIndex)})},e.isFractional&&(this._validValueRegExp=/^([-−]?([0-9]+'?[0-9]*([0-9]+'?)[0-9]*)|([-−]?[0-9]*))$/);const t=dt(e.priceIndex,e.priceFormatter,e.indexToPrice);this.state={priceList:t,selected:ut(t,e.priceIndex),isOpened:!1,mouseOver:!1,keyboardEventHandlers:this._getKeyboardEventHandlers("close")},this._resetSwipeState()}componentDidMount(){this._setInitialFocus()}render(){const e=s.createElement("span",{className:ft.priceLabel},this.props.priceLabel),t=s.createElement(Oe.CaretButton,{isDropped:this.state.isOpened,onFocus:this._onCaretButtonFocus,onClick:this._onCaretButtonClick});return s.createElement(me.OutsideEvent,{mouseDown:!0,touchStart:!0,handler:this._onOutsideClick},(r=>s.createElement("span",{className:this.props.className,ref:r},e,s.createElement(vt.PrimaryInputSelector,{inputId:"absolute-price-field",
handleSelect:this._onFocus,inputElement:this._priceInput,isSelected:this.props.isSelected,tooltipText:this.props.primaryPriceSelectorTooltip},s.createElement(gt,{className:l(ft.input,this.props.status===M.OrderPanelStatus.Wait&&ft.wait),keyboardEventHandlers:this.state.keyboardEventHandlers,value:(0,lt.startWithLTR)((0,lt.stripLTRMarks)(this.props.displayPrice)),containerReference:this._setPriceControl,inputReference:this._setPriceInput,warning:this.props.warning,error:this.props.error,errorMessage:this.props.errorMessage,onChange:this._onInputChange,noReadonlyStyles:!0,onBlur:this.props.onBlur,onMouseOver:this._onMouseOver,onMouseOut:this._onMouseOut,onWheelNoPassive:this._onInputMouseWheel,highlight:this.props.highlight,highlightRemoveRoundBorder:this.props.highlightRemoveRoundBorder,fontSizeStyle:this.props.fontSizeStyle,button:t,intent:this.props.intent,id:"absolute-price-field","aria-current":this.props.isSelected,"aria-controls":this.props.isSelected?"relative-price-field":void 0,readonly:!this.props.isSelected})),s.createElement(et,{anchor:"bottom",root:"document",reference:this._setDropdownRef,className:ft.list,command:this.state.command,isOpened:this.state.isOpened,items:this._makeListItems(),itemsClassName:ft.item,width:this._getDropdownWidth(),selected:this.state.selected,selectedClassName:ft.selected,target:null!==this._priceControl?this._priceControl:void 0,attachmentOffsetY:3,inheritWidthFromTarget:!1,shouldScrollIfNotVisible:!1,onSelect:this._onSelect,onNavigate:this._onNavigate,onDropdownTouchStart:this._onTouchStart,onDropdownTouchMove:this._onTouchMove,onDropdownTouchEnd:this._onTouchEnd,onDropdownWheelNoPassive:this._onDropdownWheelNoPassive}))))}componentDidUpdate(e,t){if(this.props.priceIndex===e.priceIndex)return;const r=dt(this.props.priceIndex,this.props.priceFormatter,this.props.indexToPrice);this.setState({priceList:r,selected:ut(r,this.props.priceIndex)})}_onPriceSelected(e){const t=this.props.priceFormatter.parse(e);t.res||this.props.errorHandler(!0,t.error),this.props.onPriceSelected(e)}_resetSwipeState(){this._swipeState={direction:null,y:null,prevDiff:null}}_getKeyboardEventHandlers(e){return"close"===e?{[Fe.KeyCode.UpArrow]:this._scrollPriceByArrow,[Fe.KeyCode.DownArrow]:this._scrollPriceByArrow}:{[Fe.KeyCode.Enter]:this._closeDropdownWithStopPropagation,[Fe.KeyCode.Escape]:this._closeDropdownWithStopPropagation,[Fe.KeyCode.UpArrow]:this._selectPrev,[Fe.KeyCode.DownArrow]:this._selectNext}}_makeListItems(){return this.state.priceList.map((e=>({...e,elem:s.createElement(nt,{price:e.displayPrice,ask:this.props.priceFormatter.format(this.props.ask),bid:this.props.priceFormatter.format(this.props.bid),last:this.props.priceFormatter.format(this.props.last),side:this.props.side,displayPriceLabel:!0})})))}_prependPriceOnDropdown(){const e=[pt(this.state.priceList[0].priceIndex+1,this.props.priceFormatter,this.props.indexToPrice)].concat(this.state.priceList.slice(0,-1));this.setState({priceList:e,selected:ut(e,this.props.priceIndex)})}_appendPriceOnDropdown(){
const e=this.state.priceList.length-1,t=pt(this.state.priceList[e].priceIndex-1,this.props.priceFormatter,this.props.indexToPrice),r=this.state.priceList.slice(1).concat(t);this.setState({priceList:r,selected:ut(r,this.props.priceIndex)})}_changeValueByStep(e){const t=this.props.priceIndex+e,r=this.props.indexToPrice(t);this._onPriceSelected((0,Te.formatValue)(r,this.props.priceFormatter))}_getDropdownWidth(){if(this.state.isOpened&&null!==this._priceControl&&(this.context.mode===M.OrderEditorDisplayMode.Popup||this.state.priceList[0].displayPrice.length<=7)){return parseFloat(window.getComputedStyle(this._priceControl,null).getPropertyValue("width"))+2}}_onModifiedCallback(){this.props.onModifiedCallback&&this.props.onModifiedCallback()}}bt.contextType=A.WidgetContext,bt.defaultProps={className:"",inputClassName:""};var Ct,Et=r(743003);function yt(e,t){return e.findIndex((e=>e.displayValue===t))}!function(e){e[e.Up=1]="Up",e[e.Down=-1]="Down"}(Ct||(Ct={}));const St=/^[-+]?[0-9]*\.?[0-9]+$/;function wt(e,t){const r=(0,Et.capitalizeFirstLetter)(e);return t>0?`${r} + ${Math.abs(t)}`:t<0?`${r} - ${Math.abs(t)}`:`${r}`}function _t(e){return String(e)}function kt(e,t){const r=e.offset;return o=e.base,s=function(e,t){return(Math.abs(e)>=1?e:0)+(1===t?1:-1)}(r,t),{displayValue:wt(o,s),base:o,offset:s};var o,s}function Pt(e,t,r){const o=[],s=[],n={displayValue:wt(t,e),base:t,offset:e};for(let e=0;e<r;e++)o.unshift(kt(0===e?n:o[0],1)),s.push(kt(0===e?n:s[e-1],-1));return[...o,n,...s]}function It(){return"phone"!==ct.mediaState.device&&"phone-vertical"!==ct.mediaState.device?6:4}var Nt=r(249065);function Ot(e){return s.createElement("div",{className:Nt.relativePriceDropdownItem},s.createElement("div",{className:Nt.price},e.price))}var xt=r(583874);const Mt=/^-?[0-9]*$/,Dt=(0,Fe.makeKeyboardListener)(at.InputWithError),Bt=E.t(null,{context:"price"},r(820163)),Tt=E.t(null,void 0,r(366123));class Ft extends s.PureComponent{constructor(e){super(e),this._priceInput=null,this._priceControl=null,this._dropdown=null,this._setDropdownRef=e=>{this._dropdown=e},this._onMouseOver=e=>{this.setState({mouseOver:!0})},this._onMouseOut=()=>{this.setState({mouseOver:!1})},this._onOutsideClick=e=>{this.state.isOpened&&this._dropdown&&!this._dropdown.contains(e.target)&&(this._closeDropdown(),this._priceInput?.blur())},this._onCaretButtonFocus=()=>{this._priceInput?.focus()},this._onFocus=()=>{this.setState({isEditing:!0,displayValue:this.state.displayValue===wt(this.props.base,this.props.offset)?_t(this.props.offset):this.state.displayValue}),this.props.onControlFocused&&this.props.onControlFocused(1)},this._onBlur=()=>{this.props.error||this.setState({isEditing:!1,displayValue:wt(this.props.base,this.props.offset)},(()=>{this._trackExitEditingModeEvent(this.props.offset)}))},this._onInputChange=e=>{this._onModifiedCallback();const t=e.currentTarget.value;var r;(!this.props.isSelected&&this.props.onControlFocused&&this.props.onControlFocused(1),Mt.test(t))&&(r=t,St.test(r)?this.setState({displayValue:t},(()=>{
this._onPriceOffsetSelected(Number(t))})):(this.setState({displayValue:t}),this.props.errorHandler(!0,Tt)))},this._onTouchStart=e=>{this._swipeState.y=e.touches[0].clientY},this._onTouchMove=e=>{if(e.changedTouches&&e.changedTouches.length&&null!==this._swipeState.y){const t=e.changedTouches[0].clientY-this._swipeState.y,r=this._swipeState.prevDiff??0;if(Math.abs(t-r)<10)return;this._swipeState.direction=t>r?"up":"down",this._swipeState.prevDiff=t,"up"===this._swipeState.direction?this._prependPriceOnTouchMove():"down"===this._swipeState.direction&&this._appendPriceOnTouchMove()}},this._onTouchEnd=e=>{this._resetSwipeState()},this._onDropdownWheelNoPassive=e=>{e.preventDefault(),e.stopPropagation(),e.deltaY<0?this._prependPriceOnWheel():this._appendPriceOnWheel()},this._onInputMouseWheel=e=>{e.preventDefault(),e.stopPropagation(),this.props.isSelected&&(this._onModifiedCallback(),e.deltaY<0?this._changeValueByStep(1):this._changeValueByStep(-1))},this._onSelect=(e,t)=>{this.setState({displayValue:t.text,selected:e},(()=>{this._onModifiedCallback(),this._closeDropdown(),this._onPriceOffsetSelected()}))},this._onNavigate=(e,t)=>{const r=this.state.command&&"prev"===this.state.command.name,o=this.state.command&&"next"===this.state.command.name;r?this._changeValueByStep(1):o&&this._changeValueByStep(-1)},this._onCaretButtonClick=e=>{e.preventDefault(),e.stopPropagation(),this.state.isOpened?this._closeDropdown():(this._openDropdown(),!fe.mobiletouch&&this._priceInput&&this._priceInput.setSelectionRange(this.state.displayValue.length,this.state.displayValue.length))},this._switchEditingMode=e=>{e.preventDefault();const t=Number(this.state.displayValue);if(Number.isInteger(t)||this.props.error)this._priceInput?.blur();else{const e=Math.round(t),r=Pt(e,this.props.base,It()),o=yt(r,wt(this.props.base,e));this.setState({displayValue:String(e),prices:r,selected:o,isEditing:!1},(()=>{this._onPriceOffsetSelected(),this._priceInput?.blur()}))}},this._setPriceInput=e=>{this._priceInput=e},this._setPriceControl=e=>{this._priceControl=e},this._scrollPriceByArrow=e=>{e.preventDefault(),this._onModifiedCallback();const t=e.keyCode===Fe.KeyCode.UpArrow?1:-1;!this.props.isSelected&&this.props.onControlFocused&&this.props.onControlFocused(1),this._changeValueByStep(t)},this._selectPrev=e=>{e.preventDefault(),this._onModifiedCallback(),this.setState({command:{name:"prev"}})},this._selectNext=e=>{e.preventDefault(),this._onModifiedCallback(),this.setState({command:{name:"next"}})},this._openDropdown=()=>{this.setState({isOpened:!0,keyboardEventHandlers:this._getKeyboardEventHandlers("open")})},this._closeDropdownOnKeyDown=e=>{e&&(e.stopPropagation(),e.nativeEvent.stopImmediatePropagation()),this._closeDropdown()},this._closeDropdown=()=>{const e=wt(this.props.base,this.props.offset),t=Pt(this.props.offset,this.props.base,It()),r=yt(t,e);this.setState({isOpened:!1,keyboardEventHandlers:this._getKeyboardEventHandlers("close"),prices:t,selected:r})},this._trackExitEditingModeEvent=e=>{
this.props.trackEvent&&this.props.trackEvent("Order Ticket","Relative Price - Exit Editing Mode",String(e))};const t=wt(e.base,e.offset),r=Pt(e.offset,e.base,It()),o=yt(r,t);this.state={displayValue:t,prices:r,selected:o,isOpened:!1,isEditing:!1,mouseOver:!1,keyboardEventHandlers:this._getKeyboardEventHandlers("close")},this._resetSwipeState()}render(){const e=s.createElement(Oe.CaretButton,{isDropped:this.state.isOpened,onFocus:this._onCaretButtonFocus,onClick:this._onCaretButtonClick});return s.createElement(me.OutsideEvent,{mouseDown:!0,touchStart:!0,handler:this._onOutsideClick},(t=>s.createElement("span",{className:this.props.className,ref:t},s.createElement("span",{className:xt.pipsLabel},Bt),s.createElement(vt.PrimaryInputSelector,{handleSelect:this._onFocus,inputId:"relative-price-field",inputElement:this._priceInput,isSelected:this.props.isSelected,tooltipText:this.props.primaryPriceSelectorTooltip},s.createElement(Dt,{noReadonlyStyles:!0,button:e,onChange:this._onInputChange,readonly:!this.props.isSelected,"aria-current":this.props.isSelected,inputReference:this._setPriceInput,id:"relative-price-field",containerReference:this._setPriceControl,onWheelNoPassive:this._onInputMouseWheel,className:l(xt.input,this.props.status===M.OrderPanelStatus.Wait&&xt.wait),keyboardEventHandlers:this.state.keyboardEventHandlers,value:this.state.displayValue,error:this.props.error,errorMessage:this.props.errorMessage,onBlur:this._onBlur,onMouseOver:this._onMouseOver,onMouseOut:this._onMouseOut,highlight:this.props.highlight,highlightRemoveRoundBorder:this.props.highlightRemoveRoundBorder,fontSizeStyle:this.props.fontSizeStyle,intent:this.props.intent,"aria-controls":this.props.isSelected?"absolute-price-field":void 0})),s.createElement(et,{anchor:"bottom",root:"document",reference:this._setDropdownRef,className:xt.list,command:this.state.command,isOpened:this.state.isOpened,items:this._makeListItems(),itemsClassName:xt.item,selected:this.state.selected,selectedClassName:xt.selected,target:null!==this._priceControl?this._priceControl:void 0,attachmentOffsetY:3,inheritWidthFromTarget:!0,shouldScrollIfNotVisible:!1,onSelect:this._onSelect,onNavigate:this._onNavigate,onDropdownTouchStart:this._onTouchStart,onDropdownTouchMove:this._onTouchMove,onDropdownTouchEnd:this._onTouchEnd,onDropdownWheelNoPassive:this._onDropdownWheelNoPassive}))))}componentDidUpdate(e){if(this.props.offset!==e.offset||this.props.base!==e.base){const e=wt(this.props.base,this.props.offset),t=Pt(this.props.offset,this.props.base,It()),r=yt(t,e);this.setState({prices:t,selected:r})}this.state.isEditing&&this.props.isSelected||this.props.offset===e.offset&&this.props.base===e.base&&this.props.error===e.error||this.setState({displayValue:wt(this.props.base,this.props.offset),isEditing:!1})}_onPriceOffsetSelected(e){const t=void 0!==e?e:this.state.prices[this.state.selected].offset;this.props.errorHandler(!1),this.props.onPriceOffsetSelected(t)}_onModifiedCallback(){this.props.onModifiedCallback&&this.props.onModifiedCallback()}_resetSwipeState(){
this._swipeState={direction:null,y:null,prevDiff:null}}_getKeyboardEventHandlers(e){return"close"===e?{[Fe.KeyCode.UpArrow]:this._scrollPriceByArrow,[Fe.KeyCode.DownArrow]:this._scrollPriceByArrow,[Fe.KeyCode.Enter]:this._switchEditingMode}:{[Fe.KeyCode.Enter]:this._closeDropdownOnKeyDown,[Fe.KeyCode.Escape]:this._closeDropdownOnKeyDown,[Fe.KeyCode.UpArrow]:this._selectPrev,[Fe.KeyCode.DownArrow]:this._selectNext}}_makeListItems(){return this.state.prices.map(((e,t)=>({base:e.base,offset:e.offset,text:e.displayValue,elem:s.createElement(Ot,{price:e.displayValue})})))}_prependPriceOnWheel(){const e=kt(this.state.prices[0],1);this.setState((t=>({prices:[e].concat(t.prices.slice(0,-1)),selected:0===t.selected?1:t.selected+1})))}_appendPriceOnWheel(){const e=this.state.prices.length-1,t=kt(this.state.prices[e],-1);this.setState((r=>({prices:r.prices.slice(1).concat(t),selected:r.selected===e?e-1:r.selected-1})))}_prependPriceOnTouchMove(){const e=this.state.prices[0],t=[];t.unshift(kt(e,1)),this.setState((e=>({prices:t.concat(e.prices.slice(0,this.state.prices.length-1)),selected:0===e.selected?1:e.selected+1})))}_appendPriceOnTouchMove(){const e=this.state.prices.length-1,t=this.state.prices[e],r=[];r.push(kt(t,-1)),this.setState((t=>({prices:t.prices.slice(1).concat(r),selected:t.selected===e?e-1:t.selected-1})))}_changeValueByStep(e){const t=this.props.offset+e,r=this.state.isEditing?_t(t):wt(this.props.base,t);this.setState({displayValue:r},(()=>{this._onPriceOffsetSelected(t)}))}}var Rt=r(44006);const Ht={1:E.t(null,void 0,r(750513)),2:E.t(null,void 0,r(733848))};function Lt(e){const{model:t,priceType:r,focus:o,priceLabel:n,trackEvent:i}=e,a=(0,s.useContext)(A.SettingsContext),{orderPanelStatus:c,isFractional:u}=(0,s.useContext)(A.WidgetContext),d=(0,b.useObservable)(t.priceControlData$,t.getPriceControlData()),p=(0,b.useObservable)(t.focusedControl$,t.getFocusedControl()),h=(0,b.useObservable)(t.error$,t.getError()),m=(0,b.useObservable)(t.quotes$,{ask:0,bid:0,trade:0}),v=(0,b.useObservable)(t.side$),f="medium",g=a.showRelativePriceControl&&!t.isRelativePriceControlHidden,C=h.msg,E=c!==M.OrderPanelStatus.Wait&&h.res&&"warning"===h.severity,y=_(0),S=_(1),w=Ht[r];return s.createElement(ke.ControlGroup,{cols:g?2:1,rows:1,className:Rt.controlGroup},s.createElement(bt,{...k(0),className:l(Rt.absolutePriceControl,g&&Rt.withRelative),priceType:r,isSelected:y,status:c,priceIndex:d?d.index:0,displayPrice:d?d.absolutePrice:"",ask:(0,Te.getAsk)(m),bid:(0,Te.getBid)(m),last:(0,Te.getLast)(m),side:v,priceFormatter:t.formatter,warning:E,error:h.res&&"error"===h.severity&&y,errorMessage:C,errorHandler:P,onControlFocused:I,onPriceSelected:t.setAbsolutePrice,trackEvent:i,onModifiedCallback:t.modifiedAbsolutePriceControlStat,focus:o,fontSizeStyle:f,priceLabel:n,indexToPrice:t.indexToPrice,isFractional:u,primaryPriceSelectorTooltip:w}),g&&s.createElement(Ft,{...k(1),isSelected:S,offset:d?d.offset:0,base:d?d.base:"",className:Rt.relativePriceControl,status:c,error:h.res&&"error"===h.severity&&S,errorMessage:C,errorHandler:P,
fontSizeStyle:f,onControlFocused:I,onPriceOffsetSelected:t.setPriceOffset,trackEvent:i,onModifiedCallback:t.modifiedRelativePriceControlStat,primaryPriceSelectorTooltip:w}));function _(e){return c!==M.OrderPanelStatus.Wait&&p===e}function k(e){const t={};return t.highlight=g&&_(e),h.res&&"error"===h.severity&&_(e)&&(t.intent="danger"),t}function P(e,r){t.setControlError(e,r)}function I(e){e!==t.getFocusedControl()&&t.setFocusedControl(e),t.onControlFocused.fire()}}function zt(e){const{model:t,trackEvent:o,focus:n}=e,i=(0,b.useObservable)(t.orderType$),a=4===i;return s.createElement(s.Fragment,null,3===i||a?s.createElement(Lt,{key:t.stopPriceModel.id,model:t.stopPriceModel,priceType:t.stopPriceModel.priceType,trackEvent:o,focus:n,priceLabel:a?E.t(null,void 0,r(475299)):E.t(null,void 0,r(7953))}):null,1===i||a?s.createElement(Lt,{key:t.limitPriceModel.id,model:t.limitPriceModel,priceType:t.limitPriceModel.priceType,trackEvent:o,focus:n,priceLabel:a?E.t(null,void 0,r(277646)):E.t(null,void 0,r(7953))}):null)}var Wt=r(570446),At=r(314266);function Vt(e){const{model:t,onClose:r}=e,{value$:o,getValue:n,setValue:i,getOrderType:a,durationMetaInfoList:l,onModifiedCallback:c,onControlFocused:u,setError:d}=t,p=(0,b.useObservable)(o,n());if(null===p)return s.createElement(s.Fragment,null);return s.createElement(At.DurationControl,{currentDuration:p,durationMetaInfoList:function(){const e=a();return(0,Te.filterDurationsByOrderType)(l,e)}(),onDurationChanged:function(e){i(e,a()),d(!1),c()},onClose:r,onControlFocused:()=>u.fire(),onError:d,showErrorMessages:!0})}var Kt=r(649719);const Zt=s.lazy((async()=>({default:(await Promise.all([r.e(9722),r.e(8739),r.e(6112)]).then(r.bind(r,322642))).LeverageDialog}))),$t=(0,Kt.withDialogLazyLoad)(Zt);var Ut=r(27950),qt=r(664863);function Gt(e){const{leverage:t,onClick:o,onControlFocused:n,blocked:i,forceDisabled:a=!1,onBlockedClick:l}=e,u=void 0===t;let d=E.t(null,{replace:{value:String(t)}},r(452386));return u&&(d=E.t(null,void 0,r(701544))),i&&(d=E.t(null,void 0,r(329776))),s.createElement("div",{className:qt.container,onClick:function(){if(i)return n.fire(),void l()}},s.createElement(Ut.EditButton,{disabled:u||a||i,value:d,startSlot:u||i?void 0:"x",onClick:function(){o(),n.fire()},className:c()(qt.leverageButton,a&&qt.disabledLeverageButton)}))}function Yt(e){const{model:t}=e,r=(0,b.useObservable)(t.leverageInfo$,t.leverageInfo()),o=(0,b.useObservable)(t.blocked$,t.getBlocked()),n=t.leveragePreviewResult(),[i,a]=(0,s.useState)(!1),[l,c]=(0,s.useState)(r?.leverage);return(0,s.useEffect)((()=>{c(r?.leverage)}),[r]),s.createElement(s.Fragment,null,s.createElement(Gt,{onClick:u,leverage:l,forceDisabled:i,blocked:o,onControlFocused:t.onControlFocused,onBlockedClick:t.onBlockedClick}),null!==r&&i&&void 0!==l&&s.createElement($t,{symbol:t.symbol,displaySymbol:t.displaySymbol,leverage:l,brokerName:t.brokerName,orderType:t.orderType,side:t.side,isOpen:i,onClose:u,onLeverageSet:t.setLeverage,onLeveragePreview:t.previewLeverage,title:r.title,min:r.min,max:r.max,step:r.step,
customFields:t.customFields,infos:n?.infos,warnings:n?.warnings,errors:n?.errors,onSetLeverageValueInOrderWidget:function(e){c(e)}}));function u(){i||t.getLeverageInfo({symbol:t.symbol,orderType:t.orderType,side:t.side,customFields:t.customFields}),a((e=>!e))}}var Xt=r(567704),jt=r(724551);class Jt extends s.PureComponent{constructor(e){super(e)}render(){const e={width:`${this.props.progress}%`},t=this.props.usedMargin>this.props.availableMargin,o=t&&this.props.marginExceededTooltipText?this.props.marginExceededTooltipText:"",n=l(jt.progress,100===this.props.progress&&jt.fullFilled,t&&jt.exceededMargin,o&&"apply-common-tooltip");return s.createElement("div",{className:l(jt.progressBarContainer,this.props.wait&&jt.wait)},s.createElement("div",{className:jt.marginsContainer},s.createElement("div",{className:l(jt.margin,jt.usedMargin)},s.createElement("div",{className:jt.title},this.props.marginMeterTitle?this.props.marginMeterTitle:E.t(null,void 0,r(214775))),s.createElement("span",{className:jt.price},(0,Xt.formatInfoValue)(this.props.usedMargin))),s.createElement("div",{className:l(jt.margin),"data-name":"availableMargin"},s.createElement("span",{className:jt.price},(0,Xt.formatInfoValue)(this.props.availableMargin)))),s.createElement("div",{className:jt.progressBar},s.createElement("div",{className:jt.track},s.createElement("div",{className:n,style:e,title:o}))),this.props.description&&s.createElement("div",{className:jt.description},s.createElement("span",null,this.props.description),void 0!==this.props.solutionId&&!1))}}var Qt=r(888074);function er(e){const{model:t,infoTableData:o,isMobile:n,isScrollable:i=!0}=e,a=(0,s.useRef)(null),{orderPanelStatus:l}=(0,s.useContext)(A.WidgetContext),u=(0,b.useObservable)(t.usedMargin$,0),d=(0,b.useObservable)(t.availableMargin$,0),p=t.marginMeterConfig(),h=(0,b.useObservable)(t.brokerProvidedData$),m=(0,b.useObservable)(t.isBrokerProvidedDataLoading$),v=(0,b.useObservable)(t.brokerProvidedDataError$),f=(0,b.useObservable)(t.isBrokerProvidedDataApproximate$),g=void 0!==h?.rows&&0!==h.rows.length,C=s.createElement("div",{className:c()(Qt.orderInfo,i&&Qt.scrollable),ref:i?a:void 0},s.createElement("div",{className:Qt.title},t.title()),s.createElement("div",{className:Qt.contentWrapper},t.hasMarginMeter()&&s.createElement(Jt,{usedMargin:u,availableMargin:d,progress:(0,Xt.calculatedMarginRatio)(u,d),wait:l===M.OrderPanelStatus.Wait,marginExceededTooltipText:p?.marginExceededTooltipText,description:p?.description,solutionId:p?.solutionId,marginMeterTitle:p?.marginMeterTitle}),s.createElement($.InfoTable,{rows:o.rows,header:o.header,disabled:l===M.OrderPanelStatus.Wait}),g&&s.createElement(s.Fragment,null,s.createElement($.InfoTable,{rows:h.rows,header:E.t(null,void 0,r(959932)),disabled:l===M.OrderPanelStatus.Wait,isDataApproximate:f,isLoading:m,error:v}),h.disclaimer?function(e){if(!Array.isArray(e))return s.createElement("div",{className:Qt.disclaimer,key:e},e);const t=[];for(const r of e)"string"!=typeof r?void 0!==r.url&&t.push(s.createElement("a",{key:r.url,
className:Qt.disclaimerLink,href:r.url,target:"_blank"},r.text)):t.push(r);return s.createElement("div",{className:Qt.disclaimer},t)}(h.disclaimer):s.createElement(s.Fragment,null))));return n||!i?C:s.createElement(G,{className:Qt.scrollWrapper,scrollContainerRef:a},C)}var tr=r(41899),rr=r(853177),or=r(556013),sr=r(970267),nr=r(411471);const ir=(0,f.getLogger)("Trading.OrderPanel"),ar=E.t(null,void 0,r(645207)),lr=(E.t(null,void 0,r(731685)),E.t(null,void 0,r(711986)),E.t(null,void 0,r(733895)),E.t(null,void 0,r(470877)),E.t(null,void 0,r(877309)),parseInt(nr["min-order-ticket-height"])),cr=parseInt(nr["min-order-info-height"]);function ur(){document.activeElement instanceof HTMLElement?document.activeElement.blur():ir.logWarn("Failed to deselect: active element is not HTMLElement")}const dr=(0,s.forwardRef)(((e,t)=>{const{model:r,focus:o,trackEvent:n,orderPanelStatus:i,orderType:a,setOrderType:l,isNoQuotes:c,orderWidgetRef:u,isMobile:d,isPopupMode:h,requestResize:v,shouldShowActionButton:f,showOrderInfo:g,infoTableData:b}=e;(0,s.useEffect)((()=>{(h||g)&&v?.()}),[a,h,g]);const C=i===M.OrderPanelStatus.Wait||p.CheckMobile.any()?void 0:o,E=function(e,t){return Boolean(2!==t&&(!e.existingOrder||e.existingOrder&&e.supportModifyOrderPrice))}(r,a),y=function(e,t){const r=2!==t||e.supportMarketBrackets();return Boolean(e.supportBrackets()&&r)}(r,a)||function(e){return Boolean(e.supportCryptoBrackets)}(r),S=function(e){return Boolean(e.durationModel&&(!e.existingOrder||e.existingOrder&&e.supportModifyDuration)&&e.durationModel.isDurationsAvailable())}(r),w=function(e){return Boolean(e.supportLeverageButton)}(r),_=E||r.supportModifyQuantity||w,k=E||r.supportModifyQuantity||w||y,P=[M.OrderPanelStatus.Active,M.OrderPanelStatus.Wait].includes(i),I=r.customFieldsModel.getCustomFieldsModels(),N=(r.links(),[]),O=(0,s.useRef)(null),x={model:r,supportOrderPrice:E,supportBrackets:y,supportDuration:S,customFieldModels:I,supportLeverageButton:w,centerBlockRef:O,trackEvent:n,currentFocus:C,showBracketSeparator:_,focusOrderWidget:function(){null!==u.current&&u.current.focus()},showCustomFieldsSeparator:k,orderPanelStatus:i,showOrderInfo:g,infoTableData:b,isMobile:d};return P&&N.push({id:"symbol-specific-warning",warningMessageSource:r.symbolSpecificWarningMessage}),s.createElement(s.Fragment,null,r.sideModel&&s.createElement("div",{className:nr.side},s.createElement(V,{model:r.sideModel,disabled:r.existingOrder})),r.limitPriceModel&&r.stopPriceModel&&s.createElement(le,{orderType:a,setOrderType:l,disabled:r.existingOrder&&!r.supportModifyOrderType,supportMarketOrders:r.supportMarketOrders(),supportLimitOrders:r.supportLimitOrders(),supportStopOrders:r.supportStopOrders(),supportStopLimitOrders:r.supportStopLimitOrders()}),d?s.createElement(pr,{...x}):s.createElement(G,{className:nr.scrollWrapper,scrollContainerRef:O},s.createElement(pr,{...x})),!1,void 0!==r.buttonModel&&f&&s.createElement("div",{className:nr.doneButton},s.createElement(Wt.PlaceAndModifyButton,{model:r,reference:(0,m.isomorphicRef)(t),buttonModel:r.buttonModel
}),!1),s.createElement(rr.TradingPanelWarningInformersContainer,{warningInformersItems:N}))}));function pr(e){const{model:t,supportOrderPrice:r,supportBrackets:o,supportDuration:n,customFieldModels:i,supportLeverageButton:a,centerBlockRef:c,trackEvent:u,currentFocus:d,showBracketSeparator:p,focusOrderWidget:h,showCustomFieldsSeparator:m,orderPanelStatus:f,showOrderInfo:g,infoTableData:b,isMobile:C}=e;return t.limitPriceModel&&t.stopPriceModel&&t.quantityModel&&s.createElement("div",{className:l(nr.centerBlock),ref:c},r&&s.createElement(zt,{model:t,trackEvent:u,focus:d}),t.supportModifyQuantity&&s.createElement("div",{className:nr.quantityWrapper},s.createElement(hr,{model:t,orderPanelStatus:f,currentFocus:d,trackEvent:u})),o&&s.createElement("div",{className:nr["brackets-wrapper"]},p&&s.createElement("div",{className:nr.separator}),s.createElement("div",{className:nr.brackets},s.createElement(ce.BracketControlGroup,{model:t,focus:d}))),n&&s.createElement("div",{className:nr.duration},s.createElement(Vt,{model:(0,v.ensureDefined)(t.durationModel)})),i.length>0&&s.createElement("div",{className:nr.customFieldsWrapper},m&&s.createElement("div",{className:nr.separator}),s.createElement(or.OrderPanelCustomFields,{customFieldModels:i,onClose:h})),a&&s.createElement("div",{className:nr.leverage},s.createElement(Yt,{model:(0,v.ensureDefined)(t.leverageModel)})),g&&null!==b&&s.createElement("div",{className:nr.orderInfoWrapper},s.createElement("div",{className:nr.separator}),s.createElement(er,{"data-name":"order-info",model:(0,v.ensureNotNull)(t.orderInfoModel),infoTableData:b,isMobile:C,isScrollable:!1})))}function hr(e){const{model:t,orderPanelStatus:r,currentFocus:o,trackEvent:n}=e;return t.supportCryptoExchangeOrderTicket()?s.createElement(Be,{model:t.quantityModel,status:r,focus:o,useContextValidation:t.useContextValidation}):s.createElement(_e,{model:t.quantityModel,symbolHasLotSize:t.symbolHasLotSize,status:r,trackEvent:n,focus:o,useContextValidation:t.useContextValidation})}function mr(e){const{model:t}=e,r=t.tradableSolution?s.createElement(h.SquareButton,{onClick:t.tradableSolution.apply,size:"small"},t.tradableSolution.title):void 0;return s.createElement("div",{className:nr.emptyStateBlockWrapper},s.createElement(g.EmptyStateBlock,{iconClass:nr.sadScreenIcon,title:ar,titleSize:"normal",text:(0,v.ensureDefined)(t.notTradableText),action:r}))}function vr(e){const{model:t,requestResize:r}=e,{mode:o,orderPanelStatus:n}=(0,s.useContext)(A.WidgetContext),[i,a]=(0,s.useState)(null),[c,h]=(0,s.useState)(),m=(0,b.useObservable)(t.isNoQuotes$,!1),f=(0,b.useObservable)(t.orderInfoModel?.infoTableData$??(0,d.of)(null),null),g=(0,b.useObservable)(t.orderType$,t.getOrderType()),E=(0,s.useRef)(null),w=(0,s.useRef)(null),_=(0,s.useRef)(null),k=function(e,t){return t===M.OrderPanelStatus.Preview&&null!==e.orderPreviewModel}(t,n),P=function(e){const t=e.orderInfoModel?.getOrderInfoTableRowsCount()||0;return!e.needSignIn&&!e.noBroker&&!e.notTradableText&&t>0&&y.enabled("order_info")
}(t)&&null!==f&&!k,I=p.CheckMobile.any()||p.CheckMobile.isIPad(),N=o===M.OrderEditorDisplayMode.Popup,O=o===M.OrderEditorDisplayMode.ResizableDrawer;(0,s.useEffect)((()=>{const e=setTimeout((()=>{void 0!==c&&0!==c&&(0,tr.isInteger)(c)&&u.setValue(S.settingsKeys.PANEL_INFO_HEIGHT,c)}),100);return()=>clearTimeout(e)}),[c]),(0,s.useEffect)((()=>{n===M.OrderPanelStatus.Wait&&E.current?.contains(document.activeElement)&&ur()}),[n]),(0,s.useEffect)((()=>{let e=u.getInt(S.settingsKeys.PANEL_INFO_HEIGHT,0);const t=(E.current?.clientHeight??0)-lr;e>t?e=t:e<cr&&(e=cr),isNaN(e)||h(e)}),[]),(0,s.useEffect)((()=>{if(null!==i)return i.addEventListener("mousedown",D,{passive:!1}),i.addEventListener("touchstart",D,{passive:!1}),()=>{i.removeEventListener("mousedown",D),i.removeEventListener("touchstart",D)}}),[i]);const x=(0,s.useCallback)((e=>{null!==e&&a(e)}),[]);function D(e){if(e.defaultPrevented||!e.cancelable)return;e.preventDefault();const t=w.current?.clientHeight??0,r=(E.current?.clientHeight??0)-lr;let o;o="touches"in e?e.touches[0].clientY:e.clientY;const s=e=>{let s;e.preventDefault(),s="touches"in e?e.touches[0].clientY:e.clientY;let n=t+(o-s);n>r?n=r:n<cr&&(n=cr),h(n)},n=()=>{document.removeEventListener("mousemove",s),document.removeEventListener("touchmove",s),document.removeEventListener("mouseup",n),document.removeEventListener("touchend",n)};document.addEventListener("mousemove",s),document.addEventListener("touchmove",s,{passive:!1}),document.addEventListener("mouseup",n),document.addEventListener("touchend",n,{passive:!1})}return s.createElement("div",{className:l(nr.orderWidget),onKeyDown:function(e){const r=(0,C.hashFromEvent)(e);if(!(13!==r&&r!==C.hashShiftPlusEnter||t.isButtonDisabled()||null===_.current)){if(p.CheckMobile.any())return ur();_.current.focus(),e.defaultPrevented||_.current.click()}},tabIndex:-1,ref:E},s.createElement("div",{className:nr.orderTicket},(N||o===M.OrderEditorDisplayMode.ResizableDrawer)&&s.createElement(sr.HeaderContainer,{...e.model.headerStateValue}),!1,!1,t.notTradableText&&s.createElement(mr,{model:t}),!t.needSignIn&&!t.noBroker&&!t.notTradableText&&(k?s.createElement(J,{model:(0,v.ensureNotNull)(t.orderPreviewModel),side:t.side(),loading$:t.loading$,isMobile:I,shouldShowActionButton:e.shouldShowActionButton,isPopupMode:N,requestResize:r}):s.createElement(dr,{...e,ref:_,orderType:g,setOrderType:t.setOrderType,orderPanelStatus:n,isNoQuotes:m,orderWidgetRef:E,isMobile:I,isPopupMode:N,showOrderInfo:P&&(N||O),requestResize:r,infoTableData:f}))),P&&o===M.OrderEditorDisplayMode.Panel&&s.createElement("div",{ref:w,className:nr.orderInfoAndSeparator,style:{height:c}},s.createElement("div",{className:nr.separator}),s.createElement("div",{className:nr.resizeHandler,ref:x}),s.createElement(er,{"data-name":"order-info",model:(0,v.ensureNotNull)(t.orderInfoModel),infoTableData:f,isMobile:I})))}var fr=r(799591);function gr(e){return s.createElement("div",{"data-name":"order-panel",className:fr.orderPanel},s.createElement(vr,{...e,key:e.model.id,shouldShowActionButton:!0}))}
var br=r(930894),Cr=r(533408),Er=r(274837),yr=r(866562);const Sr=s.memo((e=>{const{model:t,isOpened:r,onClose:o,trackEvent:n,focus:i,onOpen:a}=e;return(0,Er.useCommonDialogHandlers)({isOpened:r,onOpen:a,onClose:o}),s.createElement(Cr.AdaptivePopupDialog,{className:yr.dialog,isOpened:r,dataName:"order-dialog-popup",render:function(e){const{requestResize:r}=e;return s.createElement(br.Body,{className:yr.dialogBody},s.createElement(vr,{key:t.id,model:t,trackEvent:n,focus:i,requestResize:r,shouldShowActionButton:!0}))},onClose:()=>o?.()})}));var wr=r(910549),_r=r(977540),kr=r(110686);const Pr=s.memo((e=>{const{model:t,isOpened:r,onClose:o,trackEvent:n,focus:i,onOpen:a,forceCollapsedOrderDrawer:l}=e;return(0,Er.useCommonDialogHandlers)({isOpened:r,onOpen:a,onClose:o}),s.createElement(_r.ResizableDrawer,{controlsContent:s.createElement(kr.OrderDrawerControlContent,{buttonModel:t.buttonModel,onClose:o,sideModel:t.sideModel,side:()=>t.side(),isButtonDisabled$:t.isButtonDisabled$,isButtonDisabled:()=>t.isButtonDisabled(),loading$:t.loading$,doneButtonClick:()=>t.doneButtonClick()}),disableSwipe:!1,disablePan:!1,initialContentHeight:l?_r.ResizableDrawerContentHeight.Collapsed:_r.ResizableDrawerContentHeight.Full},s.createElement(wr.PopupContext.Consumer,null,(e=>s.createElement(vr,{model:t,key:t.id,trackEvent:n,focus:i,shouldShowActionButton:!1}))))}));let Ir=null,Nr=null;function Or(e){const{viewModel:t,settings$:r,mode:o,onClose:n,onOpen:i,focus:l,trackEvent:c}=e,u=s.createElement(A.OrderTicketProvider,{settings$:r,mode:o,orderPanelStatus$:t.status$,isFractional:t.isFractional},s.createElement(Sr,{model:t,isOpened:!0,onOpen:i,onClose:()=>xr(n),trackEvent:c,focus:l}));Nr?Nr.render(u):(Ir=document.createElement("div"),document.body.appendChild(Ir),Nr=(0,a.createReactRoot)(u,Ir))}function xr(e){Ir&&(Nr?.unmount(),Nr=null,Ir=null,e?.())}const Mr="order-drawer-widget";function Dr(e){const{viewModel:t,settings$:r,mode:o,onClose:n,onOpen:l,focus:c,trackEvent:u,forceCollapsedOrderDrawer:d}=e,p=(0,i.getRootOverlapManager)().ensureWindow(Mr),h=s.createElement(A.OrderTicketProvider,{settings$:r,mode:o,orderPanelStatus$:t.status$,isFractional:t.isFractional},s.createElement(Pr,{model:t,isOpened:!0,onOpen:l,onClose:()=>Br(n),trackEvent:u,focus:c,forceCollapsedOrderDrawer:d}));Nr?Nr.render(h):Nr=(0,a.createReactRoot)(h,p)}function Br(e){Nr?.unmount(),Nr=null;(0,i.getRootOverlapManager)().removeWindow(Mr),e?.()}function Tr(e){const{viewModel:t,settings$:r,mode:o,resizerBridgeElement:i,focus:a,trackEvent:l}=e;n.render(s.createElement(A.OrderTicketProvider,{settings$:r,mode:o,orderPanelStatus$:t.status$,isFractional:t.isFractional},s.createElement(gr,{model:t,trackEvent:l,focus:a})),i)}function Fr(e){n.unmountComponentAtNode(e)}},649719:(e,t,r)=>{"use strict";r.d(t,{withDialogLazyLoad:()=>i});var o=r(50959),s=r(252130);const n=e=>{const[t,r]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{!t&&e&&r(!0)}),[e]),t};function i(e){return t=>{const r=(0,s.useIsMounted)();return(n(t.isOpen)||t.isOpen)&&r?o.createElement(o.Suspense,{fallback:null
},o.createElement(e,{...t})):null}}},115589:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9.36 5a4.44 4.44 0 0 0-4.39 4.5c0 2.49 1.97 4.5 4.39 4.5 1.62 0 3.04-.9 3.8-2.25l.84.5A5.33 5.33 0 0 1 9.36 15 5.43 5.43 0 0 1 4 9.5C4 6.46 6.4 4 9.36 4c1.98 0 3.71 1.1 4.64 2.75l-.84.5A4.36 4.36 0 0 0 9.36 5Z"/></svg>'},96634:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M5 9h9v1H5V9Z"/></svg>'},218666:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9.5 4.5h-1v4h-4v1h4v4h1v-4h4v-1h-4v-4Z"/></svg>'},610600:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M11.44 3.34a1.5 1.5 0 0 1 2.12 0l1.09 1.09a1.5 1.5 0 0 1 0 2.12l-7 7a1.5 1.5 0 0 1-1.06.44H4V11.4c0-.4.16-.78.44-1.06l7-7Zm1.41.7a.5.5 0 0 0-.7 0l-.7.7 1.8 1.79.69-.7a.5.5 0 0 0 0-.7l-1.09-1.08Zm-.3 3.2-1.8-1.8-5.6 5.6a.5.5 0 0 0-.15.36v1.59h1.59a.5.5 0 0 0 .35-.15l5.6-5.6Z"/></svg>'},672197:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M10 6.13V8L6 5.5 10 3v2.1A5 5 0 1 1 4 10a.5.5 0 0 1 1 0 4 4 0 1 0 5-3.87Z"/></svg>'},863509:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.57 7.85 9 12.62l5.43-4.77-1.32-1.5L9 9.95l-4.11-3.6-1.32 1.5Z"/></svg>'},168874:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m14 18.41-6.7-6.7 1.4-1.42 5.3 5.3 5.3-5.3 1.4 1.41-6.7 6.71Z"/></svg>'},914987:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><mask width="17" height="20" fill="#000" x="6" y="4" maskUnits="userSpaceOnUse" id="aayofezey"><path fill="#fff" d="M6 4h17v20H6z"/><path fill-rule="evenodd" d="M9 5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h11a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2H9Zm1 2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h9a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1h-9Zm2 8.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm2.5 1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm5.5-1.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM10.5 21a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm5.5-1.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm2.5 1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"/></mask><path fill="currentcolor" d="M8 7a1 1 0 0 1 1-1V4a3 3 0 0 0-3 3h2Zm0 14V7H6v14h2Zm1 1a1 1 0 0 1-1-1H6a3 3 0 0 0 3 3v-2Zm11 0H9v2h11v-2Zm1-1a1 1 0 0 1-1 1v2a3 3 0 0 0 3-3h-2Zm0-14v14h2V7h-2Zm-1-1a1 1 0 0 1 1 1h2a3 3 0 0 0-3-3v2ZM9 6h11V4H9v2Zm1 2V6a2 2 0 0 0-2 2h2Zm0 2V8H8v2h2Zm0 0H8c0 1.1.9 2 2 2v-2Zm9 0h-9v2h9v-2Zm0 0v2a2 2 0 0 0 2-2h-2Zm0-2v2h2V8h-2Zm0 0h2a2 2 0 0 0-2-2v2Zm-9 0h9V6h-9v2Zm.5 10a2.5 2.5 0 0 0 2.5-2.5h-2a.5.5 0 0 1-.5.5v2ZM8 15.5a2.5 2.5 0 0 0 2.5 2.5v-2a.5.5 0 0 1-.5-.5H8Zm2.5-2.5A2.5 2.5 0 0 0 8 15.5h2c0-.28.22-.5.5-.5v-2Zm2.5 2.5a2.5 2.5 0 0 0-2.5-2.5v2c.28 0 .5.22.5.5h2Zm2 0a.5.5 0 0 1-.5.5v2a2.5 2.5 0 0 0 2.5-2.5h-2Zm-.5-.5c.28 0 .5.22.5.5h2a2.5 2.5 0 0 0-2.5-2.5v2Zm-.5.5c0-.28.22-.5.5-.5v-2a2.5 2.5 0 0 0-2.5 2.5h2Zm.5.5a.5.5 0 0 1-.5-.5h-2a2.5 2.5 0 0 0 2.5 2.5v-2Zm4 2a2.5 2.5 0 0 0 2.5-2.5h-2a.5.5 0 0 1-.5.5v2ZM16 15.5a2.5 2.5 0 0 0 2.5 2.5v-2a.5.5 0 0 1-.5-.5h-2Zm2.5-2.5a2.5 2.5 0 0 0-2.5 2.5h2c0-.28.22-.5.5-.5v-2Zm2.5 2.5a2.5 2.5 0 0 0-2.5-2.5v2c.28 0 .5.22.5.5h2Zm-10 4a.5.5 0 0 1-.5.5v2a2.5 2.5 0 0 0 2.5-2.5h-2Zm-.5-.5c.28 0 .5.22.5.5h2a2.5 2.5 0 0 0-2.5-2.5v2Zm-.5.5c0-.28.22-.5.5-.5v-2A2.5 2.5 0 0 0 8 19.5h2Zm.5.5a.5.5 0 0 1-.5-.5H8a2.5 2.5 0 0 0 2.5 2.5v-2Zm4 2a2.5 2.5 0 0 0 2.5-2.5h-2a.5.5 0 0 1-.5.5v2ZM12 19.5a2.5 2.5 0 0 0 2.5 2.5v-2a.5.5 0 0 1-.5-.5h-2Zm2.5-2.5a2.5 2.5 0 0 0-2.5 2.5h2c0-.28.22-.5.5-.5v-2Zm2.5 2.5a2.5 2.5 0 0 0-2.5-2.5v2c.28 0 .5.22.5.5h2Zm2 0a.5.5 0 0 1-.5.5v2a2.5 2.5 0 0 0 2.5-2.5h-2Zm-.5-.5c.28 0 .5.22.5.5h2a2.5 2.5 0 0 0-2.5-2.5v2Zm-.5.5c0-.28.22-.5.5-.5v-2a2.5 2.5 0 0 0-2.5 2.5h2Zm.5.5a.5.5 0 0 1-.5-.5h-2a2.5 2.5 0 0 0 2.5 2.5v-2Z" mask="url(#aayofezey)"/></svg>'}}]);