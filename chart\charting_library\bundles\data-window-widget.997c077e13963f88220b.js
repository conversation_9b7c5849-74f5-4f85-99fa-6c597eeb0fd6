(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1205],{600046:e=>{e.exports={view:"view-_gbYDtbd",hidden:"hidden-_gbYDtbd",button:"button-_gbYDtbd",hover:"hover-_gbYDtbd",hoverEnable:"hoverEnable-_gbYDtbd",icon:"icon-_gbYDtbd",hovered:"hovered-_gbYDtbd",disabled:"disabled-_gbYDtbd",actions:"actions-_gbYDtbd",header:"header-_gbYDtbd",headerTitle:"headerTitle-_gbYDtbd",headerIcon:"headerIcon-_gbYDtbd",values:"values-_gbYDtbd",item:"item-_gbYDtbd",active:"active-_gbYDtbd",itemTitle:"itemTitle-_gbYDtbd"}},931532:e=>{e.exports={container:"container-UNtFS6lU",divider:"divider-UNtFS6lU"}},518561:e=>{e.exports={scrollWrap:"scrollWrap-FaOvTD2r"}},131987:e=>{e.exports={container:"container-Tv7LSjUz",overlayScrollWrap:"overlayScrollWrap-Tv7LSjUz",wrapper:"wrapper-Tv7LSjUz"}},151810:e=>{e.exports={wrap:"wrap-vSb6C0Bj","wrap--horizontal":"wrap--horizontal-vSb6C0Bj",bar:"bar-vSb6C0Bj",barInner:"barInner-vSb6C0Bj","barInner--horizontal":"barInner--horizontal-vSb6C0Bj","bar--horizontal":"bar--horizontal-vSb6C0Bj"}},792199:(e,t,n)=>{"use strict";n.r(t),n.d(t,{DataWindow:()=>q});var r=n(50959),o=n(650151),i=n(930202),l=n(965800),a=n(609838),s=n(209039),c=n(663557),u=n(964640),d=n(787729),v=n(272047),m=n(654910),h=n(529596),b=n(101903),f=n(497754),g=n.n(f),p=n(878112),w=n(724377),y=n(926048),S=n(540642),C=n(111982),N=n(865266),E=n(565631),D=n(94007),T=n(962766),P=n(600046);const x={[C.StdTheme.Light]:y.colorsPalette["color-white"],[C.StdTheme.Dark]:y.colorsPalette["color-cold-gray-900"]};function _(e){const{view:t,onClick:n,isActive:i,isVisible:l=!0,onVisibilityChange:a,onContextMenu:s,onHeaderDoubleClick:c,isHovered:u,icon:d,theme:v=C.StdTheme.Light,isHoverEnable:m=!1,onKeyDown:h,...b}=e,[f,w]=(0,N.useRovingTabindexElement)(null),y=(0,S.clean)(t.header(),!0),E=t.canShowItems()?t.items().filter((e=>e.visible())):[];return r.createElement("div",{className:g()(P.view,i&&P.active,!l&&P.hidden,m&&P.hoverEnable,m&&u&&P.hover),ref:f,onClick:n,onKeyDown:h,tabIndex:w,onContextMenu:function(e){if(!s)return;e.preventDefault(),s(e)},onDoubleClick:c,...b},y&&r.createElement("div",{className:P.header},d&&r.createElement(p.Icon,{className:P.headerIcon,icon:d}),r.createElement("span",{className:g()(P.headerTitle,"apply-common-tooltip"),title:(0,S.clean)(t.title(),!0)},y),a&&r.createElement("div",{role:"toolbar",className:P.actions},r.createElement(z,{isVisible:l,onClick:function(e){e.stopPropagation(),(0,o.ensureDefined)(a)()}}))),l&&E.length>0&&r.createElement("div",{className:P.values},E.map(((e,t)=>r.createElement(M,{key:t,item:e,theme:v})))))}function M(e){const{item:t,theme:n}=e,o=t.color(),i=x[n],l=(0,S.clean)(t.title(),!0);return r.createElement("div",{className:P.item},r.createElement("div",{className:g()(P.itemTitle,"apply-overflow-tooltip"),"data-test-id-value-title":l},l),r.createElement("div",null,r.createElement("span",{style:{color:o&&L(o,i)?o:void 0}},t.value())))}function z(e){const{onClick:t,isVisible:o}=e,[i,l]=(0,N.useRovingTabindexElement)(null);return r.createElement(E.AccessibleIconButton,{
icon:r.createElement(p.Icon,{icon:o?D:T,className:P.icon}),ref:i,tabIndex:l,tooltip:o?a.t(null,void 0,n(760724)):a.t(null,void 0,n(198337)),onClick:t,className:P.button})}function L(e,t){const n=(0,w.rgbToGrayscale)((0,w.parseRgb)(e)),r=(0,w.rgbToGrayscale)((0,w.parseRgb)(t));return Math.abs(n-r)>=25}var I=n(109955),k=n(278906),V=n(979910),W=n(128492),B=n(994567),H=n(442092),O=n(333086),A=n(416911),R=n(728824),Q=n(607295),U=n(14083),F=n(931532);const G=()=>new v.TranslatedString("hide {title}",a.t(null,void 0,n(713017))),Y=()=>new v.TranslatedString("show {title}",a.t(null,void 0,n(351382))),j=()=>new v.TranslatedString("change visibility",a.t(null,void 0,n(201924)));function q(e){const{chartWidget:t,updateDelegate:f,selectedSourcesWV:g}=e,[p,w]=(0,r.useState)([]),[y,S]=(0,r.useState)(null),C=(0,r.useRef)(null),N=(0,r.useRef)(null),E=(0,l.useTheme)();return(0,r.useEffect)((()=>{function e(){t.hasModel()||w([]);const e=t.model().model().dataSources();w(e.map((e=>e.dataWindowView())).filter($))}return e(),f.subscribe(null,e),()=>{f.unsubscribe(null,e)}}),[t]),(0,r.useEffect)((()=>{function e(e){const t=e[0];if(!t)return void S(null);const n=C.current?.querySelector(`[data-id="${t.id()}"]`),r=n?.getBoundingClientRect(),o=C.current?.getBoundingClientRect();n&&o&&r&&(o.top>r.top||o.bottom<r.bottom)&&n.scrollIntoView({block:"nearest"}),S(t)}return g.subscribe(e,{callWithLast:!0}),()=>{g.unsubscribe(e)}}),[g]),(0,r.useLayoutEffect)((()=>{const e=(0,o.ensureNotNull)(N.current),t=()=>{const t=(0,H.queryTabbableElements)(e).sort(H.navigationOrderComparator);if(0===t.length){const[t]=(0,B.queryMenuElements)(e).sort(H.navigationOrderComparator);if(void 0===t)return;(0,O.becomeMainElement)(t)}if(t.length>1){const[,...e]=t;for(const t of e)(0,O.becomeSecondaryElement)(t)}};return window.addEventListener("keyboard-navigation-activation",t),()=>window.removeEventListener("keyboard-navigation-activation",t)}),[]),r.createElement(I.OverlayScrollWrapper,{scrollContainerRef:C,className:"chart-data-window"},r.createElement("div",{ref:N,onKeyDown:B.handleAccessibleMenuKeyDown,className:F.container,role:"treegrid"},p.map(((e,n)=>{let l,a;return e instanceof c.SeriesDataWindowView?(l=e.series(),a=U):(e instanceof u.StudyDataWindowView||e instanceof d.OverlayDataWindowView)&&(l=e.study(),a=(0,k.isStudyStrategy)(l)?Q:R),r.createElement(r.Fragment,{key:n},0!==n&&r.createElement("div",{className:F.divider}),l?r.createElement(_,{"data-role":"menuitem",role:"row","data-id":l.id(),view:e,theme:E,icon:a,onClick:()=>D((0,o.ensureDefined)(l)),onKeyDown:e=>function(e,t){if(e.defaultPrevented||e.currentTarget!==e.target)return;const n=(0,i.hashFromEvent)(e);13!==n&&32!==n||D((0,o.ensureDefined)(t))}(e,l),onHeaderDoubleClick:()=>function(e){t.showChartPropertiesForSource(e)}((0,o.ensureDefined)(l)),isActive:l===y,isVisible:l.properties().childs().visible.value(),isHovered:l===t.model().hoveredSource(),onVisibilityChange:()=>function(e){const n=e.properties().childs().visible,r=!n.value();t.model().setProperty(n,r,(r?Y():G()).format({
title:new v.TranslatedString(e.name(),e.title(s.TitleDisplayTarget.DataWindow))}),(0,W.isLineTool)(e)&&V.lineToolsDoNotAffectChartInvalidation)}((0,o.ensureDefined)(l)),onContextMenu:e=>function(e,n){if(t.readOnly()||!n.hasContextMenu())return;D(n),n instanceof m.Series?function(e,n){const r=t.actions(),i=n.properties().visible.value(),l=i?void 0:T(n);h.ContextMenuManager.showMenu([r.format,i?r.seriesHide:(0,o.ensureDefined)(l)],e,void 0,{menuName:"DataWindowWidgetSeriesContextMenu",detail:{type:"series",id:n.instanceId()}},(()=>l?.destroy()))}(e,n):(0,k.isStudy)(n)&&function(e,n){const r=t.actions(),i=n.properties().visible.value(),l=i?void 0:T(n);h.ContextMenuManager.showMenu([r.format,i?r.studyHide:(0,o.ensureDefined)(l),r.studyRemove],e,void 0,{menuName:"DataWindowWidgetStudyContextMenu",detail:{type:"study",id:n.id()}},(()=>l?.destroy()))}(e,n)}(e,(0,o.ensureDefined)(l)),isHoverEnable:l.isHoveredEnabled()}):r.createElement(_,{"aria-disabled":!0,view:e,theme:E}))}))));function D(e){t.model().selectionMacro((t=>{t.addSourceToSelection(e)}))}function T(e){return new b.PropertyBoundAction({actionId:"Chart.SelectedObject.Show",options:{label:a.t(null,void 0,n(698334)),icon:A,checkable:!0}},{property:e.properties().visible,undoModel:t.model(),undoText:j()})}}function $(e){return null!==e&&e.items().length>0}},73288:(e,t,n)=>{"use strict";n.d(t,{OverlayScrollContainer:()=>g});var r=n(50959),o=n(497754),i=n.n(o),l=n(431520),a=n(650151),s=n(822960);const c=n(151810);var u;!function(e){e[e.Vertical=0]="Vertical",e[e.Horizontal=1]="Horizontal",e[e.HorizontalRtl=2]="HorizontalRtl"}(u||(u={}));const d={0:{isHorizontal:!1,isNegative:!1,sizePropName:"height",minSizePropName:"minHeight",startPointPropName:"top",currentMousePointPropName:"clientY",progressBarTransform:"translateY"},1:{isHorizontal:!0,isNegative:!1,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"left",currentMousePointPropName:"clientX",progressBarTransform:"translateX"},2:{isHorizontal:!0,isNegative:!0,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"right",currentMousePointPropName:"clientX",progressBarTransform:"translateX"}},v=40;function m(e){const{size:t,scrollSize:n,clientSize:o,scrollProgress:l,onScrollProgressChange:u,scrollMode:m,theme:h=c,onDragStart:b,onDragEnd:f,minBarSize:g=v}=e,p=(0,r.useRef)(null),w=(0,r.useRef)(null),[y,S]=(0,r.useState)(!1),C=(0,r.useRef)(0),{isHorizontal:N,isNegative:E,sizePropName:D,minSizePropName:T,startPointPropName:P,currentMousePointPropName:x,progressBarTransform:_}=d[m];(0,r.useEffect)((()=>{const e=(0,a.ensureNotNull)(p.current).ownerDocument;return y?(b&&b(),e&&(e.addEventListener("mousemove",H),e.addEventListener("mouseup",O))):f&&f(),()=>{e&&(e.removeEventListener("mousemove",H),e.removeEventListener("mouseup",O))}}),[y]);const M=t/n||0,z=o*M||0,L=Math.max(z,g),I=(t-L)/(t-z),k=n-t,V=E?-k:0,W=E?0:k,B=R((0,s.clamp)(l,V,W))||0;return r.createElement("div",{ref:p,className:i()(h.wrap,N&&h["wrap--horizontal"]),style:{[D]:t},onMouseDown:function(e){if(e.isDefaultPrevented())return
;e.preventDefault();const t=A(e.nativeEvent,(0,a.ensureNotNull)(p.current)),n=Math.sign(t),r=(0,a.ensureNotNull)(w.current).getBoundingClientRect();C.current=n*r[D]/2;let o=Math.abs(t)-Math.abs(C.current);const i=R(k);o<0?(o=0,C.current=t):o>i&&(o=i,C.current=t-n*i);u(Q(n*o)),S(!0)}},r.createElement("div",{ref:w,className:i()(h.bar,N&&h["bar--horizontal"]),style:{[T]:g,[D]:L,transform:`${_}(${B}px)`},onMouseDown:function(e){e.preventDefault(),C.current=A(e.nativeEvent,(0,a.ensureNotNull)(w.current)),S(!0)}},r.createElement("div",{className:i()(h.barInner,N&&h["barInner--horizontal"])})));function H(e){const t=A(e,(0,a.ensureNotNull)(p.current))-C.current;u(Q(t))}function O(){S(!1)}function A(e,t){const n=t.getBoundingClientRect()[P];return e[x]-n}function R(e){return e*M*I}function Q(e){return e/M/I}}var h=n(522224),b=n(518561);const f=8;function g(e){const{reference:t,className:n,containerHeight:i=0,containerWidth:a=0,contentHeight:s=0,contentWidth:c=0,scrollPosTop:u=0,scrollPosLeft:d=0,onVerticalChange:v,onHorizontalChange:g,visible:p}=e,[w,y]=(0,h.useHover)(),[S,C]=(0,r.useState)(!1),N=i<s,E=a<c,D=N&&E?f:0;return r.createElement("div",{...y,ref:t,className:o(n,b.scrollWrap),style:{visibility:p||w||S?"visible":"hidden"}},N&&r.createElement(m,{size:i-D,scrollSize:s-D,clientSize:i-D,scrollProgress:u,onScrollProgressChange:function(e){v&&v(e)},onDragStart:T,onDragEnd:P,scrollMode:0}),E&&r.createElement(m,{size:a-D,scrollSize:c-D,clientSize:a-D,scrollProgress:d,onScrollProgressChange:function(e){g&&g(e)},onDragStart:T,onDragEnd:P,scrollMode:(0,l.isRtl)()?2:1}));function T(){C(!0)}function P(){C(!1)}}},109955:(e,t,n)=>{"use strict";n.d(t,{OverlayScrollWrapper:()=>c});var r=n(50959),o=n(497754),i=n.n(o),l=n(73288),a=n(139043),s=n(131987);function c(e){const{children:t,className:n,wrapperClassName:o,reference:c,hasCustomTouchScrollAnimation:u,scrollContainerRef:d,isForceVisible:v,tabIndex:m,...h}=e,[b,f,g,p]=(0,a.useOverlayScroll)(d,u);return(0,r.useImperativeHandle)(c,(()=>({updateScrollState:p}))),r.createElement("div",{...h,...f,className:i()(s.container,n)},r.createElement(l.OverlayScrollContainer,{...b,visible:v??b.visible,className:s.overlayScrollWrap}),r.createElement("div",{className:i()(s.wrapper,o),tabIndex:m,ref:g,onScroll:p},t))}},139043:(e,t,n)=>{"use strict";n.d(t,{useOverlayScroll:()=>s});var r=n(50959),o=n(650151),i=n(522224),l=n(601227);const a={onMouseOver:()=>{},onMouseOut:()=>{}};function s(e,t=l.CheckMobile.any()){const n=(0,r.useRef)(null),s=e||(0,r.useRef)(null),[c,u]=(0,i.useHover)(),[d,v]=(0,r.useState)({reference:n,containerHeight:0,containerWidth:0,contentHeight:0,contentWidth:0,scrollPosTop:0,scrollPosLeft:0,onVerticalChange:function(e){v((t=>({...t,scrollPosTop:e}))),(0,o.ensureNotNull)(s.current).scrollTop=e},onHorizontalChange:function(e){v((t=>({...t,scrollPosLeft:e}))),(0,o.ensureNotNull)(s.current).scrollLeft=e},visible:c}),m=(0,r.useCallback)((()=>{if(!s.current)return
;const{clientHeight:e,scrollHeight:t,scrollTop:r,clientWidth:o,scrollWidth:i,scrollLeft:l}=s.current,a=n.current?n.current.offsetTop:0;v((n=>({...n,containerHeight:e-a,contentHeight:t-a,scrollPosTop:r,containerWidth:o,contentWidth:i,scrollPosLeft:l})))}),[]);function h(){v((e=>({...e,scrollPosTop:(0,o.ensureNotNull)(s.current).scrollTop,scrollPosLeft:(0,o.ensureNotNull)(s.current).scrollLeft})))}return(0,r.useEffect)((()=>{c&&m(),v((e=>({...e,visible:c})))}),[c]),(0,r.useEffect)((()=>{const e=s.current;return e&&e.addEventListener("scroll",h),()=>{e&&e.removeEventListener("scroll",h)}}),[s]),[d,t?a:u,s,m]}},965800:(e,t,n)=>{"use strict";n.d(t,{useTheme:()=>i});var r=n(297265),o=n(702054);function i(){return(0,r.useWatchedValueReadonly)({watchedValue:o.watchedTheme})}},297265:(e,t,n)=>{"use strict";n.d(t,{useWatchedValueReadonly:()=>o});var r=n(50959);const o=(e,t=!1,n=[])=>{const o="watchedValue"in e?e.watchedValue:void 0,i="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[l,a]=(0,r.useState)(o?o.value():i);return(t?r.useLayoutEffect:r.useEffect)((()=>{if(o){a(o.value());const e=e=>a(e);return o.subscribe(e),()=>o.unsubscribe(e)}return()=>{}}),[o,...n]),l}},438576:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},697373:e=>{e.exports={button:"button-xNqEcuN2"}},994567:(e,t,n)=>{"use strict";n.d(t,{focusFirstMenuItem:()=>u,handleAccessibleMenuFocus:()=>s,handleAccessibleMenuKeyDown:()=>c,queryMenuElements:()=>m});var r=n(442092),o=n(333086),i=n(180185),l=n(32556);const a=[37,39,38,40];function s(e,t){if(!e.target)return;const n=e.relatedTarget?.getAttribute("aria-activedescendant");if(e.relatedTarget!==t.current){const e=n&&document.getElementById(n);if(!e||e!==t.current)return}u(e.target)}function c(e){if(e.defaultPrevented)return;const t=(0,i.hashFromEvent)(e);if(!a.includes(t))return;const n=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const l=m(e.currentTarget).sort(r.navigationOrderComparator);if(0===l.length)return;const s=document.activeElement.closest('[data-role="menuitem"]')||document.activeElement.parentElement?.querySelector('[data-role="menuitem"]');if(!(s instanceof HTMLElement))return;const c=l.indexOf(s);if(-1===c)return;const u=h(s),b=u.indexOf(document.activeElement),f=-1!==b,g=e=>{n&&(0,o.becomeSecondaryElement)(n),(0,o.becomeMainElement)(e),e.focus()};switch((0,r.mapKeyCodeToDirection)(t)){case"inlinePrev":if(!u.length)return;e.preventDefault(),g(0===b?l[c]:f?d(u,b,-1):u[u.length-1]);break;case"inlineNext":if(!u.length)return;e.preventDefault(),b===u.length-1?g(l[c]):g(f?d(u,b,1):u[0]);break;case"blockPrev":{e.preventDefault();const t=d(l,c,-1);if(f){const e=v(t,b);g(e||t);break}g(t);break}case"blockNext":{e.preventDefault();const t=d(l,c,1);if(f){
const e=v(t,b);g(e||t);break}g(t)}}}function u(e){const[t]=m(e);t&&((0,o.becomeMainElement)(t),t.focus())}function d(e,t,n){return e[(t+e.length+n)%e.length]}function v(e,t){const n=h(e);return n.length?n[(t+n.length)%n.length]:null}function m(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled]):not([aria-disabled])')).filter((0,l.createScopedVisibleElementFilter)(e))}function h(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled]):not([aria-disabled])")).filter((0,l.createScopedVisibleElementFilter)(e))}},565631:(e,t,n)=>{"use strict";n.d(t,{AccessibleIconButton:()=>i});var r=n(50959),o=n(511349);const i=(0,r.forwardRef)((function(e,t){const{tooltip:n,...i}=e;return r.createElement(o.ToolWidgetIconButton,{"aria-label":n,...i,tag:"button",ref:t,"data-tooltip":n,"data-tooltip-show-on-focus":"true"})}))},155352:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>a,ToolWidgetButton:()=>s});var r=n(50959),o=n(497754),i=n(878112),l=n(438576);const a=l,s=r.forwardRef(((e,t)=>{const{tag:n="div",icon:a,endIcon:s,isActive:c,isOpened:u,isDisabled:d,isGrouped:v,isHovered:m,isClicked:h,onClick:b,text:f,textBeforeIcon:g,title:p,theme:w=l,className:y,forceInteractive:S,inactive:C,"data-name":N,"data-tooltip":E,...D}=e,T=o(y,w.button,(p||E)&&"apply-common-tooltip",{[w.isActive]:c,[w.isOpened]:u,[w.isInteractive]:(S||Boolean(b))&&!d&&!C,[w.isDisabled]:Boolean(d||C),[w.isGrouped]:v,[w.hover]:m,[w.clicked]:h}),P=a&&("string"==typeof a?r.createElement(i.Icon,{className:w.icon,icon:a}):r.cloneElement(a,{className:o(w.icon,a.props.className)}));return"button"===n?r.createElement("button",{...D,ref:t,type:"button",className:o(T,w.accessible),disabled:d&&!C,onClick:b,title:p,"data-name":N,"data-tooltip":E},g&&f&&r.createElement("div",{className:o("js-button-text",w.text)},f),P,!g&&f&&r.createElement("div",{className:o("js-button-text",w.text)},f)):r.createElement("div",{...D,ref:t,"data-role":"button",className:T,onClick:d?void 0:b,title:p,"data-name":N,"data-tooltip":E},g&&f&&r.createElement("div",{className:o("js-button-text",w.text)},f),P,!g&&f&&r.createElement("div",{className:o("js-button-text",w.text)},f),s&&r.createElement(i.Icon,{icon:s,className:l.endIcon}))}))},511349:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetIconButton:()=>a});var r=n(50959),o=n(497754),i=n(155352),l=n(697373);const a=r.forwardRef((function(e,t){const{className:n,id:a,...s}=e;return r.createElement(i.ToolWidgetButton,{id:a,"data-name":a,...s,ref:t,className:o(n,l.button)})}))},787729:(e,t,n)=>{"use strict";n.d(t,{OverlayDataWindowView:()=>d});var r=n(601227),o=n(624202),i=n(651407),l=n(440891),a=n(209039),s=n(797833),c=n(964640);const u=r.CheckMobile.any();class d extends c.StudyDataWindowView{_updateImpl(){this._header=this._study.title(a.TitleDisplayTarget.DataWindow);let e;this._showLastPriceAndChangeOnly()?e=this._study.data().lastIndex():(e=this._model.crosshairSource().appliedIndex(),(null===e||isNaN(e))&&(e=this._study.data().lastIndex(),
l.enabled("use_last_visible_bar_value_in_legend")&&(e=this._model.timeScale().visibleBarsStrictRange()?.lastBar()??NaN)));const t=this._valueProvider.getValues(e);for(let e=0;e<t.length;++e){const n=t[e],r=this._items[e];r.setValue(n.value),r.setVisible(n.visible),r.setColor(n.color)}}_showLastPriceAndChangeOnly(){return u&&(null===this._model.crosshairSource().pane||(0,o.isLineToolName)(i.tool.value())||null!==this._model.lineBeingEdited())}_createValuesProvider(e,t){return new s.OverlayValuesProvider(e,t)}}},797833:(e,t,n)=>{"use strict";n.d(t,{OverlayValuesProvider:()=>N});var r=n(150335),o=n(609838),i=n(601227),l=n(589637),a=n(431520),s=n(251940),c=n(103936),u=n(742769),d=n(419617),v=n(651407),m=n(52205),h=n(551119),b=n(624202),f=n(696292);const g=d.lastDayChangeAvailable||d.alwaysShowLastPriceAndLastDayChange,p=i.CheckMobile.any(),w=(0,s.getPercentageFormatter)();var y;!function(e){e[e.Open=0]="Open",e[e.High=1]="High",e[e.Low=2]="Low",e[e.Close=3]="Close",e[e.Source=4]="Source",e[e.LastPrice=5]="LastPrice",e[e.Change=6]="Change",e[e.LastDayChange=7]="LastDayChange"}(y||(y={}));const S=f.notAvailable,C=`${S} (${S}%)`;class N{constructor(e,t){this._study=e,this._model=t,this._emptyValues=[{title:o.t(null,void 0,n(16610)),visible:!1,value:"",index:0,id:""},{title:o.t(null,void 0,n(778254)),visible:!1,value:"",index:1,id:""},{title:o.t(null,void 0,n(165318)),visible:!1,value:"",index:2,id:""},{title:o.t(null,void 0,n(862578)),visible:!1,value:"",index:3,id:""},{title:"",visible:!1,value:"",index:4,id:""},{title:"",visible:!1,value:"",index:5,id:""},{title:o.t(null,void 0,n(537276)),visible:!1,value:"",index:6,id:""},{title:o.t(null,void 0,n(463815)),visible:!1,value:"",index:7,id:""}]}getItems(){return this._emptyValues}getValues(e){const t=this._emptyValues.map((e=>({...e})));if(this._model.timeScale().isEmpty())return t;const n=this._study.data(),o=n.lastIndex();if(0===n.size()||null===o)return t;const i=n.search(o,u.PlotRowSearchMode.NearestLeft,1);if(null===i)return t;const s=this._showLastPriceAndChangeOnly();if((0,r.isNumber)(e)||(s?e=o:(e=this._model.crosshairSource().appliedIndex(),(0,r.isNumber)(e)||(e=o))),null===e||!(0,r.isNumber)(e))return t;const d=n.search(e,u.PlotRowSearchMode.NearestLeft,1),v=this._model.backgroundTopColor().value();if(null===d)return t;const m=d.index,b=d.value,f=b[1],p=b[2],y=b[3],N=b[4];t[0].value=S,t[1].value=S,t[2].value=S,t[3].value=S,t[7].value=C,t[6].value=C;for(const e of t)e.visible=!s;const E=t[4];E.visible=!1;const{barChange:D,lastDayChange:T}=(0,c.changesData)(n,this._study.quotes(),d.value,d.index,i.value),P=(0,h.getPriceValueFormatterForSource)(this._study);if((0,h.shouldBeFormattedAsPercent)(this._study)||(0,h.shouldBeFormattedAsIndexedTo100)(this._study))t[7].value="",t[6].value="";else{const e=this._study.formatter(),n={signPositive:!0};if(void 0!==D){const{currentPrice:r,prevPrice:o,change:i}=D,l=e.formatChange?.(r,o,n)??e.format(i,n);t[6].value=(0,a.forceLTRStr)(`${l} (${w.format(D.percentChange,n)})`)}if(void 0!==T){
const{currentPrice:r,prevPrice:o,change:i,percentChange:l}=T,s=e.formatChange?.(r,o,n)??e.format(i,n);t[7].value=(0,a.forceLTRStr)(`${s} (${w.format(l,n)})`)}}let x=null;if(s)t[5].value=null==N?S:P(N),t[5].visible=!0,x=this._getChangeColor(T?.change),t[6].visible=void 0!==D,t[7].visible=void 0!==T||g;else{t[0].value=null==f?S:P(f),t[1].value=null==p?S:P(p),t[2].value=null==y?S:P(y),t[3].value=null==N?S:P(N),E.value=P(this._study.barFunction()(b)),t[5].visible=!1;const e=this._model.mainSeries().intervalObj().is1Tick(),n=21!==this._study.properties().childs().style.value();t[0].visible=!e&&n,t[1].visible=!e,t[2].visible=!e,t[7].visible=void 0!==T||g,t[6].visible=void 0!==D;const r=this._study.barColorer().barStyle(m,!1),o=r.barBorderColor??r.barColor;x=(0,c.calculateColor)(v,o)}x=(0,l.resetTransparency)((0,c.calculateColor)(v,x));for(const e of t)e.color||(e.color=x);return t[7].visible&&(t[7].color=(0,l.resetTransparency)((0,c.calculateColor)(v,this._getChangeColor(T?.change)))),t}_mobileNonTrackingMode(){return p&&(null===this._model.crosshairSource().pane||(0,b.isLineToolName)(v.tool.value())||null!==this._model.lineBeingEdited())}_showLastPriceAndChangeOnly(){return d.alwaysShowLastPriceAndLastDayChange||this._mobileNonTrackingMode()}_getChangeColor(e){const t=void 0===e||e>=0?m.SeriesBarColorer.upColor(this._study.properties()):m.SeriesBarColorer.downColor(this._study.properties());return t.barBorderColor??t.barColor}}},14083:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 11v6h3v-6h-3zm-.5-1h4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5z"/><path d="M18 7h1v3.5h-1zm0 10.5h1V21h-1z"/><path d="M9 8v12h3V8H9zm-.5-1h4a.5.5 0 0 1 .5.5v13a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-13a.5.5 0 0 1 .5-.5z"/><path d="M10 4h1v3.5h-1zm0 16.5h1V24h-1z"/></svg>'},94007:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M2.448 10.124a10.82 10.82 0 0 1-.336-.609L2.105 9.5l.007-.015a12.159 12.159 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373c2.297 0 4.047 1.292 5.25 2.646a12.166 12.166 0 0 1 1.687 2.466l.007.015-.007.015a12.163 12.163 0 0 1-1.686 2.466c-1.204 1.354-2.954 2.646-5.251 2.646-2.298 0-4.048-1.292-5.252-2.646a12.16 12.16 0 0 1-1.35-1.857zm14.558-.827l-.456.203.456.203v.002l-.003.005-.006.015-.025.052a11.813 11.813 0 0 1-.461.857 13.163 13.163 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982-2.703 0-4.703-1.522-6-2.982a13.162 13.162 0 0 1-1.83-2.677 7.883 7.883 0 0 1-.118-.243l-.007-.015-.002-.005v-.001l.456-.204-.456-.203v-.002l.002-.005.007-.015a4.66 4.66 0 0 1 .119-.243 13.158 13.158 0 0 1 1.83-2.677c1.296-1.46 3.296-2.982 5.999-2.982 2.702 0 4.702 1.522 5.998 2.981a13.158 13.158 0 0 1 1.83 2.678 8.097 8.097 0 0 1 .119.243l.006.015.003.005v.001zm-.456.203l.456-.203.09.203-.09.203-.456-.203zM1.092 9.297l.457.203-.457.203-.09-.203.09-.203zm9.958.203c0 1.164-.917 2.07-2 2.07-1.084 0-2-.906-2-2.07 0-1.164.916-2.07 2-2.07 1.083 0 2 .906 2 2.07zm1 0c0 1.695-1.344 3.07-3 3.07-1.657 0-3-1.375-3-3.07 0-1.695 1.343-3.07 3-3.07 1.656 0 3 1.375 3 3.07z"/></svg>'},607295:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M4.5 12.5l4.59-4.59a2 2 0 0 1 2.83 0l3.17 3.17a2 2 0 0 0 2.83 0L22.5 6.5m-8 9.5v5.5M12 19l2.5 2.5L17 19m4.5 3v-5.5M19 19l2.5-2.5L24 19"/></svg>'},728824:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>'},962766:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14.692 3.012l-12 12.277.715.699 12-12.277-.715-.699zM9.05 15.627a7.042 7.042 0 0 1-3.144-.741l.742-.76c.72.311 1.52.5 2.402.5 2.297 0 4.047-1.29 5.25-2.645a12.168 12.168 0 0 0 1.687-2.466l.007-.015-.007-.015A12.166 12.166 0 0 0 14.3 7.019c-.11-.124-.225-.247-.344-.37l.699-.715c.137.14.268.28.392.42a13.16 13.16 0 0 1 1.83 2.678 8.117 8.117 0 0 1 .119.243l.006.015.003.005v.001l-.456.204.456.203v.002l-.003.005-.006.015-.025.052a11.762 11.762 0 0 1-.461.857 13.158 13.158 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982zm7.5-6.127l.456-.203.09.203-.09.203-.456-.203zm-7.5 3.07c-.27 0-.53-.037-.778-.105l.879-.899c.999-.052 1.833-.872 1.895-1.938l.902-.923c.**************.102.795 0 1.695-1.344 3.07-3 3.07zM6.15 10.294l.902-.923c.063-1.066.896-1.886 1.895-1.938l.879-.9a2.94 2.94 0 0 0-.777-.103c-1.657 0-3 1.374-3 3.069 0 .275.035.541.101.795zM9.05 4.373c.88 0 1.68.19 2.4.5l.743-.759a7.043 7.043 0 0 0-3.143-.74c-2.703 0-4.703 1.521-6 2.98a13.159 13.159 0 0 0-1.83 2.678 7.886 7.886 0 0 0-.118.243l-.007.015-.002.005v.001l.456.204-.457-.203-.09.203.09.203.457-.203-.456.203v.002l.002.005.007.015a4.5 4.5 0 0 0 .119.243 13.152 13.152 0 0 0 1.83 2.677c.124.14.255.28.392.42l.7-.715c-.12-.122-.235-.245-.345-.369a12.156 12.156 0 0 1-1.686-2.466L2.105 9.5l.007-.015a12.158 12.158 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373z"/></svg>'}}]);