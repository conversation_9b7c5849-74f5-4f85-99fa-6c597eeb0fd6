.textArea-pBDekXDd{-webkit-appearance:auto;appearance:textfield;background:#0000;background-color:initial;border:0;box-sizing:border-box;display:block;height:100%;margin:0;min-width:0;opacity:0;outline:0;overflow:hidden;padding:2px;position:absolute;width:100%;-webkit-text-fill-color:var(--ui-lib-control-text-fill-color,currentColor);font-family:inherit;font-size:inherit;line-height:inherit;order:0;pointer-events:all}.textArea-pBDekXDd::placeholder{color:var(--themed-color-text-field-placeholder-default,#707070);-webkit-text-fill-color:currentColor;opacity:1}html.theme-dark .textArea-pBDekXDd::placeholder{color:var(--themed-color-text-field-placeholder-default,#8c8c8c)}.textArea-pBDekXDd::-webkit-calendar-picker-indicator,.textArea-pBDekXDd::-webkit-clear-button,.textArea-pBDekXDd::-webkit-inner-spin-button,.textArea-pBDekXDd::-webkit-outer-spin-button,.textArea-pBDekXDd::-webkit-search-cancel-button{-webkit-appearance:none;appearance:none}.textArea-pBDekXDd:-webkit-autofill,.textArea-pBDekXDd:-webkit-autofill:active,.textArea-pBDekXDd:-webkit-autofill:focus{border-radius:6px}@media (any-hover:hover){.textArea-pBDekXDd:-webkit-autofill:hover{border-radius:6px}}html.theme-dark .textArea-pBDekXDd::-webkit-calendar-picker-indicator{filter:invert(1)}.chart-debug .textArea-pBDekXDd{opacity:.7}