(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7259],{183428:e=>{e.exports={defaultsButtonText:"defaultsButtonText-aJgjxj2V",defaultsButtonItem:"defaultsButtonItem-aJgjxj2V",defaultsButtonIcon:"defaultsButtonIcon-aJgjxj2V"}},450670:e=>{e.exports={themesButtonText:"themesButtonText-AeBgp7zz",themesButtonIcon:"themesButtonIcon-AeBgp7zz",defaultsButtonText:"defaultsButtonText-AeBgp7zz",defaultsButtonItem:"defaultsButtonItem-AeBgp7zz"}},333636:e=>{e.exports={"link-item":"link-item-eIA09f0e"}},632498:e=>{e.exports={"arrow-icon":"arrow-icon-NIrWNOPk",dropped:"dropped-NIrWNOPk","size-xsmall":"size-xsmall-NIrWNOPk","size-small":"size-small-NIrWNOPk","size-medium":"size-medium-NIrWNOPk","size-large":"size-large-NIrWNOPk","size-xlarge":"size-xlarge-NIrWNOPk"}},956406:e=>{e.exports={"underline-tab":"underline-tab-cfYYXvwA","disable-focus-outline":"disable-focus-outline-cfYYXvwA","enable-cursor-pointer":"enable-cursor-pointer-cfYYXvwA",selected:"selected-cfYYXvwA","disable-active-state-styles":"disable-active-state-styles-cfYYXvwA","size-xsmall":"size-xsmall-cfYYXvwA","size-small":"size-small-cfYYXvwA","size-medium":"size-medium-cfYYXvwA","size-large":"size-large-cfYYXvwA","size-xlarge":"size-xlarge-cfYYXvwA",fake:"fake-cfYYXvwA","margin-xsmall":"margin-xsmall-cfYYXvwA","margin-small":"margin-small-cfYYXvwA","margin-medium":"margin-medium-cfYYXvwA","margin-large":"margin-large-cfYYXvwA","margin-xlarge":"margin-xlarge-cfYYXvwA",collapse:"collapse-cfYYXvwA","ellipsis-children":"ellipsis-children-cfYYXvwA"}},898163:e=>{e.exports={"scroll-wrap":"scroll-wrap-SmxgjhBJ","size-xlarge":"size-xlarge-SmxgjhBJ","enable-scroll":"enable-scroll-SmxgjhBJ","underline-tabs":"underline-tabs-SmxgjhBJ","size-large":"size-large-SmxgjhBJ","size-medium":"size-medium-SmxgjhBJ","size-small":"size-small-SmxgjhBJ","size-xsmall":"size-xsmall-SmxgjhBJ","make-grid-column":"make-grid-column-SmxgjhBJ","stretch-tabs":"stretch-tabs-SmxgjhBJ","equal-tab-size":"equal-tab-size-SmxgjhBJ"}},150368:e=>{e.exports={underline:"underline-Pun8HxCz",center:"center-Pun8HxCz",corner:"corner-Pun8HxCz",disabled:"disabled-Pun8HxCz"}},839246:(e,t,a)=>{"use strict";a.d(t,{DialogTabs:()=>o});var l=a(50959),n=a(488831);const o=l.forwardRef((function(e,t){const{id:a,tabs:o,activeTab:r,onChange:s,className:i}=e;return l.createElement("div",{className:i,ref:t},l.createElement(n.UnderlineButtonTabs,{id:a,items:o,isActive:function(e){return e.id===r},onActivate:function(e){s(e.id)},overflowBehaviour:"scroll"}))}))},677090:(e,t,a)=>{"use strict";a.d(t,{PropertyActions:()=>f});var l=a(50959),n=a(497754),o=a.n(n),r=a(878112),s=a(609838),i=a(299120),c=a(192063),u=a(844996),m=a(183428);const d={reset:s.t(null,void 0,a(133533)),saveAsDefault:s.t(null,void 0,a(299687)),defaults:s.t(null,void 0,a(448572))};var p;!function(e){e.Normal="normal",e.Compact="compact"}(p||(p={}));class f extends l.PureComponent{render(){const{mode:e,saveAsDefaults:t,resetToDefaults:a}=this.props;return l.createElement(i.ControlDisclosure,{id:"property-actions",
className:o()("normal"===e&&m.defaultsButtonText),hideArrowButton:"compact"===e,buttonChildren:this._getPlaceHolderItem("compact"===e)},l.createElement(c.PopupMenuItem,{className:m.defaultsButtonItem,isActive:!1,label:d.reset,onClick:a}),l.createElement(c.PopupMenuItem,{className:m.defaultsButtonItem,isActive:!1,label:d.saveAsDefault,onClick:t}))}_getPlaceHolderItem(e){return e?l.createElement(r.Icon,{className:m.defaultsButtonIcon,icon:u}):d.defaults}}},944316:(e,t,a)=>{"use strict";a.d(t,{FooterMenu:()=>I});var l=a(50959),n=a(743766),o=a(601227),r=a(917568),s=a(609838),i=a(272047),c=a(878112),u=a(299120),m=a(930052),d=a(450670),p=a(844996);function f(e){return e.isTabletWidth?l.createElement(c.Icon,{className:d.themesButtonIcon,icon:p}):l.createElement(l.Fragment,null,s.t(null,void 0,a(93553)))}function v(e){return l.createElement(m.MatchMedia,{rule:"(max-width: 768px)"},(t=>l.createElement(u.ControlDisclosure,{className:!t&&d.themesButtonText,hideArrowButton:t,buttonChildren:l.createElement(f,{isTabletWidth:t})},e.children)))}var g=a(192063),b=a(72621),T=a(522224),E=a(972535),h=a(460925);function A(e){const{name:t,onRemove:a,onClick:n}=e,[o,r]=(0,T.useHover)(),s=l.useCallback((()=>n(t)),[n,t]),i=l.useCallback((()=>{a&&a(t)}),[a,t]);return l.createElement("div",{...r},l.createElement(g.PopupMenuItem,{className:d.defaultsButtonItem,isActive:!1,label:t,onClick:s,toolbox:a&&l.createElement(b.RemoveButton,{hidden:!E.mobiletouch&&!o,onClick:i,icon:h})}))}var C=a(917850),x=a(153055),w=a(753327),y=a(669874),k=a(78943);var S=a(279708);const N=(0,n.connect)((function(e,t){return{templates:e.templates[t.toolName]}}),(function(e,t){const{toolName:a,applyTemplate:l,getSourceTemplate:n}=t;return{getTemplates:()=>e((0,S.getTemplates)(a)),loadTemplate:l?t=>e((0,S.loadTemplate)(a,t,l)):void 0,removeTemplate:t=>e((0,S.startRemoveTemplate)(a,t)),saveTemplate:n?t=>e((0,S.saveTemplate)(a,t,JSON.stringify(n()))):void 0}}))((function(e){const{templates:t,getTemplates:n,applyDefaults:o,saveTemplate:r,applyTemplate:i}=e,c=(0,l.useContext)(w.SlotContext);return(0,l.useEffect)((()=>{i&&!t&&n()}),[t,n,i]),l.createElement(v,null,r&&l.createElement(A,{onClick:function(){window.runOrSignIn((()=>{(0,x.showRename)({title:s.t(null,void 0,a(295190)),text:s.t(null,void 0,a(59233))+":",maxLength:64,source:t||[],autocompleteFilter:k.autocompleteFilter,onRename:({newValue:l,focusInput:n,dialogClose:o,innerManager:r})=>{if(t&&-1!==t.indexOf(l)){const t=s.t(null,void 0,a(7762)).format({name:l});(0,x.showConfirm)({text:t,onConfirm:({dialogClose:t})=>{e.saveTemplate?.(l),t(),o()},onClose:n},r)}else e.saveTemplate?.(l),o()}},c)}),{source:"Save line tool template",sourceMeta:"Chart"})},name:(0,y.appendEllipsis)(s.t(null,void 0,a(276266)))}),l.createElement(A,{onClick:o,name:s.t(null,void 0,a(896413))}),i&&t&&t.length>0&&l.createElement(l.Fragment,null,l.createElement(C.PopupMenuSeparator,null),t.map((e=>l.createElement(A,{key:e,name:e,onRemove:u,onClick:m})))));function u(t){window.runOrSignIn((()=>{const l=s.t(null,void 0,a(825255)).format({name:t});(0,
x.showConfirm)({text:l,onConfirm:({dialogClose:a})=>{e.removeTemplate(t),a()}},c)}),{source:"Delete line tool template"})}function m(t){e.loadTemplate?.(t)}}));var B=a(440891);const M=new i.TranslatedString("apply drawing template",s.t(null,void 0,a(89720))),z=new i.TranslatedString("apply drawing template",s.t(null,void 0,a(89720)));function P(e){const{sources:t,chartUndoModel:a}=e,n=(0,l.useMemo)((()=>t.every((e=>e.toolname===t[0].toolname))),[t]);return B.enabled("drawing_templates")?l.createElement(N,{toolName:t[0].toolname,applyDefaults:o,applyTemplate:n?function(e){1===t.length?a.applyLineToolTemplate(t[0],e,M):(a.beginUndoMacro(z),t.forEach((t=>{a.applyLineToolTemplate(t,e,null)})),a.endUndoMacro())}:void 0,getSourceTemplate:1===t.length?function(){return t[0].template()}:void 0}):l.createElement(N,{toolName:t[0].toolname,applyDefaults:o});function o(){a.restoreLineToolsFactoryDefaults(t)}}function L(e){return l.createElement(v,null,l.createElement(A,{onClick:function(){const{sources:t,chartUndoModel:a}=e;a.restoreLineToolsFactoryDefaults(t)},name:s.t(null,void 0,a(462511))}))}function I(e){return(0,o.onWidget)()?l.createElement(L,{...e}):l.createElement(n.Provider,{store:r.store},l.createElement(P,{...e}))}},380865:(e,t,a)=>{"use strict";a.d(t,{PropertiesEditorTab:()=>c});var l=a(50959),n=a(633064);const o={"Elliott Impulse Wave (12345)Degree":"normal","Elliott Triangle Wave (ABCDE)Degree":"normal","Elliott Triple Combo Wave (WXYXZ)Degree":"normal","Elliott Correction Wave (ABC)Degree":"normal","Elliott Double Combo Wave (WXY)Degree":"normal",BarsPatternMode:"normal",StudyInputSource:"normal"},r={TextText:"big",AnchoredTextText:"big",NoteText:"big",AnchoredNoteText:"big",CalloutText:"big",BalloonText:"big"};var s=a(47924),i=a(767055);function c(e){const{page:t,pageRef:a,tableKey:c}=e;return l.createElement(n.ControlCustomHeightContext.Provider,{value:r},l.createElement(n.ControlCustomWidthContext.Provider,{value:o},t&&l.createElement(s.PropertyTable,{reference:a,key:c},t.definitions.value().map((e=>l.createElement(i.Section,{key:e.id,definition:e}))))))}},737402:(e,t,a)=>{"use strict";a.d(t,{useSafeMatchMedia:()=>l.useSafeMatchMedia});var l=a(671129)},488831:(e,t,a)=>{"use strict";a.d(t,{UnderlineButtonTabs:()=>U});var l,n=a(50959),o=a(497754),r=a.n(o),s=a(609838),i=a(429510),c=a(525388),u=a(269842),m=a(772069),d=a(984164),p=a(953517);!function(e){e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge"}(l||(l={}));const f=(0,n.createContext)({size:"small",overflowBehaviour:void 0});var v=a(234539),g=a(956406);function b(e){const{size:t="xsmall",active:a,fake:l,enableActiveStateStyles:n,anchor:r=!1,hideFocusOutline:s=!1,equalTabSize:i,className:c,overflowBehaviour:u}=e;return o(g["underline-tab"],g[`size-${t}`],a&&g.selected,!n&&g["disable-active-state-styles"],s&&g["disable-focus-outline"],l&&g.fake,r&&g["enable-cursor-pointer"],i&&g[`margin-${t}`],"collapse"===u&&g.collapse,c)}const T=(0,n.forwardRef)(((e,t)=>{const{size:a,overflowBehaviour:l}=(0,n.useContext)(f),o=(0,
n.useContext)(v.CustomBehaviourContext),{active:s,fake:i,className:c,enableActiveStateStyles:u=o.enableActiveStateStyles,hideFocusOutline:m=!1,equalTabSize:d,children:p,...T}=e;return n.createElement("button",{...T,ref:t,className:b({size:a,active:s,fake:i,enableActiveStateStyles:u,hideFocusOutline:m,equalTabSize:d,className:c,overflowBehaviour:l})},d&&"string"==typeof p?n.createElement("span",{className:r()(g["ellipsis-children"],"apply-overflow-tooltip")},p):p)}));T.displayName="UnderlineTabsBaseButton";const E=(0,n.forwardRef)(((e,t)=>{const{item:a,highlighted:l,handleItemRef:o,onClick:r,"aria-disabled":s,...i}=e,c=(0,n.useCallback)((()=>{r&&r(a)}),[r,a]),u=(0,n.useCallback)((e=>{o&&o(a,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[a,o,t]);return n.createElement(T,{...i,id:a.id,onClick:c,ref:u},a.label)}));E.displayName="UnderlineButtonTab";var h=a(650151),A=a(192063),C=a(904925),x=a(878112),w=a(347531),y=a(602948),k=a(863509),S=a(168874),N=a(632498);function B(e){switch(e){case"xsmall":return w;case"small":return y;case"medium":case"large":return k;case"xlarge":return S}}function M(e){const{size:t,isDropped:a=!1}=e;return n.createElement(x.Icon,{icon:B(t),className:o(N["arrow-icon"],N[`size-${t}`],a&&N.dropped)})}var z=a(333636);const P=4,L=4;function I(e){const{size:t,disabled:a,isOpened:l,enableActiveStateStyles:o,hideFocusOutline:r,fake:s,items:i,buttonContent:u,buttonRef:m,isAnchorTabs:d,isHighlighted:p,onButtonClick:f,onItemClick:v,onClose:g}=e,b=(0,n.useRef)(null),E=(0,c.useMergedRefs)([m,b]),x=function(e,t){const a=(0,n.useRef)(R);return(0,n.useEffect)((()=>{const e=getComputedStyle((0,h.ensureNotNull)(t.current));a.current={xsmall:D(e,"xsmall"),small:D(e,"small"),medium:D(e,"medium"),large:D(e,"large"),xlarge:D(e,"xlarge")}}),[t]),(0,n.useCallback)((()=>{const l=(0,h.ensureNotNull)(t.current).getBoundingClientRect(),n=a.current[e];return{x:l.left,y:l.top+l.height+n+P,indentFromWindow:{top:L,bottom:L,left:L,right:L}}}),[t,e])}(t,b);return n.createElement(C.PopupMenuDisclosureView,{buttonRef:b,listboxTabIndex:-1,isOpened:l,onClose:g,listboxAria:{"aria-hidden":!0},popupPosition:x,button:n.createElement(T,{"aria-hidden":!0,disabled:a,active:l,onClick:f,ref:E,tabIndex:-1,enableActiveStateStyles:o,hideFocusOutline:r,fake:s},u,n.createElement(M,{size:t,isDropped:l})),popupChildren:i.map((e=>n.createElement(A.PopupMenuItem,{key:e.id,className:d?z["link-item"]:void 0,onClick:v,onClickArg:e,isActive:p(e),label:e.label,isDisabled:e.disabled,link:"href"in e?e.href:void 0,rel:"rel"in e?e.rel:void 0,target:"target"in e?e.target:void 0,renderComponent:"renderComponent"in e?e.renderComponent:void 0,dontClosePopup:!0})))})}function D(e,t){return parseInt(e.getPropertyValue(`--ui-lib-underline-tabs-tab-margin-bottom-${t}`),10)}const R={xsmall:0,small:0,medium:0,large:0,xlarge:0};var O=a(799573),_=a(737402),Y=a(586240),X=a(898163);function W(e){const{size:t,overflowBehaviour:a,className:l}=e;return o(X["scroll-wrap"],X[`size-${t}`],"scroll"===a&&X["enable-scroll"],l)}function V(){const[e,t]=(0,
n.useState)(!1);return(0,n.useEffect)((()=>{t(O.mobiletouch)}),[]),e}var F=a(290484),j=a(431520),H=a(150368),J=a.n(H);const $=100;function K(e){const{disabled:t,translateX:a,transitionDuration:l}=e,r=e.scale/100;return n.createElement("div",{className:o(J().underline,t&&J().disabled),style:{transform:`translateX(${a}px) scaleX(${r})`,transitionDuration:`${l}ms`}},n.createElement("div",{className:J().corner,style:{transform:`scaleX(${1/r})`}}),n.createElement("div",{className:J().center,style:{transform:`scaleX(${1-30/e.scale})`}}),n.createElement("div",{className:J().corner,style:{transform:`scaleX(${1/r})`}}))}function U(e){const{id:t,items:l,activationType:o,orientation:v,disabled:g,moreButtonContent:b=s.t(null,void 0,a(437117)),size:T="small",onActivate:h,isActive:A,className:C,style:x,overflowBehaviour:w,enableActiveStateStyles:y,tablistLabelId:k,tablistLabel:S,"data-name":N="underline-tabs-buttons",stretchTabs:B,equalTabSize:M,defaultKeyboardFocus:z,stopPropagationIfKeyboardActionHandled:P,keyboardNavigationLoop:L,focusableItemAttributes:D}=e,R=V(),O=function(e){const t=(0,_.useSafeMatchMedia)(Y["media-mf-phone-landscape"],!0),a=V();return e??(a||!t?"scroll":"collapse")}(w),H=(0,n.useRef)(!1),J=(0,n.useCallback)((e=>e.id),[]),U="none"===O&&B,q="none"===O&&M,G=y??!R,{visibleItems:Z,hiddenItems:Q,containerRefCallback:ee,innerContainerRefCallback:te,moreButtonRef:ae,setItemRef:le}=(0,i.useCollapsible)(l,J,A),ne="collapse"===O?Z:l,oe="collapse"===O?Q:[],re=(0,n.useCallback)((e=>oe.includes(e)),[oe]),se=(0,n.useRef)(new Map),{isOpened:ie,open:ce,close:ue,onButtonClick:me}=(0,m.useDisclosure)({id:t,disabled:g}),de=function(e="xsmall"){switch(e){case"xsmall":case"small":return 12;case"medium":return 16;case"large":case"xlarge":return 20}}(T),{tabsBindings:pe,tablistBinding:fe,scrollWrapBinding:ve,onActivate:ge,onHighlight:be,isHighlighted:Te}=(0,d.useTabs)({id:t,items:[...ne,...oe],activationType:o,orientation:v,disabled:g,tablistLabelId:k,tablistLabel:S,onActivate:h,isActive:A,isCollapsed:re,isRtl:j.isRtl,itemsRefs:se,isDisclosureOpened:ie,defaultKeyboardFocus:z,stopPropagationIfKeyboardActionHandled:P,keyboardNavigationLoop:L,focusableItemAttributes:D,scrollIntoViewOptions:{additionalScroll:de}}),Ee=l.find(A),he=oe.find(Te),Ae=(0,n.useCallback)((()=>{Ee&&be(Ee)}),[be,Ee]),Ce=(0,n.useCallback)((e=>pe.find((t=>t.id===e.id))??{}),[pe]),xe=(0,n.useCallback)((()=>{ue(),Ae(),H.current=!0}),[ue,Ae]),we=(0,n.useCallback)((()=>{he&&(ge(he),be(he,200))}),[ge,be,he]);ve.ref=(0,c.useMergedRefs)([ve.ref,ee]),fe.ref=(0,c.useMergedRefs)([fe.ref,te]),fe.onKeyDown=(0,u.createSafeMulticastEventHandler)((0,p.useKeyboardEventHandler)([(0,p.useKeyboardClose)(ie,xe),(0,p.useKeyboardActionHandler)([13,32],we,(0,n.useCallback)((()=>Boolean(he)),[he]))]),fe.onKeyDown);const ye=(0,n.useCallback)((e=>{H.current=!0,me(e)}),[H,me]),ke=(0,n.useCallback)((e=>{e&&ge(e)}),[ge]);(0,n.useEffect)((()=>{H.current?H.current=!1:(he&&!ie&&ce(),!he&&ie&&ue())}),[he,ie,ce,ue]);const Se=function(e,t,a=[]){const[l,o]=(0,n.useState)(),r=(0,n.useRef)(),s=(0,
n.useRef)(),i=e=>{const t=e.parentElement??void 0;if(void 0===t)return;const a=void 0===s.current||s.current===e?0:$;s.current=e;const{left:l,right:n,width:r}=e.getBoundingClientRect(),{left:i,right:c}=t.getBoundingClientRect(),u=(0,j.isRtl)()?n-c:l-i;o({translateX:u,scale:r,transitionDuration:a})};return(0,n.useEffect)((()=>{const e=(0,F.default)((e=>{const t=e[0].target;void 0!==t&&i(t)}),50);r.current=new ResizeObserver(e)}),[]),(0,n.useEffect)((()=>{if(void 0===t)return;const a=e.get(t);return void 0!==a?(i(a),r.current?.observe(a),()=>r.current?.disconnect()):void 0}),a),l}(se.current,Ee??he,[Ee??he,ne,T,U,O]);return n.createElement(f.Provider,{value:{size:T,overflowBehaviour:O}},n.createElement("div",{...ve,className:W({size:T,overflowBehaviour:O,className:C}),style:x,"data-name":N},n.createElement("div",{...fe,className:r()(X["underline-tabs"],{[X["make-grid-column"]]:U||q,[X["stretch-tabs"]]:U,[X["equal-tab-size"]]:q})},ne.map((e=>n.createElement(E,{...Ce(e),key:e.id,item:e,onClick:ge,enableActiveStateStyles:G,hideFocusOutline:R,ref:le(J(e)),...e.dataId&&{"data-id":e.dataId},equalTabSize:q}))),oe.map((e=>n.createElement(E,{...Ce(e),ref:le(J(e)),key:e.id,item:e,fake:!0}))),"collapse"===O&&n.createElement(I,{size:T,disabled:g,isOpened:ie,items:oe,buttonContent:b,buttonRef:ae,isHighlighted:Te,onButtonClick:ye,onItemClick:ke,onClose:ue,enableActiveStateStyles:G,hideFocusOutline:R,fake:0===oe.length}),Se?n.createElement(K,{...Se,disabled:g}):n.createElement("div",null))))}var q=a(409245);function G(e){return n.createElement("a",{...(0,q.renameRef)(e)})}(0,n.forwardRef)(((e,t)=>{const{size:a,overflowBehaviour:l}=(0,n.useContext)(f),o=(0,n.useContext)(v.CustomBehaviourContext),{item:r,highlighted:s,handleItemRef:i,onClick:c,active:u,fake:m,className:d,enableActiveStateStyles:p=o.enableActiveStateStyles,hideFocusOutline:g=!1,disabled:T,"aria-disabled":E,...h}=e,A=(0,n.useCallback)((e=>{E?e.preventDefault():c&&c(r)}),[c,E,r]),C=(0,n.useCallback)((e=>{i&&i(r,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[r,i,t]),x=r.renderComponent??G;return n.createElement(x,{...h,id:r.id,"aria-disabled":E,onClick:A,reference:C,href:r.href,rel:r.rel,target:r.target,className:b({size:a,active:u,fake:m,enableActiveStateStyles:p,anchor:!0,hideFocusOutline:g,className:d,overflowBehaviour:l})},r.label)})).displayName="UnderlineAnchorTab"},279708:(e,t,a)=>{"use strict";a.d(t,{addTemplate:()=>r,getTemplates:()=>n,loadTemplate:()=>u,removeTemplate:()=>i,saveTemplate:()=>c,setTemplates:()=>o,startRemoveTemplate:()=>s});var l=a(667259);function n(e,t){return{type:l.GET_TEMPLATES,toolName:e,callback:t}}function o(e,t){return{type:l.SET_TEMPLATES,templates:t,toolName:e}}function r(e,t){return{type:l.ADD_TEMPLATE,templateName:t,toolName:e}}function s(e,t){return{type:l.START_REMOVE_TEMPLATE,templateName:t,toolName:e}}function i(e,t){return{type:l.REMOVE_TEMPLATE,templateName:t,toolName:e}}function c(e,t,a){return{type:l.SAVE_TEMPLATE,templateName:t,toolName:e,content:a}}function u(e,t,a){return{type:l.LOAD_TEMPLATE,toolName:e,
templateName:t,callback:a}}},667259:(e,t,a)=>{"use strict";function l(e){return"LINE_TOOL_TEMPLATE__"+e}a.d(t,{ADD_TEMPLATE:()=>c,GET_TEMPLATES:()=>n,LOAD_TEMPLATE:()=>u,REMOVE_TEMPLATE:()=>s,SAVE_TEMPLATE:()=>i,SET_TEMPLATES:()=>o,START_REMOVE_TEMPLATE:()=>r});const n=l("GET_TEMPLATES"),o=l("SET_TEMPLATES"),r=l("START_REMOVE_TEMPLATE"),s=l("REMOVE_TEMPLATE"),i=l("SAVE_TEMPLATE"),c=l("ADD_TEMPLATE"),u=l("LOAD_TEMPLATE")},917568:(e,t,a)=>{"use strict";a.d(t,{store:()=>C});var l=a(406047),n=a(746212),o=a(129885),r=a(650151),s=a(667259),i=a(671945),c=a(279708);function u(e,t){return t}var m=a(301341);const d=(0,i.getLogger)("Chart.LineToolTemplatesList");function p(e,t){return t}function*f(){for(;;){const{toolName:e,templateName:t,content:a}=p(s.SAVE_TEMPLATE,yield(0,o.take)(s.SAVE_TEMPLATE));try{yield(0,o.call)(m.backend.saveDrawingTemplate,e,t,a),yield(0,o.put)((0,c.addTemplate)(e,t))}catch(e){d.logWarn(e)}}}function*v(){for(;;){const{toolName:e,templateName:t}=p(s.START_REMOVE_TEMPLATE,yield(0,o.take)(s.START_REMOVE_TEMPLATE));try{yield(0,o.call)(m.backend.removeDrawingTemplate,e,t),yield(0,o.put)((0,c.removeTemplate)(e,t))}catch(e){d.logWarn(e)}}}function*g(){const e=new Map;for(;;){const{toolName:a,callback:l}=p(s.GET_TEMPLATES,yield(0,o.take)(s.GET_TEMPLATES));e.has(a)?(0,r.ensureDefined)(e.get(a)).push(l):(e.set(a,[l]),yield(0,o.fork)(t,a))}function*t(t){try{const e=u(m.backend.getDrawingTemplates,yield(0,o.call)(m.backend.getDrawingTemplates,t));yield(0,o.put)((0,c.setTemplates)(t,e))}catch(e){d.logWarn(e)}(0,r.ensureDefined)(e.get(t)).forEach((e=>e?.())),e.delete(t)}}function*b(){for(;;){const{toolName:e,templateName:t,callback:a}=p(s.LOAD_TEMPLATE,yield(0,o.take)(s.LOAD_TEMPLATE));try{const l=u(m.backend.loadDrawingTemplate,yield(0,o.call)(m.backend.loadDrawingTemplate,e,t));a&&a(l)}catch(e){d.logWarn(e)}}}function*T(){yield(0,o.all)([(0,o.call)(g),(0,o.call)(f),(0,o.call)(v),(0,o.call)(b)])}const E={templates:{}};function h(e,t){return e.localeCompare(t,void 0,{numeric:!0})}function A(e=E,t){switch(t.type){case s.ADD_TEMPLATE:{const{toolName:a,templateName:l}=t;if(!e.templates[a].includes(l)){const t=[...e.templates[a],l].sort(h);return{...e,templates:{...e.templates,[a]:t}}}return e}case s.SET_TEMPLATES:{const{toolName:a,templates:l}=t;return{...e,templates:{...e.templates,[a]:[...l].sort(h)}}}case s.REMOVE_TEMPLATE:{const{toolName:a,templateName:l}=t;return{...e,templates:{...e.templates,[a]:e.templates[a].filter((e=>e!==l))}}}default:return e}}const C=function(){const e=(0,n.default)(),t=(0,l.createStore)(A,(0,l.applyMiddleware)(e));return e.run(T),t}()},78943:(e,t,a)=>{"use strict";function l(e,t){return Boolean(""===e||e&&-1!==t.toLowerCase().indexOf(e.toLowerCase()))}a.d(t,{autocompleteFilter:()=>l})},347531:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="m4.67 7.38.66-.76L9 9.84l3.67-3.22.66.76L9 11.16 4.67 7.38Z"/></svg>'},863509:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.57 7.85 9 12.62l5.43-4.77-1.32-1.5L9 9.95l-4.11-3.6-1.32 1.5Z"/></svg>'},168874:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m14 18.41-6.7-6.7 1.4-1.42 5.3 5.3 5.3-5.3 1.4 1.41-6.7 6.71Z"/></svg>'}}]);