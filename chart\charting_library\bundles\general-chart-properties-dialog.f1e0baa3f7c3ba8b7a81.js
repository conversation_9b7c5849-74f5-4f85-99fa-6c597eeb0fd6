(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7078],{259142:function(e,t){var n,i,o;i=[t],n=function(e){"use strict";function t(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var n=!1;if("undefined"!=typeof window){var i={get passive(){n=!0}};window.addEventListener("testPassive",null,i),window.removeEventListener("testPassive",null,i)}var o="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),l=[],a=!1,s=-1,r=void 0,c=void 0,m=function(e){return l.some((function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))}))},u=function(e){var t=e||window.event;return!!m(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},d=function(){setTimeout((function(){void 0!==c&&(document.body.style.paddingRight=c,c=void 0),void 0!==r&&(document.body.style.overflow=r,r=void 0)}))};e.disableBodyScroll=function(e,i){if(o){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!l.some((function(t){return t.targetElement===e}))){var d={targetElement:e,options:i||{}};l=[].concat(t(l),[d]),e.ontouchstart=function(e){1===e.targetTouches.length&&(s=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var n,i,o,l;1===t.targetTouches.length&&(i=e,l=(n=t).targetTouches[0].clientY-s,!m(n.target)&&(i&&0===i.scrollTop&&0<l||(o=i)&&o.scrollHeight-o.scrollTop<=o.clientHeight&&l<0?u(n):n.stopPropagation()))},a||(document.addEventListener("touchmove",u,n?{passive:!1}:void 0),a=!0)}}else{b=i,setTimeout((function(){if(void 0===c){var e=!!b&&!0===b.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(c=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===r&&(r=document.body.style.overflow,document.body.style.overflow="hidden")}));var h={targetElement:e,options:i||{}};l=[].concat(t(l),[h])}var b},e.clearAllBodyScrollLocks=function(){o?(l.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),a&&(document.removeEventListener("touchmove",u,n?{passive:!1}:void 0),a=!1),l=[],s=-1):(d(),l=[])},e.enableBodyScroll=function(e){if(o){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,l=l.filter((function(t){return t.targetElement!==e})),a&&0===l.length&&(document.removeEventListener("touchmove",u,n?{passive:!1}:void 0),a=!1)}else 1===l.length&&l[0].targetElement===e?(d(),l=[]):l=l.filter((function(t){return t.targetElement!==e}))}},void 0===(o="function"==typeof n?n.apply(t,i):n)||(e.exports=o)},784263:e=>{e.exports={backButton:"backButton-yMMXpYEB"}},633090:e=>{e.exports={wrapper:"wrapper-nGEmjtaX",container:"container-nGEmjtaX",tab:"tab-nGEmjtaX",active:"active-nGEmjtaX",title:"title-nGEmjtaX",
icon:"icon-nGEmjtaX",withoutIcon:"withoutIcon-nGEmjtaX",titleText:"titleText-nGEmjtaX",nested:"nested-nGEmjtaX",isTablet:"isTablet-nGEmjtaX",isMobile:"isMobile-nGEmjtaX",showLastDivider:"showLastDivider-nGEmjtaX",medium:"medium-nGEmjtaX",large:"large-nGEmjtaX",withoutArrow:"withoutArrow-nGEmjtaX",accessible:"accessible-nGEmjtaX"}},129016:e=>{e.exports={"tablet-normal-breakpoint":"(max-width: 768px)","tablet-small-breakpoint":"(max-width: 440px)",withSidebar:"withSidebar-F0WBLDV5",content:"content-F0WBLDV5",tabContent:"tabContent-F0WBLDV5",applyToAllButton:"applyToAllButton-F0WBLDV5"}},758467:e=>{e.exports={themesButtonText:"themesButtonText-w7kgghoW",themesButtonIcon:"themesButtonIcon-w7kgghoW",defaultsButtonText:"defaultsButtonText-w7kgghoW",defaultsButtonItem:"defaultsButtonItem-w7kgghoW",remove:"remove-w7kgghoW"}},982850:(e,t,n)=>{"use strict";n.d(t,{DialogSidebarItem:()=>d,DialogSidebarWrapper:()=>u});var i,o=n(50959),l=n(497754),a=n.n(l),s=n(878112),r=n(393832),c=n(568648),m=n(633090);function u(e){return o.createElement("div",{className:m.wrapper,...e})}function d(e){const{mode:t,title:n,icon:i,isActive:l,onClick:u,tag:d="div",reference:h,className:b,mobileFontSize:p="medium",showLastDivider:g,useBoldIconsForMobile:v,hideArrow:y,...f}=e,{isMobile:T,isTablet:_}=(0,r.getSidebarMode)(t),S=function(){if(T&&v)return i?.bold;return l?i?.bold:i?.default}();return o.createElement(d,{...f,ref:h,title:_?n:"",className:a()(m.tab,_&&m.isTablet,T&&m.isMobile,l&&m.active,y&&m.withoutArrow,b,_&&"apply-common-tooltip"),onClick:u},i&&o.createElement(s.Icon,{className:m.icon,icon:S}),!_&&o.createElement("span",{className:a()(m.title,!i&&m.withoutIcon,"medium"===p?m.medium:m.large,g&&m.showLastDivider)},o.createElement("span",{className:a()(m.titleText,"apply-overflow-tooltip")},n),T&&!y&&o.createElement(s.Icon,{className:m.nested,icon:c})))}!function(e){e.Medium="medium",e.Large="large"}(i||(i={}))},393832:(e,t,n)=>{"use strict";var i,o;function l(e){return{isMobile:"mobile"===e,isTablet:"tablet"===e}}n.d(t,{getSidebarMode:()=>l}),function(e){e.Bold="bold",e.Default="default"}(i||(i={})),function(e){e.Tablet="tablet",e.Mobile="mobile"}(o||(o={}))},116557:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GeneralChartPropertiesDialogRenderer:()=>de});var i=n(609838),o=n(50959),l=n(805184),a=n(767055),s=n(976669),r=n(633064),c=n(47924),m=n(870122),u=n.n(m),d=n(571956),h=n(930052),b=n(878112),p=n(440891),g=n(46305),v=n(917850),y=n(747830),f=(n(68212),n(153055));const T=i.t(null,void 0,n(306113));var _=n(753327),S=n(669874),C=n(455496),E=n(32133),w=n(522224),k=n(972535),A=n(948711),M=n(758467);function B(e){const{themeName:t,chartWidgetCollection:n,onRemove:i,manager:l}=e,[a,s]=(0,w.useHover)(),r=o.useCallback((()=>function(e,t,n){(0,f.showConfirm)({text:T.format({name:e}),onConfirm:({dialogClose:n})=>{(0,y.removeTheme)(e),t&&t(e),n()}},n)}(t,i,l)),[t,i,l]),c=o.useCallback((()=>{(0,A.loadTheme)(n,{themeName:t,standardTheme:!1}).then((()=>{(0,E.trackEvent)("GUI","Switch to custom theme")}))}),[t,n]);return o.createElement("div",{...s
},o.createElement(g.AccessibleMenuItem,{"data-series-theme-item-theme-name":t,className:M.defaultsButtonItem,isActive:!1,label:t,onClick:c,toolbox:o.createElement(C.MenuRemoveButton,{className:M.remove,hidden:!k.mobiletouch&&!a,onClick:r})}))}var P=n(299120),x=n(994567),D=n(844996);const I=i.t(null,void 0,n(93553)),L=i.t(null,void 0,n(896413)),R=(0,S.appendEllipsis)(i.t(null,void 0,n(276266))),N=i.t(null,void 0,n(277571)),W=p.enabled("chart_template_storage");class F extends o.PureComponent{constructor(e){super(e),this._manager=null,this._reference=o.createRef(),this._handleApplyDefaults=()=>{const{model:e,chartWidgetCollection:t}=this.props;e.restorePreferences();const n=(0,y.getCurrentTheme)().name;(0,A.loadTheme)(t,{themeName:n,standardTheme:!0,applyOverrides:!0,onlyActiveChart:!0})},this._handleSaveAs=()=>{if(W){const{model:e}=this.props;window.runOrSignIn((()=>async function(e,t,i){const[o,l]=await Promise.all([Promise.all([n.e(1715),n.e(7648)]).then(n.bind(n,686099)),(0,y.getThemeNames)()]);o.showThemeSaveDialog(e,t,l,i)}(e.model().template(),this._syncThemeList,this._handleRenameClose)),{source:"Save theme in chart properties"})}},this._handleRemoveTheme=e=>{this.setState({themes:this.state.themes.filter((t=>t!==e))})},this._syncThemeList=()=>{W&&(0,y.getThemeNames)().then((e=>{this.setState({themes:e})}))},this._handleListboxFocus=e=>{e.target instanceof HTMLElement&&(0,x.handleAccessibleMenuFocus)(e,this._reference)},this._handleRenameClose=()=>{this._reference.current?.focus()},this.state={themes:[]},this._syncThemeList()}render(){return o.createElement(_.SlotContext.Consumer,null,(e=>(this._manager=e,o.createElement(h.MatchMedia,{rule:"(max-width: 768px)"},(e=>o.createElement(P.ControlDisclosure,{id:"series-theme-manager",className:!e&&M.themesButtonText,hideArrowButton:e,"data-name":"theme-select",ref:this._reference,buttonChildren:this._getPlaceHolderItem(e),onListboxFocus:this._handleListboxFocus,onListboxKeyDown:x.handleAccessibleMenuKeyDown},this._getThemeItems(e)))))))}_getPlaceHolderItem(e){return e?o.createElement(b.Icon,{className:M.themesButtonIcon,icon:D}):I}_getThemeItems(e){const{isApplyToAllVisible:t,chartWidgetCollection:n,applyToAllCallback:i}=this.props,{themes:l}=this.state;return o.createElement(o.Fragment,null,e&&t&&o.createElement(g.AccessibleMenuItem,{className:M.defaultsButtonItem,isActive:!1,label:N,onClick:i}),o.createElement(g.AccessibleMenuItem,{"data-name":"series-theme-manager-apply-defaults",className:M.defaultsButtonItem,isActive:!1,label:L,onClick:this._handleApplyDefaults}),W&&o.createElement(g.AccessibleMenuItem,{"data-name":"series-theme-manager-save-as",className:M.defaultsButtonItem,isActive:!1,label:R,onClick:this._handleSaveAs}),l.length>0&&o.createElement(o.Fragment,null,o.createElement(v.PopupMenuSeparator,{key:"separator"}),l.map((e=>o.createElement(B,{key:e,themeName:e,onRemove:this._handleRemoveTheme,chartWidgetCollection:n,manager:this._manager})))))}}
var z=n(370981),V=n(653898),j=n(996038),G=n(497754),X=n.n(G),O=n(865266),K=n(602069),H=n(982850),q=n(393832),U=n(633090);const Y=(0,o.forwardRef)(((e,t)=>{const[n,i]=(0,O.useRovingTabindexElement)(t),{className:l}=e;return o.createElement(H.DialogSidebarItem,{...e,className:X()(U.accessible,l),tag:"button",reference:n,tabIndex:i})}));function J(e){const{mode:t,className:n,...i}=e,{isMobile:l,isTablet:a}=(0,q.getSidebarMode)(t),s=X()(U.container,a&&U.isTablet,l&&U.isMobile,n);return o.createElement(K.Toolbar,{...i,className:s,orientation:"vertical",blurOnEscKeydown:!1,blurOnClick:!1,"data-role":"dialog-sidebar"})}var Q=n(742554);const Z={areaSymbolMinTick:"normal",areaSymbolTimezone:"normal",barSymbolMinTick:"normal",barSymbolTimezone:"normal",baselineSymbolMinTick:"normal",baselineSymbolTimezone:"normal",candleSymbolMinTick:"normal",candleSymbolTimezone:"normal",dateFormat:"normal",haSymbolMinTick:"normal",haSymbolTimezone:"normal",hiloSymbolMinTick:"normal",hiloSymbolTimezone:"normal",hollowCandleSymbolMinTick:"normal",hollowCandleSymbolTimezone:"normal",kagiAtrLength:"normal",kagiReversalAmount:"normal",kagiStyle:"normal",kagiSymbolMinTick:"normal",kagiSymbolTimezone:"normal",lineSymbolMinTick:"normal",lineSymbolTimezone:"normal",sessionId:"normal",lockScale:"normal",mainSeriesSymbolAreaPriceSource:"normal",mainSeriesSymbolBaseLevelPercentage:"normal",mainSeriesSymbolBaseLinePriceSource:"normal",mainSeriesSymbolLinePriceSource:"normal",mainSeriesSymbolStyleType:"normal",navButtons:"big",paneButtons:"big",scalesCurrencyUnit:"big",autoLogButtonsVisibility:"big",pbLb:"normal",pbSymbolMinTick:"normal",pbSymbolTimezone:"normal",pnfAtrLength:"normal",pnfBoxSize:"normal",pnfReversalAmount:"normal",pnfSources:"normal",pnfStyle:"normal",pnfSymbolMinTick:"normal",pnfSymbolTimezone:"normal",rangeSymbolMinTick:"normal",rangeSymbolTimezone:"normal",renkoAtrLength:"normal",renkoBoxSize:"normal",renkoStyle:"normal",renkoSymbolMinTick:"normal",renkoSymbolTimezone:"normal",scalesPlacement:"normal",symbolLastValueLabel:"big",symbolTextSource:"normal",tradingNotifications:"normal",tpoSymbolMinTick:"normal",tpoSymbolTimezone:"normal",volFootprintSymbolMinTick:"normal",volFootprintSymbolTimezone:"normal"};var $=n(380132),ee=n(431520),te=n(784263);function ne(e){return o.createElement($.BackButton,{className:te.backButton,size:"medium","aria-label":i.t(null,{context:"input"},n(148199)),preservePaddings:!0,flipIconOnRtl:(0,ee.isRtl)(),...e})}var ie=n(442092),oe=n(129016);const le="properties_dialog.last_page_id";class ae extends o.PureComponent{constructor(e){super(e),this._renderChildren=({requestResize:e,isSmallWidth:t})=>(this._requestResize=e,o.createElement("div",{className:oe.content},this._renderTabs(t),this._renderTabContent(t))),this._renderApplyToAllButton=()=>o.createElement(h.MatchMedia,{rule:j.DialogBreakpoints.TabletNormal},(e=>this._renderApplyToAll(e))),this._renderFooterLeft=()=>{const{model:e,chartWidgetCollection:t}=this.props,{isApplyToAllVisible:n}=this.state;return o.createElement(F,{model:e,isApplyToAllVisible:n,
applyToAllCallback:this._handleApplyToAll,chartWidgetCollection:t})},this._createTabClickHandler=e=>()=>this._selectPage(e),this._selectPage=(e,t)=>{const{activePage:n}=this.state;e!==n&&(n&&n.definitions.unsubscribe(this._onChangeActivePageDefinitions),null!==e&&(t||u().setValue(le,e.id),e.definitions.subscribe(this._onChangeActivePageDefinitions)),this.setState({activePage:e,tableKey:Date.now()},(()=>{this._requestResize&&this._requestResize()})))},this._onChangeActivePageDefinitions=()=>{V.logger.logNormal("Definition collection was updated"),this.setState({tableKey:Date.now()},(()=>{this._requestResize&&this._requestResize()}))},this._onTabVisibilityChanged=()=>{const e=this.props.pages.filter((e=>e.visible.value()));this.setState({visiblePages:e});const t=this.state.activePage;null===t||e.includes(t)||this._selectPage(0===e.length?null:e[0],!0)},this._handleCancel=()=>{this.props.onCancel(),this.props.onClose()},this._handleSubmit=()=>{this.props.onSubmit(),this.props.onClose()},this._handleScroll=()=>{z.globalCloseDelegate.fire()},this._handleApplyToAll=()=>{const{chartWidgetCollection:e,model:t}=this.props,{isApplyToAllVisible:n}=this.state;n&&e.applyPreferencesToAllCharts(t)},this._syncApplyToAllVisibility=()=>{const{chartWidgetCollection:e}=this.props;this.setState({isApplyToAllVisible:(0,d.isMultipleLayout)(e.layout.value())})},this._handleBackClick=()=>{const{activePage:e}=this.state;e&&e.definitions.unsubscribe(this._onChangeActivePageDefinitions),this.setState({activePage:null})},this._handleForceFocus=e=>{(0,ie.updateTabIndexes)(),setTimeout((()=>{const[t]=(0,ie.queryTabbableElements)(e);t&&t.focus()}))};const{pages:t,activePageId:n}=e,i=t.filter((e=>e.visible.value()));let l=i.find((e=>e.id===n))??null;if(!l){const e=u().getValue(le),t=i.find((t=>t.id===e));l=t||(i.length>0?i[0]:null)}this.state={activePage:l,visiblePages:i,isApplyToAllVisible:(0,d.isMultipleLayout)(e.chartWidgetCollection.layout.value()),tableKey:Date.now()}}componentDidMount(){const{chartWidgetCollection:e,pages:t}=this.props,{activePage:n}=this.state;e.layout.subscribe(this._syncApplyToAllVisibility),n&&n.definitions.subscribe(this._onChangeActivePageDefinitions),t.forEach((e=>e.visible.subscribe(this._onTabVisibilityChanged)))}componentWillUnmount(){const{chartWidgetCollection:e,pages:t}=this.props,{activePage:n}=this.state;n&&n.definitions.unsubscribe(this._onChangeActivePageDefinitions),e.layout.unsubscribe(this._syncApplyToAllVisibility),t.forEach((e=>e.visible.unsubscribe(this._onTabVisibilityChanged)))}render(){const{isOpened:e,onClose:t,shouldReturnFocus:l}=this.props,{activePage:a}=this.state;return o.createElement(h.MatchMedia,{rule:j.DialogBreakpoints.TabletSmall},(r=>o.createElement(s.AdaptiveConfirmDialog,{className:oe.withSidebar,dataName:"series-properties-dialog",onClose:t,isOpened:e,title:null!==a&&r?a.title:i.t(null,void 0,n(232514)),footerLeftRenderer:this._renderFooterLeft,additionalButtons:this._renderApplyToAllButton(),additionalHeaderElement:null!==a&&r?o.createElement(ne,{onClick:this._handleBackClick
}):void 0,onSubmit:this._handleSubmit,onForceFocus:this._handleForceFocus,onCancel:this._handleCancel,render:this._renderChildren,submitOnEnterKey:!1,shouldReturnFocus:l})))}_renderTabContent(e){const{pages:t}=this.props,n=this._getCurrentPage(e);if(n){const e=t.find((e=>e.id===n.id)),i=e?e.definitions.value():[];return o.createElement(Q.TouchScrollContainer,{className:oe.tabContent,onScroll:this._handleScroll},o.createElement(r.ControlCustomWidthContext.Provider,{value:Z},o.createElement(c.PropertyTable,{key:this.state.tableKey},i.map((e=>o.createElement(a.Section,{key:e.id,definition:e}))))))}return null}_renderTabs(e){const{activePage:t,visiblePages:n}=this.state;if(t&&e)return null;const i=this._getCurrentPage(e);return o.createElement(h.MatchMedia,{rule:j.DialogBreakpoints.TabletNormal},(e=>o.createElement(h.MatchMedia,{rule:j.DialogBreakpoints.TabletSmall},(t=>{const l=t?"mobile":e?"tablet":void 0;return o.createElement(J,{mode:l,onScroll:this._handleScroll},n.map((e=>o.createElement(Y,{key:e.id,mode:l,"data-name":e.id,title:e.title,icon:e.icon,onClick:this._createTabClickHandler(e),isActive:i?e.id===i.id:void 0}))))}))))}_renderApplyToAll(e){const{isApplyToAllVisible:t}=this.state;return!e&&t&&o.createElement("span",{className:oe.applyToAllButton},o.createElement(l.Button,{appearance:"stroke",onClick:this._handleApplyToAll},i.t(null,void 0,n(277571))))}_getCurrentPage(e){const{pages:t}=this.props,{activePage:n}=this.state;let i=null;return n?i=n:!e&&t.length&&(i=t[0]),i}}var se=n(559410),re=n(995553),ce=n(209039),me=n(651674);const ue=i.t(null,void 0,n(232514));class de extends re.DialogRenderer{constructor(e){super(),this._handleClose=()=>{this._rootInstance?.unmount(),this._setVisibility(!1),this._onClose&&this._onClose()},this._handleSubmit=()=>{},this._handleCancel=()=>{this._model.undoToCheckpoint(this._checkpoint)},this._propertyPages=e.propertyPages,this._model=e.model,this._activePageId=e.activePageId,this._onClose=e.onClose,this._chartWidgetCollection=e.chartWidgetCollection,this._checkpoint=this._ensureCheckpoint(e.undoCheckPoint)}hide(e){e?this._handleCancel():this._handleSubmit(),this._handleClose()}isVisible(){return this.visible().value()}show(e){this.visible().value()||(this._rootInstance=(0,me.createReactRoot)(o.createElement(ae,{title:ue,isOpened:!0,onSubmit:this._handleSubmit,onClose:this._handleClose,onCancel:this._handleCancel,pages:this._propertyPages,model:this._model,activePageId:this._activePageId,chartWidgetCollection:this._chartWidgetCollection,shouldReturnFocus:e?.shouldReturnFocus}),this._container),this._setVisibility(!0),se.emit("edit_object_dialog",{objectType:"mainSeries",scriptTitle:this._model.mainSeries().title(ce.TitleDisplayTarget.StatusLine)}))}_ensureCheckpoint(e){return void 0===e&&(e=this._model.createUndoCheckpoint()),e}}},526448:e=>{e.exports={accessible:"accessible-NQERJsv9",active:"active-NQERJsv9"}},309306:e=>{e.exports={button:"button-Y1TCZogJ",active:"active-Y1TCZogJ"}},46305:(e,t,n)=>{"use strict";n.d(t,{AccessibleMenuItem:()=>m})
;var i=n(50959),o=n(497754),l=n.n(o),a=n(930202),s=n(865266),r=n(192063),c=n(526448);function m(e){const{className:t,...n}=e,[o,m]=(0,s.useRovingTabindexElement)(null);return i.createElement(r.PopupMenuItem,{...n,className:l()(c.accessible,e.isActive&&c.active,t),reference:o,tabIndex:m,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,a.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),o.current instanceof HTMLElement&&o.current.click())},"data-role":"menuitem","aria-disabled":e.isDisabled||void 0,toolboxRole:"toolbar"})}},455496:(e,t,n)=>{"use strict";n.d(t,{MenuRemoveButton:()=>u});var i=n(50959),o=n(497754),l=n.n(o),a=n(609838),s=n(865266),r=n(72621),c=n(460925),m=n(309306);function u(e){const{onClick:t,isActive:o,onKeyDown:u,...d}=e,[h,b]=(0,s.useRovingTabindexElement)(null);return i.createElement("button",{ref:h,tabIndex:b,onClick:t,onKeyDown:u,className:l()(m.button,o&&m.active,"apply-common-tooltip"),"aria-label":a.t(null,void 0,n(767410)),"data-tooltip":a.t(null,void 0,n(767410)),type:"button"},i.createElement(r.RemoveButton,{...d,isActive:o,title:"",icon:c}))}},568648:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentcolor" stroke-width="1.3" d="M12 9l5 5-5 5"/></svg>'},925931:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>i});let i=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);