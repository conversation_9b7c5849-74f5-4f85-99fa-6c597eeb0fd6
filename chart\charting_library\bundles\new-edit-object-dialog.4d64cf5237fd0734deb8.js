(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6265],{845658:e=>{e.exports={small:"small-CtnpmPzP",medium:"medium-CtnpmPzP",large:"large-CtnpmPzP",switchView:"switchView-CtnpmPzP",checked:"checked-CtnpmPzP",disabled:"disabled-CtnpmPzP",track:"track-CtnpmPzP",thumb:"thumb-CtnpmPzP"}},420071:e=>{e.exports={switcher:"switcher-fwE97QDf",input:"input-fwE97QDf",thumbWrapper:"thumbWrapper-fwE97QDf",disabled:"disabled-fwE97QDf",checked:"checked-fwE97QDf"}},953483:e=>{e.exports={scrollable:"scrollable-vwgPOHG8",tabs:"tabs-vwgPOHG8"}},984950:(e,t,l)=>{"use strict";l.d(t,{Switch:()=>m,SwitchView:()=>c});var r,n=l(50959),o=l(497754),i=l.n(o),s=l(234539),a=l(845658);function c(e){const{size:t="small",checked:l,disabled:r}=e;return n.createElement("span",{className:i()(a.switchView,a[t],r&&a.disabled,l&&a.checked)},n.createElement("span",{className:a.track}),n.createElement("span",{className:a.thumb}))}!function(e){e.Small="small",e.Medium="medium",e.Large="large"}(r||(r={}));var p,d=l(930202),h=l(420071),u=l.n(h);function m(e){const t=(0,n.useContext)(s.CustomBehaviourContext),{size:l,intent:r="default",checked:i,className:a,enableActiveStateStyles:p=t.enableActiveStateStyles,disabled:h,onChange:m,title:y,id:v,name:g,value:b,tabIndex:w,role:f="switch",ariaDisabled:P,reference:C,ariaLabelledBy:S,ariaLabel:T,...E}=e;return n.createElement("span",{className:o(a,u().switcher)},n.createElement("input",{...E,type:"checkbox",className:o(u().input,p&&u().activeStylesEnabled,i&&u().checked,h&&u().disabled),role:f,"aria-checked":i,checked:i,onKeyDown:e=>{13===(0,d.hashFromEvent)(e)&&e.currentTarget?.click()},onChange:m,disabled:h,"aria-disabled":P,tabIndex:w,title:y,id:v,name:g,value:b,ref:C,"aria-label":T,"aria-labelledby":S}),n.createElement("span",{className:o(u().thumbWrapper,u()[r])},n.createElement(c,{checked:i,size:l,disabled:h})))}!function(e){e.Default="default",e.Select="select"}(p||(p={}))},653084:(e,t,l)=>{"use strict";l.r(t),l.d(t,{EditObjectDialogRenderer:()=>kl});var r=l(50959),n=l(650151),o=l(609838),i=l(272047),s=l(540642),a=l(870122),c=l(440891),p=l(559410),d=l(32133),h=l(976669),u=l(370981),m=l(742554),y=l(278906),v=l(380865),g=l(128492),b=l(944316),w=l(839246),f=l(677090),P=l(953483);class C extends r.PureComponent{constructor(e){super(e),this._handleClose=e=>{e?.target&&(e.target.closest('[data-dialog-name="gopro"]')||e.target.closest("[data-name=support-dialog]"))||this.props.onClose()},this._handleResetToDefaults=()=>{const{source:e,model:t}=this.props;(0,y.isStudy)(e)&&t.restorePropertiesForSource(e)},this._handleSaveAsDefaults=()=>{const{source:e}=this.props;(0,y.isStudy)(e)&&(e.properties().saveDefaults(),p.emit("study_dialog_save_defaults",e.id()))},this._renderFooterLeft=e=>{const{source:t,model:l}=this.props;if((0,g.isLineTool)(t))return r.createElement(b.FooterMenu,{sources:[t],chartUndoModel:l});if((0,y.isStudy)(t))return r.createElement(f.PropertyActions,{saveAsDefaults:this._handleSaveAsDefaults,resetToDefaults:this._handleResetToDefaults,mode:e?"compact":"normal"})
;throw new TypeError("Unsupported source type.")},this._handleSelect=e=>{this.setState({activeTabId:e},(()=>{this._requestResize&&this._requestResize()})),this.props.onActiveTabChanged&&this.props.onActiveTabChanged(e)},this._handleScroll=()=>{u.globalCloseDelegate.fire()},this._handleSubmit=()=>{this.props.onSubmit(),this.props.onClose()};const{pages:t,initialActiveTab:l}=this.props,n=t.find((e=>e.id===l))??t[0];this.state={activeTabId:n.id}}render(){const{title:e,onCancel:t,onClose:l,shouldReturnFocus:n}=this.props;return r.createElement(h.AdaptiveConfirmDialog,{dataName:"indicator-properties-dialog",title:e,isOpened:!0,onSubmit:this._handleSubmit,onCancel:t,onClickOutside:this._handleClose,onClose:l,footerLeftRenderer:this._renderFooterLeft,render:this._renderChildren(),submitOnEnterKey:!1,shouldReturnFocus:n})}_renderChildren(){return({requestResize:e})=>{this._requestResize=e;const{pages:t,source:l,model:n}=this.props,{activeTabId:o}=this.state,i=t.find((e=>e.id===o))??t[0],s="Component"in i?void 0:i.page,a=t.map((({label:e,id:t})=>({label:e,id:t,dataId:`indicator-properties-dialog-tabs-${t}`})));return r.createElement(r.Fragment,null,r.createElement(w.DialogTabs,{className:P.tabs,id:"indicator-properties-dialog-tabs",activeTab:i.id,onChange:this._handleSelect,tabs:a}),r.createElement(m.TouchScrollContainer,{className:P.scrollable,onScroll:this._handleScroll},"Component"in i?r.createElement(i.Component,{source:l,model:n}):r.createElement(v.PropertiesEditorTab,{page:s,tableKey:i.id})))}}}var S=l(80831),T=l(383533);class E extends r.PureComponent{constructor(e){super(e),this._properties=this.props.source.properties(),this._inputs=new T.MetaInfoHelper(this.props.source.metaInfo()).getUserEditableInputs()}render(){return r.createElement(S.InputsTabContent,{property:this._properties,model:this.props.model,study:this.props.source,studyMetaInfo:this.props.source.metaInfo(),inputs:this._inputs})}}var _=l(414823),k=l(806353),x=l(762293),L=l(527053),I=l(562364),V=l(494182),R=l(14043);const D=new i.TranslatedString("change visibility",o.t(null,void 0,l(201924)));class M extends r.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{visible:l}=this.props;l&&t(l,e,D)}}render(){const{id:e,title:t,visible:l,disabled:n}=this.props,o=(0,s.clean)((0,R.getTranslatedInputTitle)(t),!0);return r.createElement(I.BoolInputComponent,{label:o,disabled:n,input:{id:e,type:"bool",defval:!0,name:"visible"},value:!l||(0,V.getPropertyValue)(l),onChange:this._onChange})}}M.contextType=L.StylePropertyContext;var N=l(193400),W=l(917850),B=l(372319),A=l(865588),z=l(294152),F=l(718819),H=l(614643),G=l(646464),U=l(396298),O=l(718621),j=l(98450),q=l(591512),Z=l(193976),Q=l(772914),K=l(321579);const $={[k.LineStudyPlotStyle.Line]:{type:k.LineStudyPlotStyle.Line,order:0,icon:z,label:o.t(null,void 0,l(903554))},[k.LineStudyPlotStyle.LineWithBreaks]:{type:k.LineStudyPlotStyle.LineWithBreaks,order:1,icon:F,label:o.t(null,void 0,l(234862))},[k.LineStudyPlotStyle.StepLine]:{type:k.LineStudyPlotStyle.StepLine,
order:2,icon:H,label:o.t(null,void 0,l(869217))},[k.LineStudyPlotStyle.StepLineWithBreaks]:{type:k.LineStudyPlotStyle.StepLineWithBreaks,order:3,icon:G,label:o.t(null,void 0,l(814788))},[k.LineStudyPlotStyle.StepLineWithDiamonds]:{type:k.LineStudyPlotStyle.StepLineWithDiamonds,order:4,icon:U,label:o.t(null,void 0,l(711877))},[k.LineStudyPlotStyle.Histogram]:{type:k.LineStudyPlotStyle.Histogram,order:5,icon:O,label:o.t(null,void 0,l(978057))},[k.LineStudyPlotStyle.Cross]:{type:k.LineStudyPlotStyle.Cross,order:6,icon:j,label:o.t(null,{context:"chart_type"},l(233857))},[k.LineStudyPlotStyle.Area]:{type:k.LineStudyPlotStyle.Area,order:7,icon:q,label:o.t(null,void 0,l(534456))},[k.LineStudyPlotStyle.AreaWithBreaks]:{type:k.LineStudyPlotStyle.AreaWithBreaks,order:8,icon:Z,label:o.t(null,void 0,l(207349))},[k.LineStudyPlotStyle.Columns]:{type:k.LineStudyPlotStyle.Columns,order:9,icon:Q,label:o.t(null,void 0,l(655761))},[k.LineStudyPlotStyle.Circles]:{type:k.LineStudyPlotStyle.Circles,order:10,icon:K,label:o.t(null,void 0,l(505669))}},X=Object.values($).sort(((e,t)=>e.order-t.order)).map((e=>({value:e.type,selectedContent:r.createElement(A.DisplayItem,{icon:e.icon}),content:r.createElement(A.DropItem,{icon:e.icon,label:e.label})}))),J=o.t(null,void 0,l(72926));class Y extends r.PureComponent{render(){const{id:e,plotType:t,className:l,priceLine:n,plotTypeChange:o,priceLineChange:i,disabled:s}=this.props;if(!(t in $))return null;const a={readonly:!0,content:r.createElement(r.Fragment,null,r.createElement(B.MenuItemSwitcher,{id:"PlotTypePriceLineSwitch",checked:n,label:J,preventLabelHighlight:!0,value:"priceLineSwitcher",onChange:i}),r.createElement(W.PopupMenuSeparator,null))};return r.createElement(A.IconDropdown,{id:e,disabled:s,className:l,hideArrowButton:!0,items:[a,...X],value:t,onChange:o})}}var ee=l(642091),te=l(252444);const le=new i.TranslatedString("change plot type",o.t(null,void 0,l(343439))),re=new i.TranslatedString("change price line visibility",o.t(null,void 0,l(108662)));class ne extends r.PureComponent{constructor(){super(...arguments),this._onPlotTypeChange=e=>{const{setValue:t}=this.context,{styleProp:{plottype:l}}=this.props;l&&t(l,e,le)},this._onPriceLineChange=e=>{const{setValue:t}=this.context,{styleProp:{trackPrice:l}}=this.props;l&&t(l,e,re)}}render(){const{id:e,paletteColor:t,paletteColorProps:l,styleProp:n,isLine:o,hasPlotTypeSelect:i,grouped:s,offset:a}=this.props,c=l.childs();return r.createElement(N.InputRow,{grouped:s,label:r.createElement("div",{className:te.childRowContainer},(0,R.getTranslatedInputTitle)(t.name)),offset:a},r.createElement(ee.ColorWithLinePropertySelect,{disabled:!n.visible.value(),color:c.color,transparency:n.transparency,thickness:o?c.width:void 0,isPaletteColor:!0}),o&&i&&n.plottype&&n.trackPrice?r.createElement(Y,{id:(0,_.createDomId)(e,"plot-type-select"),disabled:!n.visible.value(),className:te.smallStyleControl,plotType:n.plottype.value(),priceLine:n.trackPrice.value(),plotTypeChange:this._onPlotTypeChange,priceLineChange:this._onPriceLineChange}):null)}}
ne.contextType=L.StylePropertyContext;var oe=l(47924);function ie(e,t,l,o,i,s,a){const c=t.colors,p=l.colors;return Object.keys(c).map(((t,l)=>r.createElement(ne,{key:a?`${t}-secondary`:t,id:e,grouped:!0,paletteColor:(0,n.ensureDefined)(c[t]),paletteColorProps:(0,n.ensureDefined)(p[t]),styleProp:o,isLine:i,hasPlotTypeSelect:0===l,offset:s})))}class se extends r.PureComponent{render(){const{plot:e,area:t,palette:l,paletteProps:o,hideVisibilitySwitch:i,styleProp:s,showOnlyTitle:a,showSeparator:c=!0,offset:p,secondaryPalette:d,secondaryPaletteProps:h,title:u}=this.props,m=e?e.id:(0,n.ensureDefined)(t).id,y=!m.startsWith("fill")&&e&&(0,k.isLinePlot)(e);return r.createElement(r.Fragment,null,!i&&r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2,offset:p},a?r.createElement("div",null,u):r.createElement(M,{id:m,title:u,visible:s.visible}))),ie(m,l,o,s,y,p),d&&h&&ie(m,d,h,s,y,p,!0),c&&r.createElement(oe.PropertyTable.GroupSeparator,null))}}se.contextType=L.StylePropertyContext;var ae=l(671945),ce=l(890410);class pe extends r.PureComponent{constructor(e){super(e),this._visible=new ce.StudyPlotVisibleProperty(e.styleProp.display)}render(){const{title:e,plot:t,area:l,palette:n,paletteProps:o,hideVisibilitySwitch:i,styleProp:s,showOnlyTitle:a,showSeparator:c=!0,offset:p}=this.props;return r.createElement(se,{plot:t,area:l,title:e,palette:n,paletteProps:o,styleProp:{...s,visible:this._visible},showSeparator:c,hideVisibilitySwitch:i,showOnlyTitle:a,offset:p})}componentWillUnmount(){this._visible.destroy()}}pe.contextType=L.StylePropertyContext;class de extends r.PureComponent{constructor(e){super(e),this._visible=new ce.StudyPlotVisibleProperty(e.display)}render(){const{id:e,title:t,disabled:l}=this.props;return r.createElement(M,{id:e,title:t,disabled:l,visible:this._visible})}componentWillUnmount(){this._visible.destroy()}}de.contextType=L.StylePropertyContext;var he=l(554393);const ue=new i.TranslatedString("change plot type",o.t(null,void 0,l(343439))),me=new i.TranslatedString("change price line visibility",o.t(null,void 0,l(108662)));class ye extends r.PureComponent{constructor(){super(...arguments),this._onPlotTypeChange=e=>{const{setValue:t}=this.context,{property:{plottype:l}}=this.props;l&&t(l,e,ue)},this._onPriceLineChange=e=>{const{setValue:t}=this.context,{property:{trackPrice:l}}=this.props;l&&t(l,e,me)}}render(){const{id:e,title:t,isRGB:l,isFundamental:n,property:{color:o,plottype:i,linewidth:s,transparency:a,trackPrice:c,display:p}}=this.props;return r.createElement(N.InputRow,{label:r.createElement(de,{id:e,title:t,display:p})},l&&!n?this._getInputForRgb():r.createElement(ee.ColorWithLinePropertySelect,{disabled:0===p.value(),color:o,transparency:a,thickness:s}),r.createElement(Y,{id:(0,_.createDomId)(e,"plot-type-select"),disabled:0===p.value(),className:te.smallStyleControl,plotType:i.value(),priceLine:c.value(),plotTypeChange:this._onPlotTypeChange,priceLineChange:this._onPriceLineChange}))}_getInputForRgb(){
const{id:e,showLineWidth:t,property:l}=this.props,{linewidth:n,display:o}=l;return n&&t?r.createElement(he.LineWidthSelect,{id:(0,_.createDomId)(e,"line-width-select"),property:n,disabled:0===o.value()}):null}}ye.contextType=L.StylePropertyContext;const ve=r.createContext(null);var ge=l(796855);const be=new i.TranslatedString("change line style",o.t(null,void 0,l(728818)));class we extends r.PureComponent{constructor(){super(...arguments),this._onLineStyleChange=e=>{const{setValue:t}=this.context,{lineStyle:l}=this.props;t(l,e,be)}}render(){const{lineStyle:e,...t}=this.props;return r.createElement(ge.LineStyleSelect,{...t,lineStyle:(0,V.getPropertyValue)(e),lineStyleChange:this._onLineStyleChange})}}we.contextType=L.StylePropertyContext;class fe extends r.PureComponent{render(){const{id:e,isRGB:t,title:l,visible:n,color:o,transparency:i,thickness:s,children:a,switchable:c=!0,offset:p,grouped:d,disabled:h,lineStyle:u}=this.props,m=h||n&&!(Array.isArray(n)?n[0].value():n.value());return r.createElement(N.InputRow,{label:c?r.createElement(M,{id:e,title:l,visible:n,disabled:h}):l,offset:p,grouped:d},t?u?r.createElement(we,{id:(0,_.createDomId)(e,"line-style-select"),disabled:m,className:te.smallStyleControl,lineStyle:u}):null:r.createElement(ee.ColorWithLinePropertySelect,{disabled:m,color:o,transparency:i,thickness:s,lineStyle:u}),a)}}fe.contextType=L.StylePropertyContext;class Pe extends r.PureComponent{constructor(e){super(e),this._visible=new ce.StudyPlotVisibleProperty(e.display)}render(){const{id:e,isRGB:t,title:l,color:n,transparency:o,thickness:i,children:s,switchable:a=!0,offset:c,grouped:p}=this.props;return r.createElement(fe,{id:e,isRGB:t,title:l,color:n,transparency:o,thickness:i,children:s,switchable:a,offset:c,grouped:p,visible:this._visible})}componentWillUnmount(){this._visible.destroy()}}Pe.contextType=L.StylePropertyContext;class Ce extends r.PureComponent{render(){const{id:e,isRGB:t,property:{colorup:l,colordown:o,transparency:i,display:s}}=this.props;return r.createElement(ve.Consumer,null,(a=>r.createElement(r.Fragment,null,r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2,grouped:!0},r.createElement(de,{id:e,title:dt((0,n.ensureNotNull)(a),e),display:s}))),!t&&r.createElement(r.Fragment,null,r.createElement(Pe,{id:e,title:ot,color:l,transparency:i,display:s,switchable:!1,offset:!0,grouped:!0}),r.createElement(Pe,{id:e,title:it,color:o,transparency:i,display:s,switchable:!1,offset:!0,grouped:!0})),r.createElement(oe.PropertyTable.GroupSeparator,null))))}}Ce.contextType=L.StylePropertyContext;var Se=l(687795),Te=l.n(Se),Ee=l(497754),_e=l.n(Ee),ke=l(654936),xe=l(982021),Le=l(529631),Ie=l(813108);const Ve={[Ie.MarkLocation.AboveBar]:{value:Ie.MarkLocation.AboveBar,content:o.t(null,void 0,l(308305)),order:0},[Ie.MarkLocation.BelowBar]:{value:Ie.MarkLocation.BelowBar,content:o.t(null,void 0,l(609417)),order:1},[Ie.MarkLocation.Top]:{value:Ie.MarkLocation.Top,content:o.t(null,void 0,l(697118)),order:2},[Ie.MarkLocation.Bottom]:{
value:Ie.MarkLocation.Bottom,content:o.t(null,void 0,l(327567)),order:3},[Ie.MarkLocation.Absolute]:{value:Ie.MarkLocation.Absolute,content:o.t(null,void 0,l(669758)),order:4}},Re=Object.values(Ve).sort(((e,t)=>e.order-t.order));class De extends r.PureComponent{render(){const{id:e,shapeLocation:t,className:l,menuItemClassName:n,shapeLocationChange:o,disabled:i}=this.props;return r.createElement(Le.Select,{id:e,disabled:i,className:l,menuItemClassName:n,items:Re,value:t,onChange:o})}}const Me=new i.TranslatedString("change char",o.t(null,void 0,l(886955))),Ne=new i.TranslatedString("change location",o.t(null,void 0,l(6834)));class We extends r.PureComponent{constructor(){super(...arguments),this._onCharChange=e=>{const{setValue:t}=this.context,l=e.currentTarget.value.trim(),r=Te()(l),o=0===r.length?"":r[r.length-1];t((0,n.ensureDefined)(this.props.property.childs().char),o,Me)},this._onLocationChange=e=>{const{setValue:t}=this.context;t(this.props.property.childs().location,e,Ne)}}render(){const{id:e,title:t,char:l,isRGB:o,property:i,hasPalette:s}=this.props,{color:a,transparency:c,char:p,location:d,display:h}=i.childs();return r.createElement(N.InputRow,{grouped:s,label:r.createElement(de,{id:e,title:t,display:h})},!s&&!o&&r.createElement(xe.BasicColorSelect,{disabled:0===h.value(),color:a,transparency:c}),r.createElement(ke.InputControl,{disabled:void 0===p||0===h.value(),className:te.smallStyleControl,value:(0,n.ensureDefined)(p?.value()??l),onChange:this._onCharChange}),r.createElement(De,{id:(0,_.createDomId)(e,"shape-style-select"),disabled:0===h.value(),className:Ee(te.defaultSelect,te.additionalSelect),menuItemClassName:te.defaultSelectItem,shapeLocation:d.value(),shapeLocationChange:this._onLocationChange}))}}We.contextType=L.StylePropertyContext;var Be,Ae=l(703252),ze=l(669151),Fe=l(167211),He=l(583786),Ge=l(250858),Ue=l(713201),Oe=l(659058),je=l(808537),qe=l(202309),Ze=l(778240),Qe=l(241683),Ke=l(663798),$e=l(923223);!function(e){e.ArrowDown="arrow_down",e.ArrowUp="arrow_up",e.Circle="circle",e.Cross="cross",e.Diamond="diamond",e.Flag="flag",e.LabelDown="label_down",e.LabelUp="label_up",e.Square="square",e.TriangleDown="triangle_down",e.TriangleUp="triangle_up",e.XCross="x_cross"}(Be||(Be={}));const Xe={arrow_down:ze,arrow_up:Fe,circle:He,cross:Ge,diamond:Ue,flag:Oe,label_down:je,label_up:qe,square:Ze,triangle_down:Qe,triangle_up:Ke,x_cross:$e};function Je(e){return Xe[e]}const Ye=[];Object.keys(Ae.plotShapesData).forEach((e=>{const t=Ae.plotShapesData[e];Ye.push({id:t.id,value:t.id,selectedContent:r.createElement(A.DisplayItem,{icon:Je(t.icon)}),content:r.createElement(A.DropItem,{icon:Je(t.icon),label:t.guiName})})}));class et extends r.PureComponent{render(){const{id:e,shapeStyleId:t,className:l,shapeStyleChange:n,disabled:o}=this.props;return r.createElement(A.IconDropdown,{id:e,disabled:o,className:l,hideArrowButton:!0,items:Ye,value:t,onChange:n})}}const tt=new i.TranslatedString("change shape",o.t(null,void 0,l(83468))),lt=new i.TranslatedString("change location",o.t(null,void 0,l(6834)))
;class rt extends r.PureComponent{constructor(){super(...arguments),this._onPlotTypeChange=e=>{const{setValue:t}=this.context;t(this.props.property.childs().plottype,e,tt)},this._onLocationChange=e=>{const{setValue:t}=this.context;t(this.props.property.childs().location,e,lt)}}render(){const{id:e,title:t,isRGB:l,hasPalette:n,property:o}=this.props,{color:i,transparency:s,plottype:a,location:c,display:p}=o.childs();return r.createElement(N.InputRow,{grouped:n,label:r.createElement(de,{id:e,title:t,display:p})},!n&&!l&&r.createElement(xe.BasicColorSelect,{disabled:0===p.value(),color:i,transparency:s}),r.createElement(et,{id:(0,_.createDomId)(e,"shape-style-select"),disabled:0===p.value(),className:te.smallStyleControl,shapeStyleId:a.value(),shapeStyleChange:this._onPlotTypeChange}),r.createElement(De,{id:(0,_.createDomId)(e,"shape-location-select"),disabled:0===p.value(),className:Ee(te.defaultSelect,te.additionalSelect),menuItemClassName:te.defaultSelectItem,shapeLocation:c.value(),shapeLocationChange:this._onLocationChange}))}}rt.contextType=L.StylePropertyContext;const nt=(0,ae.getLogger)("Chart.Study.PropertyPage"),ot=o.t(null,void 0,l(922691)),it=o.t(null,void 0,l(671776)),st=o.t(null,void 0,l(874406)),at=o.t(null,void 0,l(632163)),ct=o.t(null,void 0,l(38408));class pt extends r.PureComponent{render(){const{plot:e,palettes:t,study:l}=this.props,o=e.id,i=l.properties().styles,s=l.metaInfo().styles,a=i[o],c=e.type,p=t.main,d=!!l.metaInfo().isRGB;if("line"===c||"bar_colorer"===c||"bg_colorer"===c)return p&&p.palette&&p.paletteProps?r.createElement(pe,{title:s?.[o]?.title??o,plot:e,palette:p.palette,paletteProps:p.paletteProps,styleProp:a}):r.createElement(ye,{id:o,title:(0,n.ensureDefined)(s?.[o]?.title),property:a,isRGB:d,isFundamental:false,showLineWidth:"line"===c});if("arrows"===c){const n=this._getPlotSwitch(o,dt(l,o),a.display);if(d)return n;const i=t.up,s=t.down;return i||s?r.createElement(r.Fragment,null,n,i&&i.palette&&i.paletteProps?r.createElement(pe,{title:ot,plot:e,palette:i.palette,paletteProps:i.paletteProps,styleProp:a,showSeparator:!1,showOnlyTitle:!0,offset:!0}):r.createElement(Pe,{id:o,isRGB:d,title:ot,color:a.colorup,display:a.display,transparency:a.transparency,switchable:!1,grouped:!0,offset:!0}),s&&s.palette&&s.paletteProps?r.createElement(pe,{title:it,plot:e,palette:s.palette,paletteProps:s.paletteProps,styleProp:a,showSeparator:!1,showOnlyTitle:!0,offset:!0}):r.createElement(Pe,{id:o,isRGB:d,title:it,color:a.colordown,display:a.display,transparency:a.transparency,switchable:!1,grouped:!0,offset:!0}),r.createElement(oe.PropertyTable.GroupSeparator,null)):r.createElement(Ce,{id:o,property:a,isRGB:d,plot:e,palettes:t,styleProp:a})}if("chars"===c||"shapes"===c){const t=(0,n.ensureDefined)(s?.[o]),l=t.title;return r.createElement(r.Fragment,null,"chars"===c?r.createElement(We,{id:o,title:l,char:t.char,property:a,hasPalette:Boolean(p&&p.palette),isRGB:d}):r.createElement(rt,{id:o,title:l,property:a,hasPalette:Boolean(p&&p.palette),isRGB:d}),p&&p.palette&&p.paletteProps&&r.createElement(pe,{
title:l,plot:e,palette:p.palette,paletteProps:p.paletteProps,hideVisibilitySwitch:!0,styleProp:a}))}if((0,k.isOhlcPlot)(e)){const i=e.target,s=l.properties().ohlcPlots[i],a=(0,n.ensureDefined)((0,n.ensureDefined)(l.metaInfo().ohlcPlots)[i]),c=this._getPlotSwitch(o,a.title,s.display);if(d)return c;const h=t.wick&&t.wick.palette&&t.wick.paletteProps,u=t.border&&t.border.palette&&t.border.paletteProps;return r.createElement(r.Fragment,null,c,p&&p.palette&&p.paletteProps?r.createElement(pe,{title:st,plot:e,palette:p.palette,paletteProps:p.paletteProps,styleProp:s,showSeparator:!1,showOnlyTitle:!0,offset:!0}):r.createElement(Pe,{id:o,isRGB:d,title:st,display:s.display,color:s.color,transparency:s.transparency,switchable:!1,grouped:!0,offset:!0}),t.wick&&t.wick.palette&&t.wick.paletteProps&&r.createElement(pe,{title:at,plot:e,palette:t.wick.palette,paletteProps:t.wick.paletteProps,styleProp:s,showSeparator:!1,showOnlyTitle:!0,offset:!0}),Boolean(!h&&s.wickColor)&&r.createElement(Pe,{id:o,isRGB:d,title:at,display:s.display,color:s.wickColor,transparency:s.transparency,switchable:!1,grouped:!0,offset:!0}),t.border&&t.border.palette&&t.border.paletteProps&&r.createElement(pe,{title:ct,plot:e,palette:t.border.palette,paletteProps:t.border.paletteProps,styleProp:s,showSeparator:!1,showOnlyTitle:!0,offset:!0}),Boolean(!u&&s.borderColor)&&r.createElement(Pe,{id:o,isRGB:d,title:ct,display:s.display,color:s.borderColor,transparency:s.transparency,switchable:!1,grouped:!0,offset:!0}),r.createElement(oe.PropertyTable.GroupSeparator,null))}return nt.logError("Unknown plot type: "+c),null}_getPlotSwitch(e,t,l){return r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2},r.createElement(de,{id:e,title:t,display:l})))}}function dt(e,t){const l=(0,n.ensureDefined)(e.metaInfo().styles),{title:r}=(0,n.ensureDefined)(l[t]);return(0,n.ensureDefined)(r)}var ht=l(802527);const ut=new i.TranslatedString("change value",o.t(null,void 0,l(821333)));class mt extends r.PureComponent{constructor(){super(...arguments),this._onValueChange=e=>{const{setValue:t}=this.context,{value:l}=this.props.property;t(l,e,ut)}}render(){const{id:e,name:t,property:{color:l,linestyle:n,linewidth:o,transparency:i,value:s,visible:a}}=this.props;return r.createElement(N.InputRow,{labelAlign:"adaptive",label:r.createElement(M,{id:e,title:t,visible:a})},r.createElement("div",{className:te.block},r.createElement("div",{className:te.group},r.createElement(ee.ColorWithLinePropertySelect,{disabled:!a.value(),color:l,transparency:i,thickness:o,lineStyle:n})),r.createElement("div",{className:Ee(te.wrapGroup,te.defaultSelect,te.additionalSelect)},r.createElement(ht.FloatInputComponent,{input:{id:"",name:"",type:"float",defval:0},value:s.value(),disabled:!a.value(),onChange:this._onValueChange}))))}}mt.contextType=L.StylePropertyContext;class yt extends r.PureComponent{render(){const{orders:{visible:e,showLabels:t,showQty:n}}=this.props
;return r.createElement(r.Fragment,null,r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2},r.createElement(M,{id:"chart-orders-switch",title:o.t(null,void 0,l(306532)),visible:e}))),r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2},r.createElement(M,{id:"chart-orders-labels-switch",title:o.t(null,void 0,l(238712)),visible:t}))),r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2},r.createElement(M,{id:"chart-orders-qty-switch",title:o.t(null,void 0,l(598721)),visible:n}))))}}yt.contextType=L.StylePropertyContext;var vt=l(598563),gt=l(517651);const bt=new i.TranslatedString("change percent width",o.t(null,void 0,l(862294))),wt=new i.TranslatedString("change placement",o.t(null,void 0,l(981891))),ft=new i.TranslatedString("change values visibility",o.t(null,void 0,l(109344))),Pt=[{value:vt.HHistDirection.LeftToRight,content:o.t(null,void 0,l(411626))},{value:vt.HHistDirection.RightToLeft,content:o.t(null,void 0,l(50421))}],Ct=o.t(null,void 0,l(304622)),St=o.t(null,void 0,l(610783)),Tt=o.t(null,void 0,l(660092)),Et=o.t(null,void 0,l(77753));class _t extends r.PureComponent{constructor(){super(...arguments),this._onPercentWidthChange=e=>{const{setValue:t}=this.context,{percentWidth:l}=this.props.property.childs();t(l,e,bt)},this._onPlacementChange=e=>{const{setValue:t}=this.context,{direction:l}=this.props.property.childs();t(l,e,wt)},this._onShowValuesChange=e=>{const{setValue:t}=this.context,{showValues:l}=this.props.property.childs();t(l,e,ft)}}render(){const{hHistInfo:e,property:t}=this.props,{percentWidth:l,direction:n,showValues:o,valuesColor:i,visible:s}=t.childs(),{title:a}=e;return r.createElement(r.Fragment,null,r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2,grouped:!0},r.createElement(M,{id:a,title:a,visible:s}))),r.createElement(N.InputRow,{label:r.createElement("div",{className:te.childRowContainer},Ct),grouped:!0},r.createElement(gt.IntegerInputComponent,{input:{id:"",name:"",type:"integer",defval:0},value:l.value(),disabled:!s.value(),onChange:this._onPercentWidthChange})),r.createElement(N.InputRow,{label:r.createElement("div",{className:te.childRowContainer},St),grouped:!0},r.createElement(Le.Select,{id:"hhist-graphic-placement-select",disabled:!s.value(),className:te.defaultSelect,menuItemClassName:te.defaultSelectItem,items:Pt,value:n.value(),onChange:this._onPlacementChange})),r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{className:te.childRowContainer,placement:"first",colSpan:2,grouped:!0},r.createElement(I.BoolInputComponent,{label:Tt,input:{id:a+"_showValues",type:"bool",defval:!0,name:"visible"},value:!o||o.value(),disabled:!s.value(),onChange:this._onShowValuesChange}))),r.createElement(N.InputRow,{label:r.createElement("div",{className:te.childRowContainer},Et),grouped:!0},r.createElement(xe.BasicColorSelect,{
disabled:s&&!s.value(),color:i})),this._renderColors(),r.createElement(oe.PropertyTable.GroupSeparator,null))}_renderColors(){const{property:e,hHistInfo:t}=this.props,{colors:l,transparencies:n,visible:o}=e.childs(),{titles:i}=t;return l.childNames().map((e=>r.createElement(N.InputRow,{key:e,grouped:!0,label:r.createElement("div",{className:te.childRowContainer},i[+e])},r.createElement(xe.BasicColorSelect,{disabled:!o.value(),color:l[+e],transparency:n[+e]}))))}}_t.contextType=L.StylePropertyContext;class kt extends r.PureComponent{render(){const{title:e,property:t}=this.props,{color:l,width:n,style:o,visible:i}=t.childs();return r.createElement(N.InputRow,{label:r.createElement(M,{id:e,title:e,visible:i})},r.createElement(ee.ColorWithLinePropertySelect,{disabled:!i.value(),color:l,transparency:t.child("transparency"),thickness:n,lineStyle:o}))}}var xt,Lt;kt.contextType=L.StylePropertyContext,function(e){e.Triangle="triangle",e.Rectangle="rectangle"}(xt||(xt={})),function(e){e.Verdana="Verdana",e.CourierNew="Courier New",e.TimesNewRoman="Times New Roman",e.Arial="Arial"}(Lt||(Lt={}));class It extends r.PureComponent{render(){const{graphicType:e,study:t}=this.props,l=t.metaInfo(),o=l.graphics,i=t.properties().graphics.childs(),s=(0,n.ensureDefined)(o[e]);return Object.keys(s).map(((t,o)=>{const s=(0,n.ensureDefined)(i[e]?.childs()[t]);return"horizlines"===e||"vertlines"===e?r.createElement(kt,{key:t,title:(0,n.ensureDefined)(l.graphics[e]?.[t]).name,property:s}):"lines"===e?r.createElement(kt,{key:t,title:(0,n.ensureDefined)(l.graphics.lines?.[t]).title,property:s}):"hhists"===e?r.createElement(_t,{key:t,hHistInfo:(0,n.ensureDefined)(l.graphics.hhists?.[t]),property:s}):null}))}}var Vt=l(39290),Rt=l(232668);const Dt=new i.TranslatedString("change font size",o.t(null,void 0,l(327745))),Mt=[10,11,12,14,16,20,24,28,32,40].map((e=>({value:e,title:e.toString()})));class Nt extends r.PureComponent{constructor(){super(...arguments),this._onFontSizeChange=e=>{const{setValue:t}=this.context,{fontSize:l}=this.props;t(l,e,Dt)}}render(){const{fontSize:e,...t}=this.props;return r.createElement(Rt.FontSizeSelect,{...t,fontSizes:Mt,fontSize:e.value(),fontSizeChange:this._onFontSizeChange})}}Nt.contextType=L.StylePropertyContext;const Wt=new i.TranslatedString("change visibility",o.t(null,void 0,l(201924))),Bt=o.t(null,void 0,l(462791)),At=o.t(null,void 0,l(5119)),zt={Traditional:new Set(["S5/R5","S4/R4","S3/R3","S2/R2","S1/R1","P"]),Fibonacci:new Set(["S3/R3","S2/R2","S1/R1","P"]),Woodie:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),Classic:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),DM:new Set(["S1/R1","P"]),DeMark:new Set(["S1/R1","P"]),Camarilla:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),Floor:new Set(["S3/R3","S2/R2","S1/R1","P"])};class Ft extends r.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{levelsStyle:l}=this.props.property.childs(),{showLabels:r}=l.childs();t(r,e,Wt)}}render(){const{fontsize:e,levelsStyle:t}=this.props.property.childs()
;return r.createElement(r.Fragment,null,r.createElement(N.InputRow,{labelAlign:"adaptive",label:r.createElement("span",null,Bt)},r.createElement("div",{className:te.block},r.createElement("div",{className:Ee(te.wrapGroup,te.additionalSelect)},r.createElement(Nt,{id:"pivot-points-standard-font-size-select",fontSize:e})))),r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2},r.createElement(I.BoolInputComponent,{label:At,input:{id:"ShowLabels",type:"bool",defval:!0,name:"visible"},value:t.childs().showLabels.value(),onChange:this._onChange}))),this._renderColors())}_renderColors(){const{levelsStyle:e,inputs:t}=this.props.property.childs(),{colors:l,widths:o,visibility:i}=e.childs(),{kind:s}=t.childs(),a=(0,n.ensureDefined)(zt[s.value()]);return l.childNames().filter((e=>a.has(e))).map((e=>r.createElement(fe,{key:e,id:e,title:e,color:l.childs()[e],visible:i.childs()[e],thickness:o.childs()[e]})))}}Ft.contextType=L.StylePropertyContext;const Ht=o.t(null,void 0,l(610783)),Gt=[{value:vt.HHistDirection.RightToLeft,content:o.t(null,void 0,l(50421))},{value:vt.HHistDirection.LeftToRight,content:o.t(null,void 0,l(411626))}],Ut=new i.TranslatedString("change visibility",o.t(null,void 0,l(201924))),Ot=(new i.TranslatedString("change expand blocks",o.t(null,void 0,l(185889))),o.t(null,void 0,l(81363))),jt=o.t(null,void 0,l(660092)),qt=o.t(null,void 0,l(304622)),Zt=o.t(null,void 0,l(673033)),Qt=o.t(null,{context:"input"},l(149191)),Kt=o.t(null,{context:"input"},l(476542));class $t extends r.PureComponent{constructor(){super(...arguments),this._onChange=e=>{this._setHhistsProperty("visible",e)},this._onShowValuesChange=e=>{this._setHhistsProperty("showValues",e)},this._onValueChange=e=>{this._setHhistsProperty("percentWidth",e)},this._onDirectionChange=e=>{this._setHhistsProperty("direction",e)}}render(){const{metaInfo:e}=this.props,{graphics:t,styles:l,showLabelsOnPriceScale:o,showLegendValues:i}=this.props.property.childs(),{hhists:s,horizlines:a,polygons:c}=t.childs(),p=(0,n.ensureDefined)(e.graphics.hhists),d=Object.keys(p),h=s.childs()[d[0]],u=h.childs().visible,m=d.map((e=>s.childs()[e].childs().showValues)),y=h.childs().percentWidth,v=h.childs().direction,g=d.map((e=>s.childs()[e].childs().valuesColor)),b=a.childs()?.vahLines,w=e.graphics.horizlines?.vahLines,f=a.childs()?.valLines,P=e.graphics.horizlines?.valLines,C=a.childs().pocLines,S=(0,n.ensureDefined)(e.graphics.horizlines?.pocLines),T=l.childs().developingPoc,E=new ce.StudyPlotVisibleProperty(T.childs().display),_=(0,n.ensureDefined)(e.styles?.developingPoc),k=l.childs().developingVAHigh,x=new ce.StudyPlotVisibleProperty(k.childs().display),L=l.childs().developingVALow,V=new ce.StudyPlotVisibleProperty(L.childs().display),D=e.graphics.polygons&&e.graphics.polygons.histBoxBg;return r.createElement(r.Fragment,null,r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2},r.createElement(I.BoolInputComponent,{label:Ot,input:{id:"VolumeProfile",type:"bool",
defval:!0,name:"visible"},value:u.value(),onChange:this._onChange}))),r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first"},r.createElement("div",{className:te.childRowContainer},r.createElement(I.BoolInputComponent,{disabled:!u.value(),label:jt,input:{id:"ShowValues",type:"bool",defval:!0,name:"visible"},value:m[0].value(),onChange:this._onShowValuesChange}))),r.createElement(oe.PropertyTable.Cell,{placement:"last"},r.createElement(xe.BasicColorSelect,{disabled:!u.value()||!m[0].value(),color:g}))),r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first"},r.createElement("div",{className:te.childRowContainer},qt)),r.createElement(oe.PropertyTable.Cell,{placement:"last"},r.createElement(gt.IntegerInputComponent,{disabled:!u.value(),input:{id:"",name:"",type:"integer",defval:0},value:y.value(),onChange:this._onValueChange}))),r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.Cell,{placement:"first"},r.createElement("div",{className:te.childRowContainer},Ht)),r.createElement(oe.PropertyTable.Cell,{placement:"last"},r.createElement(Le.Select,{id:"hhist-direction-select",disabled:!u.value(),className:te.defaultSelect,menuItemClassName:te.defaultSelectItem,items:Gt,value:v.value(),onChange:this._onDirectionChange}))),d.map((e=>r.createElement(r.Fragment,{key:e},s.childs()[e].childs().colors.childNames().map(((t,l)=>{const n=p[e];return r.createElement(N.InputRow,{key:l,label:r.createElement("div",{className:te.childRowContainer},n&&(0,R.getTranslatedInputTitle)(n.titles[l])||"")},r.createElement(xe.BasicColorSelect,{disabled:!u.value(),color:s.childs()[e].childs().colors.childs()[l],transparency:s.childs()[e].childs().transparencies.childs()[l]}))}))))),w&&b&&r.createElement(fe,{id:"vahLines",title:w.name,color:b.childs().color,visible:b.childs().visible,thickness:b.childs().width,lineStyle:b.childs().style}),P&&f&&r.createElement(fe,{id:"valLines",title:P.name,color:f.childs().color,visible:f.childs().visible,thickness:f.childs().width,lineStyle:f.childs().style}),r.createElement(fe,{id:"pocLines",title:S.name,color:C.childs().color,visible:C.childs().visible,thickness:C.childs().width,lineStyle:C.childs().style}),T&&r.createElement(fe,{id:"developingPoc",title:_.title&&(0,R.getTranslatedInputTitle)(_.title)||"",color:T.childs().color,visible:E,thickness:T.childs().linewidth,lineStyle:T.childs().linestyle}),k&&L&&r.createElement(fe,{id:"developingPoc",title:Zt,color:[k.childs().color,L.childs().color],visible:[x,V],thickness:[k.childs().linewidth,L.childs().linewidth],lineStyle:[k.childs().linestyle,L.childs().linestyle]}),c&&r.createElement(N.InputRow,{label:r.createElement("div",null,D&&(0,R.getTranslatedInputTitle)(D.name)||"")},r.createElement(xe.BasicColorSelect,{color:c.childs().histBoxBg.childs().color,transparency:c.childs().histBoxBg.childs().transparency})),(o||i)&&"VbPFixed"!==e.shortId&&r.createElement(r.Fragment,null,o&&r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2
},r.createElement(M,{id:"showLabelsOnPriceScale",title:Kt,visible:o})),i&&r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2},r.createElement(M,{id:"showLegendValues",title:Qt,visible:i}))))}_setHhistsProperty(e,t){const{setValue:l}=this.context,{metaInfo:r,property:o}=this.props,i=o.childs().graphics.childs().hhists,s=Object.keys((0,n.ensureDefined)(r.graphics.hhists)),a=i.childs();l(s.map((t=>(0,n.ensureDefined)(a[t].child(e)))),t,Ut)}}function Xt(){const e=(0,n.ensureNotNull)((0,r.useContext)(ve)),t=e.metaInfo(),l=e.properties();return r.createElement($t,{metaInfo:t,property:l})}$t.contextType=L.StylePropertyContext;var Jt=l(176086);const Yt={VbPFixed:Xt,PivotPointsStandard:function(){const e=(0,n.ensureNotNull)((0,r.useContext)(ve)).properties();return r.createElement(Ft,{property:e})},VbPVisible:Xt,VbPAnchored:Xt};class el extends r.PureComponent{render(){const e=(0,n.ensureNotNull)(this.context);return r.createElement(ve.Consumer,null,(t=>r.createElement(L.StylePropertyContainer,{property:(0,n.ensureNotNull)(t).properties(),affectSave:(0,Vt.doesStudyLikeAffectSave)((0,n.ensureNotNull)(t)),model:e},r.createElement(oe.PropertyTable,null,this._renderCustomContent((0,n.ensureNotNull)(t).metaInfo().shortId)))))}_renderCustomContent(e){if(e in Yt){const t=Yt[e];return r.createElement(t,null)}return null}}el.contextType=Jt.ModelContext;var tl=l(92264);const ll=new i.TranslatedString("change precision",o.t(null,void 0,l(61863))),rl=o.t(null,void 0,l(916564)),nl=o.t(null,void 0,l(159766)),ol=[{value:"default",content:rl}];for(let e=0;e<=8;e++)ol.push({value:e,content:e.toString()});class il extends r.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{precision:l}=this.props;t(l,e,ll)}}render(){const{id:e,precision:t}=this.props;return r.createElement(N.InputRow,{label:nl},r.createElement(Le.Select,{id:e,className:te.defaultSelect,menuItemClassName:te.defaultSelectItem,items:ol,value:t.value(),onChange:this._onChange}))}}il.contextType=L.StylePropertyContext;const sl=new i.TranslatedString("change min tick",o.t(null,void 0,l(626476))),al=o.t(null,void 0,l(916564)),cl=o.t(null,void 0,l(964075)),pl=[{priceScale:1,minMove:1,frac:!1},{priceScale:10,minMove:1,frac:!1},{priceScale:100,minMove:1,frac:!1},{priceScale:1e3,minMove:1,frac:!1},{priceScale:1e4,minMove:1,frac:!1},{priceScale:1e5,minMove:1,frac:!1},{priceScale:1e6,minMove:1,frac:!1},{priceScale:1e7,minMove:1,frac:!1},{priceScale:1e8,minMove:1,frac:!1},{priceScale:2,minMove:1,frac:!0},{priceScale:4,minMove:1,frac:!0},{priceScale:8,minMove:1,frac:!0},{priceScale:16,minMove:1,frac:!0},{priceScale:32,minMove:1,frac:!0},{priceScale:64,minMove:1,frac:!0},{priceScale:128,minMove:1,frac:!0},{priceScale:320,minMove:1,frac:!0}],dl=[{id:"tick-default",value:"default",content:al}];for(let e=0;e<pl.length;e++){const t=pl[e];dl.push({value:t.priceScale+","+t.minMove+","+t.frac,content:t.minMove+"/"+t.priceScale})}class hl extends r.PureComponent{constructor(){super(...arguments),this._onChange=e=>{
const{setValue:t}=this.context,{minTick:l}=this.props;t(l,e,sl)}}render(){const{id:e,minTick:t}=this.props;return r.createElement(N.InputRow,{label:cl},r.createElement(Le.Select,{id:e,className:te.defaultSelect,menuItemClassName:te.defaultSelectItem,items:dl,value:t.value(),onChange:this._onChange}))}}hl.contextType=L.StylePropertyContext;var ul=l(852830);class ml extends r.PureComponent{render(){const{id:e,isRGB:t,title:l,visible:n,bottomColor:o,topColor:i,transparency:s,children:a,switchable:c=!0,offset:p,grouped:d}=this.props;return r.createElement(N.InputRow,{label:c?r.createElement(M,{id:e,title:l,visible:n}):l,offset:p,grouped:d},t?null:r.createElement(r.Fragment,null,i&&r.createElement(xe.BasicColorSelect,{disabled:n&&!(Array.isArray(n)?n[0].value():n.value()),color:i,transparency:s}),o&&r.createElement("div",{className:_e()(o&&i&&te.additionalSelect)},r.createElement(xe.BasicColorSelect,{disabled:n&&!(Array.isArray(n)?n[0].value():n.value()),color:o,transparency:s}))),a)}}ml.contextType=L.StylePropertyContext;const yl=o.t(null,void 0,l(379468)),vl=o.t(null,{context:"input"},l(149191)),gl=o.t(null,{context:"input"},l(476542)),bl=o.t(null,void 0,l(89702));class wl extends r.PureComponent{constructor(){super(...arguments),this._findPlotPalettes=e=>{const{study:t}=this.props,l=t.metaInfo(),r=(0,n.ensureDefined)(l.palettes);return(0,k.isBarColorerPlot)(e)||(0,k.isBgColorerPlot)(e)?{main:{palette:r[e.palette],paletteProps:t.properties().palettes[e.palette]}}:this._findPalettesByTargetId(e.id)}}render(){const{study:e}=this.props,t=e.metaInfo();if((0,tl.isCustomStudy)(t.shortId))return r.createElement(el,null);const l=e.properties(),{precision:n,strategy:o,minTick:i,showLabelsOnPriceScale:s,showLegendValues:a}=l,c=t.plots.length>0,p=t.plots.some((e=>!(0,k.isPlotWithTechnicalValues)(e))),d=c||t.inputs.some((e=>"price"===e.type)),h=(0,Vt.createAdapter)(e).canOverrideMinTick();return r.createElement(oe.PropertyTable,null,this._plotsElement(),this._bandsElement(),this._bandsBackgroundsElement(),this._areasBackgroundsElement(),this._filledAreasElement(),this._graphicsElement(),h&&r.createElement(hl,{id:(0,_.createDomId)(t.id,"min-tick-select"),minTick:i}),x.StudyMetaInfo.isScriptStrategy(t)&&r.createElement(yt,{orders:o.orders}),(d||p)&&r.createElement(oe.PropertyTable.Row,null,r.createElement(oe.PropertyTable.GroupSeparator,{size:1}),r.createElement(ul.GroupTitleSection,{title:bl,name:bl}),d&&r.createElement(il,{id:(0,_.createDomId)(t.id,"precision-select"),precision:n}),p&&r.createElement(r.Fragment,null,r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2},r.createElement(M,{id:"showLabelsOnPriceScale",title:gl,visible:s})),r.createElement(oe.PropertyTable.Cell,{placement:"first",colSpan:2},r.createElement(M,{id:"showLegendValues",title:vl,visible:a})))))}_plotsElement(){const{study:e}=this.props,t=e.metaInfo();return new T.MetaInfoHelper(t).getUserEditablePlots().filter((e=>!((0,k.isUpColorerPlot)(e)||(0,k.isDownColorerPlot)(e)||(0,k.isCandleBorderColorerPlot)(e)||(0,
k.isCandleWickColorerPlot)(e)))).map((t=>{const l=(0,k.isOhlcPlot)(t)?{...t,id:t.target}:t,n=this._findPlotPalettes(l);return r.createElement(pt,{key:t.id,plot:t,palettes:n,study:e})}))}_bandsElement(){const{study:e}=this.props,t=e.metaInfo().bands,l=e.properties().childs().bands;return t&&l&&t.map(((e,t)=>{if(!e.isHidden)return r.createElement(mt,{key:t,id:e.name,name:e.name,property:l[t]})}))}_bandsBackgroundsElement(){const{study:e}=this.props,t=e.properties(),{bandsBackground:l}=t;return l&&r.createElement(fe,{id:"bandsBackground",title:yl,visible:l.fillBackground,color:l.backgroundColor,transparency:l.transparency})}_areasBackgroundsElement(){const{study:e}=this.props,t=e.metaInfo(),l=e.properties(),{areaBackground:n}=l;return t.isRGB?null:n&&r.createElement(fe,{id:"areaBackground",title:yl,visible:n.fillBackground,color:n.backgroundColor,transparency:n.transparency})}_filledAreasElement(){const{study:e}=this.props,t=e.metaInfo(),l=t.filledAreas;return!l||t.isRGB?[]:l.map((l=>{const o=t.filledAreasStyle?.[l.id];if(o){if(o.isHidden)return null}else if(l.isHidden)return null;const i=e.properties().filledAreasStyle[l.id],s=l.title||yl;if(i.hasChild("fillType")&&"gradient"===i.childs().fillType.value()){if(i.topColor||i.bottomColor)return r.createElement(ml,{key:l.id,id:l.id,title:s,bottomColor:i.bottomColor,topColor:i.topColor,visible:i.visible,transparency:i.transparency});if(l.palette){const e=this._findPalettesByTargetId(l.id),t=(0,n.ensureDefined)(e.main),o=e.secondary;return r.createElement(se,{key:l.id,title:l.title,area:l,palette:(0,n.ensureDefined)(t.palette),paletteProps:(0,n.ensureDefined)(t.paletteProps),secondaryPalette:o?.palette,secondaryPaletteProps:o?.paletteProps,styleProp:i})}return null}if(l.palette){const e=this._findPalettesByTargetId(l.id),t=(0,n.ensureDefined)(e.main);return r.createElement(se,{key:l.id,title:l.title,area:l,palette:(0,n.ensureDefined)(t.palette),paletteProps:(0,n.ensureDefined)(t.paletteProps),styleProp:i})}return r.createElement(fe,{key:l.id,id:l.id,title:s,color:i.color,visible:i.visible,transparency:i.transparency})}))}_graphicsElement(){const{study:e}=this.props,t=e.metaInfo().graphics;return t&&Object.keys(t).map(((t,l)=>r.createElement(It,{key:t,graphicType:t,study:e})))}_findPalettesByTargetId(e){const{study:t}=this.props,l=t.metaInfo(),r=l.plots,o=(0,n.ensureDefined)(l.palettes),i={};for(const l of r){if(((0,k.isPaletteColorerPlot)(l)||(0,k.isOhlcColorerPlot)(l))&&l.target===e){if(i.main){i.secondary={palette:o[l.palette],paletteProps:t.properties().palettes[l.palette]};continue}i.main={palette:o[l.palette],paletteProps:t.properties().palettes[l.palette]}}(0,k.isUpColorerPlot)(l)&&l.target===e&&(i.up={palette:o[l.palette],paletteProps:t.properties().palettes[l.palette]}),(0,k.isDownColorerPlot)(l)&&l.target===e&&(i.down={palette:o[l.palette],paletteProps:t.properties().palettes[l.palette]}),(0,k.isCandleWickColorerPlot)(l)&&l.target===e&&(i.wick={palette:o[l.palette],paletteProps:t.properties().palettes[l.palette]}),(0,
k.isCandleBorderColorerPlot)(l)&&l.target===e&&(i.border={palette:o[l.palette],paletteProps:t.properties().palettes[l.palette]})}return i}}function fl(e){return(0,L.bindPropertyContext)(wl,{...e,property:e.study.properties()},(0,Vt.doesStudyLikeAffectSave)(e.study))}class Pl extends r.PureComponent{render(){return r.createElement(Jt.ModelContext.Provider,{value:this.props.model},r.createElement(ve.Provider,{value:this.props.source},r.createElement(fl,{study:this.props.source})))}}var Cl=l(363127),Sl=l(995553),Tl=l(854030),El=l(209039),_l=l(651674);class kl extends Sl.DialogRenderer{constructor(e,t,l,r){super(),this._timeout=null,this._handleClose=()=>{this._rootInstance?.unmount(),this._setVisibility(!1),this._subscription.unsubscribe(this,this._handleCollectionChanged)},this._handleCancel=()=>{this._model.undoToCheckpoint(this._checkpoint)},this._handleSubmit=()=>{},this._handleActiveTabChanged=e=>{a.setValue(this._activeTabSettingsName(),e)},this._source=e,this._model=t,this._propertyPages=r,this._checkpoint=this._ensureCheckpoint(l),this._subscription=this._model.model().dataSourceCollectionChanged(),this._subscription.subscribe(this,this._handleCollectionChanged)}hide(e){e?this._handleCancel():this._handleSubmit(),this._handleClose()}isVisible(){return this.visible().value()}show(e={}){if(!c.enabled("property_pages")||this.isVisible())return;const t=this._source.metaInfo();if((0,g.isLineTool)(this._source)&&(0,d.trackEvent)("GUI","Drawing Properties",this._source.name()),(0,y.isStudy)(this._source)){const e=!this._source.isPine()||this._source.isStandardPine()?t.description:"Custom Pine";(0,d.trackEvent)("GUI","Study Properties",e)}let n=[];const i=new T.MetaInfoHelper(t);i.hasUserEditableInputs()&&n.push({id:"inputs",label:o.t(null,void 0,l(321429)),Component:E}),i.hasUserEditableProperties(),i.hasUserEditableStyles()&&n.push({id:"style",label:o.t(null,void 0,l(492516)),Component:Pl}),this._propertyPages||n.push({id:"visibilities",label:o.t(null,void 0,l(640091)),page:this._createVisibilitiesPropertyPage()}),n=this._getPagesForStudyLineTool(n);const h=e.initialTab||a.getValue(this._activeTabSettingsName())||"inputs";let u=(0,s.clean)(t.shortDescription,!0);const m=n.find((e=>e.id===h))??n[0];this._rootInstance=(0,_l.createReactRoot)(r.createElement(C,{title:u,model:this._model,source:this._source,initialActiveTab:m.id,pages:n,shouldReturnFocus:e.shouldReturnFocus,onSubmit:this._handleSubmit,onCancel:this._handleCancel,onClose:this._handleClose,onActiveTabChanged:this._handleActiveTabChanged}),this._container),this._setVisibility(!0),p.emit("edit_object_dialog",{objectType:"study",scriptTitle:this._source.title(El.TitleDisplayTarget.StatusLine)})}_createVisibilitiesPropertyPage(){const e=this._source.properties().childs().intervalsVisibilities.childs();return(0,Cl.createPropertyPage)((0,Tl.getIntervalsVisibilitiesPropertiesDefinitions)(this._model,e,new i.TranslatedString(this._source.name(!0),this._source.title(El.TitleDisplayTarget.StatusLine,!0))),"visibility",o.t(null,void 0,l(640091)))}
_activeTabSettingsName(){return"properties_dialog.active_tab.study"}_ensureCheckpoint(e){return void 0===e&&(e=this._model.createUndoCheckpoint()),e}_getPagesForStudyLineTool(e){if(this._propertyPages){const t=this._propertyPages.filter((e=>"coordinates"===e.id||"visibility"===e.id));return[...e,...t.map((e=>({id:e.id,label:e.title,page:e})))]}return e}_handleCollectionChanged(){null===this._timeout&&(this._timeout=setTimeout((()=>{this._closeDialogIfSourceIsDeleted(),this._timeout=null})))}_closeDialogIfSourceIsDeleted(){null===this._model.model().dataSourceForId(this._source.id())&&this._handleClose()}}},222315:e=>{e.exports={wrapper:"wrapper-bl9AR3Gv",hovered:"hovered-bl9AR3Gv",switchWrap:"switchWrap-bl9AR3Gv",withIcon:"withIcon-bl9AR3Gv",labelRow:"labelRow-bl9AR3Gv",label:"label-bl9AR3Gv",icon:"icon-bl9AR3Gv",labelHint:"labelHint-bl9AR3Gv",labelOn:"labelOn-bl9AR3Gv",accessible:"accessible-bl9AR3Gv"}},252444:e=>{e.exports={smallStyleControl:"smallStyleControl-l5f4IL9k",additionalSelect:"additionalSelect-l5f4IL9k",childRowContainer:"childRowContainer-l5f4IL9k",defaultSelect:"defaultSelect-l5f4IL9k",defaultSelectItem:"defaultSelectItem-l5f4IL9k",block:"block-l5f4IL9k",group:"group-l5f4IL9k",wrapGroup:"wrapGroup-l5f4IL9k",textMarkGraphicBlock:"textMarkGraphicBlock-l5f4IL9k",textMarkGraphicWrapGroup:"textMarkGraphicWrapGroup-l5f4IL9k",transparency:"transparency-l5f4IL9k",color:"color-l5f4IL9k"}},372319:(e,t,l)=>{"use strict";l.d(t,{DEFAULT_MENU_ITEM_SWITCHER_THEME:()=>h,MenuItemSwitcher:()=>u});var r=l(50959),n=l(497754),o=l.n(n),i=l(984950),s=l(878112),a=l(930202),c=l(865266),p=l(800417),d=l(222315);const h=d;function u(e){const{role:t,checked:l,onChange:n,className:h,id:u,label:m,labelDescription:y,preventLabelHighlight:v,value:g,reference:b,switchReference:w,theme:f=d,disabled:P,switchRole:C,icon:S}=e,[T,E]=(0,c.useRovingTabindexElement)(null),_=o()(f.label,l&&!v&&f.labelOn),k=o()(h,f.wrapper,l&&f.wrapperWithOnLabel,y&&f.wrapperWithDescription);return r.createElement("label",{role:t,className:o()(k,S&&f.withIcon,d.accessible),htmlFor:u,ref:b,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,a.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),T.current instanceof HTMLElement&&T.current.click())},tabIndex:E,"data-role":"menuitem","aria-disabled":e.disabled||void 0,"aria-selected":l},void 0!==S&&r.createElement(s.Icon,{className:f.icon,icon:S}),r.createElement("div",{className:f.labelRow},r.createElement("div",{className:_},m),y&&r.createElement("div",{className:f.labelHint},y)),r.createElement("div",{className:d.switchWrap},r.createElement(i.Switch,{disabled:P,className:f.switch,reference:function(e){T(e),w?.(e)},checked:l,onChange:function(e){const t=e.target.checked;void 0!==n&&n(t)},value:g,tabIndex:-1,id:u,role:C,ariaDisabled:!0,...(0,p.filterDataProps)(e)})))}},363127:(e,t,l)=>{"use strict";l.r(t),l.d(t,{createPropertyPage:()=>n});var r=l(240534);function n(e,t,l,n=null){const o={id:t,title:l,definitions:new r.WatchedValue(e.definitions),
visible:e.visible??new r.WatchedValue(!0).readonly()};return null!==n&&(o.icon=n),o}},854030:(e,t,l)=>{"use strict";l.r(t),l.d(t,{getIntervalsVisibilitiesPropertiesDefinitions:()=>ae,getSelectionIntervalsVisibilitiesPropertiesDefinition:()=>ce});var r=l(609838),n=l(272047),o=l(798346),i=l(240534),s=l(989175),a=l(206212),c=l(981856),p=l(622860);const d=new n.TranslatedString("change {title} visibility on ticks",r.t(null,void 0,l(798596))),h=new n.TranslatedString("change {title} visibility on seconds",r.t(null,void 0,l(441315))),u=new n.TranslatedString("change {title} seconds from",r.t(null,void 0,l(86780))),m=new n.TranslatedString("change {title} seconds to",r.t(null,void 0,l(906573))),y=new n.TranslatedString("change {title} visibility on minutes",r.t(null,void 0,l(578219))),v=new n.TranslatedString("change {title} minutes from",r.t(null,void 0,l(859820))),g=new n.TranslatedString("change {title} minutes to",r.t(null,void 0,l(938011))),b=new n.TranslatedString("change {title} visibility on hours",r.t(null,void 0,l(368715))),w=new n.TranslatedString("change {title} hours from",r.t(null,void 0,l(508306))),f=new n.TranslatedString("change {title} hours to",r.t(null,void 0,l(567233))),P=new n.TranslatedString("change {title} visibility on days",r.t(null,void 0,l(783137))),C=new n.TranslatedString("change {title} days from",r.t(null,void 0,l(591201))),S=new n.TranslatedString("change {title} days to",r.t(null,void 0,l(196135))),T=new n.TranslatedString("change {title} visibility on weeks",r.t(null,void 0,l(871084))),E=new n.TranslatedString("change {title} weeks from",r.t(null,void 0,l(832481))),_=new n.TranslatedString("change {title} weeks to",r.t(null,void 0,l(818678))),k=new n.TranslatedString("change {title} visibility on months",r.t(null,void 0,l(167583))),x=new n.TranslatedString("change {title} months from",r.t(null,void 0,l(399122))),L=new n.TranslatedString("change {title} months to",r.t(null,void 0,l(910518))),I=(new n.TranslatedString("change {title} visibility on ranges",r.t(null,{replace:{ranges:"ranges"}},l(855616))),
r.t(null,void 0,l(824821))),V=r.t(null,void 0,l(65188)),R=r.t(null,void 0,l(942562)),D=r.t(null,void 0,l(356796)),M=r.t(null,void 0,l(272942)),N=r.t(null,void 0,l(900835)),W=r.t(null,void 0,l(943154)),B=new n.TranslatedString("ticks",r.t(null,void 0,l(803539))),A=new n.TranslatedString("seconds",r.t(null,void 0,l(751))),z=new n.TranslatedString("seconds from",r.t(null,void 0,l(535801))),F=new n.TranslatedString("seconds to",r.t(null,void 0,l(273419))),H=new n.TranslatedString("minutes",r.t(null,void 0,l(418726))),G=new n.TranslatedString("minutes from",r.t(null,void 0,l(22476))),U=new n.TranslatedString("minutes to",r.t(null,void 0,l(73063))),O=new n.TranslatedString("hours",r.t(null,void 0,l(902359))),j=new n.TranslatedString("hours from",r.t(null,void 0,l(182267))),q=new n.TranslatedString("hours to",r.t(null,void 0,l(715600))),Z=new n.TranslatedString("days",r.t(null,void 0,l(235813))),Q=new n.TranslatedString("days from",r.t(null,void 0,l(559215))),K=new n.TranslatedString("days to",r.t(null,void 0,l(89919))),$=new n.TranslatedString("weeks",r.t(null,void 0,l(445537))),X=new n.TranslatedString("weeks from",r.t(null,void 0,l(292859))),J=new n.TranslatedString("weeks to",r.t(null,void 0,l(844127))),Y=new n.TranslatedString("months",r.t(null,void 0,l(295300))),ee=new n.TranslatedString("months from",r.t(null,void 0,l(317250))),te=new n.TranslatedString("months to",r.t(null,void 0,l(702828))),le=(new n.TranslatedString("ranges","ranges"),[1,59]),re=[1,59],ne=[1,24],oe=[1,366],ie=[1,52],se=[1,12];function ae(e,t,l){const r=[];if((0,a.isTicksEnabled)()){const n=(0,o.createCheckablePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.ticks,d.format({title:l}))},{id:"IntervalsVisibilitiesTicks",title:I});r.push(n)}if((0,s.isSecondsEnabled)()){const n=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.seconds,h.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.secondsFrom,u.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.secondsTo,m.format({title:l}))},{id:"IntervalsVisibilitiesSecond",title:V,min:new i.WatchedValue(le[0]),max:new i.WatchedValue(le[1])});r.push(n)}const n=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.minutes,y.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.minutesFrom,v.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.minutesTo,g.format({title:l}))},{id:"IntervalsVisibilitiesMinutes",title:R,min:new i.WatchedValue(re[0]),max:new i.WatchedValue(re[1])}),c=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.hours,b.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.hoursFrom,w.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.hoursTo,f.format({title:l}))},{id:"IntervalsVisibilitiesHours",title:D,min:new i.WatchedValue(ne[0]),max:new i.WatchedValue(ne[1])}),p=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.days,P.format({title:l})),from:(0,
o.convertToDefinitionProperty)(e,t.daysFrom,C.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.daysTo,S.format({title:l}))},{id:"IntervalsVisibilitiesDays",title:M,min:new i.WatchedValue(oe[0]),max:new i.WatchedValue(oe[1])});r.push(n,c,p);const B=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.weeks,T.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.weeksFrom,E.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.weeksTo,_.format({title:l}))},{id:"IntervalsVisibilitiesWeeks",title:N,min:new i.WatchedValue(ie[0]),max:new i.WatchedValue(ie[1])}),A=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.months,k.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.monthsFrom,x.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.monthsTo,L.format({title:l}))},{id:"IntervalsVisibilitiesMonths",title:W,min:new i.WatchedValue(se[0]),max:new i.WatchedValue(se[1])});return r.push(B,A),{definitions:r}}function ce(e,t){const l=[];if((0,a.isTicksEnabled)()){const r=(0,o.createCheckablePropertyDefinition)({checked:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.ticks),B,t)},{id:"IntervalsVisibilitiesTicks",title:I});l.push(r)}if((0,s.isSecondsEnabled)()){const r=(0,o.createRangePropertyDefinition)({checked:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.seconds),A,t),from:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.secondsFrom),z,t),to:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.secondsTo),F,t)},{id:"IntervalsVisibilitiesSecond",title:V,min:new i.WatchedValue(le[0]),max:new i.WatchedValue(le[1])});l.push(r)}const r=(0,o.createRangePropertyDefinition)({checked:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.minutes),H,t),from:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.minutesFrom),G,t),to:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.minutesTo),U,t)},{id:"IntervalsVisibilitiesMinutes",title:R,min:new i.WatchedValue(re[0]),max:new i.WatchedValue(re[1])}),n=(0,o.createRangePropertyDefinition)({checked:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.hours),O,t),from:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.hoursFrom),j,t),to:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.hoursTo),q,t)},{id:"IntervalsVisibilitiesHours",title:D,min:new i.WatchedValue(ne[0]),max:new i.WatchedValue(ne[1])}),d=(0,o.createRangePropertyDefinition)({checked:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.days),Z,t),from:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.daysFrom),Q,t),to:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.daysTo),K,t)},{id:"IntervalsVisibilitiesDays",title:M,min:new i.WatchedValue(oe[0]),max:new i.WatchedValue(oe[1])});l.push(r,n,d);const h=(0,o.createRangePropertyDefinition)({
checked:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.weeks),$,t),from:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.weeksFrom),X,t),to:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.weeksTo),J,t)},{id:"IntervalsVisibilitiesWeeks",title:N,min:new i.WatchedValue(ie[0]),max:new i.WatchedValue(ie[1])}),u=(0,o.createRangePropertyDefinition)({checked:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.months),Y,t),from:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.monthsFrom),ee,t),to:new p.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.monthsTo),te,t)},{id:"IntervalsVisibilitiesMonths",title:W,min:new i.WatchedValue(se[0]),max:new i.WatchedValue(se[1])});return l.push(h,u),{definitions:l}}},622860:(e,t,l)=>{"use strict";l.d(t,{CollectiblePropertyUndoWrapper:()=>a});var r=l(650151),n=l(609838),o=l(272047),i=l(571772);const s=new o.TranslatedString("change {propertyName} property",n.t(null,void 0,l(925167)));class a extends i.Property{constructor(e,t,l){super(),this._isProcess=!1,this._listenersMappers=[],this._valueApplier={applyValue:(e,t)=>{this._propertyApplier.setProperty(e,t,s)}},this._baseProperty=e,this._propertyApplier=l,this._propertyName=t}destroy(){this._baseProperty.destroy(),super.destroy()}value(){return this._baseProperty.value()}setValue(e,t){this._propertyApplier.beginUndoMacro(s.format({propertyName:this._propertyName})),this._isProcess=!0,this._baseProperty.setValue(e,void 0,this._valueApplier),this._isProcess=!1,this._propertyApplier.endUndoMacro(),this._listenersMappers.forEach((e=>{e.method.call(e.obj,this,"")}))}subscribe(e,t){const l=()=>{this._isProcess||t.call(e,this,"")};this._listenersMappers.push({obj:e,method:t,callback:l}),this._baseProperty.subscribe(e,l)}unsubscribe(e,t){const l=(0,r.ensureDefined)(this._listenersMappers.find((l=>l.obj===e&&l.method===t))?.callback);this._baseProperty.unsubscribe(e,l)}unsubscribeAll(e){this._baseProperty.unsubscribeAll(e)}}},890410:(e,t,l)=>{"use strict";l.d(t,{StudyPlotVisibleProperty:()=>o});var r=l(514538),n=l(631445);class o extends n.PropertyBase{constructor(e){super(),this._displayProperty=e,this._displayProperty.subscribe(this,this._displayPropertyValueChanged)}destroy(){this._displayProperty.unsubscribe(this,this._displayPropertyValueChanged),this._listeners.destroy()}value(){return 0!==this._displayProperty.value()}setValue(e,t){this._displayProperty.setValue(e?15:0)}setValueSilently(e){this._displayProperty.setValueSilently(e?15:0)}storeStateIfUndefined(){return!1}weakReference(){return(0,r.weakReference)(this)}ownership(){return(0,r.ownership)(this)}_displayPropertyValueChanged(){this.fireChanged()}}},669151:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 21l7.424-6.114a.5.5 0 0 0-.318-.886H18.5V7h-9v7H6.894a.5.5 0 0 0-.318.886L14 21z"/></svg>'},167211:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 7l7.424 6.114a.5.5 0 0 1-.318.886H18.5v7h-9v-7H6.894a.5.5 0 0 1-.318-.886L14 7z"/></svg>'},583786:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><circle stroke="currentColor" cx="14" cy="14" r="6.5"/></svg>'},250858:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 14.5h11M14.5 20V9"/></svg>'},713201:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14.354 6.646L14 6.293l-.354.353-7 7-.353.354.353.354 7 7 .354.353.354-.353 7-7 .353-.354-.353-.354-7-7z"/></svg>'},659058:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8.5 22v-5.5m0 0v-8L12 7l4 2.5 3.5-1v8l-3.5 1-4-2.5-3.5 1.5z"/></svg>'},808537:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 8.5h-.5v9.707l.146.147 3 3 .354.353.354-.353 3-3 .146-.147V8.5H11z"/></svg>'},202309:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 18.5h-.5V8.793l.146-.147 3-3L14 5.293l.354.353 3 3 .146.147V18.5H11z"/></svg>'},778240:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M7.5 7.5h13v13h-13z"/></svg>'},241683:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 11.265l.478-.765H8.098l.478.765 5 8 .424.678.424-.678 5-8z"/></svg>'},663798:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 16.735l.478.765H8.098l.478-.765 5-8L14 8.057l.424.678 5 8z"/></svg>'},923223:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 9l11 11M9 20L20 9"/></svg>'},193976:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M13 11.5l-1.915-1.532a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82V18.5a1 1 0 0 0 1 1H13m3.5-7l4.293-4.293c.63-.63 1.707-.184 1.707.707V18.5a1 1 0 0 1-1 1H16"/><path fill="currentColor" d="M14 6h1v2h-1zM14 11h1v2h-1zM14 16h1v2h-1zM14 21h1v2h-1z"/></svg>'},591512:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 13.52v4.98a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1V8.914c0-.89-1.077-1.337-1.707-.707l-4.66 4.66a1 1 0 0 1-1.332.074l-3.716-2.973a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82z"/></svg>'},321579:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M10.5 13a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM16.5 19a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM22.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/></svg>'},772914:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 12.5v8h3v-8h-3zM12.5 7.5v13h3v-13h-3zM18.5 15.5v5h3v-5h-3z"/></svg>'},98450:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M17 8.5h7M20.5 12V5M10 19.5h7M13.5 23v-7M3 12.5h7M6.5 16V9"/></svg>'},718621:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4.5 20v-7m3 7V10m3 10V8m3 12V10m3 10v-8m3 8V10m3 10V8"/></svg>'},718819:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l5-5a1.414 1.414 0 0 1 2 0m11-1l-5 5a1.414 1.414 0 0 1-2 0"/><path fill="currentColor" d="M14 5h1v2h-1zM14 10h1v2h-1zM14 15h1v2h-1zM14 20h1v2h-1z"/></svg>'},294152:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>'},646464:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 3h1v2h-1V3Zm1 5h-1v2h1V8Zm-1 5h1v2h-1v-2Zm0 5h1v2h-1v-2Zm0 5h1v2h-1v-2ZM10 5h2V4H9v18H6v-5H5v6h5V5Zm11 16h1V7h-5v10h1V8h3v13Z"/></svg>'},396298:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M9.8 2.7l.7-.7.7.7 2.1 2.1.2.2H18v9.5l.2.2 2.1 2.1.2.2H24v1h-3.5l-.2.2-2.1 2.1-.7.7-.7-.7-2.1-2.1-.7-.7.7-.7 2.1-2.1.2-.2V6h-3.5l-.2.2-2.1 2.1-.2.2V24H5.5v-1H10V8.5l-.2-.2-2.1-2.1-.7-.7.7-.7 2.1-2.1zM8.4 5.5l2.09 2.09 2.09-2.09-2.09-2.09L8.41 5.5zm9.09 14.09l-2.09-2.09 2.09-2.09 2.09 2.09-2.09 2.09z"/></svg>'},614643:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 17v5.5h4v-18h4v12h4v-9h4V21"/></svg>'}}]);