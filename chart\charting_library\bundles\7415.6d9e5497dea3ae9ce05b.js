(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7415],{243478:e=>{e.exports={title:"title-uNZ8yW1y",withoutIcon:"withoutIcon-uNZ8yW1y",buttons:"buttons-uNZ8yW1y",button:"button-uNZ8yW1y",disabled:"disabled-uNZ8yW1y",spacing:"spacing-uNZ8yW1y",toolbar:"toolbar-uNZ8yW1y"}},806746:e=>{e.exports={wrap:"wrap-C8ln3wvp",dialog:"dialog-C8ln3wvp",mobile:"mobile-C8ln3wvp",offset:"offset-C8ln3wvp",title:"title-C8ln3wvp",main:"main-C8ln3wvp",disabled:"disabled-C8ln3wvp",icon:"icon-C8ln3wvp",pathIcon:"pathIcon-C8ln3wvp",syncIconWrap:"syncIconWrap-C8ln3wvp",syncIcon:"syncIcon-C8ln3wvp",rightButtons:"rightButtons-C8ln3wvp",hover:"hover-C8ln3wvp",expandHandle:"expandHandle-C8ln3wvp",button:"button-C8ln3wvp",selected:"selected-C8ln3wvp",childOfSelected:"childOfSelected-C8ln3wvp",renameInput:"renameInput-C8ln3wvp",warn:"warn-C8ln3wvp",visible:"visible-C8ln3wvp"}},442905:e=>{e.exports={wrap:"wrap-ukH4sVzT",space:"space-ukH4sVzT",tree:"tree-ukH4sVzT"}},979377:(e,t,n)=>{"use strict";n.d(t,{formatTime:()=>h,isValidTimeOptionsDateStyle:()=>d,isValidTimeOptionsRange:()=>u});const o={calendar:"gregory",numberingSystem:"latn",hour12:!1},i={year:"numeric",month:"short",day:"numeric"},s={year:"numeric",month:"2-digit",day:"2-digit"},r={hour:"2-digit",minute:"2-digit",second:"2-digit"},l={timeZoneName:"shortOffset",weekday:"short"},a={year:0,month:1,day:2,hour:3,minute:4,second:5};const c=["year","month","day","hour","minute","second"];function u(e){return c.includes(e)}function d(e){return"numeric"===e||"short"===e}function h(e,t,n="year",c="day",u){const d=function(e="year",t="day",n={}){[e,t]=a[t]>a[e]?[e,t]:[t,e];const c={..."numeric"===n.dateStyle?s:i,...r},u=n.fractionalSecondDigits,d={...o,fractionalSecondDigits:void 0===u?void 0:Math.floor(Math.min(Math.max(1,u),3)),timeZone:n.timeZone,weekday:n.weekday?l.weekday:void 0,timeZoneName:n.timeZoneName?l.timeZoneName:void 0};return Object.keys(c).forEach((n=>{a[n]>=a[e]&&a[n]<=a[t]&&(d[n]=c[n])})),d}(n,c,u),h=new Intl.DateTimeFormat(t,d),g=new Date(e);return h.format(g)}},887859:(e,t,n)=>{"use strict";n.d(t,{getLocaleIso:()=>r});var o=n(650151)
;const i=JSON.parse('{"ar_AE":{"language":"ar","language_name":"العربية","flag":"sa","geoip_code":"sa","countries_with_this_language":["ae","bh","dj","dz","eg","er","iq","jo","km","kw","lb","ly","ma","mr","om","qa","sa","sd","so","sy","td","tn","ye"],"priority":500,"dir":"rtl","iso":"ar","iso_639_3":"arb","show_on_widgets":true,"global_name":"Arabic"},"br":{"language":"pt","language_name":"Português","flag":"br","geoip_code":"br","priority":650,"iso":"pt","iso_639_3":"por","show_on_widgets":true,"global_name":"Portuguese"},"ca_ES":{"language":"ca_ES","language_name":"Català","flag":"es","geoip_code":"es","priority":745,"iso":"ca","iso_639_3":"cat","disabled":true,"show_on_widgets":true,"global_name":"Catalan"},"cs":{"language":"cs","language_name":"Czech","flag":"cz","geoip_code":"cz","priority":718,"iso":"cs","iso_639_3":"ces","show_on_widgets":true,"global_name":"Czech","is_in_european_union":true,"isBattle":true},"de_DE":{"language":"de","language_name":"Deutsch","flag":"de","geoip_code":"de","countries_with_this_language":["at","ch"],"priority":756,"iso":"de","iso_639_3":"deu","show_on_widgets":true,"global_name":"German","is_in_european_union":true},"en":{"language":"en","language_name":"English","flag":"us","geoip_code":"us","priority":1000,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"English","is_only_recommended_tw_autorepost":true},"es":{"language":"es","language_name":"Español","flag":"es","geoip_code":"es","countries_with_this_language":["mx","ar","ve","cl","co","pe","uy","py","cr","gt","c","bo","pa","pr"],"priority":744,"iso":"es","iso_639_3":"spa","show_on_widgets":true,"global_name":"Spanish","is_in_european_union":true},"fr":{"language":"fr","language_name":"Français","flag":"fr","geoip_code":"fr","priority":750,"iso":"fr","iso_639_3":"fra","show_on_widgets":true,"global_name":"French","is_in_european_union":true},"he_IL":{"language":"he_IL","language_name":"עברית","flag":"il","geoip_code":"il","priority":490,"dir":"rtl","iso":"he","iso_639_3":"heb","show_on_widgets":true,"global_name":"Israeli"},"hu_HU":{"language":"hu_HU","language_name":"Magyar","flag":"hu","geoip_code":"hu","priority":724,"iso":"hu","iso_639_3":"hun","show_on_widgets":true,"global_name":"Hungarian","is_in_european_union":true,"disabled":true},"id":{"language":"id_ID","language_name":"Bahasa Indonesia","flag":"id","geoip_code":"id","priority":648,"iso":"id","iso_639_3":"ind","show_on_widgets":true,"global_name":"Indonesian"},"in":{"language":"en","language_name":"English ‎(India)‎","flag":"in","geoip_code":"in","priority":800,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"Indian"},"it":{"language":"it","language_name":"Italiano","flag":"it","geoip_code":"it","priority":737,"iso":"it","iso_639_3":"ita","show_on_widgets":true,"global_name":"Italian","is_in_european_union":true},"ja":{"language":"ja","language_name":"日本語","flag":"jp","geoip_code":"jp","priority":600,"iso":"ja","iso_639_3":"jpn","show_on_widgets":true,"global_name":"Japanese"},"kr":{"language":"ko","language_name":"한국어","flag":"kr","geoip_code":"kr","priority":550,"iso":"ko","iso_639_3":"kor","show_on_widgets":true,"global_name":"Korean"},"ms_MY":{"language":"ms_MY","language_name":"Bahasa Melayu","flag":"my","geoip_code":"my","priority":647,"iso":"ms","iso_639_3":"zlm","show_on_widgets":true,"global_name":"Malaysian"},"pl":{"language":"pl","language_name":"Polski","flag":"pl","geoip_code":"pl","priority":725,"iso":"pl","iso_639_3":"pol","show_on_widgets":true,"global_name":"Polish","is_in_european_union":true},"ru":{"language":"ru","language_name":"Русский","flag":"ru","geoip_code":"ru","countries_with_this_language":["am","by","kg","kz","md","tj","tm","uz"],"priority":700,"iso":"ru","iso_639_3":"rus","show_on_widgets":true,"global_name":"Russian","is_only_recommended_tw_autorepost":true},"sv_SE":{"language":"sv","language_name":"Svenska","flag":"se","geoip_code":"se","priority":723,"iso":"sv","iso_639_3":"swe","show_on_widgets":true,"global_name":"Swedish","is_in_european_union":true,"disabled":true},"th_TH":{"language":"th","language_name":"ภาษาไทย","flag":"th","geoip_code":"th","priority":646,"iso":"th","iso_639_3":"tha","show_on_widgets":true,"global_name":"Thai"},"tr":{"language":"tr","language_name":"Türkçe","flag":"tr","geoip_code":"tr","priority":713,"iso":"tr","iso_639_3":"tur","show_on_widgets":true,"global_name":"Turkish","is_only_recommended_tw_autorepost":true},"vi_VN":{"language":"vi","language_name":"Tiếng Việt","flag":"vn","geoip_code":"vn","priority":645,"iso":"vi","iso_639_3":"vie","show_on_widgets":true,"global_name":"Vietnamese"},"zh_CN":{"language":"zh","language_name":"简体中文","flag":"cn","geoip_code":"cn","countries_with_this_language":["zh"],"priority":537,"iso":"zh-Hans","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Chinese"},"zh_TW":{"language":"zh_TW","language_name":"繁體中文","flag":"tw","geoip_code":"tw","countries_with_this_language":["hk"],"priority":536,"iso":"zh-Hant","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Taiwanese"},"el":{"language":"el","language_name":"Greek","flag":"gr","geoip_code":"gr","priority":625,"iso":"el","iso_639_3":"ell","global_name":"Greek","is_in_european_union":true,"isBattle":true},"nl_NL":{"language":"nl_NL","language_name":"Dutch","flag":"nl","geoip_code":"nl","priority":731,"iso":"nl","iso_639_3":"nld","global_name":"Dutch","is_in_european_union":true,"isBattle":true},"ro":{"language":"ro","language_name":"Romanian","flag":"ro","geoip_code":"ro","priority":707,"iso":"ro","iso_639_3":"ron","global_name":"Romanian","is_in_european_union":true,"isBattle":true}}'),s=function(){
const e=document.querySelectorAll("link[rel~=link-locale][data-locale]");if(0===e.length)return i;const t={};return e.forEach((e=>{const n=(0,o.ensureNotNull)(e.getAttribute("data-locale"));t[n]={...i[n],href:e.href}})),t}();function r(e){return e=e||window.locale,s[e]?.iso}},106690:(e,t,n)=>{"use strict";n.d(t,{ObjectTreeNodeRendererContext:()=>o});const o=n(50959).createContext(null)},420779:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Component:()=>D,NodeRenderer:()=>L});var o=n(50959),i=n(497754),s=n(650151),r=n(878112),l=n(440891),a=n(609838),c=n(535842),u=n(693838),d=n(689376),h=n(654936),g=n(409174),_=n(180185),p=n(106690),m=n(800296),f=n(297265),b=n(888740),v=n(571956),S=n(206672),y=n(582e3),w=n(951827),T=n(69111),C=n(460925),I=n(52870),M=n(749756),A=n(94007),k=n(962766),E=n(806746);const N=l.enabled("saveload_separate_drawings_storage");function L(e){const{id:t}=e,n=(0,o.useContext)(u.ObjectTreeContext),{viewModel:i}=(0,s.ensureNotNull)(n),r=i.entity(t);return null===r?null:o.createElement(D,{...e,entity:r})}const O=500,B={0:void 0,1:S,2:y};function D(e){const{id:t,isOffset:S,isDisabled:y,isSelected:L,isChildOfSelected:D,isHovered:j,parentId:x,entity:z,isExpanded:G}=e,P=(0,o.useContext)(u.ObjectTreeContext),{viewModel:V}=(0,s.ensureNotNull)(P),F=(0,o.useContext)(p.ObjectTreeNodeRendererContext),{size:R}=(0,o.useContext)(c.SizeContext),[H,W]=(0,o.useState)(!1),U=(0,o.useRef)(null),[Z,K]=(0,o.useState)(z.title().value()),[$,q]=(0,o.useState)((()=>z.getIcon())),[J,Y]=(0,o.useState)(z.isLocked()),[X,Q]=(0,o.useState)(z.isVisible()),[ee,te]=(0,o.useState)(z.isActualInterval()),[ne,oe]=(0,o.useState)(z.getDrawingSyncState()),[ie,se]=(0,o.useState)(z.getModificationDate),[re,le]=(0,o.useState)(!1),ae=(0,f.useWatchedValueReadonly)({watchedValue:V.getChartLayout()}),[ce,ue]=(0,o.useState)(!1),de=(0,o.useRef)(null);(0,o.useEffect)((()=>{const e={};z.onLockChanged().subscribe(e,(()=>Y(z.isLocked()))),z.onVisibilityChanged().subscribe(e,(()=>Q(z.isVisible())));const t=z.title().spawn();t.subscribe((e=>K(e))),z.onIsActualIntervalChange().subscribe(e,(()=>te(z.isActualInterval()))),z.onSyncStateChanged().subscribe(e,(()=>oe(z.getDrawingSyncState()))),z.modificationDateChanged().subscribe(e,(()=>se(z.getModificationDate())));const n=z.onIconChanged?z.onIconChanged():void 0;return n&&n.subscribe(e,(()=>q(z.getIcon()))),()=>{z.onIsActualIntervalChange().unsubscribeAll(e),z.onLockChanged().unsubscribeAll(e),z.onVisibilityChanged().unsubscribeAll(e),z.onSyncStateChanged().unsubscribeAll(e),z.modificationDateChanged().unsubscribeAll(e),t.destroy(),de.current&&clearTimeout(de.current),n&&n.unsubscribeAll(e)}}),[z]),(0,o.useEffect)((()=>{H&&U.current&&(U.current.focus(),U.current.setSelectionRange(0,Z.length))}),[H]),(0,o.useEffect)((()=>{const e={};return V.hoveredObjectChanged().subscribe(e,Ce),()=>{V.hoveredObjectChanged().unsubscribeAll(e)}}),[G]),(0,o.useEffect)((()=>{V.setHoveredObject(j?t:null)}),[j]),(0,o.useEffect)((()=>{!L&&de.current&&(clearTimeout(de.current),de.current=null),W(!1)}),[L]);const he={};if(x){const e=V.entity(x)
;e&&(he["data-parent-name"]=e.title().value()),he["data-type"]=z.hasChildren()?"group":"data-source"}const ge=l.enabled("test_show_object_tree_debug")?`<${z.id()}> (${z.zOrder()}) ${z.title()}`:z.title().value(),_e=null!==ne?B[ne]:void 0,pe=j||re,me=H&&L,fe=!!F&&F.isTouch,be=!!F&&F.isDialog,ve=ee&&X?A:k,Se=z.hasChildren()?a.t(null,void 0,n(440983)):a.t(null,void 0,n(380014));let ye=null;return $&&$.type===d.IconType.Svg&&(ye=o.createElement(r.Icon,{icon:$.content||"",className:E.icon})),o.createElement("span",{className:i(E.wrap,y&&E.disabled,L&&E.selected,S&&E.offset,D&&E.childOfSelected,re&&!y&&!L&&!D&&E.hover,be&&!y&&!L&&!D&&E.dialog,be&&!y&&!L&&!D&&(0,T.isOnMobileAppPage)("any")&&E.mobile,!!ie&&["apply-common-tooltip","common-tooltip-vertical"]),onMouseDown:function(e){H&&!(0,s.ensureNotNull)(U.current).contains(e.target)&&ue(!0)},onClick:1===R?we:function(e){if(e.defaultPrevented)return;if(0!==(0,_.modifiersFromEvent)(e))return;if(de.current)e.preventDefault(),clearTimeout(de.current),de.current=null,V.openProperties(z),ue(!1);else{const e=V.selection().selected();de.current=setTimeout((()=>{de.current=null,L&&!ce&&1===e.length&&V.rename(z,(()=>W(!0))),ue(!1)}),O)}},onContextMenu:0===R?we:void 0,"data-tooltip":ie?a.t(null,{replace:{modificationDate:ie}},n(963944)):void 0},!me&&o.createElement(o.Fragment,null,ye,_e&&(N||(0,v.isMultipleLayout)(ae))&&o.createElement("div",{className:E.syncIconWrap},o.createElement(r.Icon,{icon:_e,className:E.syncIcon})),o.createElement("span",{className:i(E.title,V.isMain(z)&&E.main,(!z.isVisible()||!ee)&&E.disabled),...he},ge),o.createElement("span",{className:E.rightButtons},z.canBeLocked()&&o.createElement(m.ListItemButton,{title:J?b.unlockTitle:b.lockTitle,icon:J?I:M,className:i(E.button,(pe||J)&&E.visible,"apply-common-tooltip"),onClick:function(e){if(e.defaultPrevented)return;e.preventDefault(),V.setIsLocked(t,!z.isLocked())},"data-role":"button","data-name":"lock","data-active":J}),o.createElement(m.ListItemButton,{icon:ve,className:i(E.button,!ee&&E.warn,(pe||!X||!ee)&&E.visible,"apply-common-tooltip"),onClick:ee?function(e){if(e.defaultPrevented)return;e.preventDefault(),V.setIsVisible(t,!z.isVisible())}:function(e){if(e.defaultPrevented)return;e.preventDefault(),V.openProperties(z,w.TabNames.visibility)},title:function(){if(!ee)return Se;return X?b.hideTitle:b.showTitle}(),"data-role":"button","data-name":"hide","data-active":!X}),z.canBeRemoved()&&o.createElement(m.ListItemButton,{title:b.removeTitle,icon:C,className:i(E.button,(fe||pe)&&E.visible,"apply-common-tooltip"),onClick:function(e){if(e.defaultPrevented)return;e.preventDefault(),e.stopPropagation(),V.remove(t)},"data-role":"button","data-name":"remove"}))),me&&o.createElement(h.InputControl,{value:Z,onChange:function(e){K(e.currentTarget.value)},onClick:g.preventDefault,className:E.renameInput,onKeyDown:function(e){27===(0,_.hashFromEvent)(e)?(e.preventDefault(),K(z.title().value()),W(!1)):13===(0,_.hashFromEvent)(e)&&(e.preventDefault(),Te())},reference:function(e){U.current=e},onBlur:Te,
onDragStart:function(e){e.preventDefault(),e.stopPropagation()},draggable:!0,stretch:!0}));function we(e){e.defaultPrevented||H||!z.fullyConstructed()||(e.preventDefault(),e.persist(),V.openContextMenu(z,(()=>W(!0)),e))}function Te(){""!==Z&&z.setName(Z),K(z.title().value()),W(!1)}function Ce(e){if(z.hasChildren()&&!G){const t=null!==e&&z.childrenIds().has(e);le(t)}else le(t===e)}}},993321:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ObjectTree:()=>F});var o=n(50959),i=n(129885),s=n(972535),r=n(180185),l=n(650151),a=n(497754),c=n(609838),u=n(878112),d=n(46305),h=n(843833),g=n(585938),_=n(693838),p=n(939157),m=n(602069),f=n(442092),b=n(636296),v=n(274059),S=n(80465),y=n(243478);n(32133);function w(e){const{hideTitle:t}=e,{viewModel:i}=(0,l.ensureNotNull)((0,o.useContext)(_.ObjectTreeContext)),s=(0,g.useForceUpdate)(),r=i.selection();(0,o.useEffect)((()=>{const e={};return i.onChange().subscribe(e,(()=>s())),()=>{i.onChange().unsubscribeAll(e)}}),[i]),(0,o.useEffect)((()=>{const e={};return r.onChange().subscribe(e,(()=>s())),()=>{r.onChange().unsubscribeAll(e)}}),[r]),(0,o.useEffect)((()=>{(0,f.updateTabIndexes)()}),[]);const w=!i.canSelectionBeUnmerged(),T=i.isSelectionCopiable(),C=i.isSelectionCloneable(),I=!T&&!C,M=i.canSelectionBeGrouped(),A=!1;return o.createElement(m.Toolbar,{orientation:"horizontal",className:y.toolbar},!t&&o.createElement("div",{className:a(y.title,y.withoutIcon)},c.t(null,void 0,n(888616)),A),o.createElement("div",{className:y.buttons},o.createElement(p.ToolbarIconButton,{className:a(y.button,!M&&y.disabled),icon:S,onClick:function(){i.createGroupFromSelection()},isDisabled:!M,tooltip:c.t(null,void 0,n(183390)),"data-name":"group-button"}),o.createElement(h.ToolbarMenuButton,{className:a(y.button,I&&y.disabled),isDisabled:I,content:o.createElement(u.Icon,{icon:b}),tooltip:c.t(null,void 0,n(792389)),arrow:!1,isShowTooltip:!0,"data-name":"copy-clone-button"},T&&o.createElement(d.AccessibleMenuItem,{"data-name":"copy",label:c.t(null,void 0,n(249680)),onClick:function(){i.copySelection()}}),C&&o.createElement(d.AccessibleMenuItem,{"data-name":"clone",label:c.t(null,void 0,n(712537)),onClick:function(){i.cloneSelection()}})),o.createElement(h.ToolbarMenuButton,{className:a(y.button,w&&y.disabled),isDisabled:w,content:o.createElement(u.Icon,{icon:v}),tooltip:c.t(null,void 0,n(635049)),arrow:!1,isShowTooltip:!0,"data-name":"move-to-button"},o.createElement(d.AccessibleMenuItem,{"aria-label":c.t(null,void 0,n(715512)),"data-name":"new-pane-above",label:c.t(null,void 0,n(715512)),onClick:function(){i.unmergeSelectionUp()}}),o.createElement(d.AccessibleMenuItem,{"aria-label":c.t(null,void 0,n(752160)),"data-name":"new-pane-below",label:c.t(null,void 0,n(752160)),onClick:function(){i.unmergeSelectionDown()}})),t&&o.createElement(o.Fragment,null,o.createElement("div",{className:y.spacing}),o.createElement(p.ToolbarIconButton,{className:y.button,icon:manageDrawingsIcon,tooltip:c.t(null,void 0,n(281031)),"data-name":"manage-drawings-button",onClick:k}))));function k(){A}}
var T=n(733393),C=n(746212),I=n(849392),M=n(451539),A=n(471505),k=n(289789),E=n(129881);function N(e){return(0,C.eventChannel)((t=>{const n={};return e.onChange().subscribe(n,(()=>t((0,I.resetTree)()))),e.onGroupCreated().subscribe(n,(e=>t((0,I.setIsExpanded)(e,!0)))),e.selection().onChange().subscribe(n,(e=>t((0,I.setSelectedIds)(e)))),()=>{e.onChange().unsubscribeAll(n),e.selection().onChange().unsubscribeAll(n),e.onGroupCreated().unsubscribeAll(n)}}),C.buffers.expanding())}function*L(){for(;;)yield(0,i.take)([A.SELECT_NEXT,A.SELECT_PREVIOUS]),(0,k.trackObjectTreeEvent)("Select","Arrow")}function*O(){for(;;){const{mode:e}=yield(0,i.take)(A.SET_IS_SELECTED);1===e&&(0,k.trackObjectTreeEvent)("Multi select","Ctrl"),2===e&&(0,k.trackObjectTreeEvent)("Multi select","Shift")}}function*B(e){for(;;){yield(0,i.take)(A.DROP_SELECTION);const{node:t,dropType:n}=(0,E.dropTargetSelector)(yield(0,i.select)());if(t){const o=(0,E.selectedNodesSelector)(yield(0,i.select)()),s=o.map((t=>(0,l.ensureNotNull)(e.entity(t.id))));let r="Drag";1===t.level&&"inside"!==n&&o.some((e=>2===e.level))?r="From the group":2!==t.level&&"inside"!==n||!o.some((e=>1===e.level))?1===o.length&&o[0].parentId!==t.parentId&&(r="Existing pane"):r="To the group",(0,k.trackObjectTreeEvent)(r,(0,k.getGaAction)(s))}}}function*D(e){yield(0,i.fork)(L),yield(0,i.fork)(O),yield(0,i.fork)(B,e)}function*j(e){yield(0,i.fork)(D,e);const t=yield(0,i.call)(N,e);M.logger.logNormal("Opened object tree data source channel");try{for(;;){const e=yield(0,i.take)(t);yield(0,i.put)(e)}}finally{M.logger.logNormal("Closed object tree data source channel"),t.close()}}var x=n(106690),z=n(409174),G=n(330344),P=n(442905);const V=s.mobiletouch?"touch":"native";function F(e){const{viewModel:t,showHeader:n=!0,nodeRenderer:l,isDialog:a=!1,hideHeaderTitle:c=!1}=e,u=(0,o.useRef)(null),d=function(e){const[t,n]=(0,o.useState)(e.getChartId()),i=(0,o.useRef)(t);return i.current=t,(0,o.useEffect)((()=>{return e.onChange().subscribe(null,t),()=>{e.onChange().unsubscribe(null,t)};function t(){const t=e.getChartId();i.current!==t&&n(t)}}),[]),t}(t),[h,g]=(0,G.useDimensions)(),[p,m]=(0,o.useState)(null),f=(0,o.useMemo)((()=>({isTouch:s.touch,isDialog:a})),[a]);return o.createElement(x.ObjectTreeNodeRendererContext.Provider,{value:f},o.createElement(_.ObjectTreeContext.Provider,{value:{viewModel:t}},o.createElement("div",{className:P.wrap,onContextMenu:z.preventDefaultForContextMenu},n&&o.createElement(w,{hideTitle:c}),o.createElement("div",{className:P.space,onClick:function(e){if(e.defaultPrevented)return;if(!(e.target instanceof Element)||null===u.current)return;e.target===u.current&&t.selection().set([])},ref:h},null!==g&&o.createElement(T.Tree,{key:d,height:g.height,width:g.width,canBeAddedToSelection:function(e){const n=t.entity(e);return t.selection().canBeAddedToSelection(n)},nodeRenderer:l,initState:function(){const{nodes:e,selection:n}=t.getState();return{selectedIds:n,nodes:e}},canMove:function(e,n,o){return t.isSelectionDropable(n.id,o)},drag:V,rowHeight:R,onSelect:function(e){
const n=e.map((e=>t.entity(e))).filter((e=>null!==e));t.selection().set(n)},onDrop:function(e){e.preventDefault();const{detail:{target:n,type:o}}=e;t.insertSelection(n,o)},scrollToId:p,saga:function*(){yield(0,i.fork)(j,t)},onKeyboardSelect:function(e){m({id:e})},outerRef:function(e){u.current=e},onKeyDown:function(e){if(13===(0,r.hashFromEvent)(e)){e.preventDefault();const n=t.selection().selected(),o=n.length>0?t.entity(n[0]):void 0;o&&t.openProperties(o)}},autofocus:a})))))}function R(e,t){switch(t.type){case"node":return 38;case"separator":return 13}}},888740:(e,t,n)=>{"use strict";n.d(t,{createGroupTitle:()=>i,hideTitle:()=>a,lockTitle:()=>l,removeTitle:()=>u,renameTitle:()=>s,showTitle:()=>c,unlockTitle:()=>r});var o=n(609838);const i=o.t(null,void 0,n(183390)),s=o.t(null,void 0,n(606321)),r=o.t(null,void 0,n(999894)),l=o.t(null,void 0,n(651077)),a=o.t(null,void 0,n(327298)),c=o.t(null,void 0,n(698334)),u=o.t(null,void 0,n(767410))},289789:(e,t,n)=>{"use strict";n.d(t,{getGaAction:()=>s,trackObjectTreeEvent:()=>i});var o=n(32133);function i(e,t){(0,o.trackEvent)("Object Tree",e,t)}function s(e){return e.length>1?"Multi select":e[0].gaLabel()}},69649:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ObjectTree:()=>ve,logger:()=>de});var o=n(650151),i=n(329452);var s=n(989348);function r(e,t){return`${e}:${t}`}function l(e){const t=e.split(":");return{persistentId:t[0],instanceId:t[1]}}var a=n(41899);class c{constructor(e){this._onChange=new i.Delegate,this._recalculate=()=>{const e=this._groupModel.groups().map((e=>r(e.id,e.instanceId()))),t=this._selectionApi.allSources();this._selected=this._selected.filter((n=>e.includes(n)||t.includes(n))),this._onChange.fire(this._selected)},this._model=e,this._selectionApi=new s.SelectionApi(this._model),this._groupModel=this._model.lineToolsGroupModel(),this._selected=this._getSelectedIds(),this._selectionApi.onChanged().subscribe(this,(()=>{this._selected=this._getSelectedIds(),this._onChange.fire(this._selected)})),this._groupModel.onChanged().subscribe(this,this._recalculate)}destroy(){this._selectionApi.onChanged().unsubscribeAll(this),this._groupModel.onChanged().unsubscribeAll(this)}set(e){const t=[];let n=e.map((e=>e.id()));for(const o of e)if(o.hasChildren()){const e=o.childrenIds();t.push(...Array.from(e.values())),n=n.filter((t=>!e.has(t)))}else t.push(o.id());this._selectionApi.set(t.map((e=>l(e).persistentId))),this._selected=n,this._onChange.fire(this._selected)}canBeAddedToSelection(e){return null!==e&&e.canBeAddedToSelection()}onChange(){return this._onChange}selected(){return this._selected}_getSelectedIds(){return this._selectionApi.allSources().map((e=>this._model.dataSourceForId(e))).filter(a.notNull).filter((e=>e.showInObjectTree())).map((e=>r(e.id(),e.instanceId())))}}class u{constructor(e,t){this._controller=e,this._facade=t,this._groupModel=e.model().lineToolsGroupModel()}buildTree(){const e={};for(const t of this._controller.model().panes()){const n=t.sourcesByGroup().allWithoutMultipane().filter((e=>e.showInObjectTree()));e[t.id()]=d(t.id(),0)
;for(const n of this._groupModel.groups()){const i=r(n.id,n.instanceId()),s=(0,o.ensureNotNull)(this._facade.getObjectById(i));if(s.pane()===t){const o=[...n.lineTools()].sort(((e,t)=>e.zorder()>t.zorder()?-1:1)).map((e=>r(e.id(),e.instanceId())));e[s.id()]=d(s.id(),1,t.id(),o),e[t.id()].children.push(s.id());for(const t of o)e[t]=d(t,2,s.id())}}for(const o of n){const n=r(o.id(),o.instanceId());e[n]||(e[n]=d(n,1,t.id()),e[t.id()].children.push(n))}e[t.id()].children.sort(((e,t)=>{const n=(0,o.ensureNotNull)(this._facade.getObjectById(e)),i=(0,o.ensureNotNull)(this._facade.getObjectById(t));return(0,o.ensureNotNull)(i.zOrder())-(0,o.ensureNotNull)(n.zOrder())}))}return this._facade.invalidateCache(new Set(Object.keys(e))),e}}function d(e,t,n,o=[]){return{id:e,level:t,parentId:n,children:o}}var h=n(609838),g=n(887859),_=n(272047),p=n(440891),m=n(601227),f=n(240534),b=n(128492),v=n(278906),S=n(149575),y=n(979377),w=n(112295),T=n(769191),C=n(979910),I=n(341991),M=n(3265),A=n(539442),k=n(651407),E=n(689376),N=n(209039),L=n(607295),O=n(728824),B=n(57674);const D=new _.TranslatedString("show {title}",h.t(null,void 0,n(351382))),j=new _.TranslatedString("hide {title}",h.t(null,void 0,n(713017))),x=new _.TranslatedString("lock {title}",h.t(null,void 0,n(576104))),z=new _.TranslatedString("unlock {title}",h.t(null,void 0,n(212525))),G=new _.TranslatedString("change {sourceTitle} title to {newSourceTitle}",h.t(null,void 0,n(923687))),P=new _.TranslatedString("insert source(s) after",h.t(null,void 0,n(610370)));function V(e,t){return t.every((t=>!(t.pane()!==e&&!t.allowsMovingBetweenPanes())))}function F(e){return e instanceof T.DataSource&&e.showInObjectTree()?r(e.id(),e.instanceId()):null}function R(e){return new _.TranslatedString(e.name(),e.title(N.TitleDisplayTarget.DataWindow))}const H=new i.Delegate,W=p.enabled("saveload_separate_drawings_storage");function U(e){return 0===e?0:1===e?1:2}class Z{constructor(e,t){this._syncStateChanged=new i.Delegate,this.getModificationDate=()=>{if(!(0,b.isLineTool)(this._dataSource))return null;const e=this._dataSource.serverUpdateTime();if(null===e)return null;const t=(0,g.getLocaleIso)();return`${(0,y.formatTime)(e,t,"year","day")}\n${(0,y.formatTime)(e,t,"hour","minute",{timeZoneName:!0})}`},this._updateSyncState=()=>{this._syncStateChanged.fire((0,o.ensureNotNull)(this.getDrawingSyncState()))},this._undoModel=e,this._dataSource=t,this._isWidgetPane=e.model().paneForSource(t)?.mode()===S.PaneMode.Widget,(0,b.isLineTool)(t)?(t.linkKey().subscribe(this._updateSyncState),t.sharingMode().subscribe(this._updateSyncState),this._title=(0,I.createWVFromGetterAndSubscription)((()=>t.properties().title.value()||t.translatedType()),t.properties().title)):(0,M.isSymbolSource)(t)?this._title=(0,I.createWVFromGetterAndSubscriptions)((()=>t.symbolTitle(N.TitleDisplayTarget.DataWindow,void 0,void 0,(0,m.onWidget)()?"exchange":"listed_exchange")),[t.symbolChanged(),t.symbolResolved()]):(0,v.isStudy)(t)?this._title=(0,
I.createWVFromGetterAndSubscriptions)((()=>t.title(N.TitleDisplayTarget.DataWindow)),[t.properties().childs().inputs,this._undoModel.model().properties().childs().paneProperties.childs().legendProperties.childs().showStudyArguments,t.onParentSourcesChanges(),t.series().symbolResolved()]):this._title=new f.WatchedValue(t.title(N.TitleDisplayTarget.DataWindow)).spawn();const n=this._undoModel.lineBeingCreated();null!==n&&n===t&&n.isSynchronizable()&&k.isToolCreatingNow.subscribe(this._updateSyncState)}destroy(){(0,b.isLineTool)(this._dataSource)&&(this._dataSource.linkKey().unsubscribe(this._updateSyncState),this._dataSource.sharingMode().unsubscribe(this._updateSyncState),this._dataSource.serverUpdateTimeChanged().unsubscribeAll(this)),this._title.destroy(),k.isToolCreatingNow.unsubscribe(this._updateSyncState)}id(){return r(this._dataSource.id(),this._dataSource.instanceId())}title(){return this._title}gaLabel(){return(0,v.isStudy)(this._dataSource)?"Study":(0,b.isLineTool)(this._dataSource)?"Drawing":"Symbol"}canBeLocked(){return(0,b.isLineTool)(this._dataSource)&&this._dataSource.userEditEnabled()}canBeRemoved(){return this._undoModel.mainSeries()!==this._dataSource&&this._dataSource.isUserDeletable()}canBeHidden(){return this._dataSource.canBeHidden()}canBeRenamed(){return(0,b.isLineTool)(this._dataSource)}fullyConstructed(){return this._undoModel.lineBeingCreated()!==this._dataSource}isVisible(){return this._dataSource.properties().visible.value()}isActualInterval(){return!(0,b.isLineTool)(this._dataSource)&&!(0,v.isStudy)(this._dataSource)||this._dataSource.isActualInterval()}onIsActualIntervalChange(){return(0,b.isLineTool)(this._dataSource)||(0,v.isStudy)(this._dataSource)?this._dataSource.onIsActualIntervalChange():H}isLocked(){return!!(0,b.isLineTool)(this._dataSource)&&this._dataSource.properties().frozen.value()}onVisibilityChanged(){return this._dataSource.properties().visible.listeners()}onLockChanged(){return(0,b.isLineTool)(this._dataSource)?this._dataSource.properties().frozen.listeners():H}getIcon(){const e=(0,w.getAllSourcesIcons)(),t=this._dataSource.getSourceIcon(),n=(0,v.isStudyStrategy)(this._dataSource);let o={type:E.IconType.Svg,content:n?L:O};if(e&&t)if("loadSvg"===t.type){const[n,i]=t.svgId.split("."),s="linetool"===n?e.linetool[i]:e.series[Number(i)];o={type:E.IconType.Svg,content:s||O}}else"svgContent"===t.type&&(o={type:E.IconType.Svg,content:t.content});return o}onIconChanged(){if(this._dataSource.onSourceIconChanged)return this._dataSource.onSourceIconChanged()}setVisible(e){const t=(e?D:j).format({title:R(this._dataSource)});this._undoModel.setProperty(this._dataSource.properties().visible,e,t,C.lineToolsDoNotAffectChartInvalidation&&(0,b.isLineTool)(this._dataSource))}setLocked(e){if((0,b.isLineTool)(this._dataSource)){const t=(e?x:z).format({title:R(this._dataSource)});this._undoModel.setProperty(this._dataSource.properties().frozen,e,t,C.lineToolsDoNotAffectChartInvalidation)}}setName(e){if((0,b.isLineTool)(this._dataSource)){const t=G.format({
sourceTitle:this._dataSource.properties().title.value()||R(this._dataSource),newSourceTitle:e});this._undoModel.setProperty(this._dataSource.properties().title,e,t,C.lineToolsDoNotAffectChartInvalidation)}}isCopiable(){return this._dataSource.copiable()}isClonable(){return this._dataSource.cloneable()}zOrder(){return this._dataSource.zorder()}remove(){this._undoModel.removeSource(this._dataSource,!1)}canBeAddedToSelection(){return this._undoModel.selection().canBeAddedToSelection(this._dataSource)}setAsSelection(){this._undoModel.model().selectionMacro((e=>{e.clearSelection(),e.addSourceToSelection(this._dataSource)}))}addToSelection(){this._undoModel.model().selectionMacro((e=>{e.addSourceToSelection(this._dataSource)}))}addSourcesToArray(e){return e.push(this._dataSource),e}insertSourcesBeforeThis(e){this._insertSources(e,(e=>this._undoModel.insertBefore(e,this._dataSource)))}insertSourcesAfterThis(e){this._insertSources(e,(e=>this._undoModel.insertAfter(e,this._dataSource)))}childrenIds(){return new Set}hasChildren(){return!1}pane(){return(0,o.ensureNotNull)(this._undoModel.model().paneForSource(this._dataSource))}allowsMovingBetweenPanes(){return this._dataSource.allowsMovingBetweenPanes()}canBeAddedToGroup(){return(0,b.isLineTool)(this._dataSource)&&this._dataSource.boundToSymbol()}canInsertBeforeThis(e){return!this._isWidgetPane&&this._canInsertBeforeOrAfter(e)}canInsertAfterThis(e){return!this._isWidgetPane&&this._canInsertBeforeOrAfter(e)}detachFromParent(){if((0,b.isLineTool)(this._dataSource)){const e=this._undoModel.model(),t=this._undoModel.lineToolsGroupController(),n=e.lineToolsGroupModel().groupForLineTool(this._dataSource);null!==n&&t.excludeLineToolFromGroup(n,this._dataSource)}}canBeSyncedInLayout(){return(0,b.isLineTool)(this._dataSource)&&this._dataSource.isSynchronizable()}onSyncStateChanged(){return this._syncStateChanged}modificationDateChanged(){return(0,b.isLineTool)(this._dataSource)?this._dataSource.serverUpdateTimeChanged():H}setDrawingSyncState(e){if(!this.canBeSyncedInLayout()||!this.fullyConstructed())return;const t=this._dataSource;switch(e){case 0:if(null===t.linkKey().value())return;this._undoModel.unlinkLines([t]);break;case 1:if(null!==t.linkKey().value())return;this._undoModel.shareLineTools([t],1)}}getDrawingSyncState(){return this.canBeSyncedInLayout()?W?this.fullyConstructed()?U(this._dataSource.sharingMode().value()):0:this.fullyConstructed()&&null!==this._dataSource.linkKey().value()?1:0:null}_canInsertBeforeOrAfter(e){const t=this._undoModel.model();if(!V(this.pane(),e))return!1;if((0,b.isLineTool)(this._dataSource)){if(null!==t.lineToolsGroupModel().groupForLineTool(this._dataSource)&&e.some((e=>!e.canBeAddedToGroup())))return!1}return!0}_insertSources(e,t){const n=this._undoModel.model(),i=this._undoModel.lineToolsGroupController();this._undoModel.beginUndoMacro(P);const s=()=>{e.forEach((e=>e.detachFromParent()))},r=e.reduce(((e,t)=>t.addSourcesToArray(e)),[]);if((0,b.isLineTool)(this._dataSource)){const t=n.lineToolsGroupModel().groupForLineTool(this._dataSource)
;null!==t?((0,o.assert)(!e.some((e=>e.hasChildren()))),r.forEach((e=>{(0,b.isLineTool)(e)&&(t.containsLineTool(e)||i.addLineToolToGroup(t,e))}))):s()}else s();t(r),this._undoModel.endUndoMacro()}}class K{constructor(e,t){this._onVisibilityChanged=new i.Delegate,this._onLockChanged=new i.Delegate,this._onIsActualIntervalChanged=new i.Delegate,this._syncStateChanged=new i.Delegate,this._linkKeyChangedBound=this._linkKeyChanged.bind(this),this._undoModel=e,this._group=t,this._lineTools=t.lineTools(),this._paneId=(0,o.ensureNotNull)(e.model().paneForSource(this._lineTools[0])).id();const n=()=>{this._lineTools.forEach((e=>{e.properties().visible.subscribe(this,(()=>this._onVisibilityChanged.fire())),e.properties().frozen.subscribe(this,(()=>this._onLockChanged.fire())),e.onIsActualIntervalChange().subscribe(this,(()=>this._onIsActualIntervalChanged.fire())),e.linkKey().subscribe(this._linkKeyChangedBound),e.sharingMode().subscribe(this._linkKeyChangedBound)}))};this._group.onChanged().subscribe(this,(e=>{this._unsubscribeFromAllLineTools(),this._lineTools=this._group.lineTools(),n(),e.lockedChanged&&this._onLockChanged.fire(),e.visibilityChanged&&this._onVisibilityChanged.fire(),e.isActualIntervalChanged&&this._onIsActualIntervalChanged.fire();const t=this.getDrawingSyncState();null!==t&&this._syncStateChanged.fire(t)})),n(),this._lastActualZOrder=this.zOrder(),this._lastIsVisible=this.isVisible(),this._lastIsActualInterval=this.isActualInterval(),this._lastIsLocked=this.isLocked()}destroy(){this._unsubscribeFromAllLineTools(),this._group.onChanged().unsubscribeAll(this)}id(){return r(this._group.id,this._group.instanceId())}title(){return this._group.name()}gaLabel(){return"Group"}getIcon(){return{type:E.IconType.Svg,content:B}}canBeRemoved(){return!0}canBeHidden(){return!0}canBeLocked(){return!0}canBeRenamed(){return!0}fullyConstructed(){return!0}isVisible(){return this._group.lineTools().length>0&&(this._lastIsVisible="Invisible"!==this._group.visibility()),this._lastIsVisible}isActualInterval(){return this._group.lineTools().length>0&&(this._lastIsActualInterval=this._group.lineTools().some((e=>e.isActualInterval()))),this._lastIsActualInterval}onIsActualIntervalChange(){return this._onIsActualIntervalChanged}isLocked(){return this._group.lineTools().length>0&&(this._lastIsLocked="Locked"===this._group.locked()),this._lastIsLocked}onVisibilityChanged(){return this._onVisibilityChanged}onLockChanged(){return this._onLockChanged}setVisible(e){this._undoModel.lineToolsGroupController().setGroupVisibility(this._group,e)}setLocked(e){this._undoModel.lineToolsGroupController().setGroupLock(this._group,e)}setName(e){this._undoModel.lineToolsGroupController().setGroupName(this._group,e)}isCopiable(){return!1}isClonable(){return!1}zOrder(){return this._group.lineTools().length>0&&(this._lastActualZOrder=this._group.lineTools()[0].zorder()),this._lastActualZOrder}remove(){this._undoModel.lineToolsGroupController().removeGroup(this._group)}canBeAddedToSelection(){const e=this._undoModel.model()
;return this._lineTools.every((t=>e.selection().canBeAddedToSelection(t)))}setAsSelection(){this._undoModel.model().selectionMacro((e=>{e.clearSelection(),this._lineTools.forEach((t=>e.addSourceToSelection(t)))}))}addToSelection(){this._undoModel.model().selectionMacro((e=>{this._lineTools.forEach((t=>e.addSourceToSelection(t)))}))}addSourcesToArray(e){return e.push(...this._lineTools),e}detachFromParent(){}insertSourcesBeforeThis(e){const t=this._insertBeforeTarget();this._insertSources(e,(e=>this._undoModel.insertBefore(e,t)))}insertSourcesAfterThis(e){const t=this._insertAfterTarget();this._insertSources(e,(e=>this._undoModel.insertAfter(e,t)))}childrenIds(){const e=[...this._lineTools];return e.sort(((e,t)=>t.zorder()-e.zorder())),new Set(e.map((e=>r(e.id(),e.instanceId()))))}hasChildren(){return!0}pane(){return(0,o.ensureDefined)(this._undoModel.model().panes().find((e=>e.id()===this._paneId)))}allowsMovingBetweenPanes(){return!1}canBeAddedToGroup(){return!1}canInsertBeforeThis(e){return this._canInsertBeforeOrAfter(e)}canInsertAfterThis(e){return this._canInsertBeforeOrAfter(e)}canBeSyncedInLayout(){return this._lineTools.length>0&&this._lineTools[0].isSynchronizable()}onSyncStateChanged(){return this._syncStateChanged}setDrawingSyncState(e){if(this.canBeSyncedInLayout())switch(e){case 0:const e=this._lineTools.filter((e=>null!==e.linkKey().value()));e.length>0&&this._undoModel.unlinkLines(e);break;case 1:const t=this._lineTools.filter((e=>null===e.linkKey().value()));t.length>0&&this._undoModel.shareLineTools(t,1)}}getDrawingSyncState(){if(!this.canBeSyncedInLayout())return null;if(W){const e=this._lineTools[0]?.sharingMode().value();if(void 0===e)return null;let t=e;if(0!==t)for(const e of this._lineTools)if(e.sharingMode().value()!==t){t=0;break}return U(t)}return this._lineTools.every((e=>null!==e.linkKey().value()))?1:0}modificationDateChanged(){return H}getModificationDate(){return null}_linkKeyChanged(){this._syncStateChanged.fire((0,o.ensureNotNull)(this.getDrawingSyncState()))}_canInsertBeforeOrAfter(e){return V(this.pane(),e)}_insertSources(e,t){this._undoModel.beginUndoMacro(P);const n=e.reduce(((e,t)=>t.addSourcesToArray(e)),[]);e.forEach((e=>e.detachFromParent())),t(n),this._undoModel.endUndoMacro()}_insertBeforeTarget(){return(0,o.ensureNotNull)(this._lineTools.reduce(((e,t)=>null===e?t:e.zorder()<t.zorder()?e:t),null))}_insertAfterTarget(){return(0,o.ensureNotNull)(this._lineTools.reduce(((e,t)=>null===e?t:e.zorder()>t.zorder()?e:t),null))}_unsubscribeFromAllLineTools(){this._lineTools.forEach((e=>{e.properties().visible.unsubscribeAll(this),e.properties().frozen.unsubscribeAll(this),e.onIsActualIntervalChange().unsubscribeAll(this),e.linkKey().unsubscribe(this._linkKeyChangedBound),e.sharingMode().unsubscribe(this._linkKeyChangedBound)}))}}class ${constructor(e){this._hoveredObjectChanged=new i.Delegate,this._entitiesCache=new Map,this._undoModel=e,this._undoModel.model().hoveredSourceChanged().subscribe(this,this._onModelHoveredSourceChanged)}destroy(){
for(const e of this._entitiesCache.values())e?.destroy();this._undoModel.model().hoveredSourceChanged().unsubscribe(this,this._onModelHoveredSourceChanged)}getObjectById(e){if(this._entitiesCache.has(e))return(0,o.ensureDefined)(this._entitiesCache.get(e));const t=this._createObjectById(e);return this._entitiesCache.set(e,t),t}invalidateCache(e){Array.from(this._entitiesCache.keys()).forEach((t=>{e.has(t)||(this._entitiesCache.get(t)?.destroy(),this._entitiesCache.delete(t))}))}canBeGroupped(e){if(0===e.length||1===e.length&&e[0].hasChildren())return!1;const t=[];if(e.forEach((e=>e.addSourcesToArray(t))),t.some((e=>!(0,b.isLineTool)(e)||!e.boundToSymbol())))return!1;const n=this._undoModel.model(),o=t.map((e=>n.paneForSource(e)));if(new Set(o).size>1)return!1;if(!W)return!0;const i=t.map((e=>e.sharingMode().value()));return 1===new Set(i).size}contextMenuActions(e,t,n){const i=new A.ActionsProvider(e,n),s=[];return t.forEach((e=>e.addSourcesToArray(s))),i.contextMenuActionsForSources(s,(0,o.ensureNotNull)(this._undoModel.paneForSource(s[0])))}insertBefore(e,t){t.insertSourcesAfterThis(e)}insertAfter(e,t){t.insertSourcesBeforeThis(e)}setHoveredObject(e){const t=this._undoModel.model();if(null===e)return void t.setHoveredSource(null,null);const n=t.dataSourceForId(e);null!==n&&t.setHoveredSource(n,null)}hoveredObjectId(){return F(this._undoModel.model().hoveredSource())}hoveredObjectChanged(){return this._hoveredObjectChanged}_onModelHoveredSourceChanged(e){this._hoveredObjectChanged.fire(F(e))}_createObjectById(e){const t=l(e).persistentId,n=this._undoModel.model(),o=n.dataSourceForId(t);if(null!==o)return new Z(this._undoModel,o);const i=n.lineToolsGroupModel().groupForId(t);return null!==i?new K(this._undoModel,i):null}}var q=n(888740),J=n(247465),Y=n(289789),X=n(256577),Q=n(529596),ee=n(621327),te=n(180185),ne=n(671945),oe=n(737891),ie=n(654910),se=n(628090),re=n(397874),le=n(902872),ae=n(484959),ce=n(191730),ue=n(993544);const de=(0,ne.getLogger)("Platform.GUI.ObjectTree");var he;!function(e){e[e.Up=0]="Up",e[e.Down=1]="Down"}(he||(he={}));const ge=new _.TranslatedString("move objects",h.t(null,void 0,n(836044))),_e=new _.TranslatedString("lock objects",h.t(null,void 0,n(18942))),pe=new _.TranslatedString("unlock objects",h.t(null,void 0,n(823230))),me=new _.TranslatedString("show objects",h.t(null,void 0,n(523771))),fe=new _.TranslatedString("hide objects",h.t(null,void 0,n(193277))),be=new _.TranslatedString("remove objects",h.t(null,void 0,n(979688)));class ve{constructor(e){this._nodes={},this._onChange=new i.Delegate,this._onGroupCreated=new i.Delegate,this._subscriptions=[],this._removeSourcesPromise=null,this._timeout=null,this._objects=[],this._options={general:!0,mainSeries:!0,mainSeriesTrade:!0,esdStudies:!0,fundamentals:!0,studies:!0,lineTools:!0,publishedCharts:!0,ordersAndPositions:!0,alerts:!1,chartEvents:!0,objectTree:!1,gotoLineTool:!0},this._isContextMenuOpened=new f.WatchedValue(!1),this._getObjectsToModify=e=>{const t=this.selection().selected()
;return t.find((t=>t===e))?t.map(this._ensuredEntity):[this._ensuredEntity(e)]},this._onActiveChartChanged=()=>{this._cleanup(),this._init()},this._cleanup=()=>{null!==this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this._subscriptions.forEach((e=>{e.unsubscribeAll(this)})),this._selection.destroy(),this._chart.unsubscribe(this._onActiveChartChanged),null!==this._removeSourcesPromise&&this._removeSourcesPromise.cancel(),this._facade.destroy()},this._init=()=>{const e=this._chart.value();e.hasModel()&&(this._controller=e.model(),this._groupController=this._controller.lineToolsGroupController(),this._model=this._controller.model(),this._groupModel=this._model.lineToolsGroupModel(),this._facade=new $(this._controller),this._subscriptions=[this._model.mainSeries().onStyleChanged(),this._model.mainSeries().dataEvents().symbolResolved(),this._model.mainSeries().onIntervalChanged(),this._model.panesCollectionChanged(),this._model.dataSourceCollectionChanged(),this._groupModel.onChanged()],this._subscriptions.forEach((e=>{e.subscribe(this,this._update)})),this._chart.subscribe(this._onActiveChartChanged),this._selection=new c(this._model),this._update())},this._update=()=>{null===this._timeout&&(this._timeout=setTimeout((()=>{this._recalculateTree(),this._onChange.fire(),this._timeout=null})))},this._ensuredEntity=e=>(0,o.ensureNotNull)(this._getEntityById(e)),this._chart=e,this._init()}destroy(){this._cleanup()}getState(){return{nodes:Object.values(this._nodes),selection:this._selection.selected()}}getChartId(){return this._chart.value().id()}insertSelection(e,t){const n=this._facade,o=this.selection().selected().map(this._ensuredEntity),[i,s]=this._normalizeTargetAndDropType(e,t);this._controller.withMacro(ge,(()=>{switch(s){case"before":n.insertBefore(o,i);break;case"after":n.insertAfter(o,i)}})),this._update()}entity(e){return this._facade.getObjectById(e)}isMain(e){return l(e.id()).persistentId===this._controller.mainSeries().id()}selection(){return this._selection}setIsLocked(e,t){const n=this._getObjectsToModify(e),o=t?_e:pe;this._controller.withMacro(o,(()=>{for(const e of n)e.setLocked(t)})),(0,Y.trackObjectTreeEvent)("Lock",(0,Y.getGaAction)(n))}setIsVisible(e,t){const n=this._getObjectsToModify(e),o=t?me:fe;this._controller.withMacro(o,(()=>{for(const e of n)e.setVisible(t)})),(0,Y.trackObjectTreeEvent)("Hide",(0,Y.getGaAction)(n))}remove(e){const t=e=>{this._controller.withMacro(be,(()=>{for(const t of n)t.isLocked()&&!e||t.remove()})),(0,Y.trackObjectTreeEvent)("Delete",(0,Y.getGaAction)(n)),this._update()},n=this._getObjectsToModify(e),o=n.some((e=>!1)),i=n.some((e=>e.isLocked()));o?(null!==this._removeSourcesPromise&&this._removeSourcesPromise.cancel(),this._removeSourcesPromise=(0,J.makeCancelable)(confirmDatasourceRemoving(i)),this._removeSourcesPromise.promise.then((e=>{e&&(this._removeSourcesPromise=null,t(!0))}))):i?(this._removeSourcesPromise?.cancel(),this._removeSourcesPromise=(0,J.makeCancelable)((0,se.confirmRemovingLockedLineTools)(se.DeleteLockedLineToolReason.RemoveSelected)),
this._removeSourcesPromise.promise.then((e=>{t(e)}))):t(!0)}canSelectionBeGrouped(){const e=this._getSelectedEntities();return this._facade.canBeGroupped(e)}createGroupFromSelection(){const e=this._groupController.createGroupFromSelection();(0,Y.trackObjectTreeEvent)("Create Group");const t=r(e.id,e.instanceId());this.selection().set([this._ensuredEntity(t)]),this._onGroupCreated.fire(t),this._update()}isSelectionDropable(e,t){const n=this.selection().selected().map(this._ensuredEntity),[o,i]=this._normalizeTargetAndDropType(e,t);switch(i){case"after":return o.canInsertAfterThis(n);case"before":return o.canInsertBeforeThis(n)}}onChange(){return this._onChange}onGroupCreated(){return this._onGroupCreated}isSelectionCloneable(){const e=this._getSelectedEntities();return e.length>0&&e.every((e=>e.isClonable()))}isSelectionCopiable(){const e=this._getSelectedEntities();return e.length>0&&e.every((e=>e.isCopiable()))}openProperties(e,t){const n=this._model.dataSourceForId(l(e.id()).persistentId);this.selection().selected().length>1&&this.selection().selected().includes(e.id())?this._chart.value().showSelectedSourcesProperties(t):(this.selection().set([e]),null!==n?this._controller.mainSeries()===n?this._chart.value().showGeneralChartProperties(void 0,{shouldReturnFocus:!0}):((0,b.isLineTool)(n)||(0,v.isStudy)(n))&&this._chart.value().showChartPropertiesForSource(n,t,{shouldReturnFocus:!0}):this._chart.value().showChartPropertiesForSources({sources:this._chart.value().model().selection().lineDataSources(),title:e.title().value(),tabName:t,renamable:!0}))}canSelectionBeUnmerged(){const e=this._getSelectedEntities();return 1===e.length&&this.canNodeWithIdBeUnmerged(l(e[0].id()).persistentId)}canNodeWithIdBeUnmerged(e){const t=this._model.dataSourceForId(e);return null!==t&&(0,X.isPriceDataSource)(t)&&this._model.isUnmergeAvailableForSource(t)}unmergeSelectionUp(){this._unmergeSelection(0)}unmergeSelectionDown(){this._unmergeSelection(1)}copySelection(){const e=this._getSelectedEntities(),t=e.map((e=>(0,o.ensureNotNull)(this._model.dataSourceForId(l(e.id()).persistentId))));this._chart.value().chartWidgetCollection().clipboard.uiRequestCopy(t),(0,Y.trackObjectTreeEvent)("Copy",(0,Y.getGaAction)(e))}cloneSelection(){const e=this._getSelectedEntities(),t=e.map((e=>(0,o.ensureNotNull)(this._model.dataSourceForId(l(e.id()).persistentId))));t.every(b.isLineTool)&&(this._controller.cloneLineTools([...t],!1),(0,Y.trackObjectTreeEvent)("Clone",(0,Y.getGaAction)(e)))}rename(e,t){const n=this._getObjectsToModify(e.id());1===n.length&&n.some((e=>e.canBeRenamed()))&&(t(),(0,Y.trackObjectTreeEvent)("Rename",(0,Y.getGaAction)(n)))}async openContextMenu(e,t,n){this._objects=this._getObjectsToModify(e.id());const o=this._facade.canBeGroupped(this._objects);let i;if(this._objects.some((e=>e.hasChildren())))i=this._getActionsForGroupItem(e,t,o);else{const e=await this._facade.contextMenuActions(this._chart.value(),this._objects,this._options);if(i=Array.from(e).filter(((e,t,n)=>"separator"!==e.type||!n[t+1]||"separator"!==n[t+1].type)),
1===this._objects.length&&this._objects[0].canBeRenamed()){const e=i.findIndex((e=>"Copy"===e.id));i.splice(-1===e?i.length:e+1,0,this._getRenameAction(t))}const n=i.findIndex((e=>"Clone"===e.id)),s=i[n];if(-1!==n&&"action"===s.type&&s.update({shortcutHint:void 0}),o){const e=this._getGroupAction();if(-1!==n)i.splice(n,0,e);else{const t=i.findIndex((e=>"Copy"===e.id));i.splice(-1===t?0:t,0,e)}}}if(i.length>0){const t=l(e.id()).persistentId,o=this._model.dataSourceForId(t),s=o instanceof ie.Series,r=0!==e.childrenIds().size;let a;a=s?{menuName:"ObjectTreeContextMenu",detail:{type:"series",id:o.instanceId()}}:(0,b.isLineTool)(o)?{menuName:"ObjectTreeContextMenu",detail:{type:"shape",id:o?.id()??null}}:r?{menuName:"ObjectTreeContextMenu",detail:{type:"groupOfShapes",id:t||null}}:{menuName:"ObjectTreeContextMenu",detail:{type:"study",id:o?.id()||null}},Q.ContextMenuManager.showMenu(i,n,{takeFocus:!0,returnFocus:!0},a,(()=>{this._isContextMenuOpened.setValue(!1)})).then((()=>{this._isContextMenuOpened.setValue(!0)}))}}setHoveredObject(e){const t=e?l(e).persistentId:null;this._facade.setHoveredObject(t)}hoveredObjectChanged(){return this._facade.hoveredObjectChanged()}getNextNodeIdAfterRemove(e){const{nodes:t}=this.getState(),n=l(e).persistentId,i=t.find((t=>t.id===e)),s=this.entity(e);if(!(i&&i.parentId&&s&&s.canBeRemoved()))return null;if(s.pane().mainDataSource()?.id()===n&&!this.canNodeWithIdBeUnmerged(n)){const e=t.filter((e=>0===e.level)).map((e=>e.id)),n=this._takeNextOrPrevElement(e,i.parentId);return(0,o.ensureDefined)(t.find((e=>e.id===n))).children[0]}const r=(0,o.ensureDefined)(t.find((e=>e.id===i.parentId))).children;return 1===r.length?this.getNextNodeIdAfterRemove(i.parentId):this._takeNextOrPrevElement(r,e)}isContextMenuOpened(){return this._isContextMenuOpened.readonly()}getChartLayout(){return this._chart.value().chartWidgetCollection().layout}_takeNextOrPrevElement(e,t){const n=e.indexOf(t);return e[n===e.length-1?n-1:n+1]}_getGroupAction(){return new oe.ActionWithStandardIcon({actionId:"ObjectsTree.CreateGroup",options:{label:q.createGroupTitle,iconId:"ObjectsTree.CreateGroup",onExecute:()=>{this.createGroupFromSelection()}}})}_getRenameAction(e){return new oe.ActionWithStandardIcon({actionId:"ObjectsTree.RenameItem",options:{label:q.renameTitle,iconId:"ObjectsTree.RenameItem",onExecute:()=>{e(),(0,Y.trackObjectTreeEvent)("Context menu rename",(0,Y.getGaAction)(this._objects))}}})}_getActionsForGroupItem(e,t,n){const o=[];this._objects.forEach((e=>e.addSourcesToArray(o)));const i=[];1===this._objects.length&&i.unshift(this._getRenameAction(t),new ee.Separator),n&&i.unshift(this._getGroupAction(),new ee.Separator);const s=(0,A.createSyncDrawingActions)(this._chart.value(),o.filter(b.isLineTool));s.length&&i.push(...s,new ee.Separator);const r=this._chart.value().actions().format.getState();return i.push(new ee.Action({actionId:"ObjectsTree.ToggleItemLocked",options:{label:e.isLocked()?q.unlockTitle:q.lockTitle,icon:e.isLocked()?re:le,onExecute:()=>this.setIsLocked(e.id(),!e.isLocked())}}),new ee.Action({
actionId:"ObjectsTree.ToggleItemVisibility",options:{label:e.isVisible()?q.hideTitle:q.showTitle,icon:e.isVisible()?ae:ce,onExecute:()=>this.setIsVisible(e.id(),!e.isVisible())}}),new ee.Action({actionId:"ObjectsTree.RemoveItem",options:{label:q.removeTitle,icon:ue,onExecute:()=>this.remove(e.id()),hotkeyHash:te.isMacKeyboard?8:46}}),new ee.Separator,new ee.Action({actionId:r.actionId,options:{label:r.label,icon:r.icon,onExecute:()=>this.openProperties(e)}})),i}_unmergeSelection(e){const t=this._getSelectedEntities();if(1!==t.length)throw new Error("Only one object can be unmerged");const n=t[0],i=(0,o.ensureNotNull)(this._model.dataSourceForId(l(n.id()).persistentId));if(!(0,X.isPriceDataSource)(i))throw new Error("Entity is not IPriceDataSource");(0===e?this._controller.unmergeSourceUp:this._controller.unmergeSourceDown).call(this._controller,i);const s=0===e?"New pane above":"New pane below";(0,Y.trackObjectTreeEvent)(s,(0,Y.getGaAction)([n]))}_recalculateTree(){const e=new u(this._controller,this._facade);this._nodes=e.buildTree()}_normalizeTargetAndDropType(e,t){let n=this._ensuredEntity(e);return"inside"===t&&(t="before",n=(0,o.ensureNotNull)(this.entity([...n.childrenIds()].shift()||""))),[n,t]}_getSelectedEntities(){const{selected:e,removed:t}=this._selection.selected().reduce(((e,t)=>{const n=this._getEntityById(t);return n?(e.selected.push(n),e):(e.removed.push(t),e)}),{selected:[],removed:[]});return t.length&&de.logWarn(`Detected dangling sources in selection. They will be ignored: ${JSON.stringify(t)}`),e}_getEntityById(e){return this._facade.getObjectById(e)}}},330344:(e,t,n)=>{"use strict";n.d(t,{useDimensions:()=>s});var o=n(50959),i=n(664332);function s(e){const[t,n]=(0,o.useState)(null),s=(0,o.useCallback)((([e])=>{const o=e.target.getBoundingClientRect();o.width===t?.width&&o.height===t.height||n(o)}),[t]);return[(0,i.useResizeObserver)({callback:s,ref:e}),t]}},939157:(e,t,n)=>{"use strict";n.d(t,{ToolbarIconButton:()=>r});var o=n(50959),i=n(865266),s=n(565631);const r=(0,o.forwardRef)((function(e,t){const[n,r]=(0,i.useRovingTabindexElement)(t);return o.createElement(s.AccessibleIconButton,{...e,ref:n,tabIndex:r})}))},843833:(e,t,n)=>{"use strict";n.d(t,{ToolbarMenuButton:()=>a});var o=n(50959),i=n(718736),s=n(865266),r=n(74336),l=n(975598);const a=(0,o.forwardRef)((function(e,t){const{tooltip:n,menuReference:a=null,...c}=e,[u,d]=(0,s.useRovingTabindexElement)(null),h=(0,i.useFunctionalRefObject)(a);return o.createElement(l.AccessibleMenuButton,{"aria-label":n,"aria-haspopup":"menu",...r.MouseClickAutoBlurHandler.attributes(),...c,ref:t,tag:"button",buttonRef:u,tabIndex:d,menuReference:h,tooltip:n})}))},689376:(e,t,n)=>{"use strict";var o,i;n.d(t,{IconType:()=>o}),function(e){e.Svg="svg"}(o||(o={})),function(e){e[e.NoSync=0]="NoSync",e[e.SyncInLayout=1]="SyncInLayout",e[e.GlobalSync=2]="GlobalSync"}(i||(i={}))},57674:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 11.5v8a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1v-8m-17 0v-4a1 1 0 0 1 1-1h4l2 2h9a1 1 0 0 1 1 1v2m-17 0h17"/></svg>'},94007:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M2.448 10.124a10.82 10.82 0 0 1-.336-.609L2.105 9.5l.007-.015a12.159 12.159 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373c2.297 0 4.047 1.292 5.25 2.646a12.166 12.166 0 0 1 1.687 2.466l.007.015-.007.015a12.163 12.163 0 0 1-1.686 2.466c-1.204 1.354-2.954 2.646-5.251 2.646-2.298 0-4.048-1.292-5.252-2.646a12.16 12.16 0 0 1-1.35-1.857zm14.558-.827l-.456.203.456.203v.002l-.003.005-.006.015-.025.052a11.813 11.813 0 0 1-.461.857 13.163 13.163 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982-2.703 0-4.703-1.522-6-2.982a13.162 13.162 0 0 1-1.83-2.677 7.883 7.883 0 0 1-.118-.243l-.007-.015-.002-.005v-.001l.456-.204-.456-.203v-.002l.002-.005.007-.015a4.66 4.66 0 0 1 .119-.243 13.158 13.158 0 0 1 1.83-2.677c1.296-1.46 3.296-2.982 5.999-2.982 2.702 0 4.702 1.522 5.998 2.981a13.158 13.158 0 0 1 1.83 2.678 8.097 8.097 0 0 1 .119.243l.006.015.003.005v.001zm-.456.203l.456-.203.09.203-.09.203-.456-.203zM1.092 9.297l.457.203-.457.203-.09-.203.09-.203zm9.958.203c0 1.164-.917 2.07-2 2.07-1.084 0-2-.906-2-2.07 0-1.164.916-2.07 2-2.07 1.083 0 2 .906 2 2.07zm1 0c0 1.695-1.344 3.07-3 3.07-1.657 0-3-1.375-3-3.07 0-1.695 1.343-3.07 3-3.07 1.656 0 3 1.375 3 3.07z"/></svg>'},52870:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M7 5.5a2.5 2.5 0 0 1 5 0V7H7V5.5zM6 7V5.5a3.5 3.5 0 1 1 7 0V7a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2zm8 2a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V9zm-3 2.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/></svg>'},274059:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M21.106 12.5H6.894a.5.5 0 0 1-.318-.886L14 5.5l7.424 6.114a.5.5 0 0 1-.318.886zM21.106 16.5H6.894a.5.5 0 0 0-.318.886L14 23.5l7.424-6.114a.5.5 0 0 0-.318-.886z"/></svg>'},191730:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M4.605 14.089A10.052 10.052 0 0 1 4.56 14l.046-.089a17.18 17.18 0 0 1 2.329-3.327C8.58 8.758 10.954 7 14 7c3.046 0 5.421 1.757 7.066 3.585A17.18 17.18 0 0 1 23.44 14l-.046.089a17.18 17.18 0 0 1-2.329 3.327C19.42 19.242 17.046 21 14 21c-3.046 0-5.421-1.757-7.066-3.584a17.18 17.18 0 0 1-2.329-3.327zm19.848-.3L24 14l.453.212-.001.002-.003.005-.009.02a16.32 16.32 0 0 1-.662 1.195c-.44.72-1.1 1.684-1.969 2.65C20.08 20.008 17.454 22 14 22c-3.454 0-6.079-1.993-7.81-3.916a18.185 18.185 0 0 1-2.469-3.528 10.636 10.636 0 0 1-.161-.318l-.01-.019-.002-.005v-.002L4 14a55.06 55.06 0 0 1-.453-.212l.001-.002.003-.005.009-.02.033-.067a16.293 16.293 0 0 1 .629-1.126c.44-.723 1.1-1.686 1.969-2.652C7.92 7.993 10.546 6 14 6c3.454 0 6.079 1.993 7.81 3.916a18.183 18.183 0 0 1 2.469 3.528 10.588 10.588 0 0 1 .161.318l.01.019.002.005v.002zM24 14l.453-.211.099.211-.099.211L24 14zm-20.453-.211L4 14l-.453.211L3.448 14l.099-.211zM11 14a3 3 0 1 1 6 0 3 3 0 0 1-6 0zm3-4a4 4 0 1 0 0 8 4 4 0 0 0 0-8zm0 5a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/></svg>'},607295:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M4.5 12.5l4.59-4.59a2 2 0 0 1 2.83 0l3.17 3.17a2 2 0 0 0 2.83 0L22.5 6.5m-8 9.5v5.5M12 19l2.5 2.5L17 19m4.5 3v-5.5M19 19l2.5-2.5L24 19"/></svg>'},728824:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>'},582e3:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path fill="currentColor" fill-rule="evenodd" d="M6.18 9.99A.8.8 0 0 1 6 10a.8.8 0 0 1-.18-.01l-.02-.01c-.05-.05-.15-.2-.33-.66l-.25-.58c-.3-.72-.6-1.4-.6-2.43L4.63 6h2.74v.31c0 1.03-.28 1.7-.6 2.43l-.24.58a2.08 2.08 0 0 1-.35.67ZM7.24 5H4.76c.17-.88.47-1.71.7-2.32a2.08 2.08 0 0 1 .36-.67A.8.8 0 0 1 6 2l.18.01.02.01c.05.05.15.2.33.66.24.6.54 1.44.7 2.32Zm1.13 1v.31c0 1.26-.38 2.15-.7 2.88l-.2.5-.02.04A4 4 0 0 0 10 6H8.37Zm1.5-1H8.25a14.09 14.09 0 0 0-.78-2.68l-.02-.05A4 4 0 0 1 9.87 5ZM3.75 5c.18-1.06.53-2.03.78-2.68l.02-.05A4 4 0 0 0 2.13 5h1.62ZM2 6a4 4 0 0 0 2.55 3.73l-.02-.05-.2-.49c-.32-.73-.7-1.62-.7-2.88V6H2Zm4-5h-.02a5 5 0 0 0 0 10h.04a5 5 0 0 0 0-10H6Z"/></svg>'},206672:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path fill="currentColor" d="M5.31 2.58l.93-.94C7 .88 8.74.6 10.07 1.93c1.34 1.33 1.05 3.07.29 3.83l-.94.93-.36.36-.1.1-.03.03v.01l-.34-.33-.33-.33.04-.04.1-.1.36-.36.94-.94c.4-.39.68-1.53-.29-2.5s-2.11-.68-2.5-.29l-.94.94-.36.36-.1.1-.03.03h-.01v.01l-.33-.33-.33-.33v-.01l.03-.03.11-.1.36-.36zM3.08 4.8l.***********-.04.04-.1.1-.36.36-.94.94c-.4.39-.68 1.53.29 2.5s2.11.68 2.5.29l.94-.94.36-.36.1-.1.03-.03h.01v-.01l.33.33.33.33v.01l-.03.03-.11.1-.36.36-.93.94c-.76.76-2.5 1.05-3.83-.29C.59 8.74.88 7 1.64 6.24l.94-.93.36-.36.1-.1.03-.03V4.8z"/><path fill="currentColor" d="M7.1 4.23L4.24 7.11l.66.66 2.88-2.88-.66-.66z"/></svg>'},749756:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M11.5 4A2.5 2.5 0 0 0 7 5.5V7h6a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2V5.5a3.5 3.5 0 0 1 6.231-2.19c-.231.19-.73.69-.73.69zM13 8H6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1zm-2 3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/></svg>'},962766:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14.692 3.012l-12 12.277.715.699 12-12.277-.715-.699zM9.05 15.627a7.042 7.042 0 0 1-3.144-.741l.742-.76c.72.311 1.52.5 2.402.5 2.297 0 4.047-1.29 5.25-2.645a12.168 12.168 0 0 0 1.687-2.466l.007-.015-.007-.015A12.166 12.166 0 0 0 14.3 7.019c-.11-.124-.225-.247-.344-.37l.699-.715c.137.14.268.28.392.42a13.16 13.16 0 0 1 1.83 2.678 8.117 8.117 0 0 1 .119.243l.006.015.003.005v.001l-.456.204.456.203v.002l-.003.005-.006.015-.025.052a11.762 11.762 0 0 1-.461.857 13.158 13.158 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982zm7.5-6.127l.456-.203.09.203-.09.203-.456-.203zm-7.5 3.07c-.27 0-.53-.037-.778-.105l.879-.899c.999-.052 1.833-.872 1.895-1.938l.902-.923c.**************.102.795 0 1.695-1.344 3.07-3 3.07zM6.15 10.294l.902-.923c.063-1.066.896-1.886 1.895-1.938l.879-.9a2.94 2.94 0 0 0-.777-.103c-1.657 0-3 1.374-3 3.069 0 .275.035.541.101.795zM9.05 4.373c.88 0 1.68.19 2.4.5l.743-.759a7.043 7.043 0 0 0-3.143-.74c-2.703 0-4.703 1.521-6 2.98a13.159 13.159 0 0 0-1.83 2.678 7.886 7.886 0 0 0-.118.243l-.007.015-.002.005v.001l.456.204-.457-.203-.09.203.09.203.457-.203-.456.203v.002l.002.005.007.015a4.5 4.5 0 0 0 .119.243 13.152 13.152 0 0 0 1.83 2.677c.124.14.255.28.392.42l.7-.715c-.12-.122-.235-.245-.345-.369a12.156 12.156 0 0 1-1.686-2.466L2.105 9.5l.007-.015a12.158 12.158 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373z"/></svg>'}}]);