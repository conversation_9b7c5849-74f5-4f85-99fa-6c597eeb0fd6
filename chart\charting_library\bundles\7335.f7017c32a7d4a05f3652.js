(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7335],{266783:e=>{"use strict";var t=Object.prototype.hasOwnProperty;function r(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}e.exports=function(e,n){if(r(e,n))return!0;if("object"!=typeof e||null===e||"object"!=typeof n||null===n)return!1;var o=Object.keys(e),a=Object.keys(n);if(o.length!==a.length)return!1;for(var l=0;l<o.length;l++)if(!t.call(n,o[l])||!r(e[o[l]],n[o[l]]))return!1;return!0}},188317:e=>{e.exports={pills:"pills-PVWoXu5j",primary:"primary-PVWoXu5j",gray:"gray-PVWoXu5j",selected:"selected-PVWoXu5j",grouped:"grouped-PVWoXu5j",active:"active-PVWoXu5j",disableActiveOnTouch:"disableActiveOnTouch-PVWoXu5j",disableActiveStateStyles:"disableActiveStateStyles-PVWoXu5j",withGrouped:"withGrouped-PVWoXu5j","quiet-primary":"quiet-primary-PVWoXu5j",green:"green-PVWoXu5j",red:"red-PVWoXu5j",blue:"blue-PVWoXu5j",secondary:"secondary-PVWoXu5j",ghost:"ghost-PVWoXu5j"}},601538:e=>{e.exports={lightButton:"lightButton-bYDQcOkp",link:"link-bYDQcOkp",ltr:"ltr-bYDQcOkp",rtl:"rtl-bYDQcOkp","typography-regular16px":"typography-regular16px-bYDQcOkp","typography-medium16px":"typography-medium16px-bYDQcOkp","typography-regular14px":"typography-regular14px-bYDQcOkp","typography-semibold14px":"typography-semibold14px-bYDQcOkp","typography-semibold16px":"typography-semibold16px-bYDQcOkp",content:"content-bYDQcOkp",visuallyHidden:"visuallyHidden-bYDQcOkp",nowrap:"nowrap-bYDQcOkp",ellipsisContainer:"ellipsisContainer-bYDQcOkp",textWrapContainer:"textWrapContainer-bYDQcOkp",textWrapWithEllipsis:"textWrapWithEllipsis-bYDQcOkp",slot:"slot-bYDQcOkp",caret:"caret-bYDQcOkp",activeCaret:"activeCaret-bYDQcOkp",xsmall:"xsmall-bYDQcOkp",withStartSlot:"withStartSlot-bYDQcOkp",withEndSlot:"withEndSlot-bYDQcOkp",noContent:"noContent-bYDQcOkp",wrap:"wrap-bYDQcOkp",small:"small-bYDQcOkp",medium:"medium-bYDQcOkp"}},78217:e=>{e.exports={pair:"pair-ocURKVwI",xxceptionallysmalldonotusebrv1023:"xxceptionallysmalldonotusebrv1023-ocURKVwI",xxxxsmall:"xxxxsmall-ocURKVwI",xxxsmall:"xxxsmall-ocURKVwI",xxsmall:"xxsmall-ocURKVwI",xsmall:"xsmall-ocURKVwI",small:"small-ocURKVwI",medium:"medium-ocURKVwI",large:"large-ocURKVwI",xlarge:"xlarge-ocURKVwI",xxlarge:"xxlarge-ocURKVwI",xxxlarge:"xxxlarge-ocURKVwI",logo:"logo-ocURKVwI",skeleton:"skeleton-ocURKVwI",empty:"empty-ocURKVwI"}},979566:e=>{e.exports={container:"container-M1mz4quA",pairContainer:"pairContainer-M1mz4quA",logo:"logo-M1mz4quA",hidden:"hidden-M1mz4quA"}},692335:e=>{e.exports={container:"container-qm7Rg5MB",mobile:"mobile-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},410070:e=>{e.exports={actions:"actions-rarsm4ka",actionButton:"actionButton-rarsm4ka"}},394869:e=>{e.exports={logo:"logo-d0vVmGvT"}},292069:e=>{e.exports={"tablet-small-breakpoint":"(max-width: 440px)",itemRow:"itemRow-oRSs8UQo",multiLine:"multiLine-oRSs8UQo",cell:"cell-oRSs8UQo",itemInfoCell:"itemInfoCell-oRSs8UQo",description:"description-oRSs8UQo",
symbolDescription:"symbolDescription-oRSs8UQo",flag:"flag-oRSs8UQo",exchangeDescription:"exchangeDescription-oRSs8UQo",marketType:"marketType-oRSs8UQo",exchangeName:"exchangeName-oRSs8UQo",actionHandleWrap:"actionHandleWrap-oRSs8UQo",source:"source-oRSs8UQo",hover:"hover-oRSs8UQo",selected:"selected-oRSs8UQo",active:"active-oRSs8UQo",highlighted:"highlighted-oRSs8UQo",light:"light-oRSs8UQo","highlight-animation-theme-light":"highlight-animation-theme-light-oRSs8UQo",dark:"dark-oRSs8UQo","highlight-animation-theme-dark":"highlight-animation-theme-dark-oRSs8UQo",markedFlag:"markedFlag-oRSs8UQo",offset:"offset-oRSs8UQo",descriptionCell:"descriptionCell-oRSs8UQo",addition:"addition-oRSs8UQo",exchangeCell:"exchangeCell-oRSs8UQo",fixedWidth:"fixedWidth-oRSs8UQo",expandHandle:"expandHandle-oRSs8UQo",expanded:"expanded-oRSs8UQo",symbolTitle:"symbolTitle-oRSs8UQo",invalid:"invalid-oRSs8UQo",noDescription:"noDescription-oRSs8UQo",highlightedText:"highlightedText-oRSs8UQo",icon:"icon-oRSs8UQo",narrow:"narrow-oRSs8UQo",wide:"wide-oRSs8UQo",dataMode:"dataMode-oRSs8UQo",actionsCell:"actionsCell-oRSs8UQo",action:"action-oRSs8UQo",targetAction:"targetAction-oRSs8UQo",removeAction:"removeAction-oRSs8UQo",addAction:"addAction-oRSs8UQo",markedFlagWrap:"markedFlagWrap-oRSs8UQo",markedFlagMobile:"markedFlagMobile-oRSs8UQo",logo:"logo-oRSs8UQo",isExpandable:"isExpandable-oRSs8UQo",primaryIcon:"primaryIcon-oRSs8UQo"}},506963:e=>{e.exports={icon:"icon-OJpk_CAQ"}},906109:e=>{e.exports={wrap:"wrap-IxKZEhmO",libAllSelected:"libAllSelected-IxKZEhmO",container:"container-IxKZEhmO",iconWrap:"iconWrap-IxKZEhmO",icon:"icon-IxKZEhmO",title:"title-IxKZEhmO",highlighted:"highlighted-IxKZEhmO",description:"description-IxKZEhmO",mobile:"mobile-IxKZEhmO",allSelected:"allSelected-IxKZEhmO",desktop:"desktop-IxKZEhmO",allSelectedIcon:"allSelectedIcon-IxKZEhmO",selected:"selected-IxKZEhmO",focused:"focused-IxKZEhmO",titleWithoutDesc:"titleWithoutDesc-IxKZEhmO",textBlock:"textBlock-IxKZEhmO",bordered:"bordered-IxKZEhmO"}},696137:e=>{e.exports={container:"container-dfKL9A7t",contentList:"contentList-dfKL9A7t",contentListDesktop:"contentListDesktop-dfKL9A7t",searchSourceItemsContainer:"searchSourceItemsContainer-dfKL9A7t",oneColumn:"oneColumn-dfKL9A7t",searchSourceItemsContainerDesktop:"searchSourceItemsContainerDesktop-dfKL9A7t",groupTitleDesktop:"groupTitleDesktop-dfKL9A7t",column:"column-dfKL9A7t",emptyText:"emptyText-dfKL9A7t",emptyIcon:"emptyIcon-dfKL9A7t",noResultsDesktop:"noResultsDesktop-dfKL9A7t"}},306591:e=>{e.exports={wrap:"wrap-gjrLBBL3",item:"item-gjrLBBL3",small:"small-gjrLBBL3",newStyles:"newStyles-gjrLBBL3",mobile:"mobile-gjrLBBL3",text:"text-gjrLBBL3",exchange:"exchange-gjrLBBL3",filterItem:"filterItem-gjrLBBL3",brokerWrap:"brokerWrap-gjrLBBL3"}},44458:e=>{e.exports={wrap:"wrap-dlewR1s1",watchlist:"watchlist-dlewR1s1",noFeed:"noFeed-dlewR1s1",newStyles:"newStyles-dlewR1s1",scrollContainer:"scrollContainer-dlewR1s1",listContainer:"listContainer-dlewR1s1",multiLineItemsContainer:"multiLineItemsContainer-dlewR1s1",
withSpinner:"withSpinner-dlewR1s1",spinnerContainer:"spinnerContainer-dlewR1s1",largeSpinner:"largeSpinner-dlewR1s1"}},976717:e=>{e.exports={search:"search-ZXzPWcCf",upperCase:"upperCase-ZXzPWcCf",bubblesContainer:"bubblesContainer-ZXzPWcCf",mobile:"mobile-ZXzPWcCf",bubbles:"bubbles-ZXzPWcCf",withFilters:"withFilters-ZXzPWcCf",spinnerWrap:"spinnerWrap-ZXzPWcCf",emptyText:"emptyText-ZXzPWcCf",emptyIcon:"emptyIcon-ZXzPWcCf",noResultsDesktop:"noResultsDesktop-ZXzPWcCf",brokerButtonWrap:"brokerButtonWrap-ZXzPWcCf"}},992244:e=>{e.exports={flagWrap:"flagWrap-QKnxaZOG",icon:"icon-QKnxaZOG",caret:"caret-QKnxaZOG",title:"title-QKnxaZOG",button:"button-QKnxaZOG",withFlag:"withFlag-QKnxaZOG",buttonContent:"buttonContent-QKnxaZOG"}},963748:e=>{e.exports={dialog:"dialog-u2dP3kv1",tabletDialog:"tabletDialog-u2dP3kv1",desktopDialog:"desktopDialog-u2dP3kv1",backButton:"backButton-u2dP3kv1"}},624517:e=>{e.exports={childrenWrapper:"childrenWrapper-_RhDhmVQ",container:"container-_RhDhmVQ"}},295059:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},173850:(e,t,r)=>{"use strict";r.d(t,{SEPARATOR_PREFIX:()=>n,isSeparatorItem:()=>o});const n="###";function o(e){return e.startsWith(n)}},380132:(e,t,r)=>{"use strict";r.d(t,{BackButton:()=>y});var n,o=r(50959),a=r(270762),l=r(295694),s=r(249498),c=r(560176),i=r(935369),u=r(758478),d=r(773063),m=r(914127),p=r(218073),h=r(99243),g=r(142576);function f(e="large",t="1.2"){switch(e){case"large":return"1.2"===t?l:d;case"medium":return"1.2"===t?s:m;case"small":return"1.2"===t?c:p;case"xsmall":return"1.2"===t?i:h;case"xxsmall":return"1.2"===t?u:g;default:return s}}!function(e){e.Thin="1.2",e.Medium="1.5"}(n||(n={}));const y=o.forwardRef(((e,t)=>{const{"aria-label":r,flipIconOnRtl:n,...l}=e;return o.createElement(a.NavButton,{...l,"aria-label":r,ref:t,icon:f(e.size,e.iconStrokeWidth),flipIconOnRtl:n})}))},959189:(e,t,r)=>{"use strict";function n(e,t){return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}r.d(t,{isIconOnly:()=>n})},898237:(e,t,r)=>{"use strict";r.d(t,{LightAnchorButton:()=>p,LightButton:()=>o.LightButton});var n=r(418920),o=r(943158),a=r(50959),l=r(591365),s=r(273388),c=r(854797),i=r(601538),u=r.n(i),d=r(188317),m=r.n(d);function p(e){const{className:t,isSelected:r,children:o,iconOnly:i,ellipsis:d,showCaret:p,forceDirection:h,endSlot:g,startSlot:f,color:y,variant:S,reference:v,size:b,enableActiveStateStyles:x,renderComponent:w=l.CustomComponentDefaultLink,typography:k,textWrap:C=!1,maxLines:E,style:I={},isActive:L,isPills:R,...T}=e,B=C?E??2:1,N=B>0?{...I,"--ui-lib-light-button-content-max-lines":B}:I;return a.createElement(w,{...T,className:(0,n.useLightButtonClasses)({...m(),...u()},{className:t,isSelected:r,isActive:L,isPills:R,children:o,iconOnly:i,showCaret:p,forceDirection:h,color:y,variant:S,size:b,enableActiveStateStyles:x,typography:k,textWrap:C,isLink:!0,endSlot:g,startSlot:f}),reference:(0,s.isomorphicRef)(v),style:N},a.createElement(c.LightButtonContent,{showCaret:p,isActiveCaret:p&&(R||L||r),iconOnly:i,ellipsis:d,textWrap:C,endSlot:g,startSlot:f},o))}
r(15378)},418920:(e,t,r)=>{"use strict";r.d(t,{useLightButtonClasses:()=>i});var n=r(50959),o=r(497754),a=r(234539),l=r(959189),s=r(380327);const c=n.createContext({isInButtonGroup:!1,isGroupPrimary:!1}),i=(e,t,r)=>{const i=(0,n.useContext)(a.CustomBehaviourContext),{className:u,isSelected:d,children:m,showCaret:p,forceDirection:h,iconOnly:g,color:f="gray",variant:y="primary",size:S="medium",enableActiveStateStyles:v=i.enableActiveStateStyles,typography:b,isLink:x=!1,textWrap:w,isPills:k,isActive:C,startSlot:E,endSlot:I}=t,L=e[`typography-${((e,t,r)=>{if(r){const e=r.replace(/^\D+/g,"");return t?`semibold${e}`:r}switch(e){case"xsmall":return t?"semibold14px":"regular14px";case"small":case"medium":return t?"semibold16px":"regular16px";default:return""}})(S,d||k,b||void 0)}`],R=(0,n.useContext)(s.ControlGroupContext),{isInButtonGroup:T,isGroupPrimary:B}=(0,n.useContext)(c);return o(u,e.lightButton,x&&e.link,C&&e.active,d&&e.selected,(0,l.isIconOnly)(m,g)&&e.noContent,!!E&&e.withStartSlot,(p||!!I)&&e.withEndSlot,r&&e.withGrouped,h&&e[h],e[B?"primary":y],e[B?"gray":f],e[S],L,!v&&e.disableActiveStateStyles,R.isGrouped&&e.grouped,w&&e.wrap,T&&e.disableActiveOnTouch,k&&e.pills)}},854797:(e,t,r)=>{"use strict";r.d(t,{LightButtonContent:()=>m});var n=r(50959),o=r(497754),a=r(601198),l=r(959189),s=r(878112),c=r(602948),i=r(601538),u=r.n(i);const d=e=>n.createElement(s.Icon,{className:o(u().caret,e&&u().activeCaret),icon:c});function m(e){const{showCaret:t,iconOnly:r,ellipsis:s=!0,textWrap:c,tooltipText:i,children:m,endSlot:p,startSlot:h,isActiveCaret:g}=e;[p,t].filter((e=>!!e));return n.createElement(n.Fragment,null,h&&n.createElement("span",{className:o(u().slot,u().startSlot)},h),!(0,l.isIconOnly)(m,r)&&n.createElement("span",{className:o(u().content,!c&&u().nowrap,"apply-overflow-tooltip","apply-overflow-tooltip--check-children-recursively","apply-overflow-tooltip--allow-text"),"data-overflow-tooltip-text":i??(0,a.getTextForTooltip)(m)},c||s?n.createElement(n.Fragment,null,n.createElement("span",{className:o(!c&&s&&u().ellipsisContainer,c&&u().textWrapContainer,c&&s&&u().textWrapWithEllipsis)},m),n.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},m)):n.createElement(n.Fragment,null,m,n.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},m))),p&&n.createElement("span",{className:o(u().slot,u().endSlot)},p),t&&d(g))}},943158:(e,t,r)=>{"use strict";r.d(t,{LightButton:()=>d});var n=r(50959),o=r(380327),a=r(418920),l=r(854797),s=r(601538),c=r.n(s),i=r(188317),u=r.n(i);function d(e){const{isGrouped:t}=n.useContext(o.ControlGroupContext),{reference:r,className:s,isSelected:i,children:d,iconOnly:m,ellipsis:p,showCaret:h,forceDirection:g,endSlot:f,startSlot:y,color:S,variant:v,size:b,enableActiveStateStyles:x,typography:w,textWrap:k=!1,maxLines:C,style:E={},isPills:I,isActive:L,tooltipText:R,role:T,...B}=e,N=k?C??2:1,M=N>0?{...E,"--ui-lib-light-button-content-max-lines":N}:E;return n.createElement("button",{...B,className:(0,a.useLightButtonClasses)({...u(),...c()},{className:s,isSelected:i,
children:d,iconOnly:m,showCaret:h,forceDirection:g,endSlot:f,startSlot:y,color:S,variant:v,size:b,enableActiveStateStyles:x,typography:w,textWrap:k,isPills:I,isActive:L},t),ref:r,style:M,role:T},n.createElement(l.LightButtonContent,{showCaret:h,isActiveCaret:h&&(I||L||i),iconOnly:m,ellipsis:p,textWrap:k,tooltipText:R,endSlot:f,startSlot:y},d))}},108937:(e,t,r)=>{"use strict";r.d(t,{getBlockStyleClasses:()=>s,getLogoStyleClasses:()=>c});var n=r(497754),o=r(92318),a=r(78217),l=r.n(a);function s(e,t){return n(l().pair,l()[e],t)}function c(e,t=2,r=!0){return n(l().logo,l()[e],l().skeleton,o.skeletonTheme.wrapper,!r&&l().empty,1===t&&n(o.skeletonTheme.animated))}},601198:(e,t,r)=>{"use strict";r.d(t,{getTextForTooltip:()=>l});var n=r(50959);const o=e=>(0,n.isValidElement)(e)&&Boolean(e.props.children),a=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",l=e=>Array.isArray(e)||(0,n.isValidElement)(e)?n.Children.toArray(e).reduce(((e,t)=>{let r="";return r=(0,n.isValidElement)(t)&&o(t)?l(t.props.children):(0,n.isValidElement)(t)&&!o(t)?"":a(t),e.concat(r)}),"").trim():a(e)},420066:(e,t,r)=>{"use strict";function n(){return window.configurationData?.exchanges?.map((e=>({...e,country:"",providerId:"",flag:""})))??[]}r.d(t,{getExchanges:()=>n})},868333:(e,t,r)=>{"use strict";var n;r.d(t,{LogoSize:()=>n,getLogoUrlResolver:()=>l}),function(e){e[e.Medium=0]="Medium",e[e.Large=1]="Large"}(n||(n={}));class o{getSymbolLogoUrl(e){return e}getCountryFlagUrl(){return""}getCryptoLogoUrl(e){return e}getProviderLogoUrl(e){return e}getSourceLogoUrl(e){return e}getBlockchainContractLogoUrl(e){return e}}let a;function l(){return a||(a=new o),a}},789749:(e,t,r)=>{"use strict";r.d(t,{CachedLogo:()=>m});var n=r(50959),o=r(497754),a=r.n(o),l=r(361701),s=r(439067),c=r(41192),i=r(35089),u=r(855393),d=r(979566);function m(e){const{className:t,placeholderLetter:r,url1:o,url2:m,size:g="xxxsmall"}=e,f=(0,n.useRef)(null),y=(0,n.useRef)(null),S=(0,n.useRef)(null),v=(0,n.useRef)(null),b=(0,n.useRef)(null),x=(0,n.useRef)(null);return(0,u.useIsomorphicLayoutEffect)((()=>{const e=void 0===o?[]:void 0===m?[o]:[o,m],t=x.current=(r=e,Promise.all(r.map((e=>(0,i.getImage)(`symbol_logo_${e}`,e,h).then((e=>e.cloneNode()))))));var r;t.catch((()=>[])).then((e=>{if(t===x.current)switch(e.length){case 0:S.current?.classList.add(d.hidden),y.current?.classList.add(l.hiddenCircleLogoClass),f.current?.classList.remove(l.hiddenCircleLogoClass);break;case 1:p(y.current,e[0]),S.current?.classList.add(d.hidden),y.current?.classList.remove(l.hiddenCircleLogoClass),f.current?.classList.add(l.hiddenCircleLogoClass);break;case 2:p(v.current,e[0]),p(b.current,e[1]),S.current?.classList.remove(d.hidden),y.current?.classList.add(l.hiddenCircleLogoClass),f.current?.classList.add(l.hiddenCircleLogoClass)}}))}),[o,m]),n.createElement("span",{className:a()(t,d.container)},n.createElement("span",{ref:S,className:a()(d.pairContainer,d.hidden)},n.createElement("span",{className:(0,c.getBlockStyleClasses)(g)},n.createElement("span",{ref:b,className:a()(d.logo,(0,
c.getLogoStyleClasses)(g))}),n.createElement("span",{ref:v,className:a()(d.logo,(0,c.getLogoStyleClasses)(g))}))),n.createElement("span",{ref:y,className:a()(d.logo,l.hiddenCircleLogoClass,(0,s.getStyleClasses)(g))}),n.createElement("span",{ref:f,className:a()(d.logo,(0,s.getStyleClasses)(g))},n.createElement(l.CircleLogo,{size:g,placeholderLetter:r})))}function p(e,t){e&&(e.innerHTML="",e.appendChild(t))}function h(e){e.crossOrigin="",e.decoding="async"}},231862:(e,t,r)=>{"use strict";r.d(t,{DialogSearch:()=>d});var n=r(50959),o=r(497754),a=r.n(o),l=r(609838),s=r(878112),c=r(606347),i=r(654313),u=r(692335);function d(e){const{children:t,isMobile:o,renderInput:d,onCancel:p,containerClassName:h,inputContainerClassName:g,iconClassName:f,cancelTitle:y=l.t(null,void 0,r(904543)),...S}=e;return n.createElement("div",{className:a()(u.container,o&&u.mobile,h)},n.createElement("div",{className:a()(u.inputContainer,o&&u.mobile,g,p&&u.withCancel)},d||n.createElement(m,{isMobile:o,...S})),t,n.createElement(s.Icon,{className:a()(u.icon,o&&u.mobile,f),icon:o?i:c}),p&&(!o||""!==S.value)&&n.createElement("div",{className:a()(u.cancel,o&&u.mobile),onClick:p},y))}function m(e){const{className:t,reference:r,isMobile:o,value:l,onChange:s,onFocus:c,onBlur:i,onKeyDown:d,onSelect:m,placeholder:p,activeDescendant:h,...g}=e;return n.createElement("input",{...g,ref:r,type:"text",className:a()(t,u.input,o&&u.mobile),autoComplete:"off",role:"searchbox","data-role":"search",placeholder:p,value:l,onChange:s,onFocus:c,onBlur:i,onSelect:m,onKeyDown:d,"aria-activedescendant":h})}},6354:(e,t,r)=>{"use strict";r.d(t,{SymbolLogo:()=>u});var n=r(50959),o=r(497754),a=r.n(o),l=r(306858),s=r(868333),c=r(789749),i=r(394869);function u(e){const{logoId:t,baseCurrencyLogoId:r,currencyLogoId:o,placeholder:u,className:d,size:m="xsmall"}=e,p=(0,n.useMemo)((()=>{const e={logoid:t,"currency-logoid":o,"base-currency-logoid":r};return(0,l.removeUsdFromCryptoPairLogos)((0,l.resolveLogoUrls)(e,s.LogoSize.Medium))}),[t,o,r]);return n.createElement(c.CachedLogo,{key:m,className:a()(i.logo,d),url1:p[0],url2:p[1],placeholderLetter:u,size:m})}},56871:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchDialogContentItem:()=>x});var n=r(50959),o=r(497754),a=r.n(o),l=(r(609838),r(650151)),s=r(878112),c=r(440891),i=r(260598),u=r(170876),d=r(944080),m=r(111982),p=r(297265),h=r(702054),g=r(972535),f=r(871645),y=r(6354),S=r(32465),v=r(569533),b=r(292069);function x(e){const{dangerousTitleHTML:t,title:r,dangerousDescriptionHTML:o,description:x,searchToken:w,exchangeName:k,marketType:C,onClick:E,isSelected:I,isEod:L=!1,isActive:R=!1,isOffset:T=!1,invalid:B=!1,isHighlighted:N=!1,hideExchange:M=!1,hideMarkedListFlag:D=!1,onExpandClick:_,isExpanded:O,hoverComponent:P,country:A,providerId:F,source:V,source2:U,type:W,flag:Q,itemRef:K,onMouseOut:z,onMouseOver:Z,className:H,actions:q,reference:$,fullSymbolName:j,logoId:G,currencyLogoId:Y,baseCurrencyLogoId:X,shortName:J,hideLogo:ee=!1,exchangeTooltip:te,hideMarketType:re,isPrimary:ne}=e,{isSmallWidth:oe,isMobile:ae}=(0,l.ensureNotNull)((0,
n.useContext)(d.SymbolSearchItemsDialogContext)),le=Boolean(P),se=!B&&!M&&(ae||!le),ce=(0,p.useWatchedValueReadonly)({watchedValue:h.watchedTheme})===m.StdTheme.Dark?b.dark:b.light,ie=P,ue=c.enabled("show_symbol_logos"),de=c.enabled("show_exchange_logos"),me=ue||!1,pe=U?.description??V,he=U?.name??V;return n.createElement("div",{className:a()(b.itemRow,oe&&b.multiLine,N&&b.highlighted,N&&ce,I&&b.selected,R&&b.active,B&&b.invalid,!ae&&g.mobiletouch&&le&&b.hover,H),onClick:function(e){if(!E||e.defaultPrevented)return;e.preventDefault(),E(e)},"data-role":e["data-role"]||"list-item","data-active":R,"data-type":C,"data-name":"symbol-search-dialog-content-item",onMouseOut:z,onMouseOver:Z,ref:$},n.createElement("div",{ref:K,className:a()(b.itemInfoCell,b.cell,T&&b.offset)},n.createElement("div",{className:a()(b.actionHandleWrap,!me&&b.fixedWidth)},n.createElement(n.Fragment,null,!1,_&&n.createElement("div",{onClick:function(e){if(!_||e.defaultPrevented)return;e.preventDefault(),_(e)}},n.createElement(s.Icon,{className:a()(b.expandHandle,O&&b.expanded,I&&b.selected),icon:v})),me&&!T&&n.createElement("div",{className:a()(b.logo,Boolean(_)&&b.isExpandable)},n.createElement(y.SymbolLogo,{key:j,logoId:G,currencyLogoId:Y,baseCurrencyLogoId:X,placeholder:J?J[0]:void 0})))),n.createElement("div",{className:a()(b.description,me&&T&&b.offset)},r&&n.createElement("div",{className:a()(b.symbolTitle,R&&b.active,B&&b.invalid,!Boolean(o)&&b.noDescription,!g.mobiletouch&&"apply-overflow-tooltip"),"data-overflow-tooltip-text":r,"data-name":"list-item-title"},"string"==typeof r&&w?n.createElement(i.HighlightedText,{className:b.highlightedText,text:r,queryString:w,rules:(0,u.createRegExpList)(w)}):r,L&&n.createElement("span",{className:b.dataMode},"E")),!r&&t&&n.createElement("div",{className:a()(b.symbolTitle,R&&b.active,B&&b.invalid,!g.mobiletouch&&"apply-overflow-tooltip"),"data-name":"list-item-title","data-overflow-tooltip-text":(0,f.removeTags)(t)},n.createElement("span",{dangerouslySetInnerHTML:{__html:t}}),L&&n.createElement("span",{className:b.dataMode},"E")),oe&&ge())),!oe&&n.createElement("div",{className:a()(b.cell,b.descriptionCell,Boolean(ie)&&b.addition)},ge(),ie?n.createElement(ie,{...e,className:b.actions,onMouseOver:void 0,onMouseOut:void 0}):null),oe&&ie?n.createElement(ie,{...e,className:b.cell,onMouseOver:void 0,onMouseOut:void 0}):null,se&&n.createElement("div",{className:a()(b.exchangeCell,b.cell)},n.createElement("div",{className:a()(b.exchangeDescription)},!re&&n.createElement("div",{className:a()(b.marketType,R&&b.active)},C),n.createElement("div",{className:b.source},!1,"economic"===W&&pe&&he?n.createElement("div",{className:a()(b.exchangeName,R&&b.active,"apply-common-tooltip",b.narrow,re&&b.wide),title:pe},he):n.createElement("div",{className:a()(b.exchangeName,R&&b.active,te&&"apply-common-tooltip"),title:te},k))),de&&n.createElement("div",{className:b.flag},n.createElement(S.SymbolSearchFlag,{key:de?`${j}_exchange`:`${A}_${F}_${U?.id}_${W}_${Q}`,className:b.icon,country:A,providerId:F,
sourceId:"economic"===W&&U?U.id:void 0}))),n.createElement("div",{className:a()(b.cell,Boolean(q)&&b.actionsCell)},q));function ge(){if(B)return null;const e=a()(b.symbolDescription,R&&b.active,!g.mobiletouch&&"apply-overflow-tooltip apply-overflow-tooltip--allow-text");return x?n.createElement("div",{className:e},w?n.createElement(i.HighlightedText,{className:b.highlightedText,text:x,queryString:w,rules:(0,u.createRegExpList)(w)}):x):o?n.createElement("div",{"data-overflow-tooltip-text":(0,f.removeTags)(o),className:e,dangerouslySetInnerHTML:{__html:o}}):null}}},32465:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchFlag:()=>f});var n=r(50959),o=r(497754),a=r.n(o),l=r(111982),s=r(868333);const c=r.p+"mock-dark.16b5f3a431f502b03ae3.svg",i=r.p+"mock-light.d201313017eb2c1b989f.svg";function u(e){return e===l.StdTheme.Dark?c:i}var d=r(297265),m=r(702054),p=r(650151);const h=s.LogoSize.Medium;var g=r(506963);function f(e){const{country:t,tooltip:r,providerId:o,sourceId:l,className:c}=e,i=(0,d.useWatchedValueReadonly)({watchedValue:m.watchedTheme}),[f,y]=(0,n.useState)(function({country:e,providerId:t,sourceId:r}){const n=(0,s.getLogoUrlResolver)();return o=>{const a=e=>n.getProviderLogoUrl(e,h),l=[{value:r,resolve:a},{value:e,resolve:e=>n.getCountryFlagUrl(e.toUpperCase(),h)},{value:t,resolve:a}].find((({value:e})=>void 0!==e&&e.length>0));return void 0!==l?l.resolve((0,p.ensureDefined)(l.value)):u(o)}}({country:t,providerId:o,sourceId:l})(i));return n.createElement("img",{className:a()(c,"apply-common-tooltip",g.icon),crossOrigin:"","data-tooltip":r,src:f,onError:function(){y(u(i))}})}},542923:(e,t,r)=>{"use strict";r.d(t,{QualifiedSources:()=>n,qualifyProName:()=>l});var n,o=r(650151),a=r(440891);r(721167);function l(e){return e}!function(e){function t(e){return e.pro_name}function r(e){{const t=a.enabled("pay_attention_to_ticker_not_symbol")?e.ticker:e.name;return(0,o.ensureDefined)(t)}}e.fromQuotesSnapshot=function(e){return"error"===e.status?e.symbolname:e.values.pro_name},e.fromQuotesResponse=function(e){const{values:r,symbolname:n,status:o}=e;return"error"===o&&n?n:t(r)},e.fromQuotes=t,e.fromSymbolSearchResult=function(e,t){{const{ticker:r,symbol:n}=t??e;return a.enabled("pay_attention_to_ticker_not_symbol")?(0,o.ensureDefined)(r??n):(0,o.ensureDefined)(n)}},e.fromSymbolInfo=r,e.fromSymbolMessage=function(e,t){return"symbol_resolved"===t.method?r(t.params[1]):e}}(n||(n={}))},384327:(e,t,r)=>{"use strict";r.d(t,{createSearchSources:()=>s,filterSearchSources:()=>a,isAllSearchSourcesSelected:()=>o,splitSearchSourcesByGroup:()=>l});const n=[];function o(e){return""===e.value()}function a(e,t){return e.filter((e=>e.includes(t)))}function l(e){const t=new Map;e.forEach((e=>{t.has(e.group())?t.get(e.group()).push(e):t.set(e.group(),[e])}));for(const e of t.values()){e[0].group()!==ExchangeGroup.NorthAmerica&&e.sort(((e,t)=>e.name().toLowerCase()>t.name().toLowerCase()?1:-1))}return new Map([...t.entries()].sort((([e],[t])=>n.indexOf(e)-n.indexOf(t))))}function s(e,t){return t.map((t=>new e(t)))}},159223:(e,t,r)=>{"use strict"
;r.d(t,{SymbolSearchDialogBodyContext:()=>n});const n=r(50959).createContext(null)},944080:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchItemsDialogContext:()=>n});const n=r(50959).createContext(null)},798154:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchItemsDialog:()=>tt});var n,o,a,l=r(50959),s=r(497754),c=r.n(s),i=r(609838),u=r(440891),d=r(707002),m=r(721167);function p(e){const t=function(e){let t,r=0,n=0;for(let o=0;o<e.length;o++){const a=e[o];if("whitespace"!==a.type)switch(r){case 0:if("number"!==a.type||1!=+a.value)return[];r=1;break;case 1:if(1!==r||"divide"!==a.type)return[];r=2,t=o+1;break;case 2:if("openBrace"===a.type)r=3,n=1;else if((0,d.isBinaryOperator)(a.type))return[];break;case 3:"openBrace"===a.type?n++:"closeBrace"===a.type&&(n--,n<=0&&(r=2))}}return e.slice(t)}(e);return t.length?(0,d.factorOutBraces)(t):(0,d.factorOutBraces)((0,d.tokenize)("1/("+h(e)+")"))}function h(e){return e.reduce(((e,t)=>"symbol"===t.type&&d.symbolTokenEscapeRe.test(t.value)?e+`'${t.value}'`:e+t.value),"")}function g(e){const t=function(e){const t=(0,d.tokenize)(e),r=[];return t.forEach((e=>{if("symbol"!==e.type)return;const[t]=(0,d.parseToken)(e);t&&r.push(t)})),r}(e);if(1===t.length)return t[0]}function f(e,t,r){const n=e.value,[o,a]=y(e,r),l=(0,m.getSymbolFullName)(t),s=d.symbolTokenEscapeRe.test(l)?`'${l}'`:l;return[n.substring(0,a)+s+n.substring(a+o.length),a+s.length]}function y(e,t){const{value:r,selectionStart:n}=e,o=(0,d.tokenize)(t?r.toUpperCase():r),a=(0,d.getTokenAtPos)(o,n||0);return[a?.value||"",a?a.offset:r.length,o]}!function(e){e.Init="init",e.Var="var",e.Operator="operator"}(n||(n={})),function(e){e[e.Init=0]="Init",e[e.Div=1]="Div",e[e.Expression=2]="Expression",e[e.BracedExpression=3]="BracedExpression"}(o||(o={})),function(e){e.Stocks="stocks",e.Futures="futures",e.Funds="funds",e.Forex="forex",e.Crypto="bitcoin,crypto",e.Index="index",e.Bond="bond",e.Economic="economic",e.Options="options"}(a||(a={}));const S=["futures","forex","bond","economic","options"];var v=r(313739),b=r(996038),x=r(533408),w=r(878112),k=r(586240),C=r(671129),E=r(944080),I=r(231862),L=r(930202),R=r(442092);function T(e,t,r){return`source-item-${e}-${t}-${r}`}var B=r(384327),N=r(624517);function M(e){const{children:t,className:r}=e;return l.createElement("div",{className:c()(N.container,r)},l.createElement("div",{className:N.childrenWrapper},t))}var D=r(650151),_=r(840976),O=r(260598),P=r(170876),A=r(491540),F=r(906109);function V(e){const{searchSource:t,onClick:r,queryString:n,isFocused:o,id:a}=e,{symbolSearchContent:s,isAllSearchSourcesSelected:i,allSearchSourcesTitle:u,isMobile:d}=(0,_.useEnsuredContext)(E.SymbolSearchItemsDialogContext),p=s.currentSelectedSearchSource,h=(0,D.ensureNotNull)(p).value(),g=i(t),f=t.value()===h,y=(0,l.useMemo)((()=>(0,P.createRegExpList)(n)),[n]),S=t.description(),v=S&&!g,b=m.isSeparateSymbolSearchTabs&&g&&u?u:t.name(),x=c()(F.container,d?F.mobile:F.desktop,f&&F.selected,o&&F.focused,g&&F.allSelected,g&&F.libAllSelected,!g&&d&&F.bordered);return l.createElement("div",{
className:c()(!d&&F.wrap,g&&F.libAllSelected),onClick:r,id:a},l.createElement("div",{className:x},l.createElement("div",{className:F.iconWrap},!!g&&l.createElement(w.Icon,{className:c()(F.icon,F.allSelectedIcon),icon:A})),l.createElement("div",{className:F.textBlock},l.createElement("div",{className:c()(F.title,!v&&!d&&F.titleWithoutDesc)},l.createElement(O.HighlightedText,{className:c()(f&&F.highlighted),queryString:n,text:b,rules:y})),v&&l.createElement("div",{className:c()(F.description,"apply-overflow-tooltip")},l.createElement(O.HighlightedText,{className:F.highlighted,queryString:n,rules:y,text:S})))))}var U=r(297265),W=r(702054),Q=r(111982),K=r(159223),z=r(366619),Z=r(267562),H=r(696137);const q={emptyTextClassName:H.emptyText};function $(e){const{searchSources:t}=e,{setSelectedIndex:n,setSelectedSearchSource:o,setMode:a,isMobile:s,emptyState:u,autofocus:d}=(0,_.useEnsuredContext)(E.SymbolSearchItemsDialogContext),p=(0,U.useWatchedValueReadonly)({watchedValue:W.watchedTheme})===Q.StdTheme.Dark?z:Z,h=(0,C.useMatchMedia)(k["media-phone-vertical"]),[g,f]=(0,l.useState)(""),y=(0,l.useMemo)((()=>[{group:null,sources:(0,m.createGroupColumns)((0,B.filterSearchSources)(t,g),h?1:2)}]),[t,g,h]),S=((0,l.useMemo)((()=>({})),[]),(0,l.useRef)(null)),v=(0,l.useRef)(null),{focusedItem:b,activeDescendant:x,handleKeyDown:N,resetFocusedItem:D}=function(e,t,r){const[n,o]=(0,l.useState)(null),[a,s]=(0,l.useState)("");function c(t){const r=e[t.groupIndex].sources[t.col].length-1;if(t.row===r){const e=d(t.groupIndex+1);if(null===e)return;return t.col>0&&!u({...t,groupIndex:e,row:0})?void o({groupIndex:e,col:0,row:0}):void o({...t,groupIndex:e,row:0})}o({...t,row:t.row+1})}function i(t){if(0===t.row){const r=d(t.groupIndex-1,-1);if(null===r)return;const n=e[r].sources[t.col]?.length??0;return 0===n?void o({groupIndex:r,col:0,row:0}):void o({...t,groupIndex:r,row:n-1})}o({...t,row:t.row-1})}function u(t){return Boolean(e[t.groupIndex]?.sources[t.col]?.[t.row])}function d(t=0,r=1){const n=e.length;let o=(t+n)%n;for(;!u({groupIndex:o,col:0,row:0});)if(o=(o+r+n)%n,o===t)return null;return o}return(0,l.useEffect)((()=>{if(!r.current)return;if(!n)return void s("");const e=T(n.groupIndex,n.col,n.row),t=r.current.querySelector(`#${e}`);t?.scrollIntoView({block:"nearest"}),s(e)}),[n]),(0,l.useEffect)((()=>{o(null)}),[t]),{focusedItem:n,activeDescendant:a,handleKeyDown:function(a){if(!r.current)return;const l=(0,L.hashFromEvent)(a);if(32!==l&&13!==l)switch((0,R.mapKeyCodeToDirection)(l)){case"blockNext":if(a.preventDefault(),!n){const e=d();if(null===e)break;o({groupIndex:e,col:0,row:0});break}c(n);break;case"blockPrev":if(a.preventDefault(),!n)break;i(n);break;case"inlineNext":{if(!n||t)break;a.preventDefault();const r=e[n.groupIndex].sources.length;if(n.col===r-1||!u({...n,col:n.col+1})){c({...n,col:0});break}o({...n,col:n.col+1});break}case"inlinePrev":{if(!n||t)break;a.preventDefault();const r=e[n.groupIndex].sources.length;if(0===n.col){if(0!==n.row){i({...n,col:r-1});break}const t=d(n.groupIndex-1,-1);if(null===t)break
;const a=e[t].sources.length,l=e[t].sources[0].length;if(!u({groupIndex:t,col:a-1,row:l-1})){i(n);break}o({groupIndex:t,col:a-1,row:l-1});break}o({...n,col:n.col-1});break}}else{if(!n)return;a.preventDefault();const e=r.current.querySelector(`#${T(n.groupIndex,n.col,n.row)}`);e instanceof HTMLElement&&e.click()}},resetFocusedItem:()=>o(null)}}(y,h,v);(0,l.useLayoutEffect)((()=>{d&&S?.current?.focus()}),[]);const O=u?l.createElement(u,null):l.createElement(M,{className:H.noResultsDesktop},l.createElement(w.Icon,{icon:p,className:H.emptyIcon}),l.createElement("div",{className:H.emptyText},i.t(null,void 0,r(953182)))),P=!(y.length&&y.every((e=>0===e.sources.length)));return l.createElement(K.SymbolSearchDialogBodyContext.Provider,{value:q},l.createElement(I.DialogSearch,{placeholder:i.t(null,void 0,r(508573)),onChange:function(e){D(),f(e.target.value),v&&v.current&&(v.current.scrollTop=0)},reference:S,onKeyDown:N,onBlur:D,"aria-activedescendant":x}),P?l.createElement("div",{ref:v,className:c()(H.contentList,!s&&H.contentListDesktop),onTouchStart:function(){S.current?.blur()}},y.map(((e,t)=>{const{group:r,sources:n}=e;return 0===n.length?l.createElement(l.Fragment,{key:r}):l.createElement(l.Fragment,{key:r},!1,l.createElement("div",{className:c()(H.searchSourceItemsContainer,!s&&H.searchSourceItemsContainerDesktop,h&&H.oneColumn)},n.map(((e,r)=>l.createElement("div",{key:`${t}-${r}`,className:H.column},e.map(((e,n)=>l.createElement(V,{id:T(t,r,n),isFocused:!!b&&(b.groupIndex===t&&b.col===r&&b.row===n),key:e.value(),searchSource:e,queryString:g,onClick:A.bind(null,e)}))))))))}))):O);function A(e){o(e),a("symbolSearch"),n(-1)}}var j,G,Y,X,J=r(632227),ee=r(61121);r(844287);function te(e){return e.hasOwnProperty("exchange")}async function re(e){{const t=await async function(e){return new Promise((t=>{window.ChartApiInstance.searchSymbols(e.text||"",e.exchange||"",e.type||"",(e=>{t(e)}),e.searchInitiationPoint??"symbolSearch")}))}(e);return{symbols:t,symbols_remaining:0}}}!function(e){e.SourceId="source_id",e.EconomicCategory="economic_category",e.SearchType="search_type",e.Sector="sector",e.Product="product",e.Centralization="centralization",e.OnlyHasOptions="only_has_options"}(j||(j={})),function(e){e.SymbolSearch="symbolSearch",e.Watchlist="watchlist",e.Compare="compare",e.IndicatorInputs="indicatorInputs"}(G||(G={})),function(e){e[e.Prod=0]="Prod",e[e.Local=1]="Local"}(Y||(Y={})),function(e){e[e.Paginated=0]="Paginated",e[e.NoLimit=1]="NoLimit"}(X||(X={}));new Map([].map((({value:e,search_type:t})=>[e,t])));var ne=r(673026),oe=r(32133),ae=r(180185),le=r(155352),se=r(900486),ce=r(481574),ie=r(935119),ue=r(132617),de=r(569135),me=r(863861),pe=r(410070);function he(e){const{state:t,update:r}=e,{searchRef:n,forceUpdate:o,upperCaseEnabled:a}=(0,D.ensureNotNull)((0,l.useContext)(E.SymbolSearchItemsDialogContext)),s=(0,d.tokenize)(n.current?.value),c=function(e){const t={braceBalance:0,currentState:"var",warnings:[],errors:[]};if(!u.enabled("show_spread_operators"))return t;let r="init";const n=[];for(let o=0;o<e.length;o++){
const a=e[o];if("whitespace"!==a.type){if("incompleteSymbol"===a.type||"incompleteNumber"===a.type){const r=o!==e.length-1,n={status:r?"error":"incomplete",reason:"incomplete_token",offset:a.offset,token:a};if(r?t.errors.push(n):t.warnings.push(n),r)continue}switch(a.type){case"symbol":case"number":if("var"===r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}r="var";break;case"plus":case"minus":case"multiply":case"divide":case"power":if("var"!==r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}r="operator";break;case"openBrace":if("var"===r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}n.push(a),r="init";break;case"closeBrace":if("var"!==r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}n.pop()||t.errors.push({status:"error",reason:"unbalanced_brace",offset:a.offset,token:a}),r="var";break;case"unparsed":t.errors.push({status:"error",reason:"unparsed_entity",offset:a.offset,token:a})}}}for(t.braceBalance=n.length,"var"!==r&&t.warnings.push({status:"incomplete",token:e[e.length-1]});n.length;){const e=n.pop();e&&t.warnings.push({status:"incomplete",reason:"unbalanced_brace",offset:e.offset,token:e})}return t.currentState=r,t}(s);let i=[{icon:se,insert:"/",type:"binaryOp",name:"division"},{icon:ce,insert:"-",type:"binaryOp",name:"subtraction"},{icon:ie,insert:"+",type:"binaryOp",name:"addition"},{icon:ue,insert:"*",type:"binaryOp",name:"multiplication"}];return u.enabled("hide_exponentiation_spread_operator")||(i=i.concat([{icon:de,insert:"^",type:"binaryOp",name:"exponentiation"}])),u.enabled("hide_reciprocal_spread_operator")||(i=i.concat([{icon:me,type:"complete",name:"1/x",callback:()=>{!n.current||c.errors.length||c.warnings.length||(n.current.value=h(p(s)),o())}}])),l.createElement("div",{className:pe.actions},i.map((e=>l.createElement(le.ToolWidgetButton,{className:pe.actionButton,icon:e.icon,key:e.name,isDisabled:ge(e,c),onClick:()=>function(e){if(!ge(e,c)){if(e.insert&&n.current){const l=n.current.value+e.insert;n.current.value=l,n.current.setSelectionRange(l.length,l.length);const[s,,c]=y(n.current,a);t.current&&(t.current.selectedIndexValue=-1,t.current.searchSpreadsValue=(0,d.isSpread)(c),t.current.searchTokenValue=s),o(),r()}e.callback&&e.callback(),n.current?.focus(),(0,oe.trackEvent)("GUI","SS",e.name)}}(e)}))))}function ge(e,t){let r=!1;if(!t.errors.length)switch(e.type){case"binaryOp":r="var"===t.currentState;break;case"openBrace":r="var"!==t.currentState;break;case"closeBrace":r="var"===t.currentState&&t.braceBalance>0;break;case"complete":r=!t.errors.length&&!t.warnings.length}return!r}var fe=r(132455),ye=r(584952),Se=r(102478),ve=r(898237),be=r(515783),xe=r(552019),we=r(992244);const ke=(0,m.getDefaultSearchSource)();function Ce(e){
const{mode:t,setMode:n,searchRef:o,cachedInputValue:a,setSelectedIndex:s,setSelectedSearchSource:u,isAllSearchSourcesSelected:d,allSearchSourcesTitle:p,upperCaseEnabled:h,symbolSearchContent:g}=(0,_.useEnsuredContext)(E.SymbolSearchItemsDialogContext),f=g.currentSelectedSearchSource,y=(0,D.ensureNotNull)(f),S="symbolSearch"===t,v=d(y),b=m.isSeparateSymbolSearchTabs&&v&&p?p:y.name(),x=(0,l.useCallback)((()=>{if(m.isSeparateSymbolSearchTabs&&!v&&ke)return u(ke),s(-1),void o.current?.focus();o.current&&(a.current=h?o.current.value.toUpperCase():o.current.value),n("exchange")}),[v,o,h,n,u]);return m.isSeparateSymbolSearchTabs?S?l.createElement(ve.LightButton,{onClick:x,isPills:!v,size:"xsmall",variant:v?"ghost":"quiet-primary",showCaret:v,endSlot:v?void 0:l.createElement(w.Icon,{icon:xe}),enableActiveStateStyles:!1,className:c()(we.button,!v&&we.withFlag),tabIndex:-1,"data-name":"sources-button"},l.createElement("div",{className:we.buttonContent},null,l.createElement("span",null,b))):null:S?l.createElement("div",{className:c()(we.flagWrap,"apply-common-tooltip",!v&&we.withFlag),title:i.t(null,void 0,r(757640)),onClick:x,"data-name":"sources-button"},v&&l.createElement(w.Icon,{className:we.icon,icon:A}),null,l.createElement("div",{className:c()(we.title)},b),l.createElement(be.ToolWidgetCaret,{className:we.caret,dropped:!1})):null}var Ee=r(306591);function Ie(e){const{brokerButton:t=null}=e,{isSmallWidth:n,selectedFilterValues:o,setSelectedFilterValues:a,setSelectedIndex:s,isMobile:u,searchRef:d,symbolSearchContent:p}=(0,_.useEnsuredContext)(E.SymbolSearchItemsDialogContext),h=p.tabSelectFilters;return m.isSeparateSymbolSearchTabs?l.createElement("div",{className:c()(Ee.wrap,Ee.small,Ee.newStyles,u&&Ee.mobile)},t&&l.createElement("div",{className:Ee.brokerWrap},t),p.canChangeExchange&&l.createElement("div",{className:Ee.filterItem},l.createElement(Ce,null)),h&&h.map((e=>{const{id:t,options:r,label:n}=e,c=r.find((e=>e.value===FILTER_DEFAULT_VALUE));if(!c)throw new Error("There must be default filter value in filter definition");const i=r.find((e=>e.value===o[p.currentSymbolType]?.[t]))||c;return l.createElement("div",{key:t,className:Ee.filterItem},l.createElement(SymbolSearchSelectFilter,{selectedOption:i,defaultOption:c,options:r,onSelect:e=>{a(p.currentSymbolType,{[t]:e.value}),trackEvent("New SS",p.currentSymbolType,null===e.value?e.analyticsLabel:e.value),s(-1),d.current?.focus()},label:n,isMobile:u,"data-name":t}))}))):l.createElement("div",{className:c()(Ee.wrap,n&&Ee.small)},l.createElement("div",{className:Ee.item},l.createElement("div",{className:Ee.text},n?i.t(null,void 0,r(774007)):i.t(null,void 0,r(595481)))),l.createElement("div",{className:Ee.item},!n&&l.createElement("div",{className:Ee.text},i.t(null,void 0,r(178734))),p.canChangeExchange&&l.createElement("div",{className:Ee.exchange},l.createElement(Ce,null))))}var Le=r(431520),Re=r(44458);function Te(e){
const{onTouchMove:t,listRef:r,className:n,listWrapRef:o,virtualListKey:a,items:s,getItemSize:i,hideFeed:u,canLoadMore:d,onLoadMoreSymbols:p}=e,{mode:h,isSmallWidth:g,handleListWidth:f}=(0,_.useEnsuredContext)(E.SymbolSearchItemsDialogContext),[y,S]=(0,l.useState)(null),v=(0,Se.useResizeObserver)((function([e]){S(e.contentRect.height),f(e.contentRect.width)})),b=(0,l.useCallback)((e=>{const{index:t,style:r}=e;return l.createElement("div",{style:r},s[t])}),[s]),x=(0,l.useCallback)((e=>(0,D.ensure)(s[e].key)),[s]),w="watchlist"===h&&null!==y;return l.createElement("div",{className:c()(Re.wrap,w&&Re.watchlist,u&&Re.noFeed,u&&m.isSeparateSymbolSearchTabs&&Re.newStyles,n),onTouchMove:t,ref:v},l.createElement("div",{ref:o,className:c()(Re.scrollContainer,u&&Re.noFeed)},w?l.createElement(ye.VariableSizeList,{key:a,ref:r,className:Re.listContainer,width:"100%",height:(0,D.ensureNotNull)(y),itemCount:s.length,itemSize:i,children:b,itemKey:x,overscanCount:20,direction:(0,Le.isRtl)()?"rtl":"ltr"}):l.createElement(l.Fragment,null,l.createElement("div",{className:c()(Re.listContainer,g&&Re.multiLineItemsContainer)},!m.isSeparateSymbolSearchTabs&&l.createElement(Ie,null),...s,!1))))}var Be=r(965800),Ne=r(56871),Me=r(686870),De=r(976717);const _e=u.enabled("hide_image_invalid_symbol");function Oe(e){const{otherSymbolsCount:t,onChangeSymbolTypeFilter:r,onResetFilters:n,onListTouchMove:o,brokerTitle:a,brokerLogoInfo:c,isBrokerActive:i,onBrokerToggle:u,listRef:d,listWrapRef:p,onLoadMoreSymbols:h,canLoadMore:g}=e,{mode:f,isMobile:y,selectedSymbolType:S,symbolTypes:v,feedItems:b,contentItem:x,emptyState:w=Pe,symbolSearchContent:k,symbolSearchState:C}=(0,_.useEnsuredContext)(E.SymbolSearchItemsDialogContext),I=a?l.createElement(BrokerButton,{brokerTitle:a,isActive:i,onToggle:u,onKeyDown:e=>{const t=(0,L.hashFromEvent)(e);t!==9+L.Modifiers.Shift&&9!==t&&e.stopPropagation()},logoInfo:c}):null,R=v.map((e=>({id:e.value,children:e.name}))),T="symbolSearch"===f&&["good","loadingWithPaginated"].includes(C),B=x??Ne.SymbolSearchDialogContentItem,N=(0,l.useMemo)((()=>b.map((e=>l.createElement(B,{...e,searchToken:k.token})))),[b]);return l.createElement(l.Fragment,null,"symbolSearch"===f&&l.createElement(l.Fragment,null,l.createElement("div",{className:s(De.bubblesContainer,!y&&I&&De.withButton,y&&De.mobile)},v.length>0&&l.createElement(Me.RoundButtonTabs,{id:"symbol-search-tabs",isActive:e=>e.id===S,onActivate:r,overflowBehaviour:y?"scroll":"wrap",className:s(De.bubbles,y&&De.mobile,m.isSeparateSymbolSearchTabs&&(k.withFilters||I)&&!y&&De.withFilters),items:R},y?null:l.createElement("div",null,I)),!m.isSeparateSymbolSearchTabs&&y&&v.length>0&&a&&l.createElement("div",{className:De.brokerButtonWrap},I)),m.isSeparateSymbolSearchTabs&&l.createElement(Ie,{brokerButton:y?I:void 0})),l.createElement(Te,{listRef:d,listWrapRef:p,onTouchMove:o,items:N,getItemSize:()=>Fe,onLoadMoreSymbols:h,canLoadMore:g,hideFeed:!T}),"loading"===C&&l.createElement("div",{className:De.spinnerWrap
},l.createElement(fe.Spinner,null)),"symbolSearch"===f&&l.createElement(l.Fragment,null,!1,"empty"===C&&l.createElement(w,null)))}function Pe(e){const t=(0,Be.useTheme)()===Q.StdTheme.Dark?z:Z;return l.createElement(M,{className:De.noResultsDesktop},!_e&&l.createElement(w.Icon,{icon:t,className:De.emptyIcon}),l.createElement("div",{className:De.emptyText},i.t(null,void 0,r(476822))))}const Ae=(0,m.getDefaultSearchSource)(),Fe=52;function Ve(e){const{mode:t,setMode:n,setSelectedIndex:o,isMobile:a,selectedSearchSource:s,setSelectedSearchSource:p,isAllSearchSourcesSelected:h,selectedSymbolType:S,setSelectedSymbolType:v,symbolSearchContent:b,setSymbolSearchContent:x,searchRef:w,setSearchSpreads:k,showSpreadActions:C,selectedItem:L,forceUpdate:R,placeholder:T,initialScreen:B,footer:N,searchInput:M,upperCaseEnabled:D,externalInput:O,handleKeyDown:P,customSearchSymbols:A,filterDefinitions:F,filterQueryParams:V,searchSources:U,symbolSearchState:W,setSymbolSearchState:Q,onEmptyResults:z,searchInitiationPoint:Z}=(0,_.useEnsuredContext)(E.SymbolSearchItemsDialogContext),H=A??re,q=(0,l.useRef)(t);q.current=t;const $=(0,l.useRef)(new AbortController),[j,G]=(0,l.useState)(0),Y=(0,l.useRef)(0),[X,le]=(0,l.useState)(b.token),se=(0,l.useRef)(null),ce=(0,l.useRef)(null),ie=(0,l.useRef)({selectedIndexValue:-1,searchTokenValue:"",searchSpreadsValue:!0}),ue=(0,l.useRef)(null),de=(0,l.useRef)(null),me=(0,l.useRef)(null),{broker:pe=null,brokerId:ge,brokerTitle:fe,brokerLogoInfo:ye,isBrokerChecked:Se=!1,setIsBrokerChecked:ve=()=>{},unhideSymbolSearchGroups:be=""}={brokerId:void 0,brokerTitle:void 0,brokerLogoInfo:void 0};(0,l.useEffect)((()=>()=>{$.current.abort(),Fe(),Ve()}),[]),(0,l.useEffect)((()=>{w?.current&&le(w.current.value)}),[]),(0,l.useEffect)((()=>{const e=w.current;if(e)return e.addEventListener("input",Le),e.addEventListener("focus",_e),e.addEventListener("select",Ie),e.addEventListener("click",Ie),e.addEventListener("keyup",Pe),O&&P&&e.addEventListener("keydown",P),()=>{e&&(e.removeEventListener("input",Le),e.removeEventListener("focus",_e),e.removeEventListener("select",Ie),e.removeEventListener("click",Ie),e.removeEventListener("keyup",Pe),O&&P&&e.removeEventListener("keydown",P))}}),[P]),(0,l.useEffect)((()=>{Boolean(B)&&""===X.trim()?x((e=>{const t=Boolean(s&&U.length>1&&!(0,m.exchangeSelectDisabled)(S)),r=F?.[S];return{...e,tabSelectFilters:r,currentSymbolType:S,canChangeExchange:t,withFilters:Boolean(t||r?.length),token:X,currentTabAvailableSearchSources:U,currentSelectedSearchSource:s}})):(x((e=>({...e,symbolStartIndex:0}))),Te(X,S,s).then((()=>{se.current&&(se.current.scrollTop=0)})))}),[X,S,s,Se,B,V]),(0,l.useEffect)((()=>{if(!L||!w.current)return;if(!u.enabled("show_spread_operators"))return w.current.value=L.symbol,void R();const e=te(L)?L.exchange:L.parent.exchange;let t;t="contracts"in L&&L.contracts?.length?L.contracts[0]:L;const r={name:t.symbol,exchange:e,prefix:t.prefix,fullName:t.full_name},[n,o]=f(w.current,r,D);w.current.value=n,w.current.setSelectionRange(o,o),R()}),[L])
;const xe=B??"div",we=Boolean(B)&&"symbolSearch"!==t,ke=M??I.DialogSearch,Ce=(0,l.useMemo)((()=>({listRef:ce,resetRecommends:Me,updateRecommends:Te,searchToken:X,emptyTextClassName:De.emptyText,isBrokerChecked:Se,symbolSearchState:W,currentMode:q})),[ce,X,Se,W,q,V]);return l.createElement(K.SymbolSearchDialogBodyContext.Provider,{value:Ce},!(O&&"symbolSearch"===t)&&l.createElement(ke,{reference:w,className:c()(De.search,D&&De.upperCase),placeholder:T||i.t(null,void 0,r(508573))},C&&l.createElement(he,{state:ie,update:Re})),we?l.createElement(xe,null):l.createElement(Oe,{otherSymbolsCount:j,onListTouchMove:function(){w.current?.blur()},onChangeSymbolTypeFilter:function(e){const{id:t}=e;v(t),o(-1)},onResetFilters:function(){m.isSeparateSymbolSearchTabs?"resetFilter"===W?v((0,m.getAllSymbolTypesValue)()):Ae&&p(Ae):(v((0,m.getAllSymbolTypesValue)()),Ae&&p(Ae));ve(!1),a||w.current?.focus()},brokerTitle:fe,brokerLogoInfo:ye,isBrokerActive:Se,onBrokerToggle:ve,listRef:ce,listWrapRef:se,onLoadMoreSymbols:void 0,canLoadMore:void 0}),N);function Ee(){if(!w.current)return;const[e,t,r]=y(w.current,D);Y.current=t,ie.current={selectedIndexValue:-1,searchSpreadsValue:(0,d.isSpread)(r),searchTokenValue:e},ue.current||(ue.current=setTimeout(Re,0))}function Ie(){if(!w.current)return;const[,e]=y(w.current,D);e!==Y.current&&Ee()}function Le(){u.enabled("show_spread_operators")?Ee():w.current&&(ie.current={selectedIndexValue:-1,searchSpreadsValue:!1,searchTokenValue:w.current.value},ue.current||(ue.current=setTimeout(Re,0)))}function Re(){const{selectedIndexValue:e,searchTokenValue:t,searchSpreadsValue:r}=ie.current;ue.current=null,(0,J.unstable_batchedUpdates)((()=>{k(r),o(e),le(D?t.toUpperCase():t)}))}async function Te(e,t,r,n){try{"noop"===W?Q("loading"):n?Q("loadingWithPaginated"):(Fe(),Ve(),de.current=setTimeout((()=>{const r=Boolean(s&&U.length>1&&!(0,m.exchangeSelectDisabled)(t)),n=F?.[t];x({token:e,canChangeExchange:r,tabSelectFilters:n,withFilters:Boolean(r||n?.length),currentSymbolType:t,currentSelectedSearchSource:s,currentTabAvailableSearchSources:U,renderSymbolSearchList:[],symbolsRemaining:0,symbolStartIndex:0}),Q("loading")}),500)),Ue();(0,m.getAllSymbolTypesValue)();const o=!1;let a;if(Se&&pe){a=(await(0,ee.respectAbort)($.current.signal,pe.accountMetainfo())).prefix}const l=u.enabled("show_spread_operators")?g(e)??a??r?.getRequestExchangeValue():s?.getRequestExchangeValue(),c=g(e)?void 0:(r||s)?.getRequestCountryValue(),[i,d]=await Promise.all([Ne($.current.signal,e,t,r,l,c,n),o&&!n?getRecent():Promise.resolve([])]),p=d.filter((e=>l?e.exchange?.toLowerCase()===l.toLowerCase():!c||e.country?.toLowerCase()===c.toLowerCase())),h=new Set(p.map((e=>`${e.exchange}_${e.symbol}`))),f=i.symbols.filter((e=>!h.has(`${e.exchange}_${e.symbol}`)));let y=function(e,t=window.ChartApiInstance.symbolsGrouping()){const r={},n=[];for(let o=0;o<e.length;++o){const a=e[o];if(a.prefix||Array.isArray(a.contracts))return e;const l=t[a.type];if(void 0===l){n.push(a);continue}const s=l.exec(a.symbol);if(s){const e=s[1];let t
;r.hasOwnProperty(e)?t=r[e]:(t=n.length,r[e]=t,n.push({type:a.type,symbol:e,exchange:a.exchange,description:a.description,full_name:a.exchange+":"+e,contracts:[]})),n[t].contracts?.push(a)}else n.push(a)}return n}([...p,...f]);if(n&&(y=[...b.renderSymbolSearchList,...y]),!y.length)return x((r=>{const n=Boolean(s&&U.length>1&&!(0,m.exchangeSelectDisabled)(t)),o=F?.[t];return{...r,canChangeExchange:n,tabSelectFilters:o,token:e,symbolsRemaining:0,withFilters:Boolean(n||o?.length),currentSymbolType:t,currentSelectedSearchSource:s,currentTabAvailableSearchSources:U}})),Fe(),Q("empty"),void Be();Fe(),x((r=>{const n=Boolean(s&&U.length>1&&!(0,m.exchangeSelectDisabled)(t)),o=F?.[t];return{...r,canChangeExchange:n,tabSelectFilters:o,renderSymbolSearchList:y,token:e,symbolsRemaining:i.symbols_remaining,withFilters:Boolean(n||o?.length),currentSymbolType:t,currentSelectedSearchSource:s,currentTabAvailableSearchSources:U,symbolStartIndex:r.symbolStartIndex+i.symbols.length}})),Q("good")}catch(e){(0,ee.skipAbortError)(e)}}function Be(){z&&(me.current=setTimeout((()=>z()),1e3))}async function Ne(e,t,r,n,o,a,l){const s={serverHighlight:!1,text:u.enabled("show_spread_operators")?(0,d.shortName)(t):w.current?.value,exchange:o,country:a,type:r,lang:window.language||"",sortByCountry:void 0,brokerId:ge,onlyTradable:Boolean(ge)&&Se,unhideSymbolSearchGroups:be,signal:e,start:l,filterQueryParams:V,searchInitiationPoint:Z},c=(0,ne.getSearchRequestDelay)();return void 0!==c&&await(0,ee.delay)(e,c),H(s)}function Me(){Ue(),Q("empty"),le(""),k(!1),x((e=>({...e,symbolStartIndex:0}))),Fe()}function _e(){"watchlist"===q.current&&(n("symbolSearch"),(0,oe.trackEvent)("Watchlist","Mobile SS","Go to SS page"))}function Pe(e){switch((0,ae.hashFromEvent)(e)){case 37:case 39:Ie()}}function Fe(){de.current&&clearTimeout(de.current)}function Ve(){me.current&&clearTimeout(me.current)}function Ue(){$.current.abort(),$.current=new AbortController}}var Ue=r(380132),We=r(603422),Qe=r(542923),Ke=r(870122);function ze(e){const[t,r]=(0,l.useState)((()=>{const{defaultSearchSource:t,searchSources:r}=e,n=Ke.getValue("symboledit.exchangefilter","");return r.find((e=>e.value()===n))||t}));return[t,(0,l.useCallback)((e=>{var t;r(e),t=e,Ke.setValue("symboledit.exchangefilter",t.value())}),[])]}function Ze(e){const[t,r]=(0,l.useState)((()=>{if(1===e.types.length)return e.types[0].value;const t=Ke.getValue("symboledit.filter",(0,m.getAllSymbolTypesValue)());return e.types.find((e=>e.value===t))?t:(0,m.getAllSymbolTypesValue)()}));return[t,(0,l.useCallback)((e=>{var t;r(e),t=e,Ke.setValue("symboledit.filter",t)}),[])]}var He=r(585938),qe=r(626800),$e=r(44258),je=r(111706),Ge=r(963748);const Ye=!1,Xe=(0,m.getAvailableSearchSources)(),Je=(0,m.getDefaultSearchSource)(),et=u.enabled("uppercase_instrument_names");function tt(e){
const{onClose:t,symbolTypeFilter:n,initialMode:o,defaultValue:a="",showSpreadActions:s,hideMarkedListFlag:c,selectSearchOnInit:d=!0,onSearchComplete:p,dialogTitle:h=i.t(null,void 0,r(751165)),placeholder:g,fullscreen:y,initialScreen:x,wrapper:w,dialog:k,contentItem:C,footer:I,searchInput:L,emptyState:T,autofocus:N,dialogWidth:M,onKeyDown:D,searchSourcesScreen:_,customSearchSymbols:O,isDisableFiltering:P,disableRecents:A,shouldReturnFocus:F,onSymbolFiltersParamsChange:V,onEmptyResults:U,enableOptionsChain:W,searchInitiationPoint:Q="symbolSearch"}=e,K=(0,l.useMemo)((()=>{if(P)return[];const t=e.symbolTypes??(0,m.getAvailableSymbolTypes)();return n?n(t):t}),[]),z=void 0!==e.input,Z=P?[]:Xe,[H,q]=(0,l.useState)((()=>st(a,W)?"options":o)),[j,G]=(0,l.useState)((()=>st(a,W))),[Y,X]=(0,l.useState)((()=>null)),J=(0,l.useRef)(function(e,t){const r=st(e,t);return(0,m.isOptionDefaultValue)(e)?r??e.value:e}(a,W)),[ee,ne]=ze({searchSources:Z,defaultSearchSource:Je}),[oe,le]=[],[se,ce]=Ze({types:K}),[ie,ue]=[{},()=>{}],[de,me]=(0,l.useState)(!1),[pe,he]=(0,l.useState)(-1),[ge,fe]=(0,l.useState)("noop"),ye=m.isSeparateSymbolSearchTabs?TAB_SELECT_FILTER_MAP:void 0,Se=m.isSeparateSymbolSearchTabs?oe?.[se]||Je:ee,ve=(0,l.useMemo)((()=>{if(!m.isSeparateSymbolSearchTabs)return Z;return Z.filter((e=>{const t=TAB_SOURCE_FILTER_MAP[se];if(!t)return!1;if(!se)return!0;const r=e.group();return r===ExchangeGroup.AllExchanges||r&&t.value.includes(r)}))}),[Z,se]),[be,xe]=(0,l.useState)((()=>{const e=Boolean(ee&&Xe.length>1&&!(0,m.exchangeSelectDisabled)(se)),t=ye?.[se];return{canChangeExchange:e,tabSelectFilters:t,withFilters:Boolean(e||t?.length),renderSymbolSearchList:[],token:J.current,symbolsRemaining:0,currentSymbolType:se,currentSelectedSearchSource:Se,currentTabAvailableSearchSources:ve,symbolStartIndex:0}})),we=(0,l.useCallback)((e=>{trackEvent("New SS",se,"Change sources"),le?.(se,e),xe((t=>({...t,currentSelectedSearchSource:e})))}),[se,xe]),ke=(0,l.useRef)(e.input??null),[Ce,Ee]=(0,l.useState)(!1),Ie=(0,He.useForceUpdate)(),[Re,Te]=(0,l.useState)(new Set),{broker:Be=null,brokerId:Ne,unhideSymbolSearchGroups:Me="",displayBrokerSymbol:De=!1}={brokerId:void 0};(0,l.useLayoutEffect)((()=>{!ke?.current||!z&&Boolean(ke.current?.value)||(z||"compare"===H||(ke.current.value=J.current),!N||z&&"symbolSearch"!==H||ke.current.focus())}),[H]),(0,l.useEffect)((()=>{ke?.current&&d&&N&&ke.current.select()}),[]);const _e=(0,l.useMemo)((()=>be.renderSymbolSearchList.reduce(((e,t)=>{const r=ot(t),n=Re.has(r);return e.push(t),n&&t.contracts&&e.push(...t.contracts.map((e=>({...e,parent:t})))),e}),[])),[be.renderSymbolSearchList,Re]),Oe=(0,l.useRef)(null);(0,l.useEffect)((()=>{-1!==pe&&Oe.current?.scrollIntoView({block:"nearest"})}),[pe,Oe]);const Pe=S.includes(se),Ae=(0,l.useMemo)((()=>_e.map(((e,t)=>{if(te(e)){const r=ot(e),n=e.contracts?Re.has(r):void 0,o=t===pe,a=be.renderSymbolSearchList.findIndex((t=>t.symbol===e.symbol&&t.exchange===e.exchange))+1;return{key:t,numberInList:a,id:r,title:nt(e,De),description:e.description,isOffset:!1,
onClick:pt.bind(null,e,a),providerId:e.provider_id,source:e.source,source2:e.source2,country:e.country?.toLocaleLowerCase(),type:e.type,exchangeName:null===e.exchange?void 0:e.exchange,exchangeTooltip:"",prefix:e.prefix||void 0,marketType:(0,We.marketType)(e.type,e.typespecs,!1),hideMarketType:Pe,isEod:e.params?.includes("eod")&&"economic"!==e.type,isYield:(0,$e.isYield)(e),isExpanded:n,onExpandClick:e.contracts?ht.bind(null,r):void 0,fullSymbolName:e.contracts?Qe.QualifiedSources.fromSymbolSearchResult(e,e.contracts[0]):Qe.QualifiedSources.fromSymbolSearchResult(e),itemRef:o?Oe:void 0,isSelected:t===pe,hideMarkedListFlag:c,item:e,logoId:e.logoid,currencyLogoId:e["currency-logoid"],baseCurrencyLogoId:e["base-currency-logoid"],shortName:(0,qe.safeShortName)(Qe.QualifiedSources.fromSymbolSearchResult(e)),currencyCode:e.currency_code,isPrimary:e.is_primary_listing}}{const{parent:r}=e,n=ot(r),o=t===pe,a=be.renderSymbolSearchList.findIndex((e=>e.symbol===r.symbol&&e.exchange===r.exchange))+1;return{key:t,numberInList:a,id:n+e.symbol,dangerousTitleHTML:nt(e,De),dangerousDescriptionHTML:`${r.description}`+(e.description?` (${e.description})`:""),isOffset:!0,isEod:e.params?.includes("eod"),isYield:(0,$e.isYield)(e),onClick:gt.bind(null,e.parent,e,a),providerId:r.provider_id,country:r.country?.toLowerCase(),type:r.type,exchangeName:null===r.exchange?void 0:r.exchange,exchangeTooltip:"",marketType:(0,We.marketType)(r.type,e.typespecs,!1),hideMarketType:Pe,fullSymbolName:Qe.QualifiedSources.fromSymbolSearchResult(e.parent,e),itemRef:o?Oe:void 0,isSelected:o,hideMarkedListFlag:c,item:e}}}))),[be.renderSymbolSearchList,Re,H,pe,D]),Fe=(0,l.useMemo)((()=>function(e,t,r){const n=t?.[e],o=new Map(n?.map((e=>[e.id,e.urlParam]))),a=r[e];let l;if(a){l={};for(const[e,t]of Object.entries(a)){const r=o.get(e);r&&(l[r]=t)}}return l}(se,ye,ie)),[se,ye,ie]),Ke=(0,l.useMemo)((()=>be.renderSymbolSearchList.slice(0,20).map((e=>e.contracts?Qe.QualifiedSources.fromSymbolSearchResult(e,e.contracts[0]):Qe.QualifiedSources.fromSymbolSearchResult(e)))),[be.renderSymbolSearchList]);(0,l.useEffect)((()=>{if(!V)return;const e=["resetFilter","resetTabFilter","empty"].includes(ge)?[]:Ke,t={...Fe,result_list:e};if(t.search_type||(t.search_type="bitcoin,crypto"===se?"crypto":se),!m.isSeparateSymbolSearchTabs)return t.exchange=Se?.getRequestCountryValue()??null,void V(t);if(se){const e=Se?.getRequestCountryValue()??null;e&&(t.country=e);const r=Se?.getRequestExchangeValue()??null;r&&(t.exchange=r)}V(t)}),[se,Fe,Ke,Se,ge]);const tt=(0,l.useMemo)((()=>{if(O)return O}),[se,O,Fe,W]),ct=k??lt,it=ct!==lt&&!z,ut=(e,r)=>({mode:H,setMode:q,selectedSearchSource:Se,setSelectedSearchSource:m.isSeparateSymbolSearchTabs?we:ne,isAllSearchSourcesSelected:B.isAllSearchSourcesSelected,allSearchSourcesTitle:m.isSeparateSymbolSearchTabs?TAB_SOURCE_FILTER_MAP[be.currentSymbolType]?.allSearchSourcesTitle:void 0,selectedSymbolType:se,setSelectedSymbolType:ce,selectedIndex:pe,setSelectedIndex:he,onClose:t,setSymbolSearchContent:xe,symbolSearchContent:be,searchRef:ke,
cachedInputValue:J,searchSpreads:de,setSearchSpreads:me,handleListWidth:ft,isSmallWidth:Ce,feedItems:Ae,isMobile:e,showSpreadActions:s,selectSearchOnInit:d,isTablet:r,selectedItem:_e[pe],forceUpdate:Ie,placeholder:g,initialScreen:x,toggleExpand:ht,openedItems:Re,onSubmit:vt,onSearchComplete:p,footer:I,symbolTypes:K,contentItem:C,searchInput:L,emptyState:T,autofocus:N,upperCaseEnabled:et,externalInput:z,handleKeyDown:it?void 0:St,customSearchSymbols:tt,searchSources:ve,filterDefinitions:ye,selectedFilterValues:ie,setSelectedFilterValues:ue,filterQueryParams:Fe,symbolSearchState:ge,setSymbolSearchState:fe,onEmptyResults:void 0,searchInitiationPoint:Q}),dt=_??$,mt=w??"div";return l.createElement(mt,null,l.createElement(v.MatchMediaMap,{rules:b.DialogBreakpoints},(({TabletSmall:e,TabletNormal:n})=>l.createElement(E.SymbolSearchItemsDialogContext.Provider,{value:ut(e,n)},l.createElement(ct,{..."exchange"===H?{title:i.t(null,void 0,r(328628)),dataName:"exchanges-search",render:()=>l.createElement(dt,{searchSources:be.currentTabAvailableSearchSources}),additionalHeaderElement:l.createElement(Ue.BackButton,{onClick:()=>q("symbolSearch"),className:Ge.backButton,size:"medium","aria-label":i.t(null,{context:"input"},r(148199)),preservePaddings:!0,flipIconOnRtl:(0,Le.isRtl)()}),additionalElementPos:"before"}:{title:h,dataName:"symbol-search-items-dialog",render:()=>l.createElement(Ve,null),additionalElementPos:"after"},shouldReturnFocus:F,fullScreen:y,onClose:t,onClickOutside:t,onKeyDown:it?void 0:St,isOpened:!0})))));function pt(e,t,r){if(e.contracts)return e.contracts.length?void gt(e,e.contracts[0],t,r):void ht(ot(e));gt(e,void 0,t,r)}function ht(e){const t=new Set(Re);t.has(e)?t.delete(e):t.add(e),Te(t)}function gt(e,r,n,o){const a=r||e,{exchange:l}=e;if(u.enabled("show_spread_operators")){const e={name:a.symbol,exchange:l,prefix:a.prefix,fullName:a.full_name};if(de)return yt(e),void Ie();if(ke.current&&ke.current.value.includes(","))return void yt(e)}bt([{resolved:!0,symbol:Qe.QualifiedSources.fromSymbolSearchResult(e,r),result:a}],n,o),t()}function ft(e){Ee("fixed"===M||e<=640)}function yt(e){if(!ke.current)return;const[t,r]=f(ke.current,e,et);ke.current.value=t,ke.current.setSelectionRange(r,r),ke.current.focus()}function St(e){if(e.target&&e.target!==ke.current)return;const r=(0,ae.hashFromEvent)(e);switch(r){case 13:e.preventDefault(),vt(!0);break;case 27:if(e.preventDefault(),"exchange"===H)return void q("symbolSearch");if("options"===H)return q("symbolSearch"),G(null),void X(null);t()}switch((0,R.mapKeyCodeToDirection)(r)){case"blockPrev":if(e.preventDefault(),0===pe||"good"!==ge)return;if(-1===pe)return void he(0);he(pe-1);break;case"blockNext":if(e.preventDefault(),pe===Ae.length-1||"good"!==ge)return;he(pe+1);break;case"inlinePrev":{if(-1===pe)return;const t=Ae[pe],{id:r,isOffset:n,onExpandClick:o}=t;if(!n&&r&&Re.has(r)&&Boolean(o)&&!Boolean(D)&&(e.preventDefault(),ht(r)),o)return void D?.(e,!0);break}case"inlineNext":{if(-1===pe)return;const t=Ae[pe],{id:r,isOffset:n,onExpandClick:o}=t
;if(n||!r||Re.has(r)||!Boolean(o)||Boolean(D)||(e.preventDefault(),ht(r)),o)return void D?.(e,!0);break}}D?.(e)}function vt(e){if(!ke.current)return;let r=ke.current.value;if(u.enabled("show_spread_operators")&&de&&r){const n=Ae[pe];if(n&&void 0!==n.isExpanded&&(n.onClick(),r=ke.current.value),r.includes(",")){return bt(at(r).map(rt),null),void(e&&t())}return bt([{symbol:et?r.toUpperCase():r,resolved:!1}],null),void(e&&t())}if(r.includes(","))return bt(at(r).map(rt),null),void(e&&t());if(-1!==pe){Ae[pe].onClick()}else if(u.enabled("allow_arbitrary_symbol_search_input")){const n=et?r.toUpperCase():r;if(n&&""!==n.trim()){const e=at(n);if(Ye||void 0===Ne||-1!==n.indexOf(":")){bt(e.map(rt),null)}else(function(e){let t=!1;return Promise.all(e.map((e=>-1!==e.indexOf(":")||t?Promise.resolve({symbol:e,resolved:!1}):(t=!0,async function(e){await(Be?.accountMetainfo());const t=void 0,r=await re({strictMatch:!0,serverHighlight:!1,text:e,lang:window.language||"",brokerId:Ne,onlyTradable:!0,unhideSymbolSearchGroups:Me,exchange:t});if(0!==r.symbols.length){const e=r.symbols[0],{contracts:t}=e,n=t&&t.length>0?t[0]:void 0,o=e.prefix||e.exchange,a=n?n.symbol:e.symbol;if(o&&a)return{symbol:Qe.QualifiedSources.fromSymbolSearchResult(e,n),resolved:!0,result:e}}return{symbol:e,resolved:!1}}(e)))))})(e).then((e=>bt(e,null)))}e&&t()}else if("empty"!==ge&&Ae.length>0){Ae[0].onClick()}}async function bt(e,t,r){const[{result:n,symbol:o,resolved:a}]=e,l=ke.current?.value,s=!r||(0,je.isKeyboardClick)(r);let c=de;void 0!==n&&te(n)&&(c="spread"===n.type),p(e,{symbolType:se,isKeyboardEvent:s,numberInList:t,inputValue:l,isSpread:c})}}function rt(e){return{symbol:et?e.toUpperCase():e,resolved:!1}}function nt(e,t){const{broker_symbol:r,symbol:n,description:o}=e;return`${"spread"===e.type?o:n}${t&&r?` (${r})`:""}`}function ot(e){return e.symbol+e.exchange+e.description}function at(e){return e.split(",").map((e=>e.trim())).filter((e=>""!==e))}function lt(e){const{isMobile:t,isTablet:r}=(0,_.useEnsuredContext)(E.SymbolSearchItemsDialogContext);return l.createElement(x.AdaptivePopupDialog,{...e,className:c()(Ge.dialog,!t&&(r?Ge.tabletDialog:Ge.desktopDialog)),backdrop:!0,draggable:!1})}function st(e,t){return null}},721167:(e,t,r)=>{"use strict";r.d(t,{createGroupColumns:()=>p,exchangeSelectDisabled:()=>m,getAllSymbolTypesValue:()=>d,getAvailableSearchSources:()=>i,getAvailableSymbolTypes:()=>u,getDefaultSearchSource:()=>c,getSymbolFullName:()=>s,isOptionDefaultValue:()=>g,isSeparateSymbolSearchTabs:()=>h});var n=r(609838),o=r(384327);class a{constructor(e){this._exchange=e}value(){return this._exchange.value}name(){return(0,o.isAllSearchSourcesSelected)(this)?n.t(null,void 0,r(234040)):this._exchange.name}description(){return this._exchange.desc}country(){return this._exchange.country}providerId(){return this._exchange.providerId}group(){return this._exchange.group}includes(e){return function(e,t){const r=t.toLowerCase(),{name:n,desc:o,searchTerms:a}=e
;return n.toLowerCase().includes(r)||o.toLowerCase().includes(r)||void 0!==a&&a.some((e=>e.toLowerCase().includes(r)))}(this._exchange,e)}getRequestExchangeValue(){return this._exchange.value}getRequestCountryValue(){}}var l=r(420066);function s(e){if(e.fullName)return e.fullName;let t;return t=e.prefix||e.exchange?(e.prefix||e.exchange)+":"+e.name:e.name,t.replace(/<\/?[^>]+(>|$)/g,"")}function c(){const e=i();return e.find(o.isAllSearchSourcesSelected)||e[0]||null}function i(){return(0,o.createSearchSources)(a,(0,l.getExchanges)())}function u(){return window.ChartApiInstance.supportedSymbolsTypes()}function d(){return""}function m(e){return!!h&&!TAB_SOURCE_FILTER_MAP[e]}function p(e,t=2){if(0===e.length)return[];if(1===t)return[e];const r=Math.floor(e.length/2)+e.length%2;return[e.slice(0,r),e.slice(r)].filter((e=>e.length>0))}const h=!1;function g(e){return"string"!=typeof e}},707002:(e,t,r)=>{"use strict";r.d(t,{factorOutBraces:()=>m,getTokenAtPos:()=>d,isBinaryOperator:()=>i,isSpread:()=>u,parseToken:()=>h,shortName:()=>p,symbolTokenEscapeRe:()=>a,tokenize:()=>c});var n,o=r(173850);!function(e){e.Symbol="symbol",e.IncompleteSymbol="incompleteSymbol",e.Number="number",e.IncompleteNumber="incompleteNumber",e.SeparatorPrefix="separatorPrefix",e.OpenBrace="openBrace",e.CloseBrace="closeBrace",e.Plus="plus",e.Minus="minus",e.Multiply="multiply",e.Divide="divide",e.Power="power",e.Whitespace="whitespace",e.Unparsed="unparsed"}(n||(n={}));const a=/[+\-/*]/,l={number:/\d+(?:\.\d*|(?![a-zA-Z0-9_!:.&]))|\.\d+/,incompleteNumber:/\./,symbol:/(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0370-\u1FFF_\u2E80-\uFFFF^])(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0020\u0370-\u1FFF_\u2E80-\uFFFF_!:.&])*|'.+?'/,incompleteSymbol:/'[^']*/,separatorPrefix:o.SEPARATOR_PREFIX,openBrace:"(",closeBrace:")",plus:"+",minus:"-",multiply:"*",divide:"/",power:"^",whitespace:/[\0-\x20\s]+/,unparsed:null},s=new RegExp(Object.values(l).map((e=>{return null===e?"":`(${"string"==typeof e?(t=e,t.replace(/[\^$()[\]{}*+?|\\]/g,"\\$&")):e.source})`;var t})).filter((e=>""!==e)).concat(".").join("|"),"g");function c(e){if(!e)return[];const t=[],r=Object.keys(l);let n;for(;n=s.exec(e);){let e=!1;for(let o=r.length;o--;)if(n[o+1]){r[o]&&t.push({value:n[o+1],type:r[o],precedence:0,offset:n.index}),e=!0;break}e||t.push({value:n[0],type:"unparsed",precedence:0,offset:n.index})}return t}function i(e){return"plus"===e||"minus"===e||"multiply"===e||"divide"===e||"power"===e}function u(e){return e.length>1&&e.some((e=>i(e.type)))}function d(e,t){for(let r=0;r<e.length;r++){const n=e[r],o="symbol"===n.type||"incompleteSymbol"===n.type||"number"===n.type;if(n.offset<=t&&t<=n.offset+n.value.length&&o)return n}return null}function m(e){e=function(e){const t=[];for(const r of e)"whitespace"!==r.type&&t.push(r);return t}(e);const t=[],r=[];let n;for(let o=0;o<e.length;o++){const a=e[o];switch(a.type){case"plus":case"minus":case"multiply":case"divide":case"power":r.length&&r[r.length-1].minPrecedence>a.precedence&&(r[r.length-1].minPrecedence=a.precedence);break;case"openBrace":n={minPrecedence:1/0,
openBraceIndex:o},r.push(n);break;case"closeBrace":{if(n=r.pop(),!n)break;const a=e[n.openBraceIndex-1],l=e[o+1],s=a&&("plus"===a.type||"multiply"===a.type);(!i(l?.type)||l?.precedence<=n.minPrecedence)&&(!i(a?.type)||a?.precedence<n?.minPrecedence||a?.precedence===n?.minPrecedence&&s)&&(t.unshift(n.openBraceIndex),t.push(o),r.length&&r[r.length-1].minPrecedence>n.minPrecedence&&(r[r.length-1].minPrecedence=n.minPrecedence))}}}for(let r=t.length;r--;)e.splice(t[r],1);return e}function p(e){return m(c(e)).reduce(((e,t)=>{if("symbol"!==t.type)return e+t.value;const[,r]=h(t);return r?e+r:e}),"")}function h(e){const t=/^'?(?:([A-Z0-9_]+):)?(.*?)'?$/i.exec(e.value);return null===t?[void 0,void 0]:[t[1],t[2]]}},306858:(e,t,r)=>{"use strict";r.d(t,{removeUsdFromCryptoPairLogos:()=>l,resolveLogoUrls:()=>a});var n=r(868333);const o=(0,n.getLogoUrlResolver)();function a(e,t=n.LogoSize.Medium){const r=e.logoid,a=e["base-currency-logoid"],l=e["currency-logoid"],s=r&&o.getSymbolLogoUrl(r,t);if(s)return[s];const c=a&&o.getSymbolLogoUrl(a,t),i=l&&o.getSymbolLogoUrl(l,t);return c&&i?[c,i]:c?[c]:i?[i]:[]}function l(e){return 2!==e.length?e:function(e){return e.some((e=>s(e)))}(e)&&!function(e){return e.some((e=>e.includes("country")&&!s(e)))}(e)?e.filter((e=>!s(e))):e}function s(e){return!1}},41192:(e,t,r)=>{"use strict";r.d(t,{getBlockStyleClasses:()=>n.getBlockStyleClasses,getLogoStyleClasses:()=>n.getLogoStyleClasses});var n=r(108937)},439067:(e,t,r)=>{"use strict";r.d(t,{getStyleClasses:()=>n.getStyleClasses});var n=r(185934)},170876:(e,t,r)=>{"use strict";r.d(t,{createRegExpList:()=>s,getHighlightedChars:()=>c,rankedSearch:()=>l});var n=r(41899);function o(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}var a;function l(e){const{data:t,rules:r,queryString:o,isPreventedFromFiltering:a,primaryKey:l,secondaryKey:s=l,optionalPrimaryKey:c,tertiaryKey:i}=e;return t.map((e=>{const t=c&&e[c]?e[c]:e[l],a=e[s],u=i&&e[i];let d,m=0;return r.forEach((e=>{const{re:r,fullMatch:l}=e;if(r.lastIndex=0,(0,n.isString)(t)&&t&&t.toLowerCase()===o.toLowerCase())return m=4,void(d=t.match(l)?.index);if((0,n.isString)(t)&&l.test(t))return m=3,void(d=t.match(l)?.index);if((0,n.isString)(a)&&l.test(a))return m=2,void(d=a.match(l)?.index);if((0,n.isString)(a)&&r.test(a))return m=2,void(d=a.match(r)?.index);if(Array.isArray(u))for(const e of u)if(l.test(e))return m=1,void(d=e.match(l)?.index)})),{matchPriority:m,matchIndex:d,item:e}})).filter((e=>a||e.matchPriority)).sort(((e,t)=>{if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}function s(e,t){const r=[],n=e.toLowerCase(),a=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${o(e)}`:o(e)})`)).join("(.*?)")+"(.*)";return r.push({fullMatch:new RegExp(`(${o(e)})`,"i"),re:new RegExp(`^${a}`,"i"),reserveRe:new RegExp(a,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(n)&&r.push({
fullMatch:t[n],re:t[n],fuzzyHighlight:!1}),r}function c(e,t,r){const n=[];return e&&r?(r.forEach((e=>{const{fullMatch:r,re:o,reserveRe:a}=e;r.lastIndex=0,o.lastIndex=0;const l=r.exec(t),s=l||o.exec(t)||a&&a.exec(t);if(e.fuzzyHighlight=!l,s)if(e.fuzzyHighlight){let e=s.index;for(let t=1;t<s.length;t++){const r=s[t],o=s[t].length;if(t%2){const t=r.startsWith(" ")||r.startsWith("/")||r.startsWith("-");n[t?e+1:e]=!0}e+=o}}else for(let e=0;e<s[0].length;e++)n[s.index+e]=!0})),n):n}!function(e){e[e.Low=0]="Low",e[e.MediumLow=1]="MediumLow",e[e.Medium=2]="Medium",e[e.High=3]="High",e[e.Highest=4]="Highest"}(a||(a={}))},260598:(e,t,r)=>{"use strict";r.d(t,{HighlightedText:()=>s});var n=r(50959),o=r(497754),a=r(170876),l=r(295059);function s(e){const{queryString:t,rules:r,text:s,className:c}=e,i=(0,n.useMemo)((()=>(0,a.getHighlightedChars)(t,s,r)),[t,r,s]);return n.createElement(n.Fragment,null,i.length?s.split("").map(((e,t)=>n.createElement(n.Fragment,{key:t},i[t]?n.createElement("span",{className:o(l.highlighted,c)},e):n.createElement("span",null,e)))):s)}},965800:(e,t,r)=>{"use strict";r.d(t,{useTheme:()=>a});var n=r(297265),o=r(702054);function a(){return(0,n.useWatchedValueReadonly)({watchedValue:o.watchedTheme})}},313739:(e,t,r)=>{"use strict";r.d(t,{MatchMediaMap:()=>l});var n=r(50959),o=r(266783),a=r.n(o);class l extends n.Component{constructor(e){super(e),this._handleMediaChange=()=>{const e=c(this.state.queries,((e,t)=>t.matches));let t=!1;for(const r in e)if(e.hasOwnProperty(r)&&this.state.matches[r]!==e[r]){t=!0;break}t&&this.setState({matches:e})};const{rules:t}=this.props;this.state=s(t)}shouldComponentUpdate(e,t){return!a()(e,this.props)||(!a()(t.rules,this.state.rules)||!a()(t.matches,this.state.matches))}componentDidMount(){this._migrate(null,this.state.queries)}componentDidUpdate(e,t){a()(e.rules,this.props.rules)||this._migrate(t.queries,this.state.queries)}componentWillUnmount(){this._migrate(this.state.queries,null)}render(){return this.props.children(this.state.matches)}static getDerivedStateFromProps(e,t){if(a()(e.rules,t.rules))return null;const{rules:r}=e;return s(r)}_migrate(e,t){null!==e&&c(e,((e,t)=>{t.removeEventListener("change",this._handleMediaChange)})),null!==t&&c(t,((e,t)=>{t.addEventListener("change",this._handleMediaChange)}))}}function s(e){const t=c(e,((e,t)=>window.matchMedia(t)));return{queries:t,matches:c(t,((e,t)=>t.matches)),rules:{...e}}}function c(e,t){const r={};for(const n in e)e.hasOwnProperty(n)&&(r[n]=t(n,e[n]));return r}},603422:(e,t,r)=>{"use strict";r.d(t,{VISIBLE_TYPESPECS:()=>l,marketType:()=>s});var n=r(609838);const o=new Map([["cfd",n.t(null,void 0,r(779599))],["dr",n.t(null,void 0,r(547268))],["index",n.t(null,void 0,r(261833))],["forex",n.t(null,void 0,r(158096))],["right",n.t(null,{context:"symbol_type"
},r(153174))],["bond",n.t(null,void 0,r(642358))],["bitcoin",n.t(null,void 0,r(146128))],["crypto",n.t(null,void 0,r(146128))],["economic",n.t(null,void 0,r(154094))],["indices",n.t(null,void 0,r(990250))],["futures",n.t(null,void 0,r(504723))],["stock",n.t(null,void 0,r(676752))],["commodity",n.t(null,void 0,r(370932))]]);r(68212);const a=new Map,l=new Set(["cfd","spreadbet","defi","yield","government","corporate","mutual","money","etf","unit","trust","reit","etn","convertible","closedend","crypto","oracle"]);function s(e,t=[],r=!0){const n=t.filter((e=>l.has(e))),s=`${e}_${n.sort().join("_")}`,c=a.get(s);if(void 0!==c)return c;const i=r?function(e){return o.get(e)||e}(e):e,u=Boolean(t.length)?[i,...n].join(" "):i;return a.set(s,u),u}},552019:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M13.35 5.35a.5.5 0 0 0-.7-.7L9 8.29 5.35 4.65a.5.5 0 1 0-.7.7L8.29 9l-3.64 3.65a.5.5 0 0 0 .7.7L9 9.71l3.65 3.64a.5.5 0 0 0 .7-.7L9.71 9l3.64-3.65z"/></svg>'},295694:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M17 22.5 6.85 12.35a.5.5 0 0 1 0-.7L17 1.5"/></svg>'},249498:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M12 16.5 4.85 9.35a.5.5 0 0 1 0-.7L12 1.5"/></svg>'},560176:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M9.5 12.5 3.9 7.37a.5.5 0 0 1 0-.74L9.5 1.5"/></svg>'},935369:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M8 10.5 3.85 6.35a.5.5 0 0 1 0-.7L8 1.5"/></svg>'},758478:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M7 8.5 3.85 5.35a.5.5 0 0 1 0-.7L7 1.5"/></svg>'},773063:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M17 22.5 6.85 12.35a.5.5 0 0 1 0-.7L17 1.5"/></svg>'},914127:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M12 16.5 4.85 9.35a.5.5 0 0 1 0-.7L12 1.5"/></svg>'},218073:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M9.5 12.5 3.9 7.37a.5.5 0 0 1 0-.74L9.5 1.5"/></svg>'},99243:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M8 10.5 3.85 6.35a.5.5 0 0 1 0-.7L8 1.5"/></svg>'},142576:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M7 8.5 3.85 5.35a.5.5 0 0 1 0-.7L7 1.5"/></svg>'},491540:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M2.5 14.5c1.68-1.26 3.7-2 6.5-2s4.91.74 6.5 2m-13-11c1.68 1.26 3.7 2 6.5 2s4.91-.74 6.5-2"/><circle stroke="currentColor" cx="9" cy="9" r="8.5"/><path stroke="currentColor" d="M13.5 9c0 2.42-.55 4.58-1.4 6.12-.87 1.56-1.98 2.38-3.1 2.38s-2.23-.82-3.1-2.38c-.85-1.54-1.4-3.7-1.4-6.12s.55-4.58 1.4-6.12C6.77 1.32 7.88.5 9 .5s2.23.82 3.1 2.38c.85 1.54 1.4 3.7 1.4 6.12z"/></svg>'},366619:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#B2B5BE" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},267562:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#131722" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},900486:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h9"/><circle fill="currentColor" cx="7" cy="3" r="1"/><circle fill="currentColor" cx="7" cy="10" r="1"/></svg>'},863861:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><g fill="none" fill-rule="evenodd" stroke="currentColor"><path stroke-linecap="square" stroke-linejoin="round" d="M3.5 10V2.5L1 5"/><path stroke-linecap="square" d="M1.5 10.5h4"/><path d="M8 12l3-11"/></g></svg>'},481574:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h8"/></svg>'},132617:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M3 10l7-7M3 3l7 7"/></svg>'},935119:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h8m-4-4v8"/></svg>'},569135:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M3 7l3.5-3.5L10 7"/></svg>'},654313:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M18.5 12.5a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm-1.25 5.8a7.5 7.5 0 1 1 1.06-1.06l4.22 4.23.53.53L22 23.06l-.53-.53-4.22-4.22Z"/></svg>'},606347:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M17.4 17.5a7 7 0 1 0-4.9 2c1.9 0 3.64-.76 4.9-2zm0 0l5.1 5"/></svg>'}}]);