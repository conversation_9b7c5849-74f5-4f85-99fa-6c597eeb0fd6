(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[68558],{24654:e=>{"use strict";e.exports=function(e){for(var t=[],_=e.length,r=0;r<_;r++){var a=e.charCodeAt(r);if(a>=55296&&a<=56319&&_>r+1){var o=e.charCodeAt(r+1);o>=56320&&o<=57343&&(a=1024*(a-55296)+o-56320+65536,r+=1)}a<128?t.push(a):a<2048?(t.push(a>>6|192),t.push(63&a|128)):a<55296||a>=57344&&a<65536?(t.push(a>>12|224),t.push(a>>6&63|128),t.push(63&a|128)):a>=65536&&a<=1114111?(t.push(a>>18|240),t.push(a>>12&63|128),t.push(a>>6&63|128),t.push(63&a|128)):t.push(239,191,189)}return new Uint8Array(t).buffer}},9995:(e,t,_)=>{var r=_(939340);e.exports=function(e){return e=r(e^=e>>>16,2246822507),e=r(e^=e>>>13,3266489909),(e^=e>>>16)>>>0}},939340:e=>{"use strict";e.exports=Math.imul||function(e,t){var _=65535&e,r=65535&t;return _*r+((e>>>16&65535)*r+_*(t>>>16&65535)<<16>>>0)|0}},855385:(e,t,_)=>{var r=_(939340),a=_(9995),o=_(24654),s=new Uint32Array([3432918353,461845907]);function n(e,t){return e<<t|e>>>32-t}e.exports=function(e,t){if(t=t?0|t:0,"string"==typeof e&&(e=o(e)),!(e instanceof ArrayBuffer))throw new TypeError("Expected key to be ArrayBuffer or string");var _=new Uint32Array([t]);return function(e,t){for(var _=e.byteLength/4|0,a=new Uint32Array(e,0,_),o=0;o<_;o++)a[o]=r(a[o],s[0]),a[o]=n(a[o],15),a[o]=r(a[o],s[1]),t[0]=t[0]^a[o],t[0]=n(t[0],13),t[0]=r(t[0],5)+3864292196}(e,_),function(e,t){var _=e.byteLength/4|0,a=e.byteLength%4,o=0,i=new Uint8Array(e,4*_,a);switch(a){case 3:o^=i[2]<<16;case 2:o^=i[1]<<8;case 1:o^=i[0],o=n(o=r(o,s[0]),15),o=r(o,s[1]),t[0]=t[0]^o}}(e,_),function(e,t){t[0]=t[0]^e.byteLength,t[0]=a(t[0])}(e,_),_.buffer}},613106:(e,t,_)=>{"use strict";_.d(t,{default:()=>l});var r=_(824824),a=_(770597),o=_(903262),s=_(520334),n=_(537080);function i(e){var t=-1,_=null==e?0:e.length;for(this.clear();++t<_;){var r=e[t];this.set(r[0],r[1])}}i.prototype.clear=r.default,i.prototype.delete=a.default,i.prototype.get=o.default,i.prototype.has=s.default,i.prototype.set=n.default;const l=i},557699:(e,t,_)=>{"use strict";_.d(t,{default:()=>l});var r=_(898968),a=_(869735),o=_(360648),s=_(454600),n=_(924977);function i(e){var t=-1,_=null==e?0:e.length;for(this.clear();++t<_;){var r=e[t];this.set(r[0],r[1])}}i.prototype.clear=r.default,i.prototype.delete=a.default,i.prototype.get=o.default,i.prototype.has=s.default,i.prototype.set=n.default;const l=i},19385:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(352918),a=_(799615);const o=(0,r.default)(a.default,"Map")},441083:(e,t,_)=>{"use strict";_.d(t,{default:()=>l});var r=_(712380),a=_(967067),o=_(609240),s=_(127323),n=_(853843);function i(e){var t=-1,_=null==e?0:e.length;for(this.clear();++t<_;){var r=e[t];this.set(r[0],r[1])}}i.prototype.clear=r.default,i.prototype.delete=a.default,i.prototype.get=o.default,i.prototype.has=s.default,i.prototype.set=n.default;const l=i},366711:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=_(799615).default.Symbol},365363:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(554523);const a=function(e,t){for(var _=e.length;_--;)if((0,
r.default)(e[_][0],t))return _;return-1}},194138:(e,t,_)=>{"use strict";_.d(t,{default:()=>n});var r=_(366711),a=_(633161),o=_(495863),s=r.default?r.default.toStringTag:void 0;const n=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":s&&s in Object(e)?(0,a.default)(e):(0,o.default)(e)}},263062:(e,t,_)=>{"use strict";_.d(t,{default:()=>h});var r=_(688987),a=_(832632),o=_(182433),s=_(265114),n=/^\[object .+?Constructor\]$/,i=Function.prototype,l=Object.prototype,c=i.toString,u=l.hasOwnProperty,d=RegExp("^"+c.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const h=function(e){return!(!(0,o.default)(e)||(0,a.default)(e))&&((0,r.default)(e)?d:n).test((0,s.default)(e))}},620712:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=_(799615).default["__core-js_shared__"]},97889:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r="object"==typeof global&&global&&global.Object===Object&&global},510791:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(46861);const a=function(e,t){var _=e.__data__;return(0,r.default)(t)?_["string"==typeof t?"string":"hash"]:_.map}},352918:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(263062),a=_(192716);const o=function(e,t){var _=(0,a.default)(e,t);return(0,r.default)(_)?_:void 0}},633161:(e,t,_)=>{"use strict";_.d(t,{default:()=>i});var r=_(366711),a=Object.prototype,o=a.hasOwnProperty,s=a.toString,n=r.default?r.default.toStringTag:void 0;const i=function(e){var t=o.call(e,n),_=e[n];try{e[n]=void 0;var r=!0}catch(e){}var a=s.call(e);return r&&(t?e[n]=_:delete e[n]),a}},192716:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=function(e,t){return null==e?void 0:e[t]}},824824:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(219620);const a=function(){this.__data__=r.default?(0,r.default)(null):{},this.size=0}},770597:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},903262:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(219620),a=Object.prototype.hasOwnProperty;const o=function(e){var t=this.__data__;if(r.default){var _=t[e];return"__lodash_hash_undefined__"===_?void 0:_}return a.call(t,e)?t[e]:void 0}},520334:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(219620),a=Object.prototype.hasOwnProperty;const o=function(e){var t=this.__data__;return r.default?void 0!==t[e]:a.call(t,e)}},537080:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(219620);const a=function(e,t){var _=this.__data__;return this.size+=this.has(e)?0:1,_[e]=r.default&&void 0===t?"__lodash_hash_undefined__":t,this}},46861:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},832632:(e,t,_)=>{"use strict";_.d(t,{default:()=>s});var r,a=_(620712),o=(r=/[^.]+$/.exec(a.default&&a.default.keys&&a.default.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";const s=function(e){return!!o&&o in e}},
898968:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=function(){this.__data__=[],this.size=0}},869735:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(365363),a=Array.prototype.splice;const o=function(e){var t=this.__data__,_=(0,r.default)(t,e);return!(_<0)&&(_==t.length-1?t.pop():a.call(t,_,1),--this.size,!0)}},360648:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(365363);const a=function(e){var t=this.__data__,_=(0,r.default)(t,e);return _<0?void 0:t[_][1]}},454600:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(365363);const a=function(e){return(0,r.default)(this.__data__,e)>-1}},924977:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(365363);const a=function(e,t){var _=this.__data__,a=(0,r.default)(_,e);return a<0?(++this.size,_.push([e,t])):_[a][1]=t,this}},712380:(e,t,_)=>{"use strict";_.d(t,{default:()=>s});var r=_(613106),a=_(557699),o=_(19385);const s=function(){this.size=0,this.__data__={hash:new r.default,map:new(o.default||a.default),string:new r.default}}},967067:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(510791);const a=function(e){var t=(0,r.default)(this,e).delete(e);return this.size-=t?1:0,t}},609240:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(510791);const a=function(e){return(0,r.default)(this,e).get(e)}},127323:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(510791);const a=function(e){return(0,r.default)(this,e).has(e)}},853843:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=_(510791);const a=function(e,t){var _=(0,r.default)(this,e),a=_.size;return _.set(e,t),this.size+=_.size==a?0:1,this}},219620:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=(0,_(352918).default)(Object,"create")},495863:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=Object.prototype.toString;const a=function(e){return r.call(e)}},799615:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(97889),a="object"==typeof self&&self&&self.Object===Object&&self;const o=r.default||a||Function("return this")()},265114:(e,t,_)=>{"use strict";_.d(t,{default:()=>a});var r=Function.prototype.toString;const a=function(e){if(null!=e){try{return r.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},290484:(e,t,_)=>{"use strict";_.d(t,{default:()=>l});var r=_(182433),a=_(799615);const o=function(){return a.default.Date.now()};var s=_(678677),n=Math.max,i=Math.min;const l=function(e,t,_){var a,l,c,u,d,h,p=0,f=!1,m=!1,b=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function y(t){var _=a,r=l;return a=l=void 0,p=t,u=e.apply(r,_)}function g(e){var _=e-h;return void 0===h||_>=t||_<0||m&&e-p>=c}function v(){var e=o();if(g(e))return w(e);d=setTimeout(v,function(e){var _=t-(e-h);return m?i(_,c-(e-p)):_}(e))}function w(e){return d=void 0,b&&a?y(e):(a=l=void 0,u)}function x(){var e=o(),_=g(e);if(a=arguments,l=this,h=e,_){if(void 0===d)return function(e){return p=e,d=setTimeout(v,t),f?y(e):u}(h);if(m)return clearTimeout(d),d=setTimeout(v,t),y(h)}return void 0===d&&(d=setTimeout(v,t)),u}return t=(0,s.default)(t)||0,(0,r.default)(_)&&(f=!!_.leading,
c=(m="maxWait"in _)?n((0,s.default)(_.maxWait)||0,t):c,b="trailing"in _?!!_.trailing:b),x.cancel=function(){void 0!==d&&clearTimeout(d),p=0,a=h=l=d=void 0},x.flush=function(){return void 0===d?u:w(o())},x}},554523:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=function(e,t){return e===t||e!=e&&t!=t}},688987:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(194138),a=_(182433);const o=function(e){if(!(0,a.default)(e))return!1;var t=(0,r.default)(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},182433:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},513795:(e,t,_)=>{"use strict";_.d(t,{default:()=>r});const r=function(e){return null!=e&&"object"==typeof e}},898111:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(194138),a=_(513795);const o=function(e){return"symbol"==typeof e||(0,a.default)(e)&&"[object Symbol]"==(0,r.default)(e)}},259332:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(441083);function a(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var _=function(){var r=arguments,a=t?t.apply(this,r):r[0],o=_.cache;if(o.has(a))return o.get(a);var s=e.apply(this,r);return _.cache=o.set(a,s)||o,s};return _.cache=new(a.Cache||r.default),_}a.Cache=r.default;const o=a},920057:(e,t,_)=>{"use strict";_.d(t,{default:()=>o});var r=_(290484),a=_(182433);const o=function(e,t,_){var o=!0,s=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return(0,a.default)(_)&&(o="leading"in _?!!_.leading:o,s="trailing"in _?!!_.trailing:s),(0,r.default)(e,t,{leading:o,maxWait:t,trailing:s})}},678677:(e,t,_)=>{"use strict";_.d(t,{default:()=>h});var r=/\s/;const a=function(e){for(var t=e.length;t--&&r.test(e.charAt(t)););return t};var o=/^\s+/;const s=function(e){return e?e.slice(0,a(e)+1).replace(o,""):e};var n=_(182433),i=_(898111),l=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,u=/^0o[0-7]+$/i,d=parseInt;const h=function(e){if("number"==typeof e)return e;if((0,i.default)(e))return NaN;if((0,n.default)(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=(0,n.default)(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=s(e);var _=c.test(e);return _||u.test(e)?d(e.slice(2),_?2:8):l.test(e)?NaN:+e}},6283:e=>{"use strict"
;e.exports=JSON.parse('{"14851":{},"custom_items_in_context_menu":{},"countdown":{},"symbol_search_parser_mixin":{},"pay_attention_to_ticker_not_symbol":{},"graying_disabled_tools_enabled":{},"update_study_formatter_on_symbol_resolve":{},"constraint_dialogs_movement":{},"phone_verification":{},"show_trading_notifications_history":{},"show_interval_dialog_on_key_press":{},"header_interval_dialog_button":{"subsets":["show_interval_dialog_on_key_press"]},"header_fullscreen_button":{},"header_symbol_search":{},"symbol_search_hot_key":{},"header_resolutions":{"subsets":["header_interval_dialog_button"]},"header_chart_type":{},"header_settings":{},"header_indicators":{},"header_compare":{},"header_undo_redo":{},"header_quick_search":{},"header_screenshot":{},"header_saveload":{},"study_on_study":{},"scales_date_format":{},"scales_time_hours_format":{},"header_widget":{"subsets":["header_widget_dom_node","header_symbol_search","header_resolutions","header_chart_type","header_settings","header_indicators","header_compare","header_undo_redo","header_quick_search","header_fullscreen_button","compare_symbol","header_screenshot"]},"legend_widget":{},"compare_symbol":{"subsets":["header_compare"]},"property_pages":{"subsets":["show_chart_property_page","chart_property_page"]},"show_chart_property_page":{},"chart_property_page":{"subsets":["chart_property_page_scales","chart_property_page_trading","chart_property_page_right_margin_editor"]},"left_toolbar":{},"right_toolbar":{},"hide_left_toolbar_by_default":{},"control_bar":{},"widget_logo":{},"timeframes_toolbar":{},"edit_buttons_in_legend":{"subsets":["show_hide_button_in_legend","format_button_in_legend","study_buttons_in_legend","delete_button_in_legend","legend_inplace_edit"]},"show_hide_button_in_legend":{},"object_tree_legend_mode":{},"format_button_in_legend":{},"study_buttons_in_legend":{},"delete_button_in_legend":{},"legend_inplace_edit":{},"broker_button":{},"buy_sell_buttons":{"subsets":["broker_button"]},"pane_context_menu":{},"scales_context_menu":{},"legend_context_menu":{},"context_menus":{"subsets":["pane_context_menu","scales_context_menu","legend_context_menu","objects_tree_context_menu"]},"items_favoriting":{},"save_chart_properties_to_local_storage":{},"use_localstorage_for_settings":{"subsets":["items_favoriting","save_chart_properties_to_local_storage"]},"handle_scale":{"subsets":["mouse_wheel_scale","pinch_scale","axis_pressed_mouse_move_scale"]},"handle_scroll":{"subsets":["mouse_wheel_scroll","pressed_mouse_move_scroll","horz_touch_drag_scroll","vert_touch_drag_scroll"]},"plain_studymarket":{},"disable_resolution_rebuild":{},"border_around_the_chart":{},"charting_library_debug_mode":{},"saveload_requires_authentication":{},"saveload_storage_customization":{},"volume_force_overlay":{},"create_volume_indicator_by_default":{},"create_volume_indicator_by_default_once":{},"saved_charts_count_restriction":{},"lean_chart_load":{},"stop_study_on_restart":{},"star_some_intervals_by_default":{},"move_logo_to_main_pane":{},"show_animated_logo":{},"link_to_tradingview":{},"logo_without_link":{},"logo_always_maximized":{},"right_bar_stays_on_scroll":{},"chart_content_overrides_by_defaults":{},"snapshot_trading_drawings":{},"allow_supported_resolutions_set_only":{},"widgetbar_tabs":{"subsets":["right_toolbar"]},"show_object_tree":{"subsets":["right_toolbar"]},"dom_widget":{"subsets":["right_toolbar"]},"collapsible_header":{},"study_templates":{},"side_toolbar_in_fullscreen_mode":{},"header_in_fullscreen_mode":{},"remove_library_container_border":{},"whotrades_auth_only":{},"support_multicharts":{},"display_market_status":{},"display_data_mode":{},"datasource_copypaste":{},"drawing_templates":{"subsets":["linetoolpropertieswidget_template_button"]},"expand_symbolsearch_items":{},"symbol_search_three_columns_exchanges":{},"symbol_search_flags":{},"symbol_search_limited_exchanges":{},"bugreport_button":{"subsets":["right_toolbar"]},"footer_publish_idea_button":{},"text_notes":{},"show_source_code":{},"symbol_info":{},"no_bars_status":{},"clear_bars_on_series_error":{},"hide_loading_screen_on_series_error":{},"seconds_resolution":{},"dont_show_boolean_study_arguments":{},"hide_last_na_study_output":{},"price_scale_always_last_bar_value":{},"study_dialog_fundamentals_economy_addons":{},"uppercase_instrument_names":{},"trading_notifications":{},"chart_crosshair_menu":{},"japanese_chart_styles":{},"hide_series_legend_item":{},"hide_study_overlay_legend_item":{},"hide_study_compare_legend_item":{},"linetoolpropertieswidget_template_button":{},"use_overrides_for_overlay":{},"timezone_menu":{},"main_series_scale_menu":{},"show_login_dialog":{},"remove_img_from_rss":{},"bars_marks":{},"chart_scroll":{},"chart_zoom":{},"source_selection_markers":{},"low_density_bars":{},"end_of_period_timescale_marks":{},"open_account_manager":{},"show_order_panel_on_start":{},"order_panel":{"subsets":["order_panel_close_button","order_panel_undock","right_toolbar","order_info"]},"multiple_watchlists":{},"watchlist_import_export":{},"study_overlay_compare_legend_option":{},"mobile_app_action_open_details_webview":{},"custom_resolutions":{},"referral_program_for_widget_owners":{},"mobile_trading":{},"real_brokers":{},"no_min_chart_width":{},"lock_visible_time_range_on_resize":{},"pricescale_currency":{},"cropped_tick_marks":{},"trading_account_manager":{},"disable_sameinterval_aligning":{},"display_legend_on_all_charts":{},"chart_style_hilo":{},"chart_style_hilo_last_price":{},"pricescale_unit":{},"show_spread_operators":{},"hide_exponentiation_spread_operator":{},"hide_reciprocal_spread_operator":{},"compare_symbol_search_spread_operators":{},"studies_symbol_search_spread_operators":{},"hide_resolution_in_legend":{},"hide_unresolved_symbols_in_legend":{},"fix_left_edge":{},"study_symbol_ticker_description":{},"two_character_bar_marks_labels":{},"tick_resolution":{},"secondary_series_extend_time_scale":{},"hide_volume_ma":{},"small_no_display":{},"charting_library_single_symbol_request":{},"use_ticker_on_symbol_info_update":{},"show_zoom_and_move_buttons_on_touch":{},"hide_main_series_symbol_from_indicator_legend":{},"chart_hide_close_position_button":{},"chart_hide_close_order_button":{},"hide_price_scale_global_last_bar_value":{"subsets":["use_last_visible_bar_value_in_legend"]},"keep_object_tree_widget_in_right_toolbar":{},"show_average_close_price_line_and_label":{},"hide_image_invalid_symbol":{},"hide_object_tree_and_price_scale_exchange_label":{},"confirm_overwrite_if_chart_layout_with_name_exists":{},"determine_first_data_request_size_using_visible_range":{},"use_na_string_for_not_available_values":{},"show_last_price_and_change_only_in_series_legend":{},"legend_last_day_change":{},"iframe_loading_compatibility_mode":{},"show_percent_option_for_right_margin":{},"watchlist_context_menu":{},"accessible_keyboard_shortcuts":{},"advanced_emoji_in_titles":{},"app_phone":{},"app_tablet":{},"mobile_app_hide_replay_toolbar":{},"symbol_search_option_chain_selector":{},"tv_production":{"subsets":["advanced_emoji_in_titles","auto_enable_symbol_labels","symbol_search_parser_mixin","header_fullscreen_button","header_widget","dont_show_boolean_study_arguments","left_toolbar","right_toolbar","buy_sell_buttons","control_bar","symbol_search_hot_key","context_menus","edit_buttons_in_legend","object_tree_legend_mode","uppercase_instrument_names","use_localstorage_for_settings","saveload_requires_authentication","volume_force_overlay","saved_charts_count_restriction","create_volume_indicator_by_default","create_volume_indicator_by_default_once","charts_auto_save","save_old_chart_before_save_as","chart_content_overrides_by_defaults","alerts","header_saveload","header_layouttoggle","datasource_copypaste","show_saved_watchlists","watchlists_from_to_file","add_to_watchlist","property_pages","support_multicharts","display_market_status","display_data_mode","show_chart_warn_message","support_manage_drawings","widgetbar_tabs","study_templates","collapsible_header","drawing_templates","footer_publish_idea_button","text_notes","show_source_code","symbol_info","linetoolpropertieswidget_template_button","trading_notifications","symbol_search_three_columns_exchanges","symbol_search_flags","symbol_search_limited_exchanges","phone_verification","custom_resolutions","compare_symbol","study_on_study","japanese_chart_styles","show_login_dialog","dom_widget","bars_marks","chart_scroll","chart_zoom","show_trading_notifications_history","source_selection_markers","study_dialog_fundamentals_economy_addons","multiple_watchlists","marked_symbols","order_panel","pricescale_currency","show_animated_logo","pricescale_currency","show_object_tree","watchlist_import_export","scales_date_format","scales_time_hours_format","popup_hints","show_right_widgets_panel_by_default","compare_recent_symbols_enabled","chart_style_hilo_last_price","symbol_search_option_chain_selector"]},"widget":{"subsets":["auto_enable_symbol_labels","symbol_search_parser_mixin","uppercase_instrument_names","left_toolbar","right_toolbar","control_bar","symbol_search_hot_key","context_menus","edit_buttons_in_legend","object_tree_legend_mode","use_localstorage_for_settings","saveload_requires_authentication","volume_force_overlay","create_volume_indicator_by_default","create_volume_indicator_by_default_once","dont_show_boolean_study_arguments","header_widget_dom_node","header_symbol_search","header_resolutions","header_chart_type","header_compare","header_indicators","star_some_intervals_by_default","display_market_status","display_data_mode","show_chart_warn_message","symbol_info","linetoolpropertieswidget_template_button","symbol_search_three_columns_exchanges","symbol_search_flags","symbol_search_limited_exchanges","widgetbar_tabs","compare_symbol","show_login_dialog","plain_studymarket","japanese_chart_styles","bars_marks","chart_scroll","chart_zoom","source_selection_markers","property_pages","show_right_widgets_panel_by_default","chart_style_hilo_last_price"]},"bovespa_widget":{"subsets":["widget","header_settings","linetoolpropertieswidget_template_button","compare_recent_symbols_enabled"]},"charting_library_base":{"subsets":["14851","allow_supported_resolutions_set_only","auto_enable_symbol_labels","border_around_the_chart","collapsible_header","constraint_dialogs_movement","context_menus","control_bar","create_volume_indicator_by_default","custom_items_in_context_menu","datasource_copypaste","uppercase_instrument_names","display_market_status","edit_buttons_in_legend","object_tree_legend_mode","graying_disabled_tools_enabled","header_widget","legend_widget","header_saveload","dont_show_boolean_study_arguments","lean_chart_load","left_toolbar","right_toolbar","link_to_tradingview","pay_attention_to_ticker_not_symbol","plain_studymarket","refresh_saved_charts_list_on_dialog_show","right_bar_stays_on_scroll","saveload_storage_customization","stop_study_on_restart","timeframes_toolbar","symbol_search_hot_key","update_study_formatter_on_symbol_resolve","update_timeframes_set_on_symbol_resolve","use_localstorage_for_settings","volume_force_overlay","widget_logo","countdown","use_overrides_for_overlay","trading_notifications","compare_symbol","symbol_info","timezone_menu","main_series_scale_menu","create_volume_indicator_by_default_once","bars_marks","chart_scroll","chart_zoom","source_selection_markers","property_pages","go_to_date","adaptive_logo","show_animated_logo","handle_scale","handle_scroll","shift_visible_range_on_new_bar","chart_content_overrides_by_defaults","cropped_tick_marks","scales_date_format","scales_time_hours_format","popup_hints","save_shortcut","show_right_widgets_panel_by_default","show_object_tree","insert_indicator_dialog_shortcut","compare_recent_symbols_enabled","hide_main_series_symbol_from_indicator_legend","chart_style_hilo","request_only_visible_range_on_reset","clear_price_scale_on_error_or_empty_bars","show_symbol_logo_in_legend","show_symbol_logo_for_compare_studies","library_custom_color_themes"]},"charting_library":{"subsets":["charting_library_base"]},"static_charts_service":{"subsets":["charting_library","disable_resolution_rebuild"]},"trading_terminal":{"subsets":["charting_library_base","support_multicharts","header_layouttoggle","japanese_chart_styles","chart_property_page_trading","add_to_watchlist","open_account_manager","show_dom_first_time","order_panel","buy_sell_buttons","multiple_watchlists","show_trading_notifications_history","always_pass_called_order_to_modify","show_object_tree","watchlist_import_export","drawing_templates","trading_account_manager","chart_crosshair_menu","compare_recent_symbols_enabled","watchlist_context_menu","show_symbol_logo_in_account_manager","watchlist_sections","prefer_quote_short_name","enable_dom_data_for_untradable_symbols","prefer_symbol_name_over_fullname","watchlist_cross_tab_sync"]}}')
}}]);