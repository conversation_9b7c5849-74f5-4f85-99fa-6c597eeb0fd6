(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3357,4109,9093],{653288:e=>{e.exports={en:["animals & nature"]}},821311:e=>{e.exports={en:["activity"]}},590678:e=>{e.exports={en:["food & drink"]}},506715:e=>{e.exports={en:["flags"]}},998355:e=>{e.exports={en:["objects"]}},843438:e=>{e.exports={en:["smiles & people"]}},140457:e=>{e.exports={en:["symbols"]}},188906:e=>{e.exports={en:["recently used"]}},628562:e=>{e.exports={en:["travel & places"]}},904543:e=>{e.exports={en:["Cancel"]}},47742:e=>{e.exports={en:["Close menu"]}},869207:e=>{e.exports={en:["Add to favorites"]}},275919:e=>{e.exports={en:["Do you really want to delete chart layout '{name}' ? This can't be undone. Your drawings and this layout will be gone forever."]}},843222:e=>{e.exports={en:["Do you really want to delete chart layout '{name}' that contains {n_drawings_on_n_symbols}? This can't be undone. Your drawings and this layout will be gone forever."]}},255108:e=>{e.exports={en:["Date modified (oldest first)"]}},175272:e=>{e.exports={en:["Date modified (newest first)"]}},211478:e=>{e.exports={en:["Layout name"]}},821329:e=>{e.exports={en:["Layout name (A to Z)"]}},111324:e=>{e.exports={en:["Layout name (Z to A)"]}},221355:e=>{e.exports={en:["Layouts"]}},863269:e=>{e.exports={en:["No layouts matched your criteria"]}},105191:e=>{e.exports={en:["Sort by layout name, date changed"]}},508573:e=>{e.exports={en:["Search"]}},685106:e=>{e.exports={en:["Remove from favorites"]}},647550:e=>{e.exports={en:["Remove selected emoji"]}},830502:e=>{e.exports={en:["on {amount} symbol","on {amount} symbols"]}}}]);