(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3010],{288276:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},173405:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},825549:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},518561:e=>{e.exports={scrollWrap:"scrollWrap-FaOvTD2r"}},151810:e=>{e.exports={wrap:"wrap-vSb6C0Bj","wrap--horizontal":"wrap--horizontal-vSb6C0Bj",bar:"bar-vSb6C0Bj",barInner:"barInner-vSb6C0Bj","barInner--horizontal":"barInner--horizontal-vSb6C0Bj","bar--horizontal":"bar--horizontal-vSb6C0Bj"}},336612:e=>{e.exports={dropTargetInside:"dropTargetInside-e_nPSSdZ",dropTarget:"dropTarget-e_nPSSdZ",before:"before-e_nPSSdZ",after:"after-e_nPSSdZ"}},469867:e=>{e.exports={sticky:"sticky-U0YaDVkl",hideSticky:"hideSticky-U0YaDVkl"}},856726:e=>{e.exports={wrap:"wrap-IEe5qpW4",selected:"selected-IEe5qpW4",childOfSelected:"childOfSelected-IEe5qpW4",disabled:"disabled-IEe5qpW4",expandHandle:"expandHandle-IEe5qpW4",expanded:"expanded-IEe5qpW4"}},127802:e=>{e.exports={separator:"separator-MgF6KBas",sticky:"sticky-MgF6KBas",accessible:"accessible-MgF6KBas",tree:"tree-MgF6KBas",overlayScrollWrap:"overlayScrollWrap-MgF6KBas",listContainer:"listContainer-MgF6KBas"}},536718:e=>{e.exports={
"default-drawer-min-top-distance":"100px",wrap:"wrap-_HnK0UIN",positionBottom:"positionBottom-_HnK0UIN",backdrop:"backdrop-_HnK0UIN",drawer:"drawer-_HnK0UIN",positionLeft:"positionLeft-_HnK0UIN"}},8510:e=>{e.exports={button:"button-w6lVe_oI",hovered:"hovered-w6lVe_oI",disabled:"disabled-w6lVe_oI"}},331774:(e,t,n)=>{"use strict";function r(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>r})},34735:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>S,InputClasses:()=>m});var r=n(50959),o=n(497754),s=n(650151),i=n(525388),l=n(800417),c=n(380327),a=n(331774);var d=n(288276),u=n.n(d);function p(e){let t="";return 0!==e&&(1&e&&(t=o(t,u()["no-corner-top-left"])),2&e&&(t=o(t,u()["no-corner-top-right"])),4&e&&(t=o(t,u()["no-corner-bottom-right"])),8&e&&(t=o(t,u()["no-corner-bottom-left"]))),t}function f(e,t,n,r){const{removeRoundBorder:s,className:i,intent:l="default",borderStyle:c="thin",size:d,highlight:f,disabled:h,readonly:m,stretch:E,noReadonlyStyles:g,isFocused:S}=e,v=p(s??(0,a.getGroupCellRemoveRoundBorders)(n));return o(u().container,u()[`container-${d}`],u()[`intent-${l}`],u()[`border-${c}`],d&&u()[`size-${d}`],v,f&&u()["with-highlight"],h&&u().disabled,m&&!g&&u().readonly,S&&u().focused,E&&u().stretch,t&&u().grouped,!r&&u()["adjust-position"],n.isTop&&u()["first-row"],n.isLeft&&u()["first-col"],i)}function h(e,t,n){const{highlight:r,highlightRemoveRoundBorder:s}=e;if(!r)return u().highlight;const i=p(s??(0,a.getGroupCellRemoveRoundBorders)(t));return o(u().highlight,u().shown,u()[`size-${n}`],i)}const m={FontSizeMedium:(0,s.ensureDefined)(u()["font-size-medium"]),FontSizeLarge:(0,s.ensureDefined)(u()["font-size-large"])},E={passive:!1};function g(e,t){const{style:n,id:o,role:s,onFocus:a,onBlur:d,onMouseOver:u,onMouseOut:p,onMouseDown:m,onMouseUp:g,onKeyDown:S,onClick:v,tabIndex:T,startSlot:D,middleSlot:N,endSlot:y,onWheel:b,onWheelNoPassive:C=null,size:_,tag:I="span",type:R}=e,{isGrouped:M,cellState:x,disablePositionAdjustment:w=!1}=(0,r.useContext)(c.ControlGroupContext),P=function(e,t=null,n){const o=(0,r.useRef)(null),s=(0,r.useRef)(null),i=(0,r.useCallback)((()=>{if(null===o.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&o.current.addEventListener(e,t,n)}),[]),l=(0,r.useCallback)((()=>{if(null===o.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&o.current.removeEventListener(e,t,n)}),[]),c=(0,r.useCallback)((e=>{l(),o.current=e,i()}),[]);return(0,r.useEffect)((()=>(s.current=[e,t,n],i(),l)),[e,t,n]),c}("wheel",C,E),O=I;return r.createElement(O,{type:R,style:n,id:o,role:s,className:f(e,M,x,w),tabIndex:T,ref:(0,i.useMergedRefs)([t,P]),onFocus:a,onBlur:d,onMouseOver:u,onMouseOut:p,onMouseDown:m,onMouseUp:g,onKeyDown:S,onClick:v,onWheel:b,...(0,l.filterDataProps)(e),...(0,l.filterAriaProps)(e)},D,N,y,r.createElement("span",{className:h(e,x,_)}))}g.displayName="ControlSkeleton";const S=r.forwardRef(g)},102691:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>u,
BeforeSlot:()=>l,EndSlot:()=>d,MiddleSlot:()=>a,StartSlot:()=>c});var r=n(50959),o=n(497754),s=n(173405),i=n.n(s);function l(e){const{className:t,children:n}=e;return r.createElement("span",{className:o(i()["before-slot"],t)},n)}function c(e){const{className:t,interactive:n=!0,icon:s=!1,children:l}=e;return r.createElement("span",{className:o(i()["inner-slot"],n&&i().interactive,s&&i().icon,t)},l)}function a(e){const{className:t,children:n}=e;return r.createElement("span",{className:o(i()["inner-slot"],i()["inner-middle-slot"],t)},n)}function d(e){const{className:t,interactive:n=!0,icon:s=!1,children:l}=e;return r.createElement("span",{className:o(i()["inner-slot"],n&&i().interactive,s&&i().icon,t)},l)}function u(e){const{className:t,children:n}=e;return r.createElement("span",{className:o(i()["after-slot"],t)},n)}},654936:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>S});var r=n(50959),o=n(497754),s=n(800417),i=n(269842),l=n(1811),c=n(525388),a=n(21778),d=n(383836),u=n(603548),p=n(34735),f=n(102691),h=n(825549),m=n.n(h);function E(e){return!(0,s.isAriaAttribute)(e)&&!(0,s.isDataAttribute)(e)}function g(e){const{id:t,title:n,role:i,tabIndex:l,placeholder:c,name:a,type:d,value:u,defaultValue:h,draggable:g,autoComplete:S,autoFocus:v,autoCapitalize:T,autoCorrect:D,maxLength:N,min:y,max:b,step:C,pattern:_,inputMode:I,onSelect:R,onFocus:M,onBlur:x,onKeyDown:w,onKeyUp:P,onKeyPress:O,onChange:L,onDragStart:k,size:B="small",className:F,inputClassName:A,disabled:z,readonly:W,containerTabIndex:U,startSlot:H,endSlot:Z,reference:K,containerReference:V,onContainerFocus:j,...G}=e,X=(0,s.filterProps)(G,E),q={...(0,s.filterAriaProps)(G),...(0,s.filterDataProps)(G),id:t,title:n,role:i,tabIndex:l,placeholder:c,name:a,type:d,value:u,defaultValue:h,draggable:g,autoComplete:S,autoFocus:v,autoCapitalize:T,autoCorrect:D,maxLength:N,min:y,max:b,step:C,pattern:_,inputMode:I,onSelect:R,onFocus:M,onBlur:x,onKeyDown:w,onKeyUp:P,onKeyPress:O,onChange:L,onDragStart:k};return r.createElement(p.ControlSkeleton,{...X,disabled:z,readonly:W,tabIndex:U,className:o(m().container,F),size:B,ref:V,onFocus:j,startSlot:H,middleSlot:r.createElement(f.MiddleSlot,null,r.createElement("input",{...q,className:o(m().input,m()[`size-${B}`],A,H&&m()["with-start-slot"],Z&&m()["with-end-slot"]),disabled:z,readOnly:W,ref:K})),endSlot:Z})}function S(e){e=(0,a.useControl)(e);const{disabled:t,autoSelectOnFocus:n,tabIndex:o=0,onFocus:s,onBlur:p,reference:f,containerReference:h=null}=e,m=(0,r.useRef)(null),E=(0,r.useRef)(null),[S,v]=(0,d.useFocus)(),T=t?void 0:S?-1:o,D=t?void 0:S?o:-1,{isMouseDown:N,handleMouseDown:y,handleMouseUp:b}=(0,u.useIsMouseDown)(),C=(0,i.createSafeMulticastEventHandler)(v.onFocus,(function(e){n&&!N.current&&(0,l.selectAllContent)(e.currentTarget)}),s),_=(0,i.createSafeMulticastEventHandler)(v.onBlur,p),I=(0,r.useCallback)((e=>{m.current=e,f&&("function"==typeof f&&f(e),"object"==typeof f&&(f.current=e))}),[m,f]);return r.createElement(g,{...e,isFocused:S,containerTabIndex:T,tabIndex:D,onContainerFocus:function(e){
E.current===e.target&&null!==m.current&&m.current.focus()},onFocus:C,onBlur:_,reference:I,containerReference:(0,c.useMergedRefs)([E,h]),onMouseDown:y,onMouseUp:b})}},21778:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>s});var r=n(269842),o=n(383836);function s(e){const{onFocus:t,onBlur:n,intent:s,highlight:i,disabled:l}=e,[c,a]=(0,o.useFocus)(void 0,l),d=(0,r.createSafeMulticastEventHandler)(l?void 0:a.onFocus,t),u=(0,r.createSafeMulticastEventHandler)(l?void 0:a.onBlur,n);return{...e,intent:s||(c?"primary":"default"),highlight:i??c,onFocus:d,onBlur:u}}},603548:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>o});var r=n(50959);function o(){const e=(0,r.useRef)(!1),t=(0,r.useCallback)((()=>{e.current=!0}),[e]),n=(0,r.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},1811:(e,t,n)=>{"use strict";function r(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>r})},73288:(e,t,n)=>{"use strict";n.d(t,{OverlayScrollContainer:()=>g});var r=n(50959),o=n(497754),s=n.n(o),i=n(431520),l=n(650151),c=n(822960);const a=n(151810);var d;!function(e){e[e.Vertical=0]="Vertical",e[e.Horizontal=1]="Horizontal",e[e.HorizontalRtl=2]="HorizontalRtl"}(d||(d={}));const u={0:{isHorizontal:!1,isNegative:!1,sizePropName:"height",minSizePropName:"minHeight",startPointPropName:"top",currentMousePointPropName:"clientY",progressBarTransform:"translateY"},1:{isHorizontal:!0,isNegative:!1,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"left",currentMousePointPropName:"clientX",progressBarTransform:"translateX"},2:{isHorizontal:!0,isNegative:!0,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"right",currentMousePointPropName:"clientX",progressBarTransform:"translateX"}},p=40;function f(e){const{size:t,scrollSize:n,clientSize:o,scrollProgress:i,onScrollProgressChange:d,scrollMode:f,theme:h=a,onDragStart:m,onDragEnd:E,minBarSize:g=p}=e,S=(0,r.useRef)(null),v=(0,r.useRef)(null),[T,D]=(0,r.useState)(!1),N=(0,r.useRef)(0),{isHorizontal:y,isNegative:b,sizePropName:C,minSizePropName:_,startPointPropName:I,currentMousePointPropName:R,progressBarTransform:M}=u[f];(0,r.useEffect)((()=>{const e=(0,l.ensureNotNull)(S.current).ownerDocument;return T?(m&&m(),e&&(e.addEventListener("mousemove",A),e.addEventListener("mouseup",z))):E&&E(),()=>{e&&(e.removeEventListener("mousemove",A),e.removeEventListener("mouseup",z))}}),[T]);const x=t/n||0,w=o*x||0,P=Math.max(w,g),O=(t-P)/(t-w),L=n-t,k=b?-L:0,B=b?0:L,F=U((0,c.clamp)(i,k,B))||0;return r.createElement("div",{ref:S,className:s()(h.wrap,y&&h["wrap--horizontal"]),style:{[C]:t},onMouseDown:function(e){if(e.isDefaultPrevented())return;e.preventDefault();const t=W(e.nativeEvent,(0,l.ensureNotNull)(S.current)),n=Math.sign(t),r=(0,l.ensureNotNull)(v.current).getBoundingClientRect();N.current=n*r[C]/2;let o=Math.abs(t)-Math.abs(N.current);const s=U(L);o<0?(o=0,N.current=t):o>s&&(o=s,N.current=t-n*s);d(H(n*o)),D(!0)}},r.createElement("div",{ref:v,className:s()(h.bar,y&&h["bar--horizontal"]),style:{[_]:g,[C]:P,
transform:`${M}(${F}px)`},onMouseDown:function(e){e.preventDefault(),N.current=W(e.nativeEvent,(0,l.ensureNotNull)(v.current)),D(!0)}},r.createElement("div",{className:s()(h.barInner,y&&h["barInner--horizontal"])})));function A(e){const t=W(e,(0,l.ensureNotNull)(S.current))-N.current;d(H(t))}function z(){D(!1)}function W(e,t){const n=t.getBoundingClientRect()[I];return e[R]-n}function U(e){return e*x*O}function H(e){return e/x/O}}var h=n(522224),m=n(518561);const E=8;function g(e){const{reference:t,className:n,containerHeight:s=0,containerWidth:l=0,contentHeight:c=0,contentWidth:a=0,scrollPosTop:d=0,scrollPosLeft:u=0,onVerticalChange:p,onHorizontalChange:g,visible:S}=e,[v,T]=(0,h.useHover)(),[D,N]=(0,r.useState)(!1),y=s<c,b=l<a,C=y&&b?E:0;return r.createElement("div",{...T,ref:t,className:o(n,m.scrollWrap),style:{visibility:S||v||D?"visible":"hidden"}},y&&r.createElement(f,{size:s-C,scrollSize:c-C,clientSize:s-C,scrollProgress:d,onScrollProgressChange:function(e){p&&p(e)},onDragStart:_,onDragEnd:I,scrollMode:0}),b&&r.createElement(f,{size:l-C,scrollSize:a-C,clientSize:l-C,scrollProgress:u,onScrollProgressChange:function(e){g&&g(e)},onDragStart:_,onDragEnd:I,scrollMode:(0,i.isRtl)()?2:1}));function _(){N(!0)}function I(){N(!1)}}},535842:(e,t,n)=>{"use strict";n.d(t,{SizeContext:()=>i});var r,o,s=n(50959);!function(e){e[e.Normal=0]="Normal",e[e.Small=1]="Small"}(r||(r={})),function(e){e[e.Select=0]="Select",e[e.Click=1]="Click"}(o||(o={}));const i=s.createContext({size:0,smallSizeTreeNodeAction:1})},849392:(e,t,n)=>{"use strict";n.d(t,{dropSelection:()=>p,endDrag:()=>b,hideDropTarget:()=>h,moveNodes:()=>E,multiSelectNext:()=>d,multiSelectPrevious:()=>a,processDropTarget:()=>u,resetTree:()=>o,scrollToId:()=>T,selectNext:()=>c,selectPrevious:()=>l,setDisabledNodes:()=>y,setFocusedNode:()=>v,setIsExpanded:()=>N,setIsSelected:()=>D,setNodes:()=>i,setSelectedIds:()=>m,startMultiSelect:()=>g,stopMultiSelect:()=>S,syncNodes:()=>s,updateDropTarget:()=>f});var r=n(471505);const o=()=>({type:r.RESET_TREE}),s=e=>({type:r.SYNC_NODES,nodes:e}),i=e=>({type:r.SET_NODES,nodes:e}),l=()=>({type:r.SELECT_PREVIOUS}),c=()=>({type:r.SELECT_NEXT}),a=()=>({type:r.MULTI_SELECT_PREVIOUS}),d=()=>({type:r.MULTI_SELECT_NEXT}),u=(e,t,n,o,s)=>({type:r.PROCESS_DROP_TARGET,dropTarget:e,dropType:t,isHoveredLeft:n,boundBox:o,isLastChild:s}),p=()=>({type:r.DROP_SELECTION}),f=(e,t,n)=>({type:r.UPDATE_DROP_TARGET,node:e,dropType:t,boundBox:n}),h=()=>({type:r.HIDE_DROP_TARGET}),m=e=>({type:r.SET_SELECTED_IDS,ids:e}),E=(e,t,n)=>({type:r.MOVE_NODES,ids:e,targetId:t,dropType:n}),g=()=>({type:r.START_MULTI_SELECT}),S=()=>({type:r.STOP_MULTI_SELECT}),v=e=>({type:r.SET_FOCUSED_NODE,nodeId:e}),T=e=>({type:r.SCROLL_TO_ID,nodeId:e}),D=(e,t,n=0)=>({type:r.SET_IS_SELECTED,nodeId:e,isSelected:t,mode:n}),N=(e,t)=>({type:r.SET_IS_EXPANDED,nodeId:e,isExpanded:t}),y=e=>({type:r.SET_DISABLED_NODES,ids:e}),b=()=>({type:r.END_DRAG})},471505:(e,t,n)=>{"use strict";n.d(t,{DROP_SELECTION:()=>d,END_DRAG:()=>_,HIDE_DROP_TARGET:()=>g,MOVE_NODES:()=>C,MULTI_SELECT_NEXT:()=>h,
MULTI_SELECT_PREVIOUS:()=>f,PROCESS_DROP_TARGET:()=>m,RESET_TREE:()=>c,SCROLL_TO_ID:()=>D,SELECT_NEXT:()=>p,SELECT_PREVIOUS:()=>u,SET_DISABLED_NODES:()=>b,SET_FOCUSED_NODE:()=>T,SET_IS_EXPANDED:()=>y,SET_IS_SELECTED:()=>N,SET_NODES:()=>o,SET_SELECTED_IDS:()=>a,START_MULTI_SELECT:()=>S,STOP_MULTI_SELECT:()=>v,SYNC_NODES:()=>s,UPDATE_DROP_TARGET:()=>E,UPDATE_NODE:()=>i,UPDATE_NODES:()=>l});const r=(0,n(215078).createActionTypeFactory)("OBJECT_TREE"),o=r("SET_NODES"),s=r("SYNC_NODES"),i=r("UPDATE_NODE"),l=r("UPDATE_NODES"),c=r("RESET_TREE"),a=r("SET_SELECTED_IDS"),d=r("DROP_SELECTION"),u=r("SELECT_PREVIOUS"),p=r("SELECT_NEXT"),f=r("MULTI_SELECT_PREVIOUS"),h=r("MULTI_SELECT_NEXT"),m=r("PROCESS_DROP_TARGET"),E=r("UPDATE_DROP_TARGET"),g=r("HIDE_DROP_TARGET"),S=r("START_MULTI_SELECT"),v=r("STOP_MULTI_SELECT"),T=(r("REMOVE_NODE"),r("SET_FOCUSED_NODE")),D=r("SCROLL_TO_ID"),N=r("SET_IS_SELECTED"),y=r("SET_IS_EXPANDED"),b=r("SET_DISABLED_NODES"),C=r("MOVE_NODES"),_=(r("START_DRAG"),r("END_DRAG"))},129881:(e,t,n)=>{"use strict";n.d(t,{dropTargetSelector:()=>l,isDisabledSelector:()=>h,isExpandedSelector:()=>f,isMultiSelectingSelector:()=>S,isSelectedSelector:()=>p,lastFocusedNodeIdSelector:()=>g,nodeIdsSelector:()=>m,nodeSelector:()=>u,nodesSelector:()=>s,orderedNodesSelector:()=>D,renderDragListSelector:()=>b,renderListSelector:()=>y,scrollToIdSelector:()=>a,selectedIdsSelector:()=>E,selectedNodesSelector:()=>v});var r=n(377145),o=n(650151);const s=e=>e.nodes,i=e=>e.selection,l=e=>e.dropTarget,c=e=>e.expanded,a=e=>e.scrollToId,d=(e,t)=>t,u=(0,r.createSelector)([s,d],((e,t)=>e[t])),p=(0,r.createSelector)([i,d],((e,t)=>e.ids.includes(t))),f=(0,r.createSelector)([c,d],((e,t)=>e.includes(t))),h=(0,r.createSelector)([e=>e.disabled,i,d],((e,t,n)=>!t.ids.includes(n)&&e.includes(n))),m=(0,r.createSelector)(s,(e=>Object.keys(e))),E=(0,r.createSelector)(i,(({ids:e})=>e)),g=(0,r.createSelector)(i,(({lastFocusedNodeId:e})=>e)),S=(0,r.createSelector)(i,(({isMultiSelecting:e})=>e)),v=(0,r.createSelector)([s,E],((e,t)=>t.map((t=>e[t])))),T=(0,r.createSelector)(s,(e=>Object.values(e).filter((e=>0===e.level)))),D=(0,r.createSelector)([s,T],((e,t)=>t.reduce(((t,n)=>[...t,...N(e,(0,o.ensureDefined)(n))]),[])));function N(e,t){const n=[];for(const r of t.children)n.push(e[r]),n.push(...N(e,e[r]));return n}const y=(0,r.createSelector)([s,T,c],((e,t,n)=>{const r=new Set(n);return t.reduce(((t,n)=>[...t,...C(e,(0,o.ensureDefined)(n),r)]),[])})),b=(0,r.createSelector)([s,E,c],((e,t,n)=>{const r=new Set(n);return[{id:"drag-list",level:-1,children:t}].reduce(((t,n)=>[...t,...C(e,(0,o.ensureDefined)(n),r)]),[])}));function C(e,t,n){const r=[];for(const o of t.children){const t=e[o];void 0!==t&&(r.push(t),n.has(o)&&r.push(...C(e,t,n)))}return r}},451539:(e,t,n)=>{"use strict";n.d(t,{getInsertIndex:()=>i,logger:()=>s});var r=n(671945),o=n(650151);const s=(0,r.getLogger)("Platform.GUI.ObjectTree.CallApi"),i=(e,t,n)=>{switch(n){case"before":return e.indexOf((0,o.ensureDefined)(t));case"inside":return e.length;case"after":return e.indexOf((0,
o.ensureDefined)(t))+1;default:return 0}}},733393:(e,t,n)=>{"use strict";n.d(t,{Tree:()=>Pe});var r=n(50959),o=n(746212),s=n(743766),i=n(10170),l=n(207809),c=n(394212),a=n(129885),d=n(471505),u=n(849392),p=n(129881);function*f(e){const{selectedIds:t,nodes:n}=yield(0,a.call)(e),r={};for(let e=0;e<n.length;++e){const t=n[e];r[t.id]=t}yield(0,a.put)((0,u.setNodes)(r)),yield(0,a.put)((0,u.setSelectedIds)(t));!(0,p.lastFocusedNodeIdSelector)(yield(0,a.select)())&&t.length>0&&(yield(0,a.put)((0,u.setFocusedNode)(t[0])),yield(0,a.put)((0,u.scrollToId)(t[0])))}var h=n(650151);function*m(e){for(;;){if((yield(0,a.take)([d.START_MULTI_SELECT,d.STOP_MULTI_SELECT])).type===d.START_MULTI_SELECT){const t=(0,p.nodeIdsSelector)(yield(0,a.select)()).filter((t=>!e(t)));yield(0,a.put)((0,u.setDisabledNodes)(t))}else yield(0,a.put)((0,u.setDisabledNodes)([]))}}function*E(){for(;;){const{type:e}=yield(0,a.take)([d.MULTI_SELECT_NEXT,d.MULTI_SELECT_PREVIOUS]),t=yield(0,a.select)(),n=(0,p.orderedNodesSelector)(t),r=n.length,o=(0,p.lastFocusedNodeIdSelector)(t),s=[...(0,p.selectedIdsSelector)(t)],i=1===s.length&&s[0]!==o,l=n.findIndex((e=>e.id===(i?s[0]:o)));if(e===d.MULTI_SELECT_PREVIOUS&&0===l||e===d.MULTI_SELECT_NEXT&&l===r-1)continue;const c=y(t,e===d.MULTI_SELECT_NEXT?"next":"previous",n,l),{id:f}=c;s.includes(f)&&o?(yield(0,a.put)((0,u.setIsSelected)(o,!1,1)),yield(0,a.put)((0,u.setFocusedNode)(f))):yield(0,a.put)((0,u.setIsSelected)(f,!0,1)),yield(0,a.put)((0,u.scrollToId)(f))}}function*g(e,t){for(;;){const{type:n}=yield(0,a.take)([d.SELECT_NEXT,d.SELECT_PREVIOUS]),r=yield(0,a.select)(),o=(0,p.orderedNodesSelector)(r),s=(0,p.selectedNodesSelector)(r),i=(0,p.lastFocusedNodeIdSelector)(r);if(1===s.length&&s[0].id!==i&&!i){if(n===d.SELECT_NEXT){yield(0,a.put)((0,u.setFocusedNode)(s[0].id));continue}if(n===d.SELECT_PREVIOUS){const e=o.findIndex((e=>e.id===s[0].id)),t=y(r,"previous",o,e);yield(0,a.put)((0,u.setFocusedNode)(t.id));continue}}const l=o.findIndex((e=>e.id===i)),c=n===d.SELECT_NEXT?"next":"previous",f=y(r,c,o,l),{id:h}=f;e?e([h],c):yield(0,a.put)((0,u.setSelectedIds)([h])),t&&t(h),yield(0,a.put)((0,u.setFocusedNode)(h))}}function*S(e,t=()=>!0){for(;;){const{mode:n,nodeId:r,isSelected:o}=yield(0,a.take)(d.SET_IS_SELECTED);let s=[...(0,p.selectedIdsSelector)(yield(0,a.select)())];const i=(0,p.orderedNodesSelector)(yield(0,a.select)());if(1===n)o?s.push(r):s.splice(s.indexOf(r),1);else if(2===n&&s.length>0){const e=(0,p.lastFocusedNodeIdSelector)(yield(0,a.select)());let n=i.findIndex((t=>t.id===e));-1===n&&(n=i.reduce(((e,t,n)=>s.includes(t.id)?n:e),-1));const o=i.findIndex((e=>e.id===r));if(n!==o)for(let e=Math.min(n,o);e<=Math.max(n,o);e++){const n=i[e].id;!s.includes(n)&&t(n)&&s.push(n)}}else s=r?[r]:[];const l=new Set(s);s=i.reduce(((e,t)=>(l.has(t.id)&&e.push(t.id),e)),[]),e?e(s):yield(0,a.put)((0,u.setSelectedIds)(s)),yield(0,a.put)((0,u.setFocusedNode)(r))}}function*v(e=()=>!0,t){const{dropTarget:n,dropType:r,isHoveredLeft:o,boundBox:s,isLastChild:i}=t,l=(0,p.dropTargetSelector)(yield(0,a.select)()),c=(0,
p.nodeSelector)(yield(0,a.select)(),(0,h.ensureDefined)(n.parentId)),d=i&&"after"===r,f=(0,p.selectedNodesSelector)(yield(0,a.select)()),m=!d||!o&&e(f,n,r)?n:c,E=l.node&&l.node.id!==m.id||l.dropType!==r;f.map((e=>e.id)).includes(m.id)?yield(0,a.put)((0,u.hideDropTarget)()):E&&e(f,m,r)&&(yield(0,a.put)((0,u.updateDropTarget)(m,r,s)))}function*T(e){yield(0,a.throttle)(0,d.PROCESS_DROP_TARGET,v,e)}function*D(e){for(;;){yield(0,a.take)(d.DROP_SELECTION);const t=(0,p.selectedNodesSelector)(yield(0,a.select)()),{node:n,dropType:r}=(0,p.dropTargetSelector)(yield(0,a.select)());if(n&&r){const o=new CustomEvent("tree-node-drop",{detail:{nodes:t,target:n.id,type:r}});if(e&&e(o),!o.defaultPrevented){const e=(0,p.selectedIdsSelector)(yield(0,a.select)());yield(0,a.put)((0,u.moveNodes)(e,n.id,r))}}}}function*N(e){for(;;){yield(0,a.take)(d.MOVE_NODES);e((0,p.nodesSelector)(yield(0,a.select)()))}}function y(e,t,n,r){const o=n.length;let s;-1===r&&"previous"===t&&(r=o);let i=0;for(;!s||Math.abs(i)<o&&((l=s).level>1&&!(0,p.isExpandedSelector)(e,(0,h.ensureDefined)(l.parentId)));)i+="next"===t?1:-1,s=n[(r+i+o)%o];var l;return s}function*b(e={}){const{saga:t,onDrop:n,canMove:r,onMove:o,onSelect:s,onKeyboardSelect:i,initState:l,canBeAddedToSelection:c}=e,u=[(0,a.fork)(T,r),(0,a.fork)(D,n),(0,a.fork)(S,s,c),(0,a.fork)(g,s,i),(0,a.fork)(E)];for(t&&u.push((0,a.fork)(t)),o&&u.push((0,a.fork)(N,o)),c&&u.push((0,a.fork)(m,c));;){l&&(yield(0,a.call)(f,l));const e=yield(0,a.all)(u);yield(0,a.take)(d.RESET_TREE);for(const t of e)yield(0,a.cancel)(t)}}var C=n(406047),_=n(41899),I=n(451539);const R={ids:[],lastFocusedNodeId:void 0,isMultiSelecting:!1};const M={node:void 0,dropType:void 0,boundBox:void 0};const x=(0,C.combineReducers)({nodes:function(e={},t){switch(t.type){case d.SET_NODES:return t.nodes;case d.SYNC_NODES:{const{nodes:n}=t,r=n.map((e=>e.id)),o={...e};for(const t of Object.keys(e))if(!r.includes(t)){const{parentId:e}=o[t];e&&(o[e]={...o[e],children:o[e].children.filter((e=>e!==t))}),delete o[t]}for(const e of n){const t=e.id;if(o.hasOwnProperty(t)){!(0,_.deepEquals)(o[t].children,e.children)[0]&&(o[t]={...o[t],children:[...e.children]})}else{o[t]=e;const{parentId:n}=e;if(n&&!o[n].children.includes(t))throw new Error("Not implemented")}}return o}case d.UPDATE_NODE:{const{type:n,nodeId:r,...o}=t;return{...e,[r]:{...e[r],...o}}}case d.UPDATE_NODES:{const{nodes:n}=t,r={...e};return Object.keys(n).forEach((e=>{r[e]={...r[e],...n[e]}})),{...e,...r}}case d.MOVE_NODES:{const{ids:n,targetId:r,dropType:o}=t,s=(0,h.ensureDefined)(e[r].parentId),i=e[s],l={};for(const t of n){const n=e[t];if(n.parentId){const r=l[n.parentId]||e[n.parentId];l[n.parentId]={...r,children:r.children.filter((e=>e!==t))}}l[t]={...n,parentId:s,level:i.level+1}}const c=i.children.filter((e=>!n.includes(e)));return c.splice((0,I.getInsertIndex)(c,r,o),0,...n),l[s]={...e[s],children:c,isExpanded:!0},{...e,...l}}default:return e}},selection:function(e=R,t){switch(t.type){case d.SET_SELECTED_IDS:{const{ids:n}=t;return{...e,ids:n,
lastFocusedNodeId:n.length>0?e.lastFocusedNodeId:void 0}}case d.START_MULTI_SELECT:return{...e,isMultiSelecting:!0};case d.STOP_MULTI_SELECT:return{...e,isMultiSelecting:!1};case d.SET_FOCUSED_NODE:return{...e,lastFocusedNodeId:t.nodeId};case d.SYNC_NODES:{const n=new Set(t.nodes.map((e=>e.id)));return e.lastFocusedNodeId&&!n.has(e.lastFocusedNodeId)&&delete e.lastFocusedNodeId,{...e,ids:e.ids.filter((e=>n.has(e)))}}default:return e}},dropTarget:function(e=M,t){switch(t.type){case d.UPDATE_DROP_TARGET:{const{node:n,dropType:r,boundBox:o}=t;return{...e,node:n,dropType:r,boundBox:o}}case d.HIDE_DROP_TARGET:case d.END_DRAG:case d.RESET_TREE:return{...M};default:return e}},expanded:function(e=[],t){if(t.type===d.SET_IS_EXPANDED){const{nodeId:n,isExpanded:r}=t;if(r)return[...e,n];const o=[...e];return o.splice(e.indexOf(n),1),o}return e},disabled:function(e=[],t){return t.type===d.SET_DISABLED_NODES?[...t.ids]:e},scrollToId:function(e=null,t){return t.type===d.SCROLL_TO_ID?null===t.nodeId?null:{id:t.nodeId}:e}});var w=n(497754),P=n.n(w),O=n(868788),L=n(469690),k=n(584952),B=n(601227);var F=n(116812),A=n(298314),z=n(878112),W=n(180185),U=n(269842),H=n(522224),Z=n(535842);const K={[W.Modifiers.Mod]:1,[W.Modifiers.Shift]:2};var V=n(569533),j=n(856726);const G=()=>{};class X extends r.PureComponent{constructor(){super(...arguments),this._ref=null,this._handleRef=e=>{this._ref=e;const{connectDragSource:t,connectDropTarget:n,connectDragPreview:r}=this.props;(0,h.ensureDefined)(n)(this._ref),(0,h.ensureDefined)(t)(this._ref),(0,h.ensureDefined)(r)((0,A.getEmptyImage)(),{captureDraggingState:!0})},this._handleTouchStart=e=>{const t=(e,t)=>{const n=function(e,t){try{const n=document.createEvent("TouchEvent");return n.initTouchEvent(e,!0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,t.touches,t.targetTouches,t.changedTouches),n}catch{return null}}(e,t);if(n)return n;const r=Array.from(t.changedTouches),o=Array.from(t.touches),s=Array.from(t.targetTouches);return new TouchEvent(e,{bubbles:!0,changedTouches:r,touches:o,targetTouches:s})},n=e.target;if(n instanceof Element){const e=e=>{const r=e;if(!n.isConnected){r.preventDefault();const e=t("touchmove",r);document.body.dispatchEvent(e)}},r=o=>{const s=o;if(!n.isConnected){s.preventDefault();const e=t("touchend",s);document.body.dispatchEvent(e)}n.removeEventListener("touchend",r),n.removeEventListener("touchmove",e)};n.addEventListener("touchend",r),n.addEventListener("touchmove",e)}}}componentDidMount(){this._ref?.addEventListener("touchstart",this._handleTouchStart)}componentWillUnmount(){this._ref?.removeEventListener("touchstart",this._handleTouchStart)}render(){return r.createElement(q,{...this.props,reference:this._handleRef})}getNode(){return(0,h.ensureNotNull)(this._ref)}}const q=e=>{
const{id:t,isSelected:n,isOffset:o,isExpandable:s,setIsSelected:i,isDisabled:l,isExpanded:c,onClick:a,parentId:d,setIsExpanded:u,reference:p,isFirstListItem:f,isLastListItem:h,nodeRenderer:m,isChildOfSelected:E=!1,isStuckNode:g}=e,{size:S,smallSizeTreeNodeAction:v}=(0,r.useContext)(Z.SizeContext),T=(0,r.useRef)(null),D=(0,U.createSafeMulticastEventHandler)((e=>T.current=e),p);let[N,y]=(0,H.useHover)();return(B.CheckMobile.any()||B.CheckMobile.isIPad())&&(N=n,y={onMouseOut:G,onMouseOver:G}),r.createElement("div",{className:w(j.wrap,n&&j.selected,E&&j.childOfSelected,l&&j.disabled,s&&j.expandable),onClick:1===S&&0===v?b:function(e){if(e.defaultPrevented)return;const r=K[(0,W.modifiersFromEvent)(e)]||0;!l&&i&&i(t,!n,r);a&&0===r&&a(e,t)},onContextMenu:b,ref:D,...y},s&&r.createElement(z.Icon,{icon:V,className:w(j.expandHandle,c&&j.expanded),onClick:function(e){e.preventDefault(),s&&u(t,!c)},onMouseDown:function(e){e.preventDefault()}}),m({id:t,isOffset:o,parentId:d,isDisabled:l,isSelected:n,isChildOfSelected:E,isHovered:N,isExpanded:c,isFirstListItem:f,isLastListItem:h,isStuckNode:g}));function b(){l||n||!i||i(t,!0)}},$=r.createContext({});function Y(e,t){const{id:n}=t,r=(0,p.nodeSelector)(e,n),o=(0,p.isSelectedSelector)(e,n);let s=!1,i=r.parentId;for(;i&&!s;)s=(0,p.isSelectedSelector)(e,i),i=(0,p.nodeSelector)(e,i).parentId;return{...r,isSelected:o,isChildOfSelected:s,isExpanded:r.children.length>0&&(0,p.isExpandedSelector)(e,n),isExpandable:r.children.length>0,isDisabled:(0,p.isDisabledSelector)(e,n)}}function J(e){return(0,C.bindActionCreators)({setIsExpanded:u.setIsExpanded,processDropTarget:u.processDropTarget,dropSelection:u.dropSelection,selectNext:u.selectNext,selectPrevious:u.selectPrevious,setIsSelected:u.setIsSelected,endDrag:u.endDrag},e)}const Q=(0,s.connect)(Y,J,null,{context:$})((function(e){const t=(0,r.useRef)(null),[,n,o]=(0,F.useDrag)({type:"node",item:t=>{const{id:n,isDisabled:r,isSelected:o}=e;return r||o||e.setIsSelected(n,!0),e},end:e=>{e.endDrag()}}),[,s]=(0,L.useDrop)({accept:"node",hover:(n,r)=>{const o=t.current;if(!o)return;if(e.isStuckNode)return;const s=o.getNode(),i=s.getBoundingClientRect(),l=i.bottom-i.top,c=r.getClientOffset();if(c){const t=c.y-i.top;let n,r;if(n=0===e.children.length?t<l/2?"before":"after":t<l/3?"before":e.isExpanded||t>=l/3&&t<2*l/3?"inside":"after",void 0!==e.getContainerElement){const t=e.getContainerElement().getBoundingClientRect();r={top:i.top-t.top,left:i.left-t.left,bottom:i.top-t.top+i.height,right:i.left-t.left+i.width,height:i.height,width:i.width}}else r={top:s.offsetTop,left:s.offsetLeft,bottom:s.offsetTop+s.offsetHeight,right:s.offsetLeft+s.offsetWidth,height:s.offsetHeight,width:s.offsetWidth};e.processDropTarget(e,n,c.x-i.left<48,r,e.isLastChild)}}});return r.createElement(X,{...e,connectDragSource:n,connectDropTarget:s,connectDragPreview:o,ref:t})})),ee=(0,s.connect)(Y,J,null,{context:$})(q);var te=n(698043),ne=n(365982),re=n(8361);function oe(e){const t=e(),n=(0,r.useRef)(t);n.current=t;const[o,s]=(0,r.useState)(n.current),i=(0,r.useRef)(null);return(0,
r.useEffect)((()=>{null===i.current&&(i.current=requestAnimationFrame((()=>{i.current=null,s(n.current)})))})),(0,r.useEffect)((()=>()=>{i.current&&cancelAnimationFrame(i.current)}),[]),o}function se(e){const{dropTargetOffset:t,mousePosition:n}=e;if(!t)return{display:"none"};const{x:r,y:o}=t,s=n&&t?n.y-t.y:0,i=`translate(${r+(n&&t?n.x-t.x:0)}px, ${o+s}px)`;return{transform:i,WebkitTransform:i}}const ie={top:0,left:0,position:"fixed",pointerEvents:"none",zIndex:100,opacity:.5,width:300,backgroundColor:"red"};function le(e){return{isDragging:e.isDragging()&&"node"===e.getItemType(),mousePosition:e.getClientOffset(),dropTargetOffset:e.getSourceClientOffset()}}const ce=(0,s.connect)((function(e){return{items:(0,p.renderDragListSelector)(e)}}),null,null,{context:$})((function(e){const{items:t,isDragging:n,nodeRenderer:o,dragPreviewRenderer:s}=e;return oe((function(){return n?r.createElement(re.Portal,null,r.createElement("div",{style:{...ie,...se(e)}},t.map((e=>{if(s){const t=s;return r.createElement(t,{key:e.id,...e})}return r.createElement(ee,{id:e.id,key:e.id,nodeRenderer:o,isDragPreview:!0,isOffset:e.level>1})})))):null}))}));function ae(e){return r.createElement(ce,{...e,...(0,ne.useDragLayer)(le)})}var de=n(693838),ue=n(73288),pe=n(139043);const fe=r.forwardRef(((e,t)=>{const n=(0,r.useRef)(null);return e.connectDropTarget(n),(0,r.useImperativeHandle)(t,(()=>({getNode:()=>(0,h.ensureNotNull)(n.current)})),[]),r.createElement("div",{ref:n,style:{height:"100%",width:"100%"}})}));function he(e){const t=(0,r.useRef)(null),[,n]=(0,L.useDrop)({accept:"node",hover:(n,r)=>{if(!t.current)return;const o=r.getClientOffset();if(null===o)return;const s=e.getOrderedNodes();if(0===s.length)return;const i=t.current.getNode().getBoundingClientRect(),l=e.getContainerElement().getBoundingClientRect();if("first"===e.type){const t={top:i.top-l.top+i.height,left:i.left-l.left,bottom:i.top-l.top+i.height,right:i.left-l.left+i.width,height:0,width:i.width};e.processDropTarget(s[0],"before",!1,t,!1)}if("last"===e.type){const t=o.x-i.left<48,n=s[s.length-1],r=t&&2===n.level?(0,h.ensureDefined)(s.find((e=>e.id===n.parentId))):n,c={top:i.top-l.top,left:i.left-l.left,bottom:i.top-l.top,right:i.left-l.left+i.width,height:i.height,width:i.width};e.processDropTarget(r,"after",t,c,!1)}}});return r.createElement(fe,{...e,connectDropTarget:n,ref:t})}const me=r.createContext({isOver:!1,transform:void 0});var Ee=n(336612);function ge(e){const{dropType:t,boundBox:n}=e,{top:r,bottom:o,left:s}=(0,h.ensureDefined)(n);return[s,"before"===t||"inside"===t?r:o]}function Se(e){return{isDragging:e.isDragging()}}const ve=(0,s.connect)((function(e){const{boundBox:t,dropType:n,node:r}=(0,p.dropTargetSelector)(e);return{boundBox:t,dropType:n,level:r?r.level:void 0}}),null,null,{context:$})((function(e){const{dropType:t,boundBox:n,isDragging:o,level:s,transform:i=ge}=e;return oe((function(){if(!o||!t||!n)return null;const l={[Ee.dropTarget]:"inside"!==t,[Ee.dropTargetInside]:"inside"===t},{width:c,height:a}=n,[d,u]=i(e),p=`translate(${d}px, ${"inside"===t?u:u-1}px)`
;return r.createElement("div",{className:w(l),style:{position:"absolute",transform:p,WebkitTransform:p,top:0,left:2===s?"46px":0,width:2===s?c-46+"px":c,height:"inside"===t?a:"2px"}})}))}));function Te(e){const{isDragging:t}=(0,ne.useDragLayer)(Se);return r.createElement(ve,{...e,isDragging:t})}const De=r.createContext(null);var Ne=n(469867),ye=n.n(Ne);const be=r.forwardRef(((e,t)=>{const n=(0,r.useContext)(me),o=(0,r.useContext)(De),s=(0,r.useMemo)((()=>o?.readOnly?ee:Q),[o?.readOnly]);return r.createElement("div",{...e,ref:t},o?.id?r.createElement("div",{className:P()(ye().sticky,!o.isShowStuck&&ye().hideSticky)},r.createElement(s,{id:o.id,key:o.id,isStuckNode:!0,nodeRenderer:o.nodeRenderer,readOnly:o.readOnly,onClick:o.onClick,getContainerElement:o.getContainerElement})):null,e.children,n.isOver&&r.createElement(Te,{transform:n.transform}))}));var Ce=n(431520),_e=n(127802);const Ie=38+W.Modifiers.Shift,Re=40+W.Modifiers.Shift;const Me=r.forwardRef((function(e,t){const{navigationKeys:n,renderList:o,stopMultiSelect:s,startMultiSelect:i,isMultiSelecting:l,nodeRenderer:c,dragPreviewRenderer:a,className:d,connectDropTarget:u,readOnly:p,onClick:f,dropLayerTransform:m,setFocusedNode:E,scrollToId:g,rowHeight:S,onMultiSelectPrevious:v,onMultiSelectNext:T,onMoveCursorToNext:D,onMoveCursorToPrevious:N,onKeyDown:y,outerRef:b,width:C,height:_,isOver:I,processDropTarget:R,autofocus:M,accessibleScope:x}=e,[w,L]=(0,r.useState)(),[F,A]=(0,r.useState)(!0),[z,U]=(0,r.useState)(!1),H=(0,r.useContext)(de.ObjectTreeContext),Z=(0,r.useRef)(null),K=(0,O.useDragDropManager)();(0,r.useEffect)((()=>{const e=K.getMonitor();e.subscribeToStateChange((()=>A(!e.isDragging())))}),[]),(0,r.useEffect)((()=>{M&&Z.current?.focus()}),[]),(0,r.useEffect)((()=>{const e=e=>{[W.Modifiers.Mod,W.Modifiers.Shift].includes((0,W.modifiersFromEvent)(e))&&i()},t=e=>{l&&![W.Modifiers.Mod,W.Modifiers.Shift].includes((0,W.modifiersFromEvent)(e))&&s()};return document.addEventListener("keydown",e),document.addEventListener("keyup",t),document.addEventListener("mousemove",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t),document.removeEventListener("mousemove",t)}}),[l]),function(e){(0,r.useEffect)((()=>{if(B.isEdge){let t=null;const n=(0,h.ensureNotNull)(e.current),r=e=>{if(e.target instanceof Element){const n=(0,h.ensureNotNull)(e.target.closest("[draggable]"));n instanceof HTMLElement&&(n.style.opacity="0",t=requestAnimationFrame((()=>n.style.opacity="1")))}};return n.addEventListener("dragstart",r),()=>{n.removeEventListener("dragstart",r),null!==t&&cancelAnimationFrame(t)}}return()=>{}}),[])}(Z);const V=(0,r.useCallback)((()=>(0,h.ensureNotNull)(ce.current)),[]),j=(0,r.useCallback)((()=>o),[o]),G=(0,r.useMemo)((()=>{const e=p?ee:Q,t=[];let n,s;t.push({type:"padding",node:r.createElement(he,{type:"first",key:"padding-top",getContainerElement:V,getOrderedNodes:j,processDropTarget:R})});for(let i=0;i<o.length;i++){const l=o[i];l.isSticky&&(s=l.id),1===l.level&&(void 0!==n&&n!==l.parentId&&t.push({type:"separator",
node:r.createElement("div",{key:n+"_separator",className:_e.separator})}),n=l.parentId),t.push({type:"node",stickyNodeId:s,isSticky:l.isSticky,node:r.createElement(e,{id:l.id,key:l.id,isFirstListItem:0===i,isLastListItem:i===o.length-1,isExpandable:l.children.length>0,nodeRenderer:c,readOnly:p,onClick:f,isOffset:l.level>1,getContainerElement:V})})}return t.push({type:"padding",node:r.createElement(he,{type:"last",key:"padding-bottom",getContainerElement:V,getOrderedNodes:j,processDropTarget:R})}),t}),[o]),X=(0,r.useMemo)((()=>{if(w){const e=G[w];return e&&"stickyNodeId"in e?e.stickyNodeId:void 0}}),[w,G]),q=(0,r.useRef)([]);q.current=G;const $=(0,r.useCallback)((e=>{let{style:t}=e;const{index:n}=e,o=q.current[n];return n===q.current.length-1&&(t={...t,bottom:0,minHeight:t.height},delete t.height),"isSticky"in o&&o.isSticky&&(t={...t,zIndex:se.current?.scrollTop&&t.top&&+t.top<=se.current?.scrollTop?2:5}),r.createElement("div",{style:t},q.current[n].node)}),[]),Y=(0,r.useCallback)((e=>{const t=q.current[e];return"padding"===t.type?6:"function"==typeof S?S(e,t):S}),[S]),J=(0,r.useCallback)((e=>(0,h.ensure)(q.current[e].node.key)),[]),ne=(0,r.useMemo)((()=>null===g?{index:-1}:{index:q.current.findIndex((e=>e.node.key===g.id))}),[g]);u(Z);const[re,oe,se,ie]=(0,pe.useOverlayScroll)(),le=(0,r.useRef)(null);(0,r.useEffect)((()=>(0,h.ensureNotNull)(le.current).resetAfterIndex(0,!0)),[G]),(0,r.useEffect)((()=>{let e=ne.index;const t=q.current[ne.index];w&&e<=w&&t&&"stickyNodeId"in t&&t.stickyNodeId&&(e-=1),(0,h.ensureNotNull)(le.current).scrollToItem(e)}),[ne]);const ce=(0,r.useRef)(null),fe=(0,r.useMemo)((()=>({isOver:I,transform:m})),[I,m]),Ee=(0,r.useMemo)((()=>({id:X,nodeRenderer:c,getContainerElement:V,onClick:f,isShowStuck:F,readOnly:p})),[X,c,V,F,f,p]),ge=(0,r.useRef)(null),Se=(0,r.useRef)({startScroll(e){const t=()=>{null!==se.current&&(ge.current=requestAnimationFrame(t),se.current.scrollBy({top:e}))};this.stopScroll(),t()},stopScroll(){null!==ge.current&&(cancelAnimationFrame(ge.current),ge.current=null)},getListElement:()=>se.current});return(0,r.useImperativeHandle)(t,(()=>Se.current),[]),(0,r.useEffect)((()=>()=>Se.current.stopScroll()),[I]),(0,r.useEffect)((()=>{if(!Z.current)return;function e(e){if(!t.matches(":focus-visible"))return;if(!H)return;const{viewModel:n}=H,r=n.selection();e.defaultPrevented||e.currentTarget!==e.target||r.selected().length||D()}const t=Z.current;return t.addEventListener("focus",e),()=>{t.removeEventListener("focus",e)}}),[Z,D,H]),r.createElement(me.Provider,{value:fe},r.createElement(De.Provider,{value:Ee},r.createElement("div",{...oe,className:P()(_e.tree,d,z&&_e.accessible),ref:Z,onFocus:function(e){if(void 0===x)return;x(e)&&U(!0)},onBlur:function(e){U(!1)},"data-name":"tree",tabIndex:0,onKeyDown:function(e){U(!1);const t=(0,W.hashFromEvent)(e);if(e.defaultPrevented||(0,te.isNativeUIInteraction)(t,e.target))return;const r=(0,h.ensureDefined)(re.scrollPosTop),o=(0,h.ensureDefined)(re.contentHeight),s=(0,h.ensureDefined)(re.containerHeight);if(s){const n=.875*s,i=r+s===o;switch(t){
case 35:i||(e.preventDefault(),ve(o));break;case 36:0!==r&&(e.preventDefault(),ve(0));break;case 33:0!==r&&(e.preventDefault(),ve(Math.max(0,r-n)));break;case 34:i||(e.preventDefault(),ve(Math.min(r+n,o)))}}H||t!==Ie||(e.preventDefault(),v());H||t!==Re||(e.preventDefault(),T());(38===t||void 0!==n&&"previous"===n[t])&&(e.preventDefault(),N());(40===t||void 0!==n&&"next"===n[t])&&(e.preventDefault(),D());if((8===t||46===t)&&H){const{viewModel:e}=H,t=e.selection(),n=t.selected();if(1!==n.length)return;const r=e.getNextNodeIdAfterRemove(n[0]);if(null===r)return;e.onChange().subscribe(null,(()=>{if(t.selected().length)return;const n=e.entity(r);n&&(t.set([n]),E(r))}),!0)}y?.(e)}},r.createElement(ue.OverlayScrollContainer,{...re,className:_e.overlayScrollWrap}),r.createElement(k.VariableSizeList,{ref:function(e){le.current=e},className:_e.listContainer,width:C,height:_,itemCount:G.length,itemSize:Y,children:$,itemKey:J,outerRef:function(e){se.current=e,b&&b(e)},innerRef:function(e){ce.current=e},innerElementType:be,onItemsRendered:function({visibleStartIndex:e}){L(e),ie()},overscanCount:20,direction:(0,Ce.isRtl)()?"rtl":"ltr"}),r.createElement(ae,{dragPreviewRenderer:a,nodeRenderer:c}))));function ve(e){se.current?.scrollTo({left:0,top:e})}}));const xe=(0,s.connect)((function(e){return{renderList:(0,p.renderListSelector)(e),orderedNodes:(0,p.orderedNodesSelector)(e),isMultiSelecting:(0,p.isMultiSelectingSelector)(e),selectedIds:(0,p.selectedIdsSelector)(e),scrollToId:(0,p.scrollToIdSelector)(e)}}),(function(e){return(0,C.bindActionCreators)({startMultiSelect:u.startMultiSelect,stopMultiSelect:u.stopMultiSelect,setFocusedNode:u.setFocusedNode,processDropTarget:u.processDropTarget,onMoveCursorToNext:u.selectNext,onMoveCursorToPrevious:u.selectPrevious,onMultiSelectPrevious:u.multiSelectPrevious,onMultiSelectNext:u.multiSelectNext},e)}),null,{context:$})((function(e){const t=(0,r.useRef)(null),[{isOver:n},o]=(0,L.useDrop)({accept:"node",drop:(n,r)=>{("touch"===e.drag||B.isFF)&&t.current?.stopScroll(),r.getItem().dropSelection()},hover:(n,r)=>{if("touch"!==e.drag&&!B.isFF)return;const o=r.getClientOffset();if(null===o)return;const s=t.current?.getListElement()??null;if(null===s)return;const i=s.getBoundingClientRect();((n,r,o)=>{const s=Math.abs(n-o),i=Math.abs(n-r);if(i>40&&s>40||s<=40&&i<=40)return void t.current?.stopScroll();var l,c,a,d;l=i>20&&i<=40,a=s<=20,d=i<=20,(c=s>20&&s<=40)||l?"touch"===e.drag?t.current?.startScroll(c?-5:5):t.current?.startScroll(c?-2:2):(a||d)&&("touch"===e.drag?t.current?.startScroll(a?-10:10):t.current?.startScroll(a?-5:5))})(o.y,i.bottom,i.top)},collect:e=>({isOver:e.isOver()})});return r.createElement(Me,{...e,isOver:n,connectDropTarget:o,ref:t})})),we={delayTouchStart:100};function Pe(e){const{canBeAddedToSelection:t,initState:n,onSelect:s,canMove:i,onDrop:l,onMove:c,nodes:a,selectedIds:d,onKeyboardSelect:p,saga:f,lastFocusedNodeObject:h,lastSyncTimestampRef:m,scrollToId:E,...g}=e,[S,v]=(0,r.useState)(null);return(0,r.useEffect)((()=>{const e=(0,o.default)();v(function(e){const t=(0,
C.applyMiddleware)(e);return(0,C.createStore)(x,t)}(e));const r=e.run(b,{initState:n,onKeyboardSelect:p,saga:f,canMove:i,onMove:c,onDrop:l,onSelect:s,canBeAddedToSelection:t});return()=>r.cancel()}),[]),(0,r.useEffect)((()=>(null!==S&&a&&(m&&(m.current=performance.now()),S.dispatch((0,u.syncNodes)(a))),()=>{})),[S,a]),(0,r.useEffect)((()=>{null!==S&&d&&S.dispatch((0,u.setSelectedIds)(d))}),[S,d]),(0,r.useEffect)((()=>{null!==S&&h?.id&&S.dispatch((0,u.setFocusedNode)(h.id))}),[S,h]),null===S?null:r.createElement(Oe,{store:S,scrollToId:E,...g})}const Oe=r.memo((function(e){const{store:t,scrollToId:n,...o}=e,a="touch"===e.drag?l.TouchBackend:i.HTML5Backend;return(0,r.useEffect)((()=>{t.dispatch((0,u.scrollToId)(n?.id??null))}),[n]),r.createElement(c.DndProvider,{backend:a,options:we},r.createElement(s.Provider,{store:t,context:$},r.createElement(xe,{...o})))}))},693838:(e,t,n)=>{"use strict";n.d(t,{ObjectTreeContext:()=>r});const r=n(50959).createContext(null)},215078:(e,t,n)=>{"use strict";function r(e){return t=>e+"__"+t}n.d(t,{createActionTypeFactory:()=>r})},163694:(e,t,n)=>{"use strict";n.d(t,{DrawerContext:()=>i,DrawerManager:()=>s});var r=n(50959),o=n(285089);class s extends r.PureComponent{constructor(e){super(e),this._isBodyFixed=!1,this._addDrawer=e=>{this.setState((t=>({stack:[...t.stack,e]})))},this._removeDrawer=e=>{this.setState((t=>({stack:t.stack.filter((t=>t!==e))})))},this.state={stack:[]}}componentDidUpdate(e,t){!t.stack.length&&this.state.stack.length&&((0,o.setFixedBodyState)(!0),this._isBodyFixed=!0),t.stack.length&&!this.state.stack.length&&this._isBodyFixed&&((0,o.setFixedBodyState)(!1),this._isBodyFixed=!1)}componentWillUnmount(){this.state.stack.length&&this._isBodyFixed&&(0,o.setFixedBodyState)(!1)}render(){return r.createElement(i.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.stack.length?this.state.stack[this.state.stack.length-1]:null}},this.props.children)}}const i=r.createContext(null)},759339:(e,t,n)=>{"use strict";n.d(t,{Drawer:()=>f});var r=n(50959),o=n(650151),s=n(497754),i=n(924910),l=n(8361),c=n(163694),a=n(28466),d=n(742554),u=n(536718);var p;function f(e){const{position:t="Bottom",onClose:n,children:d,reference:p,className:f,theme:m=u}=e,E=(0,o.ensureNotNull)((0,r.useContext)(c.DrawerContext)),[g]=(0,r.useState)((()=>(0,i.randomHash)())),S=(0,r.useRef)(null),v=(0,r.useContext)(a.CloseDelegateContext);return(0,r.useLayoutEffect)((()=>((0,o.ensureNotNull)(S.current).focus({preventScroll:!0}),v.subscribe(E,n),E.addDrawer(g),()=>{E.removeDrawer(g),v.unsubscribe(E,n)})),[]),r.createElement(l.Portal,null,r.createElement("div",{ref:p,className:s(u.wrap,u[`position${t}`])},g===E.currentDrawer&&r.createElement("div",{className:u.backdrop,onClick:n}),r.createElement(h,{className:s(m.drawer,u[`position${t}`],f),ref:S,"data-name":e["data-name"]},d)))}!function(e){e.Left="Left",e.Bottom="Bottom"}(p||(p={}));const h=(0,r.forwardRef)(((e,t)=>{const{className:n,...o}=e;return r.createElement(d.TouchScrollContainer,{className:s(u.drawer,n),
tabIndex:-1,ref:t,...o})}))},139043:(e,t,n)=>{"use strict";n.d(t,{useOverlayScroll:()=>c});var r=n(50959),o=n(650151),s=n(522224),i=n(601227);const l={onMouseOver:()=>{},onMouseOut:()=>{}};function c(e,t=i.CheckMobile.any()){const n=(0,r.useRef)(null),c=e||(0,r.useRef)(null),[a,d]=(0,s.useHover)(),[u,p]=(0,r.useState)({reference:n,containerHeight:0,containerWidth:0,contentHeight:0,contentWidth:0,scrollPosTop:0,scrollPosLeft:0,onVerticalChange:function(e){p((t=>({...t,scrollPosTop:e}))),(0,o.ensureNotNull)(c.current).scrollTop=e},onHorizontalChange:function(e){p((t=>({...t,scrollPosLeft:e}))),(0,o.ensureNotNull)(c.current).scrollLeft=e},visible:a}),f=(0,r.useCallback)((()=>{if(!c.current)return;const{clientHeight:e,scrollHeight:t,scrollTop:r,clientWidth:o,scrollWidth:s,scrollLeft:i}=c.current,l=n.current?n.current.offsetTop:0;p((n=>({...n,containerHeight:e-l,contentHeight:t-l,scrollPosTop:r,containerWidth:o,contentWidth:s,scrollPosLeft:i})))}),[]);function h(){p((e=>({...e,scrollPosTop:(0,o.ensureNotNull)(c.current).scrollTop,scrollPosLeft:(0,o.ensureNotNull)(c.current).scrollLeft})))}return(0,r.useEffect)((()=>{a&&f(),p((e=>({...e,visible:a})))}),[a]),(0,r.useEffect)((()=>{const e=c.current;return e&&e.addEventListener("scroll",h),()=>{e&&e.removeEventListener("scroll",h)}}),[c]),[u,t?l:d,c,f]}},800296:(e,t,n)=>{"use strict";n.d(t,{ListItemButton:()=>c});var r=n(50959),o=n(497754),s=n.n(o),i=n(878112),l=n(8510);function c(e){const{className:t,disabled:n,...o}=e;return r.createElement(i.Icon,{className:s()(l.button,n&&l.disabled,t),...o})}},742554:(e,t,n)=>{"use strict";n.d(t,{TouchScrollContainer:()=>a});var r=n(50959),o=n(259142),s=n(650151),i=n(601227);const l=CSS.supports("overscroll-behavior","none");let c=0;const a=(0,r.forwardRef)(((e,t)=>{const{children:n,...s}=e,a=(0,r.useRef)(null);return(0,r.useImperativeHandle)(t,(()=>a.current)),(0,r.useLayoutEffect)((()=>{if(i.CheckMobile.iOS())return c++,null!==a.current&&(l?1===c&&(document.body.style.overscrollBehavior="none"):(0,o.disableBodyScroll)(a.current,{allowTouchMove:d(a)})),()=>{c--,null!==a.current&&(l?0===c&&(document.body.style.overscrollBehavior=""):(0,o.enableBodyScroll)(a.current))}}),[]),r.createElement("div",{ref:a,...s},n)}));function d(e){return t=>{const n=(0,s.ensureNotNull)(e.current),r=document.activeElement;return!n.contains(t)||null!==r&&n.contains(r)&&r.contains(t)}}},526448:e=>{e.exports={accessible:"accessible-NQERJsv9",active:"active-NQERJsv9"}},697373:e=>{e.exports={button:"button-xNqEcuN2"}},295389:e=>{e.exports={button:"button-merBkM5y",hover:"hover-merBkM5y",clicked:"clicked-merBkM5y",accessible:"accessible-merBkM5y",arrow:"arrow-merBkM5y",arrowWrap:"arrowWrap-merBkM5y",isOpened:"isOpened-merBkM5y"}},994567:(e,t,n)=>{"use strict";n.d(t,{focusFirstMenuItem:()=>d,handleAccessibleMenuFocus:()=>c,handleAccessibleMenuKeyDown:()=>a,queryMenuElements:()=>f});var r=n(442092),o=n(333086),s=n(180185),i=n(32556);const l=[37,39,38,40];function c(e,t){if(!e.target)return;const n=e.relatedTarget?.getAttribute("aria-activedescendant")
;if(e.relatedTarget!==t.current){const e=n&&document.getElementById(n);if(!e||e!==t.current)return}d(e.target)}function a(e){if(e.defaultPrevented)return;const t=(0,s.hashFromEvent)(e);if(!l.includes(t))return;const n=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const i=f(e.currentTarget).sort(r.navigationOrderComparator);if(0===i.length)return;const c=document.activeElement.closest('[data-role="menuitem"]')||document.activeElement.parentElement?.querySelector('[data-role="menuitem"]');if(!(c instanceof HTMLElement))return;const a=i.indexOf(c);if(-1===a)return;const d=h(c),m=d.indexOf(document.activeElement),E=-1!==m,g=e=>{n&&(0,o.becomeSecondaryElement)(n),(0,o.becomeMainElement)(e),e.focus()};switch((0,r.mapKeyCodeToDirection)(t)){case"inlinePrev":if(!d.length)return;e.preventDefault(),g(0===m?i[a]:E?u(d,m,-1):d[d.length-1]);break;case"inlineNext":if(!d.length)return;e.preventDefault(),m===d.length-1?g(i[a]):g(E?u(d,m,1):d[0]);break;case"blockPrev":{e.preventDefault();const t=u(i,a,-1);if(E){const e=p(t,m);g(e||t);break}g(t);break}case"blockNext":{e.preventDefault();const t=u(i,a,1);if(E){const e=p(t,m);g(e||t);break}g(t)}}}function d(e){const[t]=f(e);t&&((0,o.becomeMainElement)(t),t.focus())}function u(e,t,n){return e[(t+e.length+n)%e.length]}function p(e,t){const n=h(e);return n.length?n[(t+n.length)%n.length]:null}function f(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled]):not([aria-disabled])')).filter((0,i.createScopedVisibleElementFilter)(e))}function h(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled]):not([aria-disabled])")).filter((0,i.createScopedVisibleElementFilter)(e))}},565631:(e,t,n)=>{"use strict";n.d(t,{AccessibleIconButton:()=>s});var r=n(50959),o=n(511349);const s=(0,r.forwardRef)((function(e,t){const{tooltip:n,...s}=e;return r.createElement(o.ToolWidgetIconButton,{"aria-label":n,...s,tag:"button",ref:t,"data-tooltip":n,"data-tooltip-show-on-focus":"true"})}))},975598:(e,t,n)=>{"use strict";n.d(t,{AccessibleMenuButton:()=>l});var r=n(50959),o=n(718736),s=n(679458),i=n(994567);const l=(0,r.forwardRef)((function(e,t){const{tooltip:n,tag:l,buttonRef:c,reference:a,...d}=e,u=(0,o.useFunctionalRefObject)(a??null);return r.createElement(s.ToolWidgetMenu,{"aria-label":n,...d,ref:t,tag:l??"button",reference:c??u,"data-tooltip":n,onMenuKeyDown:i.handleAccessibleMenuKeyDown,onMenuFocus:e=>(0,i.handleAccessibleMenuFocus)(e,c??u)})}))},46305:(e,t,n)=>{"use strict";n.d(t,{AccessibleMenuItem:()=>d});var r=n(50959),o=n(497754),s=n.n(o),i=n(930202),l=n(865266),c=n(192063),a=n(526448);function d(e){const{className:t,...n}=e,[o,d]=(0,l.useRovingTabindexElement)(null);return r.createElement(c.PopupMenuItem,{...n,className:s()(a.accessible,e.isActive&&a.active,t),reference:o,tabIndex:d,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,i.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),o.current instanceof HTMLElement&&o.current.click())},"data-role":"menuitem","aria-disabled":e.isDisabled||void 0,
toolboxRole:"toolbar"})}},162458:(e,t,n)=>{"use strict";n.d(t,{HorizontalAttachEdge:()=>o,HorizontalDropDirection:()=>i,VerticalAttachEdge:()=>r,VerticalDropDirection:()=>s,getPopupPositioner:()=>a});var r,o,s,i,l=n(650151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(r||(r={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(o||(o={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(s||(s={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(i||(i={}));const c={verticalAttachEdge:r.Bottom,horizontalAttachEdge:o.Left,verticalDropDirection:s.FromTopToBottom,horizontalDropDirection:i.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function a(e,t){return n=>{const{contentWidth:a,contentHeight:d,availableHeight:u}=n,p=(0,l.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:f=c.horizontalAttachEdge,horizontalDropDirection:h=c.horizontalDropDirection,horizontalMargin:m=c.horizontalMargin,verticalMargin:E=c.verticalMargin,matchButtonAndListboxWidths:g=c.matchButtonAndListboxWidths}=t;let S=t.verticalAttachEdge??c.verticalAttachEdge,v=t.verticalDropDirection??c.verticalDropDirection;S===r.AutoStrict&&(u<p.y+p.height+E+d?(S=r.Top,v=s.FromBottomToTop):(S=r.Bottom,v=s.FromTopToBottom));const T=S===r.Top?-1*E:E,D=f===o.Right?p.right:p.left,N=S===r.Top?p.top:p.bottom,y={x:D-(h===i.FromRightToLeft?a:0)+m,y:N-(v===s.FromBottomToTop?d:0)+T};return g&&(y.overrideWidth=p.width),y}}},511349:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetIconButton:()=>l});var r=n(50959),o=n(497754),s=n(155352),i=n(697373);const l=r.forwardRef((function(e,t){const{className:n,id:l,...c}=e;return r.createElement(s.ToolWidgetButton,{id:l,"data-name":l,...c,ref:t,className:o(n,i.button)})}))},679458:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_TOOL_WIDGET_MENU_THEME:()=>g,ToolWidgetMenu:()=>v});var r=n(50959),o=n(497754),s=n.n(o),i=n(930202),l=n(624216),c=n(515783),a=n(800417),d=n(163694),u=n(759339),p=n(162458),f=n(930052),h=n(440891),m=n(111706),E=n(295389);const g=E;var S;!function(e){e[e.Vertical=2]="Vertical",e[e.Horizontal=0]="Horizontal"}(S||(S={}));class v extends r.PureComponent{constructor(e){super(e),this._wrapperRef=null,this._controller=r.createRef(),this._onPopupCloseOnClick=e=>{"keyboard"===e.detail.clickType&&this.focus()},this._handleMenuFocus=e=>{e.relatedTarget===this._wrapperRef&&this.setState((e=>({...e,isOpenedByButton:!0}))),this.props.onMenuFocus?.(e)},this._handleWrapperRef=e=>{this._wrapperRef=e,this.props.reference&&this.props.reference(e)},this._handleOpen=()=>{"div"!==this.props.tag&&(this.setState((e=>({...e,isOpenedByButton:!1}))),this.props.menuReference?.current?.addEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._controller.current?.focus())},this._handleClick=e=>{(h.enabled("skip_event_target_check")||e.target instanceof Node)&&e.currentTarget.contains(e.target)&&(this._handleToggleDropdown(void 0,(0,m.isKeyboardClick)(e)),
this.props.onClick&&this.props.onClick(e,!this.state.isOpened))},this._handleToggleDropdown=(e,t=!1)=>{const{onClose:n,onOpen:r}=this.props,{isOpened:o}=this.state,s="boolean"==typeof e?e:!o;this.setState({isOpened:s,shouldReturnFocus:!!s&&t}),s&&r&&r(),!s&&n&&n()},this._handleClose=()=>{this.close()},this._handleKeyDown=e=>{const{orientation:t="horizontal"}=this.props;if(e.defaultPrevented)return;if(!(e.target instanceof Node))return;const n=(0,i.hashFromEvent)(e);if(e.currentTarget.contains(e.target))switch(n){case 40:if("div"===this.props.tag||"horizontal"!==t)return;if(this.state.isOpened)return;e.preventDefault(),this._handleToggleDropdown(!0,!0);break;case 27:if(!this.state.isOpened||!this.props.closeOnEsc)return;e.preventDefault(),e.stopPropagation(),this._handleToggleDropdown(!1)}else{if("div"===this.props.tag)return;switch(n){case 27:{e.preventDefault();const{shouldReturnFocus:t,isOpenedByButton:n}=this.state;this._handleToggleDropdown(!1),t&&n&&this._wrapperRef?.focus();break}}}},this.state={isOpened:!1,shouldReturnFocus:!1,isOpenedByButton:!1}}render(){const{tag:e="div",id:t,arrow:n,content:o,isDisabled:i,isDrawer:l,isShowTooltip:d,title:u,className:p,hotKey:h,theme:m,drawerBreakpoint:E,tabIndex:g,isClicked:S}=this.props,{isOpened:v}=this.state,D=s()(p,m.button,{"apply-common-tooltip":d||!i,[m.isDisabled]:i,[m.isOpened]:v,[m.clicked]:S}),N=T(o)?o({isOpened:v}):o;return"button"===e?r.createElement("button",{type:"button",id:t,className:s()(D,m.accessible),disabled:i,onClick:this._handleClick,title:u,"data-tooltip-hotkey":h,ref:this._handleWrapperRef,onKeyDown:this._handleKeyDown,tabIndex:g,...(0,a.filterDataProps)(this.props),...(0,a.filterAriaProps)(this.props)},N,n&&r.createElement("div",{className:m.arrow},r.createElement("div",{className:m.arrowWrap},r.createElement(c.ToolWidgetCaret,{dropped:v}))),this.state.isOpened&&(E?r.createElement(f.MatchMedia,{rule:E},(e=>this._renderContent(e))):this._renderContent(l))):r.createElement("div",{id:t,className:D,onClick:i?void 0:this._handleClick,title:u,"data-tooltip-hotkey":h,ref:this._handleWrapperRef,"data-role":"button",tabIndex:g,onKeyDown:this._handleKeyDown,"aria-haspopup":this.props["aria-haspopup"],...(0,a.filterDataProps)(this.props)},N,n&&r.createElement("div",{className:m.arrow},r.createElement("div",{className:m.arrowWrap},r.createElement(c.ToolWidgetCaret,{dropped:v}))),this.state.isOpened&&(E?r.createElement(f.MatchMedia,{rule:E},(e=>this._renderContent(e))):this._renderContent(l)))}close(){this.props.menuReference?.current?.removeEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._handleToggleDropdown(!1)}focus(){this._wrapperRef?.focus()}update(){null!==this._controller.current&&this._controller.current.update()}_renderContent(e){const{menuDataName:t,minWidth:n,menuClassName:o,menuRole:s,maxHeight:i,drawerPosition:c="Bottom",children:a,noMomentumBasedScroll:f}=this.props,{isOpened:h}=this.state,m={horizontalMargin:this.props.horizontalMargin||0,verticalMargin:this.props.verticalMargin||2,
verticalAttachEdge:this.props.verticalAttachEdge,horizontalAttachEdge:this.props.horizontalAttachEdge,verticalDropDirection:this.props.verticalDropDirection,horizontalDropDirection:this.props.horizontalDropDirection,matchButtonAndListboxWidths:this.props.matchButtonAndListboxWidths},E=Boolean(h&&e&&c),g=T(a)?a({isDrawer:E}):a;return E?r.createElement(d.DrawerManager,null,r.createElement(u.Drawer,{reference:this.props.drawerReference,onClose:this._handleClose,position:c,"data-name":t},g)):r.createElement(l.PopupMenu,{reference:this.props.menuReference,role:s,controller:this._controller,closeOnClickOutside:this.props.closeOnClickOutside,doNotCloseOn:this,isOpened:h,minWidth:n,onClose:this._handleClose,position:(0,p.getPopupPositioner)(this._wrapperRef,m),className:o,maxHeight:i,"data-name":t,tabIndex:"div"!==this.props.tag?-1:void 0,onOpen:this._handleOpen,onKeyDown:this.props.onMenuKeyDown,onFocus:this._handleMenuFocus,noMomentumBasedScroll:f},g)}}function T(e){return"function"==typeof e}v.defaultProps={arrow:!0,closeOnClickOutside:!0,theme:E}},460925:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12 4h3v1h-1.04l-.88 9.64a1.5 1.5 0 0 1-1.5 1.36H6.42a1.5 1.5 0 0 1-1.5-1.36L4.05 5H3V4h3v-.5C6 2.67 6.67 2 7.5 2h3c.83 0 1.5.67 1.5 1.5V4ZM7.5 3a.5.5 0 0 0-.5.5V4h4v-.5a.5.5 0 0 0-.5-.5h-3ZM5.05 5l.87 9.55a.5.5 0 0 0 .5.45h5.17a.5.5 0 0 0 .5-.45L12.94 5h-7.9Z"/></svg>'},636296:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8 9.5H6.5a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V20m-8-1.5h11a1 1 0 0 0 1-1v-11a1 1 0 0 0-1-1h-11a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1z"/></svg>'}}]);