"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1652],{751792:(e,t,i)=>{i.r(t),i.d(t,{TradedSourcesManager:()=>ye,canCreateOrUpdateSourceByContext:()=>_e});var r,s=i(154834),o=i(650151),a=(i(601227),i(41899)),n=i(759333),d=i(341991),l=i(671945),c=i(248361),p=i(599016),h=i(145367),u=i(470692),m=i(936145),_=i(912321),y=i(184114),b=i(374410),v=i(658895),f=i(240534),g=i(293029),S=i(658098),P=i(305141);!function(e){e[e.Threshold=4]="Threshold"}(r||(r={}));class k{constructor(){this._prevPixelRatio=null,this._prevMediaWidth=null,this._horizontalBorderPoint=null,this._renderers=[],this._visibleItemsRenderers=null,this._sortedItemRenderers=null,this._isNoOverlapMode=new f.WatchedValue(!1),this._isItemsOverlap=!1,this._invalidated=!1}registerRenderer(e){this._renderers.push(e)}removeRenderer(e){if(null!==e)for(let t=0;t<this._renderers.length;t++){if(this._renderers[t]===e){this._renderers.splice(t,1);break}}}invalidateCache(){this._renderers.forEach((e=>e.clearCache())),this._horizontalBorderPoint=null,this._visibleItemsRenderers=null,this._sortedItemRenderers=null,this._invalidated=!0}isItemsOverlap(){return this._isItemsOverlap}setNoOverlapMode(e){this.invalidateCache(),this._isNoOverlapMode.setValue(e)}isNoOverlapMode(){return this._isNoOverlapMode.value()}noOverlapModeChanged(){return this._isNoOverlapMode}getBorderPoint(e){return this._horizontalBorderPoint}alignItems(e){if(this._invalidated){if(this._checkCacheValid(e),this._alignItemsHorizontally(e),this._checkIfItemsOverlap(e),this._isNoOverlapMode.value()){if(!this._isItemsOverlap)return void this.setNoOverlapMode(!1);this._alignItemsVertically(e)}this._invalidated=!1}}_checkCacheValid(e){const{horizontalPixelRatio:t,verticalPixelRatio:i}=e,r=null===this._prevPixelRatio||!(0,P.equalPixelRatios)(this._prevPixelRatio,e),s=this._prevMediaWidth!==e.mediaSize.width;(r||s)&&(this._prevPixelRatio={horizontalPixelRatio:t,verticalPixelRatio:i},this._prevMediaWidth=e.mediaSize.width,r&&this._renderers.forEach((e=>e.clearTextCache())),this.invalidateCache())}_alignItemsHorizontally(e){if(null===this._horizontalBorderPoint){const t=this._getVisibleItemRenderers(e);if(0===t.length)return;const i=t.map((t=>t.rect(e)));let r=i[0].right,s=i[0].left;for(const e of i)e.right>r&&(r=e.right),e.left<s&&(s=e.left);this._horizontalBorderPoint={rightmostBorder:r,leftmostBorder:s},this._renderers.forEach((t=>t.applyHorizontalOffset((0,o.ensureNotNull)(this._horizontalBorderPoint),e)))}}_checkIfItemsOverlap(e){const t=this._getSortedRenderers(e);let i=!1;for(let s=1;s<t.length;s++){const o=t[s],a=t[s-1];if(o.rect(e).top-a.rect(e).bottom+r.Threshold<=0){i=!0;break}}this._isItemsOverlap=i}_alignItemsVertically(e){const t=this._getSortedRenderers(e);for(let i=1;i<t.length;i++){const s=t[i],o=t[i-1];s.rectWithOffsets(e).top-o.rectWithOffsets(e).bottom+r.Threshold<=0&&(s.clearCache(),s.setAlignedTopCoordinate(o.rectWithOffsets(e).bottom))}}_getSortedRenderers(e){
return null===this._sortedItemRenderers&&(this._sortedItemRenderers=this._getVisibleItemRenderers(e).sort(((e,t)=>t.data().sortingIndex-e.data().sortingIndex))),this._sortedItemRenderers}_getVisibleItemRenderers(e){if(null===this._visibleItemsRenderers){this._visibleItemsRenderers=[];for(const t of this._renderers){const i=t.itemRenderers().filter((t=>{const i=t.rect(e);return t.data().visible&&i.top<e.bitmapSize.height&&i.bottom>0}));this._visibleItemsRenderers.push(...i)}}return this._visibleItemsRenderers}}var T=i(14654),O=i(167543),C=i(172912),I=i(329452);const D=(0,l.getLogger)("Trading.Source.SymbolDataProvider");var G;!function(e){e[e.Order=0]="Order",e[e.Position=1]="Position"}(G||(G={}));class w{constructor(e,t,i,r,s){this._symbol=null,this._actualTradingSymbol=null,this._isStarted=!1,this._isSubscribed=!1,this._symbolData=null,this._last=null,this._currencies={},this._specificTradingOptions=null,this._pipValueType=null,this._pipValuesSpawn=null,this._onUpdate=new I.Delegate,this._mainSeries=e,this._realtimeProvider=t,this._symbolSpecificTradingOptionsGetter=s.symbolSpecificTradingOptionsGetter,this._positionSupportReverseGetter=s.positionSupportReverseGetter,this._positionSupportBracketsGetter=s.positionSupportBracketsGetter,this._symbolData={minTick:1/this._mainSeries.base(),pipSize:1,lotSize:1,priceMagnifier:1,quantityFormatter:new O.QuantityFormatter,priceFormatter:new C.PriceFormatter},this._currencyGetters={1:s.positionCurrencyGetter,0:s.orderCurrencyGetter},this._pipValueType$=i,D.logNormal(`[Symbol_${this._symbol}] Init with default values: minTick ${this._symbolData.minTick}, pipSize ${this._symbolData.pipSize}, pipValue: ${JSON.stringify(this.pipValue())}, pipValueType ${this._pipValueType}`),this._updateLastHandler=this._updateLast.bind(this),this._makeActualTradingSymbolObservable=r,this._mainSeries.dataEvents().symbolResolved().subscribe(this,(()=>this._updateActualTradingSymbol())),this._mainSeries.dataEvents().symbolError().subscribe(this,(()=>this._updateActualTradingSymbol())),this._hibernated=e.model().collapsed().spawn(),this._hibernated.subscribe((e=>{this.isActualSymbol()&&!e?this._subscribe():this._unsubscribe()})),this._updateActualTradingSymbol()}start(e){null!==this._symbol&&null===e&&(this._stop(),D.logNormal("Current symbol null")),this._symbol=e;const t=this.isActualSymbol();if(t&&!this._hibernated.value()?this._subscribe():this._unsubscribe(),!t||this._isStarted)return;const i=(0,o.ensureNotNull)(this._symbol);D.logNormal(`Start symbol ${i}`),this._isStarted=!0,this._updateSymbolData(),this._updateCurrencies(),this._updateSpecificTradingOptions()}destroy(){this._stop(),this._mainSeries.dataEvents().symbolResolved().unsubscribeAll(this),this._mainSeries.dataEvents().symbolError().unsubscribeAll(this),this._hibernated.destroy()}symbol(){return this._symbol}isActualSymbol(){if(null===this._symbol)return!1;if(null===this._mainSeries.symbolInfo())return!1
;return!(this._mainSeries.isConvertedToOtherCurrency()||this._mainSeries.isConvertedToOtherUnit())&&(this._mainSeries.symbolSameAsCurrent(this._symbol)||this._actualTradingSymbol===this._symbol)}onUpdate(){return this._onUpdate}positionCurrency(){return this._currencies[1]}orderCurrency(){return this._currencies[0]}supportOrderBrackets(){return this._specificTradingOptions?.supportOrderBrackets??!1}supportModifyPositionBrackets(){return!1!==this._specificTradingOptions?.supportModifyBrackets&&(this._specificTradingOptions?.supportModifyPositionBrackets??!1)}supportModifyOrderBrackets(){return Boolean(this._specificTradingOptions?.supportModifyBrackets&&this._specificTradingOptions?.supportModifyOrderBrackets)}supportAddBracketsToExistingOrder(){return Boolean(this._specificTradingOptions?.supportAddBracketsToExistingOrder)}supportPositionBrackets(){return null!==this._specificTradingOptions&&this._positionSupportBracketsGetter(this._specificTradingOptions)}supportPositionReverse(){return null!==this._specificTradingOptions&&this._positionSupportReverseGetter(this._specificTradingOptions)}supportModifyBrackets(){return this._specificTradingOptions?.supportModifyBrackets??!0}async qtyInfo(){return null===this._symbol?null:(await this._realtimeProvider.symbolInfo(this._symbol)).qty}lastData(){return this._last}symbolData(){return this._symbolData}pipValue(){return this._pipValuesSpawn?.value()??null}pipValueType(){return this._pipValueType}getMinTick(e){const t=(0,o.ensureNotNull)(this._symbolData);return(0,T.getMinTick)({minTick:t.minTick,price:e,variableMinTickData:t.variableMinTickData})}_subscribe(){this._isSubscribed||(this._isSubscribed=!0,this._realtimeProvider.subscribeRealtime((0,o.ensureNotNull)(this._symbol),this._updateLastHandler),this._realtimeProvider.onStatusChanged.subscribe(this,this._updateSymbolData),this._createPipsHelpersSpawns())}_unsubscribe(){this._isSubscribed&&(this._isSubscribed=!1,this._realtimeProvider.unsubscribeRealtime((0,o.ensureNotNull)(this._symbol),this._updateLastHandler),this._realtimeProvider.onStatusChanged.unsubscribeAll(this),this._removePipsHelpersSpawns())}_updateActualTradingSymbol(){this._actualTradingSymbol=null,this._actualTradingSymbolSubscription?.unsubscribe();const e=this._mainSeries.symbolInfo();null!==e?this._actualTradingSymbolSubscription=this._makeActualTradingSymbolObservable(e.pro_name).subscribe((({symbol:e})=>{e!==this._actualTradingSymbol&&(this._actualTradingSymbol=e,this.start(this._symbol))})):this.start(this._symbol)}_stop(){D.logNormal(`Stop symbol ${this._symbol}`),this._unsubscribe(),this._actualTradingSymbolSubscription?.unsubscribe(),this._symbolData=null,this._last=null,this._currencies={},this._symbol=null,this._isStarted=!1}async _updateSymbolData(){if(!this._isValidSymbol())return void D.logNormal(`Can't update symbol data because symbol '${this._symbol}' not valid (isActualSymbol ${this.isActualSymbol()})`);const e=(0,
o.ensureNotNull)(this._symbol),[t,i,r]=await Promise.all([this._realtimeProvider.quantityFormatter(e),this._realtimeProvider.formatter(e),this._realtimeProvider.symbolInfo(e)]);this._symbolData={quantityFormatter:t,priceFormatter:i,minTick:r.minTick,pipSize:r.pipSize,variableMinTickData:r.variableMinTick?(0,T.makeVariableMinTickData)(r.minTick,r.variableMinTick):void 0,lotSize:r.lotSize,priceMagnifier:r.priceMagnifier??1},D.logNormal(`[Symbol_${this._symbol}] Update symbol data values: minTick ${this._symbolData.minTick}, pipSize ${this._symbolData.pipSize}, pipValue: ${JSON.stringify(this.pipValue())}, pipValueType ${this._pipValueType}`),this._onUpdate.fire()}_updateLast(e,t){(this._last?.ask!==t.ask||this._last?.bid!==t.bid||this._last?.trade!==t.trade)&&this._isValidSymbol()&&(this._last={trade:t.trade??NaN,ask:t.ask??NaN,bid:t.bid??NaN},this._onUpdate.fire())}_updateCurrencies(){this._updateCurrency(1),this._updateCurrency(0)}async _updateCurrency(e){void 0===this._currencies[e]&&this._isValidSymbol()&&(this._currencies[e]="",this._currencies[e]=await this._currencyGetters[e]((0,o.ensureNotNull)(this._symbol)),this._onUpdate.fire())}async _updateSpecificTradingOptions(){null===this._specificTradingOptions&&this._isValidSymbol()&&(this._specificTradingOptions={},this._specificTradingOptions=await this._symbolSpecificTradingOptionsGetter((0,o.ensureNotNull)(this._symbol))??null,this._onUpdate.fire())}_createPipsHelpersSpawns(){if(!this._isValidSymbol())return;const e=null===this._pipValuesSpawn;if(e){const e=(0,o.ensureNotNull)(this._realtimeProvider.activeBroker());this._pipValuesSpawn=function(e,t,i,r){const s=e=>({sellPipValue:e,buyPipValue:e}),o=new f.WatchedValue(s(1));let a=!1;(async()=>{if(!a){const i=await t(e);o.setValue(s(i.pipValue))}})();const n=(e,t)=>{a=!0,o.setValue(t)};return i(e,n),o.readonly().spawn((()=>r(e,n)))}((0,o.ensureNotNull)(this._symbol),this._realtimeProvider.symbolInfo.bind(this._realtimeProvider),e.subscribePipValue.bind(e),e.unsubscribePipValue.bind(e)),this._pipValuesSpawn.subscribe((e=>{D.logNormal(`[Symbol_${this._symbol}] Update pipValues ${JSON.stringify(this.pipValue())}`),this._onUpdate.fire()}))}const t=void 0===this._pipValueTypeSubscription;t&&(this._pipValueTypeSubscription=this._pipValueType$.subscribe((e=>this._updatePipValueType(e)))),(e||t)&&this._onUpdate.fire()}_removePipsHelpersSpawns(){this._pipValuesSpawn?.destroy(),this._pipValuesSpawn=null,this._pipValueTypeSubscription?.unsubscribe(),this._pipValueTypeSubscription=void 0}_updatePipValueType(e){this._pipValueType=e,D.logNormal(`[Symbol_${this._symbol}] Update pipValueType ${this._pipValueType}`),this._onUpdate.fire()}_isValidSymbol(){return Boolean(this._symbol&&this.isActualSymbol())}}const B=()=>{},M=()=>{};function R(e,t=!1){const i=e.properties().childs().tradingProperties.childs(),r=(0,d.combineProperty)(((e,t)=>t),(0,S.createPrimitivePropertyFromWatchedValue)(e.isInReplay().weakReference()).ownership(),i.showOrders.weakReference()),s=(0,d.combineProperty)(((e,t)=>t),(0,
S.createPrimitivePropertyFromWatchedValue)(e.isInReplay().weakReference()).ownership(),i.showPositions.weakReference());return{lineProperties:{width:i.lineWidth,style:i.lineStyle,extend:i.extendLeft},horizontalAlignment:i.horizontalAlignment,showOrders:r,showPositions:s,showReverse:i.showReverse,positionPL:i.positionPL.childs(),bracketsPL:i.bracketsPL.childs(),plusButtonVisibility:g.addPlusButtonProperty}}function V(e){const{chartWidgetCollection:t,chartModel:i,configFlags:r,externalCustomSourceServices:s,tradedGroupRenderersControllerMap:a,specialCtor:n,properties:d}=e,l=function(e,t){return e.has(t)||e.set(t,new k),(0,o.ensureDefined)(e.get(t))}(a,i),{realtimeProvider:c,qtySuggester:p,pipValueType$:h,positionSupportBracketsGetter:u,positionSupportReverseGetter:m,positionCurrencyGetter:_,orderCurrencyGetter:y,symbolSpecificTradingOptionsGetter:b,makeActualTradingSymbolObservable:g}=s,S=new w(i.mainSeries(),c,h,g,{positionSupportReverseGetter:m,positionSupportBracketsGetter:u,positionCurrencyGetter:_,orderCurrencyGetter:y,symbolSpecificTradingOptionsGetter:b}),P=window.TradingView.bottomWidgetBar?.isVisible(),T=void 0===P?new f.WatchedValue(!1):P;return n(r,d,(()=>t.activeChartWidget.value().exitTrackingMode()),l,S,p,(0,v.combine)((e=>!e),T.weakReference()).ownership())}var N=i(926032),L=i(609838),A=i(404357),x=i(46415),E=i(168873),F=i(482303),W=i(333433),$=i(525915),U=i(86441);const j=document.createElement("div");var q,H=i(376161),z=i(431635),Q=i(265328),J=i(112232),K=i(631488),X=i(90470);!function(e){e[e.Up=1]="Up",e[e.Down=-1]="Down"}(q||(q={}));const Y={contextMenu:{cancel:L.t(null,void 0,i(206979)),edit:L.t(null,void 0,i(784570))},qty:{title:L.t(null,void 0,i(899255))},close:{title:L.t(null,void 0,i(206979))}};class Z extends H.TradedGroupBase{constructor(e,t,r,s,o,a,n,d,l,c,p,u,m,_,y){super(e,t,r,s,o,a,l,c,p,u),this._supportedOrderTypes=[],this._dataUpdated=new I.Delegate,this._confirmWidget=null,this._confirmWidgetShown=!1,this.onQtyModify=async(e,t)=>{if(void 0===this.mainItem())return;const i=this.mainItem().data();i.qty=e,this.items().brackets.forEach((t=>{t.data().qty=e})),await this._modifyPlaceOrder(this._makePreOrderData(i),void 0,t),this.redraw()},this._onToggleQtyCalcHandler=e=>(e.isOpened&&this._onCalculatorOpened(),async function(e){const{isOpened:t,position:r,qtyInfoGetter:s,valueGetter:o,onClose:a,onDestroy:n,onValueChange:d}=e,[l,c]=await Promise.all([Promise.all([i.e(1086),i.e(5480),i.e(9296),i.e(303),i.e(4920),i.e(2743),i.e(7125),i.e(3809)]).then(i.bind(i,713191)),s()]);if(null===c)return void l.render(!1,j);const p={...c,withInput:!0,valueGetter:o,position:function(e){return new U.Point(r.x-e.contentWidth,r.y)},targetEl:null,onClickOutside:function(e){if(e){const t=e.target.getBoundingClientRect()||{left:0,top:0};a(new U.Point(e.clientX-t.left,e.clientY-t.top))}},onClose:h,onKeyboardClose:h,onValueChange:d};function h(){n()}t?l.render(!0,j,p):l.render(!1,j)}(e)),this._moveSelectedItem=e=>{const t=this.selectedItem();if(null===t)return;const i=this.findItem(t.id);this._moveItem(i,e),
this.redraw()},this._moveGroupItems=e=>{const t=this.items(!0);for(const i of t)this._moveItem(i,e);this.redraw()},this._moveItem=(e,t)=>{const i=this._model.mainSeries().priceStep();null!==i&&null!==e.price()&&(e.applyPriceDiff(i*t),this.modifyAllItems(e.id()),e.setInEdit(!1))},this._onConfirm=async(e,t)=>!this.disabled()&&!this.hasError()&&(this._callbacks.trackEvent("Chart Place Order","Order placed"),this._sendOrder()),this._modifyPlaceOrder=l.modifyOrder,this._cancelPlaceOrder=l.cancelOrder,this._sendOrder=l.sendOrder,this._openOrderTicket=l.openOrderTicket,this._onCalculatorOpened=l.onCalculatorOpened,this._supportedOrderTypes=m,this._setData(this._rawDataSpawn.value(),!1),this._rawDataSpawn.subscribe((()=>this._dataUpdated.fire())),this._isActiveSource=_,this._isActiveSource.subscribe((()=>this._updateConfirmButtonDisplay())),this._isConfirmButtonOnDomWidgetWV=y,this._isConfirmButtonOnDomWidgetWV.subscribe((()=>this._updateConfirmButtonDisplay()),{callWithLast:!0}),this._disabled=(0,v.combine)((e=>e===h.PlaceOrEditContextStatus.Loading),d.ownership()),this._disabled.subscribe((()=>this.redraw())),this._errors=(0,v.combine)((e=>e),n.ownership()),this._errors.subscribe((()=>this.redraw())),this._hotKeysActionGroup=function(e){const{isDisabled:t,moveGroupItems:i,moveSelectedItem:r,sendOrder:s}=e,o=N.createGroup({desc:"TradedGroupPlace",modal:!1});return o.add({desc:"Shift+KeyUp",hotkey:N.Modifiers.Shift+38,handler:()=>i(1),isDisabled:t,isRepeatAccepted:!0}),o.add({desc:"Shift+KeyDown",hotkey:N.Modifiers.Shift+40,handler:()=>i(-1),isDisabled:t,isRepeatAccepted:!0}),o.add({desc:"KeyUp",hotkey:38,handler:()=>r(1),isDisabled:t,isRepeatAccepted:!0}),o.add({desc:"KeyDown",hotkey:40,handler:()=>r(-1),isDisabled:t,isRepeatAccepted:!0}),o.add({desc:"Enter",hotkey:13,handler:()=>s(),isDisabled:t,isRepeatAccepted:!1}),o}({isDisabled:()=>!this._isActiveSource.value(),moveGroupItems:this._moveGroupItems,moveSelectedItem:this._moveSelectedItem,sendOrder:async()=>{await this._onConfirm(this.items().main.id(),{})}})}destroy(){this._hideConfirmWidgetIfNeeded(),this._isActiveSource.release(),this._isConfirmButtonOnDomWidgetWV.release(),this._disabled.destroy(),this._errors.destroy(),this._hotKeysActionGroup.destroy(),super.destroy()}isPlaced(){return!1}disabled(){return this._disabled.value()}hasError(){return Object.keys(this._errors.value()).length>0}isConfirmButtonOnDomWidget(){return!1}mainItem(){return this.items().main}items(e=!1,t){return super.items(e,t)}async setMainOrderType(e){const t=this.mainItem().data();t.type=e;const i=(0,$.orderTypeToText)({orderType:e,uppercase:!1,shorten:!1});this._callbacks.trackEvent("Chart Place Order","Change order type",i),await this._modifyPlaceOrder(this._makePreOrderData(t)),this.redraw()}onClose(e){const{origin:t="Chart Place Order",event:i="Cancel order",label:r=""}=e;this._callbacks.trackEvent(t,i,r),this._cancelPlaceOrder()}onModify(e){const{origin:t="Chart Place Order",event:i="Modify order",label:r=""}=e;this._callbacks.trackEvent(t,i,r),this._onOpenOrderTicket()}onMove(e,t){
super.onMove(e,t,!0),this.modifyAllItems(e)}async onFinishMove(e,t){this.setIsBlocked(!1);const{origin:i="Chart Place Order",event:r="Move Order",label:s=""}=t;return t=(0,E.mergeGaParams)({origin:i,event:r,label:s},{label:this._hadBeenModifiedAllItems?"group":"single"}),await this.modifyAllItems(e,t),this.items(!0).forEach((e=>{e.setInEdit(!1)})),this._hadBeenModifiedAllItems=!1,this.syncData(),this.redraw(),!0}async onFinishMoveProjectionBracket(e,t){return this.onFinishMove(e,t)}async onConfirm(e,t){return this._onConfirm(e,t)}async modifyAllItems(e,t){if(void 0!==t){const{origin:e="Chart Place Order",event:i="Modify order",label:r=""}=t;this._callbacks.trackEvent(e,i,r)}const i=this._makePreOrderData(this.mainItem().data());return await this._modifyPlaceOrder(i),!0}isActiveSource(){return this._isActiveSource.value()}isInErrorState(){return Object.keys(this._errors.value()).length>0}extractItemErrorMessage(e){const t=this._errors.value();if(0===Object.keys(t).length)return null;const i=te[e];if(void 0===i)return null;let r="";for(const e of i){const i=t[e];if(void 0===i)continue;const s=(0,A.getErrorMessage)(i);r=`${r}${0===r.length?"":", "}${s}`}return r.length>0?r:null}_createMainItem(e){const t=(0,o.ensureDefined)(e.main);if("PreOrder"!==t.dataType)throw new Error(`Unexpected data type for main item, it should be pre-order, not ${t.dataType}`);const i=(0,Q.createItem)(Q.TradedGroupItemType.PreOrder,t,this,this._model,{itemExternalServices:{symbolDataProvider:this._symbolDataProvider,qtySuggester:this._qtySuggester,tradedGroupRenderersController:this._tradedGroupRenderersController},visibilityGetters:{order:this._orderVisibilityGetter.bind(this),position:this._positionVisibilityGetter.bind(this)},sourceCallbacks:this._callbacks,menuCallbacks:{onToggleTypeMenuHandler:this._getToggleOrderTypeMenuHandler(),closeDropdownMenuHandler:W.closeDropdownMenu},qtyModifyCallbacks:{onToggleQtyCalcHandler:this._onToggleQtyCalcHandler,onQtyApplyHandler:e=>this.onQtyModify(e),onQtyProviderUpdate:e=>this.onQtyModify(e,!0),qtyInfoGetter:()=>this._symbolDataProvider.qtyInfo()}},Y);return i.setSupportOrderType(this._supportedOrderTypes),i}_updateMainItem(e){const t=this.items().main;if(!(0,F.isPreOrderItemRawData)(e)||!(0,z.isPreOrderItem)(t))return;const{dataType:i,...r}=e;t.setData(r)}_createStopLimitItem(e,t){let i;const r=(0,o.ensureDefined)(e.main);if("PreOrder"!==r.dataType)throw new Error(`Unexpected data type for main item, it should be pre-order, not ${r.dataType}`);if(4===t.type()){const e=this._dataForStopLimitOrder(r);i=(0,Q.createItem)(Q.TradedGroupItemType.LimitPartStopLimitOrder,e,this,this._model,{itemExternalServices:{symbolDataProvider:this._symbolDataProvider,qtySuggester:this._qtySuggester,tradedGroupRenderersController:this._tradedGroupRenderersController},visibilityGetters:{order:this._orderVisibilityGetter.bind(this),position:this._positionVisibilityGetter.bind(this)},sourceCallbacks:this._callbacks,qtyModifyCallbacks:{onToggleQtyCalcHandler:this._onToggleQtyCalcHandler,
onQtyApplyHandler:e=>this.onQtyModify(e),onQtyProviderUpdate:e=>this.onQtyModify(e,!0),qtyInfoGetter:()=>this._symbolDataProvider.qtyInfo()}},Y)}return i}_dataForStopLimitOrder(e){const{seenPrice:t,...i}=(0,s.default)(e);return{...i,id:"projectionStopPartStopLimit",dataType:"Order",status:6,considerFilledQty:!1,price:(0,o.ensureDefined)(e.limitPrice),callbacks:{modifyOrder:()=>this._onOpenOrderTicket(),modifyOrderFromContextMenu:()=>this._onOpenOrderTicket()}}}_convertStopLimitOrderToMainItem(e,t){const i=this.items().main;if(void 0===i||!(0,F.isOrderLikeItem)(i)||void 0===e||(0,F.isPositionItemRawData)(e))return;const r=this._calculateSeenPrice(e),o=this._getCurrentQuotes();i.setData({...(0,s.default)(e),seenPrice:r,currentQuotes:o})}_processedBracketData(e){if(e.callbacks.cancelOrder=async e=>{const t=this.items().brackets,i=t.findIndex((t=>t.data().id===e));return-1!==i&&t.splice(i,1),await this.modifyAllItems(e),!0},e.stopType===x.StopType.TrailingStop){const t=this._symbolDataProvider.symbolData()?.pipSize,i=void 0!==this.items().stopLimit?this.items().main.data().limitPrice:this.items().main?.price();e.price=(0,K.calcPriceByPips)(e.trailingStopPips??0,i??0,e.side,t??0)}return e.callbacks.modifyOrderFromContextMenu=(e,t,i)=>this._onOpenOrderTicket(i),e}_createBracketItem(e){return(0,Q.createItem)(Q.TradedGroupItemType.Order,this._processedBracketData(e),this,this._model,{itemExternalServices:{symbolDataProvider:this._symbolDataProvider,qtySuggester:this._qtySuggester,tradedGroupRenderersController:this._tradedGroupRenderersController},visibilityGetters:{order:this._orderVisibilityGetter.bind(this),position:this._positionVisibilityGetter.bind(this)},sourceCallbacks:this._callbacks,gaOrigin:"Chart Place Order"},{...Y,qty:{title:L.t(null,void 0,i(784570))}})}_isSourceShouldBeShown(){return!!this._symbolDataProvider.isActualSymbol()&&!(window.TradingView.printing&&!(0,X.isTradingObjVisibleOnScreenshot)())}async _onOpenOrderTicket(e){const t=this._makePreOrderData(this.mainItem().data());return this._openOrderTicket(t,e),!0}async _updateConfirmButtonDisplay(){const e=this._isActiveSource.value(),t=this.isConfirmButtonOnDomWidget();if(e&&t&&!this._confirmWidgetShown){this._confirmWidgetShown=!0;const{ConfirmWidgetRenderer:e}=await Promise.all([i.e(5008),i.e(3989)]).then(i.bind(i,178520));if(this._confirmWidgetShown&&!this._isDestroyed){const t=(0,o.ensureDefined)(this.baseItem()),i=(0,d.createWVFromGetterAndSubscription)((()=>t.confirmText(!1)),this._dataUpdated),r=(0,d.createWVFromGetterAndSubscription)((()=>t.data().side),this._dataUpdated),s=(0,v.combine)((()=>this.hasError()),this._errors.weakReference());this._confirmWidget=new e(i.ownership(),r.ownership(),this._disabled.spawnOwnership(),s.ownership(),(()=>this.onConfirm(this.mainItem().id(),{})))}}else e&&t||this._hideConfirmWidgetIfNeeded();this.redraw()}_hideConfirmWidgetIfNeeded(){this._confirmWidget?.destroy(),this._confirmWidget=null,this._confirmWidgetShown=!1}_makePreOrderData(e){
const t=this._calculateSeenPrice(e),i=this._getCurrentQuotes(),{symbol:r,type:s,side:a,qty:n,isClose:d,stopType:l}=e,c={symbol:r,type:s,side:a,qty:n,isClose:d,seenPrice:t,currentQuotes:i,stopType:l},p=(0,o.ensureNotNull)(e.price);switch(e.type){case 4:c.stopPrice=p??void 0;const e=this._symbolDataProvider.getMinTick(p),t=1===c.side?1:-1;c.limitPrice=this.items().stopLimit?.price()??p+t*e;break;case 1:c.limitPrice=p??void 0;break;case 3:c.stopPrice=p??void 0}const{takeProfit:h,stopLoss:u,trailingStop:m,guaranteedStop:_}=(0,J.bracketsByType)(this.items().projectionBrackets),{takeProfit:y,stopLoss:b,trailingStop:v,guaranteedStop:f}=(0,J.bracketsByType)(this.items().brackets);c.takeProfit=y?.price()??h?.price()??void 0,c.stopLoss=b?.price()??u?.price()??void 0;const g=v||m,S=g?.price()??void 0,P=f||_,k=P?.price()??void 0;if(void 0!==u?c.stopType=x.StopType.StopLoss:void 0!==_?c.stopType=x.StopType.GuaranteedStop:void 0!==m&&(c.stopType=x.StopType.TrailingStop),void 0!==g&&void 0!==S){const t=(0,o.ensureNotNull)(this._symbolDataProvider.lastData()),i=t?.bid??void 0,r=t?.ask??void 0,{pipSize:s}=this._symbolDataProvider.symbolData()??{};if(void 0!==i&&void 0!==r&&void 0!==s){const t=g.data(),i=4===e.type?this.items().stopLimit?.price()??p:p;c.trailingStopPips=(0,K.calcPipsByPrice)(S,i,t.side,s),c.trailingStopPrice=S}}return void 0!==P&&void 0!==k&&(c.guaranteedStop=k),c}_calculateSeenPrice(e){const t=this._symbolDataProvider.lastData();return(1===e.side?t?.ask:t?.bid)??0}_getCurrentQuotes(){const e=this._symbolDataProvider.lastData();return null===e?void 0:{ask:e.ask,bid:e.bid}}_getToggleOrderTypeMenuHandler(){return(e,t,i,r,s)=>{const o=this.mainItem();if(o.canSwitchType()){this._callbacks.trackEvent("Chart Place Order","Order type menu opening",i.label??"");const a=o.orderTypesItems().map((({type:e,typeText:t})=>({title:t,value:e}))),n=this._model.timeScale(),d=[n.onScroll(),n.barSpacingChanged(),this._model.mainSeries().onSymbolIntervalChanged()],l=a.findIndex((e=>e.value===o.data().type));(0,W.updateDropdownMenu)(e,t,a,l,"Order type",d,(e=>this.setMainOrderType(e)),r,s)}}}}var ee;!function(e){e[e.StopLoss=0]="StopLoss",e[e.TakeProfit=1]="TakeProfit",e[e.TrailingStop=2]="TrailingStop",e[e.GuaranteedStop=3]="GuaranteedStop",e.MainOrder="preOrder",e.ProjectionBracketStopLoss="ProjectionBracketStopLoss",e.ProjectionBracketTakeProfit="ProjectionBracketTakeProfit",e.ProjectionBracketTrailingStop="ProjectionBracketTrailingStop",e.ProjectionBracketGuaranteedStop="ProjectionBracketGuaranteedStop"}(ee||(ee={}));const te={preOrder:["qty","limitPrice","stopPrice"],ProjectionBracketStopLoss:["stopLoss"],ProjectionBracketTakeProfit:["takeProfit"],ProjectionBracketTrailingStop:["trailingStopPips"],ProjectionBracketGuaranteedStop:["guaranteedStop"],0:["stopLoss"],1:["takeProfit"],2:["trailingStopPips"],3:["guaranteedStop"]};var ie=i(74258),re=i(260449),se=i(71991);const oe=ie.tradedGroupPlaceOrderPrefix,ae=[1,3,4];var ne=i(669874);const de={contextMenu:{cancel:L.t(null,void 0,i(719634)),edit:(0,
ne.appendEllipsis)(L.t(null,void 0,i(224522)))},qty:{title:(0,ne.appendEllipsis)(L.t(null,void 0,i(224522)))},close:{title:L.t(null,void 0,i(719634))}};class le extends H.TradedGroupBase{constructor(e,t,i,r,s,o,a,n,d,l,c){super(e,t,i,r,s,o,a,n,d,l),this._resetProjBracketsTimeout=null,this._setData(this._rawDataSpawn.value(),!1),this._callbacks.onDataUpdateRejected.subscribe(this,this._resetProjBracketsHandler),this._styleOverrides=c}destroy(){this._clearResetProjBracketsTimeout(),this._callbacks.onDataUpdateRejected.unsubscribeAll(this),super.destroy()}isPlaced(){return!0}async onConfirm(e,t){return!0}isActiveSource(){return!1}items(e=!1,t){return super.items(e,t)}mainItem(){return this.items().main}async onFinishMove(e,t){if(this._hadBeenModifiedAllItems)return this.modifyAllItems(e,t);this.setIsBlocked(!0);const i=this.findItem(e);(0,o.assert)((0,F.isOrderLikeItem)(i),"This item does not support move"),t=(0,E.mergeGaParams)(t,{event:"Move Order",label:"single"});const r=await i.onFinishMove(t);return!this._isDestroyed&&(this.setIsBlocked(!1),this.syncData(),this.redraw(),this.allItemsSupportMove(this.items(!0))&&this._callbacks.showChartHint(),r)}cancelMove(){for(const e of this.items(!0))(0,F.isOrderLikeItem)(e)&&e.setInEdit(!1);return this._resetProjBracketsHandler(),this.syncData(),this.redraw(),!0}async onFinishMoveProjectionBracket(e,t){return this.modifyAllItems(e,t)}async modifyAllItems(e,t){this.setIsBlocked(!0);const{takeProfit:i,stopLoss:r,guaranteedStop:s,trailingStop:a}=(0,J.bracketsByType)(this.items().projectionBrackets),{takeProfit:n,stopLoss:d,guaranteedStop:l,trailingStop:c}=(0,J.bracketsByType)(this.items().brackets),h={limitPrice:this.items().stopLimit?.price()??void 0,takeProfit:n?.price()??i?.price()??void 0,stopLoss:d?.price()??r?.price()??void 0,guaranteedStop:l?.price()??s?.price()??void 0,trailingStop:c?.price()??a?.price()??void 0},u=(0,o.ensureDefined)(this.items().main),m=this.findItem(e);(0,o.assert)((0,F.isOrderLikeItem)(m),"This item does not support move");const _=this._hadBeenModifiedAllItems?"Move Order":"Add bracket from btn",y=function(e,t,i){return(0,F.isPositionLikeItem)(i)||"Move Order"===e?"group":(0,z.isPositionItem)(t)&&t.supportOnlyPairBrackets()?"both bracket":i.bracketType()===p.BracketType.TakeProfit?"take profit":i.bracketType()===p.BracketType.TrailingStop?"trailing stop":i.bracketType()===p.BracketType.GuaranteedStop?"guaranteed stop":"stop loss"}(_,u,m);t=(0,E.mergeGaParams)(t,{event:_,label:y});const b=this._ticketFocus(m);let v=!1;return(0,F.isPositionLikeItem)(u)?v=await u.onModifyWithBracket(t,h,!1,b):(v=await u.onFinishMove(t,h,!1,b),this.items(!0).forEach((e=>{e.setInEdit(!1)}))),!this._isDestroyed&&(a?.setPrice(null),i?.setPrice(null),r?.setPrice(null),s?.setPrice(null),this.setIsBlocked(!1),this.syncData(),this.redraw(),this._hadBeenModifiedAllItems=!1,v)}styleOverrides(){return this._styleOverrides}_createMainItem(e){const t=e.main;let i;return void 0!==t&&((0,F.isOrderItemRawData)(t)?i=(0,Q.createItem)(Q.TradedGroupItemType.Order,t,this,this._model,{
itemExternalServices:{symbolDataProvider:this._symbolDataProvider,qtySuggester:this._qtySuggester,tradedGroupRenderersController:this._tradedGroupRenderersController},visibilityGetters:{order:this._orderVisibilityGetter.bind(this),position:this._positionVisibilityGetter.bind(this)},sourceCallbacks:this._callbacks},de):(0,F.isPositionItemRawData)(t)&&(i=(0,Q.createItem)(Q.TradedGroupItemType.Position,t,this,this._model,{itemExternalServices:{symbolDataProvider:this._symbolDataProvider,qtySuggester:this._qtySuggester,tradedGroupRenderersController:this._tradedGroupRenderersController},visibilityGetters:{order:this._orderVisibilityGetter.bind(this),position:this._positionVisibilityGetter.bind(this)},sourceCallbacks:this._callbacks},de))),i}_updateMainItem(e){const t=this.items().main;if(void 0!==t&&void 0!==e)if((0,z.isPositionItem)(t)&&(0,F.isPositionItemRawData)(e)){const{dataType:i,...r}=e;t.setData(r)}else{if(!(0,F.isOrderLikeItem)(t)||!(0,F.isOrderItemRawData)(e))throw new Error(`Main item and main data are incompatible, main item type ${typeof t}, main data type ${e.dataType}`);{const{dataType:i,...r}=e;t.setData(r)}}}_dataForStopLimitOrder(e){return(0,o.assert)(!(0,F.isPositionItemRawData)(e)&&void 0!==e.limitPrice),{...(0,s.default)(e),price:e.limitPrice,considerFilledQty:!1}}_convertStopLimitOrderToMainItem(e,t){const i=this.items().main;if(void 0===i||!(0,F.isOrderLikeItem)(i)||void 0===e||(0,F.isPositionItemRawData)(e))return;const r={...(0,s.default)(t.data()),type:e.type,price:i.price(),callbacks:e.callbacks};i.setData(r)}_createBracketItem(e){return(0,Q.createItem)(Q.TradedGroupItemType.Order,this._processedBracketData(e),this,this._model,{itemExternalServices:{symbolDataProvider:this._symbolDataProvider,qtySuggester:this._qtySuggester,tradedGroupRenderersController:this._tradedGroupRenderersController},visibilityGetters:{order:this._orderVisibilityGetter.bind(this),position:this._positionVisibilityGetter.bind(this)},sourceCallbacks:this._callbacks,gaOrigin:"Chart Order"},de)}_ticketFocus(e){if(!(0,F.isPositionLikeItem)(e)){if(null!==e.bracketType())return e.bracketType()===p.BracketType.TakeProfit?3:4;switch(e.type()){case 4:return(0,z.isLimitPartStopLimitOrderItem)(e)?1:2;case 1:return 1;case 3:return 2}}return 5}_clearResetProjBracketsTimeout(){null!==this._resetProjBracketsTimeout&&(clearTimeout(this._resetProjBracketsTimeout),this._resetProjBracketsTimeout=null)}_resetProjBracketsHandler(){this._clearResetProjBracketsTimeout();const{takeProfit:e,stopLoss:t,guaranteedStop:i,trailingStop:r}=(0,J.bracketsByType)(this.items().projectionBrackets);e?.setPrice(null),t?.setPrice(null),i?.setPrice(null),r?.setPrice(null),this.redraw()}}const ce=`${ie.tradedGroupBaseNamePrefix}EditOrder`;var pe=i(735248);const he=(0,l.getLogger)("Trading.Source.Manager");function ue(e){let t="",i="",r="";return e.main&&("Position"===e.main.dataType?(t="Position",i=JSON.stringify((0,m.cropPositionData)(e.main))):"Order"===e.main.dataType?(t="Order",i=JSON.stringify((0,u.cropOrderData)(e.main))):(t="PreOrder",
i=JSON.stringify(e.main))),e.brackets.forEach(((e,t)=>{const i=(0,J.getBracketType)(e),s=null!==i?p.BracketType[i]:"";r=0===t?`\nbracketData: ${s} - ${JSON.stringify((0,u.cropOrderData)(e))}`:`${r};\n ${s} - ${JSON.stringify((0,u.cropOrderData)(e))}`})),`mainType: ${t};\nmainData: ${i};${r}`}const me="preOrder";function _e(e){return 2!==e?.data().type}class ye{constructor(e,t,i,r,s){this._tradedGroupData=new Map,this._tradedGroupPlaceData=null,this._tradedGroupPlaceStatus=null,this._ordersData=new Map,this._positionsData=new Map,this._positionsRealToParentIds=new Map,this._tradedGroupRenderersControllerMap=new WeakMap,this._getStyleOverrides=s,this._ordersService=e,this._positionsService=t,this._chartWidgetCollection=i,this._realtimeProvider=r.realtimeProvider,this._qtySuggester=r.qtySuggester,this._brokerCommandsUIGetter=r.brokerCommandsUI,this._closeTradingPanel=r.tradingPanelCloser,this._onCalculatorOpened=r.onCalculatorOpened,this._trackEvent=r.trackEvent,this._showTradedGroup=r.showTradedSources,this._pipValueType$=r.pipValueType$,this._makeActualTradingSymbolObservable=r.makeActualSymbolObservable,this._symbolSpecificTradingOptionsGetter=r.getSymbolSpecificTradingOptions,this._tradedContextLinking=r.activeTradedLinking,this._tradedContextLinking.onContextChanged().subscribe(this,this._updateTradedGroupFromLinking),this._addOrderItems(this._ordersService.activeOrders()),this._ordersService.activeOrdersUpdated().subscribe(this,(e=>this._addOrderItems([e]))),this._ordersService.activeOrdersRemoved().subscribe(this,(e=>this._removeTradedGroupItems(e))),this._addPositionItems(this._positionsService.positions().map((e=>({data:e,type:m.PositionsUpdateType.Full})))),this._positionsService.positionUpdate().subscribe(this,(e=>this._addPositionItems([e]))),this._positionsService.positionsRemoved().subscribe(this,(e=>this._removeTradedGroupItems(e)))}destroy(){this._tradedContextLinking.onContextChanged().unsubscribeAll(this),this._ordersService.activeOrdersUpdated().unsubscribeAll(this),this._ordersService.activeOrdersRemoved().unsubscribeAll(this),this._ordersData.clear(),this._positionsService.positionUpdate().unsubscribeAll(this),this._positionsService.positionsRemoved().unsubscribeAll(this),this._positionsData.clear(),this._tradedGroupData.clear()}_configFlags(){return(0,o.ensureNotNull)(this._realtimeProvider.activeBroker()).metainfo().configFlags}_getBrackets(e){const t=[];if(!this._ordersData.has(e)){const t=this._positionsRealToParentIds.get(e);void 0!==t&&(e=t)}return this._ordersData.forEach((i=>{i.parentId===e&&t.push({dataType:"Order",...(0,s.default)(i),callbacks:this._createCallbacksForOrder(i)})})),t}_updateTradedGroupFromLinking(e){if((0,h.isContextPlaceOrderContext)(e)&&_e(e)){const h=this._createPlaceSourceData(e);null!==this._tradedGroupPlaceData?this._tradedGroupPlaceData.setValue(h):(this._tradedGroupPlaceData=new n.WatchedObject(h),t=this._configFlags(),i=this._tradedGroupPlaceData,r=this._createProjectionOrderErrorWV().ownership(),s=this._createProjectionOrderStatusW().ownership(),
o=this._ordersService.orderRejected(),a=this._showTradedGroup,d=this._chartWidgetCollection,l={realtimeProvider:this._realtimeProvider,qtySuggester:this._qtySuggester,pipValueType$:this._pipValueType$,positionSupportReverseGetter:this._positionsService.supportReverse.bind(this._positionsService),positionSupportBracketsGetter:this._positionsService.supportBrackets.bind(this._positionsService),positionCurrencyGetter:this._positionsService.getCurrency.bind(this._positionsService),orderCurrencyGetter:this._ordersService.getCurrency.bind(this._ordersService),symbolSpecificTradingOptionsGetter:this._symbolSpecificTradingOptionsGetter,makeActualTradingSymbolObservable:this._makeActualTradingSymbolObservable},c=this._tradedGroupRenderersControllerMap,p=this._createCallbacksForPlaceOrder(),u=this._trackEvent,d.addCustomSource(`${oe}`,((e,n)=>{const h=R(n,!0);return V({chartWidgetCollection:d,chartModel:n,configFlags:t,externalCustomSourceServices:l,tradedGroupRenderersControllerMap:c,specialCtor:(t,c,h,m,_,f,g)=>{const S=function(e){if(null===e||1!==e.connectionStatus())return[2];const t=[],{supportMarketOrders:i,supportLimitOrders:r,supportStopOrders:s,supportStopLimitOrders:o}=e.metainfo().configFlags;return i&&t.push(2),r&&t.push(1),s&&t.push(3),o&&t.push(4),t}(l.realtimeProvider.activeBroker()).filter((e=>ae.includes(e))),P=(0,v.combine)((e=>e.model().model()===n),d.activeChartWidget.weakReference()),k=new Z(e,n,c,t,a,i,r.ownership(),s.ownership(),{exitTrackingMode:h,onDataUpdateRejected:o,trackEvent:u,showChartHint:B,hideChartHint:M,...p},_,f,m,S,P.ownership(),g.ownership());return P.value()&&d.activeChartWidget.value().model().model().selectionMacro((e=>{const t=new y.HitTestResult(y.HitTarget.Custom,{cursorType:b.PaneCursorType.Default,hideCrosshairLinesOnHover:!0,...(0,re.activeItemToHitTestResultData)({id:se.preOrderItemId,part:9})});e.addSourceToSelection(k,t.data())})),k},properties:h})}),_.CustomSourceLayer.Topmost))}else null!==this._tradedGroupPlaceData&&(!function(e){e.removeCustomSource(`${oe}`)}(this._chartWidgetCollection),this._tradedGroupPlaceData=null);var t,i,r,s,o,a,d,l,c,p,u}_createProjectionOrderErrorWV(){return(0,d.createWVFromGetterAndSubscription)((()=>this._tradedContextLinking.context()?.errors()??{}),this._tradedContextLinking.onContextChanged())}_createProjectionOrderStatusW(){return(0,d.createWVFromGetterAndSubscription)((()=>this._tradedContextLinking.context()?.status()??h.PlaceOrEditContextStatus.Undefined),this._tradedContextLinking.onContextChanged())}_createTradedGroupData(e){let t;const i=this._ordersData.get(e),r=this._positionsData.get(e);return void 0!==i?t={dataType:"Order",...(0,s.default)(i),callbacks:this._createCallbacksForOrder(i)}:void 0!==r&&(t={dataType:"Position",...(0,s.default)(r),callbacks:this._createCallbacksForPosition()}),{main:t,brackets:this._getBrackets(e)}}_createPlaceSourceData(e){const t=(0,s.default)(e.data()),i=(0,u.getOrderPriceByType)(t);(0,a.isNumber)(i)||he.logError("pre-order price is not defined");const r={dataType:"PreOrder",...t,price:i,callbacks:{},
supportModify:!0,supportModifyOrderPrice:!0,supportMove:!0,supportCancel:!0,supportStopLoss:(0,pe.checkStopLossAvailability)(this._configFlags()),supportTrailingStop:(0,pe.checkTrailingStopAvailability)(this._configFlags()),supportGuaranteedStop:(0,pe.checkGuaranteedStopAvailability)(this._configFlags()),plBasedOnLast:!0};return{main:r,brackets:this._createBracketsFromPlaceContextData(r)}}_createBracketsFromPlaceContextData(e){const t=[];return void 0!==e.takeProfit&&t.push(this._createBracketDataForPlaceOrder(e,p.BracketType.TakeProfit,e.takeProfit)),void 0!==e.stopLoss&&t.push(this._createBracketDataForPlaceOrder(e,p.BracketType.StopLoss,e.stopLoss)),void 0!==e.trailingStopPips&&t.push(this._createBracketDataForPlaceOrder(e,p.BracketType.TrailingStop,null)),void 0!==e.guaranteedStop&&t.push(this._createBracketDataForPlaceOrder(e,p.BracketType.GuaranteedStop,e.guaranteedStop)),t}_createBracketDataForPlaceOrder(e,t,i){return{...(0,J.buildProjectionBracketData)(me,t,e),dataType:"Order",price:i,parentId:me,parentType:1,callbacks:{},supportModify:!0,supportMove:!0,supportCancel:!0,plBasedOnLast:!1}}_recreateTradedGroupSource(e,t=!0,i=""){const r=this._createTradedGroupData(e),s=this._tradedGroupData.get(e);if(void 0!==s)s.setValue(r),t&&he.logNormal(`Source updated${i}: ${ue(r)}`);else{const t=new n.WatchedObject(r);o=e,a=this._configFlags(),d=t,l=this._ordersService.orderRejected(),c=this._showTradedGroup,p=this._chartWidgetCollection,h={styleOverrides:this._getStyleOverrides(),realtimeProvider:this._realtimeProvider,qtySuggester:this._qtySuggester,pipValueType$:this._pipValueType$,positionSupportReverseGetter:this._positionsService.supportReverse.bind(this._positionsService),positionSupportBracketsGetter:this._positionsService.supportBrackets.bind(this._positionsService),positionCurrencyGetter:this._positionsService.getCurrency.bind(this._positionsService),orderCurrencyGetter:this._ordersService.getCurrency.bind(this._ordersService),symbolSpecificTradingOptionsGetter:this._symbolSpecificTradingOptionsGetter,makeActualTradingSymbolObservable:this._makeActualTradingSymbolObservable},u=this._tradedGroupRenderersControllerMap,m=this._trackEvent,p.addCustomSource(`${ce}${o}`,((e,t)=>{const i=R(t,!1);return V({chartWidgetCollection:p,chartModel:t,configFlags:a,externalCustomSourceServices:h,tradedGroupRenderersControllerMap:u,specialCtor:(i,r,s,o,a,n)=>new le(e,t,r,i,c,d,{exitTrackingMode:s,onDataUpdateRejected:l,trackEvent:m,showChartHint:B,hideChartHint:M},a,n,o,h.styleOverrides??null),properties:i})}),_.CustomSourceLayer.Topmost),this._tradedGroupData.set(e,t),he.logNormal(`Source created: ${ue(r)}`)}var o,a,d,l,c,p,h,u,m}async _findPositionRealIdByParentId(e){const t=this._positionsData.get(e);let i;if(void 0!==t)i=t.id;else if(i=await this._positionsService.realIdFromBroker(e),null===i){const t=this._currentRealPositionIdByParentId(e);return null!==t&&this._positionsRealToParentIds.delete(t),null}return this._positionsRealToParentIds.set(i,e),i}_currentRealPositionIdByParentId(e){
for(const t of Array.from(this._positionsRealToParentIds.keys()))if(this._positionsRealToParentIds.get(t)===e)return t;return null}async _findAndUpdateParentPosition(e,t=!1){const i=this._currentRealPositionIdByParentId(e),r=await this._findPositionRealIdByParentId(e);if(null!==i&&null===r&&this._tradedGroupData.has(i)&&(he.logNormal(`Position update (brackets unlink from position), id: ${e}`),this._recreateTradedGroupSource(i)),null===i&&null!==r){const t=this._tradedGroupData.get(e);void 0!==t&&void 0===t.value().main&&(he.logNormal(`Position remove (brackets link to position), id: ${e}`),this._removeTradedGroupEditSource(e))}t&&i===r||this._recreateTradedGroupSource(r||e,void 0," after parent position been found (async operation)")}_addOrderItems(e){for(const t of e){this._removeSourceIfChangedParent(t),this._ordersData.set(t.id,t);const e=void 0!==t.parentId?t.parentId:t.id;2!==t.parentType&&3!==t.parentType?this._recreateTradedGroupSource(e):this._findAndUpdateParentPosition(e)}}_addPositionItems(e){for(const t of e){const e=t.data,i=e.id;this._positionsData.set(i,e),this._recreateTradedGroupSource(i,t.type===m.PositionsUpdateType.Full);this._ordersService.activeOrders().filter(u.isBracketOrderRawData).filter((t=>2===t.parentType&&t.symbol===e.symbol)).forEach((e=>{this._findAndUpdateParentPosition(e.parentId,!0)}))}}_removeSourceIfChangedParent(e){const t=this._ordersData.get(e.id);void 0===t||t.parentId===e.parentId&&t.parentType===e.parentType||this._removeTradedGroupItems([t.id])}_removeTradedGroupItems(e){for(const t of e){let e=t;if(!this._tradedGroupData.has(t))for(const i of this._tradedGroupData.keys()){for(const r of(0,o.ensureDefined)(this._tradedGroupData.get(i)).value().brackets)if(r.id===t){e=i;break}if(e===i)break}this._positionsData.delete(t),this._ordersData.delete(t);const i=this._createTradedGroupData(e);if(void 0===i.main&&0===i.brackets.length)this._removeTradedGroupEditSource(e);else{const t=this._tradedGroupData.get(e);void 0!==t&&(t.setValue(i),he.logNormal(`Source updated: ${ue(i)}`))}}}_removeTradedGroupEditSource(e){var t,i;this._tradedGroupData.delete(e),t=this._chartWidgetCollection,i=e,t.removeCustomSource(`${ce}${i}`),he.logNormal(`Traded-source removed, mainItemId: ${e}`)}_createCallbacksForOrder(e){const t={};return e.supportModify&&(t.modifyOrder=this._modifyOrder.bind(this),t.modifyOrderFromContextMenu=this._modifyOrder.bind(this)),e.supportMove&&(t.moveOrder=this._moveOrder.bind(this)),e.supportCancel&&(t.cancelOrder=this._cancelOrder.bind(this)),t}_createCallbacksForPlaceOrder(){return{modifyOrder:async(e,t,i)=>{const r=(0,s.default)(e),a=await(0,o.ensureNotNull)(this._realtimeProvider.activeBroker()).createPlaceOrderContext({order:r,source:"traded-source",silent:i});return this._tradedContextLinking.setContext(a,i),!0},openOrderTicket:(e,t)=>{this._brokerCommandsUIGetter()?.placeOrder(e,!1,void 0,t)},cancelOrder:()=>{this._tradedContextLinking.clear()},sendOrder:async()=>{const e=await(0,o.ensureNotNull)(this._tradedContextLinking.context()).send()
;return this._tradedContextLinking.clear(),e},onCalculatorOpened:()=>this._onCalculatorOpened()}}_createCallbacksForPosition(){return{reversePosition:this._reversePosition.bind(this),modifyPosition:this._modifyPosition.bind(this),closePosition:this._closePosition.bind(this)}}async _processingModifyOrder(e,t,i,r){let s=!1;try{s=await(0,o.ensureNotNull)(this._brokerCommandsUIGetter()).modifyOrder(e,i,r)}catch(e){he.logError(`Try to modify order with error ${(0,c.errorToString)(e)}`)}return s}_modifyOrder(e,t,i,r){const s=(0,o.ensureNotNull)(this._ordersService.find(e));return this._processingModifyOrder(t,s,r,i)}_moveOrder(e,t,i){const r=(0,o.ensureNotNull)(this._ordersService.find(e));return this._processingModifyOrder(t,r,!0,i)}_cancelOrder(e){return(0,o.ensureNotNull)(this._brokerCommandsUIGetter()).cancelOrder(e)}_reversePosition(e){return(0,o.ensureNotNull)(this._brokerCommandsUIGetter()).reversePosition(e)}_modifyPosition(e,t={},i,r){const s=(0,o.ensureNotNull)(this._brokerCommandsUIGetter());return this._positionsService.isDisplayModeIndividualPositions()?(0,o.ensureDefined)(s.editIndividualPositionBrackets).call(s,e,t,i,r):s.editPositionBrackets(e,t,i,r)}_closePosition(e){const t=(0,o.ensureNotNull)(this._brokerCommandsUIGetter());return this._positionsService.isDisplayModeIndividualPositions()?t.closeIndividualPosition(e):t.closePosition(e)}}}}]);