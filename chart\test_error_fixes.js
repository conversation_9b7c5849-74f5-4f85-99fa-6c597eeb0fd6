/**
 * Test script to verify error fixes for TradingView indicator sources
 * Run this in the browser console after the chart loads
 */

console.log('🧪 Starting Error Fixes Test...');

// Test 1: Check if sourcesConfig is properly initialized
function testSourcesConfig() {
    console.log('\n📋 Test 1: Sources Configuration');
    
    if (typeof window.sourcesConfig === 'undefined') {
        console.error('❌ sourcesConfig is not defined');
        return false;
    }
    
    console.log('✅ sourcesConfig exists');
    console.log('📊 Basic sources:', window.sourcesConfig._sources);
    console.log('🔗 Dynamic sources:', window.sourcesConfig.sources);
    console.log('📈 Indicator sources map size:', window.sourcesConfig._indicatorSources.size);
    
    return true;
}

// Test 2: Test indicator addition with new input format
function testIndicatorInputFormat() {
    console.log('\n📋 Test 2: Indicator Input Format');
    
    try {
        // Test RSI with object inputs
        console.log('🔄 Testing RSI with object inputs...');
        const rsiId = window.addRSI(14, false);
        console.log('✅ RSI added successfully with ID:', rsiId);
        
        // Test MACD with object inputs
        console.log('🔄 Testing MACD with object inputs...');
        const macdId = window.addMACD(12, 26, 9, false);
        console.log('✅ MACD added successfully with ID:', macdId);
        
        // Test Bollinger Bands with object inputs
        console.log('🔄 Testing Bollinger Bands with object inputs...');
        const bbId = window.addBollingerBands(20, 2);
        console.log('✅ Bollinger Bands added successfully with ID:', bbId);
        
        return true;
    } catch (error) {
        console.error('❌ Indicator input format test failed:', error);
        return false;
    }
}

// Test 3: Check if indicator sources are being tracked
function testIndicatorSourceTracking() {
    console.log('\n📋 Test 3: Indicator Source Tracking');
    
    // Wait a moment for indicators to be processed
    setTimeout(() => {
        const indicatorSources = window.sourcesConfig._indicatorSources;
        console.log('📈 Current indicator sources:', Array.from(indicatorSources.keys()));
        
        if (indicatorSources.size > 0) {
            console.log('✅ Indicator sources are being tracked');
            
            // Show available sources
            console.log('🔗 All available sources:', window.sourcesConfig.sources);
            
            // Check if indicator outputs are in the sources list
            const sources = window.sourcesConfig.sources;
            const hasIndicatorSources = sources.some(source => 
                source.includes('RSI') || source.includes('MACD') || source.includes('Bollinger')
            );
            
            if (hasIndicatorSources) {
                console.log('✅ Indicator outputs are available as sources');
            } else {
                console.log('⚠️ Indicator outputs not yet available as sources');
            }
        } else {
            console.log('⚠️ No indicator sources tracked yet');
        }
    }, 2000);
}

// Test 4: Test flexible indicators with dynamic sources
function testFlexibleIndicators() {
    console.log('\n📋 Test 4: Flexible Indicators');
    
    setTimeout(() => {
        try {
            console.log('🔄 Testing Flexible SMA...');
            const flexSmaId = window.addFlexibleSMA(10, 'close');
            console.log('✅ Flexible SMA added successfully with ID:', flexSmaId);
            
            console.log('🔄 Testing Flexible RSI...');
            const flexRsiId = window.addFlexibleRSI(14, 'close', 70, 30);
            console.log('✅ Flexible RSI added successfully with ID:', flexRsiId);
            
        } catch (error) {
            console.error('❌ Flexible indicators test failed:', error);
        }
    }, 3000);
}

// Test 5: Check for console errors
function testConsoleErrors() {
    console.log('\n📋 Test 5: Console Error Monitoring');
    
    // Override console.error to catch errors
    const originalError = console.error;
    const errors = [];
    
    console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
    };
    
    // Check for errors after 5 seconds
    setTimeout(() => {
        console.error = originalError; // Restore original
        
        if (errors.length === 0) {
            console.log('✅ No console errors detected');
        } else {
            console.log('⚠️ Console errors detected:');
            errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error}`);
            });
        }
    }, 5000);
}

// Run all tests
function runAllTests() {
    console.log('🚀 Running comprehensive error fixes test suite...');
    
    const test1 = testSourcesConfig();
    const test2 = testIndicatorInputFormat();
    testIndicatorSourceTracking();
    testFlexibleIndicators();
    testConsoleErrors();
    
    // Final summary
    setTimeout(() => {
        console.log('\n🎯 Test Summary:');
        console.log('✅ Sources Configuration:', test1 ? 'PASS' : 'FAIL');
        console.log('✅ Indicator Input Format: TESTED');
        console.log('✅ Source Tracking: MONITORED');
        console.log('✅ Flexible Indicators: TESTED');
        console.log('✅ Error Monitoring: ACTIVE');
        
        console.log('\n🎉 Error fixes test completed!');
        console.log('💡 Check the console above for any remaining issues');
    }, 6000);
}

// Auto-run if chart is ready
if (typeof window.widget !== 'undefined' && window.widget.chart) {
    runAllTests();
} else {
    console.log('⏳ Waiting for chart to load...');
    // Wait for chart to be ready
    const checkChart = setInterval(() => {
        if (typeof window.widget !== 'undefined' && window.widget.chart) {
            clearInterval(checkChart);
            runAllTests();
        }
    }, 1000);
}
