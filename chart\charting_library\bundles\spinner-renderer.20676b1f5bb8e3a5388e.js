(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7102],{485862:e=>{e.exports={disableSelfPositioning:"disableSelfPositioning-dYiqkKAE"}},672511:(e,n,i)=>{"use strict";i.d(n,{Spinner:()=>o});var r=i(50959),s=i(497754),a=i(843442),t=(i(715216),i(485862)),l=i.n(t);function o(e){const{ariaLabel:n,ariaLabelledby:i,className:t,style:o,size:d,id:p,disableSelfPositioning:b}=e;return r.createElement("div",{className:s(t,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${a.spinnerSizeMap[d||a.DEFAULT_SIZE]}`,b&&l().disableSelfPositioning),style:o,role:"progressbar",id:p,"aria-label":n,"aria-labelledby":i})}},974063:(e,n,i)=>{"use strict";i.r(n),i.d(n,{destroy:()=>l,render:()=>t});var r=i(50959),s=i(632227),a=i(132455);function t(e){s.render(r.createElement(a.Spinner),e)}function l(e){s.unmountComponentAtNode(e)}},132455:(e,n,i)=>{"use strict";i.d(n,{Spinner:()=>r.Spinner});var r=i(672511)}}]);