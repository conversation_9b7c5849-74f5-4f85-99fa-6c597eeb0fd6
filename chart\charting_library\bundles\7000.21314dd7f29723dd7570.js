(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7e3],{961853:e=>{e.exports={"textarea-container":"textarea-container-x5KHDULU","change-highlight":"change-highlight-x5KHDULU",focused:"focused-x5KHDULU","resize-vertical":"resize-vertical-x5KHDULU","resize-horizontal":"resize-horizontal-x5KHDULU","resize-both":"resize-both-x5KHDULU",textarea:"textarea-x5KHDULU","with-icon":"with-icon-x5KHDULU",endslot:"endslot-x5KHDULU"}},946105:e=>{e.exports={default:"default-EZuD3gZZ",danger:"danger-EZuD3gZZ",warning:"warning-EZuD3gZZ",success:"success-EZuD3gZZ",neutral:"neutral-EZuD3gZZ","neutral-light":"neutral-light-EZuD3gZZ",small:"small-EZuD3gZZ",medium:"medium-EZuD3gZZ",large:"large-EZuD3gZZ",iconWrapper:"iconWrapper-EZuD3gZZ",icon:"icon-EZuD3gZZ"}},193139:e=>{e.exports={radioButtonView:"radioButtonView-zzLJI6BH",checked:"checked-zzLJI6BH",disabled:"disabled-zzLJI6BH"}},533560:e=>{e.exports={radio:"radio-ALqkCUvs",reverse:"reverse-ALqkCUvs",label:"label-ALqkCUvs",wrapper:"wrapper-ALqkCUvs",input:"input-ALqkCUvs",box:"box-ALqkCUvs",disabled:"disabled-ALqkCUvs"}},199864:e=>{e.exports={wrap:"wrap-g_7HJ1Y8",item:"item-g_7HJ1Y8",checked:"checked-g_7HJ1Y8",accessible:"accessible-g_7HJ1Y8",focusVisible:"focusVisible-g_7HJ1Y8",radio:"radio-g_7HJ1Y8",paddingCompact:"paddingCompact-g_7HJ1Y8"}},502182:e=>{e.exports={"small-height-breakpoint":"(max-height: 360px)",footer:"footer-PhMf7PhQ",submitButton:"submitButton-PhMf7PhQ",buttons:"buttons-PhMf7PhQ"}},515931:e=>{e.exports={wrap:"wrap-ne5qGlZh",icon:"icon-ne5qGlZh",text:"text-ne5qGlZh",disabled:"disabled-ne5qGlZh"}},674997:e=>{e.exports={container:"container-zTsGmQlA",title:"title-zTsGmQlA",lineContainer:"lineContainer-zTsGmQlA",lineSegment:"lineSegment-zTsGmQlA",checked:"checked-zTsGmQlA"}},598406:e=>{e.exports={colorPickerWrap:"colorPickerWrap-Sw_a4qpB",focused:"focused-Sw_a4qpB",readonly:"readonly-Sw_a4qpB",disabled:"disabled-Sw_a4qpB","size-small":"size-small-Sw_a4qpB","size-medium":"size-medium-Sw_a4qpB","size-large":"size-large-Sw_a4qpB","font-size-small":"font-size-small-Sw_a4qpB","font-size-medium":"font-size-medium-Sw_a4qpB","font-size-large":"font-size-large-Sw_a4qpB","border-none":"border-none-Sw_a4qpB",shadow:"shadow-Sw_a4qpB","border-thin":"border-thin-Sw_a4qpB","border-thick":"border-thick-Sw_a4qpB","intent-default":"intent-default-Sw_a4qpB","intent-success":"intent-success-Sw_a4qpB","intent-warning":"intent-warning-Sw_a4qpB","intent-danger":"intent-danger-Sw_a4qpB","intent-primary":"intent-primary-Sw_a4qpB","corner-top-left":"corner-top-left-Sw_a4qpB","corner-top-right":"corner-top-right-Sw_a4qpB","corner-bottom-right":"corner-bottom-right-Sw_a4qpB","corner-bottom-left":"corner-bottom-left-Sw_a4qpB",colorPicker:"colorPicker-Sw_a4qpB",swatch:"swatch-Sw_a4qpB",placeholderContainer:"placeholderContainer-Sw_a4qpB",placeholder:"placeholder-Sw_a4qpB",mixedColor:"mixedColor-Sw_a4qpB",white:"white-Sw_a4qpB",opacitySwatch:"opacitySwatch-Sw_a4qpB",colorLine:"colorLine-Sw_a4qpB",multiWidth:"multiWidth-Sw_a4qpB",lineStyleSelect:"lineStyleSelect-Sw_a4qpB",
overflowContainer:"overflowContainer-Sw_a4qpB",linePropertyContainer:"linePropertyContainer-Sw_a4qpB",whiteContainer:"whiteContainer-Sw_a4qpB",multiProperty:"multiProperty-Sw_a4qpB",lineItem:"lineItem-Sw_a4qpB",lineSegment:"lineSegment-Sw_a4qpB"}},569211:e=>{e.exports={thicknessContainer:"thicknessContainer-C05zSid7",thicknessTitle:"thicknessTitle-C05zSid7",bar:"bar-C05zSid7",checked:"checked-C05zSid7"}},158851:e=>{e.exports={hasTooltip:"hasTooltip-DcvaoxPU",uppercase:"uppercase-DcvaoxPU"}},792492:e=>{e.exports={wrap:"wrap-Q2NZ0gvI"}},152257:e=>{e.exports={checkbox:"checkbox-FG0u1J5p",title:"title-FG0u1J5p"}},307208:e=>{e.exports={hintButton:"hintButton-qEI9XsjF",infoTooltip:"infoTooltip-qEI9XsjF"}},371035:e=>{e.exports={titleWrap:"titleWrap-SexRbl__",title:"title-SexRbl__"}},863460:e=>{e.exports={button:"button-HBcDEU4c",accessible:"accessible-HBcDEU4c"}},160387:e=>{e.exports={container:"container-mdcOkvbj",sectionTitle:"sectionTitle-mdcOkvbj",separator:"separator-mdcOkvbj",customButton:"customButton-mdcOkvbj",accessible:"accessible-mdcOkvbj"}},675187:e=>{e.exports={container:"container-iiEYaqPD",form:"form-iiEYaqPD",swatch:"swatch-iiEYaqPD",white:"white-iiEYaqPD",inputWrap:"inputWrap-iiEYaqPD",inputHash:"inputHash-iiEYaqPD",input:"input-iiEYaqPD",buttonWrap:"buttonWrap-iiEYaqPD",hueSaturationWrap:"hueSaturationWrap-iiEYaqPD",saturation:"saturation-iiEYaqPD",hue:"hue-iiEYaqPD"}},58065:e=>{e.exports={hue:"hue-r4uo5Wn6",pointer:"pointer-r4uo5Wn6",accessible:"accessible-r4uo5Wn6",pointerContainer:"pointerContainer-r4uo5Wn6"}},294085:e=>{e.exports={opacity:"opacity-EnWts7Xu",opacitySlider:"opacitySlider-EnWts7Xu",opacitySliderGradient:"opacitySliderGradient-EnWts7Xu",pointer:"pointer-EnWts7Xu",dragged:"dragged-EnWts7Xu",opacityPointerWrap:"opacityPointerWrap-EnWts7Xu",opacityInputWrap:"opacityInputWrap-EnWts7Xu",opacityInput:"opacityInput-EnWts7Xu",opacityInputPercent:"opacityInputPercent-EnWts7Xu",accessible:"accessible-EnWts7Xu"}},287109:e=>{e.exports={saturation:"saturation-NFNfqP2w",pointer:"pointer-NFNfqP2w",accessible:"accessible-NFNfqP2w"}},811992:e=>{e.exports={swatches:"swatches-sfn7Lezv",swatch:"swatch-sfn7Lezv",hover:"hover-sfn7Lezv",empty:"empty-sfn7Lezv",white:"white-sfn7Lezv",selected:"selected-sfn7Lezv",contextItem:"contextItem-sfn7Lezv",row:"row-sfn7Lezv"}},99505:e=>{e.exports={button:"button-tFul0OhX","button-children":"button-children-tFul0OhX",hiddenArrow:"hiddenArrow-tFul0OhX",invisibleFocusHandler:"invisibleFocusHandler-tFul0OhX"}},541140:e=>{e.exports={"icon-wrapper":"icon-wrapper-dikdewwx","with-link":"with-link-dikdewwx","with-tooltip":"with-tooltip-dikdewwx","no-active-state":"no-active-state-dikdewwx"}},165630:e=>{e.exports={placeholder:"placeholder-V6ceS6BN"}},805184:(e,t,n)=>{"use strict";var o,s,a;function r(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function i(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function l(e="m"){switch(e){case"s":return"xsmall";case"m":
return"small";case"l":return"large"}}n.d(t,{Button:()=>d}),function(e){e.Primary="primary",e.Success="success",e.Default="default",e.Danger="danger"}(o||(o={})),function(e){e.Small="s",e.Medium="m",e.Large="l"}(s||(s={})),function(e){e.Default="default",e.Stroke="stroke"}(a||(a={}));var c=n(50959),u=n(228837);function p(e){const{intent:t,size:n,appearance:o,useFullWidth:s,icon:a,...c}=e;return{...c,color:i(t),size:l(n),variant:r(o),stretch:s}}function d(e){return c.createElement(u.SquareButton,{...p(e)})}},558213:(e,t,n)=>{"use strict";n.d(t,{Textarea:()=>C});var o,s=n(50959),a=n(497754),r=n(525388),i=n(383836),l=n(21778),c=n(603548),u=n(269842),p=n(1811),d=n(34735),h=n(102691),m=n(961853),g=n.n(m);!function(e){e.None="none",e.Vertical="vertical",e.Horizontal="horizontal",e.Both="both"}(o||(o={}));const v=s.forwardRef(((e,t)=>{const{id:n,title:o,tabIndex:r,containerTabIndex:i,role:l,inputClassName:c,autoComplete:u,autoFocus:p,cols:m,disabled:v,isFocused:f,form:b,maxLength:C,minLength:y,name:x,placeholder:E,readonly:w,required:S,rows:_,value:T,defaultValue:N,wrap:I,containerReference:k,onChange:P,onSelect:D,onFocus:B,onContainerFocus:O,onBlur:L,onPaste:M,"aria-describedby":R,"aria-required":V,"aria-invalid":A,hasIcon:F,endSlot:W,hasAttachImage:q,...z}=e,Z={id:n,title:o,tabIndex:r,role:l,autoComplete:u,autoFocus:p,cols:m,disabled:v,form:b,maxLength:C,minLength:y,name:x,placeholder:E,readOnly:w,required:S,rows:_,value:T,defaultValue:N,wrap:I,onChange:P,onSelect:D,onFocus:B,onBlur:L,onPaste:M,"aria-describedby":R,"aria-required":V,"aria-invalid":A};return s.createElement(d.ControlSkeleton,{...z,tabIndex:i,disabled:v,readonly:w,isFocused:f,ref:k,onFocus:O,middleSlot:s.createElement(h.MiddleSlot,null,s.createElement("textarea",{...Z,className:a(g().textarea,c,W&&g().endslot),ref:t})),...W&&{endSlot:s.createElement("span",{className:a(!q&&g()["with-icon"])},W)}})}));v.displayName="TextareaView";const f=(e,t,n)=>t?void 0:e?-1:n,b=(e,t,n)=>t?void 0:e?n:-1,C=s.forwardRef(((e,t)=>{e=(0,l.useControl)(e);const{className:n,disabled:d,autoSelectOnFocus:h,tabIndex:m=0,borderStyle:C,highlight:y,resize:x,containerReference:E=null,onFocus:w,onBlur:S,hasIcon:_,...T}=e,N=(0,s.useRef)(null),I=(0,s.useRef)(null),{isMouseDown:k,handleMouseDown:P,handleMouseUp:D}=(0,c.useIsMouseDown)(),[B,O]=(0,i.useFocus)(),L=(0,u.createSafeMulticastEventHandler)(O.onFocus,(function(e){h&&!k.current&&(0,p.selectAllContent)(e.currentTarget)}),w),M=(0,u.createSafeMulticastEventHandler)(O.onBlur,S),R=void 0!==x&&x!==o.None,V=C??(R?y?"thick":"thin":void 0),A=y??(!R&&void 0);return s.createElement(v,{...T,className:a(g()["textarea-container"],R&&g()["change-highlight"],x&&x!==o.None&&g()[`resize-${x}`],B&&g().focused,n),disabled:d,isFocused:B,containerTabIndex:f(B,d,m),tabIndex:b(B,d,m),borderStyle:V,highlight:A,onContainerFocus:function(e){I.current===e.target&&null!==N.current&&N.current.focus()},onFocus:L,onBlur:M,onMouseDown:P,onMouseUp:D,ref:function(e){N.current=e,"function"==typeof t?t(e):t&&(t.current=e)},containerReference:(0,r.useMergedRefs)([E,I]),
hasIcon:_})}));C.displayName="Textarea"},942544:(e,t,n)=>{"use strict";n.d(t,{useControlDisclosure:()=>s});var o=n(772069);function s(e){const{intent:t,highlight:n,...s}=e,{isFocused:a,...r}=(0,o.useDisclosure)(s);return{...r,isFocused:a,highlight:n??a,intent:t??(a?"primary":"default")}}},139784:(e,t,n)=>{"use strict";n.d(t,{useTooltip:()=>i});var o=n(50959),s=n(799573);var a=n(718736);const r=200;function i(e,t=null){const{showTooltip:n,hideTooltip:i,onClick:l,doNotShowTooltipOnTouch:c=!1}=e,u=(0,a.useFunctionalRefObject)(t),p=function(){const[e,t]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{t(s.mobiletouch)}),[]),e}(),d=p&&c?void 0:e.tooltip;(0,o.useEffect)((()=>{const e=()=>i&&i();return document.addEventListener("scroll",e,!0),()=>document.removeEventListener("scroll",e,!0)}),[u,i]);return{onBlur:(0,o.useCallback)((e=>{i&&i()}),[i]),onFocus:(0,o.useCallback)((e=>{!e.target.matches(":hover")&&n&&e.target.matches(":focus-visible")&&n(e.currentTarget,{tooltipDelay:r})}),[n]),onClick:(0,o.useCallback)((e=>{p&&u?.current?.focus(),l&&l(e)}),[l,u,p]),tooltip:d,className:void 0!==d?"apply-common-tooltip":void 0,ref:u}}},404568:(e,t,n)=>{"use strict";n.d(t,{RadioButtonView:()=>i});var o=n(50959),s=n(497754),a=n.n(s),r=n(193139);function i(e){const{disabled:t,checked:n}=e;return o.createElement("span",{className:a()(r.radioButtonView,t&&r.disabled,n&&r.checked)})}},976669:(e,t,n)=>{"use strict";n.r(t),n.d(t,{AdaptiveConfirmDialog:()=>h,DialogCloseAction:()=>o});var o,s=n(50959),a=n(497754),r=n.n(a),i=n(805184),l=n(650151),c=n(609838),u=n(180185),p=n(533408),d=n(502182);!function(e){e.Submit="submit",e.Cancel="cancel",e.None="none"}(o||(o={}));class h extends s.PureComponent{constructor(){super(...arguments),this._dialogRef=s.createRef(),this._handleClose=()=>{const{defaultActionOnClose:e,onSubmit:t,onCancel:n,onClose:o}=this.props;switch(e){case"submit":t();break;case"cancel":n()}o()},this._handleCancel=()=>{this.props.onCancel(),this.props.onClose()},this._handleKeyDown=e=>{const{onSubmit:t,submitButtonDisabled:n,submitOnEnterKey:o}=this.props;13===(0,u.hashFromEvent)(e)&&o&&(e.preventDefault(),n||t())}}render(){const{render:e,onClose:t,onSubmit:n,onCancel:o,footerLeftRenderer:a,submitButtonText:r,submitButtonDisabled:i,defaultActionOnClose:l,submitOnEnterKey:c,...u}=this.props;return s.createElement(p.AdaptivePopupDialog,{...u,ref:this._dialogRef,onKeyDown:this._handleKeyDown,render:this._renderChildren(),onClose:this._handleClose})}focus(){(0,l.ensureNotNull)(this._dialogRef.current).focus()}_renderChildren(){return e=>{const{render:t,footerLeftRenderer:o,additionalButtons:a,submitButtonText:l,submitButtonDisabled:u,onSubmit:p,cancelButtonText:h,showCancelButton:m=!0,showSubmitButton:g=!0,submitButtonClassName:v,cancelButtonClassName:f,buttonsWrapperClassName:b}=this.props;return s.createElement(s.Fragment,null,t(e),s.createElement("div",{className:d.footer},o&&o(e.isSmallWidth),s.createElement("div",{className:r()(d.buttons,b)},a,m&&s.createElement(i.Button,{className:f,name:"cancel",appearance:"stroke",
onClick:this._handleCancel},h??c.t(null,void 0,n(904543))),g&&s.createElement("span",{className:d.submitButton},s.createElement(i.Button,{className:v,disabled:u,name:"submit",onClick:p,"data-name":"submit-button"},l??c.t(null,void 0,n(819295)))))))}}}h.defaultProps={defaultActionOnClose:"submit",submitOnEnterKey:!0}},27950:(e,t,n)=>{"use strict";n.d(t,{EditButton:()=>l});var o=n(50959),s=n(497754),a=n(878112),r=n(610600),i=n(515931);function l(e){const{value:t,onClick:n,className:l,startSlot:c,disabled:u=!1}=e;return o.createElement("div",{className:s(i.wrap,u&&i.disabled,l),onClick:n,"data-name":"edit-button"},o.createElement("div",{className:s(i.text,"apply-overflow-tooltip")},void 0!==c&&c,o.createElement("span",null,t)),o.createElement(a.Icon,{icon:r,className:i.icon}))}},782302:(e,t,n)=>{"use strict";n.d(t,{ColorSelect:()=>A});var o=n(50959),s=n(497754),a=n.n(s),r=n(650151),i=n(180185),l=n(624216),c=n(383836),u=n(44093),p=n(230553),d=n(442092),h=n(333086),m=n(863460);function g(e){const{button:t,children:n,className:s,onPopupClose:g,"data-name":v,onColorChange:f,disabled:b,...C}=e,[y,x]=(0,o.useState)(!1),[E,w]=(0,o.useState)(!1),[S,_]=(0,c.useFocus)(),T=(0,o.useRef)(null),N=(0,o.useRef)(null),I=(0,o.useRef)(null);return o.createElement("div",{className:s,"data-name":v},o.createElement("button",{className:a()(m.button,m.accessible),tabIndex:b?-1:0,ref:I,onClick:function(){if(e.disabled)return;w((e=>!e)),x(!1)},onFocus:_.onFocus,onBlur:_.onBlur,disabled:b},"function"==typeof t?t(E,S):t),o.createElement(l.PopupMenu,{reference:N,controller:T,onFocus:function(e){if(!e.target||e.target!==e.currentTarget||y)return;const t=e.currentTarget,n=(0,r.ensureNotNull)((o=e.target,o.querySelector('[data-role="swatch"]:not([disabled]):not([aria-disabled])')));var o;(0,h.becomeMainElement)(n),setTimeout((()=>{if(document.activeElement!==t||!e.target.matches(":focus-visible"))return;const[n]=(0,d.queryTabbableElements)(t).sort(d.navigationOrderComparator);n&&n.focus()}))},isOpened:E,onClose:k,position:function(){const e=(0,r.ensureNotNull)(I.current).getBoundingClientRect();return{x:e.left,y:e.top+e.height}},doNotCloseOn:I.current,onKeyDown:function(e){if(27===(0,i.hashFromEvent)(e))E&&(e.preventDefault(),k())},onOpen:function(){T.current?.focus()},tabIndex:-1},o.createElement(p.MenuContext.Consumer,null,(e=>o.createElement(u.ColorPicker,{...C,onColorChange:f,onToggleCustom:x,menu:e}))),!y&&n));function k(){w(!1),(0,r.ensureNotNull)(I.current).focus(),g&&g()}}var v=n(206397),f=n(589637),b=n(860184),C=n(609838),y=n(230789),x=n(199864);const E=(0,y.makeSwitchGroupItem)(class extends o.PureComponent{constructor(e){super(e),this._onChange=()=>{this.props.onChange&&this.props.onChange(this.props.value)},this._handleFocus=e=>{e.target.matches(":focus-visible")&&this.setState({isFocusVisible:!0})},this._handleBlur=()=>{this.state.isFocusVisible&&this.setState({isFocusVisible:!1})},this.state={isFocusVisible:!1}}render(){const{name:e,checked:t,value:n,renderItemContent:a,className:r}=this.props,i=s(x.item,x.accessible,{[x.checked]:t,
[x.focusVisible]:this.state.isFocusVisible},r);return o.createElement("div",{className:i},o.createElement("input",{type:"radio",className:x.radio,name:e,value:n,onChange:this._onChange,onFocus:this._handleFocus,onBlur:this._handleBlur,checked:t}),a(parseInt(n),t))}});function w(e){const{name:t,values:n,selectedValues:a,onChange:r,renderItemContent:i}=e,l=n.map(((e,t)=>o.createElement(E,{key:t,value:e.toString(),renderItemContent:i,className:s(n.length>3&&x.paddingCompact)}))),c=a.map((e=>e.toString()));return o.createElement("div",{className:x.wrap},o.createElement(y.SwitchGroup,{name:t,onChange:e=>{r(parseInt(e))},values:c},l))}var S=n(569211);const _=C.t(null,void 0,n(254971));function T(e){const{value:t,items:n,onChange:s}=e;return o.createElement("div",{className:S.thicknessContainer},o.createElement("div",{className:S.thicknessTitle},_),o.createElement(w,{name:"color_picker_thickness_select",onChange:s,values:n,selectedValues:"mixed"===t?[]:[t],renderItemContent:(e,t)=>o.createElement("div",{className:a()(S.bar,t&&S.checked),style:{borderTopWidth:e}})}))}var N=n(936879);function I(e,t,n,o,s,a){return{repeat:e,width:t,height:n,segmentsGap:o,marginTop:s,segmentsGaps:a}}const k={"style-SOLID_thickness-1":I(1,"30px","1px"),"style-DASHED_thickness-1":I(4,"5px","1px","3px"),"style-DOTTED_thickness-1":I(6,"2px","2px","3px"),"style-SOLID_thickness-2":I(1,"30px","2px"),"style-DASHED_thickness-2":I(4,"5px","2px","3px"),"style-DOTTED_thickness-2":I(5,"3px","3px","3px"),"style-SOLID_thickness-3":I(1,"30px","3px"),"style-DASHED_thickness-3":I(4,"5px","3px","3px"),"style-DOTTED_thickness-3":I(5,"4px","4px","2px"),"style-SOLID_thickness-4":I(1,"30px","4px"),"style-DASHED_thickness-4":I(4,"5px","4px","3px"),"style-DOTTED_thickness-4":I(4,"5px","5px","3px")},P={...k,"style-mixed_thickness-large":I(1,"30px","16px"),"style-mixed_thickness-1":[I(1,"27px","1px"),I(4,"4px","1px","4px","5px",{3:"3px"}),I(6,"2px","2px","3px","5px")],"style-mixed_thickness-2":[I(1,"27px","2px"),I(4,"4px","2px","4px","4px",{3:"3px"}),I(6,"2px","2px","3px","4px")],"style-mixed_thickness-3":[I(1,"27px","3px"),I(4,"4px","3px","4px","3px",{3:"3px"}),I(6,"2px","2px","3px","3px")],"style-mixed_thickness-4":[I(1,"27px","4px"),I(4,"4px","4px","4px","3px",{3:"3px"}),I(6,"2px","2px","3px","3px")],"style-SOLID_thickness-mixed":[I(1,"30px","3px",void 0,"4px"),I(1,"30px","2px",void 0,"4px"),I(1,"30px","1px",void 0,"4px")],"style-DASHED_thickness-mixed":[I(4,"5px","3px","3px"),I(4,"5px","2px","3px","4px"),I(4,"5px","1px","3px","4px")],"style-DOTTED_thickness-mixed":[I(5,"3px","3px","3px"),I(5,"3px","2px","3px","4px"),I(6,"2px","2px","3px","4px")],"style-mixed_thickness-mixed":[I(1,"30px","3px"),I(4,"6px","2px","2px","4px"),I(5,"2px","2px","5px","4px")]};function D(e,t){if(!t)return[];const n=e??N.LINESTYLE_SOLID;if("mixed"!==t&&t>4)return P["style-mixed_thickness-large"]||[];const o="mixed"===n?"mixed":B(n);return P[`style-${o}_thickness-${"mixed"===t?"mixed":t}`]||[]}function B(e){switch(e){case N.LINESTYLE_SOLID:return"SOLID";case N.LINESTYLE_DOTTED:return"DOTTED"
;case N.LINESTYLE_DASHED:return"DASHED";default:return"UNKNOWN"}}var O=n(431520),L=n(674997);function M(e){const{value:t,items:s,onChange:r,className:i}=e;return o.createElement("div",{className:a()(L.container,i)},o.createElement("div",{className:L.title},C.t(null,void 0,n(228603))),o.createElement(w,{name:"color_picker_line_style_select",onChange:r,values:s,selectedValues:"mixed"===t?[]:[t],renderItemContent:R}))}function R(e,t){const n=function(e,t=1){const n=B(e);return k[`style-${n}_thickness-${t}`]||null}(e,1);return n?o.createElement("div",{className:L.lineContainer},Array.from({length:n.repeat}).map(((e,s)=>{const r=n?.segmentsGaps?.[s]||n?.segmentsGap;return o.createElement("div",{key:s,className:a()(L.lineSegment,t&&L.checked),style:{width:n.width,height:n.height,...0!==s&&{[(0,O.isRtl)()?"marginRight":"marginLeft"]:r}}})}))):null}var V=n(598406);function A(e){const{className:t,selectOpacity:n=void 0!==e.opacity,thickness:s,lineStyle:r,color:i,disabled:l,opacity:c=1,onColorChange:u,onOpacityChange:p,onThicknessChange:d,onLineStyleChange:h,thicknessItems:m,lineStyleItems:f,onPopupClose:b,"data-name":C}=e,[y,x,E]=(0,v.useCustomColors)(),w=s&&m&&m.length>0;return o.createElement(g,{className:t,disabled:l,color:"mixed"!==i?i:null,selectOpacity:n,opacity:c,selectCustom:!0,customColors:y,onColorChange:u,onOpacityChange:i?p:void 0,onAddColor:x,onRemoveCustomColor:E,button:function(e,t){const n=e||t,u=n?"primary":"default";return o.createElement("div",{className:a()(V.colorPickerWrap,V[`intent-${u}`],V["border-thin"],V["size-medium"],n&&V.highlight,n&&V.focused,l&&V.disabled),"data-role":"button","data-name":s?"color-with-thickness-select":"color-select"},o.createElement("div",{className:a()(V.colorPicker,l&&V.disabled)},i&&"mixed"!==i?function(){const e=F(i,c),t=c>=.95&&W(i);return o.createElement("div",{className:V.opacitySwatch},o.createElement("div",{style:{backgroundColor:e},className:a()(V.swatch,t&&V.white)}))}():o.createElement("div",{className:V.placeholderContainer},o.createElement("div",{className:"mixed"===i?V.mixedColor:V.placeholder})),(void 0!==r||s)&&function(){const e=i&&"mixed"!==i?F(i,c):void 0;if(void 0===r&&!s)return null;const t=D(r,s);if(!t||Array.isArray(t)&&!t.length)return null;if(Array.isArray(t))return o.createElement("div",{className:V.overflowContainer},o.createElement("div",{className:a()(V.linePropertyContainer,V.multiProperty,W(i)&&V.whiteContainer)},t.map(((t,n)=>o.createElement("div",{key:n,className:V.lineItem,style:{marginTop:W(i)&&t?.marginTop?`calc(${t.marginTop} - 2px)`:t?.marginTop}},S(t,e))))));return o.createElement("div",{className:V.overflowContainer},o.createElement("div",{className:a()(V.linePropertyContainer,W(i)&&V.whiteContainer)},S(t,e)))}()),n&&o.createElement("span",{className:V.shadow}))},onPopupClose:b,"data-name":C},w&&o.createElement(T,{value:s,items:m,onChange:function(e){d?.(e)}}),void 0!==r&&f&&f?.length>0&&o.createElement(M,{className:a()(w&&V.lineStyleSelect),value:r,items:f,onChange:function(e){h?.(e)}}));function S(e,t){return Array.from({
length:e.repeat}).map(((n,s)=>{const r=e?.segmentsGaps?.[s]||e?.segmentsGap,l=W(i)&&r?`calc(${r} - 2px)`:r;return o.createElement("div",{key:s,className:a()(V.lineSegment,W(i)&&V.white),style:{width:e.width,height:e.height,backgroundColor:t,...0!==s&&{[(0,O.isRtl)()?"marginRight":"marginLeft"]:l}}})}))}}function F(e,t){return e?(0,f.generateColor)(e,(0,f.alphaToTransparency)(t),!0):"#000000"}function W(e){return!!e&&e.toLowerCase()===b.white}},377707:(e,t,n)=>{"use strict";n.d(t,{defaultLineStyleItems:()=>c,lineStyleItemValues:()=>l});var o=n(787382),s=n(936879),a=n(200501),r=n(123851),i=n(357740);const l=[{type:s.LINESTYLE_SOLID,icon:a,label:o.t(null,void 0,n(903554))},{type:s.LINESTYLE_DASHED,icon:r,label:o.t(null,void 0,n(488123))},{type:s.LINESTYLE_DOTTED,icon:i,label:o.t(null,void 0,n(27390))}],c=l.map((e=>e.type))},193315:(e,t,n)=>{"use strict";n.d(t,{SymbolInputsButton:()=>x});var o=n(50959),s=n(497754),a=n.n(s),r=n(609838),i=n(650151),l=n(753327),c=n(910549),u=n(440891),p=n(375751),d=n(284741),h=n(558323),m=n(27950),g=n(955261),v=n(720911),f=n(707002),b=n(626800),C=n(158851);function y(e){const{symbol:t,onSymbolChanged:s,disabled:i,className:p}=e,[g,v]=(0,o.useState)(t),y=(0,o.useContext)(l.SlotContext),x=(0,o.useContext)(c.PopupContext);let E;return u.enabled("symbol_search_option_chain_selector")&&({quotes:E}=useSymbolQuotes(matchesOptionPattern(g)?g:null)),o.createElement(m.EditButton,{value:g,onClick:function(){const e=function(e){const t=(0,f.tokenize)(e);return(0,f.isSpread)(t)}(g)?g:(0,b.safeShortName)(g),t="option"===E?.type,o=E?.["underlying-symbol"],a=u.enabled("symbol_search_option_chain_selector")&&t&&o?{type:"option",value:g,underlying:o}:e,i=(0,d.getSymbolSearchCompleteOverrideFunction)();(0,h.showSymbolSearchItemsDialog)({onSearchComplete:e=>{i(e[0].symbol,e[0].result).then((e=>{s(e.symbol),v(e.name)}))},dialogTitle:r.t(null,void 0,n(963245)),defaultValue:a,manager:y,onClose:()=>{x&&x.focus()},searchInitiationPoint:"indicatorInputs",enableOptionsChain:u.enabled("symbol_search_option_chain_selector"),showSpreadActions:u.enabled("show_spread_operators")&&u.enabled("studies_symbol_search_spread_operators")})},disabled:i,className:a()(p,u.enabled("uppercase_instrument_names")&&C.uppercase)})}function x(e){if("definition"in e){const{propType:t,properties:n,id:s,title:a="",solutionId:r}=e.definition,l=n[t],c=l.value()||"",u=e=>{l.setValue(e)};return o.createElement(g.CommonSection,{id:s,title:a,solutionId:r},o.createElement(v.CellWrap,null,o.createElement(y,{symbol:(0,i.ensureDefined)(c),onSymbolChanged:u})))}{const{study:t,value:n,input:{id:s,name:r},onChange:l,disabled:c,hasTooltip:u}=e,d=e=>{const n=(0,p.getInternalSymbolName)(e,t);l(n,s,r)};return o.createElement(y,{symbol:(0,i.ensureDefined)(n),onSymbolChanged:d,disabled:c,className:a()(u&&C.hasTooltip)})}}},558323:(e,t,n)=>{"use strict";n.d(t,{showSymbolSearchItemsDialog:()=>l});var o=n(50959),s=n(753327),a=n(63192),r=n(798154),i=n(651674);function l(e){
const{symbolTypeFilter:t,initialMode:n="symbolSearch",autofocus:l=!0,defaultValue:c,showSpreadActions:u,selectSearchOnInit:p,onSearchComplete:d,dialogTitle:h,placeholder:m,fullscreen:g,initialScreen:v,wrapper:f,dialog:b,contentItem:C,onClose:y,onOpen:x,footer:E,symbolTypes:w,searchInput:S,emptyState:_,hideMarkedListFlag:T,dialogWidth:N="auto",manager:I,shouldReturnFocus:k,onSymbolFiltersParamsChange:P,onEmptyResults:D,customSearchSymbols:B,enableOptionsChain:O,searchInitiationPoint:L}=e;if(a.dialogsOpenerManager.isOpened("SymbolSearch")||a.dialogsOpenerManager.isOpened("ChangeIntervalDialog"))return;const M=document.createElement("div"),R=o.createElement(s.SlotContext.Provider,{value:I??null},o.createElement(r.SymbolSearchItemsDialog,{symbolTypeFilter:t,onClose:A,initialMode:n,defaultValue:c,showSpreadActions:u,hideMarkedListFlag:T,selectSearchOnInit:p,onSearchComplete:d,dialogTitle:h,placeholder:m,fullscreen:g,initialScreen:v,wrapper:f,dialog:b,contentItem:C,footer:E,symbolTypes:w,searchInput:S,emptyState:_,autofocus:l,dialogWidth:N,shouldReturnFocus:k,onSymbolFiltersParamsChange:P,onEmptyResults:D,customSearchSymbols:B,enableOptionsChain:O,searchInitiationPoint:L})),V=(0,i.createReactRoot)(R,M);function A(){V.unmount(),a.dialogsOpenerManager.setAsClosed("SymbolSearch"),y&&y()}return a.dialogsOpenerManager.setAsOpened("SymbolSearch"),x&&x(),{close:A}}},39290:(e,t,n)=>{"use strict";n.d(t,{createAdapter:()=>r,doesStudyLikeAffectSave:()=>i});var o=n(128492),s=n(979910),a=n(278906);function r(e){if((0,o.isLineTool)(e))return{isPine:()=>!1,isStandardPine:()=>!1,canOverrideMinTick:()=>!1,resolvedSymbolInfoBySymbol:()=>{throw new TypeError("Only study is supported.")},symbolsResolved:()=>{throw new TypeError("Only study is supported.")},parentSources:()=>{throw new TypeError("Only study is supported.")},getAllChildren:()=>[],sourceId:()=>{throw new TypeError("Only study is supported.")},inputs:()=>({}),parentSourceForInput:()=>{throw new TypeError("Only study is supported.")}};if((0,a.isStudy)(e))return e;if("isInputsStudy"in e)return e;throw new TypeError("Unsupported source type.")}function i(e){return(0,a.isStudy)(e)||!s.lineToolsDoNotAffectChartInvalidation}},771586:(e,t,n)=>{"use strict";n.d(t,{useDefinitionProperty:()=>a});var o=n(50959),s=n(653898);const a=e=>{const t="property"in e?e.property:void 0,n="defaultValue"in e?e.defaultValue:e.property.value(),[a,r]=(0,o.useState)(t?t.value():n);(0,o.useEffect)((()=>{if(t){const n={};return r(t.value()),t.subscribe(n,(t=>{const n=t.value();e.handler&&e.handler(n),r(n)})),()=>{t.unsubscribeAll(n)}}return()=>{}}),[t]);return[a,e=>{if(void 0!==t){const n=t.value();s.logger.logNormal(`Changing property value from "${n}" to "${e}"`),t.setValue(e)}}]}},720911:(e,t,n)=>{"use strict";n.d(t,{CellWrap:()=>i});var o=n(50959),s=n(497754),a=n.n(s),r=n(792492);function i(e){return o.createElement("div",{className:a()(r.wrap,e.className)},e.children)}},76882:(e,t,n)=>{"use strict";n.d(t,{CheckableTitle:()=>c});var o=n(50959),s=n(302946),a=n(771586);function r(e){
const{property:t,...n}=e,[r,i]=(0,a.useDefinitionProperty)({property:t}),l="mixed"===r;return o.createElement(s.Checkbox,{...n,name:"toggle-enabled",checked:l||r,indeterminate:l,onChange:function(){i("mixed"===r||!r)}})}var i=n(720911),l=n(152257);function c(e){const{property:t,disabled:n,title:s,className:a,name:c}=e,u=o.createElement("span",{className:l.title},s);return o.createElement(i.CellWrap,{className:a},t?o.createElement(r,{name:c,className:l.checkbox,property:t,disabled:n,label:u,labelAlignBaseline:!0}):u)}},955261:(e,t,n)=>{"use strict";n.d(t,{CommonSection:()=>r});var o=n(50959),s=n(47924),a=n(76882);n(307208);function r(e){const{id:t,offset:n,disabled:r,checked:i,title:l,children:c,solutionId:u,infoTooltip:p}=e;return o.createElement(s.PropertyTable.Row,null,o.createElement(s.PropertyTable.Cell,{placement:"first",verticalAlign:"adaptive",offset:n,"data-section-name":t,colSpan:Boolean(c)?void 0:2,checkableTitle:!0},o.createElement(a.CheckableTitle,{name:`is-enabled-${t}`,title:l,disabled:r,property:i}),u&&!Boolean(c)&&!1,p&&!Boolean(c)&&!1),Boolean(c)&&o.createElement(s.PropertyTable.Cell,{placement:"last","data-section-name":t},c,u&&!1,p&&!1))}},852830:(e,t,n)=>{"use strict";n.d(t,{GroupTitleSection:()=>i});var o=n(50959),s=n(47924),a=n(76882),r=n(371035);function i(e){return o.createElement(s.PropertyTable.Row,null,o.createElement(s.PropertyTable.Cell,{className:r.titleWrap,placement:"first",verticalAlign:"adaptive",colSpan:2,"data-section-name":e.name,checkableTitle:!0},o.createElement(a.CheckableTitle,{title:e.title,name:`is-enabled-${e.name}`,className:r.title})))}},653898:(e,t,n)=>{"use strict";n.d(t,{logger:()=>o});const o=(0,n(671945).getLogger)("Platform.GUI.PropertyDefinitionTrace")},886176:(e,t,n)=>{"use strict";n.d(t,{getChartTimezoneOffsetMs:()=>a,getTimezoneName:()=>s});var o=n(834002);function s(e){const t=e.model().timezone();if("exchange"!==t)return t;const n=e.model().mainSeries().symbolInfo();return n?.timezone}function a(e,t){if(void 0===t)return 0;return(0,o.get_timezone)(t).offset_utc(e)}},63192:(e,t,n)=>{"use strict";n.d(t,{DialogsOpenerManager:()=>o,dialogsOpenerManager:()=>s});class o{constructor(){this._storage=new Map}setAsOpened(e,t){this._storage.set(e,t)}setAsClosed(e){this._storage.delete(e)}isOpened(e){return this._storage.has(e)}getDialogPayload(e){return this._storage.get(e)}}const s=new o},44093:(e,t,n)=>{"use strict";n.d(t,{ColorPicker:()=>z});var o=n(50959),s=n(497754),a=n.n(s),r=n(609838),i=n(578601),l=n(382665),c=n(724377),u=n(650151),p=n(601227),d=n(624216),h=n(192063),m=n(860184),g=n(865266),v=n(993544),f=n(811992);const b=4;function C(e){const{color:t,selected:a,onSelect:i,onSwatchRemove:l}=e,[c,C]=(0,o.useState)(!1),[y,x]=(0,g.useRovingTabindexElement)(null),E=Boolean(l)&&!p.CheckMobile.any();return o.createElement(o.Fragment,null,o.createElement("button",{ref:y,style:t?{color:t}:void 0,className:s(f.swatch,c&&f.hover,a&&f.selected,!t&&f.empty,String(t).toLowerCase()===m.white&&f.white),onClick:function(){i(t)},onContextMenu:E?w:void 0,tabIndex:x,
"data-role":"swatch"}),E&&o.createElement(d.PopupMenu,{isOpened:c,onClose:w,position:function(){const e=(0,u.ensureNotNull)(y.current).getBoundingClientRect();return{x:e.left,y:e.top+e.height+b}},onClickOutside:w},o.createElement(h.PopupMenuItem,{className:f.contextItem,label:r.t(null,void 0,n(989984)),icon:v,onClick:function(){w(),(0,u.ensureDefined)(l)()},dontClosePopup:!0})));function w(){C(!c)}}const y=10;function x(e){const{colors:t,color:n,children:s,onSelect:a,onRemoveCustomColor:r}=e;if(!t)return null;const i=n?(0,c.parseRgb)(String(n)):void 0,u=(0,l.default)(t,y);return o.createElement("div",{className:f.swatches},u.map(((e,t)=>o.createElement("div",{className:f.row,"data-role":"row",key:t},e.map(((e,n)=>o.createElement(C,{key:String(e)+n,color:e,selected:i&&(0,c.areEqualRgb)(i,(0,c.parseRgb)(String(e))),onSelect:p,onSwatchRemove:r?()=>function(e,t){const n=e*y+t;r?.(n)}(t,n):void 0})))))),s);function p(e){a&&a(e)}}var E=n(939075),w=n(805184);function S(e){const t=`Invalid RGB color: ${e}`;if(null===e)throw new Error(t);const n=e.match(/^#?([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})$/i);if(null===n)throw new Error(t);const[,o,s,a]=n;if(!o||!s||!a)throw new Error(t);const r=parseInt(o,16)/255,i=parseInt(s,16)/255,l=parseInt(a,16)/255,c=Math.max(r,i,l),u=Math.min(r,i,l);let p;const d=c,h=c-u,m=0===c?0:h/c;if(c===u)p=0;else{switch(c){case r:p=(i-l)/h+(i<l?6:0);break;case i:p=(l-r)/h+2;break;case l:p=(r-i)/h+4;break;default:p=0}p/=6}return{h:p,s:m,v:d}}var _=n(920057),T=n(180185),N=n(822960),I=n(287109);const k=[37,39,38,40],P=.01;class D extends o.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=e=>{this._container=e},this._handlePosition=e=>{const{hsv:{h:t},onChange:n}=this.props;if(!n)return;const o=(0,u.ensureNotNull)(this._container).getBoundingClientRect(),s=e.clientX-o.left,a=e.clientY-o.top;n({h:t,s:(0,N.clamp)(s/o.width,0,1),v:(0,N.clamp)(1-a/o.height,0,1)})},this._handleKeyDown=e=>{const{hsv:{h:t,s:n,v:o},onChange:s}=this.props,a=(0,T.hashFromEvent)(e);if(!s||!k.includes(a))return;if(37===a||39===a){return void s({h:t,s:(0,N.clamp)(37===a?n-P:n+P,0,1),v:o})}s({h:t,s:n,v:(0,N.clamp)(40===a?o-P:o+P,0,1)})},this._mouseDown=e=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=e=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(e)},this._mouseMove=(0,_.default)(this._handlePosition,100),this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])}}render(){const{className:e,hsv:{h:t,s:n,v:s}}=this.props,r=`hsl(${360*t}, 100%, 50%)`;return o.createElement("div",{tabIndex:0,className:a()(I.accessible,e),onKeyDown:this._handleKeyDown},o.createElement("div",{className:I.saturation,style:{backgroundColor:r},ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},o.createElement("div",{className:I.pointer,style:{left:100*n+"%",top:100*(1-s)+"%"}})))}}var B=n(58065)
;class O extends o.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=e=>{this._container=e},this._handlePosition=e=>{const{hsv:{s:t,v:n},onChange:o}=this.props;if(!o)return;const s=(0,u.ensureNotNull)(this._container).getBoundingClientRect(),a=e.clientY-s.top;o({h:(0,N.clamp)(a/s.height,0,1),s:t,v:n})},this._handleKeyDown=e=>{const{hsv:{h:t,s:n,v:o},onChange:s}=this.props,a=(0,T.hashFromEvent)(e);if(!s||38!==a&&40!==a)return;s({h:(0,N.clamp)(38===a?t-.01:t+.01,0,1),s:n,v:o})},this._mouseDown=e=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=e=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(e)},this._mouseMove=(0,_.default)(this._handlePosition,100),this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])}}render(){const{className:e,hsv:{h:t}}=this.props;return o.createElement("div",{className:a()(B.hue,B.accessible,e),tabIndex:0,onKeyDown:this._handleKeyDown},o.createElement("div",{className:B.pointerContainer,ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},o.createElement("div",{className:B.pointer,style:{top:100*t+"%"}})))}}var L=n(675187);const M="#000000",R=r.t(null,{context:"Color Picker"},n(155517));class V extends o.PureComponent{constructor(e){super(e),this._inputRef=o.createRef(),this._handleHSV=e=>{const t=function(e){const{h:t,s:n,v:o}=e;let s,a,r;const i=Math.floor(6*t),l=6*t-i,c=o*(1-n),u=o*(1-l*n),p=o*(1-(1-l)*n);switch(i%6){case 0:s=o,a=p,r=c;break;case 1:s=u,a=o,r=c;break;case 2:s=c,a=o,r=p;break;case 3:s=c,a=u,r=o;break;case 4:s=p,a=c,r=o;break;case 5:s=o,a=c,r=u;break;default:s=0,a=0,r=0}return"#"+[255*s,255*a,255*r].map((e=>("0"+Math.round(e).toString(16)).replace(/.+?([a-f0-9]{2})$/i,"$1"))).join("")}(e)||M;this.setState({color:t,inputColor:A(t),hsv:e}),this.props.onSelect(t)},this._handleInput=e=>{const t=A(e.currentTarget.value);try{const e=S(t),n=`#${t}`;this.setState({color:n,inputColor:t,hsv:e}),this.props.onSelect(n)}catch(e){this.setState({inputColor:t})}},this._handleAddColor=()=>this.props.onAdd(this.state.color);const t=e.color||M;this.state={color:t,inputColor:A(t),hsv:S(t)}}componentDidMount(){p.CheckMobile.any()||this._inputRef.current?.focus()}render(){const{color:e,hsv:t,inputColor:n}=this.state;return o.createElement("div",{className:L.container},o.createElement("div",{className:L.form},o.createElement("div",{className:a()(L.swatch,String(e).toLowerCase()===m.white&&L.white),style:{backgroundColor:e}}),o.createElement("div",{className:L.inputWrap},o.createElement("span",{className:L.inputHash},"#"),o.createElement("input",{ref:this._inputRef,type:"text",className:L.input,value:n,onChange:this._handleInput})),o.createElement("div",{className:L.buttonWrap},o.createElement(w.Button,{size:"s",onClick:this._handleAddColor},R))),o.createElement("div",{className:L.hueSaturationWrap},o.createElement(D,{className:L.saturation,
hsv:t,onChange:this._handleHSV}),o.createElement(O,{className:L.hue,hsv:t,onChange:this._handleHSV})))}}function A(e){return e.replace(/^#/,"")}var F=n(160387);const W=r.t(null,{context:"Color Picker"},n(329619)),q=r.t(null,{context:"Color Picker"},n(980936));function z(e){const{color:t,opacity:n,selectCustom:s,selectOpacity:r,customColors:l,onRemoveCustomColor:c,onToggleCustom:u,onOpacityChange:p,menu:d}=e,[h,g]=(0,o.useState)(!1),v="number"==typeof n?n:1,[f,b]=(0,i.useRowsNavigation)();return(0,o.useLayoutEffect)((()=>{d&&d.update()}),[r,d]),h?o.createElement(V,{color:t,onSelect:C,onAdd:function(t){g(!1),u?.(!1);const{onAddColor:n}=e;n&&n(t)}}):o.createElement("div",{className:F.container},o.createElement("div",{ref:f,onKeyDown:b},o.createElement(x,{colors:m.basic,color:t,onSelect:C}),o.createElement(x,{colors:m.extended,color:t,onSelect:C}),o.createElement("div",{className:F.separator}),o.createElement(x,{colors:l,color:t,onSelect:C,onRemoveCustomColor:c},s&&o.createElement(o.Fragment,null,l?.length?o.createElement("button",{title:W,onClick:y,className:a()(F.customButton,F.accessible,"apply-common-tooltip"),tabIndex:-1}):o.createElement("div",{"data-role":"row"},o.createElement("button",{title:W,onClick:y,className:a()(F.customButton,F.accessible,"apply-common-tooltip"),tabIndex:-1}))))),r&&o.createElement(o.Fragment,null,o.createElement("div",{className:F.sectionTitle},q),o.createElement(E.Opacity,{color:t,opacity:v,onChange:function(e){p&&p(e)}})));function C(t){const{onColorChange:n}=e;n&&n(t,h)}function y(e){g(!0),u?.(!0)}}},939075:(e,t,n)=>{"use strict";n.d(t,{Opacity:()=>c});var o=n(50959),s=n(497754),a=n(650151),r=n(822960),i=n(180185),l=n(294085);class c extends o.PureComponent{constructor(e){super(e),this._container=null,this._pointer=null,this._raf=null,this._refContainer=e=>{this._container=e},this._refPointer=e=>{this._pointer=e},this._handlePosition=e=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{const t=(0,a.ensureNotNull)(this._container),n=(0,a.ensureNotNull)(this._pointer),o=t.getBoundingClientRect(),s=n.offsetWidth,i=e.clientX-s/2-o.left,l=(0,r.clamp)(i/(o.width-s),0,1);this.setState({inputOpacity:Math.round(100*l).toString()}),this.props.onChange(l),this._raf=null})))},this._onSliderClick=e=>{this._handlePosition(e.nativeEvent),this._dragSubscribe()},this._mouseUp=e=>{this.setState({isPointerDragged:!1}),this._dragUnsubscribe(),this._handlePosition(e)},this._mouseMove=e=>{this.setState({isPointerDragged:!0}),this._handlePosition(e)},this._onTouchStart=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouch=e=>{this.setState({isPointerDragged:!0}),this._handlePosition(e.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this.setState({isPointerDragged:!1})},this._handleInput=e=>{const t=e.currentTarget.value,n=Number(t)/100;this.setState({inputOpacity:t}),Number.isNaN(n)||n>1||this.props.onChange(n)},this._handleKeyDown=e=>{const t=(0,i.hashFromEvent)(e);if(37!==t&&39!==t)return;e.preventDefault();const n=Number(this.state.inputOpacity)
;37===t&&0!==n&&this._changeOpacity(n-1),39===t&&100!==n&&this._changeOpacity(n+1)},this.state={inputOpacity:Math.round(100*e.opacity).toString(),isPointerDragged:!1}}componentWillUnmount(){null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),this._dragUnsubscribe()}render(){const{color:e,opacity:t,hideInput:n,disabled:a}=this.props,{inputOpacity:r,isPointerDragged:i}=this.state,c={color:e||void 0};return o.createElement("div",{className:l.opacity},o.createElement("div",{className:s(l.opacitySlider,l.accessible),style:c,tabIndex:a?-1:0,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd,onKeyDown:this._handleKeyDown,"aria-disabled":a},o.createElement("div",{className:l.opacitySliderGradient,style:{backgroundImage:`linear-gradient(90deg, transparent, ${e})`}}),o.createElement("div",{className:l.opacityPointerWrap},o.createElement("div",{className:s(l.pointer,i&&l.dragged),style:{left:100*t+"%"},ref:this._refPointer}))),!n&&o.createElement("div",{className:l.opacityInputWrap},o.createElement("input",{type:"text",className:l.opacityInput,value:r,onChange:this._handleInput}),o.createElement("span",{className:l.opacityInputPercent},"%")))}_dragSubscribe(){const e=(0,a.ensureNotNull)(this._container).ownerDocument;e&&(e.addEventListener("mouseup",this._mouseUp),e.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const e=(0,a.ensureNotNull)(this._container).ownerDocument;e&&(e.removeEventListener("mousemove",this._mouseMove),e.removeEventListener("mouseup",this._mouseUp))}_changeOpacity(e){this.setState({inputOpacity:e.toString()}),this.props.onChange(e/100)}}},860184:(e,t,n)=>{"use strict";n.d(t,{basic:()=>i,extended:()=>c,white:()=>s});var o=n(926048);const s=o.colorsPalette["color-white"],a=["ripe-red","tan-orange","banana-yellow","iguana-green","minty-green","sky-blue","tv-blue","deep-blue","grapes-purple","berry-pink"],r=[200,300,400,500,600,700,800,900].map((e=>`color-cold-gray-${e}`));r.unshift("color-white"),r.push("color-black"),a.forEach((e=>{r.push(`color-${e}-500`)}));const i=r.map((e=>o.colorsPalette[e])),l=[];[100,200,300,400,700,900].forEach((e=>{a.forEach((t=>{l.push(`color-${t}-${e}`)}))}));const c=l.map((e=>o.colorsPalette[e]))},444144:(e,t,n)=>{"use strict";n.d(t,{ControlDisclosureView:()=>d});var o=n(50959),s=n(497754),a=n.n(s),r=n(525388),i=n(34735),l=n(102691),c=n(904925),u=n(763802),p=n(99505);const d=o.forwardRef(((e,t)=>{const{listboxId:n,className:s,listboxClassName:d,listboxTabIndex:h,hideArrowButton:m,matchButtonAndListboxWidths:g,popupPosition:v,disabled:f,isOpened:b,scrollWrapReference:C,repositionOnScroll:y,closeOnHeaderOverlap:x,listboxReference:E,size:w="small",onClose:S,onOpen:_,onListboxFocus:T,onListboxBlur:N,onListboxKeyDown:I,buttonChildren:k,children:P,caretClassName:D,buttonContainerClassName:B,listboxAria:O,...L}=e,M=(0,o.useRef)(null),R=!m&&o.createElement(l.EndSlot,null,o.createElement(u.Caret,{isDropped:b,disabled:f,className:D}))
;return o.createElement(c.PopupMenuDisclosureView,{buttonRef:M,listboxId:n,listboxClassName:d,listboxTabIndex:h,isOpened:b,onClose:S,onOpen:_,listboxReference:E,scrollWrapReference:C,onListboxFocus:T,onListboxBlur:N,onListboxKeyDown:I,listboxAria:O,matchButtonAndListboxWidths:g,popupPosition:v,button:o.createElement(i.ControlSkeleton,{...L,"data-role":"listbox",disabled:f,className:a()(p.button,s),size:w,ref:(0,r.useMergedRefs)([M,t]),middleSlot:o.createElement(l.MiddleSlot,null,o.createElement("span",{className:a()(p["button-children"],m&&p.hiddenArrow,B)},k)),endSlot:R}),popupChildren:P,repositionOnScroll:y,closeOnHeaderOverlap:x})}));d.displayName="ControlDisclosureView"},206397:(e,t,n)=>{"use strict";n.d(t,{useCustomColors:()=>c});var o=n(50959),s=n(870122),a=n(559410);function r(e,t){(0,o.useEffect)((()=>(a.subscribe(e,t,null),()=>{a.unsubscribe(e,t,null)})),[e,t])}var i,l=n(724377);function c(){const[e,t]=(0,o.useState)((0,s.getJSON)("pickerCustomColors",[]));r("add_new_custom_color",(n=>t(u(n,e)))),r("remove_custom_color",(n=>t(p(n,e))));const n=(0,o.useCallback)((t=>{const n=t?(0,l.parseRgb)(t):null;e.some((e=>null!==e&&null!==n&&(0,l.areEqualRgb)((0,l.parseRgb)(e),n)))||(a.emit("add_new_custom_color",t),(0,s.setJSON)("pickerCustomColors",u(t,e)))}),[e]),i=(0,o.useCallback)((t=>{(t>=0||t<e.length)&&(a.emit("remove_custom_color",t),(0,s.setJSON)("pickerCustomColors",p(t,e)))}),[e]);return[e,n,i]}function u(e,t){const n=t.slice();return n.push(e),n.length>29&&n.shift(),n}function p(e,t){return t.filter(((t,n)=>e!==n))}!function(e){e.SettingsKey="pickerCustomColors",e.GlobalAddEventName="add_new_custom_color",e.GlobalRemoveEventName="remove_custom_color",e[e.MaxColors=29]="MaxColors"}(i||(i={}))},737563:(e,t,n)=>{"use strict";n.d(t,{IconQuestionInformation:()=>N});var o,s=n(50959),a=n(497754),r=n.n(a),i=n(800417),l=n(878112),c=n(482353),u=n(527941),p=n(499084),d=n(530162),h=n(946105),m=n.n(h);!function(e){e.Small="small",e.Medium="medium",e.Large="large"}(o||(o={}));const g="small";var v,f;!function(e){e.Default="default",e.Danger="danger",e.Warning="warning",e.Success="success",e.Neutral="neutral",e.NeutralLight="neutral-light"}(v||(v={})),function(e){e.Info="info",e.Question="question",e.Check="check",e.Exclamation="exclamation"}(f||(f={}));const b={info:u,question:c,check:p,exclamation:d},C=s.forwardRef(((e,t)=>s.createElement("span",{...e,ref:t,className:r()(e.className,m()["no-active-state"])}))),y=s.forwardRef(((e,t)=>{const{icon:n="exclamation",intent:o="default",ariaLabel:a,tooltip:c,className:u,renderComponent:p=C,tabIndex:d=0,size:h=g,onFocus:v,onBlur:f,onClick:y,...x}=e,E=b[n]??n;return s.createElement(p,{className:r()(u,m().iconWrapper,m()[o],m()[h]),title:c,"aria-label":a,ref:t,tabIndex:d,onFocus:v,onBlur:f,onClick:y,...(0,i.filterDataProps)(x)},s.createElement(l.Icon,{"aria-hidden":!0,icon:E,className:m().icon}))}));var x=n(139784),E=n(744471),w=n(541140),S=n.n(w);function _(){document.removeEventListener("scroll",_),document.removeEventListener("touchstart",_),
document.removeEventListener("mouseout",_),(0,E.hide)()}const T=e=>{(0,E.showOnElement)(e.currentTarget,{tooltipDelay:0}),document.addEventListener("scroll",_),document.addEventListener("touchstart",_),document.addEventListener("mouseout",_)},N=(0,s.forwardRef)(((e,t)=>{const{className:n,onClick:o=T,doNotShowTooltipOnTouch:r,size:i,...l}=e,{tooltip:c,className:u,...p}=(0,x.useTooltip)({tooltip:e.tooltip,doNotShowTooltipOnTouch:!1,showTooltip:E.showOnElement,hideTooltip:E.hide,onClick:o},t);return s.createElement(y,{className:a(n,S()["icon-wrapper"],c&&S()["with-tooltip"],u),tooltip:c,size:i,...l,...p})}));(0,s.forwardRef)(((e,t)=>{const{className:n,href:o,rel:r,target:i,...l}=e,c=(0,s.useMemo)((()=>(0,s.forwardRef)(((e,t)=>s.createElement("a",{href:o,rel:r,target:i,ref:t,...e})))),[o,r,i]);return s.createElement(N,{...l,className:a(n,S()["with-link"]),renderComponent:c,ref:t,doNotShowTooltipOnTouch:!0})})),(0,s.forwardRef)(((e,t)=>{const{className:n,withActiveState:o,...r}=e,i=(0,s.useMemo)((()=>(0,s.forwardRef)(((e,t)=>s.createElement("button",{...e,ref:t,type:"button"})))),[]);return s.createElement(N,{...r,className:a(n,!o&&S()["no-active-state"]),renderComponent:i,ref:t})}))},763802:(e,t,n)=>{"use strict";n.d(t,{Caret:()=>o.Caret,CaretButton:()=>o.CaretButton});var o=n(148982)},529631:(e,t,n)=>{"use strict";n.d(t,{Select:()=>y});var o=n(50959),s=n(855393),a=n(414823),r=n(525388),i=n(930617),l=n(192063),c=n(290484),u=n(920057);var p=n(648621),d=n(953517),h=n(444144),m=n(942544),g=n(431520),v=n(165630);function f(e){return!e.readonly}function b(e,t){return t?.id??(0,a.createDomId)(e,"item",t?.value)}function C(e){const{selectedItem:t,placeholder:n}=e;if(!t)return o.createElement("span",{className:v.placeholder},n);const s=t.selectedContent??t.content??t.value;return o.createElement("span",null,s)}const y=o.forwardRef(((e,t)=>{const{id:n,menuClassName:v,menuItemClassName:y,tabIndex:x,disabled:E,highlight:w,intent:S,hideArrowButton:_,placeholder:T,addPlaceholderToItems:N=!1,value:I,"aria-labelledby":k,onFocus:P,onBlur:D,onClick:B,onChange:O,onKeyDown:L,repositionOnScroll:M=!0,openMenuOnEnter:R=!0,"aria-describedby":V,"aria-invalid":A,...F}=e;let{items:W}=e;if(T&&N){W=[{value:void 0,content:T,id:(0,a.createDomId)(n,"placeholder")},...W]}const{listboxId:q,isOpened:z,isFocused:Z,buttonTabIndex:H,listboxTabIndex:U,highlight:K,intent:G,open:$,onOpen:Y,close:Q,toggle:X,buttonFocusBindings:J,onButtonClick:j,buttonRef:ee,listboxRef:te,buttonAria:ne}=(0,m.useControlDisclosure)({id:n,disabled:E,buttonTabIndex:x,intent:S,highlight:w,onFocus:P,onBlur:D,onClick:B}),oe=W.filter(f),se=oe.find((e=>e.value===I)),[ae,re]=o.useState(T&&N?oe[0].value:se?.value),[ie,le,ce]=(0,i.useKeepActiveItemIntoView)({activeItem:se});(0,s.useIsomorphicLayoutEffect)((()=>re(se?.value)),[I]);const ue=(0,a.joinDomIds)(k,n),pe=ue.length>0?ue:void 0,de=(0,o.useMemo)((()=>({role:"listbox","aria-labelledby":k,"aria-activedescendant":b(n,se)})),[k,se]),he=(0,o.useCallback)((e=>e.value===ae),[ae]),me=(0,o.useCallback)((()=>(Q(),O&&O(ae))),[Q,O,ae]),ge=(0,
p.useItemsKeyboardNavigation)("vertical",g.isRtl,oe,he,(e=>{re(e.value)}),!1,{next:[40],previous:[38]}),ve=(0,d.useKeyboardToggle)(X,z||R),fe=(0,d.useKeyboardToggle)(me),be=(0,d.useKeyboardClose)(z,Se),Ce=(0,d.useKeyboardOpen)(z,$),ye=(0,d.useKeyboardEventHandler)([ve,be,Ce]),xe=(0,d.useKeyboardEventHandler)([ge,fe,be]),Ee=function(e){const t=(0,o.useRef)(""),n=(0,o.useMemo)((()=>(0,c.default)((()=>{t.current=""}),500)),[]),s=(0,o.useMemo)((()=>(0,u.default)(e,200)),[e]);return(0,o.useCallback)((e=>{e.key.length>0&&e.key.length<3&&(t.current+=e.key,s(t.current,e),n())}),[n,s])}(((t,n)=>{const o=function(e,t,n){return e.find((e=>{const o=t.toLowerCase();return!e.readonly&&(n?n(e).toLowerCase().startsWith(o):!e.readonly&&("string"==typeof e.content&&e.content.toLowerCase().startsWith(o)||"string"==typeof e.textContent&&e.textContent.toLowerCase().startsWith(o)||String(e.value??"").toLowerCase().startsWith(o)))}))}(oe,t,e.getSearchKey);void 0!==o&&O&&(n.stopPropagation(),z||$(),O(o.value))}));return o.createElement(h.ControlDisclosureView,{...F,...ne,...J,id:n,role:"button",tabIndex:H,"aria-owns":ne["aria-controls"],"aria-haspopup":"listbox","aria-labelledby":pe,disabled:E,hideArrowButton:_,isFocused:Z,isOpened:z,highlight:K,intent:G,ref:(0,r.useMergedRefs)([ee,t]),onClick:j,onOpen:function(){ce(se,{duration:0}),Y()},onClose:Se,onKeyDown:function(e){ye(e),L&&L(e);e.defaultPrevented||Ee(e)},listboxId:q,listboxTabIndex:U,listboxClassName:v,listboxAria:de,"aria-describedby":V,"aria-invalid":A,listboxReference:te,scrollWrapReference:ie,onListboxKeyDown:function(e){xe(e),e.defaultPrevented||Ee(e)},buttonChildren:o.createElement(C,{selectedItem:se??null,placeholder:T}),repositionOnScroll:M},W.map(((e,t)=>{if(e.readonly)return o.createElement(o.Fragment,{key:`readonly_item_${t}`},e.content);const s=b(n,e);return o.createElement(l.PopupMenuItem,{key:s,id:s,className:y,role:"option","aria-selected":I===e.value,isActive:ae===e.value,label:e.content??e.value,onClick:we,onClickArg:e.value,isDisabled:e.disabled,reference:t=>le(e,t)})})));function we(e){O&&(O(e),re(e))}function Se(){re(se?.value),Q()}}));y.displayName="Select"},727246:e=>{e.exports={titleWrap:"titleWrap-Izz3hpJc",groupFooter:"groupFooter-Izz3hpJc"}},592359:e=>{e.exports={wrapper:"wrapper-JXHzsa7P"}},248286:e=>{e.exports={inlineRow:"inlineRow-D8g11qqA"}},480779:e=>{e.exports={container:"container-QyF09i7Y",hasTooltip:"hasTooltip-QyF09i7Y",datePickerWrapper:"datePickerWrapper-QyF09i7Y",timePickerWrapper:"timePickerWrapper-QyF09i7Y"}},897650:e=>{e.exports={input:"input-ZOx_CVY3",symbol:"symbol-ZOx_CVY3",checkbox:"checkbox-ZOx_CVY3",label:"label-ZOx_CVY3",dropdownMenu:"dropdownMenu-ZOx_CVY3",sessionStart:"sessionStart-ZOx_CVY3",sessionEnd:"sessionEnd-ZOx_CVY3",sessionInputContainer:"sessionInputContainer-ZOx_CVY3",sessionDash:"sessionDash-ZOx_CVY3",inputGroup:"inputGroup-ZOx_CVY3",textarea:"textarea-ZOx_CVY3",inlineGroup:"inlineGroup-ZOx_CVY3",hasTooltip:"hasTooltip-ZOx_CVY3"}},785027:e=>{e.exports={content:"content-tBgV1m0B",cell:"cell-tBgV1m0B",inner:"inner-tBgV1m0B",
first:"first-tBgV1m0B",inlineCell:"inlineCell-tBgV1m0B",fill:"fill-tBgV1m0B",top:"top-tBgV1m0B",topCenter:"topCenter-tBgV1m0B",offset:"offset-tBgV1m0B",inlineRow:"inlineRow-tBgV1m0B",grouped:"grouped-tBgV1m0B",separator:"separator-tBgV1m0B",groupSeparator:"groupSeparator-tBgV1m0B",big:"big-tBgV1m0B",adaptive:"adaptive-tBgV1m0B",checkableTitle:"checkableTitle-tBgV1m0B"}},930584:e=>{e.exports={wrap:"wrap-QutFvTLS",labelWrap:"labelWrap-QutFvTLS",label:"label-QutFvTLS",hasTooltip:"hasTooltip-QutFvTLS"}},587513:(e,t,n)=>{"use strict";n.d(t,{bind:()=>r,setter:()=>i});var o=n(50959),s=n(294859),a=n(886176);function r(e){var t;return t=class extends o.PureComponent{constructor(){super(...arguments),this._onChange=(e,t,n)=>{const{setValue:o}=this.context,{onChange:s}=this.props;i(o,s)(e,t,n)}}render(){const{input:t}=this.props,{values:n,model:s}=this.context;return o.createElement(e,{...this.props,value:n[t.id],tzName:(0,a.getTimezoneName)(s),onChange:this._onChange})}},t.contextType=s.PropertyContext,t}function i(e,t){return(n,o,s)=>{e(o,n,s),t&&t(n,o,s)}}},294859:(e,t,n)=>{"use strict";n.d(t,{PropertyContainer:()=>m,PropertyContext:()=>h});var o=n(50959),s=n(650151),a=n(671945),r=n(609838),i=n(272047),l=n(278906),c=n(979910),u=n(14043);const p=(0,a.getLogger)("Platform.GUI.StudyInputPropertyContainer"),d=new i.TranslatedString("change {propertyName} property",r.t(null,void 0,n(925167))),h=o.createContext(null);class m extends o.PureComponent{constructor(e){super(e),this._setValue=(e,t,n)=>{const{property:o,model:a,study:r}=this.props,h=!(0,l.isStudy)(r),m=(0,s.ensureDefined)(o.child(e));p.logNormal(`Changing property "${e}" value from "${o.value()}" to "${t}"`);const g=new i.TranslatedString(n,(0,u.getTranslatedInputTitle)(n));a.setProperty(m,t,d.format({propertyName:g}),h&&c.lineToolsDoNotAffectChartInvalidation)};const{property:t}=e,n={};t.childNames().forEach((e=>{const o=(0,s.ensureDefined)(t.child(e));n.hasOwnProperty(e)||(n[e]=o.value())})),this.state=n}componentDidMount(){const{property:e,onStudyInputChange:t}=this.props;e.childNames().forEach((n=>{(0,s.ensureDefined)(e.child(n)).subscribe(this,(e=>{const o=e.value();p.logNormal(`Property "${n}" updated to value "${o}"`),this.setState({[n]:o}),t?.(o,n)}))}))}componentWillUnmount(){const{property:e}=this.props;e.childNames().forEach((t=>{(0,s.ensureDefined)(e.child(t)).unsubscribeAll(this)}))}render(){const{study:e,model:t,children:n}=this.props,s={study:e,model:t,values:this.state,setValue:this._setValue};return o.createElement(h.Provider,{value:s},n)}}},176086:(e,t,n)=>{"use strict";n.d(t,{ModelContext:()=>s,bindModel:()=>a});var o=n(50959);const s=o.createContext(null);function a(e,t){return o.createElement(s.Consumer,null,(n=>n?o.createElement(e,{...Object.assign({model:n},t)}):null))}},527053:(e,t,n)=>{"use strict";n.d(t,{StylePropertyContainer:()=>r,StylePropertyContext:()=>a,bindPropertyContext:()=>i});var o=n(50959),s=n(176086);const a=o.createContext(null);class r extends o.PureComponent{constructor(){super(...arguments),this._setValue=(e,t,n)=>{
const{model:o,affectSave:s}=this.props;Array.isArray(e)?o.setProperties(e,e.map((()=>t)),n,s):o.setProperty(e,t,n,!s)}}componentDidMount(){const{property:e}=this.props;e.subscribe(this,(()=>this.forceUpdate()))}componentWillUnmount(){const{property:e}=this.props;e.unsubscribeAll(this)}render(){const e={setValue:this._setValue};return o.createElement(a.Provider,{value:e},this.props.children)}}function i(e,t,n){return(0,s.bindModel)((({model:s})=>o.createElement(r,{model:s,affectSave:n,property:t.property},o.createElement(e,{...t}))),t)}},756639:(e,t,n)=>{"use strict";n.d(t,{IconGroupWrapper:()=>a});var o=n(50959),s=n(592359);function a(e){const{children:t}=e;return o.createElement("div",{className:s.wrapper},t)}},423112:(e,t,n)=>{"use strict";n.d(t,{InputTooltip:()=>a});var o=n(50959),s=n(737563);function a(e){const{className:t,title:n}=e;return o.createElement(s.IconQuestionInformation,{icon:"info",className:t,ariaLabel:n,tooltip:n,tabIndex:-1})}},457540:(e,t,n)=>{"use strict";n.d(t,{getInputGroups:()=>i,isGroup:()=>a,isInputInlines:()=>r});var o,s=n(650151);function a(e){return e.hasOwnProperty("groupType")}function r(e){return a(e)&&"inline"===e.groupType}function i(e){const t=[],n=new Map,o=new Map;return o.set(void 0,new Map),e.forEach((e=>{const{group:a,inline:r}=e;if(void 0!==a||void 0!==r)if(void 0!==a)if(void 0!==r)if(n.has(a)){const t=(0,s.ensureDefined)(n.get(a));let i;o.has(t)?i=(0,s.ensureDefined)(o.get(t)):(i=new Map,o.set(t,i)),l(e,"inline",r,i,t.children)}else{const s={id:r,groupType:"inline",children:[e]},i={id:a,groupType:"group",children:[s]},l=new Map;l.set(r,s),o.set(i,l),n.set(a,i),t.push(i)}else l(e,"group",a,n,t);else{const n=(0,s.ensureDefined)(o.get(void 0));l(e,"inline",(0,s.ensureDefined)(r),n,t)}else t.push(e)})),t}function l(e,t,n,o,a){if(o.has(n))(0,s.ensureDefined)(o.get(n)).children.push(e);else{const s={id:n,groupType:t,children:[e]};o.set(n,s),a.push(s)}}!function(e){e.Inline="inline",e.Group="group"}(o||(o={}))},193400:(e,t,n)=>{"use strict";n.d(t,{InputRow:()=>ae});var o=n(50959),s=n(650151),a=n(53868),r=n(517651),i=n(802527),l=n(562364),c=n(497754),u=n.n(c),p=n(654936),d=n(587513),h=n(130949),m=n(897650);class g extends o.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{input:{id:t,name:n},onChange:o}=this.props;o(e.currentTarget.value,t,n)}}render(){const{input:{defval:e},value:t,disabled:n,onBlur:s,onKeyDown:a,hasTooltip:r}=this.props;return o.createElement(p.InputControl,{className:u()(m.input,r&&m.hasTooltip),value:void 0===t?e:t,onChange:this._onChange,onBlur:s,onKeyDown:a,disabled:n,maxLength:4096})}}const v=(0,h.debounced)(g),f=(0,d.bind)(v);var b=n(375751),C=n(47924);function y(e){const{className:t}=e,n=(0,o.useContext)(C.PropertyTable.InlineRowContext);return o.createElement("div",{className:c(m.inputGroup,n&&m.inlineGroup,t)},e.children)}var x=n(44807);function E(e=""){const[,t="",n="",o="",s=""]=Array.from(e.match(/^(\d\d)(\d\d)-(\d\d)(\d\d)/)||[]);return[`${t}:${n}`,`${o}:${s}`]}class w extends o.PureComponent{constructor(e){super(e),
this._onStartPick=e=>{this.setState({startTime:e},this._onChange)},this._onEndPick=e=>{this.setState({endTime:e},this._onChange)},this._onChange=()=>{const{input:{id:e,name:t},onChange:n}=this.props,{startTime:o,endTime:s}=this.state;n(o.replace(":","")+"-"+s.replace(":",""),e,t)};const t=e.value||e.input.defval,[n,o]=E(t);this.state={prevValue:t,startTime:n,endTime:o}}render(){const{startTime:e,endTime:t}=this.state,{hasTooltip:n,disabled:a}=this.props;return o.createElement(y,{className:u()(n&&m.hasTooltip)},o.createElement("div",{className:m.sessionStart},o.createElement(x.TimeInput,{className:u()(m.input,m.sessionInputContainer),name:"start",value:(0,s.ensureDefined)(e),onChange:this._onStartPick,disabled:a}),o.createElement("span",{className:m.sessionDash}," — ")),o.createElement("div",{className:m.sessionEnd},o.createElement(x.TimeInput,{className:u()(m.input,m.sessionInputContainer),name:"end",value:(0,s.ensureDefined)(t),onChange:this._onEndPick,disabled:a})))}static getDerivedStateFromProps(e,t){if(e.value===t.prevValue)return t;const[n,o]=E(e.value);return{prevValue:e.value,startTime:n,endTime:o}}}const S=(0,d.bind)(w);var _=n(609838),T=n(440891),N=n(762293),I=n(209039),k=n(294859),P=n(529631),D=n(14043);class B extends o.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{input:{id:t,name:n},onChange:o}=this.props;o(e,t,n)}}render(){const{input:{id:e,defval:t,options:n,optionsTitles:s},value:a,disabled:r,hasTooltip:i}=this.props,l=n.map((e=>{const t=s&&s[e]?s[e]:e;return{value:e,content:(0,D.getTranslatedInputTitle)(t)}})),c=void 0!==a&&n.includes(a)?a:t;return o.createElement(P.Select,{id:e,className:u()(m.input,i&&m.hasTooltip),menuClassName:m.dropdownMenu,value:c,items:l,onChange:this._onChange,disabled:r})}}const O=(0,d.bind)(B);var L=n(39290),M=n(278906);const R={open:_.t(null,void 0,n(483584)),high:_.t(null,void 0,n(159319)),low:_.t(null,void 0,n(841902)),close:_.t(null,void 0,n(305741)),hl2:_.t(null,void 0,n(396008)),hlc3:_.t(null,void 0,n(391189)),ohlc4:_.t(null,void 0,n(452793)),hlcc4:_.t(null,void 0,n(149242))};class V extends o.PureComponent{render(){const{input:e}=this.props,{study:t,model:n}=this.context;let r={...R};delete r.hlcc4;const i=(0,L.createAdapter)(t);if(t&&this._isStudy(t)&&t.isChildStudy()){const t=(0,a.getInputValue)(i.inputs()[e.id]),n=i.parentSourceForInput(t);if(n&&(0,M.isStudy)(n)){const t=n.title(I.TitleDisplayTarget.StatusLine),o=N.StudyMetaInfo.getChildSourceInputTitles(e,n.metaInfo(),t);r={...r,...o}}}if(T.enabled("study_on_study")&&t&&this._isStudy(t)&&(t.isChildStudy()||N.StudyMetaInfo.canBeChild(t.metaInfo()))){const e=[t,...i.getAllChildren()];n.model().allStudies().filter((t=>t.canHaveChildren()&&!e.includes(t))).forEach((e=>{const t=e.title(I.TitleDisplayTarget.StatusLine,!0,void 0,!0),n=e.id(),o=e.metaInfo(),a=o.styles,i=o.plots||[];if(1===i.length)r[n+"$0"]=t;else if(i.length>1){const e=i.reduce(((e,o,r)=>{if(!N.StudyMetaInfo.canPlotBeSourceOfChildStudy(o.type))return e;let i;try{i=(0,s.ensureDefined)((0,s.ensureDefined)(a)[o.id]).title
}catch(e){i=o.id}return{...e,[`${n}$${r}`]:`${t}: ${i}`}}),{});r={...r,...e}}}))}const l={...e,type:"text",options:Object.keys(r),optionsTitles:r};return o.createElement(O,{...this.props,input:l})}_isStudy(e){return!e.hasOwnProperty("isInputsStudy")}}V.contextType=k.PropertyContext;var A=n(28964),F=n(989175),W=n(206212);const q=void 0,z=["1","3","5","15","30","45","60","120","180","240","1D","1W","1M","3M","6M","12M"],Z=["1S","5S","10S","15S","30S"],H=["1T","10T","100T","1000T"];class U extends o.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{input:{id:t,name:n},onChange:o}=this.props;o(e,t,n)}}render(){const{input:e,value:t,disabled:s,hasTooltip:a}=this.props,r=A.Interval.parse(void 0===t?e.defval:t),i=r.isValid()?r.value():t,l=q?q.get().filter((e=>!A.Interval.parse(e).isRange())):[],c=(0,F.mergeResolutions)(z,(0,F.isSecondsEnabled)()?Z:[],(0,W.isTicksEnabled)()?H:[],l);return c.unshift(""),o.createElement(P.Select,{id:e.id,className:u()(m.input,m.resolution,a&&m.hasTooltip),menuClassName:u()(m.dropdownMenu,m.resolution),items:(p=c,p.map((e=>({value:e,content:""===e?_.t(null,void 0,n(354613)):(0,F.getTranslatedResolutionModel)(e).hint})))),value:i,onChange:this._onChange,disabled:s});var p}}const K=(0,d.bind)(U);var G=n(982021),$=n(527053);class Y extends o.PureComponent{render(){return o.createElement(k.PropertyContext.Consumer,null,(e=>e?this._getColorInputWithContext(e):null))}_getColorInputWithContext(e){const{input:{id:t},disabled:n,hasTooltip:s}=this.props,{model:a,study:r}=e;if("properties"in r||"tempProperties"in r){const e="properties"in r?r.properties().inputs[t]:r.tempProperties?.inputs.child(t);return o.createElement($.StylePropertyContainer,{model:a,affectSave:!0,property:e},o.createElement(G.BasicColorSelect,{className:u()(s&&m.hasTooltip),color:e,disabled:n}))}return null}}var Q=n(614793),X=n(350324),J=n(508550),j=n(886176),ee=n(480779);const te=(0,d.bind)((function(e){const{value:t,onChange:n,input:s,tzName:a,hasTooltip:r}=e,{id:i,name:l,defval:c}=s,p=(0,o.useMemo)((()=>Number(t??c)),[t,c]),d=(0,o.useMemo)((()=>(0,j.getChartTimezoneOffsetMs)(p,a)),[p,a]),h=(0,o.useMemo)((()=>{const e=new Date(p+d+g(p));return e.setSeconds(0),e}),[p,d]),m=(0,o.useMemo)((()=>(0,J.twoDigitsFormat)(h.getHours())+":"+(0,J.twoDigitsFormat)(h.getMinutes())),[h]);return o.createElement("div",{className:u()(ee.container,r&&ee.hasTooltip)},o.createElement("div",{className:ee.datePickerWrapper},o.createElement(Q.DatePicker,{InputComponent:X.DateInput,initial:h,onPick:function(e){if(null===e)return;const t=new Date(h);t.setFullYear(e.getFullYear()),t.setMonth(e.getMonth()),t.setDate(e.getDate()),n(v(t),i,l)},revertInvalidData:!0})),o.createElement("div",{className:ee.timePickerWrapper},o.createElement(x.TimeInput,{value:m,onChange:function(e){const[t,o]=e.split(":"),s=new Date(h);s.setHours(Number(t)),s.setMinutes(Number(o)),n(v(s),i,l)}})));function g(e){return 60*new Date(e).getTimezoneOffset()*1e3}function v(e){return e.valueOf()-d-g(p)}}));class ne extends o.PureComponent{render(){
const{input:e,disabled:t,onChange:n,tzName:s,hasTooltip:c}=this.props;if((0,a.isStudyInputOptionsInfo)(e))return o.createElement(O,{input:e,disabled:t,onChange:n,hasTooltip:c});switch(e.type){case"integer":return o.createElement(r.IntegerInput,{input:e,disabled:t,onChange:n,hasTooltip:c});case"float":case"price":return o.createElement(i.FloatInput,{input:e,disabled:t,onChange:n,hasTooltip:c});case"bool":return o.createElement(l.BoolInput,{input:e,disabled:t,onChange:n,hasTooltip:c});case"text":return o.createElement(f,{input:e,disabled:t,onChange:n,hasTooltip:c});case"symbol":return o.createElement(b.SymbolInput,{input:e,disabled:t,onChange:n,hasTooltip:c});case"session":return o.createElement(S,{input:e,disabled:t,onChange:n,hasTooltip:c});case"source":return o.createElement(V,{input:e,disabled:t,onChange:n,hasTooltip:c});case"resolution":return o.createElement(K,{input:e,disabled:t,onChange:n,hasTooltip:c});case"time":return o.createElement(te,{input:e,tzName:s,onChange:n,hasTooltip:c});case"color":return o.createElement(Y,{input:e,disabled:t,onChange:n,hasTooltip:c});default:return null}}}var oe=n(423112),se=n(756639);class ae extends o.PureComponent{render(){const{label:e,children:t,input:n,disabled:a,onChange:r,labelAlign:i,grouped:l,tooltip:c,solutionId:u,offset:p}=this.props,d=Boolean(c);return o.createElement(C.PropertyTable.Row,null,o.createElement(C.PropertyTable.Cell,{"data-study-input-name":n?.id&&`${n.id}-label`,placement:"first",verticalAlign:i,grouped:l,offset:p},void 0!==e?e:(0,D.getTranslatedInputTitle)((0,s.ensureDefined)(n).name)),o.createElement(C.PropertyTable.Cell,{"data-study-input-name":n?.id&&`${n.id}-input`,placement:"last",grouped:l},t||o.createElement(ne,{input:(0,s.ensureDefined)(n),onChange:r,disabled:a,hasTooltip:d}),d&&o.createElement(se.IconGroupWrapper,null,c&&o.createElement(oe.InputTooltip,{title:c}),!1)))}}},80831:(e,t,n)=>{"use strict";n.d(t,{InputsTabContent:()=>q});var o=n(50959),s=n(650151),a=n(609838),r=n(294859),i=n(47924),l=n(14043),c=n(230789),u=n(497754),p=n.n(u),d=n(404568),h=n(533560),m=n.n(h);const g=(0,c.makeSwitchGroupItem)((e=>{const{disabled:t,checked:n,label:s,value:a="on",name:r,onChange:i,className:l,id:c,title:p,labelPositionReverse:h,reference:g,ariaDescribedby:v,"data-name":f}=e,b=u(l,m().radio,h&&m().reverse),C=u(m().label,t&&m().disabled);return o.createElement("label",{className:b},o.createElement("span",{className:u(m().wrapper,t&&m().disabled),title:p},o.createElement("input",{type:"radio",id:c,className:m().input,name:r,checked:n,disabled:t,value:a,onChange:()=>i?.(a),ref:g,"aria-describedby":v,"data-name":f}),o.createElement("span",{className:u(m().box,n&&m().checked)},o.createElement(d.RadioButtonView,{checked:n,disabled:t}))),s&&o.createElement("span",{className:C},s))}));var v=n(375751),f=n(587513),b=n(423112),C=n(756639),y=n(897650);function x(e){const{children:t,input:l,disabled:u,onChange:p,grouped:d,tooltip:h,solutionId:m}=e,x=(0,o.useContext)(r.PropertyContext),{values:E,setValue:w}=(0,s.ensureNotNull)(x),S=E[l.id],[_,T]=(0,
o.useState)(S?"another-symbol":"main-symbol"),[N,I]=(0,o.useState)(S),k=Boolean(h);return(0,o.useEffect)((()=>{S&&I(S)}),[S]),o.createElement(c.SwitchGroup,{name:`symbol-source-${l.id}`,values:[_],onChange:function(e){T(e),"main-symbol"===e?(0,f.setter)(w)("",l.id,l.name):"another-symbol"===e&&N&&(0,f.setter)(w,p)(N,l.id,l.name)}},o.createElement(i.PropertyTable.Row,null,o.createElement(i.PropertyTable.Cell,{colSpan:2,placement:"first",grouped:d,"data-study-input-name":l?.id&&`${l.id}-main-symbol`},o.createElement(g,{value:"main-symbol",className:y.checkbox,disabled:u,label:o.createElement("span",{className:y.label},a.t(null,{context:"input"},n(594849)))}))),o.createElement(i.PropertyTable.Row,null,o.createElement(i.PropertyTable.Cell,{placement:"first",grouped:d,"data-study-input-name":l?.id&&`${l.id}-another-symbol-label`},o.createElement(g,{value:"another-symbol",className:y.checkbox,disabled:u,label:o.createElement("span",{className:y.label},a.t(null,{context:"input"},n(624716)))})),o.createElement(i.PropertyTable.Cell,{placement:"last",grouped:d,"data-study-input-name":l?.id&&`${l.id}-another-symbol-input`},t||o.createElement(v.SymbolInput,{input:(0,s.ensureDefined)(l),onChange:p,disabled:u||"main-symbol"===_,hasTooltip:k}),k&&o.createElement(C.IconGroupWrapper,null,h&&o.createElement(b.InputTooltip,{title:h}),!1))))}var E=n(562364);class w extends o.PureComponent{render(){const{label:e,input:t,tooltip:n,solutionId:s}=this.props,a=Boolean(n);return o.createElement(i.PropertyTable.Row,null,o.createElement(i.PropertyTable.Cell,{placement:"first",colSpan:2,"data-study-input-name":t?.id&&`${t.id}-checkbox`},o.createElement(E.BoolInput,{label:e,input:t,hasTooltip:a}),a&&o.createElement(C.IconGroupWrapper,null,n&&o.createElement(b.InputTooltip,{title:n}),!1)))}}var S=n(193400),_=n(558213),T=n(34735),N=n(130949);class I extends o.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{input:{id:t,name:n},onChange:o}=this.props;o(e.currentTarget.value,t,n)}}render(){const{input:{defval:e},value:t,disabled:n,onBlur:s,onKeyDown:a}=this.props;return o.createElement(_.Textarea,{className:p()(y.input,y.textarea,T.InputClasses.FontSizeMedium),value:void 0===t?e:t,onChange:this._onChange,onBlur:s,onKeyDown:a,disabled:n,maxLength:4096})}}const k=(0,N.debounced)(I),P=(0,f.bind)(k);var D=n(930584);function B(e){const{input:t,label:n,tooltip:s,solutionId:a}=e,r=Boolean(s);return o.createElement(i.PropertyTable.Row,null,o.createElement(i.PropertyTable.Cell,{placement:"first",colSpan:2,className:D.wrap,"data-study-input-name":t?.id&&`${t.id}-textarea`},o.createElement("div",{className:D.labelWrap},o.createElement("span",{className:p()(D.label,r&&D.hasTooltip)},n),r&&o.createElement(C.IconGroupWrapper,null,s&&o.createElement(b.InputTooltip,{title:s}),!1)),o.createElement(P,{input:t})))}function O(e){const{input:t,tooltip:n,solutionId:s}=e;return"symbol"===t.type&&t.optional?o.createElement(x,{input:t,tooltip:n,solutionId:s}):"bool"===t.type?o.createElement(w,{label:(0,l.getTranslatedInputTitle)(t.name),input:t,
tooltip:n,solutionId:s}):"text_area"===t.type?o.createElement(B,{label:(0,l.getTranslatedInputTitle)(t.name),input:t,tooltip:n,solutionId:s}):o.createElement(S.InputRow,{labelAlign:function(e){switch(e){case"session":return"adaptive";case"time":return"topCenter";default:return}}(t.type),input:t,tooltip:n,solutionId:s})}var L=n(852830),M=n(248286);function R(e){const{content:t}=e;let n;return o.createElement(i.PropertyTable.InlineRowContext.Provider,{value:!0},o.createElement("div",{className:M.inlineRow},t.children.map(((e,s)=>(void 0!==e.tooltip&&(n=e.tooltip),o.createElement(O,{key:e.id,input:e,tooltip:s===t.children.length-1?n:void 0}))))))}var V=n(457540),A=n(727246);function F(e){const{content:t}=e;return(0,V.isGroup)(t)?(0,V.isInputInlines)(t)?o.createElement(R,{content:t}):o.createElement(o.Fragment,null,o.createElement("div",{className:A.titleWrap},o.createElement(L.GroupTitleSection,{title:(0,l.getTranslatedInputTitle)(t.id),name:t.id})),t.children.map((e=>(0,V.isGroup)(e)?o.createElement(R,{key:e.id,content:e}):o.createElement(O,{key:e.id,input:e,tooltip:e.tooltip,solutionId:e.solutionId}))),o.createElement("div",{className:A.groupFooter})):o.createElement(O,{input:t,tooltip:t.tooltip,solutionId:t.solutionId})}const W={offset:a.t(null,void 0,n(855450))};class q extends o.PureComponent{render(){const{reference:e,inputs:t,property:n,study:a,studyMetaInfo:r,model:l,onStudyInputChange:c,className:u}=this.props,{offset:p,offsets:d}=n;return o.createElement(i.PropertyTable,{reference:e,className:u},o.createElement(Z,{study:a,model:l,property:n.inputs,inputs:t,onStudyInputChange:c}),p&&this._createOffsetSection(p,(0,s.ensureDefined)(r.offset)),d&&d.childNames().map((e=>{const t=d.childs()[e];return this._createOffsetSection(t,(0,s.ensureDefined)(r.offsets?.[e]))})))}_createOffsetSection(e,t){const n=e.childs();return o.createElement(Z,{key:`offset_${t.title}`,study:this.props.study,model:this.props.model,inputs:[U(n,t)],property:e})}}const z=e=>!1;function Z(e){const{study:t,model:n,inputs:s,property:a,onStudyInputChange:i}=e,l=z(t)?s.filter(H):s,c=(0,o.useMemo)((()=>(0,V.getInputGroups)(l)),[l]);return o.createElement(r.PropertyContainer,{property:a,study:t,model:n,onStudyInputChange:i},!1,!1,c.map((e=>o.createElement(o.Fragment,{key:e.id},o.createElement(F,{content:e}),!1))))}function H(e){return!defaultCustomSessionIds.has(e.id)&&!vbpAnchorPeriodInputsIds.has(e.id)&&!tpoCustomRowSizeIds.has(e.id)}function U(e,t){return{id:"val",name:t.title||W.offset,defval:e.val.value(),type:"integer",min:t.min,max:t.max}}},562364:(e,t,n)=>{"use strict";n.d(t,{BoolInput:()=>u,BoolInputComponent:()=>c});var o=n(50959),s=n(302946),a=n(497754),r=n.n(a),i=n(587513),l=n(897650);class c extends o.PureComponent{constructor(){super(...arguments),this._onChange=()=>{const{input:{id:e,name:t},value:n,onChange:o}=this.props;o(!n,e,t)}}render(){const{input:{defval:e},value:t,disabled:n,label:a,hasTooltip:i}=this.props,c=void 0===t?e:t;return o.createElement(s.Checkbox,{className:r()(l.checkbox,i&&l.hasTooltip),disabled:n,checked:c,
onChange:this._onChange,label:o.createElement("span",{className:l.label},a),labelAlignBaseline:!0})}}const u=(0,i.bind)(c)},130949:(e,t,n)=>{"use strict";n.d(t,{debounced:()=>a});var o=n(50959);const s={blur:0,commit:0,change:1/0};function a(e,t=s){return class extends o.PureComponent{constructor(e){super(e),this._onChange=(e,n,o)=>{const s=t.change;s?(clearTimeout(this._timeout),this.setState({value:e},(()=>{s!==1/0&&(this._timeout=setTimeout((()=>this._flush()),s))}))):this._flush(e)},this._onBlur=()=>{this._debounce(t.blur);const{onBlur:e}=this.props;e&&e()},this._onKeyDown=e=>{13===e.keyCode&&this._debounce(t.commit)},this.state={prevValue:e.value,value:e.value}}componentWillUnmount(){this._flush()}render(){const{value:t}=this.state;return o.createElement(e,{...this.props,value:t,onChange:this._onChange,onBlur:this._onBlur,onKeyDown:this._onKeyDown})}static getDerivedStateFromProps(e,t){return e.value===t.prevValue?t:{prevValue:e.value,value:e.value}}_debounce(e){e?(clearTimeout(this._timeout),e!==1/0&&(this._timeout=setTimeout((()=>this._flush()),e))):this.setState((e=>{this._flush(e.value)}))}_flush(e){const{input:{id:t,name:n},onChange:o}=this.props,{prevValue:s,value:a}=this.state;clearTimeout(this._timeout);const r=void 0!==e?e:a;void 0!==r&&r!==s&&o(r,t,n)}}}},802527:(e,t,n)=>{"use strict";n.d(t,{FloatInput:()=>d,FloatInputComponent:()=>p});var o=n(50959),s=n(497754),a=n.n(s),r=n(275277),i=n(587513),l=n(130949),c=n(897650);class u extends o.PureComponent{render(){const{hasTooltip:e}=this.props;return o.createElement(r.NumericInput,{...this.props,className:a()(c.input,e&&c.hasTooltip),stretch:!1})}}const p=(0,l.debounced)(u,{change:1/0,commit:0,blur:0}),d=(0,i.bind)(p)},517651:(e,t,n)=>{"use strict";n.d(t,{IntegerInput:()=>d,IntegerInputComponent:()=>p});var o=n(50959),s=n(497754),a=n.n(s),r=n(587513),i=n(130949),l=n(275277),c=n(897650);class u extends o.PureComponent{render(){const{hasTooltip:e}=this.props;return o.createElement(l.NumericInput,{...this.props,mode:"integer",className:a()(c.input,e&&c.hasTooltip),stretch:!1})}}const p=(0,i.debounced)(u,{change:1/0,commit:0,blur:0}),d=(0,r.bind)(p)},275277:(e,t,n)=>{"use strict";n.d(t,{NumericInput:()=>r});var o=n(50959),s=n(650151),a=n(587125);class r extends o.PureComponent{constructor(){super(...arguments),this._container=null,this._handleContainerRef=e=>this._container=e,this._onChange=(e,t)=>{const{input:{id:n,name:o},onChange:s,onBlur:a}=this.props;s(e,n,o),"step"===t&&a&&a()},this._onBlur=e=>{const{onBlur:t}=this.props;if(t){const n=(0,s.ensureNotNull)(this._container);n.contains(document.activeElement)||n.contains(e.relatedTarget)||t()}}}render(){const{input:{defval:e,min:t,max:n,step:s},value:r,disabled:i,onKeyDown:l,className:c,mode:u,stretch:p}=this.props;return o.createElement(a.NumberInput,{className:c,value:Number(void 0===r?e:r),min:t,max:n,step:s,mode:u,onBlur:this._onBlur,onValueChange:this._onChange,onKeyDown:l,disabled:i,containerReference:this._handleContainerRef,fontSizeStyle:"medium",roundByStep:!1,stretch:p})}}},375751:(e,t,n)=>{
"use strict";n.d(t,{SymbolInput:()=>p,getInternalSymbolName:()=>c});var o=n(50959),s=n(650151),a=n(294859),r=n(587513),i=n(39290),l=n(193315);function c(e,t){const n=(0,i.createAdapter)(t).resolvedSymbolInfoBySymbol(e);return n&&(n.ticker||n.full_name)?n.ticker||n.full_name:e}function u(e,t){const n=(0,i.createAdapter)(t).resolvedSymbolInfoBySymbol(e);return null===n?e:n.name}const p=(0,r.bind)((function(e){const t=(0,o.useContext)(a.PropertyContext),{study:n}=(0,s.ensureNotNull)(t),{input:{defval:r},value:i}=e;return o.createElement(l.SymbolInputsButton,{...e,value:u(i||r||"",n),study:n})}))},982021:(e,t,n)=>{"use strict";n.d(t,{BasicColorSelect:()=>a});var o=n(50959),s=n(642091);function a(e){return o.createElement(s.ColorWithLinePropertySelect,{...e})}},642091:(e,t,n)=>{"use strict";n.d(t,{ColorWithLinePropertySelect:()=>b});var o=n(50959),s=n(724377),a=n(609838),r=n(272047),i=n(589637),l=n(527053),c=n(782302),u=n(494182),p=n(32133),d=n(377707);const h=new r.TranslatedString("change thickness",a.t(null,void 0,n(773281))),m=new r.TranslatedString("change color",a.t(null,void 0,n(731675))),g=new r.TranslatedString("change opacity",a.t(null,void 0,n(629426))),v=new r.TranslatedString("change line style",a.t(null,void 0,n(728818))),f=[1,2,3,4];class b extends o.PureComponent{constructor(){super(...arguments),this._trackEventLabel=null,this._getTransparencyValue=()=>{const{transparency:e}=this.props;return e?e.value():0},this._getOpacityValue=()=>{const{color:e}=this.props,t=(0,u.getPropertyValue)(e);if(t)return(0,i.isHexColor)(t)?(0,i.transparencyToAlpha)(this._getTransparencyValue()):(0,s.parseRgba)(t)[3]},this._getColorValueInHex=()=>{const{color:e}=this.props,t=(0,u.getPropertyValue)(e);return t?(0,i.isHexColor)(t)?t:(0,s.rgbToHexString)((0,s.parseRgb)(t)):null},this._onThicknessChange=e=>{const{thickness:t}=this.props;void 0!==t&&this._setProperty(t,e,h)},this._onLineStyleChange=e=>{const{lineStyle:t}=this.props;void 0!==t&&this._setProperty(t,e,v)},this._onColorChange=e=>{const{color:t,isPaletteColor:n}=this.props,o=(0,u.getPropertyValue)(t);let a=0;o&&(a=(0,i.isHexColor)(o)?this._getTransparencyValue():(0,i.alphaToTransparency)((0,s.parseRgba)(o)[3])),this._setProperty(t,(0,i.generateColor)(String(e),a,!0),m),this._trackEventLabel="Plot color > "+(n?"Palette":"Single")},this._onOpacityChange=e=>{const{color:t}=this.props,n=(0,u.getPropertyValue)(t);this._setProperty(t,(0,i.generateColor)(n,(0,i.alphaToTransparency)(e),!0),g)},this._onPopupClose=()=>{this._trackEventLabel&&((0,p.trackEvent)("GUI","Study settings",this._trackEventLabel),this._trackEventLabel=null)}}componentWillUnmount(){this._onPopupClose()}render(){const{selectOpacity:e=!0,disabled:t,className:n}=this.props;return o.createElement(c.ColorSelect,{className:n,disabled:t,color:this._getColorValueInHex(),selectOpacity:e,opacity:this._getOpacityValue(),thickness:this._getThicknessValue(),lineStyle:this._getLineStyleValue(),thicknessItems:f,lineStyleItems:d.defaultLineStyleItems,onColorChange:this._onColorChange,onOpacityChange:this._onOpacityChange,
onThicknessChange:this._onThicknessChange,onLineStyleChange:this._onLineStyleChange,onPopupClose:this._onPopupClose})}_getThicknessValue(){const{thickness:e}=this.props;return e?(0,u.getPropertyValue)(e):void 0}_getLineStyleValue(){const{lineStyle:e}=this.props;return e?(0,u.getPropertyValue)(e):void 0}_setProperty(e,t,n){const{setValue:o}=this.context;o(e,t,n)}}b.contextType=l.StylePropertyContext},47924:(e,t,n)=>{"use strict";n.d(t,{PropertyTable:()=>l});var o=n(50959),s=n(497754),a=n(800417),r=n(785027);const i=o.createContext(!1);class l extends o.PureComponent{render(){return o.createElement("div",{ref:this.props.reference,className:s(r.content,this.props.className)},this.props.children)}}var c,u,p;l.InlineRowContext=i,l.Row=function(e){const{children:t}=e;return(0,o.useContext)(i)?o.createElement("span",{className:r.inlineRow},t):o.createElement(o.Fragment,null,t)},function(e){e.Top="top",e.TopCenter="topCenter",e.Middle="middle",e.Adaptive="adaptive"}(c||(c={})),function(e){e.First="first",e.Last="last"}(u||(u={})),l.Cell=function(e){const t=(0,o.useContext)(i),n=s(r.cell,e.offset&&r.offset,e.grouped&&r.grouped,t&&r.inlineCell,"top"===e.verticalAlign&&r.top,"topCenter"===e.verticalAlign&&r.topCenter,"adaptive"===e.verticalAlign&&r.adaptive,e.checkableTitle&&r.checkableTitle,2===e.colSpan&&r.fill,"first"===e.placement&&2!==e.colSpan&&r.first,"last"===e.placement&&2!==e.colSpan&&r.last),l=(0,a.filterDataProps)(e);return o.createElement("div",{...l,className:n},o.createElement("div",{className:s(r.inner,e.className)},e.children))},l.Separator=function(e){return o.createElement(l.Row,null,o.createElement("div",{className:s(r.cell,r.separator,r.fill)}))},function(e){e[e.Small=0]="Small",e[e.Big=1]="Big"}(p||(p={})),l.GroupSeparator=function(e){const t=e.size||0;return o.createElement(l.Row,null,o.createElement("div",{className:s(r.cell,r.groupSeparator,r.fill,1===t&&r.big)}))}},494182:(e,t,n)=>{"use strict";function o(e){return Array.isArray(e)?e[0].value():e.value()}n.d(t,{getPropertyValue:()=>o})},610600:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M11.44 3.34a1.5 1.5 0 0 1 2.12 0l1.09 1.09a1.5 1.5 0 0 1 0 2.12l-7 7a1.5 1.5 0 0 1-1.06.44H4V11.4c0-.4.16-.78.44-1.06l7-7Zm1.41.7a.5.5 0 0 0-.7 0l-.7.7 1.8 1.79.69-.7a.5.5 0 0 0 0-.7l-1.09-1.08Zm-.3 3.2-1.8-1.8-5.6 5.6a.5.5 0 0 0-.15.36v1.59h1.59a.5.5 0 0 0 .35-.15l5.6-5.6Z"/></svg>'},499084:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16Zm4-9.97L11.9 6 8.3 9.82 6.1 7.46 4.99 8.5 8.32 12 13 7.03Z"/></svg>'},530162:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16ZM9 4c-.79 0-1.38.7-1.25 1.48l.67 4.03a.59.59 0 0 0 1.16 0l.67-4.03A1.27 1.27 0 0 0 9 4Zm0 8a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"/></svg>'},527941:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16Zm1-12a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM8.5 9.5H7V8h3v6H8.5V9.5Z"/></svg>'},482353:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16Zm-1-4a1 1 0 1 0 2 0 1 1 0 0 0-2 0Zm2.83-3.52c-.49.43-.97.85-1.06 1.52H8.26c.08-1.18.74-1.69 1.32-2.13.49-.38.92-.71.92-1.37C10.5 6.67 9.82 6 9 6s-1.5.67-1.5 1.5V8H6v-.5a3 3 0 1 1 6 0c0 .96-.6 1.48-1.17 1.98Z"/></svg>'},123851:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z"/></svg>'},357740:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M3 13h2v2H3v-2Zm5 0h2v2H8v-2Zm7 0h-2v2h2v-2Zm3 0h2v2h-2v-2Zm7 0h-2v2h2v-2Z"/></svg>'},200501:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4 13.5h20"/></svg>'},755883:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});const o=function(){}}}]);