"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[69022],{87593:(t,e,r)=>{r.d(e,{default:()=>i});var n=r(557699);const a=function(){this.__data__=new n.default,this.size=0};const o=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r};const u=function(t){return this.__data__.get(t)};const c=function(t){return this.__data__.has(t)};var f=r(19385),d=r(441083);const l=function(t,e){var r=this.__data__;if(r instanceof n.default){var a=r.__data__;if(!f.default||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new d.default(a)}return r.set(t,e),this.size=r.size,this};function s(t){var e=this.__data__=new n.default(t);this.size=e.size}s.prototype.clear=a,s.prototype.delete=o,s.prototype.get=u,s.prototype.has=c,s.prototype.set=l;const i=s},216299:(t,e,r)=>{r.d(e,{default:()=>n});const n=r(799615).default.Uint8Array},837390:(t,e,r)=>{r.d(e,{default:()=>o});var n=r(352918),a=r(799615);const o=(0,n.default)(a.default,"WeakMap")},514131:(t,e,r)=>{r.d(e,{default:()=>n});const n=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n&&!1!==e(t[r],r,t););return t}},860545:(t,e,r)=>{r.d(e,{default:()=>l});const n=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n};var a=r(154404),o=r(456052),u=r(232437),c=r(399313),f=r(109125),d=Object.prototype.hasOwnProperty;const l=function(t,e){var r=(0,o.default)(t),l=!r&&(0,a.default)(t),s=!r&&!l&&(0,u.default)(t),i=!r&&!l&&!s&&(0,f.default)(t),b=r||l||s||i,p=b?n(t.length,String):[],v=p.length;for(var j in t)!e&&!d.call(t,j)||b&&("length"==j||s&&("offset"==j||"parent"==j)||i&&("buffer"==j||"byteLength"==j||"byteOffset"==j)||(0,c.default)(j,v))||p.push(j);return p}},218573:(t,e,r)=>{r.d(e,{default:()=>n});const n=function(t,e){for(var r=-1,n=e.length,a=t.length;++r<n;)t[a+r]=e[r];return t}},161572:(t,e,r)=>{r.d(e,{default:()=>u});var n=r(600857),a=r(554523),o=Object.prototype.hasOwnProperty;const u=function(t,e,r){var u=t[e];o.call(t,e)&&(0,a.default)(u,r)&&(void 0!==r||e in t)||(0,n.default)(t,e,r)}},600857:(t,e,r)=>{r.d(e,{default:()=>a});var n=r(355136);const a=function(t,e,r){"__proto__"==e&&n.default?(0,n.default)(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},732016:(t,e,r)=>{r.d(e,{default:()=>H});var n=r(87593),a=r(514131),o=r(161572),u=r(652949),c=r(377251);const f=function(t,e){return t&&(0,u.default)(e,(0,c.default)(e),t)};var d=r(602960);const l=function(t,e){return t&&(0,u.default)(e,(0,d.default)(e),t)};var s=r(414054),i=r(232126),b=r(21578);const p=function(t,e){return(0,u.default)(t,(0,b.default)(t),e)};var v=r(521320);const j=function(t,e){return(0,u.default)(t,(0,v.default)(t),e)};var y=r(438366),h=r(596842),w=r(726617),A=Object.prototype.hasOwnProperty;const g=function(t){var e=t.length,r=new t.constructor(e);return e&&"string"==typeof t[0]&&A.call(t,"index")&&(r.index=t.index,r.input=t.input),r};var O=r(797990);const _=function(t,e){var r=e?(0,O.default)(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)};var m=/\w*$/;const x=function(t){
var e=new t.constructor(t.source,m.exec(t));return e.lastIndex=t.lastIndex,e};var S=r(366711),U=S.default?S.default.prototype:void 0,I=U?U.valueOf:void 0;const P=function(t){return I?Object(I.call(t)):{}};var B=r(411523);const M=function(t,e,r){var n=t.constructor;switch(e){case"[object ArrayBuffer]":return(0,O.default)(t);case"[object Boolean]":case"[object Date]":return new n(+t);case"[object DataView]":return _(t,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return(0,B.default)(t,r);case"[object Map]":case"[object Set]":return new n;case"[object Number]":case"[object String]":return new n(t);case"[object RegExp]":return x(t);case"[object Symbol]":return P(t)}};var k=r(865607),z=r(456052),E=r(232437),F=r(343888),D=r(182433),T=r(513795);const C=function(t){return(0,T.default)(t)&&"[object Set]"==(0,w.default)(t)};var V=r(805467),N=r(292350),W=N.default&&N.default.isSet;const L=W?(0,V.default)(W):C;var R="[object Arguments]",q="[object Function]",$="[object Object]",G={};G[R]=G["[object Array]"]=G["[object ArrayBuffer]"]=G["[object DataView]"]=G["[object Boolean]"]=G["[object Date]"]=G["[object Float32Array]"]=G["[object Float64Array]"]=G["[object Int8Array]"]=G["[object Int16Array]"]=G["[object Int32Array]"]=G["[object Map]"]=G["[object Number]"]=G[$]=G["[object RegExp]"]=G["[object Set]"]=G["[object String]"]=G["[object Symbol]"]=G["[object Uint8Array]"]=G["[object Uint8ClampedArray]"]=G["[object Uint16Array]"]=G["[object Uint32Array]"]=!0,G["[object Error]"]=G[q]=G["[object WeakMap]"]=!1;const H=function t(e,r,u,b,v,A){var O,_=1&r,m=2&r,x=4&r;if(u&&(O=v?u(e,b,v,A):u(e)),void 0!==O)return O;if(!(0,D.default)(e))return e;var S=(0,z.default)(e);if(S){if(O=g(e),!_)return(0,i.default)(e,O)}else{var U=(0,w.default)(e),I=U==q||"[object GeneratorFunction]"==U;if((0,E.default)(e))return(0,s.default)(e,_);if(U==$||U==R||I&&!v){if(O=m||I?{}:(0,k.default)(e),!_)return m?j(e,l(O,e)):p(e,f(O,e))}else{if(!G[U])return v?e:{};O=M(e,U,_)}}A||(A=new n.default);var P=A.get(e);if(P)return P;A.set(e,O),L(e)?e.forEach((function(n){O.add(t(n,r,u,n,e,A))})):(0,F.default)(e)&&e.forEach((function(n,a){O.set(a,t(n,r,u,a,e,A))}));var B=x?m?h.default:y.default:m?d.default:c.default,T=S?void 0:B(e);return(0,a.default)(T||e,(function(n,a){T&&(n=e[a=n]),(0,o.default)(O,a,t(n,r,u,a,e,A))})),O}},456511:(t,e,r)=>{r.d(e,{default:()=>o});var n=r(182433),a=Object.create;const o=function(){function t(){}return function(e){if(!(0,n.default)(e))return{};if(a)return a(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}()},296909:(t,e,r)=>{r.d(e,{default:()=>o});var n=r(218573),a=r(456052);const o=function(t,e,r){var o=e(t);return(0,a.default)(t)?o:(0,n.default)(o,r(t))}},589815:(t,e,r)=>{r.d(e,{default:()=>u});var n=r(5196);const a=(0,r(245635).default)(Object.keys,Object);var o=Object.prototype.hasOwnProperty;const u=function(t){if(!(0,
n.default)(t))return a(t);var e=[];for(var r in Object(t))o.call(t,r)&&"constructor"!=r&&e.push(r);return e}},805467:(t,e,r)=>{r.d(e,{default:()=>n});const n=function(t){return function(e){return t(e)}}},797990:(t,e,r)=>{r.d(e,{default:()=>a});var n=r(216299);const a=function(t){var e=new t.constructor(t.byteLength);return new n.default(e).set(new n.default(t)),e}},414054:(t,e,r)=>{r.d(e,{default:()=>f});var n=r(799615),a="object"==typeof exports&&exports&&!exports.nodeType&&exports,o=a&&"object"==typeof module&&module&&!module.nodeType&&module,u=o&&o.exports===a?n.default.Buffer:void 0,c=u?u.allocUnsafe:void 0;const f=function(t,e){if(e)return t.slice();var r=t.length,n=c?c(r):new t.constructor(r);return t.copy(n),n}},411523:(t,e,r)=>{r.d(e,{default:()=>a});var n=r(797990);const a=function(t,e){var r=e?(0,n.default)(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}},232126:(t,e,r)=>{r.d(e,{default:()=>n});const n=function(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}},652949:(t,e,r)=>{r.d(e,{default:()=>o});var n=r(161572),a=r(600857);const o=function(t,e,r,o){var u=!r;r||(r={});for(var c=-1,f=e.length;++c<f;){var d=e[c],l=o?o(r[d],t[d],d,r,t):void 0;void 0===l&&(l=t[d]),u?(0,a.default)(r,d,l):(0,n.default)(r,d,l)}return r}},355136:(t,e,r)=>{r.d(e,{default:()=>a});var n=r(352918);const a=function(){try{var t=(0,n.default)(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},438366:(t,e,r)=>{r.d(e,{default:()=>u});var n=r(296909),a=r(21578),o=r(377251);const u=function(t){return(0,n.default)(t,o.default,a.default)}},596842:(t,e,r)=>{r.d(e,{default:()=>u});var n=r(296909),a=r(521320),o=r(602960);const u=function(t){return(0,n.default)(t,o.default,a.default)}},110964:(t,e,r)=>{r.d(e,{default:()=>n});const n=(0,r(245635).default)(Object.getPrototypeOf,Object)},21578:(t,e,r)=>{r.d(e,{default:()=>c});const n=function(t,e){for(var r=-1,n=null==t?0:t.length,a=0,o=[];++r<n;){var u=t[r];e(u,r,t)&&(o[a++]=u)}return o};var a=r(469043),o=Object.prototype.propertyIsEnumerable,u=Object.getOwnPropertySymbols;const c=u?function(t){return null==t?[]:(t=Object(t),n(u(t),(function(e){return o.call(t,e)})))}:a.default},521320:(t,e,r)=>{r.d(e,{default:()=>c});var n=r(218573),a=r(110964),o=r(21578),u=r(469043);const c=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)(0,n.default)(e,(0,o.default)(t)),t=(0,a.default)(t);return e}:u.default},726617:(t,e,r)=>{r.d(e,{default:()=>_});var n=r(352918),a=r(799615);const o=(0,n.default)(a.default,"DataView");var u=r(19385);const c=(0,n.default)(a.default,"Promise");const f=(0,n.default)(a.default,"Set");var d=r(837390),l=r(194138),s=r(265114),i="[object Map]",b="[object Promise]",p="[object Set]",v="[object WeakMap]",j="[object DataView]",y=(0,s.default)(o),h=(0,s.default)(u.default),w=(0,s.default)(c),A=(0,s.default)(f),g=(0,s.default)(d.default),O=l.default;(o&&O(new o(new ArrayBuffer(1)))!=j||u.default&&O(new u.default)!=i||c&&O(c.resolve())!=b||f&&O(new f)!=p||d.default&&O(new d.default)!=v)&&(O=function(t){var e=(0,
l.default)(t),r="[object Object]"==e?t.constructor:void 0,n=r?(0,s.default)(r):"";if(n)switch(n){case y:return j;case h:return i;case w:return b;case A:return p;case g:return v}return e});const _=O},865607:(t,e,r)=>{r.d(e,{default:()=>u});var n=r(456511),a=r(110964),o=r(5196);const u=function(t){return"function"!=typeof t.constructor||(0,o.default)(t)?{}:(0,n.default)((0,a.default)(t))}},399313:(t,e,r)=>{r.d(e,{default:()=>a});var n=/^(?:0|[1-9]\d*)$/;const a=function(t,e){var r=typeof t;return!!(e=e??9007199254740991)&&("number"==r||"symbol"!=r&&n.test(t))&&t>-1&&t%1==0&&t<e}},5196:(t,e,r)=>{r.d(e,{default:()=>a});var n=Object.prototype;const a=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||n)}},292350:(t,e,r)=>{r.d(e,{default:()=>c});var n=r(97889),a="object"==typeof exports&&exports&&!exports.nodeType&&exports,o=a&&"object"==typeof module&&module&&!module.nodeType&&module,u=o&&o.exports===a&&n.default.process;const c=function(){try{var t=o&&o.require&&o.require("util").types;return t||u&&u.binding&&u.binding("util")}catch(t){}}()},245635:(t,e,r)=>{r.d(e,{default:()=>n});const n=function(t,e){return function(r){return t(e(r))}}},154834:(t,e,r)=>{r.d(e,{default:()=>a});var n=r(732016);const a=function(t){return(0,n.default)(t,5)}},154404:(t,e,r)=>{r.d(e,{default:()=>d});var n=r(194138),a=r(513795);const o=function(t){return(0,a.default)(t)&&"[object Arguments]"==(0,n.default)(t)};var u=Object.prototype,c=u.hasOwnProperty,f=u.propertyIsEnumerable;const d=o(function(){return arguments}())?o:function(t){return(0,a.default)(t)&&c.call(t,"callee")&&!f.call(t,"callee")}},456052:(t,e,r)=>{r.d(e,{default:()=>n});const n=Array.isArray},649634:(t,e,r)=>{r.d(e,{default:()=>o});var n=r(688987),a=r(965743);const o=function(t){return null!=t&&(0,a.default)(t.length)&&!(0,n.default)(t)}},232437:(t,e,r)=>{r.d(e,{default:()=>f});var n=r(799615);const a=function(){return!1};var o="object"==typeof exports&&exports&&!exports.nodeType&&exports,u=o&&"object"==typeof module&&module&&!module.nodeType&&module,c=u&&u.exports===o?n.default.Buffer:void 0;const f=(c?c.isBuffer:void 0)||a},965743:(t,e,r)=>{r.d(e,{default:()=>n});const n=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},343888:(t,e,r)=>{r.d(e,{default:()=>d});var n=r(726617),a=r(513795);const o=function(t){return(0,a.default)(t)&&"[object Map]"==(0,n.default)(t)};var u=r(805467),c=r(292350),f=c.default&&c.default.isMap;const d=f?(0,u.default)(f):o},569708:(t,e,r)=>{r.d(e,{default:()=>o});var n=r(194138),a=r(513795);const o=function(t){return"number"==typeof t||(0,a.default)(t)&&"[object Number]"==(0,n.default)(t)}},109125:(t,e,r)=>{r.d(e,{default:()=>s});var n=r(194138),a=r(965743),o=r(513795),u={};u["[object Float32Array]"]=u["[object Float64Array]"]=u["[object Int8Array]"]=u["[object Int16Array]"]=u["[object Int32Array]"]=u["[object Uint8Array]"]=u["[object Uint8ClampedArray]"]=u["[object Uint16Array]"]=u["[object Uint32Array]"]=!0,
u["[object Arguments]"]=u["[object Array]"]=u["[object ArrayBuffer]"]=u["[object Boolean]"]=u["[object DataView]"]=u["[object Date]"]=u["[object Error]"]=u["[object Function]"]=u["[object Map]"]=u["[object Number]"]=u["[object Object]"]=u["[object RegExp]"]=u["[object Set]"]=u["[object String]"]=u["[object WeakMap]"]=!1;const c=function(t){return(0,o.default)(t)&&(0,a.default)(t.length)&&!!u[(0,n.default)(t)]};var f=r(805467),d=r(292350),l=d.default&&d.default.isTypedArray;const s=l?(0,f.default)(l):c},377251:(t,e,r)=>{r.d(e,{default:()=>u});var n=r(860545),a=r(589815),o=r(649634);const u=function(t){return(0,o.default)(t)?(0,n.default)(t):(0,a.default)(t)}},602960:(t,e,r)=>{r.d(e,{default:()=>l});var n=r(860545),a=r(182433),o=r(5196);const u=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e};var c=Object.prototype.hasOwnProperty;const f=function(t){if(!(0,a.default)(t))return u(t);var e=(0,o.default)(t),r=[];for(var n in t)("constructor"!=n||!e&&c.call(t,n))&&r.push(n);return r};var d=r(649634);const l=function(t){return(0,d.default)(t)?(0,n.default)(t,!0):f(t)}},469043:(t,e,r)=>{r.d(e,{default:()=>n});const n=function(){return[]}}}]);