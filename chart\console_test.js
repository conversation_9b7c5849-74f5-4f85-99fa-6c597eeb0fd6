// Copy and paste this into the browser console to test the indicator sources functionality

console.log('🧪 Testing Indicator Sources Functionality...');

// Step 1: Check initial state
console.log('\n📋 Step 1: Initial Sources');
console.log('Basic sources:', window.sourcesConfig._sources);
console.log('Current sources:', window.sourcesConfig.sources);
console.log('Tracked indicators:', window.sourcesConfig._indicatorSources.size);

// Step 2: Add RSI indicator
console.log('\n📋 Step 2: Adding RSI Indicator');
try {
    const rsiId = window.addRSI(14, false);
    console.log('✅ RSI added successfully with ID:', rsiId);
} catch (error) {
    console.error('❌ Failed to add RSI:', error);
}

// Step 3: Check sources after RSI (wait 2 seconds)
setTimeout(() => {
    console.log('\n📋 Step 3: Sources After RSI');
    console.log('Current sources:', window.sourcesConfig.sources);
    console.log('Tracked indicators:', window.sourcesConfig._indicatorSources.size);
    
    const hasRSI = window.sourcesConfig.sources.some(s => s.toLowerCase().includes('rsi'));
    console.log(hasRSI ? '✅ RSI found in sources!' : '⚠️ RSI not yet in sources');
    
    // Step 4: Add MACD indicator
    console.log('\n📋 Step 4: Adding MACD Indicator');
    try {
        const macdId = window.addMACD(12, 26, 9, false);
        console.log('✅ MACD added successfully with ID:', macdId);
    } catch (error) {
        console.error('❌ Failed to add MACD:', error);
    }
    
    // Step 5: Final check (wait another 2 seconds)
    setTimeout(() => {
        console.log('\n📋 Step 5: Final Sources Check');
        console.log('All sources:', window.sourcesConfig.sources);
        console.log('Tracked indicators:', Array.from(window.sourcesConfig._indicatorSources.keys()));
        
        const hasMACD = window.sourcesConfig.sources.some(s => s.toLowerCase().includes('macd'));
        console.log(hasMACD ? '✅ MACD found in sources!' : '⚠️ MACD not yet in sources');
        
        console.log('\n🎯 Test Summary:');
        console.log('- Total indicators tracked:', window.sourcesConfig._indicatorSources.size);
        console.log('- Total sources available:', window.sourcesConfig.sources.length);
        console.log('- Indicator sources working:', window.sourcesConfig._indicatorSources.size > 0 ? '✅ YES' : '❌ NO');
        
        console.log('\n💡 Next: Try clicking "Add Flexible SMA" and check its source dropdown!');
    }, 2000);
    
}, 2000);
