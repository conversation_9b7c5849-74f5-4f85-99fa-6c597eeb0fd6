(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6780],{29521:e=>{e.exports={scrollable:"scrollable-Ycj0dUGE",tabs:"tabs-Ycj0dUGE"}},695327:e=>{e.exports={titleWrap:"titleWrap-e3jFxbHm",ellipsis:"ellipsis-e3jFxbHm",hideInput:"hideInput-e3jFxbHm",hideText:"hideText-e3jFxbHm",empty:"empty-e3jFxbHm",hideEmpty:"hideEmpty-e3jFxbHm",editIcon:"editIcon-e3jFxbHm"}},895747:e=>{e.exports={tabs:"tabs-xNPrJ8dY"}},284145:(e,t,i)=>{"use strict";i.r(t),i.d(t,{SourcePropertiesEditorRenderer:()=>M});var s=i(50959),o=i(559410),n=i(650151),a=i(601227),r=i(870122),l=i.n(r),c=i(609838),d=i(976669),h=i(370981),u=i(944316),_=i(380865),p=i(742554),m=i(209039),g=i(128492),b=i(278906),C=i(654910),v=i(889868),T=i(979910),f=i(930052),S=i(272047),P=i(839246),y=i(677090),I=i(272675),D=i(440891),E=i(29521);const R=new S.TranslatedString("change {sourceTitle} title to {newSourceTitle}",c.t(null,void 0,i(923687)));class w extends s.PureComponent{constructor(e){super(e),this._activePageRef=s.createRef(),this._handleChangeMode=e=>{this.setState({isRenaming:e})},this._getTranslatedStringForSource=e=>new S.TranslatedString(e.name(),e.title(m.TitleDisplayTarget.StatusLine)),this._setTitle=e=>{const{source:t,model:i}=this.props,s=R.format({sourceTitle:t.properties().title.value()||this._getTranslatedStringForSource(t),newSourceTitle:e});i.setProperty(t.properties().title,e,s,T.lineToolsDoNotAffectChartInvalidation&&(0,g.isLineTool)(t))},this._getActionPageById=e=>{if(!e)return;const{pages:t}=this.props;return t.find((t=>t.id.toLowerCase()===e.toLowerCase()))},this._onChangeActivePageDefinitions=()=>{this.setState({tableKey:Date.now()},(()=>{this._requestResize&&this._requestResize()}))},this._handleResetToDefaults=()=>{const{source:e,model:t}=this.props;(0,b.isStudy)(e)&&t.restorePropertiesForSource(e)},this._handleSaveAsDefaults=()=>{const{source:e}=this.props;(0,b.isStudy)(e)&&e.properties().saveDefaults()},this._renderFooterLeft=()=>{const{source:e,model:t}=this.props;return(0,g.isLineTool)(e)?s.createElement(u.FooterMenu,{sources:[e],chartUndoModel:t}):s.createElement(f.MatchMedia,{rule:"(max-width: 440px)"},(t=>(0,b.isStudy)(e)&&s.createElement(y.PropertyActions,{saveAsDefaults:this._handleSaveAsDefaults,resetToDefaults:this._handleResetToDefaults,mode:t?"compact":"normal"})))},this._subscribe=e=>{e&&e.definitions.subscribe(this._onChangeActivePageDefinitions)},this._unsubscribe=e=>{e&&e.definitions.unsubscribe(this._onChangeActivePageDefinitions)},this._getActiveTabSettingsName=()=>{const{source:e}=this.props;return e instanceof C.Series?"properties_dialog.active_tab.chart":e instanceof v.LineDataSource?"properties_dialog.active_tab.drawing":(0,b.isStudy)(e)?"properties_dialog.active_tab.study":""},this._handleSelectPage=e=>{const{activePageId:t}=this.state,i=this._getActionPageById(t),s=this._getActionPageById(e),o=this._getActiveTabSettingsName();t!==e&&(this._unsubscribe(i),o&&l().setValue(o,e),this._subscribe(s),this.setState({activePageId:e,tableKey:Date.now()},(()=>{this._requestResize&&this._requestResize(),
this._focusActivePageFirstTextInput()})))},this._handleScroll=()=>{h.globalCloseDelegate.fire()},this._handleSubmit=()=>{this.props.onSubmit(),this._closePopupDialog()},this._closePopupDialog=()=>{window.lineToolPropertiesToolbar&&window.lineToolPropertiesToolbar.refresh(),this.props.onClose()};const{pages:t}=this.props;let i;if(this._getActionPageById(this.props.activePageId))i=(0,n.ensureDefined)(this.props.activePageId);else{const e=l().getValue(this._getActiveTabSettingsName(),""),s=this._getActionPageById(e);i=s?s.id:t[0].id}this.state={activePageId:i,tableKey:Date.now(),isRenaming:!1},window.lineToolPropertiesToolbar&&window.lineToolPropertiesToolbar.hide()}componentDidMount(){const{activePageId:e}=this.state,t=this._getActionPageById(e);this._focusActivePageFirstTextInput(),this._subscribe(t)}componentWillUnmount(){const{activePageId:e}=this.props,t=this._getActionPageById(e);clearTimeout(this._timeout),this._unsubscribe(t)}render(){const{onCancel:e,source:t}=this.props,{activePageId:i,isRenaming:o}=this.state,n=t.properties().title?.value()||t.title(m.TitleDisplayTarget.StatusLine),a=s.createElement(I.Title,{isRenaming:o,onChangeMode:this._handleChangeMode,setTitle:this._setTitle,defaultTitle:n,canBeRenamed:(0,g.isLineTool)(t)&&!D.enabled("widget")});return s.createElement(d.AdaptiveConfirmDialog,{dataName:(0,b.isStudy)(t)?"indicator-properties-dialog":"source-properties-editor",dataDialogName:n,title:a,isOpened:!0,onSubmit:this._handleSubmit,onCancel:e,onClickOutside:this._handleSubmit,onClose:this._closePopupDialog,footerLeftRenderer:this._renderFooterLeft,render:this._renderChildren(i),submitOnEnterKey:!1,showCloseIcon:!o})}_renderChildren(e){return({requestResize:t})=>{this._requestResize=t;const{pages:i,source:o}=this.props,n=i.filter((e=>e.visible.value())),a=n.find((t=>t.id===e))??n[0],r=(0,b.isStudy)(o)?"indicator-properties-dialog-tabs":"source-properties-editor-tabs",l=n.map((({title:e,id:t})=>({label:e,id:t,dataId:`${r}-${t}`})));return s.createElement(s.Fragment,null,s.createElement(P.DialogTabs,{className:E.tabs,id:r,activeTab:a.id,onChange:this._handleSelectPage,tabs:l}),s.createElement(p.TouchScrollContainer,{className:E.scrollable,onScroll:this._handleScroll},s.createElement(_.PropertiesEditorTab,{page:a,pageRef:this._activePageRef,tableKey:this.state.tableKey})))}}_focusActivePageFirstTextInput(){if(!a.CheckMobile.any()&&this._activePageRef.current){const e=this._activePageRef.current.querySelector("input[type=text],textarea");e&&(this._timeout=setTimeout((()=>{e.focus()}),0))}}}var A=i(995553),F=i(651674);class M extends A.DialogRenderer{constructor(e){super(),this._timeout=null,this._handleClose=()=>{this._rootInstance?.unmount(),this._setVisibility(!1),this._onClose&&this._onClose(),this._subscription.unsubscribe(this,this._handleCollectionChanged)},this._handleSubmit=()=>{const e=this._source;(0,g.isLineTool)(e)&&e.hasAlert().value()&&setTimeout((()=>{e.areLocalAndServerAlertsMismatch()&&e.synchronizeAlert(!0)}))},this._handleCancel=()=>{this._model.undoToCheckpoint(this._checkpoint)},
this._propertyPages=e.propertyPages,this._model=e.model,this._activePageId=e.activePageId,this._onClose=e.onClose,this._source=e.source,this._checkpoint=this._ensureCheckpoint(e.undoCheckPoint),this._subscription=this._model.model().dataSourceCollectionChanged(),this._subscription.subscribe(this,this._handleCollectionChanged)}hide(e){e?this._handleCancel():this._handleSubmit(),this._handleClose()}isVisible(){return this.visible().value()}show(e){this.isVisible()||(this._rootInstance=(0,F.createReactRoot)(s.createElement(w,{source:this._source,onSubmit:this._handleSubmit,onClose:this._handleClose,onCancel:this._handleCancel,pages:this._propertyPages,model:this._model,activePageId:this._activePageId,shouldReturnFocus:e?.shouldReturnFocus}),this._container),this._setVisibility(!0),o.emit("drawings_settings_dialog",{objectType:"drawing",scriptTitle:this._source.title(m.TitleDisplayTarget.StatusLine)}))}_handleCollectionChanged(){null===this._timeout&&(this._timeout=setTimeout((()=>{this._closeDialogIfSourceIsDeleted(),this._timeout=null})))}_closeDialogIfSourceIsDeleted(){null===this._model.model().dataSourceForId(this._source.id())&&this._handleClose()}_ensureCheckpoint(e){return void 0===e&&(e=this._model.createUndoCheckpoint()),e}}},272675:(e,t,i)=>{"use strict";i.d(t,{Title:()=>u});var s=i(50959),o=i(497754),n=i(654936),a=i(878112),r=i(409174),l=i(180185),c=i(32133),d=i(748040),h=i(695327);function u(e){const{isRenaming:t,onChangeMode:i,setTitle:u,defaultTitle:_,canBeRenamed:p}=e,m=(0,s.useRef)(null),[g,b]=(0,s.useState)(_),[C,v]=(0,s.useState)(_);return(0,s.useEffect)((()=>{t&&m.current&&(m.current.focus(),m.current.setSelectionRange(0,g.length))}),[t]),s.createElement(s.Fragment,null,s.createElement("div",{className:o(h.titleWrap,t&&h.hideText)},s.createElement("span",{className:h.ellipsis}," ",C),p&&s.createElement(s.Fragment,null,s.createElement(a.Icon,{className:o(h.editIcon),icon:d,onClick:function(){(0,c.trackEvent)("GUI","Rename","Drawing settings"),b(C),i(!0)},"data-name":"edit","data-role":"button"}),s.createElement("div",{className:o(h.empty,!t&&h.hideEmpty)}))),p&&s.createElement("div",{className:o(!t&&h.hideInput),"data-disable-drag":!0},s.createElement(n.InputControl,{value:g,onChange:function(e){b(e.currentTarget.value)},onBlur:T,reference:m,onClick:r.preventDefault,onKeyDown:function(e){27===(0,l.hashFromEvent)(e)&&(e.preventDefault(),b(_),i(!1));13===(0,l.hashFromEvent)(e)&&(e.preventDefault(),T())},"data-disable-drag":!0,stretch:!0})));function T(){""!==g&&(u(g),v(g)),i(!1)}}},772551:(e,t,i)=>{"use strict";i.r(t),i.d(t,{SourcesPropertiesEditorRenderer:()=>b});var s=i(50959),o=i(995553),n=i(651674),a=i(609838),r=i(742554),l=i(976669),c=i(944316),d=i(380865),h=i(272675),u=i(440891),_=i(839246),p=i(895747);const m=a.t(null,void 0,i(408954));function g(e){
const{propertyPages:t,onSubmit:i,onCancel:o,onClose:n,title:a,activeTabId:g,sources:b,undoModel:C,renamable:v}=e,T=g&&t.filter((e=>e.id===g)).length>0?g:t[0].id,f=C.model().lineToolsGroupModel().groupForLineTool(b[0]),S=!!(v&&f&&a&&b.every((e=>f?.id===C.model().lineToolsGroupModel().groupForLineTool(e)?.id))),[P,y]=(0,s.useState)(T),[I,D]=(0,s.useState)(!1),[E,R]=(0,s.useState)(a||m),w=(0,s.useMemo)((()=>t.map((({title:e,id:t})=>({label:e,id:t,dataId:`sources-properties-editor-tabs-${t}`})))),[t]);const A=s.createElement(h.Title,{isRenaming:I,onChangeMode:function(e){D(e)},setTitle:function(e){f&&(f.setName(e),R(e))},defaultTitle:E,canBeRenamed:S&&!u.enabled("widget")});return s.createElement(l.AdaptiveConfirmDialog,{dataName:"sources-properties-editor",dataDialogName:E,title:A,isOpened:!0,onSubmit:i,onCancel:o,onClickOutside:n,onClose:n,footerLeftRenderer:function(){return s.createElement(c.FooterMenu,{sources:b,chartUndoModel:C})},render:function(){const e=t.find((e=>e.id===P));return s.createElement(s.Fragment,null,s.createElement(_.DialogTabs,{className:p.tabs,id:"sources-properties-editor-tabs",activeTab:P,onChange:y,tabs:w}),s.createElement(r.TouchScrollContainer,null,s.createElement(d.PropertiesEditorTab,{page:e,tableKey:P})))},submitOnEnterKey:!1,showCloseIcon:!I})}class b extends o.DialogRenderer{constructor(e){super(),this._dataSourceChangedPromise=null,this._submitHandler=()=>{Promise.resolve().then((()=>{this._sources.map((e=>{e.areLocalAndServerAlertsMismatch()&&e.synchronizeAlert(!0)}))})),this._close()},this._cancelHandler=()=>{this._undoModel.undoToCheckpoint(this._undoCheckpoint)},this._closeHandler=()=>{this._close()},this._dataSourceCollectionChangedHandler=()=>{null===this._dataSourceChangedPromise&&(this._dataSourceChangedPromise=Promise.resolve().then((()=>{const e=this._undoModel.model();this._sources.find((t=>null===e.dataSourceForId(t.id())))&&this._close(),this._dataSourceChangedPromise=null})))},this._sources=e.sources,this._propertyPages=e.propertyPages,this._undoModel=e.undoModel,this._title=e.title,this._activeTabId=e.activeTabId,this._renamable=e.renamable,this._undoCheckpoint=this._undoModel.createUndoCheckpoint(),this._undoModel.model().dataSourceCollectionChanged().subscribe(this,this._dataSourceCollectionChangedHandler)}destroy(){this._close()}show(){this._isVisible()||(this._mount(),this._setVisibility(!0))}hide(){this._isVisible()&&(this._unmount(),this._setVisibility(!1))}_mount(){this._rootInstance=(0,n.createReactRoot)(s.createElement(g,{propertyPages:this._propertyPages,sources:this._sources,undoModel:this._undoModel,onSubmit:this._submitHandler,onCancel:this._cancelHandler,onClose:this._closeHandler,title:this._title,activeTabId:this._activeTabId,renamable:this._renamable}),this._container)}_unmount(){this._rootInstance?.unmount()}_isVisible(){return this.visible().value()}_close(){this.hide(),this._undoModel.model().dataSourceCollectionChanged().unsubscribe(this,this._dataSourceCollectionChangedHandler)}}}}]);