(self.webpackChunktradingview = self.webpackChunktradingview || []).push([[1748, 4040, 4821, 1086], {
    155517: e => {
        e.exports = {
            en: ["Add"]
        }
    }
    ,
    329619: e => {
        e.exports = {
            en: ["Add custom color"]
        }
    }
    ,
    980936: e => {
        e.exports = {
            en: ["Opacity"]
        }
    }
    ,
    233857: e => {
        e.exports = {
            en: ["Cross"]
        }
    }
    ,
    923230: e => {
        e.exports = {
            en: ["Fr"]
        }
    }
    ,
    930961: e => {
        e.exports = {
            en: ["Mo"]
        }
    }
    ,
    894748: e => {
        e.exports = {
            en: ["Sa"]
        }
    }
    ,
    875005: e => {
        e.exports = {
            en: ["Su"]
        }
    }
    ,
    392578: e => {
        e.exports = {
            en: ["We"]
        }
    }
    ,
    608765: e => {
        e.exports = {
            en: ["Th"]
        }
    }
    ,
    244254: e => {
        e.exports = {
            en: ["Tu"]
        }
    }
    ,
    653288: e => {
        e.exports = {
            en: ["animals & nature"]
        }
    }
    ,
    821311: e => {
        e.exports = {
            en: ["activity"]
        }
    }
    ,
    590678: e => {
        e.exports = {
            en: ["food & drink"]
        }
    }
    ,
    506715: e => {
        e.exports = {
            en: ["flags"]
        }
    }
    ,
    998355: e => {
        e.exports = {
            en: ["objects"]
        }
    }
    ,
    843438: e => {
        e.exports = {
            en: ["smiles & people"]
        }
    }
    ,
    140457: e => {
        e.exports = {
            en: ["symbols"]
        }
    }
    ,
    188906: e => {
        e.exports = {
            en: ["recently used"]
        }
    }
    ,
    628562: e => {
        e.exports = {
            en: ["travel & places"]
        }
    }
    ,
    624716: e => {
        e.exports = {
            en: ["Another symbol"]
        }
    }
    ,
    148199: e => {
        e.exports = {
            en: ["Back"]
        }
    }
    ,
    476542: e => {
        e.exports = {
            en: ["Labels on price scale"]
        }
    }
    ,
    594849: e => {
        e.exports = {
            en: ["Main chart symbol"]
        }
    }
    ,
    149191: e => {
        e.exports = {
            en: ["Values in status line"]
        }
    }
    ,
    153174: e => {
        e.exports = {
            en: ["Right"]
        }
    }
    ,
    107861: e => {
        e.exports = {
            en: ["Calendar is currently on year {year}"]
        }
    }
    ,
    48126: e => {
        e.exports = {
            en: ["Calendar is currently on years from {year_start} to {year_end}"]
        }
    }
    ,
    299470: e => {
        e.exports = {
            en: ["Calendar is currently on {month}"]
        }
    }
    ,
    904543: e => {
        e.exports = {
            en: ["Cancel"]
        }
    }
    ,
    224197: e => {
        e.exports = {
            en: ["Center"]
        }
    }
    ,
    963245: e => {
        e.exports = {
            en: ["Change symbol"]
        }
    }
    ,
    354613: e => {
        e.exports = {
            en: ["Chart"]
        }
    }
    ,
    505669: e => {
        e.exports = {
            en: ["Circles"]
        }
    }
    ,
    47742: e => {
        e.exports = {
            en: ["Close menu"]
        }
    }
    ,
    308305: e => {
        e.exports = {
            en: ["Above bar"]
        }
    }
    ,
    669758: e => {
        e.exports = {
            en: ["Absolute"]
        }
    }
    ,
    234040: e => {
        e.exports = {
            en: ["All sources"]
        }
    }
    ,
    462511: e => {
        e.exports = {
            en: ["Apply Defaults"]
        }
    }
    ,
    896413: e => {
        e.exports = {
            en: ["Apply defaults"]
        }
    }
    ,
    528896: e => {
        e.exports = {
            en: ["April"]
        }
    }
    ,
    207349: e => {
        e.exports = {
            en: ["Area with breaks"]
        }
    }
    ,
    811081: e => {
        e.exports = {
            en: ["August"]
        }
    }
    ,
    327567: e => {
        e.exports = {
            en: ["Bottom"]
        }
    }
    ,
    874406: e => {
        e.exports = {
            en: ["Body"]
        }
    }
    ,
    642358: e => {
        e.exports = {
            en: ["Bond"]
        }
    }
    ,
    38408: e => {
        e.exports = {
            en: ["Border"]
        }
    }
    ,
    379468: e => {
        e.exports = {
            en: ["Background"]
        }
    }
    ,
    609417: e => {
        e.exports = {
            en: ["Below bar"]
        }
    }
    ,
    468043: e => {
        e.exports = {
            en: ["Gradient"]
        }
    }
    ,
    825255: e => {
        e.exports = {
            en: ["Do you really want to delete drawing template '{name}' ?"]
        }
    }
    ,
    27390: e => {
        e.exports = {
            en: ["Dotted line"]
        }
    }
    ,
    671776: e => {
        e.exports = {
            en: ["Down"]
        }
    }
    ,
    272942: e => {
        e.exports = {
            en: ["Days"]
        }
    }
    ,
    488123: e => {
        e.exports = {
            en: ["Dashed line"]
        }
    }
    ,
    673033: e => {
        e.exports = {
            en: ["Developing VA"]
        }
    }
    ,
    890082: e => {
        e.exports = {
            en: ["December"]
        }
    }
    ,
    455319: e => {
        e.exports = {
            en: ["Decrease"]
        }
    }
    ,
    916564: e => {
        e.exports = {
            en: ["Default"]
        }
    }
    ,
    448572: e => {
        e.exports = {
            en: ["Defaults"]
        }
    }
    ,
    178734: e => {
        e.exports = {
            en: ["Description"]
        }
    }
    ,
    7762: e => {
        e.exports = {
            en: ["Drawing template '{name}' already exists. Do you really want to replace it?"]
        }
    }
    ,
    581069: e => {
        e.exports = {
            en: ["February"]
        }
    }
    ,
    503570: e => {
        e.exports = {
            en: ["Friday"]
        }
    }
    ,
    356796: e => {
        e.exports = {
            en: ["Hours"]
        }
    }
    ,
    395406: e => {
        e.exports = {
            en: ["Horizontal"]
        }
    }
    ,
    8854: e => {
        e.exports = {
            en: ["Hidden"]
        }
    }
    ,
    978057: e => {
        e.exports = {
            en: ["Histogram"]
        }
    }
    ,
    539832: e => {
        e.exports = {
            en: ["Increase"]
        }
    }
    ,
    321429: e => {
        e.exports = {
            en: ["Inputs"]
        }
    }
    ,
    100200: e => {
        e.exports = {
            en: ["January"]
        }
    }
    ,
    206608: e => {
        e.exports = {
            en: ["July"]
        }
    }
    ,
    661487: e => {
        e.exports = {
            en: ["June"]
        }
    }
    ,
    137997: e => {
        e.exports = {
            en: ["October"]
        }
    }
    ,
    855450: e => {
        e.exports = {
            en: ["Offset"]
        }
    }
    ,
    819295: e => {
        e.exports = {
            en: ["Ok"]
        }
    }
    ,
    89702: e => {
        e.exports = {
            en: ["Outputs"]
        }
    }
    ,
    964075: e => {
        e.exports = {
            en: ["Override min tick"]
        }
    }
    ,
    462791: e => {
        e.exports = {
            en: ["Labels font"]
        }
    }
    ,
    411626: e => {
        e.exports = {
            en: ["Left"]
        }
    }
    ,
    234862: e => {
        e.exports = {
            en: ["Line with breaks"]
        }
    }
    ,
    228603: e => {
        e.exports = {
            en: ["Line style"]
        }
    }
    ,
    419573: e => {
        e.exports = {
            en: ["Monday"]
        }
    }
    ,
    943154: e => {
        e.exports = {
            en: ["Months"]
        }
    }
    ,
    437117: e => {
        e.exports = {
            en: ["More"]
        }
    }
    ,
    125734: e => {
        e.exports = {
            en: ["May"]
        }
    }
    ,
    193878: e => {
        e.exports = {
            en: ["March"]
        }
    }
    ,
    668833: e => {
        e.exports = {
            en: ["Middle"]
        }
    }
    ,
    942562: e => {
        e.exports = {
            en: ["Minutes"]
        }
    }
    ,
    953182: e => {
        e.exports = {
            en: ["No exchanges match your criteria"]
        }
    }
    ,
    476822: e => {
        e.exports = {
            en: ["No symbols match your criteria"]
        }
    }
    ,
    604607: e => {
        e.exports = {
            en: ["November"]
        }
    }
    ,
    65353: e => {
        e.exports = {
            en: ["Normal"]
        }
    }
    ,
    269102: e => {
        e.exports = {
            en: ["Next year"]
        }
    }
    ,
    517538: e => {
        e.exports = {
            en: ["Next years"]
        }
    }
    ,
    392790: e => {
        e.exports = {
            en: ["Next month"]
        }
    }
    ,
    71300: e => {
        e.exports = {
            en: ["Number format is invalid."]
        }
    }
    ,
    328628: e => {
        e.exports = {
            en: ["Sources"]
        }
    }
    ,
    88686: e => {
        e.exports = {
            en: ["Solid"]
        }
    }
    ,
    130348: e => {
        e.exports = {
            en: ["Saturday"]
        }
    }
    ,
    299687: e => {
        e.exports = {
            en: ["Save as default"]
        }
    }
    ,
    295190: e => {
        e.exports = {
            en: ["Save drawing template as"]
        }
    }
    ,
    508573: e => {
        e.exports = {
            en: ["Search"]
        }
    }
    ,
    65188: e => {
        e.exports = {
            en: ["Seconds"]
        }
    }
    ,
    757640: e => {
        e.exports = {
            en: ["Select source"]
        }
    }
    ,
    632179: e => {
        e.exports = {
            en: ["September"]
        }
    }
    ,
    238712: e => {
        e.exports = {
            en: ["Signal labels"]
        }
    }
    ,
    631331: e => {
        e.exports = {
            en: ["Specified value is more than the instrument maximum of {max}."]
        }
    }
    ,
    324216: e => {
        e.exports = {
            en: ["Specified value is less than the instrument minimum of {min}."]
        }
    }
    ,
    814788: e => {
        e.exports = {
            en: ["Step line with breaks"]
        }
    }
    ,
    711877: e => {
        e.exports = {
            en: ["Step line with diamonds"]
        }
    }
    ,
    661480: e => {
        e.exports = {
            en: ["Sunday"]
        }
    }
    ,
    170235: e => {
        e.exports = {
            en: ["Switch to months"]
        }
    }
    ,
    891245: e => {
        e.exports = {
            en: ["Switch to dates"]
        }
    }
    ,
    971961: e => {
        e.exports = {
            en: ["Switch to years"]
        }
    }
    ,
    595481: e => {
        e.exports = {
            en: ["Symbol"]
        }
    }
    ,
    774007: e => {
        e.exports = {
            en: ["Symbol & description"]
        }
    }
    ,
    751165: e => {
        e.exports = {
            en: ["Symbol Search"]
        }
    }
    ,
    610783: e => {
        e.exports = {
            en: ["Placement"]
        }
    }
    ,
    527254: e => {
        e.exports = {
            en: ["Please enter the right date"]
        }
    }
    ,
    446420: e => {
        e.exports = {
            en: ["Please enter the right date format yyyy-mm-dd"]
        }
    }
    ,
    159766: e => {
        e.exports = {
            en: ["Precision"]
        }
    }
    ,
    593984: e => {
        e.exports = {
            en: ["Previous month"]
        }
    }
    ,
    275776: e => {
        e.exports = {
            en: ["Previous year"]
        }
    }
    ,
    244701: e => {
        e.exports = {
            en: ["Previous years"]
        }
    }
    ,
    598721: e => {
        e.exports = {
            en: ["Quantity"]
        }
    }
    ,
    989984: e => {
        e.exports = {
            en: ["Remove color"]
        }
    }
    ,
    647550: e => {
        e.exports = {
            en: ["Remove selected emoji"]
        }
    }
    ,
    133533: e => {
        e.exports = {
            en: ["Reset settings"]
        }
    }
    ,
    50421: e => {
        e.exports = {
            en: ["Right"]
        }
    }
    ,
    894226: e => {
        e.exports = {
            en: ["Wednesday"]
        }
    }
    ,
    900835: e => {
        e.exports = {
            en: ["Weeks"]
        }
    }
    ,
    632163: e => {
        e.exports = {
            en: ["Wick"]
        }
    }
    ,
    304622: e => {
        e.exports = {
            en: ["Width (% of the box)"]
        }
    }
    ,
    697118: e => {
        e.exports = {
            en: ["Top"]
        }
    }
    ,
    225485: e => {
        e.exports = {
            en: ["Text alignment"]
        }
    }
    ,
    77753: e => {
        e.exports = {
            en: ["Text color"]
        }
    }
    ,
    967781: e => {
        e.exports = {
            en: ["Text orientation"]
        }
    }
    ,
    59233: e => {
        e.exports = {
            en: ["Template name"]
        }
    }
    ,
    879137: e => {
        e.exports = {
            en: ["Thursday"]
        }
    }
    ,
    254971: e => {
        e.exports = {
            en: ["Thickness"]
        }
    }
    ,
    824821: e => {
        e.exports = {
            en: ["Ticks"]
        }
    }
    ,
    306532: e => {
        e.exports = {
            en: ["Trades on chart"]
        }
    }
    ,
    682160: e => {
        e.exports = {
            en: ["Tuesday"]
        }
    }
    ,
    922691: e => {
        e.exports = {
            en: ["Up"]
        }
    }
    ,
    713748: e => {
        e.exports = {
            en: ["Use special math signs to displace selected drawings: +,-,/,* for price and +,- for bar index."]
        }
    }
    ,
    81363: e => {
        e.exports = {
            en: ["Volume profile"]
        }
    }
    ,
    660092: e => {
        e.exports = {
            en: ["Values"]
        }
    }
    ,
    569526: e => {
        e.exports = {
            en: ["Vertical"]
        }
    }
    ,
    666181: e => {
        e.exports = {
            en: ["Years"]
        }
    }
    ,
    370932: e => {
        e.exports = {
            en: ["commodity"]
        }
    }
    ,
    779599: e => {
        e.exports = {
            en: ["cfd"]
        }
    }
    ,
    626476: e => {
        e.exports = {
            en: ["change min tick"]
        }
    }
    ,
    629426: e => {
        e.exports = {
            en: ["change opacity"]
        }
    }
    ,
    886955: e => {
        e.exports = {
            en: ["change char"]
        }
    }
    ,
    731675: e => {
        e.exports = {
            en: ["change color"]
        }
    }
    ,
    185889: e => {
        e.exports = {
            en: ["change expand blocks"]
        }
    }
    ,
    327745: e => {
        e.exports = {
            en: ["change font size"]
        }
    }
    ,
    728818: e => {
        e.exports = {
            en: ["change line style"]
        }
    }
    ,
    6834: e => {
        e.exports = {
            en: ["change location"]
        }
    }
    ,
    862294: e => {
        e.exports = {
            en: ["change percent width"]
        }
    }
    ,
    981891: e => {
        e.exports = {
            en: ["change placement"]
        }
    }
    ,
    343439: e => {
        e.exports = {
            en: ["change plot type"]
        }
    }
    ,
    61863: e => {
        e.exports = {
            en: ["change precision"]
        }
    }
    ,
    83468: e => {
        e.exports = {
            en: ["change shape"]
        }
    }
    ,
    773281: e => {
        e.exports = {
            en: ["change thickness"]
        }
    }
    ,
    821333: e => {
        e.exports = {
            en: ["change value"]
        }
    }
    ,
    109344: e => {
        e.exports = {
            en: ["change values visibility"]
        }
    }
    ,
    196135: e => {
        e.exports = {
            en: ["change {title} days to"]
        }
    }
    ,
    591201: e => {
        e.exports = {
            en: ["change {title} days from"]
        }
    }
    ,
    508306: e => {
        e.exports = {
            en: ["change {title} hours from"]
        }
    }
    ,
    567233: e => {
        e.exports = {
            en: ["change {title} hours to"]
        }
    }
    ,
    399122: e => {
        e.exports = {
            en: ["change {title} months from"]
        }
    }
    ,
    910518: e => {
        e.exports = {
            en: ["change {title} months to"]
        }
    }
    ,
    938011: e => {
        e.exports = {
            en: ["change {title} minutes to"]
        }
    }
    ,
    859820: e => {
        e.exports = {
            en: ["change {title} minutes from"]
        }
    }
    ,
    906573: e => {
        e.exports = {
            en: ["change {title} seconds to"]
        }
    }
    ,
    86780: e => {
        e.exports = {
            en: ["change {title} seconds from"]
        }
    }
    ,
    832481: e => {
        e.exports = {
            en: ["change {title} weeks from"]
        }
    }
    ,
    818678: e => {
        e.exports = {
            en: ["change {title} weeks to"]
        }
    }
    ,
    798596: e => {
        e.exports = {
            en: ["change {title} visibility on ticks"]
        }
    }
    ,
    871084: e => {
        e.exports = {
            en: ["change {title} visibility on weeks"]
        }
    }
    ,
    855616: e => {
        e.exports = {
            en: ["change {title} visibility on {ranges}"]
        }
    }
    ,
    783137: e => {
        e.exports = {
            en: ["change {title} visibility on days"]
        }
    }
    ,
    368715: e => {
        e.exports = {
            en: ["change {title} visibility on hours"]
        }
    }
    ,
    578219: e => {
        e.exports = {
            en: ["change {title} visibility on minutes"]
        }
    }
    ,
    167583: e => {
        e.exports = {
            en: ["change {title} visibility on months"]
        }
    }
    ,
    441315: e => {
        e.exports = {
            en: ["change {title} visibility on seconds"]
        }
    }
    ,
    925167: e => {
        e.exports = {
            en: ["change {propertyName} property"]
        }
    }
    ,
    305741: e => {
        e.exports = {
            en: ["close"]
        }
    }
    ,
    146128: e => {
        e.exports = {
            en: ["crypto"]
        }
    }
    ,
    235813: e => {
        e.exports = {
            en: ["days"]
        }
    }
    ,
    89919: e => {
        e.exports = {
            en: ["days to"]
        }
    }
    ,
    559215: e => {
        e.exports = {
            en: ["days from"]
        }
    }
    ,
    547268: e => {
        e.exports = {
            en: ["dr"]
        }
    }
    ,
    400406: e => {
        e.exports = {
            en: ["e.g. +1"]
        }
    }
    ,
    549957: e => {
        e.exports = {
            en: ["e.g. /2"]
        }
    }
    ,
    154094: e => {
        e.exports = {
            en: ["economy"]
        }
    }
    ,
    158096: e => {
        e.exports = {
            en: ["forex"]
        }
    }
    ,
    504723: e => {
        e.exports = {
            en: ["futures"]
        }
    }
    ,
    902359: e => {
        e.exports = {
            en: ["hours"]
        }
    }
    ,
    182267: e => {
        e.exports = {
            en: ["hours from"]
        }
    }
    ,
    715600: e => {
        e.exports = {
            en: ["hours to"]
        }
    }
    ,
    159319: e => {
        e.exports = {
            en: ["high"]
        }
    }
    ,
    396008: e => {
        e.exports = {
            en: ["hl2"]
        }
    }
    ,
    391189: e => {
        e.exports = {
            en: ["hlc3"]
        }
    }
    ,
    149242: e => {
        e.exports = {
            en: ["hlcc4"]
        }
    }
    ,
    261833: e => {
        e.exports = {
            en: ["index"]
        }
    }
    ,
    990250: e => {
        e.exports = {
            en: ["indices"]
        }
    }
    ,
    452793: e => {
        e.exports = {
            en: ["ohlc4"]
        }
    }
    ,
    483584: e => {
        e.exports = {
            en: ["open"]
        }
    }
    ,
    841902: e => {
        e.exports = {
            en: ["low"]
        }
    }
    ,
    295300: e => {
        e.exports = {
            en: ["months"]
        }
    }
    ,
    317250: e => {
        e.exports = {
            en: ["months from"]
        }
    }
    ,
    702828: e => {
        e.exports = {
            en: ["months to"]
        }
    }
    ,
    418726: e => {
        e.exports = {
            en: ["minutes"]
        }
    }
    ,
    73063: e => {
        e.exports = {
            en: ["minutes to"]
        }
    }
    ,
    22476: e => {
        e.exports = {
            en: ["minutes from"]
        }
    }
    ,
    751: e => {
        e.exports = {
            en: ["seconds"]
        }
    }
    ,
    273419: e => {
        e.exports = {
            en: ["seconds to"]
        }
    }
    ,
    535801: e => {
        e.exports = {
            en: ["seconds from"]
        }
    }
    ,
    676752: e => {
        e.exports = {
            en: ["stock"]
        }
    }
    ,
    445537: e => {
        e.exports = {
            en: ["weeks"]
        }
    }
    ,
    292859: e => {
        e.exports = {
            en: ["weeks from"]
        }
    }
    ,
    844127: e => {
        e.exports = {
            en: ["weeks to"]
        }
    }
    ,
    803539: e => {
        e.exports = {
            en: ["ticks"]
        }
    }
}]);
