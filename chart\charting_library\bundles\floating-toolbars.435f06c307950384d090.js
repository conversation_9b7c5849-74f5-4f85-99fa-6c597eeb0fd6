(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2306,3177],{497754:(t,e)=>{var o;!function(){"use strict";var i={}.hasOwnProperty;function n(){for(var t=[],e=0;e<arguments.length;e++){var o=arguments[e];if(o){var r=typeof o;if("string"===r||"number"===r)t.push(o);else if(Array.isArray(o)&&o.length){var s=n.apply(null,o);s&&t.push(s)}else if("object"===r)for(var a in o)i.call(o,a)&&o[a]&&t.push(a)}}return t.join(" ")}t.exports?(n.default=n,t.exports=n):void 0===(o=function(){return n}.apply(e,[]))||(t.exports=o)}()},266783:t=>{"use strict";var e=Object.prototype.hasOwnProperty;function o(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!=t&&e!=e}t.exports=function(t,i){if(o(t,i))return!0;if("object"!=typeof t||null===t||"object"!=typeof i||null===i)return!1;var n=Object.keys(t),r=Object.keys(i);if(n.length!==r.length)return!1;for(var s=0;s<n.length;s++)if(!e.call(i,n[s])||!o(t[n[s]],i[n[s]]))return!1;return!0}},845946:t=>{t.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC",iconOnly:"iconOnly-D4RPB3ZC",link:"link-D4RPB3ZC",brand:"brand-D4RPB3ZC",primary:"primary-D4RPB3ZC",secondary:"secondary-D4RPB3ZC",gray:"gray-D4RPB3ZC",green:"green-D4RPB3ZC",red:"red-D4RPB3ZC",black:"black-D4RPB3ZC","black-friday":"black-friday-D4RPB3ZC","cyber-monday":"cyber-monday-D4RPB3ZC",slot:"slot-D4RPB3ZC",xsmall:"xsmall-D4RPB3ZC",withStartSlot:"withStartSlot-D4RPB3ZC",withEndSlot:"withEndSlot-D4RPB3ZC",startSlotWrap:"startSlotWrap-D4RPB3ZC",endSlotWrap:"endSlotWrap-D4RPB3ZC",small:"small-D4RPB3ZC",medium:"medium-D4RPB3ZC",large:"large-D4RPB3ZC",xlarge:"xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC",adjustPosition:"adjustPosition-D4RPB3ZC",firstRow:"firstRow-D4RPB3ZC",firstCol:"firstCol-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC",textWrap:"textWrap-D4RPB3ZC",multilineContent:"multilineContent-D4RPB3ZC",secondaryText:"secondaryText-D4RPB3ZC",primaryText:"primaryText-D4RPB3ZC"}},904224:t=>{t.exports={}},702030:t=>{t.exports={}},492575:t=>{t.exports={}},676357:t=>{t.exports={wrap:"wrap-Nn3SCuEL",icon:"icon-Nn3SCuEL",colorBg:"colorBg-Nn3SCuEL",color:"color-Nn3SCuEL",multicolor:"multicolor-Nn3SCuEL",white:"white-Nn3SCuEL"}},921103:t=>{t.exports={button:"button-BuUjli6L"}},787913:t=>{t.exports={item:"item-WWrZ1MEn",buttonWrap:"buttonWrap-WWrZ1MEn",small:"small-WWrZ1MEn",buttonLabel:"buttonLabel-WWrZ1MEn"}},684969:t=>{t.exports={item:"item-KdWj36gM",withIcon:"withIcon-KdWj36gM",icon:"icon-KdWj36gM",labelRow:"labelRow-KdWj36gM",multiWidth:"multiWidth-KdWj36gM",buttonWrap:"buttonWrap-KdWj36gM",buttonLabel:"buttonLabel-KdWj36gM"}},160387:t=>{t.exports={container:"container-mdcOkvbj",sectionTitle:"sectionTitle-mdcOkvbj",separator:"separator-mdcOkvbj",customButton:"customButton-mdcOkvbj",accessible:"accessible-mdcOkvbj"}},675187:t=>{t.exports={container:"container-iiEYaqPD",
form:"form-iiEYaqPD",swatch:"swatch-iiEYaqPD",white:"white-iiEYaqPD",inputWrap:"inputWrap-iiEYaqPD",inputHash:"inputHash-iiEYaqPD",input:"input-iiEYaqPD",buttonWrap:"buttonWrap-iiEYaqPD",hueSaturationWrap:"hueSaturationWrap-iiEYaqPD",saturation:"saturation-iiEYaqPD",hue:"hue-iiEYaqPD"}},58065:t=>{t.exports={hue:"hue-r4uo5Wn6",pointer:"pointer-r4uo5Wn6",accessible:"accessible-r4uo5Wn6",pointerContainer:"pointerContainer-r4uo5Wn6"}},294085:t=>{t.exports={opacity:"opacity-EnWts7Xu",opacitySlider:"opacitySlider-EnWts7Xu",opacitySliderGradient:"opacitySliderGradient-EnWts7Xu",pointer:"pointer-EnWts7Xu",dragged:"dragged-EnWts7Xu",opacityPointerWrap:"opacityPointerWrap-EnWts7Xu",opacityInputWrap:"opacityInputWrap-EnWts7Xu",opacityInput:"opacityInput-EnWts7Xu",opacityInputPercent:"opacityInputPercent-EnWts7Xu",accessible:"accessible-EnWts7Xu"}},287109:t=>{t.exports={saturation:"saturation-NFNfqP2w",pointer:"pointer-NFNfqP2w",accessible:"accessible-NFNfqP2w"}},811992:t=>{t.exports={swatches:"swatches-sfn7Lezv",swatch:"swatch-sfn7Lezv",hover:"hover-sfn7Lezv",empty:"empty-sfn7Lezv",white:"white-sfn7Lezv",selected:"selected-sfn7Lezv",contextItem:"contextItem-sfn7Lezv",row:"row-sfn7Lezv"}},149128:t=>{t.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},805184:(t,e,o)=>{"use strict";var i,n,r;function s(t="default"){switch(t){case"default":return"primary";case"stroke":return"secondary"}}function a(t="primary"){switch(t){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function l(t="m"){switch(t){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}o.d(e,{Button:()=>p}),function(t){t.Primary="primary",t.Success="success",t.Default="default",t.Danger="danger"}(i||(i={})),function(t){t.Small="s",t.Medium="m",t.Large="l"}(n||(n={})),function(t){t.Default="default",t.Stroke="stroke"}(r||(r={}));var c=o(50959),h=o(228837);function d(t){const{intent:e,size:o,appearance:i,useFullWidth:n,icon:r,...c}=t;return{...c,color:a(e),size:l(o),variant:s(i),stretch:n}}function p(t){return c.createElement(h.SquareButton,{...d(t)})}},373989:(t,e,o)=>{"use strict";function i(t){return"brand"===t?"black":"blue"===t?"brand":t}o.d(e,{renameColors:()=>i})},228837:(t,e,o)=>{"use strict";o.d(e,{SquareButton:()=>v});var i=o(50959),n=o(497754),r=o(331774),s=o(373989),a=o(845946),l=o.n(a);const c="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text apply-common-tooltip";function h(t){const{size:e="medium",variant:o="primary",stretch:i=!1,startSlot:a,endSlot:h,iconOnly:d=!1,className:p,isGrouped:u,cellState:m,disablePositionAdjustment:g=!1,primaryText:_,secondaryText:v,isAnchor:f=!1}=t,y=(0,s.renameColors)(t.color??"brand"),b=function(t){let e="";return 0!==t&&(1&t&&(e=n(e,l()["no-corner-top-left"])),2&t&&(e=n(e,l()["no-corner-top-right"])),4&t&&(e=n(e,l()["no-corner-bottom-right"])),8&t&&(e=n(e,l()["no-corner-bottom-left"]))),e}((0,r.getGroupCellRemoveRoundBorders)(m)),w=d&&(a||h)
;return n(p,l().button,l()[e],l()[y],l()[o],i&&l().stretch,a&&l().withStartIcon,h&&l().withEndIcon,w&&l().iconOnly,b,u&&l().grouped,u&&!g&&l().adjustPosition,u&&m.isTop&&l().firstRow,u&&m.isLeft&&l().firstCol,_&&v&&l().multilineContent,f&&l().link,c)}function d(t){const{startSlot:e,iconOnly:o,children:r,endSlot:s,primaryText:a,secondaryText:h}=t;if(e&&s&&o)return i.createElement("span",{className:n(l().slot,l().startSlotWrap)},e);const d=o&&(e??s),p=!e&&!s&&!o&&!r&&a&&h;return i.createElement(i.Fragment,null,e&&i.createElement("span",{className:n(l().slot,l().startSlotWrap)},e),r&&!d&&i.createElement("span",{className:l().content},r),s&&i.createElement("span",{className:n(l().slot,l().endSlotWrap)},s),p&&!d&&function(t){return t.primaryText&&t.secondaryText&&i.createElement("div",{className:n(l().textWrap,c)},i.createElement("span",{className:l().primaryText}," ",t.primaryText," "),"string"==typeof t.secondaryText?i.createElement("span",{className:l().secondaryText}," ",t.secondaryText," "):i.createElement("span",{className:l().secondaryText},i.createElement("span",null,t.secondaryText.firstLine),i.createElement("span",null,t.secondaryText.secondLine)))}(t))}var p=o(601198),u=o(380327),m=o(800417);function g(t,e){return o=>{if(e)return o.preventDefault(),void o.stopPropagation();t?.(o)}}function _(t){const{className:e,color:o,variant:i,size:n,stretch:r,animated:s,iconOnly:a,startSlot:l,endSlot:c,primaryText:h,secondaryText:d,...p}=t;return{...p,...(0,m.filterDataProps)(t),...(0,m.filterAriaProps)(t)}}function v(t){const{reference:e,tooltipText:o,disabled:n,onClick:r,onMouseOver:s,onMouseOut:a,onMouseDown:l,...c}=t,{isGrouped:m,cellState:v,disablePositionAdjustment:f}=(0,i.useContext)(u.ControlGroupContext),y=h({...c,isGrouped:m,cellState:v,disablePositionAdjustment:f}),b=o??(t.primaryText?[t.primaryText,t.secondaryText].join(" "):(0,p.getTextForTooltip)(t.children));return i.createElement("button",{..._(c),"aria-disabled":n,tabIndex:t.tabIndex??(n?-1:0),className:y,ref:e,onClick:g(r,n),onMouseDown:g(l,n),"data-overflow-tooltip-text":b},i.createElement(d,{...c}))}o(15378)},15378:(t,e,o)=>{"use strict";var i,n,r,s;!function(t){t.Primary="primary",t.QuietPrimary="quiet-primary",t.Secondary="secondary",t.Ghost="ghost"}(i||(i={})),function(t){t.XXSmall="xxsmall",t.XSmall="xsmall",t.Small="small",t.Medium="medium",t.Large="large",t.XLarge="xlarge",t.XXLarge="xxlarge"}(n||(n={})),function(t){t.Brand="brand",t.Blue="blue",t.Gray="gray",t.LightGray="light-gray",t.Green="green",t.Red="red",t.Black="black",t.Gradient="gradient",t.BlackFriday="black-friday",t.CyberMonday="cyber-monday"}(r||(r={})),function(t){t.Semibold18px="semibold18px",t.Semibold16px="semibold16px",t.Semibold14px="semibold14px",t.Medium16px="medium16px",t.Regular16px="regular16px",t.Regular14px="regular14px"}(s||(s={}))},380327:(t,e,o)=>{"use strict";o.d(e,{ControlGroupContext:()=>i});const i=o(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},331774:(t,e,o)=>{"use strict";function i(t){let e=0
;return t.isTop&&t.isLeft||(e+=1),t.isTop&&t.isRight||(e+=2),t.isBottom&&t.isLeft||(e+=8),t.isBottom&&t.isRight||(e+=4),e}o.d(e,{getGroupCellRemoveRoundBorders:()=>i})},65160:(t,e,o)=>{"use strict";function i(t){const{paddingTop:e,paddingBottom:o}=window.getComputedStyle(t);return[e,o].reduce(((t,e)=>t-Number((e||"").replace("px",""))),t.clientHeight)}function n(t,e=!1){const o=getComputedStyle(t),i=[o.height];return"border-box"!==o.boxSizing&&i.push(o.paddingTop,o.paddingBottom,o.borderTopWidth,o.borderBottomWidth),e&&i.push(o.marginTop,o.marginBottom),i.reduce(((t,e)=>t+(parseFloat(e)||0)),0)}function r(t,e=!1){const o=getComputedStyle(t),i=[o.width];return"border-box"!==o.boxSizing&&i.push(o.paddingLeft,o.paddingRight,o.borderLeftWidth,o.borderRightWidth),e&&i.push(o.marginLeft,o.marginRight),i.reduce(((t,e)=>t+(parseFloat(e)||0)),0)}o.d(e,{contentHeight:()=>i,outerHeight:()=>n,outerWidth:()=>r})},601198:(t,e,o)=>{"use strict";o.d(e,{getTextForTooltip:()=>s});var i=o(50959);const n=t=>(0,i.isValidElement)(t)&&Boolean(t.props.children),r=t=>null==t||"boolean"==typeof t||"{}"===JSON.stringify(t)?"":t.toString()+" ",s=t=>Array.isArray(t)||(0,i.isValidElement)(t)?i.Children.toArray(t).reduce(((t,e)=>{let o="";return o=(0,i.isValidElement)(e)&&n(e)?s(e.props.children):(0,i.isValidElement)(e)&&!n(e)?"":r(e),t.concat(o)}),"").trim():r(t)},738036:(t,e,o)=>{"use strict";o.d(e,{OutsideEvent:()=>n});var i=o(908783);function n(t){const{children:e,...o}=t;return e((0,i.useOutsideEvent)(o))}},496818:(t,e,o)=>{"use strict";o.d(e,{Draggable:()=>r,PointerBackend:()=>s});var i=o(650151),n=o(821205);o(492575);class r{constructor(t){this._helper=null,this._handleDragStart=t=>{if(null!==this._helper)return;const e=this._source;e.classList.add("ui-draggable-dragging");const[o,i]=[(0,n.outerWidth)(e),(0,n.outerHeight)(e)];this._helper={startTop:parseFloat(e.style.top)||0,startLeft:parseFloat(e.style.left)||0,nextTop:null,nextLeft:null,raf:null,size:[o,i],containment:this._containment instanceof HTMLElement?[parseInt(getComputedStyle(this._containment).borderLeftWidth)+parseInt(getComputedStyle(this._containment).paddingLeft),parseInt(getComputedStyle(this._containment).borderTopWidth)+parseInt(getComputedStyle(this._containment).paddingTop),this._containment.offsetWidth-parseInt(getComputedStyle(this._containment).borderRightWidth)-parseInt(getComputedStyle(this._containment).paddingRight)-parseInt(getComputedStyle(e).marginLeft)-parseInt(getComputedStyle(e).marginRight)-o,this._containment.offsetHeight-parseInt(getComputedStyle(this._containment).borderBottomWidth)-parseInt(getComputedStyle(this._containment).paddingBottom)-parseInt(getComputedStyle(e).marginTop)-parseInt(getComputedStyle(e).marginBottom)-i]:"window"===this._containment?[window.scrollX,window.scrollY,window.scrollX+document.documentElement.offsetWidth-o,window.scrollY+document.documentElement.offsetHeight-i]:null},this._start?.()},this._handleDragMove=t=>{if(null===this._helper)return
;const{current:e,initial:o}=t.detail,i=this._source,n=this._helper.nextTop,r=this._helper.nextLeft,s="y"===this._axis||!1===this._axis||0!==e.movementY;if(s){const t=this._helper.startTop;isFinite(t)&&(this._helper.nextTop=e.clientY-o.clientY+t)}const a="x"===this._axis||!1===this._axis||0!==e.movementY;if(a){const t=this._helper.startLeft;isFinite(t)&&(this._helper.nextLeft=e.clientX-o.clientX+t)}if(null!==this._helper.containment){const[t,e,o,i]=this._helper.containment;s&&this._helper.nextTop&&(this._helper.nextTop=Math.min(this._helper.nextTop,i),this._helper.nextTop=Math.max(this._helper.nextTop,e)),a&&this._helper.nextLeft&&(this._helper.nextLeft=Math.min(this._helper.nextLeft,o),this._helper.nextLeft=Math.max(this._helper.nextLeft,t))}null!==this._helper.raf||n===this._helper.nextTop&&r===this._helper.nextLeft||(this._helper.raf=requestAnimationFrame((()=>{null!==this._helper&&(null!==this._helper.nextTop&&(i.style.top=this._helper.nextTop+"px",this._helper.nextTop=null),null!==this._helper.nextLeft&&(i.style.left=this._helper.nextLeft+"px",this._helper.nextLeft=null),this._helper.raf=null)}))),this._drag?.()},this._handleDragStop=t=>{if(null===this._helper)return;this._source.classList.remove("ui-draggable-dragging"),this._helper=null,this._stop?.()};const e=this._source=t.source;e.classList.add("ui-draggable");const o=this._handle=(t.handle?e.querySelector(t.handle):null)??e;o.classList.add("ui-draggable-handle"),this._start=t.start,this._stop=t.stop,this._drag=t.drag,this._backend=new s({handle:o,onDragStart:this._handleDragStart,onDragMove:this._handleDragMove,onDragStop:this._handleDragStop}),this._axis=t.axis??!1,this._containment=t.containment}destroy(){const t=this._source;t.classList.remove("ui-draggable"),t.classList.remove("ui-draggable-dragging");this._handle.classList.remove("ui-draggable-handle"),this._backend.destroy(),null!==this._helper&&(this._helper.raf&&cancelAnimationFrame(this._helper.raf),this._helper=null)}}class s{constructor(t){this._pointerStarted=!1,this._initial=null,this._handlePointerDown=t=>{if(null!==this._initial||0!==t.button)return;if(!(t.target instanceof Element&&this._handle.contains(t.target)))return;if(this._initial=t,!this._distance&&(this._pointerStart(),!this._pointerStarted))return;t.preventDefault();const e=this._getEventTarget();e.addEventListener("pointermove",this._handlePointerMove),e.addEventListener("pointerup",this._handlePointerUp),e.addEventListener("pointercancel",this._handlePointerUp),e.addEventListener("lostpointercapture",this._handleLostPointerCapture)},this._handleLostPointerCapture=t=>{this._getEventTarget()===t.target&&this._handlePointerUp(t)},this._handlePointerMove=t=>{if(null!==this._initial&&this._initial.pointerId===t.pointerId)if(this._pointerStarted)this._pointerDrag(t);else if(this._pointerDistanceMet(t)){if(this._pointerStart(),this._pointerStarted)return void this._pointerDrag(t);this._handlePointerUp(t)}},this._handlePointerUp=t=>{if(null===this._initial||this._initial.pointerId!==t.pointerId)return;t.preventDefault()
;const e=this._getEventTarget();e.removeEventListener("pointermove",this._handlePointerMove),e.removeEventListener("pointerup",this._handlePointerUp),e.removeEventListener("pointercancel",this._handlePointerUp),e.removeEventListener("lostpointercapture",this._handlePointerUp),this._pointerStarted&&(this._pointerStarted=!1,e.releasePointerCapture(this._initial.pointerId),this._dispatchEvent(this._createEvent("pointer-drag-stop",t))),this._initial=null};const e=this._handle=t.handle;this._onDragStart=t.onDragStart,this._onDragMove=t.onDragMove,this._onDragStop=t.onDragStop,this._distance=t.distance??0,this._rootElement=t.rootElement,e.style.touchAction="none",e.addEventListener("pointerdown",this._handlePointerDown)}destroy(){const t=this._handle;t.style.touchAction="",t.removeEventListener("pointerdown",this._handlePointerDown),t.removeEventListener("pointermove",this._handlePointerMove),t.removeEventListener("pointerup",this._handlePointerUp),t.removeEventListener("pointercancel",this._handlePointerUp),t.removeEventListener("lostpointercapture",this._handlePointerUp),null!==this._initial&&(t.releasePointerCapture(this._initial.pointerId),this._initial=null),this._pointerStarted=!1}_pointerStart(){if(!this._initial)return;const t=this._getEventTarget();this._dispatchEvent(this._createEvent("pointer-drag-start",this._initial))?(this._pointerStarted=!0,t.setPointerCapture(this._initial.pointerId)):this._initial=null}_pointerDrag(t){t.preventDefault(),this._dispatchEvent(this._createEvent("pointer-drag-move",t))}_pointerDistanceMet(t){return!this._initial||!this._distance||Math.max(Math.abs(this._initial.clientX-t.clientX),Math.abs(this._initial.clientY-t.clientY))>=this._distance}_getEventTarget(){return this._rootElement??this._handle}_dispatchEvent(t){switch(t.type){case"pointer-drag-start":this._onDragStart(t);break;case"pointer-drag-move":this._onDragMove(t);break;case"pointer-drag-stop":this._onDragStop(t)}return!t.defaultPrevented}_createEvent(t,e){return(0,i.assert)(null!==this._initial),new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:{backend:this,initial:this._initial,current:e}})}}},821205:(t,e,o)=>{"use strict";o.d(e,{contentHeight:()=>n.contentHeight,html:()=>r,outerHeight:()=>n.outerHeight,outerWidth:()=>n.outerWidth,position:()=>a});var i=o(650151),n=o(65160);function r(t,e){return void 0===e||(null===e&&(t.innerHTML=""),"string"!=typeof e&&"number"!=typeof e||(t.innerHTML=String(e))),t}function s(t){if(!t.getClientRects().length)return{top:0,left:0};const e=t.getBoundingClientRect(),o=(0,i.ensureNotNull)(t.ownerDocument.defaultView);return{top:e.top+o.pageYOffset,left:e.left+o.pageXOffset}}function a(t){const e=getComputedStyle(t);let o,i={top:0,left:0};if("fixed"===e.position)o=t.getBoundingClientRect();else{o=s(t);const e=t.ownerDocument;let n=t.offsetParent||e.documentElement;for(;n&&(n===e.body||n===e.documentElement)&&"static"===getComputedStyle(n).position;)n=n.parentElement;n&&n!==t&&1===n.nodeType&&(i=s(n),i.top+=parseFloat(getComputedStyle(n).borderTopWidth),
i.left+=parseFloat(getComputedStyle(n).borderLeftWidth))}return{top:o.top-i.top-parseFloat(e.marginTop),left:o.left-i.left-parseFloat(e.marginLeft)}}},523847:(t,e,o)=>{"use strict";o.d(e,{ColorPickerButton:()=>v});var i=o(50959),n=o(497754),r=o.n(n),s=o(650151),a=o(878112),l=o(724377),c=o(589637),h=o(206397),d=o(44093),p=o(860184),u=o(679458),m=o(834297),g=o(162458),_=o(676357);function v(t){const{property:e,icon:o,propertyApplier:n,title:v,undoText:f,isToolbarFixed:y,className:b}=t,w=(0,m.useProperty)(e),T=(0,i.useRef)(null),C=w?(0,l.parseRgba)(w)[3]:void 0,E=""===w,x=String(L()).toLowerCase()===p.white,[S,P,D]=(0,h.useCustomColors)();return i.createElement(u.ToolWidgetMenu,{className:b,verticalDropDirection:y?g.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:y?g.HorizontalDropDirection.FromLeftToRight:void 0,horizontalAttachEdge:y?g.HorizontalAttachEdge.Left:void 0,verticalAttachEdge:y?g.VerticalAttachEdge.Top:void 0,content:i.createElement("div",{className:_.wrap},i.createElement(a.Icon,{className:_.icon,icon:o}),i.createElement("div",{className:_.colorBg},i.createElement("div",{className:r()(_.color,E&&_.multicolor,x&&_.white),style:E?void 0:{backgroundColor:w}}))),arrow:!1,title:v,ref:T,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`},i.createElement(d.ColorPicker,{color:L(),opacity:C,onColorChange:function(t,e){const o=w?(0,c.alphaToTransparency)((0,l.parseRgba)(w)[3]):0;B((0,c.generateColor)(String(t),o,true)),e||(0,s.ensureNotNull)(T.current).close()},onOpacityChange:function(t){B((0,c.generateColor)(w,(0,c.alphaToTransparency)(t),!0))},selectOpacity:void 0!==C,selectCustom:!0,customColors:S,onAddColor:function(t){P(t),(0,s.ensureNotNull)(T.current).close()},onRemoveCustomColor:D}));function L(){return w?(0,l.rgbToHexString)((0,l.parseRgb)(w)):null}function B(t){n.setProperty(e,t,f)}}},822598:(t,e,o)=>{"use strict";o.d(e,{LineWidthButton:()=>b});var i=o(50959),n=o(497754),r=o(650151),s=o(878112),a=o(679458),l=o(834297),c=o(192063),h=o(493173),d=o(162458),p=o(222978),u=o(114631),m=o(206096),g=o(206483),_=o(266611),v=o(684969);const f=(0,h.mergeThemes)(c.DEFAULT_POPUP_MENU_ITEM_THEME,v),y=[{value:1,icon:p},{value:2,icon:u},{value:3,icon:m},{value:4,icon:g}];function b(t){const{multipleProperty:e,title:o,undoText:h,propertyApplier:p,isToolbarFixed:u,className:m,isSmallScreen:g}=t,b=(0,l.useProperty)((0,r.ensureDefined)(e)),w="mixed"===b||!b,T=function(t){const e=y.find((e=>e.value===t));if(!e)return _;return e.icon}(b);return i.createElement(a.ToolWidgetMenu,{className:m,arrow:!1,title:o,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`,verticalDropDirection:u?d.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:u?d.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:u?d.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:u?d.VerticalAttachEdge.Top:void 0,content:i.createElement("div",null,w?i.createElement("div",{className:v.multiWidth},i.createElement(s.Icon,{icon:_})):i.createElement("div",{className:v.buttonWrap
},!g&&i.createElement(s.Icon,{icon:T}),i.createElement("div",{className:n(!g&&v.buttonLabel)},`${b}px`)))},y.map((({value:t,icon:e})=>i.createElement(c.PopupMenuItem,{key:t,theme:f,label:`${t}px`,icon:e,isActive:t===b,onClick:C,onClickArg:t}))));function C(t){t&&e&&(p.beginUndoMacro(h),e.setValue(t,void 0,{applyValue:(t,e)=>{p.setProperty(t,e,h)}}),p.endUndoMacro())}}},834297:(t,e,o)=>{"use strict";o.d(e,{useProperty:()=>n});var i=o(50959);const n=t=>{const[e,o]=(0,i.useState)(t.value());return(0,i.useEffect)((()=>{const e=t=>{o(t.value())};e(t);const i={};return t.subscribe(i,e),()=>t.unsubscribe(i,e)}),[t]),e}},578601:(t,e,o)=>{"use strict";o.d(e,{useRowsNavigation:()=>h});var i=o(50959),n=o(650151),r=o(442092),s=o(180185),a=o(333086),l=o(32556);const c=[37,39,38,40];function h(t){const e=(0,i.useRef)(null);return(0,i.useLayoutEffect)((()=>{const t=(0,n.ensureNotNull)(e.current),o=()=>{const o=(0,r.queryTabbableElements)(t).sort(r.navigationOrderComparator);if(0===o.length||o[0].parentElement&&!u(o[0].parentElement,(0,n.ensureNotNull)(e.current))){const i=function(t){const o=p(t).sort(r.navigationOrderComparator),i=o.find((t=>u(t,(0,n.ensureNotNull)(e.current))));if(!i)return null;const s=Array.from(i.children);if(!s.length)return null;return s[0]}(t);if(null===i)return;if((0,a.becomeMainElement)(i),o.length>0)for(const t of o)(0,a.becomeSecondaryElement)(t)}};return window.addEventListener("keyboard-navigation-activation",o),o(),()=>window.removeEventListener("keyboard-navigation-activation",o)}),[]),[e,function(e){if(e.defaultPrevented)return;const o=(0,s.hashFromEvent)(e);if(!c.includes(o))return;const i=document.activeElement;if(!(i instanceof HTMLElement))return;const n=e.currentTarget;let a,h;if(t){const t=i.parentElement;a=t?Array.from(t.children):[],h=a.indexOf(i)}else a=(u=n,Array.from(u.querySelectorAll("button:not([disabled]):not([aria-disabled])")).filter((0,l.createScopedVisibleElementFilter)(u))).sort(r.navigationOrderComparator),h=a.indexOf(i);var u;if(0===a.length||-1===h)return;const g=(0,r.mapKeyCodeToDirection)(o);switch(g){case"inlinePrev":if(e.preventDefault(),!t&&0===h)break;m(d(a,h,-1));break;case"inlineNext":if(e.preventDefault(),!t&&h===a.length-1)break;m(d(a,h,1));break;case"blockPrev":case"blockNext":(o=>{if(!document.activeElement)return;const i=p(n),r=document.activeElement.parentElement;if(!r)return;const s=Array.from(r.children).indexOf(document.activeElement);if(-1===s)return;const a=i["blockNext"===o?i.indexOf(r)+1:i.indexOf(r)-1];if(!a)return;e.preventDefault();const l=Array.from(a.children);l.length&&(!t&&s<=l.length-1?m(l[s]):m(l[0]))})(g)}}]}function d(t,e,o){return t[(e+t.length+o)%t.length]}function p(t){return Array.from(t.querySelectorAll('[data-role="row"]')).filter((0,l.createScopedVisibleElementFilter)(t))}function u(t,e){const o=(0,n.ensureNotNull)(t.parentElement).offsetTop,i=o+(0,n.ensureNotNull)(t.parentElement).clientHeight,r=e.scrollTop,s=r+e.clientHeight;return o>=r&&i<=s}function m(t){document.activeElement&&(0,a.becomeSecondaryElement)(document.activeElement),(0,
a.becomeMainElement)(t),t.focus()}},190266:(t,e,o)=>{"use strict";o.d(e,{runOrSignIn:()=>i,runOrSignInWithPromo:()=>n});function i(t,e){t()}function n(t,e,o){o()}},44093:(t,e,o)=>{"use strict";o.d(e,{ColorPicker:()=>H});var i=o(50959),n=o(497754),r=o.n(n),s=o(609838),a=o(578601),l=o(382665),c=o(724377),h=o(650151),d=o(601227),p=o(624216),u=o(192063),m=o(860184),g=o(865266),_=o(993544),v=o(811992);const f=4;function y(t){const{color:e,selected:r,onSelect:a,onSwatchRemove:l}=t,[c,y]=(0,i.useState)(!1),[b,w]=(0,g.useRovingTabindexElement)(null),T=Boolean(l)&&!d.CheckMobile.any();return i.createElement(i.Fragment,null,i.createElement("button",{ref:b,style:e?{color:e}:void 0,className:n(v.swatch,c&&v.hover,r&&v.selected,!e&&v.empty,String(e).toLowerCase()===m.white&&v.white),onClick:function(){a(e)},onContextMenu:T?C:void 0,tabIndex:w,"data-role":"swatch"}),T&&i.createElement(p.PopupMenu,{isOpened:c,onClose:C,position:function(){const t=(0,h.ensureNotNull)(b.current).getBoundingClientRect();return{x:t.left,y:t.top+t.height+f}},onClickOutside:C},i.createElement(u.PopupMenuItem,{className:v.contextItem,label:s.t(null,void 0,o(989984)),icon:_,onClick:function(){C(),(0,h.ensureDefined)(l)()},dontClosePopup:!0})));function C(){y(!c)}}const b=10;function w(t){const{colors:e,color:o,children:n,onSelect:r,onRemoveCustomColor:s}=t;if(!e)return null;const a=o?(0,c.parseRgb)(String(o)):void 0,h=(0,l.default)(e,b);return i.createElement("div",{className:v.swatches},h.map(((t,e)=>i.createElement("div",{className:v.row,"data-role":"row",key:e},t.map(((t,o)=>i.createElement(y,{key:String(t)+o,color:t,selected:a&&(0,c.areEqualRgb)(a,(0,c.parseRgb)(String(t))),onSelect:d,onSwatchRemove:s?()=>function(t,e){const o=t*b+e;s?.(o)}(e,o):void 0})))))),n);function d(t){r&&r(t)}}var T=o(939075),C=o(805184);function E(t){const e=`Invalid RGB color: ${t}`;if(null===t)throw new Error(e);const o=t.match(/^#?([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})$/i);if(null===o)throw new Error(e);const[,i,n,r]=o;if(!i||!n||!r)throw new Error(e);const s=parseInt(i,16)/255,a=parseInt(n,16)/255,l=parseInt(r,16)/255,c=Math.max(s,a,l),h=Math.min(s,a,l);let d;const p=c,u=c-h,m=0===c?0:u/c;if(c===h)d=0;else{switch(c){case s:d=(a-l)/u+(a<l?6:0);break;case a:d=(l-s)/u+2;break;case l:d=(s-a)/u+4;break;default:d=0}d/=6}return{h:d,s:m,v:p}}var x=o(920057),S=o(180185),P=o(822960),D=o(287109);const L=[37,39,38,40],B=.01;class W extends i.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=t=>{this._container=t},this._handlePosition=t=>{const{hsv:{h:e},onChange:o}=this.props;if(!o)return;const i=(0,h.ensureNotNull)(this._container).getBoundingClientRect(),n=t.clientX-i.left,r=t.clientY-i.top;o({h:e,s:(0,P.clamp)(n/i.width,0,1),v:(0,P.clamp)(1-r/i.height,0,1)})},this._handleKeyDown=t=>{const{hsv:{h:e,s:o,v:i},onChange:n}=this.props,r=(0,S.hashFromEvent)(t);if(!n||!L.includes(r))return;if(37===r||39===r){return void n({h:e,s:(0,P.clamp)(37===r?o-B:o+B,0,1),v:i})}n({h:e,s:o,v:(0,P.clamp)(40===r?i-B:i+B,0,1)})},this._mouseDown=t=>{
window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=t=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(t)},this._mouseMove=(0,x.default)(this._handlePosition,100),this._handleTouch=t=>{this._handlePosition(t.nativeEvent.touches[0])}}render(){const{className:t,hsv:{h:e,s:o,v:n}}=this.props,s=`hsl(${360*e}, 100%, 50%)`;return i.createElement("div",{tabIndex:0,className:r()(D.accessible,t),onKeyDown:this._handleKeyDown},i.createElement("div",{className:D.saturation,style:{backgroundColor:s},ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},i.createElement("div",{className:D.pointer,style:{left:100*o+"%",top:100*(1-n)+"%"}})))}}var A=o(58065);class k extends i.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=t=>{this._container=t},this._handlePosition=t=>{const{hsv:{s:e,v:o},onChange:i}=this.props;if(!i)return;const n=(0,h.ensureNotNull)(this._container).getBoundingClientRect(),r=t.clientY-n.top;i({h:(0,P.clamp)(r/n.height,0,1),s:e,v:o})},this._handleKeyDown=t=>{const{hsv:{h:e,s:o,v:i},onChange:n}=this.props,r=(0,S.hashFromEvent)(t);if(!n||38!==r&&40!==r)return;n({h:(0,P.clamp)(38===r?e-.01:e+.01,0,1),s:o,v:i})},this._mouseDown=t=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=t=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(t)},this._mouseMove=(0,x.default)(this._handlePosition,100),this._handleTouch=t=>{this._handlePosition(t.nativeEvent.touches[0])}}render(){const{className:t,hsv:{h:e}}=this.props;return i.createElement("div",{className:r()(A.hue,A.accessible,t),tabIndex:0,onKeyDown:this._handleKeyDown},i.createElement("div",{className:A.pointerContainer,ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},i.createElement("div",{className:A.pointer,style:{top:100*e+"%"}})))}}var M=o(675187);const N="#000000",R=s.t(null,{context:"Color Picker"},o(155517));class I extends i.PureComponent{constructor(t){super(t),this._inputRef=i.createRef(),this._handleHSV=t=>{const e=function(t){const{h:e,s:o,v:i}=t;let n,r,s;const a=Math.floor(6*e),l=6*e-a,c=i*(1-o),h=i*(1-l*o),d=i*(1-(1-l)*o);switch(a%6){case 0:n=i,r=d,s=c;break;case 1:n=h,r=i,s=c;break;case 2:n=c,r=i,s=d;break;case 3:n=c,r=h,s=i;break;case 4:n=d,r=c,s=i;break;case 5:n=i,r=c,s=h;break;default:n=0,r=0,s=0}return"#"+[255*n,255*r,255*s].map((t=>("0"+Math.round(t).toString(16)).replace(/.+?([a-f0-9]{2})$/i,"$1"))).join("")}(t)||N;this.setState({color:e,inputColor:F(e),hsv:t}),this.props.onSelect(e)},this._handleInput=t=>{const e=F(t.currentTarget.value);try{const t=E(e),o=`#${e}`;this.setState({color:o,inputColor:e,hsv:t}),this.props.onSelect(o)}catch(t){this.setState({inputColor:e})}},
this._handleAddColor=()=>this.props.onAdd(this.state.color);const e=t.color||N;this.state={color:e,inputColor:F(e),hsv:E(e)}}componentDidMount(){d.CheckMobile.any()||this._inputRef.current?.focus()}render(){const{color:t,hsv:e,inputColor:o}=this.state;return i.createElement("div",{className:M.container},i.createElement("div",{className:M.form},i.createElement("div",{className:r()(M.swatch,String(t).toLowerCase()===m.white&&M.white),style:{backgroundColor:t}}),i.createElement("div",{className:M.inputWrap},i.createElement("span",{className:M.inputHash},"#"),i.createElement("input",{ref:this._inputRef,type:"text",className:M.input,value:o,onChange:this._handleInput})),i.createElement("div",{className:M.buttonWrap},i.createElement(C.Button,{size:"s",onClick:this._handleAddColor},R))),i.createElement("div",{className:M.hueSaturationWrap},i.createElement(W,{className:M.saturation,hsv:e,onChange:this._handleHSV}),i.createElement(k,{className:M.hue,hsv:e,onChange:this._handleHSV})))}}function F(t){return t.replace(/^#/,"")}var O=o(160387);const V=s.t(null,{context:"Color Picker"},o(329619)),z=s.t(null,{context:"Color Picker"},o(980936));function H(t){const{color:e,opacity:o,selectCustom:n,selectOpacity:s,customColors:l,onRemoveCustomColor:c,onToggleCustom:h,onOpacityChange:d,menu:p}=t,[u,g]=(0,i.useState)(!1),_="number"==typeof o?o:1,[v,f]=(0,a.useRowsNavigation)();return(0,i.useLayoutEffect)((()=>{p&&p.update()}),[s,p]),u?i.createElement(I,{color:e,onSelect:y,onAdd:function(e){g(!1),h?.(!1);const{onAddColor:o}=t;o&&o(e)}}):i.createElement("div",{className:O.container},i.createElement("div",{ref:v,onKeyDown:f},i.createElement(w,{colors:m.basic,color:e,onSelect:y}),i.createElement(w,{colors:m.extended,color:e,onSelect:y}),i.createElement("div",{className:O.separator}),i.createElement(w,{colors:l,color:e,onSelect:y,onRemoveCustomColor:c},n&&i.createElement(i.Fragment,null,l?.length?i.createElement("button",{title:V,onClick:b,className:r()(O.customButton,O.accessible,"apply-common-tooltip"),tabIndex:-1}):i.createElement("div",{"data-role":"row"},i.createElement("button",{title:V,onClick:b,className:r()(O.customButton,O.accessible,"apply-common-tooltip"),tabIndex:-1}))))),s&&i.createElement(i.Fragment,null,i.createElement("div",{className:O.sectionTitle},z),i.createElement(T.Opacity,{color:e,opacity:_,onChange:function(t){d&&d(t)}})));function y(e){const{onColorChange:o}=t;o&&o(e,u)}function b(t){g(!0),h?.(!0)}}},939075:(t,e,o)=>{"use strict";o.d(e,{Opacity:()=>c});var i=o(50959),n=o(497754),r=o(650151),s=o(822960),a=o(180185),l=o(294085);class c extends i.PureComponent{constructor(t){super(t),this._container=null,this._pointer=null,this._raf=null,this._refContainer=t=>{this._container=t},this._refPointer=t=>{this._pointer=t},this._handlePosition=t=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{const e=(0,r.ensureNotNull)(this._container),o=(0,r.ensureNotNull)(this._pointer),i=e.getBoundingClientRect(),n=o.offsetWidth,a=t.clientX-n/2-i.left,l=(0,s.clamp)(a/(i.width-n),0,1);this.setState({
inputOpacity:Math.round(100*l).toString()}),this.props.onChange(l),this._raf=null})))},this._onSliderClick=t=>{this._handlePosition(t.nativeEvent),this._dragSubscribe()},this._mouseUp=t=>{this.setState({isPointerDragged:!1}),this._dragUnsubscribe(),this._handlePosition(t)},this._mouseMove=t=>{this.setState({isPointerDragged:!0}),this._handlePosition(t)},this._onTouchStart=t=>{this._handlePosition(t.nativeEvent.touches[0])},this._handleTouch=t=>{this.setState({isPointerDragged:!0}),this._handlePosition(t.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this.setState({isPointerDragged:!1})},this._handleInput=t=>{const e=t.currentTarget.value,o=Number(e)/100;this.setState({inputOpacity:e}),Number.isNaN(o)||o>1||this.props.onChange(o)},this._handleKeyDown=t=>{const e=(0,a.hashFromEvent)(t);if(37!==e&&39!==e)return;t.preventDefault();const o=Number(this.state.inputOpacity);37===e&&0!==o&&this._changeOpacity(o-1),39===e&&100!==o&&this._changeOpacity(o+1)},this.state={inputOpacity:Math.round(100*t.opacity).toString(),isPointerDragged:!1}}componentWillUnmount(){null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),this._dragUnsubscribe()}render(){const{color:t,opacity:e,hideInput:o,disabled:r}=this.props,{inputOpacity:s,isPointerDragged:a}=this.state,c={color:t||void 0};return i.createElement("div",{className:l.opacity},i.createElement("div",{className:n(l.opacitySlider,l.accessible),style:c,tabIndex:r?-1:0,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd,onKeyDown:this._handleKeyDown,"aria-disabled":r},i.createElement("div",{className:l.opacitySliderGradient,style:{backgroundImage:`linear-gradient(90deg, transparent, ${t})`}}),i.createElement("div",{className:l.opacityPointerWrap},i.createElement("div",{className:n(l.pointer,a&&l.dragged),style:{left:100*e+"%"},ref:this._refPointer}))),!o&&i.createElement("div",{className:l.opacityInputWrap},i.createElement("input",{type:"text",className:l.opacityInput,value:s,onChange:this._handleInput}),i.createElement("span",{className:l.opacityInputPercent},"%")))}_dragSubscribe(){const t=(0,r.ensureNotNull)(this._container).ownerDocument;t&&(t.addEventListener("mouseup",this._mouseUp),t.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const t=(0,r.ensureNotNull)(this._container).ownerDocument;t&&(t.removeEventListener("mousemove",this._mouseMove),t.removeEventListener("mouseup",this._mouseUp))}_changeOpacity(t){this.setState({inputOpacity:t.toString()}),this.props.onChange(t/100)}}},860184:(t,e,o)=>{"use strict";o.d(e,{basic:()=>a,extended:()=>c,white:()=>n});var i=o(926048);const n=i.colorsPalette["color-white"],r=["ripe-red","tan-orange","banana-yellow","iguana-green","minty-green","sky-blue","tv-blue","deep-blue","grapes-purple","berry-pink"],s=[200,300,400,500,600,700,800,900].map((t=>`color-cold-gray-${t}`));s.unshift("color-white"),s.push("color-black"),r.forEach((t=>{s.push(`color-${t}-500`)}));const a=s.map((t=>i.colorsPalette[t])),l=[]
;[100,200,300,400,700,900].forEach((t=>{r.forEach((e=>{l.push(`color-${e}-${t}`)}))}));const c=l.map((t=>i.colorsPalette[t]))},206397:(t,e,o)=>{"use strict";o.d(e,{useCustomColors:()=>c});var i=o(50959),n=o(870122),r=o(559410);function s(t,e){(0,i.useEffect)((()=>(r.subscribe(t,e,null),()=>{r.unsubscribe(t,e,null)})),[t,e])}var a,l=o(724377);function c(){const[t,e]=(0,i.useState)((0,n.getJSON)("pickerCustomColors",[]));s("add_new_custom_color",(o=>e(h(o,t)))),s("remove_custom_color",(o=>e(d(o,t))));const o=(0,i.useCallback)((e=>{const o=e?(0,l.parseRgb)(e):null;t.some((t=>null!==t&&null!==o&&(0,l.areEqualRgb)((0,l.parseRgb)(t),o)))||(r.emit("add_new_custom_color",e),(0,n.setJSON)("pickerCustomColors",h(e,t)))}),[t]),a=(0,i.useCallback)((e=>{(e>=0||e<t.length)&&(r.emit("remove_custom_color",e),(0,n.setJSON)("pickerCustomColors",d(e,t)))}),[t]);return[t,o,a]}function h(t,e){const o=e.slice();return o.push(t),o.length>29&&o.shift(),o}function d(t,e){return e.filter(((e,o)=>t!==o))}!function(t){t.SettingsKey="pickerCustomColors",t.GlobalAddEventName="add_new_custom_color",t.GlobalRemoveEventName="remove_custom_color",t[t.MaxColors=29]="MaxColors"}(a||(a={}))},313739:(t,e,o)=>{"use strict";o.d(e,{MatchMediaMap:()=>s});var i=o(50959),n=o(266783),r=o.n(n);class s extends i.Component{constructor(t){super(t),this._handleMediaChange=()=>{const t=l(this.state.queries,((t,e)=>e.matches));let e=!1;for(const o in t)if(t.hasOwnProperty(o)&&this.state.matches[o]!==t[o]){e=!0;break}e&&this.setState({matches:t})};const{rules:e}=this.props;this.state=a(e)}shouldComponentUpdate(t,e){return!r()(t,this.props)||(!r()(e.rules,this.state.rules)||!r()(e.matches,this.state.matches))}componentDidMount(){this._migrate(null,this.state.queries)}componentDidUpdate(t,e){r()(t.rules,this.props.rules)||this._migrate(e.queries,this.state.queries)}componentWillUnmount(){this._migrate(this.state.queries,null)}render(){return this.props.children(this.state.matches)}static getDerivedStateFromProps(t,e){if(r()(t.rules,e.rules))return null;const{rules:o}=t;return a(o)}_migrate(t,e){null!==t&&l(t,((t,e)=>{e.removeEventListener("change",this._handleMediaChange)})),null!==e&&l(e,((t,e)=>{e.addEventListener("change",this._handleMediaChange)}))}}function a(t){const e=l(t,((t,e)=>window.matchMedia(e)));return{queries:e,matches:l(e,((t,e)=>e.matches)),rules:{...t}}}function l(t,e){const o={};for(const i in t)t.hasOwnProperty(i)&&(o[i]=e(i,t[i]));return o}},874485:(t,e,o)=>{"use strict";o.d(e,{makeOverlapable:()=>r});var i=o(50959),n=o(8361);function r(t,e){return class extends i.PureComponent{render(){const{isOpened:o,root:r}=this.props;if(!o)return null;const s=i.createElement(t,{...this.props,ref:this.props.componentRef,zIndex:150});return"parent"===r?s:i.createElement(n.Portal,{shouldTrapFocus:e},s)}}}},515783:(t,e,o)=>{"use strict";o.d(e,{ToolWidgetCaret:()=>l});var i=o(50959),n=o(497754),r=o(878112),s=o(149128),a=o(100578);function l(t){const{dropped:e,className:o}=t;return i.createElement(r.Icon,{className:n(o,s.icon,{[s.dropped]:e}),icon:a})}},
493173:(t,e,o)=>{"use strict";function i(t,e,o={}){return Object.assign({},t,function(t,e,o={}){const i=Object.assign({},e);for(const n of Object.keys(e)){const r=o[n]||n;r in t&&(i[n]=[t[r],e[n]].join(" "))}return i}(t,e,o))}o.d(e,{mergeThemes:()=>i})},438576:t=>{t.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},697373:t=>{t.exports={button:"button-xNqEcuN2"}},295389:t=>{t.exports={button:"button-merBkM5y",hover:"hover-merBkM5y",clicked:"clicked-merBkM5y",accessible:"accessible-merBkM5y",arrow:"arrow-merBkM5y",arrowWrap:"arrowWrap-merBkM5y",isOpened:"isOpened-merBkM5y"}},281050:t=>{t.exports={dots:"dots-meVFo3Y9"}},139535:t=>{t.exports={mobile:"(max-width: 567px)"}},162458:(t,e,o)=>{"use strict";o.d(e,{HorizontalAttachEdge:()=>n,HorizontalDropDirection:()=>s,VerticalAttachEdge:()=>i,VerticalDropDirection:()=>r,getPopupPositioner:()=>c});var i,n,r,s,a=o(650151);!function(t){t[t.Top=0]="Top",t[t.Bottom=1]="Bottom",t[t.AutoStrict=2]="AutoStrict"}(i||(i={})),function(t){t[t.Left=0]="Left",t[t.Right=1]="Right"}(n||(n={})),function(t){t[t.FromTopToBottom=0]="FromTopToBottom",t[t.FromBottomToTop=1]="FromBottomToTop"}(r||(r={})),function(t){t[t.FromLeftToRight=0]="FromLeftToRight",t[t.FromRightToLeft=1]="FromRightToLeft"}(s||(s={}));const l={verticalAttachEdge:i.Bottom,horizontalAttachEdge:n.Left,verticalDropDirection:r.FromTopToBottom,horizontalDropDirection:s.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(t,e){return o=>{const{contentWidth:c,contentHeight:h,availableHeight:d}=o,p=(0,a.ensureNotNull)(t).getBoundingClientRect(),{horizontalAttachEdge:u=l.horizontalAttachEdge,horizontalDropDirection:m=l.horizontalDropDirection,horizontalMargin:g=l.horizontalMargin,verticalMargin:_=l.verticalMargin,matchButtonAndListboxWidths:v=l.matchButtonAndListboxWidths}=e;let f=e.verticalAttachEdge??l.verticalAttachEdge,y=e.verticalDropDirection??l.verticalDropDirection;f===i.AutoStrict&&(d<p.y+p.height+_+h?(f=i.Top,y=r.FromBottomToTop):(f=i.Bottom,y=r.FromTopToBottom));const b=f===i.Top?-1*_:_,w=u===n.Right?p.right:p.left,T=f===i.Top?p.top:p.bottom,C={x:w-(m===s.FromRightToLeft?c:0)+g,y:T-(y===r.FromBottomToTop?h:0)+b};return v&&(C.overrideWidth=p.width),C}}},155352:(t,e,o)=>{"use strict";o.d(e,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>a,ToolWidgetButton:()=>l});var i=o(50959),n=o(497754),r=o(878112),s=o(438576);const a=s,l=i.forwardRef(((t,e)=>{const{tag:o="div",icon:a,endIcon:l,isActive:c,isOpened:h,isDisabled:d,isGrouped:p,isHovered:u,isClicked:m,onClick:g,text:_,textBeforeIcon:v,title:f,theme:y=s,className:b,forceInteractive:w,inactive:T,"data-name":C,"data-tooltip":E,...x}=t,S=n(b,y.button,(f||E)&&"apply-common-tooltip",{[y.isActive]:c,[y.isOpened]:h,[y.isInteractive]:(w||Boolean(g))&&!d&&!T,
[y.isDisabled]:Boolean(d||T),[y.isGrouped]:p,[y.hover]:u,[y.clicked]:m}),P=a&&("string"==typeof a?i.createElement(r.Icon,{className:y.icon,icon:a}):i.cloneElement(a,{className:n(y.icon,a.props.className)}));return"button"===o?i.createElement("button",{...x,ref:e,type:"button",className:n(S,y.accessible),disabled:d&&!T,onClick:g,title:f,"data-name":C,"data-tooltip":E},v&&_&&i.createElement("div",{className:n("js-button-text",y.text)},_),P,!v&&_&&i.createElement("div",{className:n("js-button-text",y.text)},_)):i.createElement("div",{...x,ref:e,"data-role":"button",className:S,onClick:d?void 0:g,title:f,"data-name":C,"data-tooltip":E},v&&_&&i.createElement("div",{className:n("js-button-text",y.text)},_),P,!v&&_&&i.createElement("div",{className:n("js-button-text",y.text)},_),l&&i.createElement(r.Icon,{icon:l,className:s.endIcon}))}))},511349:(t,e,o)=>{"use strict";o.d(e,{ToolWidgetIconButton:()=>a});var i=o(50959),n=o(497754),r=o(155352),s=o(697373);const a=i.forwardRef((function(t,e){const{className:o,id:a,...l}=t;return i.createElement(r.ToolWidgetButton,{id:a,"data-name":a,...l,ref:e,className:n(o,s.button)})}))},679458:(t,e,o)=>{"use strict";o.d(e,{DEFAULT_TOOL_WIDGET_MENU_THEME:()=>v,ToolWidgetMenu:()=>y});var i=o(50959),n=o(497754),r=o.n(n),s=o(930202),a=o(624216),l=o(515783),c=o(800417),h=o(163694),d=o(759339),p=o(162458),u=o(930052),m=o(440891),g=o(111706),_=o(295389);const v=_;var f;!function(t){t[t.Vertical=2]="Vertical",t[t.Horizontal=0]="Horizontal"}(f||(f={}));class y extends i.PureComponent{constructor(t){super(t),this._wrapperRef=null,this._controller=i.createRef(),this._onPopupCloseOnClick=t=>{"keyboard"===t.detail.clickType&&this.focus()},this._handleMenuFocus=t=>{t.relatedTarget===this._wrapperRef&&this.setState((t=>({...t,isOpenedByButton:!0}))),this.props.onMenuFocus?.(t)},this._handleWrapperRef=t=>{this._wrapperRef=t,this.props.reference&&this.props.reference(t)},this._handleOpen=()=>{"div"!==this.props.tag&&(this.setState((t=>({...t,isOpenedByButton:!1}))),this.props.menuReference?.current?.addEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._controller.current?.focus())},this._handleClick=t=>{(m.enabled("skip_event_target_check")||t.target instanceof Node)&&t.currentTarget.contains(t.target)&&(this._handleToggleDropdown(void 0,(0,g.isKeyboardClick)(t)),this.props.onClick&&this.props.onClick(t,!this.state.isOpened))},this._handleToggleDropdown=(t,e=!1)=>{const{onClose:o,onOpen:i}=this.props,{isOpened:n}=this.state,r="boolean"==typeof t?t:!n;this.setState({isOpened:r,shouldReturnFocus:!!r&&e}),r&&i&&i(),!r&&o&&o()},this._handleClose=()=>{this.close()},this._handleKeyDown=t=>{const{orientation:e="horizontal"}=this.props;if(t.defaultPrevented)return;if(!(t.target instanceof Node))return;const o=(0,s.hashFromEvent)(t);if(t.currentTarget.contains(t.target))switch(o){case 40:if("div"===this.props.tag||"horizontal"!==e)return;if(this.state.isOpened)return;t.preventDefault(),this._handleToggleDropdown(!0,!0);break;case 27:if(!this.state.isOpened||!this.props.closeOnEsc)return
;t.preventDefault(),t.stopPropagation(),this._handleToggleDropdown(!1)}else{if("div"===this.props.tag)return;switch(o){case 27:{t.preventDefault();const{shouldReturnFocus:e,isOpenedByButton:o}=this.state;this._handleToggleDropdown(!1),e&&o&&this._wrapperRef?.focus();break}}}},this.state={isOpened:!1,shouldReturnFocus:!1,isOpenedByButton:!1}}render(){const{tag:t="div",id:e,arrow:o,content:n,isDisabled:s,isDrawer:a,isShowTooltip:h,title:d,className:p,hotKey:m,theme:g,drawerBreakpoint:_,tabIndex:v,isClicked:f}=this.props,{isOpened:y}=this.state,w=r()(p,g.button,{"apply-common-tooltip":h||!s,[g.isDisabled]:s,[g.isOpened]:y,[g.clicked]:f}),T=b(n)?n({isOpened:y}):n;return"button"===t?i.createElement("button",{type:"button",id:e,className:r()(w,g.accessible),disabled:s,onClick:this._handleClick,title:d,"data-tooltip-hotkey":m,ref:this._handleWrapperRef,onKeyDown:this._handleKeyDown,tabIndex:v,...(0,c.filterDataProps)(this.props),...(0,c.filterAriaProps)(this.props)},T,o&&i.createElement("div",{className:g.arrow},i.createElement("div",{className:g.arrowWrap},i.createElement(l.ToolWidgetCaret,{dropped:y}))),this.state.isOpened&&(_?i.createElement(u.MatchMedia,{rule:_},(t=>this._renderContent(t))):this._renderContent(a))):i.createElement("div",{id:e,className:w,onClick:s?void 0:this._handleClick,title:d,"data-tooltip-hotkey":m,ref:this._handleWrapperRef,"data-role":"button",tabIndex:v,onKeyDown:this._handleKeyDown,"aria-haspopup":this.props["aria-haspopup"],...(0,c.filterDataProps)(this.props)},T,o&&i.createElement("div",{className:g.arrow},i.createElement("div",{className:g.arrowWrap},i.createElement(l.ToolWidgetCaret,{dropped:y}))),this.state.isOpened&&(_?i.createElement(u.MatchMedia,{rule:_},(t=>this._renderContent(t))):this._renderContent(a)))}close(){this.props.menuReference?.current?.removeEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._handleToggleDropdown(!1)}focus(){this._wrapperRef?.focus()}update(){null!==this._controller.current&&this._controller.current.update()}_renderContent(t){const{menuDataName:e,minWidth:o,menuClassName:n,menuRole:r,maxHeight:s,drawerPosition:l="Bottom",children:c,noMomentumBasedScroll:u}=this.props,{isOpened:m}=this.state,g={horizontalMargin:this.props.horizontalMargin||0,verticalMargin:this.props.verticalMargin||2,verticalAttachEdge:this.props.verticalAttachEdge,horizontalAttachEdge:this.props.horizontalAttachEdge,verticalDropDirection:this.props.verticalDropDirection,horizontalDropDirection:this.props.horizontalDropDirection,matchButtonAndListboxWidths:this.props.matchButtonAndListboxWidths},_=Boolean(m&&t&&l),v=b(c)?c({isDrawer:_}):c;return _?i.createElement(h.DrawerManager,null,i.createElement(d.Drawer,{reference:this.props.drawerReference,onClose:this._handleClose,position:l,"data-name":e},v)):i.createElement(a.PopupMenu,{reference:this.props.menuReference,role:r,controller:this._controller,closeOnClickOutside:this.props.closeOnClickOutside,doNotCloseOn:this,isOpened:m,minWidth:o,onClose:this._handleClose,position:(0,p.getPopupPositioner)(this._wrapperRef,g),
className:n,maxHeight:s,"data-name":e,tabIndex:"div"!==this.props.tag?-1:void 0,onOpen:this._handleOpen,onKeyDown:this.props.onMenuKeyDown,onFocus:this._handleMenuFocus,noMomentumBasedScroll:u},v)}}function b(t){return"function"==typeof t}y.defaultProps={arrow:!0,closeOnClickOutside:!0,theme:_}},27833:(t,e,o)=>{"use strict";o.r(e),o.d(e,{FavoriteDrawingToolbar:()=>P});var i=o(50959),n=o(632227),r=o(609838),s=o(310107),a=o(909434),l=o(441827),c=o(621327),h=o(529596),d=o(651407),p=o(16509),u=o(870122),m=o(131595),g=o(240534),_=o(744471),v=o(128492),f=o(878112),y=o(679458),b=o(46305),w=o(403933),T=o(297265),C=o(497754),E=o(844996),x=o(281050);function S(t){const{title:e,actions:o,className:n,onActionClick:r}=t,s=(0,T.useWatchedValueReadonly)({watchedValue:d.tool}),a=o.map((t=>function(t,e,o){return i.createElement(b.AccessibleMenuItem,{key:t,onClick:()=>o(t),label:p.lineToolsInfo[t].localizedName,icon:p.lineToolsInfo[t].icon,isActive:t===e})}(t,s,r)));return i.createElement(y.ToolWidgetMenu,{className:C(n,x.dots),arrow:!1,title:e,drawerBreakpoint:w.TradingLayoutBreakpoint.Mobile,content:i.createElement(f.Icon,{icon:E}),"data-name":"more",menuDataName:"more-menu",closeOnEsc:!0},a)}o(904224);class P extends s.FloatingToolbar{constructor(t){super({allowSortable:!0,dragOnlyInsideToolbar:!0,defaultPosition:t,positionSettingsKey:"chart.favoriteDrawingsPosition",positionStorageType:"device"}),this._linetoolsWidgets={},this._canBeShownValue=new g.WatchedValue(!1),this._reactContainer=null,this._onActionClick=async t=>{await(0,v.initLineTool)(t),h.ContextMenuManager.hideAll(),d.tool.value()!==t&&d.tool.setValue(t)},this._createLineToolRenderer=()=>{let t=!0;const e=window.innerHeight>window.innerWidth,o=window.innerWidth>window.innerHeight,i=window.innerHeight===window.innerWidth;return n=>{if(t){const r=this._createLinetoolWidget(n);this.addWidget(r),e&&this.isVertical()||o&&!this.isVertical()||i?(t=this._isWidgetCanBeOnScreen(),t?this._linetoolsWidgets[n]=r:(this.removeWidget(r),this._dropdownItems.push(n))):this._linetoolsWidgets[n]=r}else this._dropdownItems.push(n)}},this._renderToolbarContent=()=>{const t=this._createLineToolRenderer();this._renderWidgetPlug(),this._linetoolsWidgets={},this._dropdownItems=[],this.removeWidgets(),a.LinetoolsFavoritesStore.favorites().filter((t=>p.lineToolsInfo[t]&&!0)).forEach(t),this._dropdownItems.length&&this._renderReactContent(),!this._dropdownItems.length&&this._reactContainer&&n.unmountComponentAtNode(this._reactContainer)},this._attachHandlers(),this._loadVisibilityState(),this._hideAction=this._createHideToolbarAction(),this._reactContainer=this.getReactWidgetContainer(),this._dropdownItems=[]}show(){this._canBeShownValue.value()&&(super.show(this._renderToolbarContent),window.addEventListener("resize",this._renderToolbarContent))}hide(){window.removeEventListener("resize",this._renderToolbarContent),super.hide()}showAndSaveSettingsValue(){this._canBeShownValue.value()&&(m.TVLocalStorage.setItem("ChartFavoriteDrawingToolbarWidget.visible","true"),this.show())}
hideAndSaveSettingsValue(){m.TVLocalStorage.setItem("ChartFavoriteDrawingToolbarWidget.visible","false"),this.hide()}canBeShown(){return this._canBeShownValue.readonly()}_onFavoriteAdded(t){this.isVisible()&&this._renderToolbarContent(),a.LinetoolsFavoritesStore.favorites().filter(D).length>0&&(this._canBeShownValue.setValue(!0),this.showAndSaveSettingsValue())}_onFavoriteRemoved(t){delete this._linetoolsWidgets[t],this.isVisible()&&this._renderToolbarContent(),0===a.LinetoolsFavoritesStore.favorites().filter(D).length&&(this._canBeShownValue.setValue(!1),this.hide())}_onFavoriteMoved(){this._renderToolbarContent()}_onSelectedLinetoolChanged(t){Object.keys(this._linetoolsWidgets).forEach((e=>{this._linetoolsWidgets[e].classList.toggle("i-active",t===e)})),this._dropdownItems.includes(t)&&this._renderReactContent()}_createLinetoolWidget(t){const e=`<span class="tv-favorited-drawings-toolbar__widget apply-common-tooltip ${t===d.tool.value()?"i-active":""}" title="${p.lineToolsInfo[t].localizedName}" data-name="FavoriteToolbar${t}">${p.lineToolsInfo[t].icon}</span>`,o=(0,l.parseHtmlElement)(e);return o.addEventListener("click",(()=>this._onActionClick(t))),o}_isWidgetCanBeOnScreen(){const t=this._getCorrectedWidgetRect();return this.isVertical()?t.height<window.innerHeight:t.width<window.innerWidth}_renderWidgetPlug(){n.render(i.createElement("div",{className:"tv-favorited-drawings-toolbar__widget"}),this._reactContainer)}_renderReactContent(){n.render(i.createElement(S,{title:r.t(null,void 0,o(437117)),actions:this._dropdownItems,onActionClick:this._onActionClick,className:"tv-favorited-drawings-toolbar__widget"}),this._reactContainer)}_attachHandlers(){a.LinetoolsFavoritesStore.favoriteAdded.subscribe(this,this._onFavoriteAdded),a.LinetoolsFavoritesStore.favoriteRemoved.subscribe(this,this._onFavoriteRemoved),a.LinetoolsFavoritesStore.favoriteMoved.subscribe(this,this._onFavoriteMoved),a.LinetoolsFavoritesStore.favoritesSynced.subscribe(null,(()=>{this._loadVisibilityState(),this._renderToolbarContent()})),this.onWidgetsReordered().subscribe(this,((t,e)=>{const o=a.LinetoolsFavoritesStore.favorite(t);if(o){const t=this._linetoolsWidgets[o];t.classList.remove("clicked"),setTimeout((()=>{t.style.pointerEvents=""}),50)}if(t!==e){if(a.LinetoolsFavoritesStore.favoriteMoved.unsubscribe(this,this._onFavoriteMoved),!a.LinetoolsFavoritesStore.moveFavorite(o,e))throw new Error("Something went wrong");a.LinetoolsFavoritesStore.favoriteMoved.subscribe(this,this._onFavoriteMoved)}})),this.onSortableStart().subscribe(this,(t=>{const e=a.LinetoolsFavoritesStore.favorite(t);if(!e)return;const o=this._linetoolsWidgets[e];o.classList.add("clicked"),(0,_.hide)(),o.style.pointerEvents="none"})),this.onContextMenu((t=>{t.preventDefault(),h.ContextMenuManager.showMenu([this._hideAction],t)})),d.tool.subscribe(this._onSelectedLinetoolChanged.bind(this))}_createHideToolbarAction(){return new c.Action({actionId:"Chart.FavoriteDrawingToolsToolbar.Hide",options:{label:r.t(null,void 0,o(322688)),onExecute:()=>{this.hideAndSaveSettingsValue()}}})
}_loadVisibilityState(){const t=a.LinetoolsFavoritesStore.favorites().filter(D).length>0;this._canBeShownValue.setValue(t);const e=a.LinetoolsFavoritesStore.favoritesCount()>0;let o;const i=u.getValue("ChartFavoriteDrawingToolbarWidget.visible");void 0!==i?(u.remove("ChartFavoriteDrawingToolbarWidget.visible",{forceFlush:!0}),o="false"!==i,m.TVLocalStorage.setItem("ChartFavoriteDrawingToolbarWidget.visible",i)):o="false"!==m.TVLocalStorage.getItem("ChartFavoriteDrawingToolbarWidget.visible"),o&&e?this.show():this.hide()}}function D(t){return!0}},310107:(t,e,o)=>{"use strict";o.d(e,{FLOATING_TOOLBAR_REACT_WIDGETS_CLASS:()=>b,FloatingToolbar:()=>T});var i=o(370981),n=o(972535),r=o(176616),s=o(870122),a=o(329452),l=o(240534),c=o(181370);class h extends c.ChunkLoader{_startLoading(){return Promise.all([o.e(1553),o.e(2377)]).then(o.bind(o,177525)).then((t=>t.HammerJS))}}var d=o(131595),p=o(496818),u=o(822960),m=o(431520),g=o(916738);class _{constructor(t){this._items=[],this._backends=[],this._helper=null,this._handleDragStart=t=>{const e=this._getAllItems()[t];if(!(e instanceof HTMLElement)||null!==this._helper)return;e.style.zIndex="10";const o=this._options.source,i=e.getBoundingClientRect(),n=o.getBoundingClientRect();this._helper={movedIndexes:new Set,draggableIndex:t,leftBorder:n.left+v(o,"padding-left")+v(o,"border-left-width")-i.left-1,rightBorder:n.right-v(o,"padding-right")-v(o,"border-right-width")-i.right+1,topBorder:n.top+v(o,"padding-top")+v(o,"border-top-width")-i.top-1,bottomBorder:n.bottom-v(o,"padding-bottom")-v(o,"border-bottom-width")-i.bottom+1},this._options.start?.(t)},this._handleDragMove=t=>{if(null===this._helper)return;const{topBorder:e,bottomBorder:o,leftBorder:i,rightBorder:n,draggableIndex:r}=this._helper,s=this._getAllItems()[r],{current:a,initial:l}=t.detail,{axis:c}=this._options,h=a.clientX-l.clientX,d=a.clientY-l.clientY;if("y"===c){const t=(0,u.clamp)(d,e,o);s.style.transform=`translateY(${t}px)`}if("x"===c){const t=(0,u.clamp)(h,i,n);s.style.transform=`translateX(${t}px)`}this._updateIdleItemsStateAndPosition()},this._handleDragStop=()=>{if(null===this._helper)return;this._getAllItems()[this._helper.draggableIndex].style.zIndex="";const t=this._applyNewItemsOrder();this._cleanup(),this._initBackends(),null!==t&&this._options.stop?.(t)},this._options=t,this._initBackends()}destroy(){this._cleanup();for(const t of this._backends)t.destroy()}updateOptions(t){(0,g.default)(this._options,t)}updateList(){this._cleanup(),this._initBackends()}_initBackends(){for(const t of this._backends)t.destroy();const t=[];this._getAllItems().forEach(((e,o)=>{t.push(new p.PointerBackend({handle:e,onDragStart:()=>this._handleDragStart(o),onDragMove:this._handleDragMove,onDragStop:this._handleDragStop,distance:5,rootElement:window.document.documentElement}))})),this._backends=t}_cleanup(){this._getAllItems().forEach((t=>{t.style.transform=""})),this._helper=null,this._items=[]}_getAllItems(){return this._items?.length||(this._items=Array.from(this._options.source.children)),this._items}
_updateIdleItemsStateAndPosition(){if(null===this._helper)return;const{axis:t}=this._options,{draggableIndex:e}=this._helper,o=this._getAllItems(),i=o[e].getBoundingClientRect(),n=i.top+i.height/2,r=i.left+i.width/2,s="x"===t?r:n,a=new Set;o.forEach(((o,n)=>{if(e===n)return;const r=o.getBoundingClientRect(),l=r.left+r.width/2,c=r.top+r.height/2,h="x"===t?l:c,d=e>n,p=(0,m.isRtl)();let u;if(u=d?p&&s>=h||!p&&s<=h:p&&s<=h||!p&&s>=h,u){a.add(n);const e=d?p?-1:1:p?1:-1;switch(t){case"x":o.style.transform=`translateX(${e*i.width}px)`;break;case"y":o.style.transform=`translateY(${e*i.height}px)`}}else o.style.transform="",a.delete(n)})),this._helper.movedIndexes=a}_applyNewItemsOrder(){if(null===this._helper)return null;const{draggableIndex:t,movedIndexes:e}=this._helper,o=this._getAllItems(),i=o[t];let n=null;const r=[];o.forEach(((o,i)=>{if(i===t)return;const n=t>i;if(!e.has(i))return void(r[i]=o);r[n?i+1:i-1]=o}));for(let t=0;t<o.length;t++){void 0===r[t]&&(n=t,r[t]=i)}return e.size&&r.forEach((t=>{this._options.source.appendChild(t)})),n}}function v(t,e){return parseInt(function(t,e){return getComputedStyle(t,null).getPropertyValue(e)}(t,e))}var f=o(441827),y=o(25388);o(702030);const b="floating-toolbar-react-widgets",w=`<div class="tv-floating-toolbar i-closed i-hidden"><div class="tv-floating-toolbar__widget-wrapper"><div class="tv-floating-toolbar__drag js-drag">${y}</div><div class="tv-floating-toolbar__content js-content"></div><div class="${b}"></div></div></div>`;class T{constructor(t){this._widget=document.createElement("div"),this._isVertical=!1,this._hiddingTimeoutId=null,this._visibility=new l.WatchedValue(!1),this._windowResizeListener=this._onWindowResize.bind(this),this._reorderedDelegate=new a.Delegate,this._startSortableDelegate=new a.Delegate,this._responsiveResizeFunction=null,this._showTimeStamp=null,this._draggable=null,this._sortable=null,this._preventClickUntilAnimation=t=>{null!==this._showTimeStamp&&performance.now()-this._showTimeStamp<this.hideDuration()&&t.stopPropagation()},T._toolbars.push(this),this._options=t,this._widget=(0,f.parseHtmlElement)(w),this._content=this._widget.getElementsByClassName("js-content").item(0),this._reactWidgetsContainer=this._widget.getElementsByClassName(b).item(0),this._setZIndex(T._startZIndex+T._toolbars.length-1),this._options.addClass&&(this._widget.className+=` ${this._options.addClass}`),this._options["data-name"]&&(this._widget.dataset.name=this._options["data-name"]),this._options.layout&&"auto"!==this._options.layout&&(this._isVertical="vertical"===this._options.layout,this._updateLayoutType()),this._widget.addEventListener("click",this._preventClickUntilAnimation,!0)}destroy(){this.hide(!0),T._toolbars.splice(T._toolbars.indexOf(this),1),this._widget.removeEventListener("click",this._preventClickUntilAnimation,!0),document.body.contains(this._widget)&&document.body.removeChild(this._widget),null!==this._draggable&&this._draggable.destroy(),null!==this._sortable&&this._sortable.destroy(),this._widget.innerHTML="",this._responsiveResizeFunction=null}
setResponsiveResizeFunc(t){this._responsiveResizeFunction=t}isVisible(){return this._visibility.value()}visibility(){return this._visibility.readonly()}isVertical(){return this._isVertical}show(t){this.isVisible()||(document.body.contains(this._widget)||(this._init(),document.body.appendChild(this._widget)),this._setHiddingTimeout(null),window.addEventListener("resize",this._windowResizeListener),this.raise(),this._visibility.setValue(!0),this._showTimeStamp=performance.now(),this._widget.classList.contains("i-hidden")?(this._widget.classList.remove("i-hidden"),setTimeout((()=>{this.isVisible()&&(t?.(),this._widget.classList.remove("i-closed"))}))):(t?.(),this._widget.classList.remove("i-closed")),this._onWindowResize())}hide(t=!1){if(!this.isVisible())return;const e=this._widget.classList.contains("i-closed");if(this._widget.classList.add("i-closed"),this._visibility.setValue(!1),t||e)this._setHiddingTimeout(null),this._widget.classList.add("i-hidden");else{const t=setTimeout((()=>{this._setHiddingTimeout(null),this._widget.classList.add("i-hidden")}),this.hideDuration());this._setHiddingTimeout(t)}window.removeEventListener("resize",this._windowResizeListener)}raise(){T._toolbars.length+T._startZIndex!==this._zIndex()&&(T._toolbars.splice(T._toolbars.indexOf(this),1),T._toolbars.push(this),T._updateAllZIndexes())}hideDuration(){return.75*r.dur}addWidget(t,e={}){const o=this.widgetsCount();if(void 0===e.index&&(e.index=o),e.index<0||e.index>o)throw new Error(`Index must be in [0, ${o}]`);const i=document.createElement("div");i.className="tv-floating-toolbar__widget js-widget",i.appendChild(t);const n=e.index===o?null:this._content.childNodes.item(e.index);this._content.insertBefore(i,n),this._onWindowResize(),this._sortable?.updateList()}getReactWidgetContainer(){return this._reactWidgetsContainer}onWidgetsReordered(){return this._reorderedDelegate}onSortableStart(){return this._startSortableDelegate}removeWidget(t){const e=this._findWrapperForWidget(t);e&&(this._content.removeChild(e),this._onWindowResize(),this._sortable?.updateList())}widgetsCount(){return this._content.childNodes.length}showWidget(t){const e=this._findWrapperForWidget(t);e&&e.classList.remove("i-hidden")}hideWidget(t){const e=this._findWrapperForWidget(t);e&&e.classList.add("i-hidden")}removeWidgets(){for(;this._content.firstChild;)this._content.removeChild(this._content.firstChild);this._onWindowResize()}onContextMenu(t){if(n.mobiletouch){(new h).load().then((e=>{const o=new e(this._widget);o.get("press").set({time:500}),o.on("press",(e=>{this._preventWidgetTouchEndEvent(),t(e.srcEvent)}))}))}else this._widget.addEventListener("contextmenu",t)}checkPosition(){const t=this._getCorrectedWidgetRect(),e={left:t.left,top:t.top};this._correctPosition(e),t.left===e.left&&t.top===e.top||(this._widget.style.left=e.left+"px",this._widget.style.top=e.top+"px")}_determineCurrentLayoutVertical(t){const e=this._isVertical?t.height:t.width;return window.innerWidth<e&&window.innerWidth<window.innerHeight}_getWidget(){return this._widget}
_findWrapperForWidget(t){const e=this._content.getElementsByClassName("js-widget");for(let o=0;o<e.length;++o){const i=e.item(o);if(i.contains(t))return i}return null}_onVerticalChanged(t,e){}_correctPosition(t){const e=this._getCorrectedWidgetRect(),o=this._getSavedPosition(),i=window.innerWidth-e.right,n=window.innerHeight-e.bottom;i<0?t.left=Math.max(0,window.innerWidth-e.width):o&&o.left>t.left&&(t.left=Math.min(t.left+i,o.left)),n<0?t.top=Math.max(0,window.innerHeight-e.height):o&&o.top>t.top&&(t.top=Math.min(t.top+n,o.top))}_getCorrectedWidgetRect(){const t=this._widget.getBoundingClientRect();if(this._widget.classList.contains("i-closed")){const e=1/.925-1,o=t.width*e,i=t.height*e;return{bottom:t.bottom+i/2,height:t.height+i,left:t.left-o/2,right:t.right+o/2,top:t.top-i/2,width:t.width+o}}return t}_getSavedPosition(){let t;if("device"===this._options.positionStorageType){const e=d.TVLocalStorage.getItem(this._options.positionSettingsKey);t=null!==e?JSON.parse(e):null}else t=(0,s.getJSON)(this._options.positionSettingsKey)??null;return null!==t&&"top"in t&&"left"in t?t:null}_setHiddingTimeout(t){null!==this._hiddingTimeoutId&&clearTimeout(this._hiddingTimeoutId),this._hiddingTimeoutId=t}_preventWidgetTouchEndEvent(){const t=e=>{e.preventDefault(),this._widget.removeEventListener("touchend",t)};this._widget.addEventListener("touchend",t)}_updateLayoutType(){this._widget.classList.toggle("i-vertical",this._isVertical)}_updateAxisOption(){this._sortable&&this._sortable.updateOptions({axis:this._isVertical?"y":"x"})}_onWindowResize(){if("auto"===(this._options.layout||"auto")){const t=this._isVertical,e=this._getCorrectedWidgetRect();this._isVertical=this._determineCurrentLayoutVertical(e),this._updateLayoutType(),t!==this._isVertical&&(this._onVerticalChanged(this._isVertical,t),this._updateAxisOption())}this.checkPosition(),this._resizeResponsive()}_resizeResponsive(){if(null===this._responsiveResizeFunction)return;let t=this._options.layout||"auto";"auto"===t&&(t=this._isVertical?"vertical":"horizontal");const e="vertical"===t?this._widget.clientHeight:this._widget.clientWidth,o=("vertical"===t?window.innerHeight:window.innerWidth)-e;this._responsiveResizeFunction(e,o,t)}_setZIndex(t){this._widget.style.zIndex=String(t)}_zIndex(){return Number(this._widget.style.zIndex)}_loadPosition(){const t=this._getSavedPosition()??this._options.defaultPosition;this._widget.style.left=Math.round(t.left)+"px",this._widget.style.top=Math.round(t.top)+"px",this._onWindowResize()}_savePosition(){const t=this._widget.getBoundingClientRect();if("device"===this._options.positionStorageType)try{d.TVLocalStorage.setItem(this._options.positionSettingsKey,JSON.stringify({left:t.left,top:t.top}))}catch{}else(0,s.setJSON)(this._options.positionSettingsKey,{left:t.left,top:t.top})}_init(){this._loadPosition(),this._draggable=new p.Draggable({source:this._widget,containment:"window",handle:".js-drag",start:i.globalCloseMenu,stop:this._savePosition.bind(this)}),this._initSortable(),
this._widget.addEventListener("pointerdown",this.raise.bind(this))}_initSortable(){if(!this._options.allowSortable)return;let t=-1;this._sortable=new _({source:this._content,axis:this._isVertical?"y":"x",start:e=>{(0,i.globalCloseMenu)(),t=e,this._startSortableDelegate.fire(t)},stop:e=>{this._reorderedDelegate.fire(t,e)}})}static _updateAllZIndexes(){T._toolbars.forEach(((t,e)=>{t._setZIndex(T._startZIndex+e)}))}}T._startZIndex=20,T._toolbars=[]},53266:(t,e,o)=>{"use strict";o.d(e,{LineToolPropertiesWidgetBase:()=>Jt});var i=o(50959),n=o(632227),r=o(609838),s=o(272047),a=o(440891),l=o(240534),c=o(41899),h=o(651407),d=o(622860),p=o(981856),u=o(650151),m=o(571772);class g extends m.Property{constructor(t,e,o){super(),this._listenersMappers=[],this._isProcess=!1,this._baseProperty=t,this._propertyApplier=e,this._undoText=o}destroy(){this._baseProperty.destroy(),super.destroy()}value(){return this._baseProperty.value()}setValue(t){this._isProcess=!0,this._baseProperty.setValue(t,void 0,{applyValue:(t,e)=>this._propertyApplier.setProperty(t,e,this._undoText)}),this._isProcess=!1,this._listenersMappers.forEach((t=>{t.method.call(t.obj,this,"")}))}subscribe(t,e){const o=o=>{this._isProcess||e.call(t,this,"")},i={obj:t,method:e,callback:o};this._listenersMappers.push(i),this._baseProperty.subscribe(t,o)}unsubscribe(t,e){const o=(0,u.ensureDefined)(this._listenersMappers.find((o=>o.obj===t&&o.method===e))?.callback);this._baseProperty.unsubscribe(t,o)}unsubscribeAll(t){this._baseProperty.unsubscribeAll(t)}}var _=o(543259),v=o(497754),f=o.n(v),y=o(313739),b=o(310107),w=o(996038);const T=b.FLOATING_TOOLBAR_REACT_WIDGETS_CLASS+"__button";function C(t){const{templateButton:e,cancelButton:o,propertyButtons:n,commonButtons:r,actionButtons:s,isDrawingFinished:a,isToolbarFixed:l,buttonClassName:c,activeChartWidget:h}=t,d=h.hasModel()&&h.model().selection().dataSources();return d&&d.length?i.createElement(y.MatchMediaMap,{rules:{isSmallWidth:w.DialogBreakpoints.TabletSmall,isSmallHeight:"(max-height: 440px)"}},(({isSmallWidth:t,isSmallHeight:e})=>i.createElement(i.Fragment,null,p(),a&&i.createElement(i.Fragment,null,Boolean(s.length)&&s.map(((t,e)=>i.createElement(t.component,{...t.props,key:`${t.props.title}_${e}`,className:f()(T,c)}))),Boolean(n.length)&&n.map(((o,n)=>i.createElement(o.component,{...o.props,key:`${o.props.title}_${n}`,className:f()(T,c),isSmallScreen:t||e,isToolbarFixed:l}))),Boolean(r.length)&&r.map(((o,n)=>{const r=t||e;return r?o.showForSmallScreen?i.createElement(o.component,{...o.props,isSmallScreen:r,key:`${o.props.title}_${n}`,className:f()(T,c)}):null:i.createElement(o.component,{...o.props,key:`${o.props.title}_${n}`,className:f()(T,c)})}))),null!==o&&i.createElement(o.component,{...o.props,key:`${o.props.title}`,className:f()(T,c),isSmallScreen:t||e,isToolbarFixed:l})))):p();function p(){return null===e?null:i.createElement(e.component,{...e.props,isToolbarFixed:l,isDrawingFinished:a,className:f()(T,c)})}}var E=o(32133),x=o(511349),S=o(634369);function P(t){const{title:e,activeChartWidget:o,className:n}=t
;return i.createElement(x.ToolWidgetIconButton,{className:n,icon:S,title:e,onClick:async function(){(0,E.trackEvent)("GUI","Context action on drawings","Settings");const t=o.model().selection().lineDataSources(),e=t.length;1===e?await o.showChartPropertiesForSource(t[0],void 0,{onWidget:o.onWidget()}):e>1&&await o.showChartPropertiesForSources({sources:t})},"data-name":"settings"})}var D=o(834297),L=o(898828),B=o(921103);function W(t){const{className:e,...o}=t;return i.createElement(L.ToolButton,{className:v(e,B.button),tooltipPosition:"horizontal",...o})}var A=o(665186),k=o(691244);function M(t){const{activeChartWidget:e,className:n}=t,s=e.model().selection().lineDataSources();if(0===s.length)return null;const a=s[0].properties().frozen,l=(0,D.useProperty)(a),c=l?{tooltip:r.t(null,void 0,o(999894)),icon:A}:{tooltip:r.t(null,void 0,o(651077)),icon:k};return i.createElement(W,{className:n,isActive:Boolean(l),onClick:function(){(0,E.trackEvent)("GUI","Context action on drawings","Lock"),e.toggleLockSelectedObject()},"data-name":Boolean(l)?"unlock":"lock",...c})}var N=o(539442),R=o(61121);function I(t,e,o){const[n,r]=(0,i.useState)(null);return(0,i.useEffect)((()=>{const o=new AbortController;let i=null;return(0,R.respectAbort)(o.signal,t()).then((t=>r(i=t))).catch(R.skipAbortError),()=>{o.abort(),i&&e&&e(i)}}),o?[...o]:void 0),n}var F=o(578820);function O(t){const{activeChartWidget:e,className:n}=t,s=e.model().selection().lineDataSources(),a=s.find((t=>t.anchorable())),l=(0,D.useProperty)(a?.properties().anchored??new m.Property(!1)),c=I((()=>new N.ActionsProvider(e).actionForLineTools(s,"Chart.SelectedObject.ToggleAnchored")),(t=>t?.destroy()),[e,l]);return c?i.createElement(W,{className:n,isActive:Boolean(l),tooltip:r.t(null,void 0,o(174089)),icon:F.icons.get("Chart.AnchorLineTool"),onClick:()=>c.execute(),"data-name":"toggle-anchor"}):null}var V=o(993544);function z(t){const{title:e,activeChartWidget:o,className:n}=t;return i.createElement(x.ToolWidgetIconButton,{className:n,icon:V,title:e,"data-name":"remove",onClick:function(){(0,E.trackEvent)("GUI","Context action on drawings","Remove"),o.removeSelectedSources()}})}var H=o(601227),U=o(953054);const Z=H.CheckMobile.any();function j(t){if(!Z)return null;const{activeChartWidget:e,className:n}=t,s=e.model().selection().lineDataSources();if(0===s.length)return null;const a=s[0];if(!a.snapTo45DegreesAvailable())return null;const l=(0,D.useProperty)((0,h.alignTo45Degrees)()),c=l?{tooltip:r.t(null,void 0,o(631145)),icon:U}:{tooltip:r.t(null,void 0,o(383187)),icon:U};return i.createElement(W,{className:n,isActive:Boolean(l),onClick:function(){(0,E.trackEvent)("GUI","Context action on drawings","Align to 45 degrees");const t=(0,h.alignTo45Degrees)().value();(0,h.alignTo45Degrees)().setValue(!t),t||e.model().alignToolTo45Degrees(a)},"data-name":Boolean(l)?"disable-align-to-45-degrees":"enable-align-to-45-degrees",...c})}var G=o(698486);function $(t){const{title:e,activeChartWidget:o,className:n}=t;return i.createElement(x.ToolWidgetIconButton,{className:n,icon:G,title:e,
"data-name":"cancel-drawing",onClick:function(){(0,E.trackEvent)("GUI","Context action on drawings","Cancel Drawing"),o.resetDrawingState()}})}var K=o(878112),q=o(799573),X=o(972535),Y=o(679458),Q=o(126096),J=o(529596),tt=o(180185),et=o(926032),ot=o(621327);async function it(t,e){const i=[(0,N.createVisualOrderAction)(t,e),(0,N.createChangeIntervalsVisibilitiesAction)(t,e)],n=function(t,e){const i=[],n=tt.isMacKeyboard?" +":"",s=e.filter((t=>t.cloneable()));s.length>0&&i.push(new ot.Action({actionId:"Chart.LineTool.Clone",options:{name:"clone",icon:o(636296),shortcutHint:tt.humanReadableModifiers(et.Modifiers.Mod)+n+" Drag",label:r.t(null,void 0,o(712537)),onExecute:()=>{t.model().cloneLineTools(s,!1),(0,E.trackEvent)("GUI","Context action on drawings","Clone")}}}));const a=e.filter((t=>t.copiable()));if(a.length>0){const e={name:"copy",label:r.t(null,void 0,o(249680)),shortcutHint:tt.humanReadableModifiers(et.Modifiers.Mod)+n+" C",onExecute:()=>{t.chartWidgetCollection().clipboard.uiRequestCopy(a)}};i.push(new ot.Action({actionId:"Chart.Clipboard.CopyLineTools",options:e,id:"Copy"}))}return i}(t,e);if(n.length&&i.push(new ot.Separator,...n),e.some((t=>t.isSynchronizable()))){const o=(0,N.createSyncDrawingActions)(t,e);o.length&&i.push(new ot.Separator,...o)}if(1===e.length&&e[0].additionalActions){const o=await e[0].additionalActions(t.model(),"FloatingToolbarButton");o.actions.length&&(i.push(new ot.Separator),i.push(...o.actions))}return i.push(new ot.Separator,(0,N.createActionToggleVisibilityDataSources)(t,e)),i}var nt=o(844996);function rt(t){const{title:e,activeChartWidget:o,isSmallScreen:n,className:r}=t,s=o.model(),a=s.selection().lineDataSources(),[l,c]=(0,i.useState)([]),h=(0,i.useRef)(null),d=(0,i.useMemo)((()=>new N.ActionsProvider(o)),[o]),p=(0,i.useCallback)((()=>d.contextMenuActionsForSources(a,(0,u.ensureNotNull)(s.paneForSource(a[0])))),[d,a]),m=(0,i.useCallback)((async()=>{if(n)return;const t=await it(o,a);c(st(t))}),[n,o,a]),g=(0,i.useCallback)((t=>{n&&p().then((e=>{const o=st(e);window.matchMedia(w.DialogBreakpoints.TabletSmall).matches||!q.isAnyMobile?J.ContextMenuManager.showMenu(o,t,{mode:q.isAnyMobile?"drawer":"menu","data-name":"more-menu"},{menuName:"LineToolFloatingToolbarMoreMenu"}):c(o)}))}),[n,p]);return(0,i.useEffect)((()=>{l.length&&h.current?.update()}),[l]),i.createElement(Y.ToolWidgetMenu,{className:r,ref:h,arrow:!1,onOpen:m,onClick:g,title:e,content:i.createElement(K.Icon,{icon:nt}),"data-name":"more",menuDataName:"more-menu",noMomentumBasedScroll:!0,closeOnEsc:!0},i.createElement(Q.ActionsTable,{parentIsOpened:!0,items:l}))}function st(t){if(X.touch&&!window.matchMedia("(pointer:fine)").matches){const e=t.filter((t=>"Copy"!==t.id));if(e.length===t.length)return e;const o=[];return e.forEach((t=>{("separator"!==t.type||o.length>0&&"separator"!==o[o.length-1].type)&&o.push(t)})),o}return t}var at=o(813108),lt=o(162458),ct=o(200501),ht=o(123851),dt=o(357740),pt=o(780427);function ut(t){const{property:e,propertyApplier:n,title:s,undoText:a,isToolbarFixed:l,className:c}=t,h=(0,
D.useProperty)(e),d=(0,i.useMemo)((()=>[new ot.Action({actionId:"Chart.LineTool.Toolbar.ChangeLineStyleToSolid",options:{icon:ct,label:r.t(null,void 0,o(903554)),active:at.LineStyle.Solid===h,onExecute:()=>n.setProperty(e,at.LineStyle.Solid,a)}}),new ot.Action({actionId:"Chart.LineTool.Toolbar.ChangeLineStyleToDashed",options:{icon:ht,label:r.t(null,void 0,o(488123)),active:at.LineStyle.Dashed===h,onExecute:()=>n.setProperty(e,at.LineStyle.Dashed,a)}}),new ot.Action({actionId:"Chart.LineTool.Toolbar.ChangeLineStyleToDotted",options:{icon:dt,label:r.t(null,void 0,o(27390)),active:at.LineStyle.Dotted===h,onExecute:()=>n.setProperty(e,at.LineStyle.Dotted,a)}})]),[n,e,h]);return i.createElement(Y.ToolWidgetMenu,{className:c,arrow:!1,content:i.createElement(K.Icon,{icon:mt(h)}),title:s,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`,verticalDropDirection:l?lt.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:l?lt.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:l?lt.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:l?lt.VerticalAttachEdge.Top:void 0},i.createElement(Q.ActionsTable,{items:d}))}function mt(t){switch(t){case at.LineStyle.Solid:return ct;case at.LineStyle.Dashed:return ht;case at.LineStyle.Dotted:return dt;case"mixed":return pt;default:return""}}const gt=[8,10,11,12,14,16,18,20,22,24,28,32,40];function _t(t){const{property:e,propertyApplier:o,title:n,undoText:r,isToolbarFixed:s,className:a}=t,l=(0,D.useProperty)(e),c=gt.map((t=>new ot.Action({actionId:"Chart.LineTool.Toolbar.ChangeFontSizeProperty",options:{label:t.toString(),onExecute:()=>o.setProperty(e,t,r),active:t===l}})));return i.createElement(Y.ToolWidgetMenu,{arrow:!1,content:l,className:a,title:n,verticalDropDirection:s?lt.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:s?lt.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:s?lt.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:s?lt.VerticalAttachEdge.Top:void 0,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`},i.createElement(Q.ActionsTable,{items:c}))}var vt=o(186661),ft=o(128492),yt=o(816232),bt=o(669874),wt=o(76821),Tt=o(608280);function Ct(t){const{model:e,onRestore:n,onSave:s,isDrawingFinished:l,isToolbarFixed:c,className:h,...d}=t,[p,u]=(0,i.useState)(!1),[m,g]=(0,i.useState)(null),_=(0,i.useRef)(null);(0,i.useEffect)((()=>{_.current?.update()}),[p]);const v=(0,i.useMemo)((()=>{if(s)return()=>{e.showSaveDialog((t=>{e.saveTemplate(t,JSON.stringify(s()))}))}}),[e,s]),f=(0,i.useMemo)((()=>{const t=[];return l&&v&&t.push(new ot.Action({actionId:"Chart.LineTool.Templates.SaveAs",options:{label:(0,bt.appendEllipsis)(r.t(null,void 0,o(776047))),onExecute:v}})),t.push(new ot.Action({actionId:"Chart.LineTool.Templates.ApplyDefaults",options:{label:r.t(null,void 0,o(114663)),onExecute:n}})),p?t.push(new ot.Loader("Chart.LineTool.Templates.Apply")):!p&&m?.length&&(t.push(new ot.Separator),t.push(...m.map((t=>new ot.Action({actionId:"Chart.LineTool.Templates.Apply",options:{label:t,
onExecute:()=>function(t){void 0!==t&&e.loadTemplate(t)}(t),showToolboxOnHover:!0,toolbox:{type:wt.ToolboxType.Delete,action:()=>e.deleteAction(t)}}}))))),t}),[v,n,m,e,l,p]);return i.createElement(Y.ToolWidgetMenu,{title:r.t(null,void 0,o(881152)),content:i.createElement(K.Icon,{icon:Tt}),onOpen:a.enabled("drawing_templates")?function(){u(!0),e.templatesLoaded().then((()=>{const t=e.getData();void 0!==t&&(g(t),u(!1))})),(0,E.trackEvent)("GUI","Context action on drawings","Templates")}:void 0,arrow:!1,className:h,"data-name":"templates",menuDataName:"templates-menu",verticalDropDirection:c?lt.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:c?lt.HorizontalDropDirection.FromLeftToRight:void 0,horizontalAttachEdge:c?lt.HorizontalAttachEdge.Left:void 0,verticalAttachEdge:c?lt.VerticalAttachEdge.Top:void 0,ref:_,...d},i.createElement(Q.ActionsTable,{items:f}))}var Et=o(792535),xt=o(185491),St=o(624202),Pt=o(363810),Dt=o(214375),Lt=o(192063),Bt=o(493173),Wt=o(128245),At=o(787913);const kt=(0,Bt.mergeThemes)(Lt.DEFAULT_POPUP_MENU_ITEM_THEME,At),Mt=[8,12,20,32,48,64,80,96];function Nt(t){const{multipleProperty:e,title:o,undoText:n,propertyApplier:r,isToolbarFixed:s,className:a,isSmallScreen:l}=t,c=(0,D.useProperty)((0,u.ensureDefined)(e));return i.createElement(Y.ToolWidgetMenu,{className:a,arrow:!1,title:o,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`,verticalDropDirection:s?lt.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:s?lt.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:s?lt.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:s?lt.VerticalAttachEdge.Top:void 0,content:i.createElement("div",{className:f()(At.buttonWrap,l&&At.small)},i.createElement(K.Icon,{icon:Wt}),!l&&"mixed"!==c&&i.createElement("div",{className:At.buttonLabel},`${c}px`))},Mt.map((t=>i.createElement(Lt.PopupMenuItem,{key:t,theme:kt,label:`${t}px`,isActive:t===c,onClick:h,onClickArg:t}))));function h(t){t&&e&&(r.beginUndoMacro(n),e.setValue(t,void 0,{applyValue:(t,e)=>{r.setProperty(t,e,n)}}),r.endUndoMacro())}}function Rt(t){const{activeChartWidget:e,className:o,actionId:n}=t,r=e.model().selection().lineDataSources(),s=I((()=>new N.ActionsProvider(e).actionForLineTools(e.model().selection().lineDataSources(),n)),(t=>t?.destroy()),[e,r]);if(!s)return null;const{label:a,icon:l}=s.options();return i.createElement(W,{className:o,tooltip:a,icon:l,onClick:()=>s.execute(),"data-name":`toggle-insert-cells-button-${n}`})}const It=!a.enabled("widget")||window.is_authenticated,Ft=new s.TranslatedString("change line tool(s) font size",r.t(null,void 0,o(237453))),Ot=new s.TranslatedString("change line tool(s) line style",r.t(null,void 0,o(413423))),Vt=new s.TranslatedString("change line tool(s) line width",r.t(null,void 0,o(81303))),zt=new s.TranslatedString("line tool(s) line style",r.t(null,{context:"line tool property name"
},o(664974))),Ht=r.t(null,void 0,o(232514)),Ut=r.t(null,void 0,o(199919)),Zt=r.t(null,void 0,o(767410)),jt=r.t(null,void 0,o(904543)),Gt=r.t(null,void 0,o(437117)),$t=r.t(null,void 0,o(492516)),Kt=r.t(null,void 0,o(702573)),qt=r.t(null,void 0,o(353002)),Xt=r.t(null,void 0,o(475056));function Yt(t){return"LineToolBrush"===t||"LineToolHighlighter"===t}const Qt=H.CheckMobile.any()||H.CheckMobile.isIPad();class Jt{constructor(t){this._isDrawingFinished=new l.WatchedValue(!0),this._currentTool=null,this._updateVisibilityTimeout=null,this._lineWidthsProperty=null,this._lineColorsProperty=null,this._highlighterWidthsProperty=null,this._currentProperties=null,this._floatingContainer=null,this._floatingToolbarRendered=!1,this._toolbarVisible=!1,this._propertiesVisible=!1,this._templatesButton=null,this._cancelButton=null,this._propertyButtons=[],this._commonButtons=[],this._actionCommonButtons=[],this._handleSourceEdit=t=>{h.isDirectionalMovementActive.value()||(t?this._floatingToolbar.hide(!0):this._floatingToolbarRendered&&this._floatingToolbar.show())},this._chartWidgetCollection=t,this._floatingToolbar=new b.FloatingToolbar({defaultPosition:{top:vt.HEADER_TOOLBAR_HEIGHT_EXPANDED+15,left:window.innerWidth/2},positionSettingsKey:"properties_toolbar.position",positionStorageType:"device",layout:"horizontal","data-name":"drawing-toolbar"}),this._floatingContainer=this._floatingToolbar.getReactWidgetContainer(),this._isToolMovingNowSpawn=h.isToolMovingNow.spawn(),this._isToolEditingNowSpawn=h.isToolEditingNow.spawn(),this._toolSpawn=h.tool.spawn(),this._iconToolSpawn=h.iconTool.spawn(),this._emojiToolSpawn=h.emojiTool.spawn(),this._selectedSourcesSpawn=this._chartWidgetCollection.selectedSources.spawn(),this._isToolMovingNowSpawn.subscribe(this._handleSourceEdit),this._isToolEditingNowSpawn.subscribe(this._handleSourceEdit),this._toolSpawn.subscribe((t=>this._onToolChanged(t)),{callWithLast:!0}),this._iconToolSpawn.subscribe((()=>this._onToolChanged(h.tool.value()))),this._emojiToolSpawn.subscribe((()=>this._onToolChanged(h.tool.value()))),this._selectedSourcesSpawn.subscribe((()=>this.refresh())),this._chartWidgetCollection.onAboutToBeDestroyed.subscribe(this,this.destroy,!0)}destroy(){this._isToolMovingNowSpawn.destroy(),this._isToolEditingNowSpawn.destroy(),this._toolSpawn.destroy(),this._iconToolSpawn.destroy(),this._emojiToolSpawn.destroy(),this._selectedSourcesSpawn.destroy()}async refresh(){this._onSourceChanged(this.selectedSources())}activeChartWidget(){return this._chartWidgetCollection.activeChartWidget.value()}selectedSources(){return this._chartWidgetCollection.selectedSources.value().filter(ft.isLineTool)}hide(){this._updateVisibilityTimeout&&clearTimeout(this._updateVisibilityTimeout),this._updateVisibilityTimeout=setTimeout((()=>{(0,ft.unsetNewToolProperties)(!0),this._floatingToolbar.hide(!0),this._isToolbarRendered()&&this._unmountFloatingToolbar(),this._clearProperties(),this._clearCommonButtons(),this._clearActionCommonButtons(),this._clearCancelButtons()}),0),delete this._propertyApplier}templatesList(){
return this._templatesList}async _onToolChanged(t,e){await(0,xt.ensureLineToolLoaded)(t),this._currentTool=t;const o=this.selectedSources();if(this._isDrawingToolExcludingCustomUrlEventTool(t)){if(this._isDrawingFinished.setValue(!1),It){const o=e instanceof Et.DefaultProperty?e:(0,ft.setNewToolProperties)(t,"LineToolIcon"===t?h.iconTool.value():"LineToolEmoji"===t?h.emojiTool.value():void 0,this._chartWidgetCollection.activeChartWidget.value().model().model());this.showPropertiesOf(t,o,e instanceof Et.DefaultProperty),this._showTemplatesOf({tool:t,properties:o}),this._toolbarVisible=!0}this._createCancelButton(),this._updateVisibility()}else o&&o.length?(o.length>1&&this._isDrawingFinished.setValue(!0),this.refresh()):this.hide()}async _onSourceChanged(t){if(!t?.length)return this._propertiesVisible=!1,this._toolbarVisible=!1,void this.hide();if(await Promise.all(t.map((async t=>(0,xt.ensureLineToolLoaded)(t.toolname)))),this._createActionCommonButtons(),this._createCommonButtons(),this._createCancelButton(),t.every((e=>e.toolname===t[0].toolname))?this._showTemplatesOf({sources:t}):this._templatesButton&&this._clearTemplatesButton(),1===t.length){const[e]=t;e.isAvailableInFloatingWidget()&&this.activeChartWidget().model().model().dataSourceForId(e.id())?(!e.userEditEnabled()||!(0,St.isLineDrawnWithPressedButton)(e.toolname)&&this.activeChartWidget().model().lineBeingCreated()||this._isDrawingFinished.setValue(!0),this.showPropertiesOf(e.toolname,e.properties(),!0),this._toolbarVisible=!0):this.hide()}else this._clearProperties(),this._createWidthsButton(void 0,!0),this._createHighlighterWidthsButton(void 0,!0),this._createLineStyleButton(),this._createColorsButton(void 0,!0),this._createBackgroundsButton(void 0,!0),this._createTextColorsButton(void 0,!0),this._propertiesVisible=!0;this._updateVisibility()}_propertyApplierImpl(){return this._propertyApplier||(this._propertyApplier=new Dt.PropertyApplierWithoutSavingChart((()=>this.activeChartWidget().model()))),this._propertyApplier}_clearProperties(){this._clearPropertyButtons(),this._lineWidthsProperty&&(this._lineWidthsProperty.destroy(),this._lineWidthsProperty=null),this._lineColorsProperty&&(this._lineColorsProperty.destroy(),this._lineColorsProperty=null),this._currentProperties&&(this._currentProperties=null)}_show(){this._updateVisibilityTimeout&&clearTimeout(this._updateVisibilityTimeout),this._updateVisibilityTimeout=setTimeout((()=>{this._renderFloatingToolbar(),this._floatingToolbar.show(),this._floatingToolbar.checkPosition()}),0)}_addPropertyButton(t){this._propertyButtons.push(t),this._renderFloatingToolbar()}_addCommonButton(t){this._commonButtons.push(t),this._renderFloatingToolbar()}_addActionCommonButton(t){this._actionCommonButtons.push(t),this._renderFloatingToolbar()}_addCancelButton(t){this._cancelButton=t,this._renderFloatingToolbar()}_addTemplatesButton(t){this._templatesButton=t}_renderFloatingToolbar(){null!==this._floatingContainer&&this.activeChartWidget()&&this.activeChartWidget().hasModel()&&(n.render(i.createElement(C,{
templateButton:this._templatesButton,cancelButton:this._cancelButton,propertyButtons:this._propertyButtons,commonButtons:this._commonButtons,actionButtons:this._actionCommonButtons,isDrawingFinished:this._isDrawingFinished.value(),activeChartWidget:this.activeChartWidget()}),this._floatingContainer),this._floatingToolbarRendered=!0)}_unmountFloatingToolbar(){null!==this._floatingContainer&&(n.unmountComponentAtNode(this._floatingContainer),this._floatingToolbarRendered=!1)}_clearTemplatesButton(){this._templatesButton=null}_clearPropertyButtons(){this._propertyButtons=[]}_clearCommonButtons(){this._commonButtons=[]}_clearActionCommonButtons(){this._actionCommonButtons=[]}_clearCancelButtons(){this._cancelButton=null}_isToolbarRendered(){return this._floatingToolbarRendered}_createSettingsButton(){const t={component:P,props:{title:Ht,activeChartWidget:this.activeChartWidget()}};this._addCommonButton(t)}_createLockButton(){const t={component:M,props:{title:"Lock",activeChartWidget:this.activeChartWidget()}};this._addCommonButton(t)}_createAnchorButton(){const t={component:O,props:{title:r.t(null,void 0,o(24185)),activeChartWidget:this.activeChartWidget()}};this._addCommonButton(t)}_createAlignTo45DegreesButton(){const t={component:j,props:{title:Ut,activeChartWidget:this.activeChartWidget()},showForSmallScreen:!0};this._addCommonButton(t)}_createRemoveButton(){if(Qt&&null!==this._currentTool&&Yt(this._currentTool))return;const t={component:z,props:{title:Zt,activeChartWidget:this.activeChartWidget()},showForSmallScreen:!0};this._addCommonButton(t)}_createCancelButton(){if(null!==this._cancelButton&&this._clearCancelButtons(),!Qt||null===this._currentTool)return;if(!Yt(this._currentTool))return;const t={component:$,props:{title:jt,activeChartWidget:this.activeChartWidget()},showForSmallScreen:!0};this._addCancelButton(t)}_createDotsButton(){this._addCommonButton({component:rt,props:{title:Gt,activeChartWidget:this.activeChartWidget()},showForSmallScreen:!0})}_createAlertButton(){}_createSourceActions(){this._createLockButton(),this._createAnchorButton()}_createActionCommonButtons(){this._actionCommonButtons.length&&this._clearActionCommonButtons();const t={component:Rt,props:{actionId:"Chart.SelectedObject.InsertColumnTable",activeChartWidget:this.activeChartWidget()}};this._addActionCommonButton(t);const e={component:Rt,props:{actionId:"Chart.SelectedObject.InsertRowTable",activeChartWidget:this.activeChartWidget()}};this._addActionCommonButton(e)}_createLineStyleButton(t){const e=this.selectedSources().filter(ft.isLineTool);if(!e.length)return!1;let o,i=this._propertyApplierImpl();if(1===e.length){if(o=e[0].properties().linesStyles||t,!o)return!1}else{const t=e.map((t=>t.properties().linestyle||t.properties().lineStyle||t.properties().linesStyles)).filter(c.notUndefined);if(!t.length)return!1;o=new d.CollectiblePropertyUndoWrapper(new p.LineToolCollectedProperty(t),zt,this._propertyApplierImpl()),i={...i,setProperty:(t,e)=>t.setValue(e)}}return this._addPropertyButton({component:ut,props:{property:o,title:$t,
propertyApplier:i,"data-name":"style",undoText:Ot}}),!0}_createFontSizeButton(t){const e=this.selectedSources();if(0===e.length)return!1;const o=e[0];if(!(0,Pt.isDataSource)(o))return!1;const i={component:_t,props:{property:o.properties().fontsize||t,title:Kt,propertyApplier:this._propertyApplierImpl(),"data-name":"font-size",undoText:Ft}};return this._addPropertyButton(i),!0}_createCommonButtons(){this._commonButtons.length&&this._clearCommonButtons(),a.enabled("property_pages")&&this._createSettingsButton(),this._createSourceActions(),this._createAlignTo45DegreesButton(),this._createRemoveButton(),this._createDotsButton()}_createHighlighterWidthsButton(t,e){if(this._highlighterWidthsProperty&&(this._highlighterWidthsProperty.destroy(),this._highlighterWidthsProperty=null),this._highlighterWidthsProperty=this._createProperty(t,e,"widths",Vt)||null,!this._highlighterWidthsProperty)return!0;const o=e&&1!==this.selectedSources().filter((t=>t.properties().widths)).length?Xt:qt;return this._addPropertyButton({component:Nt,props:{title:o,multipleProperty:this._highlighterWidthsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"highlighter-width",undoText:Vt}}),!0}_prepareProperties(t){const e=this.selectedSources().filter((e=>e.properties()[t]));if(!(e.filter((e=>e.properties()[t].visible())).length<1))return e.map((e=>e.properties()[t])).filter(c.notNull)}_createProperty(t,e,o,i){if(e){const t=this._prepareProperties(o);if(!t)return;return this._isWidthProperty(t[0])?new g(new p.MultipleLineWidthsProperty(t),this._propertyApplierImpl(),i):new _.CollectibleColorPropertyUndoWrapper(new p.MultipleLineColorsProperty(t),this._propertyApplierImpl(),i)}if(t&&t.visible())return this._isWidthProperty(t)?new p.MultipleLineWidthsProperty([t]):new _.CollectibleColorPropertyDirectWrapper(new p.MultipleLineColorsProperty([t]))}_shouldShowBackgroundProperty(t,e){return!e||!e.fillBackground||!!e.fillBackground.value()}_isDrawingToolExcludingCustomUrlEventTool(t){return Boolean(t?.toLowerCase().includes("linetool"))&&"LineToolTweet"!==t&&"LineToolIdea"!==t&&"LineToolImage"!==t}_updateVisibility(){(It||this._isDrawingFinished.value())&&(this._toolbarVisible||this._propertiesVisible)?this._show():this.hide()}_showTemplatesOf(t){if(this._templatesButton&&this._clearTemplatesButton(),!It)return;const e="sources"in t;if(e&&!t.sources.length)return;const o=e?t.sources[0].toolname:t.tool;let i;if(this._templatesList=new yt.LinetoolTemplatesList(o,(i=>{this.activeChartWidget().model().applyLineToolsTemplate({...t,data:i}),e?this._onSourceChanged(t.sources):this._onToolChanged(o,t.properties)})),e&&1===t.sources.length){const{sources:[e]}=t;i=()=>e.template()}this._addTemplatesButton({component:Ct,props:{key:"Templates",model:this._templatesList,onSave:a.enabled("drawing_templates")?i:void 0,onRestore:()=>{e?this.activeChartWidget().model().restoreLineToolsFactoryDefaults(t.sources):t.properties.restoreFactoryDefaults(),this.refresh()}}})}_isWidthProperty(t){return t instanceof p.LineToolWidthsProperty}}},816232:(t,e,o)=>{
"use strict";o.r(e),o.d(e,{LinetoolTemplatesList:()=>d});var i=o(609838),n=o(917568),r=o(279708),s=o(540642),a=o(190266),l=o(153055),c=o(78943);const h=o(440891).enabled("drawing_templates");class d{constructor(t,e){this._toolName=t,this._applyTemplate=e,this._templatesDeferred=h?this._loadData():Promise.resolve()}getData(){return n.store.getState().templates[this._toolName]}templatesLoaded(){return this._templatesDeferred}loadTemplate(t,e){n.store.dispatch((0,r.loadTemplate)(this._toolName,t,(async t=>{this._applyTemplate(t),e?.()})))}removeTemplate(t){n.store.dispatch((0,r.startRemoveTemplate)(this._toolName,t))}saveTemplate(t,e){const o=(0,s.clean)(t);n.store.dispatch((0,r.saveTemplate)(this._toolName,o,e))}deleteAction(t){(0,a.runOrSignIn)((()=>{const e=i.t(null,{replace:{name:t}},o(57285));(0,l.showConfirm)({text:e,onConfirm:e=>{this.removeTemplate(t),e.dialogClose()}})}),{source:"Delete line tool template"})}showSaveDialog(t){(0,a.runOrSignIn)((()=>{(0,l.showRename)({title:i.t(null,void 0,o(776047)),text:i.t(null,void 0,o(59233))+":",maxLength:64,source:this.getData()||[],autocompleteFilter:c.autocompleteFilter,onRename:e=>{if(-1!==(this.getData()||[]).indexOf(e.newValue)){const n=i.t(null,{replace:{name:e.newValue}},o(623276));(0,l.showConfirm)({text:n,onConfirm:o=>{t(e.newValue),o.dialogClose(),e.dialogClose()},onClose:e.focusInput},e.innerManager)}else t(e.newValue),e.dialogClose()}})}),{source:"Save line tool template",sourceMeta:"Chart"})}async _loadData(){return new Promise((t=>{this.getData()?t():n.store.dispatch((0,r.getTemplates)(this._toolName,t))}))}}},49337:(t,e,o)=>{"use strict";var i=o(272047).TranslatedString,n=o(53266).LineToolPropertiesWidgetBase;const r=o(523847).ColorPickerButton,s=o(822598).LineWidthButton;var a=o(405880),l=o(621065),c=o(448984),h=new i("change line tool(s) color",o.i18next(null,void 0,o(578655))),d=new i("change line tool(s) background color",o.i18next(null,void 0,o(150522))),p=new i("change line tool(s) text color",o.i18next(null,void 0,o(196142))),u=new i("change line tool(s) line width",o.i18next(null,void 0,o(81303))),m=o.i18next(null,void 0,o(747370)),g=o.i18next(null,void 0,o(211989)),_=o.i18next(null,void 0,o(77753)),v=o.i18next(null,void 0,o(681956)),f=o.i18next(null,void 0,o(369715)),y=o.i18next(null,void 0,o(614097)),b=o.i18next(null,void 0,o(492516)),w=o.i18next(null,void 0,o(992409)),T=o.i18next(null,void 0,o(353002)),C=o.i18next(null,void 0,o(475056)),E=o.i18next(null,void 0,o(128736)),x=o.i18next(null,void 0,o(265086)),S=o.i18next(null,void 0,o(346193)),P=o.i18next(null,void 0,o(307977)),D=o.i18next(null,void 0,o(908111));class L extends n{constructor(t){super(t),this._templatesButton=null}_createWidthsButton(t,e){if(this._lineWidthsProperty&&(this._lineWidthsProperty.destroy(),this._lineWidthsProperty=null),this._lineWidthsProperty=this._createProperty(t,e,"linesWidths",u),!this._lineWidthsProperty)return!0;var o=T;e&&(1!==this.selectedSources().filter((t=>t.properties().linesWidths)).length&&(o=C));return this._addPropertyButton({component:s,props:{title:o,
multipleProperty:this._lineWidthsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"line-tool-width",undoText:u}}),!0}_createColorsButton(t,e){return this._lineColorsProperty&&(this._lineColorsProperty.destroy(),this._lineColorsProperty=null),this._lineColorsProperty=this._createProperty(t,e,"linesColors",h),!this._lineColorsProperty||(this._addPropertyButton({component:r,props:{icon:a,title:g,property:this._lineColorsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"line-tool-color",undoText:h}}),!0)}_createBackgroundsButton(t,e){return this._backgroundsProperty&&(this._backgroundsProperty.destroy(),this._backgroundsProperty=null),this._backgroundsProperty=this._createProperty(t,e,"backgroundsColors",d),!this._backgroundsProperty||(this._addPropertyButton({component:r,props:{icon:l,title:y,property:this._backgroundsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"background-color",undoText:d}}),!0)}_createTextColorsButton(t,e){return this._textColorsProperty&&(this._textColorsProperty.destroy(),this._textColorsProperty=null),this._textColorsProperty=this._createProperty(t,e,"textsColors",p),!this._textColorsProperty||(this._addPropertyButton({component:r,props:{icon:c,title:v,property:this._textColorsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"text-color",undoText:p}}),!0)}_getPossibleProperty(t){for(var e=[],o=this._defaultToolProperties(),i=0;i<o.length;i++){var n=o[i];n.name in t&&e.push(n)}return e}showPropertiesOf(t,e,o){this._toolExceptionCases||(this._toolExceptionCases=this._createToolExceptionCases());var i=this._toolExceptionCases[t]||this._getPossibleProperty(e);if(this._clearProperties(),this._propertiesVisible=!1,i.length){for(var n={},s=0;s<i.length;s++){for(var a=i[s],l=e,c=a.name.split("."),h=0;h<c.length;++h)l=l&&l[c[h]];var d=a.showIf;if("function"!=typeof d||d(l,e)){var p=a.factory;if(p&&p.call(this,l,o))continue;if(!l)continue;if(this._propertiesVisible=!0,"combobox"!==a.inputType){const t={component:r,props:{icon:a.iconSvgCode,title:a.title,"data-name":a.dataName,property:l,propertyApplier:this._propertyApplierImpl(),undoText:a.undoText}};this._addPropertyButton(t);continue}n[a.name]=l}}this._currentProperties=n}}_defaultToolProperties(){return[this._defaultLinesColorProperty(),{name:"backgroundsColors",inputType:"colorPicker",iconSvgCode:l,title:f,factory:L.prototype._createBackgroundsButton,dataName:"background-color",showIf:this._shouldShowBackgroundProperty},{name:"textsColors",title:_,inputType:"colorPicker",iconSvgCode:c,factory:L.prototype._createTextColorsButton,dataName:"text-color"},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton},{name:"linesStyles",title:b,inputType:"combobox",factory:L.prototype._createLineStyleButton}]}_regressionToolExceptionCases(){return[{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton}]}_pathExceptionCases(){return[this._defaultLinesColorProperty(),{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton
},{name:"lineStyle",title:b,inputType:"combobox",factory:L.prototype._createLineStyleButton}]}_riskPropertiesExceptionCases(){return[{name:"textcolor",title:_,inputType:"colorPicker",iconSvgCode:c,dataName:"text-color",undoText:p},{name:"profitBackground",title:E,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:d},{name:"stopBackground",title:x,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:d}]}_rangeExceptionCases(){return[{name:"linecolor",inputType:"colorPicker",iconSvgCode:a,title:m,dataName:"line-tool-color",undoText:h},{name:"backgroundColor",inputType:"colorPicker",iconSvgCode:l,title:f,dataName:"background-color",showIf:this._shouldShowBackgroundProperty,undoText:d},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton}]}_brushPropertiesExceptionCase(){return[this._defaultLinesColorProperty(),{name:"backgroundsColors",inputType:"colorPicker",iconSvgCode:l,title:f,dataName:"background-color",factory:L.prototype._createBackgroundsButton},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton}]}_highlighterPropertiesExceptionCase(){return[this._defaultLinesColorProperty(),{name:"widths",inputType:"combobox",factory:L.prototype._createHighlighterWidthsButton}]}_bezierPropertiesExceptionCases(){return[this._defaultLinesColorProperty(),{name:"backgroundsColors",inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",title:f,factory:L.prototype._createBackgroundsButton,showIf:this._shouldShowBackgroundProperty},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton},{name:"linesStyles",title:b,inputType:"combobox",factory:L.prototype._createLineStyleButton}]}_textPropertiesExceptionCases(){return[{name:"color",title:_,inputType:"colorPicker",iconSvgCode:c,dataName:"text-color",undoText:p},{name:"backgroundColor",title:f,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",showIf:this._shouldShowBackgroundProperty,undoText:d},{name:"fontsize",title:w,inputType:"combobox",factory:L.prototype._createFontSizeButton}]}_notePropertiesExceptionCases(){return[{name:"markerColor",title:S,inputType:"colorPicker",iconSvgCode:a,dataName:"line-tool-color",undoText:h},{name:"textColor",title:_,inputType:"colorPicker",iconSvgCode:c,dataName:"text-color",undoText:p},{name:"fontSize",title:w,inputType:"combobox",factory:L.prototype._createFontSizeButton}]}_createToolExceptionCases(){return{LineToolBrush:L.prototype._brushPropertiesExceptionCase(),LineToolBezierQuadro:L.prototype._bezierPropertiesExceptionCases(),LineToolBezierCubic:L.prototype._bezierPropertiesExceptionCases(),LineToolText:L.prototype._textPropertiesExceptionCases(),LineToolTextAbsolute:L.prototype._textPropertiesExceptionCases(),LineToolBalloon:L.prototype._textPropertiesExceptionCases(),LineToolComment:L.prototype._textPropertiesExceptionCases(),LineToolCallout:L.prototype._textPropertiesExceptionCases(),LineToolPriceLabel:L.prototype._textPropertiesExceptionCases(),
LineToolDateRange:L.prototype._rangeExceptionCases(),LineToolPriceRange:L.prototype._rangeExceptionCases(),LineToolDateAndPriceRange:L.prototype._rangeExceptionCases(),LineToolNote:L.prototype._notePropertiesExceptionCases(),LineToolNoteAbsolute:L.prototype._notePropertiesExceptionCases(),LineToolRiskRewardLong:L.prototype._riskPropertiesExceptionCases(),LineToolRiskRewardShort:L.prototype._riskPropertiesExceptionCases(),LineToolPath:L.prototype._pathExceptionCases(),LineToolRegressionTrend:L.prototype._regressionToolExceptionCases(),LineToolBarsPattern:[{name:"color",title:m,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:h}],LineToolProjection:[{name:"color1",title:P,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:d},{name:"color2",title:D,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:d},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton}],LineToolSignpost:[{name:"linesColors",inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",title:m,factory:L.prototype._createBackgroundsButton,showIf:function(t,e){return e&&e.showImage.value()}},{name:"fontSize",title:w,inputType:"combobox",factory:L.prototype._createFontSizeButton}],LineToolHighlighter:L.prototype._highlighterPropertiesExceptionCase()}}_defaultLinesColorProperty(){return{name:"linesColors",inputType:"colorPicker",iconSvgCode:a,title:m,factory:L.prototype._createColorsButton,dataName:"line-tool-color"}}}t.exports=L},403933:(t,e,o)=>{"use strict";o.d(e,{TradingLayoutBreakpoint:()=>n});var i=o(139535);const n={Mobile:i.mobile}},279708:(t,e,o)=>{"use strict";o.d(e,{addTemplate:()=>s,getTemplates:()=>n,loadTemplate:()=>h,removeTemplate:()=>l,saveTemplate:()=>c,setTemplates:()=>r,startRemoveTemplate:()=>a});var i=o(667259);function n(t,e){return{type:i.GET_TEMPLATES,toolName:t,callback:e}}function r(t,e){return{type:i.SET_TEMPLATES,templates:e,toolName:t}}function s(t,e){return{type:i.ADD_TEMPLATE,templateName:e,toolName:t}}function a(t,e){return{type:i.START_REMOVE_TEMPLATE,templateName:e,toolName:t}}function l(t,e){return{type:i.REMOVE_TEMPLATE,templateName:e,toolName:t}}function c(t,e,o){return{type:i.SAVE_TEMPLATE,templateName:e,toolName:t,content:o}}function h(t,e,o){return{type:i.LOAD_TEMPLATE,toolName:t,templateName:e,callback:o}}},667259:(t,e,o)=>{"use strict";function i(t){return"LINE_TOOL_TEMPLATE__"+t}o.d(e,{ADD_TEMPLATE:()=>c,GET_TEMPLATES:()=>n,LOAD_TEMPLATE:()=>h,REMOVE_TEMPLATE:()=>a,SAVE_TEMPLATE:()=>l,SET_TEMPLATES:()=>r,START_REMOVE_TEMPLATE:()=>s});const n=i("GET_TEMPLATES"),r=i("SET_TEMPLATES"),s=i("START_REMOVE_TEMPLATE"),a=i("REMOVE_TEMPLATE"),l=i("SAVE_TEMPLATE"),c=i("ADD_TEMPLATE"),h=i("LOAD_TEMPLATE")},917568:(t,e,o)=>{"use strict";o.d(e,{store:()=>T});var i=o(406047),n=o(746212),r=o(129885),s=o(650151),a=o(667259),l=o(671945),c=o(279708);function h(t,e){return e}var d=o(301341);const p=(0,l.getLogger)("Chart.LineToolTemplatesList");function u(t,e){return e}function*m(){for(;;){
const{toolName:t,templateName:e,content:o}=u(a.SAVE_TEMPLATE,yield(0,r.take)(a.SAVE_TEMPLATE));try{yield(0,r.call)(d.backend.saveDrawingTemplate,t,e,o),yield(0,r.put)((0,c.addTemplate)(t,e))}catch(t){p.logWarn(t)}}}function*g(){for(;;){const{toolName:t,templateName:e}=u(a.START_REMOVE_TEMPLATE,yield(0,r.take)(a.START_REMOVE_TEMPLATE));try{yield(0,r.call)(d.backend.removeDrawingTemplate,t,e),yield(0,r.put)((0,c.removeTemplate)(t,e))}catch(t){p.logWarn(t)}}}function*_(){const t=new Map;for(;;){const{toolName:o,callback:i}=u(a.GET_TEMPLATES,yield(0,r.take)(a.GET_TEMPLATES));t.has(o)?(0,s.ensureDefined)(t.get(o)).push(i):(t.set(o,[i]),yield(0,r.fork)(e,o))}function*e(e){try{const t=h(d.backend.getDrawingTemplates,yield(0,r.call)(d.backend.getDrawingTemplates,e));yield(0,r.put)((0,c.setTemplates)(e,t))}catch(t){p.logWarn(t)}(0,s.ensureDefined)(t.get(e)).forEach((t=>t?.())),t.delete(e)}}function*v(){for(;;){const{toolName:t,templateName:e,callback:o}=u(a.LOAD_TEMPLATE,yield(0,r.take)(a.LOAD_TEMPLATE));try{const i=h(d.backend.loadDrawingTemplate,yield(0,r.call)(d.backend.loadDrawingTemplate,t,e));o&&o(i)}catch(t){p.logWarn(t)}}}function*f(){yield(0,r.all)([(0,r.call)(_),(0,r.call)(m),(0,r.call)(g),(0,r.call)(v)])}const y={templates:{}};function b(t,e){return t.localeCompare(e,void 0,{numeric:!0})}function w(t=y,e){switch(e.type){case a.ADD_TEMPLATE:{const{toolName:o,templateName:i}=e;if(!t.templates[o].includes(i)){const e=[...t.templates[o],i].sort(b);return{...t,templates:{...t.templates,[o]:e}}}return t}case a.SET_TEMPLATES:{const{toolName:o,templates:i}=e;return{...t,templates:{...t.templates,[o]:[...i].sort(b)}}}case a.REMOVE_TEMPLATE:{const{toolName:o,templateName:i}=e;return{...t,templates:{...t.templates,[o]:t.templates[o].filter((t=>t!==i))}}}default:return t}}const T=function(){const t=(0,n.default)(),e=(0,i.createStore)(w,(0,i.applyMiddleware)(t));return t.run(f),e}()},543259:(t,e,o)=>{"use strict";o.d(e,{CollectibleColorPropertyDirectWrapper:()=>a,CollectibleColorPropertyUndoWrapper:()=>s});var i=o(650151),n=o(571772);class r extends n.Property{constructor(t){super(),this._listenersMappers=[],this._isProcess=!1,this._baseProperty=t}destroy(){this._baseProperty.destroy(),super.destroy()}value(){const t=this._baseProperty.value();return"mixed"===t?"":t}visible(){return this._baseProperty.visible()}setValue(t){this._isProcess=!0,this._baseProperty.setValue(""===t?"mixed":t,void 0,{applyValue:this._applyValue.bind(this)}),this._isProcess=!1,this._listenersMappers.forEach((t=>{t.method.call(t.obj,this,"")}))}subscribe(t,e){const o=o=>{this._isProcess||e.call(t,this,"")},i={obj:t,method:e,callback:o};this._listenersMappers.push(i),this._baseProperty.subscribe(t,o)}unsubscribe(t,e){const o=(0,i.ensureDefined)(this._listenersMappers.find((o=>o.obj===t&&o.method===e))?.callback);this._baseProperty.unsubscribe(t,o)}unsubscribeAll(t){this._baseProperty.unsubscribeAll(t)}}class s extends r{constructor(t,e,o){super(t),this._propertyApplier=e,this._undoText=o}_applyValue(t,e){
this._propertyApplier.setProperty(t,e,this._undoText)}}class a extends r{_applyValue(t,e){t.setValue(e)}}},622860:(t,e,o)=>{"use strict";o.d(e,{CollectiblePropertyUndoWrapper:()=>l});var i=o(650151),n=o(609838),r=o(272047),s=o(571772);const a=new r.TranslatedString("change {propertyName} property",n.t(null,void 0,o(925167)));class l extends s.Property{constructor(t,e,o){super(),this._isProcess=!1,this._listenersMappers=[],this._valueApplier={applyValue:(t,e)=>{this._propertyApplier.setProperty(t,e,a)}},this._baseProperty=t,this._propertyApplier=o,this._propertyName=e}destroy(){this._baseProperty.destroy(),super.destroy()}value(){return this._baseProperty.value()}setValue(t,e){this._propertyApplier.beginUndoMacro(a.format({propertyName:this._propertyName})),this._isProcess=!0,this._baseProperty.setValue(t,void 0,this._valueApplier),this._isProcess=!1,this._propertyApplier.endUndoMacro(),this._listenersMappers.forEach((t=>{t.method.call(t.obj,this,"")}))}subscribe(t,e){const o=()=>{this._isProcess||e.call(t,this,"")};this._listenersMappers.push({obj:t,method:e,callback:o}),this._baseProperty.subscribe(t,o)}unsubscribe(t,e){const o=(0,i.ensureDefined)(this._listenersMappers.find((o=>o.obj===t&&o.method===e))?.callback);this._baseProperty.unsubscribe(t,o)}unsubscribeAll(t){this._baseProperty.unsubscribeAll(t)}}},214375:(t,e,o)=>{"use strict";o.d(e,{PropertyApplierWithoutSavingChart:()=>n});var i=o(979910);class n{constructor(t){this._undoModelSupplier=t}setProperty(t,e,o){this._undoModelSupplier().setProperty(t,e,o,i.lineToolsDoNotAffectChartInvalidation)}beginUndoMacro(t){return this._undoModelSupplier().beginUndoMacro(t)}endUndoMacro(){this._undoModelSupplier().endUndoMacro()}setWatchedValue(t,e,o){this._undoModelSupplier().undoHistory().setWatchedValue(t,e,o,i.lineToolsDoNotAffectChartInvalidation)}}},78943:(t,e,o)=>{"use strict";function i(t,e){return Boolean(""===t||t&&-1!==e.toLowerCase().indexOf(t.toLowerCase()))}o.d(e,{autocompleteFilter:()=>i})},405880:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16"><path fill="currentColor" d="M10.62.72a2.47 2.47 0 0 1 3.5 0l1.16 1.16c.96.97.96 2.54 0 3.5l-.58.58-8.9 8.9-1 1-.14.14H0v-4.65l.14-.15 1-1 8.9-8.9.58-.58Zm2.8.7a1.48 1.48 0 0 0-2.1 0l-.23.23 3.26 3.26.23-.23c.58-.58.58-1.52 0-2.1l-1.16-1.16Zm.23 4.2-3.26-3.27-8.2 8.2 3.25 3.27 8.2-8.2Zm-8.9 8.9-3.27-3.26-.5.5V15h3.27l.5-.5Z"/></svg>'},953054:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M12.56 16.15 6.71 22h8.28a9.46 9.46 0 0 0-2.43-5.85Zm.71-.71 8.08-8.09-.7-.7-15.5 15.5-.86.85H24v-1h-8.01a10.46 10.46 0 0 0-2.72-6.56Z"/></svg>'},698486:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M21.35 7.35a.5.5 0 0 0-.7-.7L14 13.29 7.35 6.65a.5.5 0 1 0-.7.7L13.29 14l-6.64 6.65a.5.5 0 0 0 .7.7L14 14.71l6.65 6.64a.5.5 0 0 0 .7-.7L14.71 14l6.64-6.65z"/></svg>'},100578:t=>{
t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},844996:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z"/></svg>'},123851:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z"/></svg>'},357740:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M3 13h2v2H3v-2Zm5 0h2v2H8v-2Zm7 0h-2v2h2v-2Zm3 0h2v2h-2v-2Zm7 0h-2v2h2v-2Z"/></svg>'},780427:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M23 8H5V7h18v1ZM9 14H5v-1h4v1Zm3 0h4v-1h-4v1Zm11 0h-4v-1h4v1ZM7 19H5v2h2v-2Zm2 0h2v2H9v-2Zm6 0h-2v2h2v-2Zm2 0h2v2h-2v-2Zm6 0h-2v2h2v-2Z"/></svg>'},200501:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4 13.5h20"/></svg>'},636296:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8 9.5H6.5a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V20m-8-1.5h11a1 1 0 0 0 1-1v-11a1 1 0 0 0-1-1h-11a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1z"/></svg>'},621065:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20" fill="none"><path stroke="currentColor" d="M13.5 6.5l-3-3-7 7 7.59 7.59a2 2 0 0 0 2.82 0l4.18-4.18a2 2 0 0 0 0-2.82L13.5 6.5zm0 0v-4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v6"/><path fill="currentColor" d="M0 16.5C0 15 2.5 12 2.5 12S5 15 5 16.5 4 19 2.5 19 0 18 0 16.5z"/><circle fill="currentColor" cx="9.5" cy="9.5" r="1.5"/></svg>'},25388:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8 12" width="8" height="12" fill="currentColor"><rect width="2" height="2" rx="1"/><rect width="2" height="2" rx="1" y="5"/><rect width="2" height="2" rx="1" y="10"/><rect width="2" height="2" rx="1" x="6"/><rect width="2" height="2" rx="1" x="6" y="5"/><rect width="2" height="2" rx="1" x="6" y="10"/></svg>'},128245:t=>{
t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M11.68 9.74a95.6 95.6 0 0 0-5.76 7.56l-.84-.56c.51-.76 3.05-4.4 5.84-7.65a33.25 33.25 0 0 1 4.16-4.22c.66-.52 1.3-.94 1.89-1.17.59-.23 1.22-.3 1.76 0 .6.33.75.99.75 1.61a8.7 8.7 0 0 1-.42 2.3 56.38 56.38 0 0 1-2.33 5.98l-.42.97c-.78 1.78-1.52 3.48-2 4.86-.3.81-.49 1.48-.55 1.96a1.57 1.57 0 0 0 .01.57c.06.02.17.04.42-.08.28-.13.62-.38 1.02-.75.8-.73 1.71-1.82 2.66-2.94l.07-.08c.9-1.08 1.84-2.2 2.67-2.98.43-.4.85-.74 1.26-.95.4-.2.9-.33 1.35-.1.47.24.63.68.65 1.1.02.41-.1.88-.24 1.34-.14.47-.34 1-.55 1.53l-.02.05-.59 1.59c-.37 1.12-.57 2.12-.39 2.88.09.36.26.66.55.9.3.24.76.45 1.45.57l-.16.98c-.81-.13-1.45-.4-1.92-.78a2.6 2.6 0 0 1-.9-1.44c-.25-1.05.05-2.3.43-3.43.18-.55.4-1.1.6-1.62l.02-.06c.2-.54.4-1.03.53-1.46.14-.45.2-.78.2-1-.02-.2-.07-.24-.1-.25-.04-.02-.16-.05-.45.1-.29.14-.63.4-1.03.78-.8.74-1.7 1.82-2.62 2.92l-.05.05a34.79 34.79 0 0 1-2.74 3.04c-.43.39-.86.72-1.27.92-.41.19-.9.29-1.36.02a1.04 1.04 0 0 1-.48-.7 2.52 2.52 0 0 1-.01-.84c.07-.6.3-1.35.59-2.18.5-1.4 1.26-3.15 2.04-4.93l.41-.96c.92-2.11 1.8-4.2 2.29-5.87.24-.83.37-1.51.38-2.01 0-.52-.14-.68-.23-.74-.17-.1-.45-.12-.92.06-.46.18-1 .53-1.63 1.02a32.32 32.32 0 0 0-4.02 4.09Zm2.1 12.21Z"/></svg>'},222978:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 1" width="18" height="1"><rect width="18" height="1" fill="currentColor" rx=".5"/></svg>'},114631:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 2" width="18" height="2"><rect width="18" height="2" fill="currentColor" rx="1"/></svg>'},206096:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 3" width="18" height="3"><rect width="18" height="3" fill="currentColor" rx="1.5"/></svg>'},206483:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 4" width="18" height="4"><rect width="18" height="4" fill="currentColor" rx="2"/></svg>'},266611:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><rect width="18" height="2" rx="1" x="5" y="14"/><rect width="18" height="1" rx=".5" x="5" y="20"/><rect width="18" height="3" rx="1.5" x="5" y="7"/></svg>'},608280:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none" stroke="currentColor"><path stroke-linecap="round" d="M15.5 18.5h6m-3 3v-6"/><rect width="6" height="6" rx="1.5" x="6.5" y="6.5"/><rect width="6" height="6" rx="1.5" x="15.5" y="6.5"/><rect width="6" height="6" rx="1.5" x="6.5" y="15.5"/></svg>'},448984:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 15" width="13" height="15" fill="none"><path stroke="currentColor" d="M4 14.5h2.5m2.5 0H6.5m0 0V.5m0 0h-5a1 1 0 0 0-1 1V4m6-3.5h5a1 1 0 0 1 1 1V4"/></svg>'},382665:(t,e,o)=>{"use strict";o.d(e,{default:()=>l});var i=o(638459),n=o(61833),r=o(256882),s=Math.ceil,a=Math.max;const l=function(t,e,o){e=(o?(0,n.default)(t,e,o):void 0===e)?1:a((0,r.default)(e),0)
;var l=null==t?0:t.length;if(!l||e<1)return[];for(var c=0,h=0,d=Array(s(l/e));c<l;)d[h++]=(0,i.default)(t,c,c+=e);return d}}}]);