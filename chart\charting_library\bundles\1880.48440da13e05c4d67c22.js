(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1880],{510555:e=>{e.exports={wrapper:"wrapper-VB9J73Gf",focused:"focused-VB9J73Gf",readonly:"readonly-VB9J73Gf",disabled:"disabled-VB9J73Gf","size-small":"size-small-VB9J73Gf","size-medium":"size-medium-VB9J73Gf","size-large":"size-large-VB9J73Gf","font-size-small":"font-size-small-VB9J73Gf","font-size-medium":"font-size-medium-VB9J73Gf","font-size-large":"font-size-large-VB9J73Gf","border-none":"border-none-VB9J73Gf",shadow:"shadow-VB9J73Gf","border-thin":"border-thin-VB9J73Gf","border-thick":"border-thick-VB9J73Gf","intent-default":"intent-default-VB9J73Gf","intent-success":"intent-success-VB9J73Gf","intent-warning":"intent-warning-VB9J73Gf","intent-danger":"intent-danger-VB9J73Gf","intent-primary":"intent-primary-VB9J73Gf","corner-top-left":"corner-top-left-VB9J73Gf","corner-top-right":"corner-top-right-VB9J73Gf","corner-bottom-right":"corner-bottom-right-VB9J73Gf","corner-bottom-left":"corner-bottom-left-VB9J73Gf",childrenContainer:"childrenContainer-VB9J73Gf"}},61425:e=>{e.exports={defaultSelect:"defaultSelect-OM7V5ndi"}},114272:e=>{e.exports={itemWrap:"itemWrap-srH7jxJB",item:"item-srH7jxJB",icon:"icon-srH7jxJB",selected:"selected-srH7jxJB",label:"label-srH7jxJB"}},954159:e=>{e.exports={lineEndSelect:"lineEndSelect-gw7ESiZg",right:"right-gw7ESiZg"}},569552:e=>{e.exports={lineStyleSelect:"lineStyleSelect-GcXENVb4",multipleStyles:"multipleStyles-GcXENVb4"}},627391:e=>{e.exports={lineWidthSelect:"lineWidthSelect-EUDB1YgB",bar:"bar-EUDB1YgB",isActive:"isActive-EUDB1YgB",item:"item-EUDB1YgB"}},246558:e=>{e.exports={container:"container-dhpv13DH",active:"active-dhpv13DH",disabled:"disabled-dhpv13DH",icon:"icon-dhpv13DH"}},750219:e=>{e.exports={wrap:"wrap-b6_0ORMg",disabled:"disabled-b6_0ORMg"}},397546:e=>{e.exports={dropdown:"dropdown-gZlS9p6t",dropdownMenu:"dropdownMenu-gZlS9p6t",gradientColor:"gradientColor-gZlS9p6t",lineWidthSelect:"lineWidthSelect-gZlS9p6t"}},284001:e=>{e.exports={row:"row-nGXZ4vJz",empty:"empty-nGXZ4vJz",noMargins:"noMargins-nGXZ4vJz",wrap:"wrap-nGXZ4vJz",breakpointNormal:"breakpointNormal-nGXZ4vJz",breakpointMedium:"breakpointMedium-nGXZ4vJz",breakpointSmall:"breakpointSmall-nGXZ4vJz"}},46741:e=>{e.exports={coordinates:"coordinates-mb1bDWNb",input:"input-mb1bDWNb",selectionCoordinates:"selectionCoordinates-mb1bDWNb",selectionCoordinates__inputs:"selectionCoordinates__inputs-mb1bDWNb",selectionCoordinates__description:"selectionCoordinates__description-mb1bDWNb",hintButton:"hintButton-mb1bDWNb"}},223391:e=>{e.exports={wrapper:"wrapper-NVcHMTVy",checkbox:"checkbox-NVcHMTVy",colorSelect:"colorSelect-NVcHMTVy",hintButton:"hintButton-NVcHMTVy"}},997995:e=>{e.exports={withoutPadding:"withoutPadding-KtEcG0Q0"}},80509:e=>{e.exports={input:"input-mIsHGNhw",control:"control-mIsHGNhw",item:"item-mIsHGNhw",cell:"cell-mIsHGNhw",fragmentCell:"fragmentCell-mIsHGNhw",largeWidth:"largeWidth-mIsHGNhw",withTitle:"withTitle-mIsHGNhw",title:"title-mIsHGNhw",hidden:"hidden-mIsHGNhw"}},537458:e=>{e.exports={line:"line-j5rMaiWF",
control:"control-j5rMaiWF",valueInput:"valueInput-j5rMaiWF",valueUnit:"valueUnit-j5rMaiWF",input:"input-j5rMaiWF"}},206289:e=>{e.exports={unit:"unit-ZtRdVxiD",input:"input-ZtRdVxiD",normal:"normal-ZtRdVxiD",big:"big-ZtRdVxiD",dropdown:"dropdown-ZtRdVxiD",dropdownMenu:"dropdownMenu-ZtRdVxiD"}},469982:e=>{e.exports={optionalTwoColors:"optionalTwoColors-LDRcAXEV",colorPicker:"colorPicker-LDRcAXEV",dropdown:"dropdown-LDRcAXEV",dropdownMenu:"dropdownMenu-LDRcAXEV"}},611131:e=>{e.exports={dropdown:"dropdown-RxdEkbF0",normal:"normal-RxdEkbF0",big:"big-RxdEkbF0",dropdownMenu:"dropdownMenu-RxdEkbF0"}},635498:e=>{e.exports={range:"range-GLEBGed4",valueInput:"valueInput-GLEBGed4",rangeSlider:"rangeSlider-GLEBGed4",rangeSlider_mixed:"rangeSlider_mixed-GLEBGed4",input:"input-GLEBGed4",hintButton:"hintButton-GLEBGed4"}},274782:e=>{e.exports={select:"select-hJtsYZ3G",preContent:"preContent-hJtsYZ3G",wrap:"wrap-hJtsYZ3G",colorsWrap:"colorsWrap-hJtsYZ3G"}},63907:e=>{e.exports={colorPicker:"colorPicker-VK3h8amb",fontStyleButton:"fontStyleButton-VK3h8amb",dropdown:"dropdown-VK3h8amb",dropdownMenu:"dropdownMenu-VK3h8amb",hintButton:"hintButton-VK3h8amb",title:"title-VK3h8amb"}},195442:e=>{e.exports={twoColors:"twoColors-C2hZXnYv",colorPicker:"colorPicker-C2hZXnYv"}},326302:e=>{e.exports={dropdown:"dropdown-eLkGg0Ft",menu:"menu-eLkGg0Ft"}},536718:e=>{e.exports={"default-drawer-min-top-distance":"100px",wrap:"wrap-_HnK0UIN",positionBottom:"positionBottom-_HnK0UIN",backdrop:"backdrop-_HnK0UIN",drawer:"drawer-_HnK0UIN",positionLeft:"positionLeft-_HnK0UIN"}},927061:e=>{e.exports={buttonWrap:"buttonWrap-icygBqe7",desktopSize:"desktopSize-icygBqe7",drawer:"drawer-icygBqe7",menuBox:"menuBox-icygBqe7"}},101774:e=>{e.exports={btnContent:"btnContent-ivexqeZZ",contentPart:"contentPart-ivexqeZZ"}},840638:e=>{e.exports={checkbox:"checkbox-aOSYFxuH"}},700238:e=>{e.exports={separator:"separator-QjUlCDId",small:"small-QjUlCDId",normal:"normal-QjUlCDId",large:"large-QjUlCDId"}},273188:e=>{e.exports={range:"range-mFgGeMmT",disabled:"disabled-mFgGeMmT",rangeSlider:"rangeSlider-mFgGeMmT",rangeSliderMiddleWrap:"rangeSliderMiddleWrap-mFgGeMmT",rangeSliderMiddle:"rangeSliderMiddle-mFgGeMmT",dragged:"dragged-mFgGeMmT",pointer:"pointer-mFgGeMmT",rangePointerWrap:"rangePointerWrap-mFgGeMmT"}},35990:e=>{e.exports={button:"button-iLKiGOdQ",hovered:"hovered-iLKiGOdQ",disabled:"disabled-iLKiGOdQ",focused:"focused-iLKiGOdQ",active:"active-iLKiGOdQ",hidden:"hidden-iLKiGOdQ"}},232668:(e,t,n)=>{"use strict";n.d(t,{FontSizeSelect:()=>l});var d=n(50959),o=n(497754),i=n.n(o),r=n(529631),u=n(800417),a=n(61425);function l(e){const{id:t,fontSize:n,fontSizes:o=[],className:l,disabled:s,fontSizeChange:c}=e;return d.createElement(r.Select,{id:t,disabled:s,className:i()(l,a.defaultSelect),menuClassName:a.defaultSelect,items:(f=o,f.map((e=>({value:e.value,content:e.title})))),value:n,onChange:c,...(0,u.filterDataProps)(e)});var f}},865588:(e,t,n)=>{"use strict";n.d(t,{DisplayItem:()=>s,DropItem:()=>c,IconDropdown:()=>l})
;var d=n(50959),o=n(497754),i=n.n(o),r=n(529631),u=n(878112),a=n(114272);function l(e){const{menuItemClassName:t,...n}=e;return d.createElement(r.Select,{...n,menuItemClassName:i()(t,a.itemWrap)})}function s(e){return d.createElement("div",{className:i()(a.item,a.selected,e.className)},d.createElement(u.Icon,{className:a.icon,icon:e.icon}))}function c(e){return d.createElement("div",{className:a.item},d.createElement(u.Icon,{className:i()(a.icon,e.iconClassName),icon:e.icon}),d.createElement("div",{className:a.label},e.label))}},796855:(e,t,n)=>{"use strict";n.d(t,{LineStyleSelect:()=>s});var d=n(50959),o=n(497754),i=n.n(o),r=n(865588),u=n(377707),a=n(780427),l=n(569552);class s extends d.PureComponent{render(){const{id:e,lineStyle:t,className:n,lineStyleChange:o,disabled:s,additionalItems:c,allowedLineStyles:f}=this.props;let p=function(e){let t=[...u.lineStyleItemValues];return void 0!==e&&(t=t.filter((t=>e.includes(t.type)))),t.map((e=>({value:e.type,selectedContent:d.createElement(r.DisplayItem,{icon:e.icon}),content:d.createElement(r.DropItem,{icon:e.icon,label:e.label})})))}(f);return c&&(p=[{readonly:!0,content:c},...p]),d.createElement(r.IconDropdown,{id:e,disabled:s,className:i()(l.lineStyleSelect,n),hideArrowButton:!0,items:p,value:t,onChange:o,"data-name":"line-style-select",addPlaceholderToItems:!1,placeholder:d.createElement(r.DisplayItem,{icon:a,className:l.multipleStyles})})}}},334291:(e,t,n)=>{"use strict";n.d(t,{Transparency:()=>a});var d=n(50959),o=n(497754),i=n(939075),r=n(926048),u=n(750219);function a(e){const{value:t,disabled:n,onChange:a,className:l}=e;return d.createElement("div",{className:o(u.wrap,l,{[u.disabled]:n})},d.createElement(i.Opacity,{hideInput:!0,color:r.colorsPalette["color-tv-blue-500"],opacity:1-t/100,onChange:function(e){n||a(100-100*e)},disabled:n}))}},227570:(e,t,n)=>{"use strict";n.d(t,{useActiveDescendant:()=>i});var d=n(50959),o=n(718736);function i(e,t=[]){const[n,i]=(0,d.useState)(!1),r=(0,o.useFunctionalRefObject)(e);return(0,d.useLayoutEffect)((()=>{const e=r.current;if(null===e)return;const t=e=>{switch(e.type){case"active-descendant-focus":i(!0);break;case"active-descendant-blur":i(!1)}};return e.addEventListener("active-descendant-focus",t),e.addEventListener("active-descendant-blur",t),()=>{e.removeEventListener("active-descendant-focus",t),e.removeEventListener("active-descendant-blur",t)}}),t),[r,n]}},554393:(e,t,n)=>{"use strict";n.d(t,{LineWidthSelect:()=>s});var d=n(50959),o=n(497754),i=n(529631),r=n(627391);const u=[1,2,3,4];function a(e){const{id:t,value:n,items:a=u,disabled:l,onChange:s,className:c}=e;return d.createElement(i.Select,{id:t,disabled:l,hideArrowButton:!0,className:o(r.lineWidthSelect,c),items:(f=a,f.map((e=>({value:e,selectedContent:p(e,!0),content:p(e)})))),value:n,onChange:s,"data-name":"line-width-select"});var f;function p(e,t){const i={borderTopWidth:e};return d.createElement("div",{className:r.item},d.createElement("div",{className:o(r.bar,{[r.isActive]:e===n&&!t}),style:i}," "))}}var l=n(771586);function s(e){
const{property:t}=e,[n,o]=(0,l.useDefinitionProperty)({property:t});return d.createElement(a,{...e,value:n,onChange:o})}},633064:(e,t,n)=>{"use strict";n.d(t,{ControlCustomHeightContext:()=>u,ControlCustomWidthContext:()=>i});var d,o=n(50959);!function(e){e.Small="small",e.Normal="normal",e.Big="big"}(d||(d={}));const i=o.createContext({});var r;!function(e){e.Normal="normal",e.Big="big"}(r||(r={}));const u=o.createContext({})},767055:(e,t,n)=>{"use strict";n.d(t,{Section:()=>Wt});var d=n(50959),o=n(798346),i=n(193315),r=n(771586),u=n(955261);function a(e){const{definition:{id:t,properties:{checked:n,disabled:o,visible:i},title:a,solutionId:l,infoTooltip:s},offset:c}=e,[f]=(0,r.useDefinitionProperty)({property:o,defaultValue:!1}),[p]=(0,r.useDefinitionProperty)({property:i,defaultValue:!0});return p?d.createElement(u.CommonSection,{id:t,offset:c,checked:n,title:a,solutionId:l,infoTooltip:s,disabled:e.disabled||f}):null}var l=n(497754),s=n.n(l),c=n(414823),f=n(796855);function p(e){const{property:t}=e,[n,o]=(0,r.useDefinitionProperty)({property:t});return d.createElement(f.LineStyleSelect,{...e,lineStyle:n,lineStyleChange:o})}var m=n(554393),h=n(960521),b=n(650151);function v(e){return"mixed"===e}function y(e,t,n){const[o,i]=(0,d.useState)(e),r=(0,d.useRef)(o);return(0,d.useEffect)((()=>{i(e)}),[e,n]),[o,function(e){r.current=e,i(e)},function(){t(r.current)},function(){r.current=e,i(e)}]}var g,E=n(180185),w=n(576805),C=n(822960),D=n(563223),S=n(601227);function N(e){const{property:t,...n}=e,[o,i]=(0,d.useState)(performance.now()),[u,a]=(0,r.useDefinitionProperty)({property:t,handler:()=>i(performance.now())}),l=y(u,a,o);return d.createElement(x,{...n,valueHash:o,sharedBuffer:l})}function x(e){const{sharedBuffer:t,min:n,max:o,step:i,...r}=e,[u,a,l,s]=t,c=(0,d.useRef)(null),f=(0,d.useRef)(null),p={flushed:!1};return d.createElement(k,{...r,ref:f,onValueChange:function(e,t){a(e),"step"!==t||p.flushed||(l(),p.flushed=!0)},onKeyDown:function(e){if(e.defaultPrevented||p.flushed)return;switch((0,E.hashFromEvent)(e.nativeEvent)){case 27:s(),p.flushed=!0;break;case 13:e.preventDefault();const t=(0,b.ensureNotNull)(f.current).getClampedValue();null!==t&&(a(t),l(),p.flushed=!0)}},onBlur:function(e){const t=(0,b.ensureNotNull)(c.current);if(!t.contains(document.activeElement)&&!t.contains(e.relatedTarget)){const e=(0,b.ensureNotNull)(f.current).getClampedValue();null===e||p.flushed||(a(e),l(),p.flushed=!0)}},value:u,roundByStep:!1,containerReference:function(e){c.current=e},inputMode:S.CheckMobile.iOS()?void 0:"numeric",min:n,max:o,step:i,stretch:!1})}!function(e){e.Input="input",e.Step="step"}(g||(g={}));const V={mode:"float",min:-Number.MAX_VALUE,max:Number.MAX_VALUE,step:1,precision:0,inheritPrecisionFromStep:!0};class k extends d.PureComponent{constructor(e){super(e),this._selection=null,this._restoreSelection=!1,this._input=null,this._handleSelectionChange=()=>{this._restoreSelection||document.activeElement!==(0,b.ensureNotNull)(this._input)||this._saveSelection((0,b.ensureNotNull)(this._input))},
this._handleInputReference=e=>{this._input=e,this.props.inputReference&&this.props.inputReference(e)},this._onFocus=e=>{this._saveSelection((0,b.ensureNotNull)(this._input)),this.setState({focused:!0}),this.props.onFocus&&this.props.onFocus(e)},this._onBlur=e=>{this._selection=null,this.setState({displayValue:M(this.props,this.props.value,_(this.props)),focused:!1}),this.props.onBlur&&this.props.onBlur(e)},this._onValueChange=e=>{const t=e.currentTarget,n=t.value,d=function(e,t,n){switch(n){case"integer":return P.test(t)?t:e;case"float":return t=t.replace(/,/g,"."),T.test(t)?t:e;case"fractional":return I.test(t)?t:e}}(this.state.displayValue,n,this.props.mode),o=L(d),i=this._checkValueBoundaries(o);var r,u;this.setState({displayValue:d}),d!==n&&(r=this.state.displayValue,u=(u=d).replace(/,/g,"."),(r=r.replace(/,/g,".")).includes(".")||!u.includes("."))?(this._restoreSelection=!0,this.forceUpdate()):this._saveSelection(t),i.value&&M(this.props,o)===d&&this.props.onValueChange(o,"input")},this._onValueByStepChange=e=>{const{roundByStep:t=!0,step:n=1}=this.props,d=L(this.state.displayValue);let o;if(isNaN(d)){const{defaultValue:e}=this.props;if(void 0===e)return;o=e}else{const i=new h.Big(d),r=new h.Big(n),u=i.mod(r);let a=i.plus(e*n);!u.eq(0)&&t&&(a=a.plus((e>0?0:1)*n).minus(u)),o=a.toNumber()}this._checkValueBoundaries(o).value&&(this.setState({displayValue:M(this.props,o,_(this.props))}),this.props.onValueChange(o,"step"))},this.state={value:A(this.props.value),displayValue:M(this.props,this.props.value,_(this.props)),focused:!1,valueHash:this.props.valueHash}}componentDidMount(){document.addEventListener("selectionchange",this._handleSelectionChange)}componentWillUnmount(){document.removeEventListener("selectionchange",this._handleSelectionChange)}componentDidUpdate(){const e=(0,b.ensureNotNull)(this._input),t=this._selection;if(null!==t&&this._restoreSelection&&document.activeElement===e){const{start:n,end:d,direction:o}=t;e.setSelectionRange(n,d,o)}this._restoreSelection=!1}render(){return d.createElement(w.NumberInputView,{type:"text",inputMode:this.props.inputMode,name:this.props.name,fontSizeStyle:"medium",value:this.state.displayValue,className:this.props.className,placeholder:this.props.placeholder,forceShowControls:this.props.forceShowControls,disabled:this.props.disabled,stretch:this.props.stretch,error:Boolean(this.props.error),errorMessage:this.props.error,onValueChange:this._onValueChange,onValueByStepChange:this._onValueByStepChange,containerReference:this.props.containerReference,inputReference:this._handleInputReference,onClick:this.props.onClick,onFocus:this._onFocus,onBlur:this._onBlur,onKeyDown:this.props.onKeyDown,autoSelectOnFocus:!0,"data-name":this.props["data-name"],highlight:this.props.highlight})}getClampedValue(){const{min:e,max:t}=this.props,n=L(this.state.displayValue);return isNaN(n)?null:(0,C.clamp)(n,e,t)}static getDerivedStateFromProps(e,t){const{valueHash:n}=e,d=A(e.value);if(t.value!==d||t.valueHash!==n){return{value:d,valueHash:n,
displayValue:M(e,d,t.focused&&t.valueHash===n?void 0:_(e))}}return null}_saveSelection(e){const{selectionStart:t,selectionEnd:n,selectionDirection:d}=e;null!==t&&null!==n&&null!==d&&(this._selection={start:t,end:n,direction:d})}_checkValueBoundaries(e){const{min:t,max:n}=this.props,d=function(e,t,n){const d=e>=t,o=e<=n;return{passMin:d,passMax:o,pass:d&&o,clamped:(0,C.clamp)(e,t,n)}}(e,t,n);return{value:d.pass}}}k.defaultProps=V;const P=/^-?[0-9]*$/,T=/^(-?([0-9]+\.?[0-9]*)|(-?[0-9]*))$/,I=/^(-?([0-9]+'?[0-9]*([0-9]+'?)[0-9]*)|(-?[0-9]*))$/;function M(e,t,n){return v(t=A(t))?"—":(null!==t&&void 0!==n&&(n=Math.max(B(t),n)),function(e,t){if(null===e)return"";return new D.NumericFormatter({precision:t}).format(e,{ignoreLocaleNumberFormat:!0})}(t,n))}function _(e){let t=0;return e.inheritPrecisionFromStep&&e.step<=1&&(t=B(e.step)),Math.max(e.precision,t)||void 0}function B(e){const t=Math.trunc(e).toString();return(0,C.clamp)(D.NumericFormatter.formatNoE(e).length-t.length-1,0,15)}function L(e,t){const n=new D.NumericFormatter({precision:t}).parse(e,{ignoreLocaleNumberFormat:!0});return n.res?n.value:NaN}function A(e){return"number"==typeof e&&Number.isFinite(e)||v(e)?e:null}var R=n(724377),F=n(782302),W=n(589637),z=n(377707);function G(e){const{color:t,thickness:n,thicknessItems:o,noAlpha:i,lineStyle:u,allowedLineStyles:a}=e,[l,s]=(0,r.useDefinitionProperty)({property:t}),[c,f]=(0,r.useDefinitionProperty)(n?{property:n}:{defaultValue:void 0}),[p,m]=(0,r.useDefinitionProperty)(u?{property:u}:{defaultValue:void 0}),h=(0,d.useMemo)((()=>a?z.defaultLineStyleItems.filter((e=>a.includes(e))):z.defaultLineStyleItems),[a]);return d.createElement(F.ColorSelect,{...e,color:function(){if(!l)return null;if("mixed"===l)return"mixed";return(0,R.rgbToHexString)((0,R.parseRgb)(l))}(),onColorChange:function(e){const t=l&&"mixed"!==l?(0,W.alphaToTransparency)((0,R.parseRgba)(l)[3]):0;s((0,W.generateColor)(String(e),t,!0))},thickness:c,lineStyle:p,thicknessItems:o,lineStyleItems:h,onThicknessChange:f,onLineStyleChange:m,opacity:i?void 0:l&&"mixed"!==l?(0,R.parseRgba)(l)[3]:void 0,onOpacityChange:i?void 0:function(e){s((0,W.generateColor)(l,(0,W.alphaToTransparency)(e),!0))}})}var O=n(609838),H=n(586982),J=n(865588),j=n(800417),Z=n(443382),K=n(666242),U=n(954159);const X=[{type:H.LineEnd.Normal,icon:Z,label:O.t(null,void 0,n(65353))},{type:H.LineEnd.Arrow,icon:K,label:O.t(null,void 0,n(511858))}];class Y extends d.PureComponent{constructor(e){super(e),this._items=[],this._items=X.map((t=>({value:t.type,selectedContent:d.createElement(J.DisplayItem,{icon:t.icon}),content:d.createElement(J.DropItem,{icon:t.icon,iconClassName:s()(e.isRight&&U.right),label:t.label})})))}render(){const{id:e,lineEnd:t,className:n,lineEndChange:o,isRight:i,disabled:r}=this.props;return d.createElement(J.IconDropdown,{id:e,disabled:r,className:s()(U.lineEndSelect,i&&U.right,n),items:this._items,value:t,onChange:o,hideArrowButton:!0,...(0,j.filterDataProps)(this.props)})}}function $(e){const{property:t}=e,[n,o]=(0,r.useDefinitionProperty)({property:t})
;return d.createElement(Y,{...e,lineEnd:n,lineEndChange:o})}var q,Q=n(720911),ee=n(284001);function te(e){const{children:t,className:n,breakPoint:o="Normal"}=e;return d.createElement(Q.CellWrap,{className:l(ee.wrap,n,ee[`breakpoint${o}`])},d.Children.map(t,(e=>d.isValidElement(e)?d.createElement("span",{key:null===e.key?void 0:e.key,className:l(ee.row,i(e)&&ee.empty,r(e)&&ee.noMargins)},e):e)));function i(e){return!(!d.isValidElement(e)||e.type!==d.Fragment||!Array.isArray(e.props.children))&&e.props.children.every((e=>null===e))}function r(e){return d.isValidElement(e)&&Boolean(e.props?.["data-no-margins"])}}!function(e){e.MobileNormal="Normal",e.MobileMedium="Medium",e.MobileSmall="Small"}(q||(q={}));const ne={1:"float",0:"integer"};var de=n(297265),oe=n(537458);function ie(e){const{definition:{id:t,properties:{checked:n,disabled:o,visible:i,leftEnd:a,rightEnd:s,value:f,extendLeft:h,extendRight:b,style:v,width:y,color:g},title:E,valueMin:w,valueMax:C,valueStep:D,valueUnit:S,extendLeftTitle:x,extendRightTitle:V,solutionId:k,widthValues:P},offset:T}=e,[I]=(0,r.useDefinitionProperty)({property:n,defaultValue:!0}),[M]=(0,r.useDefinitionProperty)({property:o,defaultValue:!1}),[_]=(0,r.useDefinitionProperty)({property:i,defaultValue:!0}),B=(0,de.useWatchedValueReadonly)({watchedValue:w,defaultValue:void 0}),L=(0,de.useWatchedValueReadonly)({watchedValue:C,defaultValue:void 0}),A=(0,de.useWatchedValueReadonly)({watchedValue:D,defaultValue:void 0}),R=(0,de.useWatchedValueReadonly)({watchedValue:S,defaultValue:void 0}),F=e.disabled||!I;return _?d.createElement(d.Fragment,null,d.createElement(u.CommonSection,{id:t,offset:T,checked:n,title:E,solutionId:k,disabled:e.disabled||M},d.createElement(te,{className:oe.line,breakPoint:"Small"},g&&d.createElement("span",{className:oe.control},d.createElement(G,{color:g,thickness:y,disabled:F,thicknessItems:P,lineStyle:v})),!g&&y&&d.createElement("span",{className:oe.control},d.createElement(m.LineWidthSelect,{id:(0,c.createDomId)(t,"line-width-select"),items:P,property:y,disabled:F})),!g&&v&&d.createElement("span",{className:oe.control},d.createElement(p,{id:(0,c.createDomId)(t,"line-style-select"),property:v,disabled:F})),(a||s||f)&&d.createElement(d.Fragment,null,d.createElement(d.Fragment,null,a&&d.createElement($,{id:(0,c.createDomId)(t,"left-end-select"),"data-name":"left-end-select",className:oe.control,property:a,disabled:F}),s&&d.createElement($,{id:(0,c.createDomId)(t,"right-end-select"),"data-name":"right-end-select",className:oe.control,property:s,disabled:F,isRight:!0})),function(){const{definition:{valueType:t}}=e;return f&&d.createElement("span",{className:l(oe.valueInput,oe.control)},d.createElement(N,{className:oe.input,property:f,min:B,max:L,step:A,disabled:F,mode:void 0!==t?ne[t]:void 0,name:"line-value-input"}),d.createElement("span",{className:oe.valueUnit},R))}()))),h&&d.createElement(u.CommonSection,{id:`${t}ExtendLeft`,offset:T,checked:h,title:x,disabled:e.disabled||M}),b&&d.createElement(u.CommonSection,{id:`${t}ExtendRight`,offset:T,checked:b,title:V,
disabled:e.disabled||M})):null}function re(e){return d.createElement(G,{...e})}var ue=n(776165),ae=n(529631),le=n(585938);function se(e){const{property:t,options:n,...o}=e,[i,u]=(0,r.useDefinitionProperty)({property:t}),a=(0,le.useForceUpdate)();return(0,d.useEffect)((()=>{const e=()=>a();return Array.isArray(n)||n.subscribe(e),()=>{Array.isArray(n)||n.unsubscribe(e)}}),[]),d.createElement(ae.Select,{...o,onChange:u,value:i,items:(Array.isArray(n)?n:n.value()).map((e=>e.readonly?{content:e.title,readonly:e.readonly}:{content:e.title,value:e.value,disabled:e.disabled,id:e.id}))})}var ce=n(397546);const fe=[{title:O.t(null,void 0,n(88686)),value:ue.ColorType.Solid},{title:O.t(null,void 0,n(468043)),value:ue.ColorType.Gradient}],pe=[1,2,3,4];function me(e){const{id:t,disabled:n,noAlpha:o,properties:i}=e,{color:u,gradientColor1:a,gradientColor2:l,type:s,width:f}=i,[p]=(0,r.useDefinitionProperty)({property:s,defaultValue:ue.ColorType.Solid});return d.createElement(te,null,s&&d.createElement(se,{id:(0,c.createDomId)(t,"background-type-options-dropdown"),"data-name":"background-type-options-dropdown",className:ce.dropdown,menuClassName:ce.dropdownMenu,disabled:n,property:s,options:fe}),p!==ue.ColorType.Solid&&p&&a&&l?d.createElement("div",{className:ce.gradientColor},d.createElement(re,{color:a,disabled:n,noAlpha:o}),d.createElement(re,{color:l,disabled:n,noAlpha:o}),f&&d.createElement(m.LineWidthSelect,{className:ce.lineWidthSelect,property:f,items:pe,disabled:!!n})):d.createElement(G,{color:u,disabled:n,noAlpha:o,thickness:f,thicknessItems:pe}))}function he(e){const{definition:{id:t,properties:n,title:o,noAlpha:i,solutionId:a},offset:l}=e,{color:s,checked:c,disabled:f,visible:p}=n,[m]=(0,r.useDefinitionProperty)({property:c,defaultValue:!0}),[h]=(0,r.useDefinitionProperty)({property:f,defaultValue:!1}),[b]=(0,r.useDefinitionProperty)({property:p,defaultValue:!0}),v=e.disabled||!m;return b?d.createElement(u.CommonSection,{id:t,offset:l,checked:c,title:o,solutionId:a,disabled:e.disabled||h},d.createElement(Q.CellWrap,null,n.hasOwnProperty("type")?d.createElement(me,{id:t,properties:n,disabled:v,noAlpha:i}):d.createElement(re,{color:s,disabled:v,noAlpha:i}))):null}var be=n(334291);function ve(e){const{property:t,...n}=e,[o,i]=(0,r.useDefinitionProperty)({property:t});return d.createElement(be.Transparency,{...n,value:o,onChange:i})}function ye(e){const{definition:{id:t,properties:{transparency:n,checked:o,disabled:i,visible:a},title:l,solutionId:s},offset:c}=e,[f]=(0,r.useDefinitionProperty)({property:o,defaultValue:!0}),[p]=(0,r.useDefinitionProperty)({property:i,defaultValue:!1}),[m]=(0,r.useDefinitionProperty)({property:a,defaultValue:!0}),h=e.disabled||!f;return m?d.createElement(u.CommonSection,{id:t,offset:c,checked:o,title:l,solutionId:s,disabled:e.disabled||p},d.createElement(Q.CellWrap,null,d.createElement(ve,{property:n,disabled:h}))):null}var ge=n(195442);function Ee(e){
const{definition:{id:t,properties:{color1:n,color2:o,checked:i,disabled:a,visible:l},title:s,noAlpha1:c,noAlpha2:f,solutionId:p},offset:m}=e,[h]=(0,r.useDefinitionProperty)({property:i,defaultValue:!0}),[b]=(0,r.useDefinitionProperty)({property:a,defaultValue:!1}),[v]=(0,r.useDefinitionProperty)({property:l,defaultValue:!0}),y=e.disabled||!h||b;return v?d.createElement(u.CommonSection,{id:t,offset:m,checked:i,solutionId:p,title:s,disabled:e.disabled||b},d.createElement(Q.CellWrap,{className:ge.twoColors},g(n,c),g(o,f))):null;function g(e,t){return d.createElement("span",{className:ge.colorPicker},d.createElement(re,{color:e,disabled:y,noAlpha:t}))}}var we=n(633064),Ce=n(206289);function De(e){const{definition:{id:t,properties:{checked:n,value:o,unitOptionsValue:i,disabled:a,visible:s},min:f,max:p,step:m,title:h,unit:v,unitOptions:y,type:g,solutionId:E},offset:w}=e,[C]=(0,r.useDefinitionProperty)({property:n,defaultValue:!0}),[D]=(0,r.useDefinitionProperty)({property:a,defaultValue:!1}),[S]=(0,r.useDefinitionProperty)({property:s,defaultValue:!0}),x=(0,de.useWatchedValueReadonly)({watchedValue:f,defaultValue:void 0}),V=(0,de.useWatchedValueReadonly)({watchedValue:p,defaultValue:void 0}),k=(0,de.useWatchedValueReadonly)({watchedValue:m,defaultValue:void 0}),P=(0,de.useWatchedValueReadonly)({watchedValue:v,defaultValue:void 0}),T=(0,d.useContext)(we.ControlCustomWidthContext),I=D||e.disabled||!C;return S?d.createElement(u.CommonSection,{id:t,offset:w,checked:n,title:h,solutionId:E,disabled:e.disabled||D},d.createElement(Q.CellWrap,null,d.createElement(te,null,d.createElement(N,{className:l(Ce.input,T[t]&&Ce[T[t]]),property:o,min:x,max:V,step:k,disabled:I,mode:ne[g],name:"number-input","data-name":t}),i&&d.createElement(se,{id:(0,c.createDomId)(t,"unit-options-dropdown"),"data-name":"unit-options-dropdown",className:Ce.dropdown,menuClassName:Ce.dropdownMenu,disabled:I,property:i,options:(0,b.ensureDefined)(y)})),P&&d.createElement("span",{className:Ce.unit},P))):null}function Se(e){const{definition:{id:t,properties:{checked:n,disabled:o,visible:i},childrenDefinitions:a,title:l},offset:s}=e,[c]=(0,r.useDefinitionProperty)({property:n,defaultValue:!0}),[f]=(0,r.useDefinitionProperty)({property:o,defaultValue:!1}),[p]=(0,r.useDefinitionProperty)({property:i,defaultValue:!0}),m=e.disabled||!c;return p?d.createElement(d.Fragment,null,d.createElement(u.CommonSection,{id:t,offset:s,checked:n,title:l,disabled:e.disabled||f}),a.map((e=>d.createElement(Wt,{key:e.id,disabled:m,definition:e,offset:!0})))):null}var Ne=n(232668);function xe(e){const{property:t}=e,[n,o]=(0,r.useDefinitionProperty)({property:t});return d.createElement(Ne.FontSizeSelect,{...e,fontSize:n,fontSizeChange:o,"data-name":"font-size-select"})}var Ve=n(878112),ke=n(246558);function Pe(e){const{className:t,checked:n,icon:o,disabled:i,onClick:r}=e;return d.createElement("div",{className:s()(t,ke.container,n&&!i&&ke.active,i&&ke.disabled),onClick:i?void 0:r,"data-role":"button",...(0,j.filterDataProps)(e)},d.createElement(Ve.Icon,{className:ke.icon,icon:o}))}
function Te(e){const{icon:t,className:n,property:o,disabled:i}=e,[u,a]=(0,r.useDefinitionProperty)({property:o});return d.createElement(Pe,{className:n,icon:t,checked:u,onClick:function(){a(!u)},disabled:i,...(0,j.filterDataProps)(e)})}var Ie=n(34735),Me=n(47924),_e=n(558213);function Be(e){const{property:t,...n}=e,[o,i]=(0,r.useDefinitionProperty)({property:t}),u=(0,d.useCallback)((e=>i(e.target.value)),[i]);return d.createElement(_e.Textarea,{...n,value:o,onChange:u})}var Le=n(8295),Ae=n(429285),Re=n(63907);const Fe=e=>({content:e.title,title:e.title,value:e.value,id:e.id}),We=e=>({content:e.title,title:e.title,value:e.value,id:e.id});function ze(e){const{definition:{id:t,properties:{color:n,size:o,checked:i,disabled:a,bold:l,italic:s,text:f,alignmentHorizontal:p,alignmentVertical:m,orientation:h,backgroundVisible:b,backgroundColor:v,borderVisible:y,borderColor:g,borderWidth:E,wrap:w},title:C,solutionId:D,sizeItems:S,alignmentTitle:N,alignmentHorizontalItems:x,alignmentVerticalItems:V,orientationTitle:k,orientationItems:P,backgroundTitle:T,borderTitle:I,borderWidthItems:M,wrapTitle:_},offset:B}=e,L=(0,d.useContext)(we.ControlCustomHeightContext),[A]=(0,r.useDefinitionProperty)({property:i,defaultValue:!0}),[R]=(0,r.useDefinitionProperty)({property:a,defaultValue:!1}),[F,W]=(0,r.useDefinitionProperty)({property:m,defaultValue:void 0}),[z,O]=(0,r.useDefinitionProperty)({property:h,defaultValue:"horizontal"}),[H,J]=(0,r.useDefinitionProperty)({property:p,defaultValue:void 0}),[j]=(0,r.useDefinitionProperty)({property:b,defaultValue:!1}),[Z]=(0,r.useDefinitionProperty)({property:y,defaultValue:!1}),K=e.disabled||!A;return d.createElement(d.Fragment,null,function(){if(C)return d.createElement(u.CommonSection,{id:t,offset:B,checked:i,title:C,solutionId:D,disabled:e.disabled||R},d.createElement(te,{breakPoint:"Small"},Y(),$()));return d.createElement(Me.PropertyTable.Row,null,d.createElement(Me.PropertyTable.Cell,{placement:"first",colSpan:2,offset:B,"data-section-name":t},Y(),$(),D&&!1))}(),f&&d.createElement(Me.PropertyTable.Row,null,d.createElement(Me.PropertyTable.Cell,{placement:"first",colSpan:2,offset:B,"data-section-name":t},d.createElement(Be,{className:Ie.InputClasses.FontSizeMedium,rows:(U=L[t],"big"===U?9:5),stretch:!0,property:f,disabled:K,onFocus:function(e){e.target.select()},name:"text-input"}))),(p||m)&&d.createElement(Me.PropertyTable.Row,null,d.createElement(Me.PropertyTable.Cell,{placement:"first",verticalAlign:"adaptive",offset:B,"data-section-name":t},d.createElement(Q.CellWrap,null,d.createElement("span",{className:Re.title},N))),d.createElement(Me.PropertyTable.Cell,{placement:"last",verticalAlign:"adaptive","data-section-name":t},d.createElement(te,{breakPoint:"Small"},void 0!==F&&void 0!==V&&d.createElement(ae.Select,{id:(0,c.createDomId)(t,"alignment-vertical-select"),"data-name":"alignment-vertical-select",className:Re.dropdown,menuClassName:Re.dropdownMenu,disabled:K,value:F,items:V.map(Fe),onChange:W}),void 0!==H&&void 0!==x&&d.createElement(ae.Select,{id:(0,
c.createDomId)(t,"alignment-horizontal-select"),"data-name":"alignment-horizontal-select",className:Re.dropdown,menuClassName:Re.dropdownMenu,disabled:K,value:H,items:x.map(Fe),onChange:J})))),void 0!==h&&void 0!==P&&d.createElement(Me.PropertyTable.Row,null,d.createElement(Me.PropertyTable.Cell,{placement:"first",verticalAlign:"adaptive",offset:B,"data-section-name":t},d.createElement(Q.CellWrap,null,d.createElement("span",{className:Re.title},k))),d.createElement(Me.PropertyTable.Cell,{placement:"last",verticalAlign:"adaptive","data-section-name":t},d.createElement(te,{breakPoint:"Small"},d.createElement(ae.Select,{id:(0,c.createDomId)(t,"orientation-select"),"data-name":"orientation-select",className:Re.dropdown,menuClassName:Re.dropdownMenu,disabled:K,value:z,items:P.map(We),onChange:O})))),q(T,b,v,!!b&&!j),q(I,y,g,!!y&&!Z,E,M),w&&d.createElement(u.CommonSection,{id:`${t}Wrap`,offset:B,checked:w,title:_,disabled:e.disabled||R}));var U;function X(e,t,n){return e?d.createElement(Te,{className:Re.fontStyleButton,icon:t,property:e,disabled:K,"data-name":n}):null}function Y(){return d.createElement(d.Fragment,null,n&&d.createElement("div",{className:Re.colorPicker},d.createElement(re,{color:n,disabled:K})),o&&S&&d.createElement(xe,{id:(0,c.createDomId)(t,"font-size-select"),property:o,fontSizes:S,disabled:K}))}function $(){return d.createElement(d.Fragment,null,X(l,Le,"toggle-bold"),X(s,Ae,"toggle-italic"))}function q(e,n,o,i,r,a){return o||n?d.createElement(u.CommonSection,{id:`${t}ColorSelect`,offset:B,checked:n,title:e,disabled:K},o&&d.createElement(G,{color:o,thickness:r,thicknessItems:a,disabled:K||i})):null}}var Ge=n(481476),Oe=n(41899);function He(e){const{property:t,mathOperations:n="+/*",mode:o="float",disabled:i,...u}=e,[a,l]=(0,d.useState)(performance.now()),[s,c]=(0,r.useDefinitionProperty)({property:t,handler:()=>l(performance.now())}),[f,p,m,h]=y(s,c,a),b=(0,d.useMemo)((()=>{const e=new RegExp(`^[${n.split("").join("\\")}-]?(${"float"===o?"(\\d+\\.\\d*)|":""}(\\d*))$`);return t=>(0,Oe.isString)(t)&&e.test(t)}),[n,o]);return d.createElement(Ge.FormInput,{...u,type:"text",value:f,onChange:function(e){const{value:t}=e.currentTarget;p(b(t)?t:f)},onKeyDown:function(e){if(e.defaultPrevented)return;switch((0,E.hashFromEvent)(e.nativeEvent)){case 27:h();break;case 13:v()}},onBlur:function(){v()},disabled:i,stretch:!1,autoSelectOnFocus:!0});function v(){f.length&&m()}}var Je=n(46741);function je(e){const{definition:{properties:{x:t,y:n,disabled:o},id:i,title:r,solutionId:u},definition:a,offset:l}=e,s=o&&o.value()||e.disabled;return d.createElement(Me.PropertyTable.Row,null,d.createElement(Me.PropertyTable.Cell,{verticalAlign:"top",placement:"first",offset:l,"data-section-name":i},d.createElement("span",{className:Je.coordinates},r)),(t||n)&&d.createElement(Me.PropertyTable.Cell,{placement:"last",offset:l,"data-section-name":i},d.createElement(te,{breakPoint:"Medium"},"coordinates"===a.propType?d.createElement(Ze,{definition:a,disabled:s}):d.createElement(Ke,{definition:a,disabled:s})),u&&!1))}function Ze(e){
const{definition:{properties:{x:t,y:n},minX:o,maxX:i,stepX:r,minY:u,maxY:a,stepY:l,typeX:s,typeY:c},disabled:f}=e,p=(0,de.useWatchedValueReadonly)({watchedValue:o,defaultValue:void 0}),m=(0,de.useWatchedValueReadonly)({watchedValue:i,defaultValue:void 0}),h=(0,de.useWatchedValueReadonly)({watchedValue:r,defaultValue:void 0}),b=(0,de.useWatchedValueReadonly)({watchedValue:u,defaultValue:void 0}),v=(0,de.useWatchedValueReadonly)({watchedValue:a,defaultValue:void 0}),y=(0,de.useWatchedValueReadonly)({watchedValue:l,defaultValue:void 0});return d.createElement(d.Fragment,null,n&&d.createElement(N,{className:Je.input,property:n,min:b,max:v,step:y,disabled:f,name:"y-input",mode:void 0!==c?ne[c]:"integer"}),t&&d.createElement(N,{className:Je.input,property:t,min:p,max:m,step:h,disabled:f,name:"x-input",mode:void 0!==s?ne[s]:"integer"}))}function Ke(e){const{definition:{properties:{x:t,y:o},mathOperationsX:i,mathOperationsY:r,modeX:u,modeY:a},disabled:l}=e;return d.createElement("div",{className:Je.selectionCoordinates},d.createElement("div",{className:Je.selectionCoordinates__inputs},o&&d.createElement(He,{property:o,mathOperations:r,mode:a,disabled:l,className:Je.input,placeholder:O.t(null,void 0,n(549957))}),t&&d.createElement(He,{property:t,mathOperations:i,mode:u,disabled:l,className:Je.input,placeholder:O.t(null,void 0,n(400406))})),d.createElement("div",{className:Je.selectionCoordinates__description},O.t(null,void 0,n(713748))))}var Ue=n(611131);function Xe(e){const{definition:{id:t,properties:{checked:n,option:o,disabled:i,visible:a,color:l},title:f,solutionId:p,options:m,infoTooltip:h},offset:b}=e,[v]=(0,r.useDefinitionProperty)({property:n,defaultValue:!0}),[y]=(0,r.useDefinitionProperty)({property:i,defaultValue:!1}),[g]=(0,r.useDefinitionProperty)({property:a,defaultValue:!0}),E=(0,d.useContext)(we.ControlCustomWidthContext),w=e.disabled||!v;return g?d.createElement(u.CommonSection,{id:t,offset:b,checked:n,title:f,solutionId:p,disabled:e.disabled||y,infoTooltip:h},d.createElement(Q.CellWrap,null,d.createElement(te,null,d.createElement(se,{id:(0,c.createDomId)(t,"options-dropdown"),"data-name":"options-dropdown",className:s()(Ue.dropdown,E[t]&&Ue[E[t]]),menuClassName:s()(Ue.dropdownMenu,E[t]&&Ue[E[t]]),disabled:w||y,property:o,options:m}),l&&d.createElement(re,{color:l,disabled:w})))):null}var Ye=n(653898);var $e,qe=n(431520),Qe=n(273188);!function(e){e[e.None=0]="None",e[e.From=1]="From",e[e.To=2]="To"}($e||($e={}));class et extends d.PureComponent{constructor(e){super(e),this._container=null,this._pointer=null,this._rafPosition=null,this._rafDragStop=null,this._refContainer=e=>{this._container=e},this._refPointer=e=>{this._pointer=e},this._handlePosition=e=>{null!==this._rafPosition||this.props.disabled||(this._rafPosition=requestAnimationFrame((()=>{const{from:t,to:n,min:d,max:o}=this.props,i=this._getNewPosition(e),r=1===this._detectPointerMode(e),u=r?(0,C.clamp)(i,d,n):t,a=r?n:(0,C.clamp)(i,t,o);u<=a&&this._handleChange(u,a),this._rafPosition=null})))},this._handleDragStop=()=>{
null!==this._rafDragStop||this.props.disabled||(this._rafDragStop=requestAnimationFrame((()=>{this.setState({pointerDragMode:0}),this._rafDragStop=null,this.props.onCommit()})))},this._onSliderClick=e=>{S.CheckMobile.any()||(this._handlePosition(e.nativeEvent),this._dragSubscribe())},this._mouseUp=e=>{this._dragUnsubscribe(),this._handlePosition(e),this._handleDragStop()},this._mouseMove=e=>{this._handlePosition(e)},this._onTouchStart=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this._handleDragStop()},this.state={pointerDragMode:0}}componentWillUnmount(){null!==this._rafPosition&&(cancelAnimationFrame(this._rafPosition),this._rafPosition=null),null!==this._rafDragStop&&(cancelAnimationFrame(this._rafDragStop),this._rafDragStop=null),this._dragUnsubscribe()}render(){const{className:e,disabled:t,from:n,to:o,min:i,max:r}=this.props,{pointerDragMode:u}=this.state,a=0!==u,s=r-i,c=0===s?i:(n-i)/s,f=0===s?r:(o-i)/s,p=(0,qe.isRtl)()?"right":"left";return d.createElement("div",{className:l(e,Qe.range,t&&Qe.disabled)},d.createElement("div",{className:Qe.rangeSlider,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd},d.createElement("div",{className:Qe.rangeSliderMiddleWrap},d.createElement("div",{className:l(Qe.rangeSliderMiddle,a&&Qe.dragged),style:{[p]:100*c+"%",width:100*(f-c)+"%"}})),d.createElement("div",{className:Qe.rangePointerWrap},d.createElement("div",{className:l(Qe.pointer,a&&Qe.dragged),style:{[p]:100*c+"%"},ref:this._refPointer})),d.createElement("div",{className:Qe.rangePointerWrap},d.createElement("div",{className:l(Qe.pointer,a&&Qe.dragged),style:{[p]:100*f+"%"}}))))}_dragSubscribe(){const e=(0,b.ensureNotNull)(this._container).ownerDocument;e&&(e.addEventListener("mouseup",this._mouseUp),e.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const e=(0,b.ensureNotNull)(this._container).ownerDocument;e&&(e.removeEventListener("mousemove",this._mouseMove),e.removeEventListener("mouseup",this._mouseUp))}_getNewPosition(e){const{min:t,max:n}=this.props,d=n-t,o=(0,b.ensureNotNull)(this._container),i=(0,b.ensureNotNull)(this._pointer),r=o.getBoundingClientRect(),u=i.offsetWidth;let a=e.clientX-u/2-r.left;return(0,qe.isRtl)()&&(a=r.width-a-u),(0,C.clamp)(a/(r.width-u),0,1)*d+t}_detectPointerMode(e){const{from:t,to:n}=this.props,{pointerDragMode:d}=this.state;if(0!==d)return d;const o=this._getNewPosition(e),i=Math.abs(t-o),r=Math.abs(n-o),u=i===r?o<t?1:2:i<r?1:2;return this.setState({pointerDragMode:u}),u}_handleChange(e,t){const{from:n,to:d,onChange:o}=this.props;e===n&&t===d||o(e,t)}}var tt=n(930052),nt=n(635498);function dt(e){const{definition:{id:t,properties:{checked:n,disabled:o,from:i,to:a},title:l,solutionId:c,max:f,min:p},offset:m,disabled:h}=e,[b]=(0,r.useDefinitionProperty)({property:n,defaultValue:!0}),[g]=(0,r.useDefinitionProperty)({property:o,defaultValue:!1}),E=(0,
de.useWatchedValueReadonly)({watchedValue:p,defaultValue:void 0}),w=(0,de.useWatchedValueReadonly)({watchedValue:f,defaultValue:void 0}),[C,D]=(0,r.useDefinitionProperty)({property:i}),[S,N]=(0,r.useDefinitionProperty)({property:a}),V=v(C)||v(S),k=y(V?"mixed":C,(function(e){if(D(e),v(_)){const e=w||100;B(e),N(e)}})),[P,T,I]=k,M=y(V?"mixed":S,(function(e){if(N(e),v(P)){const e=E||0;T(e),D(e)}})),[_,B,L]=M,A=v(P)||v(_),R=h||v(b)||!b,F={flushed:!1};return d.createElement(u.CommonSection,{id:t,offset:m,checked:n,title:l,disabled:h||g},d.createElement(Q.CellWrap,{className:nt.range},function(){if(!E||!w)return null;return d.createElement(tt.MatchMedia,{rule:"(max-width: 460px)"},(e=>d.createElement(te,{breakPoint:"Medium"},d.createElement(d.Fragment,null,d.createElement("span",{className:nt.valueInput},d.createElement(x,{className:nt.input,sharedBuffer:k,min:E,max:v(_)?w:_,step:1,disabled:R,name:"from-input",mode:"integer",defaultValue:E}),e?d.createElement("span",{className:nt.rangeSlider},"—"):d.createElement(et,{className:s()(nt.rangeSlider,A&&nt.rangeSlider_mixed),from:A?E:P,to:A?w:_,min:E,max:w,onChange:W,onCommit:z,disabled:R}))),d.createElement(d.Fragment,null,d.createElement("span",{className:nt.valueInput},d.createElement(x,{className:nt.input,sharedBuffer:M,min:v(P)?E:P,max:w,step:1,disabled:R,name:"to-input",mode:"integer",defaultValue:w}),c&&!1)))))}()));function W(e,t){T(Math.round(e)),B(Math.round(t))}function z(){F.flushed||(I(),L(),F.flushed=!0)}}var ot=n(852830),it=n(76882),rt=n(80509);function ut(e){const{definitions:t,name:n,offset:o}=e,i=s()(rt.cell,rt.fragmentCell,t.some((e=>void 0!==e.solutionId))&&rt.largeWidth);return d.createElement(Me.PropertyTable.Row,null,d.createElement(Me.PropertyTable.Cell,{className:i,offset:o,placement:"first",verticalAlign:"adaptive",colSpan:2,"data-section-name":n,checkableTitle:!0},t.map((e=>d.createElement("div",{className:rt.item,key:e.id,"data-section-name":e.id},d.createElement(lt,{definition:e}))))))}function at(e){const{definition:t,offset:n}=e;return d.createElement(Me.PropertyTable.Row,null,d.createElement(Me.PropertyTable.Cell,{className:rt.cell,offset:n,placement:"first",verticalAlign:"adaptive",colSpan:2,checkableTitle:!0},d.createElement(lt,{definition:t})))}function lt(e){const{definition:{id:t,properties:{disabled:n,checked:o,color:i,level:u,width:a,style:l},solutionId:f,title:m,widthValues:h,styleValues:b,locked:v}}=e,[y]=(0,r.useDefinitionProperty)({property:o,defaultValue:!0}),[g]=(0,r.useDefinitionProperty)({property:n,defaultValue:!1}),E=g||!y;return d.createElement(d.Fragment,null,d.createElement(it.CheckableTitle,{name:`is-enabled-${t}`,className:s()(m&&rt.withTitle,v&&rt.hidden),title:m&&d.createElement("span",{className:rt.title},m),property:o,disabled:g}),u&&d.createElement(N,{className:s()(rt.input,rt.control),property:u,disabled:E}),i&&d.createElement(G,{className:rt.control,disabled:E,color:i,thickness:a,thicknessItems:h,lineStyle:l,allowedLineStyles:b}),!i&&l&&d.createElement(p,{id:(0,c.createDomId)(t,"leveled-line-style-select"),
className:rt.control,property:l,disabled:E,allowedLineStyles:b}),f&&!1)}var st=n(326302);function ct(e){const{definition:{id:t,properties:{option1:n,option2:o,checked:i,disabled:a},title:l,solutionId:s,optionsItems1:f,optionsItems2:p},offset:m}=e,[h]=(0,r.useDefinitionProperty)({property:i,defaultValue:!0}),[b]=(0,r.useDefinitionProperty)({property:a,defaultValue:!1}),v=e.disabled||!h;return d.createElement(u.CommonSection,{id:t,offset:m,checked:i,title:l,solutionId:s,disabled:e.disabled||b},d.createElement(te,{className:st.twoOptions},d.createElement(se,{id:(0,c.createDomId)(t,"two-options-dropdown-1"),"data-name":"two-options-dropdown-1",className:st.dropdown,menuClassName:st.menu,property:n,disabled:v,options:f}),d.createElement(se,{id:(0,c.createDomId)(t,"two-options-dropdown-2"),"data-name":"two-options-dropdown-2",className:st.dropdown,menuClassName:st.menu,property:o,disabled:v,options:p})))}var ft=n(469982);function pt(e){const{definition:{id:t,properties:{color1:n,color2:o,option:i},options:r,color1Visible:a,color2Visible:l,title:f,noAlpha1:p,noAlpha2:m,solutionId:h},offset:b}=e,v=(0,de.useWatchedValueReadonly)({watchedValue:a,defaultValue:!1}),y=(0,de.useWatchedValueReadonly)({watchedValue:l,defaultValue:!1}),g=(0,d.useContext)(we.ControlCustomWidthContext);return d.createElement(u.CommonSection,{id:t,offset:b,solutionId:h,title:f},d.createElement(Q.CellWrap,{className:ft.optionalTwoColors},d.createElement(te,null,d.createElement(se,{id:(0,c.createDomId)(t,"options-dropdown"),"data-name":"options-dropdown",className:s()(ft.dropdown,g[t]&&ft[g[t]]),menuClassName:s()(ft.dropdownMenu,g[t]&&ft[g[t]]),property:i,options:r}),d.createElement(d.Fragment,null,v&&E(n,p),y&&E(o,m)))));function E(e,t){return d.createElement("span",{className:ft.colorPicker},d.createElement(re,{color:e,noAlpha:t}))}}var mt=n(80831),ht=n(997995);function bt(e){const{source:t,inputs:n,model:o,inputsTabProperty:i,studyMetaInfo:r}=e.definition;return d.createElement(mt.InputsTabContent,{className:ht.withoutPadding,property:i,model:o,study:t,studyMetaInfo:r,inputs:n})}var vt=n(926048),yt=n(205408),gt=n(223391);function Et(e){const{definition:{id:t,title:n,properties:o,solutionId:i},offset:a}=e,{checked:l,emoji:s,backgroundColor:c}=o,[f]=(0,r.useDefinitionProperty)({property:l,defaultValue:!1}),[p,m]=(0,r.useDefinitionProperty)({property:s,defaultValue:"🙂"}),[h,b]=(0,r.useDefinitionProperty)({property:c,defaultValue:vt.colorsPalette["color-tv-blue-a600"]}),[v]=(0,r.useDefinitionProperty)({property:o.disabled,defaultValue:!1}),y=e.disabled||!f;return d.createElement(u.CommonSection,{id:t,offset:a,checked:l,title:n,solutionId:i,disabled:e.disabled||v},d.createElement(yt.EmojiPicker,{value:p,disabled:y,onSelect:m}),d.createElement(F.ColorSelect,{className:gt.colorSelect,disabled:y,color:function(){if("mixed"===h)return h;return(0,R.rgbToHexString)((0,R.parseRgb)(h))}(),opacity:h&&"mixed"!==h?(0,R.parseRgba)(h)[3]:void 0,onColorChange:function(e){const t=h&&"mixed"!==h?(0,W.alphaToTransparency)((0,R.parseRgba)(h)[3]):0;b((0,
W.generateColor)(String(e),t,!0))},onOpacityChange:function(e){b((0,W.generateColor)(h,(0,W.alphaToTransparency)(e),!0))}}))}function wt(e){const{definition:{id:t,properties:{disabled:n,visible:o},childrenDefinitions:i,title:a},offset:l}=e,[s]=(0,r.useDefinitionProperty)({property:n,defaultValue:!1}),[c]=(0,r.useDefinitionProperty)({property:o,defaultValue:!0}),f=e.disabled;return c?d.createElement(d.Fragment,null,a&&d.createElement(u.CommonSection,{id:t,offset:l,title:a,disabled:e.disabled||s}),i.map((e=>d.createElement(Wt,{key:e.id,disabled:f,definition:e,offset:e.offset??Boolean(a)})))):null}var Ct=n(525388),Dt=n(942544),St=n(930202),Nt=n(953517),xt=n(444144),Vt=n(865266),kt=n(379266),Pt=n(913631);function Tt(e){const[t,n]=(0,Vt.useRovingTabindexElement)(null);return d.createElement(kt.PopupMenuItemToggle,{...e,className:Pt.item,checkboxClassName:Pt.checkbox,checkboxReference:t,checkboxTabIndex:n,checkboxDataRole:"menuitem",onKeyDown:function(e){const n=(0,St.hashFromEvent)(e);13!==n&&32!==n||(e.preventDefault(),t.current instanceof HTMLElement&&t.current.click())},"aria-disabled":e.isDisabled||void 0})}var It=n(994567),Mt=n(101774);function _t(e){return!e.readonly&&!e.disabled}function Bt(e){const{selectedItems:t,placeholder:n}=e;if(!t.length)return d.createElement("span",null,n);const o=t.map((e=>e.selectedContent??e.content??e.value?.toString())).reduce(((e,t,n)=>t?(e.push(d.createElement("span",{key:n,className:Mt.contentPart},t)),e.push(d.createElement("span",{key:`separator_${n}`},","," ")),e):e),[]);return o.length&&o.splice(-1),d.createElement("span",{className:Mt.btnContent},o)}function Lt(e,t){const{id:n,items:o,menuClassName:i,menuItemClassName:r,tabIndex:u,disabled:a,highlight:l,intent:s,hideArrowButton:f,placeholder:p,value:m,"aria-labelledby":h,onFocus:b,onBlur:v,onClick:y,onChange:g,onKeyDown:E,openMenuOnEnter:w=!0,"aria-describedby":C,"aria-invalid":D,...S}=e,{listboxId:N,isOpened:x,isFocused:V,buttonTabIndex:k,highlight:P,intent:T,open:I,onOpen:M,close:_,toggle:B,buttonFocusBindings:L,onButtonClick:A,buttonRef:R,listboxRef:F,listboxTabIndex:W,buttonAria:z}=(0,Dt.useControlDisclosure)({id:n,disabled:a,buttonTabIndex:u,intent:s,highlight:l,onFocus:b,onBlur:v,onClick:y}),G=o.filter(_t).filter((e=>m.some((t=>e.value===t)))),O=(0,c.joinDomIds)(h,n),H=O.length>0?O:void 0,J=(0,d.useMemo)((()=>({role:"listbox","aria-labelledby":h})),[h]),j=(0,Nt.useKeyboardToggle)(B,x||w),Z=(0,Nt.useKeyboardOpen)(x,I),K=(0,Nt.useKeyboardEventHandler)([j,Z]),U=(0,Ct.useMergedRefs)([R,t]);return d.createElement(xt.ControlDisclosureView,{...S,...z,...L,id:n,role:"button",tabIndex:k,"aria-owns":z["aria-controls"],"aria-haspopup":"listbox","aria-labelledby":H,disabled:a,hideArrowButton:f,isFocused:V,isOpened:x,highlight:P,intent:T,ref:U,onClick:A,onClose:_,onKeyDown:K,onOpen:M,listboxTabIndex:W,listboxId:N,listboxClassName:i,listboxAria:J,"aria-describedby":C,"aria-invalid":D,listboxReference:F,onListboxKeyDown:function(e){switch((0,St.hashFromEvent)(e)){case 27:case 9:return void(x&&(e.preventDefault(),_()))}(0,
It.handleAccessibleMenuKeyDown)(e)},onListboxFocus:e=>(0,It.handleAccessibleMenuFocus)(e,R),buttonChildren:d.createElement(Bt,{selectedItems:G??null,placeholder:p})},o.map(((e,t)=>{if(e.readonly)return d.createElement(d.Fragment,{key:`readonly_item_${t}`},e.content);const o=function(e,t){return t?.id??(0,c.createDomId)(e,"item",t?.value)}(n,e);return d.createElement(Tt,{key:o,id:o,className:r,"aria-selected":m===e.value,isChecked:m.includes(e.value),label:e.content??e.value?.toString()??"",onClick:()=>function(e){const t=new Set(m);t.has(e)?t.delete(e):t.add(e);g(Array.from(t))}(e.value),isDisabled:e.disabled})})))}Lt.displayName="Multiselect";const At=d.forwardRef(Lt);var Rt=n(274782);function Ft(e){const{definition:t}=e,{checked:i,checkableListOptions:u,definitions:a}=t,[l,s]=(0,d.useState)(y()),[c]=(0,r.useDefinitionProperty)({property:i,defaultValue:!0});(0,d.useEffect)((()=>{const e={},t=()=>{const e=y();s(e)};return t(),u.forEach((n=>{n.properties.checked&&n.properties.checked?.subscribe(e,t)})),()=>{u.forEach((n=>{n.properties.checked&&n.properties.checked?.unsubscribe(e,t)}))}}),[t]);const f=[],p=[],m=[],h=[];a.value().forEach((e=>{(0,o.isPropertyDefinition)(e)&&((0,o.isColorDefinition)(e)?f.push(e):(0,o.isTwoColorDefinition)(e)?p.push(e):(0,o.isLineDefinition)(e)?m.push(e):(0,o.isOptionsDefinition)(e)&&h.push(e))}));const v=!c||0===l.length;return d.createElement(d.Fragment,null,d.createElement(Me.PropertyTable.Row,null,d.createElement(it.CheckableTitle,{name:`is-enabled-${t.id}`,title:t.title,property:i}),d.createElement(Me.PropertyTable.Cell,{placement:"last"},d.createElement("div",{className:Rt.wrap},d.createElement(At,{className:Rt.select,placeholder:O.t(null,void 0,n(8854)),disabled:!c,onChange:function(e){const t=new Set(e);u.forEach((e=>{!t.has(e.id)||e.properties.checked?.value()?!t.has(e.id)&&e.properties.checked?.value()&&e.properties.checked.setValue(!1):(0,b.ensureDefined)(e.properties.checked).setValue(!0)}))},value:l,matchButtonAndListboxWidths:!0,items:u.map((e=>({id:e.id,content:e.notFormatedTitle?d.createElement("span",{className:Rt.preContent},(0,b.ensureDefined)(e.title)):(0,b.ensureDefined)(e.title),value:e.id})))}),f.length||m.length?d.createElement("div",{className:Rt.colorsWrap},m.map((e=>d.createElement(G,{key:e.id,color:(0,b.ensureDefined)(e.properties.color),thickness:e.properties.width,thicknessItems:e.widthValues,disabled:v}))),f.map((e=>d.createElement(re,{key:e.id,color:e.properties.color,disabled:v})))):null,p.length?p.map((e=>d.createElement("div",{key:e.id,className:Rt.colorsWrap},d.createElement(re,{color:e.properties.color1,disabled:v,noAlpha:e.noAlpha1}),d.createElement(re,{color:e.properties.color2,disabled:v,noAlpha:e.noAlpha2})))):null))),h.map((e=>d.createElement(Me.PropertyTable.Row,{key:e.id},d.createElement(Me.PropertyTable.Cell,{placement:"first"}),d.createElement(Me.PropertyTable.Cell,{placement:"last"},d.createElement(se,{className:Rt.select,property:e.properties.option,options:e.options,disabled:v}))))));function y(){return u.filter((e=>(0,
b.ensureDefined)(e.properties?.checked).value())).map((e=>e.id))}}function Wt(e){const{definition:t,offset:n,disabled:r}=e;if(function(e){(0,d.useEffect)((()=>{if(void 0===e)return;const t={...e.properties};return Object.entries(t).forEach((([n,d])=>{void 0!==d&&d.subscribe(t,(()=>Ye.logger.logNormal(`Property "${n}" in definition "${e.id}" was updated to value "${d.value()}"`)))})),()=>{Object.entries(t).forEach((([,e])=>{e?.unsubscribeAll(t)}))}}),[e])}((0,o.isPropertyDefinitionsGroup)(t)?void 0:t),(0,o.isPropertyDefinitionsGroup)(t))return d.createElement(zt,{definition:t,offset:n,disabled:r});switch(t.propType){case"line":return d.createElement(ie,{...e,definition:t});case"checkable":return d.createElement(a,{...e,definition:t});case"color":return d.createElement(he,{...e,definition:t});case"transparency":return d.createElement(ye,{...e,definition:t});case"twoColors":return d.createElement(Ee,{...e,definition:t});case"optionalTwoColors":return d.createElement(pt,{...e,definition:t});case"fourColors":case"session":case"soundSelect":case"soundVolume":case"image":default:return null;case"number":return d.createElement(De,{...e,definition:t});case"symbol":return d.createElement(i.SymbolInputsButton,{...e,definition:t});case"text":return d.createElement(ze,{...e,definition:t});case"checkableSet":return d.createElement(Se,{...e,definition:t});case"set":return d.createElement(wt,{...e,definition:t});case"options":return d.createElement(Xe,{...e,definition:t});case"range":return d.createElement(dt,{...e,definition:t});case"coordinates":case"selectionCoordinates":return d.createElement(je,{...e,definition:t});case"twoOptions":return d.createElement(ct,{...e,definition:t});case"leveledLine":return d.createElement(at,{...e,definition:t});case"emoji":return d.createElement(Et,{...e,definition:t});case"studyInputs":return d.createElement(bt,{...e,definition:t})}}function zt(e){const{definition:t}=e,n=(0,de.useWatchedValueReadonly)({watchedValue:t.definitions});return(0,de.useWatchedValueReadonly)({watchedValue:t.visible,defaultValue:!0})?(0,o.isCheckableListOptionsDefinition)(t)?d.createElement(Ft,{definition:t}):d.createElement(d.Fragment,null,t.title&&d.createElement(ot.GroupTitleSection,{title:t.title,name:t.id}),n&&function(e){const t=[];return e.reduce(((e,t)=>{if((0,o.isPropertyDefinitionsGroup)(t)||"leveledLine"!==t.propType)e.push(t);else{const n=e[e.length-1];Array.isArray(n)?n.push(t):e.push([t])}return e}),t)}(n).map((n=>Array.isArray(n)?d.createElement(ut,{key:n[0].id,name:t.id,definitions:n}):d.createElement(Wt,{key:n.id,...e,definition:n}))),"general"===t.groupType&&d.createElement(Me.PropertyTable.GroupSeparator,{size:1})):null}},299120:(e,t,n)=>{"use strict";n.d(t,{ControlDisclosure:()=>s});var d=n(50959),o=n(525388),i=n(953517),r=n(444144),u=n(942544),a=n(180185),l=n(99505);const s=d.forwardRef(((e,t)=>{const{id:n,tabIndex:s,disabled:c,highlight:f,intent:p,children:m,onClick:h,onFocus:b,onBlur:v,listboxAria:y,onListboxKeyDown:g,...E}=e,w=(0,d.useRef)({"aria-labelledby":n
}),{listboxId:C,isOpened:D,isFocused:S,buttonTabIndex:N,listboxTabIndex:x,highlight:V,intent:k,onOpen:P,close:T,toggle:I,buttonFocusBindings:M,onButtonClick:_,buttonRef:B,listboxRef:L,buttonAria:A}=(0,u.useControlDisclosure)({id:n,disabled:c,buttonTabIndex:s,intent:p,highlight:f,onFocus:b,onBlur:v,onClick:h}),R=(0,i.useKeyboardToggle)(I),F=(0,i.useKeyboardClose)(D,T),W=(0,i.useKeyboardEventHandler)([R,F]);return d.createElement(r.ControlDisclosureView,{...E,...M,...A,id:n,role:"button",tabIndex:N,disabled:c,isOpened:D,isFocused:S,ref:(0,o.useMergedRefs)([B,t]),highlight:V,intent:k,onClose:T,onOpen:P,onClick:_,onKeyDown:W,listboxId:C,listboxTabIndex:x,listboxReference:L,listboxAria:y??w.current,onListboxKeyDown:function(e){if(27===(0,a.hashFromEvent)(e))return e.preventDefault(),void T();g?.(e)}},m,d.createElement("span",{className:l.invisibleFocusHandler,tabIndex:0,"aria-hidden":!0,onFocus:()=>T()}))}));s.displayName="ControlDisclosure"},163694:(e,t,n)=>{"use strict";n.d(t,{DrawerContext:()=>r,DrawerManager:()=>i});var d=n(50959),o=n(285089);class i extends d.PureComponent{constructor(e){super(e),this._isBodyFixed=!1,this._addDrawer=e=>{this.setState((t=>({stack:[...t.stack,e]})))},this._removeDrawer=e=>{this.setState((t=>({stack:t.stack.filter((t=>t!==e))})))},this.state={stack:[]}}componentDidUpdate(e,t){!t.stack.length&&this.state.stack.length&&((0,o.setFixedBodyState)(!0),this._isBodyFixed=!0),t.stack.length&&!this.state.stack.length&&this._isBodyFixed&&((0,o.setFixedBodyState)(!1),this._isBodyFixed=!1)}componentWillUnmount(){this.state.stack.length&&this._isBodyFixed&&(0,o.setFixedBodyState)(!1)}render(){return d.createElement(r.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.stack.length?this.state.stack[this.state.stack.length-1]:null}},this.props.children)}}const r=d.createContext(null)},759339:(e,t,n)=>{"use strict";n.d(t,{Drawer:()=>p});var d=n(50959),o=n(650151),i=n(497754),r=n(924910),u=n(8361),a=n(163694),l=n(28466),s=n(742554),c=n(536718);var f;function p(e){const{position:t="Bottom",onClose:n,children:s,reference:f,className:p,theme:h=c}=e,b=(0,o.ensureNotNull)((0,d.useContext)(a.DrawerContext)),[v]=(0,d.useState)((()=>(0,r.randomHash)())),y=(0,d.useRef)(null),g=(0,d.useContext)(l.CloseDelegateContext);return(0,d.useLayoutEffect)((()=>((0,o.ensureNotNull)(y.current).focus({preventScroll:!0}),g.subscribe(b,n),b.addDrawer(v),()=>{b.removeDrawer(v),g.unsubscribe(b,n)})),[]),d.createElement(u.Portal,null,d.createElement("div",{ref:f,className:i(c.wrap,c[`position${t}`])},v===b.currentDrawer&&d.createElement("div",{className:c.backdrop,onClick:n}),d.createElement(m,{className:i(h.drawer,c[`position${t}`],p),ref:y,"data-name":e["data-name"]},s)))}!function(e){e.Left="Left",e.Bottom="Bottom"}(f||(f={}));const m=(0,d.forwardRef)(((e,t)=>{const{className:n,...o}=e;return d.createElement(s.TouchScrollContainer,{className:i(c.drawer,n),tabIndex:-1,ref:t,...o})}))},205408:(e,t,n)=>{"use strict";n.d(t,{EmojiPicker:()=>P})
;var d=n(50959),o=n(870122),i=n(499547),r=n(994437),u=n(611005);var a=n(624216),l=n(163694),s=n(759339),c=n(510618),f=n(930202),p=n(493173),m=n(930052);function h(e){!function(e,t){(0,d.useEffect)((()=>{const n=t||document;return n.addEventListener("scroll",e),()=>n.removeEventListener("scroll",e)}),[e])}(e,document)}var b=n(162458),v=n(996038),y=n(497754),g=n.n(y),E=n(510555);function w(e){const{children:t,highlight:n,disabled:o,reference:i,...r}=e,u=n?"primary":"default";return d.createElement("div",{...r,ref:i,className:g()(E.wrapper,E[`intent-${u}`],E["border-thin"],E["size-medium"],n&&E.highlight,n&&E.focused,o&&E.disabled),"data-role":"button"},d.createElement("div",{className:g()(E.childrenContainer,o&&E.disabled)},t),n&&d.createElement("span",{className:E.shadow}))}var C=n(99021),D=n(927061);const S=()=>null,N=(0,p.mergeThemes)(c.DEFAULT_MENU_THEME,{menuBox:D.menuBox}),x=378,V=18,k=200;function P(e){const{value:t,disabled:n,onSelect:r,onClose:c,canBeEmpty:p,renderButton:y=T}=e,g=(0,d.useRef)(null),{current:E}=(0,d.useRef)((w=t,o.getJSON("RecentlyUsedEmojis",[w]).filter((e=>e!==C.EMPTY_EMOJI))));var w;const P=(0,d.useRef)(null),[I,M]=(0,d.useState)(E),[_,B]=(0,d.useState)(!1),L=(0,d.useCallback)((()=>{B(!1),c?.()}),[c]),A=(0,d.useRef)(0);h((0,d.useCallback)((()=>{Date.now()-A.current<k||L()}),[L]));const R=(0,d.useCallback)((e=>{if(e!==C.EMPTY_EMOJI){const t=Array.from(new Set([e,...I])).slice(0,V);o.setJSON("RecentlyUsedEmojis",t),M(t)}r(e),L()}),[I,r]),F=(0,d.useMemo)((()=>p?[C.EMPTY_EMOJI,...I].slice(0,V):I),[I,p]),W=(z=F,(0,d.useMemo)((()=>{const e=(0,u.emojiGroups)();return e[0].emojis=z,e}),[z]));var z;return d.createElement(d.Fragment,null,d.createElement("div",{ref:g,className:D.buttonWrap},y({emoji:t,isOpened:_,disabled:n,onClick:function(){if(_)return void L();n||(B(!0),A.current=Date.now())}})),d.createElement(m.MatchMedia,{rule:v.DialogBreakpoints.TabletSmall},(e=>_&&d.createElement(l.DrawerManager,null,e?d.createElement(s.Drawer,{className:D.drawer,position:"Bottom",onClose:L},d.createElement(i.EmojiList,{emojis:W,onSelect:R,height:x})):d.createElement(a.PopupMenu,{theme:N,onKeyDown:O,isOpened:!0,position:(0,b.getPopupPositioner)(g.current,{horizontalDropDirection:b.HorizontalDropDirection.FromLeftToRight,horizontalAttachEdge:b.HorizontalAttachEdge.Left}),closeOnClickOutside:!1,onClickOutside:H,onClose:S,controller:P,onOpen:G,tabIndex:-1},d.createElement(i.EmojiList,{className:D.desktopSize,emojis:W,onSelect:R,height:x}))))));function G(){P.current?.focus()}function O(e){27===(0,f.hashFromEvent)(e)&&(e.preventDefault(),e.stopPropagation(),L())}function H(e){const t=e.target;t instanceof Node&&g.current?.contains(t)||L()}}function T(e){const{emoji:t,isOpened:n,disabled:o,onClick:i}=e;return d.createElement(w,{highlight:n,disabled:o,"data-name":"emoji-picker"},d.createElement(r.EmojiWrap,{emoji:t,onClick:i}))}},379266:(e,t,n)=>{"use strict";n.d(t,{PopupMenuItemToggle:()=>a});var d=n(50959),o=n(497754),i=n(192063),r=n(302946),u=n(840638);function a(e){
const{isDisabled:t,hint:n,label:a,isChecked:l,checkboxClassName:s,labelClassName:c,indeterminate:f,isActive:p,checkboxTabIndex:m,checkboxReference:h,checkboxDataRole:b,checkboxDataName:v,...y}=e;return d.createElement(i.PopupMenuItem,{...y,isDisabled:t,shortcut:n,dontClosePopup:!0,labelRowClassName:c,label:d.createElement(r.Checkbox,{reference:h,disabled:t,label:a,checked:l,indeterminate:f,className:o(u.checkbox,s),tabIndex:m,"data-role":b,"data-name":v})})}},917850:(e,t,n)=>{"use strict";n.d(t,{PopupMenuSeparator:()=>a});var d,o=n(50959),i=n(497754),r=n.n(i),u=n(700238);function a(e){const{size:t="normal",className:n,ariaHidden:d=!1}=e;return o.createElement("div",{className:r()(u.separator,"small"===t&&u.small,"normal"===t&&u.normal,"large"===t&&u.large,n),role:"separator","aria-hidden":d})}!function(e){e.Small="small",e.Large="large",e.Normal="normal"}(d||(d={}))},72621:(e,t,n)=>{"use strict";n.d(t,{RemoveButton:()=>c});var d=n(609838),o=n(50959),i=n(497754),r=n.n(i),u=n(878112),a=n(227570),l=n(333765),s=n(35990);function c(e){const{className:t,isActive:i,onClick:c,onMouseDown:f,title:p,hidden:m,"data-name":h="remove-button",icon:b,...v}=e,[y,g]=(0,a.useActiveDescendant)(null);return o.createElement(u.Icon,{...v,"data-name":h,className:r()(s.button,"apply-common-tooltip",i&&s.active,m&&s.hidden,g&&s.focused,t),icon:b||l,onClick:c,onMouseDown:f,title:p??d.t(null,void 0,n(767410)),ariaLabel:p??d.t(null,void 0,n(767410)),ref:y})}},913631:e=>{e.exports={checkbox:"checkbox-hcyAOCXc",item:"item-hcyAOCXc"}},994567:(e,t,n)=>{"use strict";n.d(t,{focusFirstMenuItem:()=>s,handleAccessibleMenuFocus:()=>a,handleAccessibleMenuKeyDown:()=>l,queryMenuElements:()=>p});var d=n(442092),o=n(333086),i=n(180185),r=n(32556);const u=[37,39,38,40];function a(e,t){if(!e.target)return;const n=e.relatedTarget?.getAttribute("aria-activedescendant");if(e.relatedTarget!==t.current){const e=n&&document.getElementById(n);if(!e||e!==t.current)return}s(e.target)}function l(e){if(e.defaultPrevented)return;const t=(0,i.hashFromEvent)(e);if(!u.includes(t))return;const n=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const r=p(e.currentTarget).sort(d.navigationOrderComparator);if(0===r.length)return;const a=document.activeElement.closest('[data-role="menuitem"]')||document.activeElement.parentElement?.querySelector('[data-role="menuitem"]');if(!(a instanceof HTMLElement))return;const l=r.indexOf(a);if(-1===l)return;const s=m(a),h=s.indexOf(document.activeElement),b=-1!==h,v=e=>{n&&(0,o.becomeSecondaryElement)(n),(0,o.becomeMainElement)(e),e.focus()};switch((0,d.mapKeyCodeToDirection)(t)){case"inlinePrev":if(!s.length)return;e.preventDefault(),v(0===h?r[l]:b?c(s,h,-1):s[s.length-1]);break;case"inlineNext":if(!s.length)return;e.preventDefault(),h===s.length-1?v(r[l]):v(b?c(s,h,1):s[0]);break;case"blockPrev":{e.preventDefault();const t=c(r,l,-1);if(b){const e=f(t,h);v(e||t);break}v(t);break}case"blockNext":{e.preventDefault();const t=c(r,l,1);if(b){const e=f(t,h);v(e||t);break}v(t)}}}function s(e){const[t]=p(e);t&&((0,
o.becomeMainElement)(t),t.focus())}function c(e,t,n){return e[(t+e.length+n)%e.length]}function f(e,t){const n=m(e);return n.length?n[(t+n.length)%n.length]:null}function p(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled]):not([aria-disabled])')).filter((0,r.createScopedVisibleElementFilter)(e))}function m(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled]):not([aria-disabled])")).filter((0,r.createScopedVisibleElementFilter)(e))}},162458:(e,t,n)=>{"use strict";n.d(t,{HorizontalAttachEdge:()=>o,HorizontalDropDirection:()=>r,VerticalAttachEdge:()=>d,VerticalDropDirection:()=>i,getPopupPositioner:()=>l});var d,o,i,r,u=n(650151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(d||(d={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(o||(o={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(i||(i={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(r||(r={}));const a={verticalAttachEdge:d.Bottom,horizontalAttachEdge:o.Left,verticalDropDirection:i.FromTopToBottom,horizontalDropDirection:r.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function l(e,t){return n=>{const{contentWidth:l,contentHeight:s,availableHeight:c}=n,f=(0,u.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:p=a.horizontalAttachEdge,horizontalDropDirection:m=a.horizontalDropDirection,horizontalMargin:h=a.horizontalMargin,verticalMargin:b=a.verticalMargin,matchButtonAndListboxWidths:v=a.matchButtonAndListboxWidths}=t;let y=t.verticalAttachEdge??a.verticalAttachEdge,g=t.verticalDropDirection??a.verticalDropDirection;y===d.AutoStrict&&(c<f.y+f.height+b+s?(y=d.Top,g=i.FromBottomToTop):(y=d.Bottom,g=i.FromTopToBottom));const E=y===d.Top?-1*b:b,w=p===o.Right?f.right:f.left,C=y===d.Top?f.top:f.bottom,D={x:w-(m===r.FromRightToLeft?l:0)+h,y:C-(g===i.FromBottomToTop?s:0)+E};return v&&(D.overrideWidth=f.width),D}}},22982:(e,t,n)=>{"use strict";n.d(t,{convertToDefinitionProperty:()=>i,makeProxyDefinitionProperty:()=>o});var d=n(32133);function o(e,t,n){const d=new Map,o=void 0!==t?t[0]:e=>e,i=void 0!==t?void 0!==t[1]?t[1]:t[0]:e=>e,r={value:()=>o(e.value()),setValue:t=>{e.setValue(i(t))},subscribe:(t,n)=>{const o=e=>{n(r)};d.set(n,o),e.subscribe(t,o)},unsubscribe:(t,n)=>{const o=d.get(n);o&&(e.unsubscribe(t,o),d.delete(n))},unsubscribeAll:t=>{e.unsubscribeAll(t),d.clear()},destroy:()=>{e.release(),n?.()}};return r}function i(e,t,n,i,r,u,a){const l=o(t.weakReference(),i,u),s=void 0!==i?void 0!==i[1]?i[1]:i[0]:e=>e,c=r??(d=>e.setProperty(t,s(d),n));return l.setValue=e=>{a&&(0,d.trackEvent)(a.category,a.event,a.label?.(e)),c(e)},l}},235918:(e,t,n)=>{"use strict";n.d(t,{createLinePropertyDefinition:()=>a});var d=n(586982),o=n(936879);const i=[o.LINESTYLE_SOLID,o.LINESTYLE_DOTTED,o.LINESTYLE_DASHED],r=[1,2,3,4],u=[d.LineEnd.Normal,d.LineEnd.Arrow];function a(e,t){const n={propType:"line",properties:e,...t}
;return void 0!==n.properties.style&&(n.styleValues=i),void 0!==n.properties.width&&(n.widthValues=r),void 0===n.properties.leftEnd&&void 0===n.properties.rightEnd||void 0!==n.endsValues||(n.endsValues=u),void 0!==n.properties.value&&void 0===n.valueType&&(n.valueType=1),n}},48703:(e,t,n)=>{"use strict";var d;function o(e,t){return{propType:"number",properties:e,type:1,...t}}n.d(t,{createNumberPropertyDefinition:()=>o}),function(e){e[e.Integer=0]="Integer",e[e.Float=1]="Float"}(d||(d={}))},761080:(e,t,n)=>{"use strict";function d(e,t){return{propType:"options",properties:e,...t}}n.d(t,{createOptionsPropertyDefinition:()=>d})},798346:(e,t,n)=>{"use strict";function d(e,t){return{propType:"checkable",properties:e,notFormatedTitle:!1,...t}}function o(e,t,n){return{propType:"checkableSet",properties:e,childrenDefinitions:n,...t}}function i(e,t){return{propType:"color",properties:e,noAlpha:!1,...t}}n.d(t,{convertFromReadonlyWVToDefinitionProperty:()=>H,convertFromWVToDefinitionProperty:()=>O,convertToDefinitionProperty:()=>z.convertToDefinitionProperty,createCheckablePropertyDefinition:()=>d,createCheckableSetPropertyDefinition:()=>o,createColorPropertyDefinition:()=>i,createCoordinatesPropertyDefinition:()=>V,createEmojiPropertyDefinition:()=>B,createImagePropertyDefinition:()=>_,createLeveledLinePropertyDefinition:()=>s,createLinePropertyDefinition:()=>r.createLinePropertyDefinition,createNumberPropertyDefinition:()=>c.createNumberPropertyDefinition,createOptionalTwoColorsPropertyDefinition:()=>x,createOptionsPropertyDefinition:()=>f.createOptionsPropertyDefinition,createPropertyDefinitionsCheckableListOptionsGroup:()=>F,createPropertyDefinitionsGeneralGroup:()=>R,createPropertyDefinitionsLeveledLinesGroup:()=>W,createRangePropertyDefinition:()=>P,createSelectionCoordinatesPropertyDefinition:()=>k,createSessionPropertyDefinition:()=>M,createStudyInputsPropertyDefinition:()=>L,createSymbolPropertyDefinition:()=>I,createTextPropertyDefinition:()=>S,createTransparencyPropertyDefinition:()=>T,createTwoColorsPropertyDefinition:()=>N,createTwoOptionsPropertyDefinition:()=>p,destroyDefinitions:()=>re,getColorDefinitionProperty:()=>$,getLockPriceScaleDefinitionProperty:()=>j,getPriceScaleSelectionStrategyDefinitionProperty:()=>J,getScaleRatioDefinitionProperty:()=>Z,getSymbolDefinitionProperty:()=>q,isCheckableListOptionsDefinition:()=>ie,isColorDefinition:()=>ne,isLineDefinition:()=>te,isOptionsDefinition:()=>oe,isPropertyDefinition:()=>Q,isPropertyDefinitionsGroup:()=>ee,isTwoColorDefinition:()=>de,makeProxyDefinitionProperty:()=>z.makeProxyDefinitionProperty});var r=n(235918),u=n(936879);const a=[u.LINESTYLE_SOLID,u.LINESTYLE_DOTTED,u.LINESTYLE_DASHED],l=[1,2,3,4];function s(e,t){const n={propType:"leveledLine",properties:e,...t};return void 0!==n.properties.style&&(n.styleValues=a),void 0!==n.properties.width&&(n.widthValues=l),n}var c=n(48703),f=n(761080);function p(e,t){return{propType:"twoOptions",properties:e,...t}}var m,h=n(609838),b=n(986226);!function(e){e.Horizontal="horizontal",e.Vertical="vertical"}(m||(m={}))
;const v=[{id:b.VerticalAlign.Top,value:b.VerticalAlign.Bottom,title:h.t(null,void 0,n(697118))},{id:b.VerticalAlign.Middle,value:b.VerticalAlign.Middle,title:h.t(null,void 0,n(668833))},{id:b.VerticalAlign.Bottom,value:b.VerticalAlign.Top,title:h.t(null,void 0,n(327567))}],y=[{id:b.HorizontalAlign.Left,value:b.HorizontalAlign.Left,title:h.t(null,void 0,n(411626))},{id:b.HorizontalAlign.Center,value:b.HorizontalAlign.Center,title:h.t(null,void 0,n(224197))},{id:b.HorizontalAlign.Right,value:b.HorizontalAlign.Right,title:h.t(null,void 0,n(50421))}],g=[{id:"horizontal",value:"horizontal",title:h.t(null,void 0,n(395406))},{id:"vertical",value:"vertical",title:h.t(null,void 0,n(569526))}],E=[8,10,11,12,14,16,18,20,22,24,28,32,40].map((e=>({title:String(e),value:e}))),w=[1,2,3,4],C=h.t(null,void 0,n(225485)),D=h.t(null,void 0,n(967781));function S(e,t){const n={propType:"text",properties:e,...t,isEditable:t.isEditable||!1};return void 0!==n.properties.size&&void 0===n.sizeItems&&(n.sizeItems=E),void 0!==n.properties.alignmentVertical&&void 0===n.alignmentVerticalItems&&(n.alignmentVerticalItems=v),void 0!==n.properties.alignmentHorizontal&&void 0===n.alignmentHorizontalItems&&(n.alignmentHorizontalItems=y),(n.alignmentVerticalItems||n.alignmentHorizontalItems)&&void 0===n.alignmentTitle&&(n.alignmentTitle=C),void 0!==n.properties.orientation&&(void 0===n.orientationItems&&(n.orientationItems=g),void 0===n.orientationTitle&&(n.orientationTitle=D)),void 0!==n.properties.borderWidth&&void 0===n.borderWidthItems&&(n.borderWidthItems=w),n}function N(e,t){return{propType:"twoColors",properties:e,noAlpha1:!1,noAlpha2:!1,...t}}function x(e,t){return{propType:"optionalTwoColors",properties:e,noAlpha1:!1,noAlpha2:!1,...t}}function V(e,t){return{propType:"coordinates",properties:e,...t}}function k(e,t){return{propType:"selectionCoordinates",properties:e,...t}}function P(e,t){return{propType:"range",properties:e,...t}}function T(e,t){return{propType:"transparency",properties:e,...t}}function I(e,t){return{propType:"symbol",properties:e,...t}}function M(e,t){return{propType:"session",properties:e,...t}}function _(e,t){return{propType:"image",properties:e,...t}}function B(e,t){return{propType:"emoji",properties:e,...t}}function L(e,t){return{propType:"studyInputs",properties:e,...t}}var A=n(240534);function R(e,t,n,d){return{id:t,title:n,visible:d,groupType:"general",definitions:new A.WatchedValue(e)}}function F(e,t,n,d,o,i){return{id:d,title:o,visible:i,groupType:"checkableListOptions",checked:t,definitions:new A.WatchedValue(n),checkableListOptions:e}}function W(e,t,n){return{id:t,title:n,groupType:"leveledLines",definitions:new A.WatchedValue(e)}}var z=n(22982);function G(e,t,n){const d=new Map,o=void 0!==t?t[0]:e=>e,i=void 0!==t?void 0!==t[1]?t[1]:t[0]:e=>e,r={value:()=>o(e.value()),setValue:t=>{e.setValue?.(i(t))},subscribe:(t,n)=>{const o=()=>{n(r)};let i=d.get(t);void 0===i?(i=new Map,i.set(n,o),d.set(t,i)):i.set(n,o),e.subscribe(o)},unsubscribe:(t,n)=>{const o=d.get(t);if(void 0!==o){const t=o.get(n)
;void 0!==t&&(e.unsubscribe(t),o.delete(n))}},unsubscribeAll:t=>{const n=d.get(t);void 0!==n&&(n.forEach(((t,n)=>{e.unsubscribe(t)})),n.clear())}};return n&&(r.destroy=()=>n()),r}function O(e,t,n,d){const o=G(t,d),i=void 0!==d?void 0!==d[1]?d[1]:d[0]:e=>e;return o.setValue=d=>e.setWatchedValue(t,i(d),n),o}function H(e,t){return function(e,t,n,d){const o=new Map,i={subscribe:(n,d)=>{const i=e=>n(t(e));o.set(n,i),e.subscribe(i,d)},unsubscribe:t=>{if(t){const n=o.get(t);n&&(e.unsubscribe(n),o.delete(t))}else o.clear(),e.unsubscribe()},value:()=>t(e.value())};return G(i,n,d)}(e,(e=>e),t,(()=>e.release()))}function J(e,t){const n=(0,z.makeProxyDefinitionProperty)(t.weakReference());return n.setValue=t=>e.setPriceScaleSelectionStrategy(t),n}function j(e,t,n,d){const o=(0,z.makeProxyDefinitionProperty)(t.weakReference());return o.setValue=t=>{const o={lockScale:t};e.setPriceScaleMode(o,n,d)},o}function Z(e,t,n,d){const o=(0,z.makeProxyDefinitionProperty)(t.weakReference(),d);return o.setValue=d=>{e.setScaleRatioProperty(t,d,n)},o}var K=n(724377),U=n(589637),X=n(341991);function Y(e,t){if((0,U.isHexColor)(e)){const n=(0,K.parseRgb)(e);return(0,K.rgbaToString)((0,K.rgba)(n,(100-t)/100))}return e}function $(e,t,n,d,o){let i;if(null!==n){const e=(0,X.combineProperty)(Y,t.weakReference(),n.weakReference());i=(0,z.makeProxyDefinitionProperty)(e.ownership())}else i=(0,z.makeProxyDefinitionProperty)(t.weakReference(),[()=>Y(t.value(),0),e=>e]);return i.setValue=n=>{o&&e.beginUndoMacro(d),e.setProperty(t,n,d),o&&e.endUndoMacro()},i}function q(e,t,n,d,o,i){const r=[(u=n,a=t,e=>{const t=u(a);if(e===a.value()&&null!==t){const e=t.ticker||t.full_name;if(e)return e}return e}),e=>e];var u,a;const l=(0,z.convertToDefinitionProperty)(e,t,o,r);i&&(l.setValue=i);const s=new Map;l.subscribe=(e,n)=>{const d=e=>{n(l)};s.set(n,d),t.subscribe(e,d)},l.unsubscribe=(e,n)=>{const d=s.get(n);d&&(t.unsubscribe(e,d),s.delete(n))};const c={};return d.subscribe(c,(()=>{s.forEach(((e,t)=>{t(l)}))})),l.destroy=()=>{d.unsubscribeAll(c),s.clear()},l}function Q(e){return e.hasOwnProperty("propType")}function ee(e){return e.hasOwnProperty("groupType")}function te(e){return"line"===e.propType}function ne(e){return"color"===e.propType}function de(e){return"twoColors"===e.propType}function oe(e){return"options"===e.propType}function ie(e){return"checkableListOptions"===e.groupType}function re(e){e.forEach((e=>{if(Q(e)){Object.keys(e.properties).forEach((t=>{const n=e.properties[t];void 0!==n&&void 0!==n.destroy&&n.destroy()}))}else re(e.definitions.value()),e.visible?.destroy()}))}},460925:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12 4h3v1h-1.04l-.88 9.64a1.5 1.5 0 0 1-1.5 1.36H6.42a1.5 1.5 0 0 1-1.5-1.36L4.05 5H3V4h3v-.5C6 2.67 6.67 2 7.5 2h3c.83 0 1.5.67 1.5 1.5V4ZM7.5 3a.5.5 0 0 0-.5.5V4h4v-.5a.5.5 0 0 0-.5-.5h-3ZM5.05 5l.87 9.55a.5.5 0 0 0 .5.45h5.17a.5.5 0 0 0 .5-.45L12.94 5h-7.9Z"/></svg>'},844996:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z"/></svg>'},333765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},780427:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M23 8H5V7h18v1ZM9 14H5v-1h4v1Zm3 0h4v-1h-4v1Zm11 0h-4v-1h4v1ZM7 19H5v2h2v-2Zm2 0h2v2H9v-2Zm6 0h-2v2h2v-2Zm2 0h2v2h-2v-2Zm6 0h-2v2h2v-2Z"/></svg>'},666242:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M4.5 13.5H24m-19.5 0L8 17m-3.5-3.5L8 10"/></svg>'},443382:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8.5 13.5a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm0 0H24"/></svg>'},8295:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 21h-3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h3c2 0 4 1 4 3 0 1 0 2-1.5 3 1.5.5 2.5 2 2.5 4 0 2.75-2.638 4-5 4zM12 9l.004 3c.39.026.82 0 1.25 0C14.908 12 16 11.743 16 10.5c0-1.1-.996-1.5-2.5-1.5-.397 0-.927-.033-1.5 0zm0 5v5h1.5c1.5 0 3.5-.5 3.5-2.5S15 14 13.5 14c-.5 0-.895-.02-1.5 0z"/></svg>'},429285:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M12.143 20l1.714-12H12V7h5v1h-2.143l-1.714 12H15v1h-5v-1h2.143z"/></svg>'},818438:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});const d=function(){var e={base:"https://twemoji.maxcdn.com/v/13.0.1/",ext:".png",size:"72x72",className:"emoji",convert:{fromCodePoint:function(e){var t="string"==typeof e?parseInt(e,16):e;if(t<65536)return u(t);return u(55296+((t-=65536)>>10),56320+(1023&t))},toCodePoint:y},onerror:function(){this.parentNode&&this.parentNode.replaceChild(a(this.alt,!1),this)},parse:function(t,n){n&&"function"!=typeof n||(n={callback:n});return("string"==typeof t?m:p)(t,{callback:n.callback||s,attributes:"function"==typeof n.attributes?n.attributes:b,base:"string"==typeof n.base?n.base:e.base,ext:n.ext||e.ext,size:n.folder||(d=n.size||e.size,"number"==typeof d?d+"x"+d:d),className:n.className||e.className,onerror:n.onerror||e.onerror});var d},replace:v,test:function(e){n.lastIndex=0;var t=n.test(e);return n.lastIndex=0,t}},t={"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"
},n=/(?:\ud83d\udc68\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc68\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc68\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffe]|\ud83e\uddd1\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\u200d\ud83e\udd1d\u200d\ud83e\uddd1|\ud83d\udc6b\ud83c[\udffb-\udfff]|\ud83d\udc6c\ud83c[\udffb-\udfff]|\ud83d\udc6d\ud83c[\udffb-\udfff]|\ud83d[\udc6b-\udc6d])|(?:\ud83d[\udc68\udc69]|\ud83e\uddd1)(?:\ud83c[\udffb-\udfff])?\u200d(?:\u2695\ufe0f|\u2696\ufe0f|\u2708\ufe0f|\ud83c[\udf3e\udf73\udf7c\udf84\udf93\udfa4\udfa8\udfeb\udfed]|\ud83d[\udcbb\udcbc\udd27\udd2c\ude80\ude92]|\ud83e[\uddaf-\uddb3\uddbc\uddbd])|(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75]|\u26f9)((?:\ud83c[\udffb-\udfff]|\ufe0f)\u200d[\u2640\u2642]\ufe0f)|(?:\ud83c[\udfc3\udfc4\udfca]|\ud83d[\udc6e\udc70\udc71\udc73\udc77\udc81\udc82\udc86\udc87\ude45-\ude47\ude4b\ude4d\ude4e\udea3\udeb4-\udeb6]|\ud83e[\udd26\udd35\udd37-\udd39\udd3d\udd3e\uddb8\uddb9\uddcd-\uddcf\uddd6-\udddd])(?:\ud83c[\udffb-\udfff])?\u200d[\u2640\u2642]\ufe0f|(?:\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f|\ud83c\udff3\ufe0f\u200d\ud83c\udf08|\ud83c\udff4\u200d\u2620\ufe0f|\ud83d\udc15\u200d\ud83e\uddba|\ud83d\udc3b\u200d\u2744\ufe0f|\ud83d\udc41\u200d\ud83d\udde8|\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc6f\u200d\u2640\ufe0f|\ud83d\udc6f\u200d\u2642\ufe0f|\ud83e\udd3c\u200d\u2640\ufe0f|\ud83e\udd3c\u200d\u2642\ufe0f|\ud83e\uddde\u200d\u2640\ufe0f|\ud83e\uddde\u200d\u2642\ufe0f|\ud83e\udddf\u200d\u2640\ufe0f|\ud83e\udddf\u200d\u2642\ufe0f|\ud83d\udc08\u200d\u2b1b)|[#*0-9]\ufe0f?\u20e3|(?:[©®\u2122\u265f]\ufe0f)|(?:\ud83c[\udc04\udd70\udd71\udd7e\udd7f\ude02\ude1a\ude2f\ude37\udf21\udf24-\udf2c\udf36\udf7d\udf96\udf97\udf99-\udf9b\udf9e\udf9f\udfcd\udfce\udfd4-\udfdf\udff3\udff5\udff7]|\ud83d[\udc3f\udc41\udcfd\udd49\udd4a\udd6f\udd70\udd73\udd76-\udd79\udd87\udd8a-\udd8d\udda5\udda8\uddb1\uddb2\uddbc\uddc2-\uddc4\uddd1-\uddd3\udddc-\uddde\udde1\udde3\udde8\uddef\uddf3\uddfa\udecb\udecd-\udecf\udee0-\udee5\udee9\udef0\udef3]|[\u203c\u2049\u2139\u2194-\u2199\u21a9\u21aa\u231a\u231b\u2328\u23cf\u23ed-\u23ef\u23f1\u23f2\u23f8-\u23fa\u24c2\u25aa\u25ab\u25b6\u25c0\u25fb-\u25fe\u2600-\u2604\u260e\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262a\u262e\u262f\u2638-\u263a\u2640\u2642\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267b\u267f\u2692-\u2697\u2699\u269b\u269c\u26a0\u26a1\u26a7\u26aa\u26ab\u26b0\u26b1\u26bd\u26be\u26c4\u26c5\u26c8\u26cf\u26d1\u26d3\u26d4\u26e9\u26ea\u26f0-\u26f5\u26f8\u26fa\u26fd\u2702\u2708\u2709\u270f\u2712\u2714\u2716\u271d\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u2764\u27a1\u2934\u2935\u2b05-\u2b07\u2b1b\u2b1c\u2b50\u2b55\u3030\u303d\u3297\u3299])(?:\ufe0f|(?!\ufe0e))|(?:(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75\udd90]|[\u261d\u26f7\u26f9\u270c\u270d])(?:\ufe0f|(?!\ufe0e))|(?:\ud83c[\udf85\udfc2-\udfc4\udfc7\udfca]|\ud83d[\udc42\udc43\udc46-\udc50\udc66-\udc69\udc6e\udc70-\udc78\udc7c\udc81-\udc83\udc85-\udc87\udcaa\udd7a\udd95\udd96\ude45-\ude47\ude4b-\ude4f\udea3\udeb4-\udeb6\udec0\udecc]|\ud83e[\udd0c\udd0f\udd18-\udd1c\udd1e\udd1f\udd26\udd30-\udd39\udd3d\udd3e\udd77\uddb5\uddb6\uddb8\uddb9\uddbb\uddcd-\uddcf\uddd1-\udddd]|[\u270a\u270b]))(?:\ud83c[\udffb-\udfff])?|(?:\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc73\udb40\udc63\udb40\udc74\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc77\udb40\udc6c\udb40\udc73\udb40\udc7f|\ud83c\udde6\ud83c[\udde8-\uddec\uddee\uddf1\uddf2\uddf4\uddf6-\uddfa\uddfc\uddfd\uddff]|\ud83c\udde7\ud83c[\udde6\udde7\udde9-\uddef\uddf1-\uddf4\uddf6-\uddf9\uddfb\uddfc\uddfe\uddff]|\ud83c\udde8\ud83c[\udde6\udde8\udde9\uddeb-\uddee\uddf0-\uddf5\uddf7\uddfa-\uddff]|\ud83c\udde9\ud83c[\uddea\uddec\uddef\uddf0\uddf2\uddf4\uddff]|\ud83c\uddea\ud83c[\udde6\udde8\uddea\uddec\udded\uddf7-\uddfa]|\ud83c\uddeb\ud83c[\uddee-\uddf0\uddf2\uddf4\uddf7]|\ud83c\uddec\ud83c[\udde6\udde7\udde9-\uddee\uddf1-\uddf3\uddf5-\uddfa\uddfc\uddfe]|\ud83c\udded\ud83c[\uddf0\uddf2\uddf3\uddf7\uddf9\uddfa]|\ud83c\uddee\ud83c[\udde8-\uddea\uddf1-\uddf4\uddf6-\uddf9]|\ud83c\uddef\ud83c[\uddea\uddf2\uddf4\uddf5]|\ud83c\uddf0\ud83c[\uddea\uddec-\uddee\uddf2\uddf3\uddf5\uddf7\uddfc\uddfe\uddff]|\ud83c\uddf1\ud83c[\udde6-\udde8\uddee\uddf0\uddf7-\uddfb\uddfe]|\ud83c\uddf2\ud83c[\udde6\udde8-\udded\uddf0-\uddff]|\ud83c\uddf3\ud83c[\udde6\udde8\uddea-\uddec\uddee\uddf1\uddf4\uddf5\uddf7\uddfa\uddff]|\ud83c\uddf4\ud83c\uddf2|\ud83c\uddf5\ud83c[\udde6\uddea-\udded\uddf0-\uddf3\uddf7-\uddf9\uddfc\uddfe]|\ud83c\uddf6\ud83c\udde6|\ud83c\uddf7\ud83c[\uddea\uddf4\uddf8\uddfa\uddfc]|\ud83c\uddf8\ud83c[\udde6-\uddea\uddec-\uddf4\uddf7-\uddf9\uddfb\uddfd-\uddff]|\ud83c\uddf9\ud83c[\udde6\udde8\udde9\uddeb-\udded\uddef-\uddf4\uddf7\uddf9\uddfb\uddfc\uddff]|\ud83c\uddfa\ud83c[\udde6\uddec\uddf2\uddf3\uddf8\uddfe\uddff]|\ud83c\uddfb\ud83c[\udde6\udde8\uddea\uddec\uddee\uddf3\uddfa]|\ud83c\uddfc\ud83c[\uddeb\uddf8]|\ud83c\uddfd\ud83c\uddf0|\ud83c\uddfe\ud83c[\uddea\uddf9]|\ud83c\uddff\ud83c[\udde6\uddf2\uddfc]|\ud83c[\udccf\udd8e\udd91-\udd9a\udde6-\uddff\ude01\ude32-\ude36\ude38-\ude3a\ude50\ude51\udf00-\udf20\udf2d-\udf35\udf37-\udf7c\udf7e-\udf84\udf86-\udf93\udfa0-\udfc1\udfc5\udfc6\udfc8\udfc9\udfcf-\udfd3\udfe0-\udff0\udff4\udff8-\udfff]|\ud83d[\udc00-\udc3e\udc40\udc44\udc45\udc51-\udc65\udc6a\udc6f\udc79-\udc7b\udc7d-\udc80\udc84\udc88-\udca9\udcab-\udcfc\udcff-\udd3d\udd4b-\udd4e\udd50-\udd67\udda4\uddfb-\ude44\ude48-\ude4a\ude80-\udea2\udea4-\udeb3\udeb7-\udebf\udec1-\udec5\uded0-\uded2\uded5-\uded7\udeeb\udeec\udef4-\udefc\udfe0-\udfeb]|\ud83e[\udd0d\udd0e\udd10-\udd17\udd1d\udd20-\udd25\udd27-\udd2f\udd3a\udd3c\udd3f-\udd45\udd47-\udd76\udd78\udd7a-\uddb4\uddb7\uddba\uddbc-\uddcb\uddd0\uddde-\uddff\ude70-\ude74\ude78-\ude7a\ude80-\ude86\ude90-\udea8\udeb0-\udeb6\udec0-\udec2\uded0-\uded6]|[\u23e9-\u23ec\u23f0\u23f3\u267e\u26ce\u2705\u2728\u274c\u274e\u2753-\u2755\u2795-\u2797\u27b0\u27bf\ue50a])|\ufe0f/g,d=/\uFE0F/g,o=String.fromCharCode(8205),i=/[&<>'"]/g,r=/^(?:iframe|noframes|noscript|script|select|style|textarea)$/,u=String.fromCharCode
;return e;function a(e,t){return document.createTextNode(t?e.replace(d,""):e)}function l(e){return e.replace(i,h)}function s(e,t){return"".concat(t.base,t.size,"/",e,t.ext)}function c(e,t){for(var n,d,o=e.childNodes,i=o.length;i--;)3===(d=(n=o[i]).nodeType)?t.push(n):1!==d||"ownerSVGElement"in n||r.test(n.nodeName.toLowerCase())||c(n,t);return t}function f(e){return y(e.indexOf(o)<0?e.replace(d,""):e)}function p(e,t){for(var d,o,i,r,u,l,s,p,m,h,b,v,y,g=c(e,[]),E=g.length;E--;){for(i=!1,r=document.createDocumentFragment(),l=(u=g[E]).nodeValue,p=0;s=n.exec(l);){if((m=s.index)!==p&&r.appendChild(a(l.slice(p,m),!0)),v=f(b=s[0]),p=m+b.length,y=t.callback(v,t),v&&y){for(o in(h=new Image).onerror=t.onerror,h.setAttribute("draggable","false"),d=t.attributes(b,v))d.hasOwnProperty(o)&&0!==o.indexOf("on")&&!h.hasAttribute(o)&&h.setAttribute(o,d[o]);h.className=t.className,h.alt=b,h.src=y,i=!0,r.appendChild(h)}h||r.appendChild(a(b,!1)),h=null}i&&(p<l.length&&r.appendChild(a(l.slice(p),!0)),u.parentNode.replaceChild(r,u))}return e}function m(e,t){return v(e,(function(e){var n,d,o=e,i=f(e),r=t.callback(i,t);if(i&&r){for(d in o="<img ".concat('class="',t.className,'" ','draggable="false" ','alt="',e,'"',' src="',r,'"'),n=t.attributes(e,i))n.hasOwnProperty(d)&&0!==d.indexOf("on")&&-1===o.indexOf(" "+d+"=")&&(o=o.concat(" ",d,'="',l(n[d]),'"'));o=o.concat("/>")}return o}))}function h(e){return t[e]}function b(){return null}function v(e,t){return String(e).replace(n,t)}function y(e,t){for(var n=[],d=0,o=0,i=0;i<e.length;)d=e.charCodeAt(i++),o?(n.push((65536+(o-55296<<10)+(d-56320)).toString(16)),o=0):55296<=d&&d<=56319?o=d:n.push(d.toString(16));return n.join(t||"-")}}()}}]);