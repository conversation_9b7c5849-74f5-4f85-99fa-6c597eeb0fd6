"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[341,5055],{959107:(e,t,i)=>{i.d(t,{LevelsProperty:()=>P});var r=i(154834),s=i(916738),n=i(41899),o=i(792535),l=i(851068);const a={prefixes:[""],range:[0,0],names:["coeff","color","visible","linestyle","linewidth"],typecheck:{pack:()=>Object(),unpack:()=>[]}};function c(e,t,i,r){return r.push(i[t]),r}function h(e,t,i,r){return r[t]=i[e],r}function p(){return[]}function d(){return{}}function u(e,t,i){return i.prefixes.forEach((r=>{const s=r+"level";for(let r=i.range[0];r<=i.range[1];r++)if(e[s+r]&&(0,n.isSameType)(e[s+r],t.typecheck())){let n=t.tpl();i.names.forEach(((i,o)=>{n=t.fill(""+o,i,e[s+r],n)})),e[s+r]=n}})),e}function f(e,t,i){return i(e,{tpl:d,fill:h,typecheck:t.typecheck.unpack},t)}class P extends o.DefaultProperty{constructor(e){const{levelsIterator:t=u,map:i={},...r}=e,s={...a,...i};r.state&&(r.state=f(r.state,s,t)),super(r),this._map=s,this._levelsIterator=t}state(e,t,i){const r=super.state(e,t);return i?r:(s=r,n=this._map,(0,this._levelsIterator)(s,{tpl:p,fill:c,typecheck:n.typecheck.pack},n));var s,n}preferences(){return(0,o.extractState)(this.state(this._excludedDefaultsKeys,void 0,!0),this._allDefaultsKeys)}applyTemplate(e,t){this.mergeAndFire((0,o.extractState)((0,s.default)((0,r.default)(t),f(e,this._map,this._levelsIterator)),this._allStateKeys,this._excludedTemplateKeys))}saveDefaults(){this._useUserPreferences&&(0,l.saveDefaults)(this._defaultName,this.preferences())}clone(){return new P(this._options())}merge(e,t){return super.merge(this._map?f(e,this._map,this._levelsIterator):e,t)}_options(){return{...super._options(),map:{...this._map},levelsIterator:this._levelsIterator}}}},137086:(e,t,i)=>{i.r(t),i.d(t,{LineToolPitchfork:()=>m});var r=i(650151),s=i(272047),n=i(609838),o=i(621327),l=i(959107),a=i(979910),c=i(889868),h=i(981856),p=i(934135),d=i(515914);const u=new s.TranslatedString("erase level line",n.t(null,void 0,i(77114))),f=new s.TranslatedString("change {title} style",n.t(null,void 0,i(598463)));var P;!function(e){e[e.LevelsCount=8]="LevelsCount"}(P||(P={}));class m extends c.LineDataSource{constructor(e,t,i,r){super(e,t??m.createProperties(e.backgroundTheme().spawnOwnership()),i,r),this._properties.childs().style.subscribe(this,this._recreatePaneView),this._recreatePaneView()}pointsCount(){return 3}name(){return"Pitchfork"}levelsCount(){return 8}async additionalActions(e){const t=[],r=[{title:n.t(null,void 0,i(546005)),actionId:"Chart.LineTool.Pitchfork.ChangeTypeToOriginal"},{title:n.t(null,void 0,i(170382)),actionId:"Chart.LineTool.Pitchfork.ChangeTypeToModifiedSchiff"},{title:n.t(null,void 0,i(391612)),actionId:"Chart.LineTool.Pitchfork.ChangeTypeToInside"},{title:n.t(null,void 0,i(469904)),actionId:"Chart.LineTool.Pitchfork.ChangeTypeToSchiff"}];for(let i=0;i<4;i++){const n=new o.Action({actionId:r[i].actionId,options:{checked:this.properties().childs().style.value()===i,checkable:!0,label:r[i].title,onExecute:()=>{e.setProperty(this.properties().childs().style,i,f.format({
title:new s.TranslatedString(this.name(),this.translatedType())}),a.lineToolsDoNotAffectChartInvalidation),this.updateAllViews((0,p.sourceChangeEvent)(this.id())),this._model.updateSource(this)}}});t.push(n)}return{actions:[t[0],t[3],t[1],t[2]],placement:"CustomAction"}}processErase(e,t){const i="level"+t,s=(0,r.ensureDefined)(this.properties().child(i)).childs().visible;e.setProperty(s,!1,u,a.lineToolsDoNotAffectChartInvalidation)}static createProperties(e,t){const i=new l.LevelsProperty({defaultName:"linetoolpitchfork",state:t,map:{range:[0,8]},theme:e});return this._configureProperties(i),i}async _recreatePaneView(){Promise.all([i.e(6290),i.e(6881),i.e(5579),i.e(1583)]).then(i.bind(i,231286)).then((e=>{let t=[];const i=this._properties.childs().style.value();i===d.LineToolPitchforkStyle.Original?t=[new e.PitchforkLinePaneView(this,this._model)]:i===d.LineToolPitchforkStyle.Schiff?t=[new e.SchiffPitchforkLinePaneView(this,this._model)]:i===d.LineToolPitchforkStyle.Schiff2?t=[new e.SchiffPitchfork2LinePaneView(this,this._model)]:i===d.LineToolPitchforkStyle.Inside&&(t=[new e.InsidePitchforkLinePaneView(this,this._model)]),this._setPaneViews(t)}))}async _getPropertyDefinitionsViewModelClass(){return(await Promise.all([i.e(6406),i.e(8511),i.e(5234),i.e(4590),i.e(8537)]).then(i.bind(i,14509))).PitchForkDefinitionsViewModel}static _configureProperties(e){super._configureProperties(e);const t=[e.childs().median.childs().linewidth],i=[e.childs().median.childs().color];for(let s=0;s<=8;s++){const n=(0,r.ensureDefined)(e.child("level"+s));t.push((0,r.ensureDefined)(n.child("linewidth"))),i.push((0,r.ensureDefined)(n.child("color")))}e.addChild("linesColors",new h.LineToolColorsProperty(i)),e.addChild("linesWidths",new h.LineToolWidthsProperty(t))}}},109800:(e,t,i)=>{i.r(t),i.d(t,{LineToolSchiffPitchfork2:()=>n});var r=i(959107),s=i(137086);class n extends s.LineToolPitchfork{constructor(e,t,i,r){super(e,t??n.createProperties(e.backgroundTheme().spawnOwnership()),i,r)}name(){return"Schiff Pitchfork"}static createProperties(e,t){const i=new r.LevelsProperty({defaultName:"linetoolschiffpitchfork2",state:t,map:{range:[0,8]},theme:e});return this._configureProperties(i),i}}}}]);