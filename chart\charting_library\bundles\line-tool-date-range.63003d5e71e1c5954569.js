"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4273],{351502:(e,t,s)=>{s.d(t,{DateAndPriceBaseProperties:()=>y,allPropertiesStateKeysBase:()=>d,nonThemedFactoryDefaultsBase:()=>c,themedFactoryDefaultsBase:()=>m});var i=s(926048),r=s(111982),o=s(589637),l=s(811199),a=s(571772),n=s(915780);const u=(0,i.getHexColorByName)("color-tv-blue-500"),c={linewidth:2,fontsize:12,fillLabelBackground:!0,fillBackground:!0,backgroundTransparency:60,showVolume:!0,intervalsVisibilities:{...l.intervalsVisibilitiesDefaults},customText:{visible:!1,fontsize:12,bold:!1,italic:!1}},m=new Map([[r.StdTheme.Light,{textcolor:(0,i.getHexColorByName)("color-black"),labelBackgroundColor:(0,i.getHexColorByName)("color-white"),linecolor:u,backgroundColor:(0,o.generateColor)(u,85),shadow:"rgba(0, 0, 0, 0.2)",customText:{color:u}}],[r.StdTheme.Dark,{textcolor:(0,i.getHexColorByName)("color-white"),labelBackgroundColor:(0,i.getHexColorByName)("color-cold-gray-800"),linecolor:u,backgroundColor:(0,o.generateColor)(u,85),shadow:"rgba(0, 0, 0, 0.4)",customText:{color:u}}]]),d=["customText.text"];function h(e){const{showVolume:t=!0,...s}=e;return{...s,showVolume:t}}class y extends n.LineDataSourceProperty{constructor({nonThemedDefaultsKeys:e,themedDefaultsKeys:t,state:s,...i}){super({nonThemedDefaultsKeys:e,themedDefaultsKeys:t,templateKeys:[...e??[],...t??[],...d],state:s?h(s):void 0,...i});const r=s?.customText;this._textProperty=new a.Property(r?.text??""),this.childs().customText?.addChild("text",this._textProperty)}template(){const e=super.template();return e.customText.text=this._textProperty.value(),e}}},289379:(e,t,s)=>{s.r(t),s.d(t,{LineToolDateRange:()=>b});var i=s(650151),r=s(889868),o=s(342529),l=s(111982),a=s(792535),n=s(171721),u=s(351502),c=s(999710);const m={...u.nonThemedFactoryDefaultsBase,extendTop:!1,extendBottom:!1},d=u.themedFactoryDefaultsBase,h=(0,a.extractThemedColors)((0,i.ensureDefined)(d.get(l.StdTheme.Light)),(0,i.ensureDefined)(d.get(l.StdTheme.Dark))),y=(0,a.extractAllPropertiesKeys)((0,i.ensureDefined)(d.get(l.StdTheme.Light))),p=(0,a.extractAllPropertiesKeys)(m),f=[...new Set([...y,...p,...c.commonLineToolPropertiesStateKeys,...u.allPropertiesStateKeysBase])];class v extends u.DateAndPriceBaseProperties{static create(e,t){return new this({defaultName:"linetooldaterange",factoryDefaultsSupplier:()=>(0,n.factoryDefaultsForCurrentTheme)(m,d),nonThemedDefaultsKeys:p,themedDefaultsKeys:y,allStateKeys:f,themedColors:h,replaceThemedColorsOnThemeChange:!0,state:t,theme:e})}}class b extends r.LineDataSource{constructor(e,t,i,r){super(e,t??b.createProperties(e.backgroundTheme().spawnOwnership()),i,r),this._volumeCalculator=null,Promise.all([s.e(6290),s.e(6881),s.e(5579),s.e(1583)]).then(s.bind(s,304988)).then((e=>{this._setPaneViews([new e.DateRangePaneView(this,this._model)])}))}destroy(){super.destroy(),null!==this._volumeCalculator&&this._volumeCalculator.destroy()}pointsCount(){return 2}name(){return"Date Range"}template(){return this._properties.template()}volume(){
if(null===this._volumeCalculator)return NaN;const e=this.points();return this._volumeCalculator.volume(e[0].index,e[1].index)}setOwnerSource(e){e===this._model.mainSeries()&&((0,i.assert)(null===this._volumeCalculator),this._volumeCalculator=new o.SeriesTimeRangeVolumeCalculator(this._model.mainSeries())),super.setOwnerSource(e)}static createProperties(e,t){const s=v.create(e,t);return this._configureProperties(s),s}async _getPropertyDefinitionsViewModelClass(){return(await Promise.all([s.e(6406),s.e(8511),s.e(5234),s.e(4590),s.e(8537)]).then(s.bind(s,297366))).GeneralDatePriceRangeDefinitionsViewModel}}},999710:(e,t,s)=>{s.d(t,{commonLineToolPropertiesStateKeys:()=>i});const i=["symbolStateVersion","zOrderVersion","frozen","title","interval","symbol","currencyId","unitId","visible","intervalsVisibilities.ticks","intervalsVisibilities.seconds","intervalsVisibilities.secondsFrom","intervalsVisibilities.secondsTo","intervalsVisibilities.minutes","intervalsVisibilities.minutesFrom","intervalsVisibilities.minutesTo","intervalsVisibilities.hours","intervalsVisibilities.hoursFrom","intervalsVisibilities.hoursTo","intervalsVisibilities.days","intervalsVisibilities.daysFrom","intervalsVisibilities.daysTo","intervalsVisibilities.weeks","intervalsVisibilities.weeksFrom","intervalsVisibilities.weeksTo","intervalsVisibilities.months","intervalsVisibilities.monthsFrom","intervalsVisibilities.monthsTo","intervalsVisibilities.ranges"];var r,o,l;!function(e){e[e.NotShared=0]="NotShared",e[e.SharedInLayout=1]="SharedInLayout",e[e.GloballyShared=2]="GloballyShared"}(r||(r={})),function(e){e.BeforeAllAction="BeforeAll",e.CustomAction="CustomAction"}(o||(o={})),function(e){e.FloatingToolbarButton="FloatingToolbarButton",e.Default="Default"}(l||(l={}))},915780:(e,t,s)=>{s.d(t,{LineDataSourceProperty:()=>a});var i=s(154834),r=s(916738),o=s(650151),l=s(792535);class a extends l.DefaultProperty{constructor({templateKeys:e,...t}){super({ignoreAllowSavingDefaults:!0,saveNonDefaultUserPreferencesOnly:!0,...t}),this._templateKeys=(0,o.ensureDefined)(e||this._allDefaultsKeys)}template(){return(0,l.extractState)(this.state(),this._templateKeys,[])}applyTemplate(e){this.mergeAndFire((0,l.extractState)((0,r.default)((0,i.default)(this._factoryDefaultsSupplier()),e),this._templateKeys))}}},171721:(e,t,s)=>{s.d(t,{factoryDefaultsForCurrentTheme:()=>n});var i=s(916738),r=s(154834),o=s(650151),l=s(702054),a=s(111982);function n(e,t){const s=l.watchedTheme.value()??a.StdTheme.Light,n=(0,r.default)(e);return(0,i.default)(n,(0,o.ensureDefined)(t.get(s))),n}}}]);