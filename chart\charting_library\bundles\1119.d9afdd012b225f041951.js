"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1119],{365343:(e,i,t)=>{t.d(i,{convertToInt:()=>n,floor:()=>r,limitedPrecision:()=>l});var o=t(908206);function r(e){return Math.floor(e)}function n(e){return parseInt(String(e))}function l(e){const i=new o.LimitedPrecisionNumericFormatter(e,!0);return e=>{if(null===e)return e;const t=i.parse(i.format(e));return t.res?t.value:null}}},794086:(e,i,t)=>{t.d(i,{basePriceSources:()=>d,createPriceSourceDefinition:()=>u});var o=t(609838),r=t(761080),n=t(240534),l=t(798346),s=t(333385),a=t(162172);const c=o.t(null,void 0,t(384684)),d=[{title:o.t(null,void 0,t(16610)),value:"open",id:"price-source-open"},{title:o.t(null,void 0,t(778254)),value:"high",id:"price-source-high"},{title:o.t(null,void 0,t(165318)),value:"low",id:"price-source-low"},{title:o.t(null,void 0,t(862578)),value:"close",id:"price-source-close"},{title:o.t(null,void 0,t(469303)),value:"hl2",id:"price-source-hl2"},{title:o.t(null,void 0,t(927311)),value:"hlc3",id:"price-source-hlc3"},{title:o.t(null,void 0,t(604348)),value:"ohlc4",id:"price-source-ohlc4"}];function u(e,i,t,o,d,u){const p=void 0,h=(0,s.createWVFromGetterAndSubscriptions)((()=>!(0,a.isCloseBasedSymbol)(e.mainSeries().symbolInfo())),[e.mainSeries().dataEvents().symbolResolved(),e.mainSeries().dataEvents().symbolError()]);return(0,r.createOptionsPropertyDefinition)({disabled:p&&(0,l.convertFromReadonlyWVToDefinitionProperty)(p.weakReference()),option:(0,l.convertToDefinitionProperty)(e,i.priceSource,u),visible:(0,l.convertFromReadonlyWVToDefinitionProperty)(h.ownership())},{id:`${o}${d}`,title:c,options:new n.WatchedValue(t)})}},380932:(e,i,t)=>{t.d(i,{getSeriesStylePropertiesDefinitions:()=>xe});var o=t(609838),r=t(272047),n=t(798346),l=(t(112532),t(240534)),s=t(440891),a=t(41899),c=t(365343),d=t(871645),u=t(794086);const p=new r.TranslatedString("change line price source",o.t(null,void 0,t(741837))),h=new r.TranslatedString("change line color",o.t(null,void 0,t(888640))),y=new r.TranslatedString("change line width",o.t(null,void 0,t(948339))),f=o.t(null,void 0,t(903554));function g(e,i,t,o){return[(0,u.createPriceSourceDefinition)(e,i,t,o,"SymbolLinePriceSource",p),(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.color,null,h),gradientColor1:i.gradientStartColor&&(0,n.getColorDefinitionProperty)(e,i.gradientStartColor,null,h),gradientColor2:i.gradientEndColor&&(0,n.getColorDefinitionProperty)(e,i.gradientEndColor,null,h),type:i.colorType&&(0,n.convertToDefinitionProperty)(e,i.colorType,h),width:(0,n.convertToDefinitionProperty)(e,i.linewidth,y)},{id:`${o}SymbolLineStyle`,title:f})]}
const v=new r.TranslatedString("change color bars based on previous close",o.t(null,void 0,t(843090))),S=new r.TranslatedString("change HLC bars",o.t(null,void 0,t(427068))),b=new r.TranslatedString("change bar up color",o.t(null,void 0,t(33464))),P=new r.TranslatedString("change bar down color",o.t(null,void 0,t(459622))),w=new r.TranslatedString("change thin bars",o.t(null,void 0,t(760834))),m=new r.TranslatedString("change area price source",o.t(null,void 0,t(404640))),T=new r.TranslatedString("change area line color",o.t(null,void 0,t(429605))),D=new r.TranslatedString("change area line width",o.t(null,void 0,t(249965))),C=new r.TranslatedString("change area fill color",o.t(null,void 0,t(486866))),_=new r.TranslatedString("change baseline price source",o.t(null,void 0,t(868609))),$=new r.TranslatedString("change baseline top line color",o.t(null,void 0,t(669044))),k=new r.TranslatedString("change baseline top line width",o.t(null,void 0,t(268197))),W=new r.TranslatedString("change baseline bottom line color",o.t(null,void 0,t(171785))),M=new r.TranslatedString("change baseline bottom line width",o.t(null,void 0,t(756175))),V=new r.TranslatedString("change baseline fill top area color",o.t(null,void 0,t(592873))),L=new r.TranslatedString("change baseline fill bottom area color",o.t(null,void 0,t(997216))),I=new r.TranslatedString("change base level",o.t(null,void 0,t(942190))),B=new r.TranslatedString("change high-low body visibility",o.t(null,void 0,t(873021))),O=new r.TranslatedString("change high-low body color",o.t(null,void 0,t(206026))),E=new r.TranslatedString("change high-low borders visibility",o.t(null,void 0,t(615801))),x=new r.TranslatedString("change high-low border color",o.t(null,void 0,t(746844))),A=new r.TranslatedString("change high-low labels visibility",o.t(null,void 0,t(272399))),F=new r.TranslatedString("change high-low labels color",o.t(null,void 0,t(356961))),j=(new r.TranslatedString("change renko wick visibility",o.t(null,void 0,t(79604))),new r.TranslatedString("change renko wick up color",o.t(null,void 0,t(692277))),new r.TranslatedString("change renko wick down color",o.t(null,void 0,t(675487))),new r.TranslatedString("change the display of real prices on price scale (instead of Heiken-Ashi price)",o.t(null,void 0,t(816660))),new r.TranslatedString("change range thin bars",o.t(null,void 0,t(565821))),new r.TranslatedString("change range bars style",o.t(null,void 0,t(516241))),
new r.TranslatedString("change {candleType} body visibility",o.t(null,void 0,t(460608)))),N=new r.TranslatedString("change {candleType} up color",o.t(null,void 0,t(736697))),U=new r.TranslatedString("change {candleType} down color",o.t(null,void 0,t(564571))),H=new r.TranslatedString("change {candleType} border visibility",o.t(null,void 0,t(311114))),R=new r.TranslatedString("change {candleType} up border color",o.t(null,void 0,t(17214))),G=new r.TranslatedString("change {candleType} down border color",o.t(null,void 0,t(20291))),z=new r.TranslatedString("change {candleType} wick visibility",o.t(null,void 0,t(794750))),J=new r.TranslatedString("change {candleType} wick up color",o.t(null,void 0,t(747664))),q=new r.TranslatedString("change {candleType} wick down color",o.t(null,void 0,t(448091))),K=new r.TranslatedString("change {chartType} up color",o.t(null,void 0,t(506970))),Q=new r.TranslatedString("change {chartType} down color",o.t(null,void 0,t(405012))),X=new r.TranslatedString("change {chartType} projection bar up color",o.t(null,void 0,t(385032))),Y=new r.TranslatedString("change {chartType} projection bar down color",o.t(null,void 0,t(172545))),Z=new r.TranslatedString("change {chartType} border bar up color",o.t(null,void 0,t(428394))),ee=new r.TranslatedString("change {chartType} border bar down color",o.t(null,void 0,t(423053))),ie=new r.TranslatedString("change {chartType} projection border bar up color",o.t(null,void 0,t(742826))),te=new r.TranslatedString("change {chartType} projection border bar up color",o.t(null,void 0,t(742826))),oe=new r.TranslatedString("change column up color",o.t(null,void 0,t(688324))),re=new r.TranslatedString("change column down color",o.t(null,void 0,t(893890))),ne=new r.TranslatedString("change column price source",o.t(null,void 0,t(604727))),le=new r.TranslatedString("change HLC bars color",o.t(null,void 0,t(921201))),se=o.t(null,void 0,t(23111)),ae=o.t(null,void 0,t(800886)),ce=o.t(null,void 0,t(523091)),de=o.t(null,void 0,t(823743)),ue=o.t(null,void 0,t(620215)),pe=o.t(null,void 0,t(874406)),he=o.t(null,void 0,t(600333)),ye=o.t(null,void 0,t(632163)),fe=o.t(null,void 0,t(903554)),ge=o.t(null,void 0,t(889349)),ve=o.t(null,void 0,t(203159)),Se=o.t(null,void 0,t(741129)),be=o.t(null,void 0,t(461112)),Pe=o.t(null,void 0,t(373185)),we=o.t(null,void 0,t(536957)),me=o.t(null,void 0,t(817611)),Te=(o.t(null,void 0,t(177430)),o.t(null,void 0,t(373466))),De=o.t(null,void 0,t(388367)),Ce=(o.t(null,void 0,t(206160)),o.t(null,void 0,t(928975))),_e=o.t(null,void 0,t(874406)),$e=o.t(null,void 0,t(600333)),ke=o.t(null,void 0,t(5119)),We=o.t(null,void 0,t(756359)),Me=o.t(null,void 0,t(884831)),Ve=o.t(null,void 0,t(134579)),Le=(o.t(null,void 0,t(492516)),o.t(null,void 0,t(527377)),o.t(null,void 0,t(745054)),o.t(null,void 0,t(527377)));function Ie(e,i,t,o){return(0,n.createCheckablePropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.barColorsOnPrevClose,v)},{id:`${t}${o}`,title:se})}function Be(e,i,t,o){const r=(0,d.removeSpaces)(t.originalText());return[(0,
n.createTwoColorsPropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.drawBody,j.format({candleType:t})),color1:(0,n.getColorDefinitionProperty)(e,i.upColor,null,N.format({candleType:t})),color2:(0,n.getColorDefinitionProperty)(e,i.downColor,null,U.format({candleType:t}))},{id:`${o}Symbol${r}CandlesColor`,title:pe}),(0,n.createTwoColorsPropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.drawBorder,H.format({candleType:t})),color1:(0,n.getColorDefinitionProperty)(e,i.borderUpColor,null,R.format({candleType:t})),color2:(0,n.getColorDefinitionProperty)(e,i.borderDownColor,null,G.format({candleType:t}))},{id:`${o}Symbol${r}BordersColor`,title:he}),(0,n.createTwoColorsPropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.drawWick,z.format({candleType:t})),color1:(0,n.getColorDefinitionProperty)(e,i.wickUpColor,null,J.format({candleType:t})),color2:(0,n.getColorDefinitionProperty)(e,i.wickDownColor,null,q.format({candleType:t}))},{id:`${o}Symbol${r}WickColors`,title:ye})]}function Oe(e,i,t,o){const r=[];{const l=(0,d.removeSpaces)(t.originalText()),s=(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.upColor,null,K.format({chartType:t}))},{id:`${o}Symbol${l}UpBars`,title:we}),a=(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.downColor,null,Q.format({chartType:t}))},{id:`${o}Symbol${l}DownBars`,title:me}),c=(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.upColorProjection,null,X.format({chartType:t}))},{id:`${o}Symbol${l}ProjectionUpBars`,title:Te}),u=(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.downColorProjection,null,Y.format({chartType:t}))},{id:`${o}Symbol${l}ProjectionDownBars`,title:De});r.push(s,a,c,u)}return r}function Ee(e,i,t,o){const r=[];{const l=(0,d.removeSpaces)(t.originalText()),s=(0,n.createTwoColorsPropertyDefinition)({color1:(0,n.getColorDefinitionProperty)(e,i.upColor,null,K.format({chartType:t})),color2:(0,n.getColorDefinitionProperty)(e,i.borderUpColor,null,Z.format({chartType:t}))},{id:`${o}Symbol${l}UpBars`,title:we}),a=(0,n.createTwoColorsPropertyDefinition)({color1:(0,n.getColorDefinitionProperty)(e,i.downColor,null,Q.format({chartType:t})),color2:(0,n.getColorDefinitionProperty)(e,i.borderDownColor,null,ee.format({chartType:t}))},{id:`${o}Symbol${l}DownBars`,title:me}),c=(0,n.createTwoColorsPropertyDefinition)({color1:(0,n.getColorDefinitionProperty)(e,i.upColorProjection,null,X.format({chartType:t})),color2:(0,n.getColorDefinitionProperty)(e,i.borderUpColorProjection,null,ie.format({chartType:t}))},{id:`${o}Symbol${l}ProjectionUpBars`,title:Te}),u=(0,n.createTwoColorsPropertyDefinition)({color1:(0,n.getColorDefinitionProperty)(e,i.downColorProjection,null,Y.format({chartType:t})),color2:(0,n.getColorDefinitionProperty)(e,i.borderDownColorProjection,null,te.format({chartType:t}))},{id:`${o}Symbol${l}ProjectionDownBars`,title:De});r.push(s,a,c,u)}return r}function xe(e,i,d,p,h){switch(d){case 0:return function(e,i,t){
return[Ie(e,i,t,"SymbolBarStyleBarColorsOnPrevClose"),(0,n.createCheckablePropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.dontDrawOpen,S)},{id:`${t}SymbolDontDrawOpen`,title:ae}),(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.upColor,null,b)},{id:`${t}SymbolUpColor`,title:ce}),(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.downColor,null,P)},{id:`${t}SymbolDownColor`,title:de}),(0,n.createCheckablePropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.thinBars,w)},{id:`${t}SymbolBarThinBars`,title:ue})].filter(a.isExistent)}(e,i.barStyle.childs(),h);case 1:case 19:return function(e,i,n){return[Ie(e,i,n,"SymbolCandleStyleBarColorsOnPrevClose"),...Be(e,i,new r.TranslatedString("candle",o.t(null,void 0,t(821105))),n)].filter(a.isExistent)}(e,1===d?i.candleStyle.childs():i.volCandlesStyle.childs(),h);case 2:return g(e,i.lineStyle.childs(),p.seriesPriceSources,h);case 14:return g(e,i.lineWithMarkersStyle.childs(),p.seriesPriceSources,h);case 15:return g(e,i.steplineStyle.childs(),p.seriesPriceSources,h);case 3:return function(e,i,t,o){return[(0,u.createPriceSourceDefinition)(e,i,t,o,"SymbolAreaPriceSource",m),(0,n.createLinePropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.linecolor,null,T),width:(0,n.convertToDefinitionProperty)(e,i.linewidth,D)},{id:`${o}SymbolAreaLineStyle`,title:fe}),(0,n.createTwoColorsPropertyDefinition)({color1:(0,n.getColorDefinitionProperty)(e,i.color1,i.transparency,C),color2:(0,n.getColorDefinitionProperty)(e,i.color2,i.transparency,C)},{id:`${o}SymbolAreaFills`,title:Se})]}(e,i.areaStyle.childs(),p.seriesPriceSources,h);case 16:return function(e,i,t,o){return[(0,n.createLinePropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.highLineColor,null,T),width:(0,n.convertToDefinitionProperty)(e,i.highLineWidth,D)},{id:`${o}SymbolHLCAreaHighLineStyle`,title:We}),(0,n.createLinePropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.lowLineColor,null,T),width:(0,n.convertToDefinitionProperty)(e,i.lowLineWidth,D)},{id:`${o}SymbolHLCAreaLowLineStyle`,title:Ve}),(0,n.createLinePropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.closeLineColor,null,T),width:(0,n.convertToDefinitionProperty)(e,i.closeLineWidth,D)},{id:`${o}SymbolHLCAreaCloseLineStyle`,title:Me}),(0,n.createTwoColorsPropertyDefinition)({color1:(0,n.getColorDefinitionProperty)(e,i.highCloseFillColor,null,C),color2:(0,n.getColorDefinitionProperty)(e,i.closeLowFillColor,null,C)},{id:`${o}SymbolHLCAreaFills`,title:Se})]}(e,i.hlcAreaStyle.childs(),p.seriesPriceSources,h);case 9:return Be(e,i.hollowCandleStyle.childs(),new r.TranslatedString("hollow candles",o.t(null,void 0,t(292598))),h);case 10:return function(e,i,t,o){return[(0,u.createPriceSourceDefinition)(e,i,t,o,"SymbolBaseLinePriceSource",_),(0,n.createLinePropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.topLineColor,null,$),width:(0,n.convertToDefinitionProperty)(e,i.topLineWidth,k)},{id:`${o}SymbolBaseLineTopLine`,title:ge}),(0,
n.createLinePropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.bottomLineColor,null,W),width:(0,n.convertToDefinitionProperty)(e,i.bottomLineWidth,M)},{id:`${o}SymbolBaseLineBottomLine`,title:ve}),(0,n.createTwoColorsPropertyDefinition)({color1:(0,n.getColorDefinitionProperty)(e,i.topFillColor1,null,V),color2:(0,n.getColorDefinitionProperty)(e,i.topFillColor2,null,V)},{id:`${o}SymbolBaseLineTopFills`,title:be}),(0,n.createTwoColorsPropertyDefinition)({color1:(0,n.getColorDefinitionProperty)(e,i.bottomFillColor1,null,L),color2:(0,n.getColorDefinitionProperty)(e,i.bottomFillColor2,null,L)},{id:`${o}SymbolBaseLineBottomFills`,title:Pe}),(0,n.createNumberPropertyDefinition)({value:(0,n.convertToDefinitionProperty)(e,i.baseLevelPercentage,I,[c.floor])},{id:`${o}SymbolBaseLevelPercentage`,title:Ce,type:0,min:new l.WatchedValue(0),max:new l.WatchedValue(100),step:new l.WatchedValue(1),unit:new l.WatchedValue("%")})]}(e,i.baselineStyle.childs(),p.seriesPriceSources,h);case 13:return function(e,i,t,o){return[(0,u.createPriceSourceDefinition)(e,i,t,o,"SymbolColumnPriceSource",ne),Ie(e,i,o,"SymbolColumnStyleColumnColorsOnPrevClose"),(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.upColor,null,oe)},{id:`${o}SymbolUpColor`,title:ce}),(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.downColor,null,re)},{id:`${o}SymbolDownColor`,title:de})].filter(a.isExistent)}(e,i.columnStyle.childs(),p.seriesPriceSources,h);case 21:return function(e,i,t){return[(0,n.createColorPropertyDefinition)({color:(0,n.getColorDefinitionProperty)(e,i.color,null,le)},{id:`${t}SymbolColor`,title:Le}),(0,n.createCheckablePropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.thinBars,w)},{id:`${t}SymbolBarThinBars`,title:ue})].filter(a.isExistent)}(e,i.hlcBarsStyle.childs(),h)}if(12===d&&s.enabled("chart_style_hilo")){return function(e,i,t){return[(0,n.createColorPropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.drawBody,B),color:(0,n.getColorDefinitionProperty)(e,i.color,null,O)},{id:`${t}SymbolBodiesColor`,title:_e}),(0,n.createColorPropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.showBorders,E),color:(0,n.getColorDefinitionProperty)(e,i.borderColor,null,x)},{id:`${t}SymbolBorderColor`,title:$e}),(0,n.createTextPropertyDefinition)({checked:(0,n.convertToDefinitionProperty)(e,i.showLabels,A),color:(0,n.getColorDefinitionProperty)(e,i.labelColor,null,F)},{id:`${t}SymbolLabels`,title:ke,isEditable:!1,isMultiLine:!1})]}(e,i.hiloStyle.childs(),h)}if(!i.hasOwnProperty("haStyle"))return[];if(p.isJapaneseChartsAvailable&&8===d){return function(e,i,n){const l=[];return l.push(Ie(e,i,n,"SymbolHAStyleBarColorsOnPrevClose"),...Be(e,i,new r.TranslatedString("Heikin Ashi",o.t(null,void 0,t(863876))),n)),l}(e,i.haStyle.childs(),h)}if(p.isJapaneseChartsAvailable&&s.enabled("japanese_chart_styles")){if(4===d||5===d||6===d||7===d||8===d)switch(d){case 4:return function(e,i,n){return[...Ee(e,i,new r.TranslatedString("renko",o.t(null,void 0,t(170282))),n)]
}(e,i.renkoStyle.childs(),h);case 5:return Oe(e,i.kagiStyle.childs(),new r.TranslatedString("kagi",o.t(null,void 0,t(310073))),h);case 6:return Oe(e,i.pnfStyle.childs(),new r.TranslatedString("point & figure",o.t(null,void 0,t(31562))),h);case 7:return Ee(e,i.pbStyle.childs(),new r.TranslatedString("line break",o.t(null,void 0,t(529152))),h)}0}return[]}},71119:(e,i,t)=>{t.d(i,{SeriesPropertyDefinitionsViewModel:()=>U,seriesPrecisionValues:()=>N});var o=t(650151),r=t(671945),n=t(609838),l=t(272047),s=t(440891),a=t(798346),c=t(983023),d=t(240534),u=t(676725),p=t(162172),h=t(380932),y=t(14043),f=t(365343);const g=new l.TranslatedString("change {inputName} property",n.t(null,void 0,t(466110)));function v(e){return e.map((e=>({value:e,title:(0,y.getTranslatedInputTitle)(e)})))}const S=new Set(["percentageLTP"]);function b(e,i,t,r,n,s,c){const u=[];return t.forEach((t=>{if(!function(e,i){return!e.isHidden&&(void 0===e.visible||function(e,i){if(!e)return!0;const t=e.split("==");return!(t.length<2)&&i[t[0]].value()===t[1]}(e.visible,i))}(t,r))return;const h=t.id;if(!r.hasOwnProperty(h))return;const b=r[h],P=function(e,i){return"style"===e.id?"Box size assignment method":"boxSize"===e.id?"Box size":i.childs().name.value()}(t,n[h]),w=(0,y.getTranslatedInputTitle)(P),m=new l.TranslatedString(P,w);if("options"in t){const i=(0,o.ensure)(t.options);u.push((0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(e,b,g.format({inputName:m}))},{id:`${c}${t.name}`,title:w,options:new d.WatchedValue(v(i))}))}else if("integer"!==t.type){if("float"===t.type){let o;return o=function(e,i){return!((i===(0,p.chartStyleStudyId)(4)||i===(0,p.chartStyleStudyId)(6))&&"boxSize"===e||i===(0,p.chartStyleStudyId)(5)&&"reversalAmount"===e)}(h,i)||null===s.value()?new d.WatchedValue(t.min):s,void u.push((0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(e,b,g.format({inputName:m}))},{id:`${c}${t.name}`,title:w,type:1,min:o,max:new d.WatchedValue(t.max),unit:S.has(t.id)?new d.WatchedValue("%"):void 0,defval:t.defval}))}"text"!==t.type?"bool"!==t.type||u.push((0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,b,g.format({inputName:m}))},{id:`${c}${t.name}`,title:w})):u.push((0,a.createTextPropertyDefinition)({text:(0,a.convertToDefinitionProperty)(e,b,g.format({inputName:m}))},{id:`${c}${t.name}`,title:w,isEditable:!0,isMultiLine:!1}))}else u.push((0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(e,b,g.format({inputName:m}),[f.floor])},{id:`${c}${t.name}`,title:w,type:0,min:new d.WatchedValue(t.min),max:new d.WatchedValue(t.max),unit:S.has(t.id)?new d.WatchedValue("%"):void 0,defval:t.defval}))})),u}var P=t(248361),w=t(341991),m=t(658895),T=t(140920),D=t(794086);const C=(0,
r.getLogger)("Chart.Definitions.Series"),_=s.enabled("pre_post_market_sessions"),$=new l.TranslatedString("change decimal places",n.t(null,void 0,t(482063))),k=new l.TranslatedString("change timezone",n.t(null,void 0,t(920137))),W=new l.TranslatedString("toggle intraday inactivity gaps",n.t(null,void 0,t(28871))),M=(new l.TranslatedString("adjust data for dividends",n.t(null,void 0,t(818077))),new l.TranslatedString("use settlement as close on daily interval",n.t(null,void 0,t(992978))),new l.TranslatedString("adjust for contract changes",n.t(null,void 0,t(601433))),new l.TranslatedString("change session",n.t(null,void 0,t(887041)))),V=new l.TranslatedString("change extended hours color",n.t(null,void 0,t(844223))),L=new l.TranslatedString("change pre market color",n.t(null,void 0,t(344371))),I=new l.TranslatedString("change post market color",n.t(null,void 0,t(538730))),B=(new l.TranslatedString("change electronic trading hours",n.t(null,void 0,t(47393))),n.t(null,void 0,t(493020)),n.t(null,void 0,t(194031))),O=(n.t(null,void 0,t(768921)),n.t(null,void 0,t(549545)),n.t(null,void 0,t(188327))),E=(n.t(null,void 0,t(759777)),n.t(null,void 0,t(159766))),x=n.t(null,void 0,t(877073)),A=n.t(null,void 0,t(916564)),F=n.t(null,void 0,t(589212)),j=[{priceScale:2,minMove:1,frac:!0},{priceScale:4,minMove:1,frac:!0},{priceScale:8,minMove:1,frac:!0},{priceScale:16,minMove:1,frac:!0},{priceScale:32,minMove:1,frac:!0},{priceScale:64,minMove:1,frac:!0},{priceScale:128,minMove:1,frac:!0},{priceScale:320,minMove:1,frac:!0}];function N(){const e=[{title:A,value:"default"}],i=function(e=15){const i=[];for(let t=0;t<=e;t++)i.push({priceScale:Math.pow(10,t),minMove:1,frac:!1});return i}();for(let o=0;o<i.length;o++){const r=Math.log10(i[o].priceScale),l=0===r?n.t(null,void 0,t(447326)):n.t(null,{plural:"{value} decimals",count:r,replace:{value:r.toString()}},t(959644));e.push({title:l,value:`${i[o].priceScale},${i[o].minMove},${i[o].frac}`})}for(let i=0;i<j.length;i++)e.push({title:`${j[i].minMove}/${j[i].priceScale}`,value:`${j[i].priceScale},${j[i].minMove},${j[i].frac}`});return e}class U{constructor(e,i,t,o,r,n){this._definitions=null,this._inputsSubscriptions=null,this._isDestroyed=!1,this._propertyPages=null,this._seriesMinTickWV=null,this._sessionIdOptionsWV=new d.WatchedValue([]),this._series=e,this._undoModel=i,this._model=this._undoModel.model(),this._propertyPageId=t,this._propertyPageName=o,this._propertyPageIcon=r,this._timezonePropertyObj=n,this._series.onStyleChanged().subscribe(this,this._updateDefinitions),this._series.properties().childs().rangeStyle.childs().barStyle.subscribe(this,this._updateDefinitions),this._series.dataEvents().symbolResolved().subscribe(this,this._updateSeriesMinTickWV),this._series.dataEvents().symbolResolved().subscribe(this,this._updateSessionIdOptionsWV),this._updateSeriesMinTickWV(),this._updateSessionIdOptionsWV()}destroy(){null!==this._propertyPages&&this._propertyPages.forEach((e=>{(0,a.destroyDefinitions)(e.definitions.value())})),
this._series.onStyleChanged().unsubscribe(this,this._updateDefinitions),this._series.properties().childs().rangeStyle.childs().barStyle.unsubscribeAll(this),this._series.dataEvents().symbolResolved().unsubscribeAll(this),this._unsubscribeInputsUpdate(),this._isDestroyed=!0}propertyPages(){return null===this._propertyPages?this._getDefinitions().then((e=>{if(this._isDestroyed)throw new Error("SeriesPropertyDefinitionsViewModel already destroyed");return null===this._propertyPages&&(this._propertyPages=[{id:this._propertyPageId,title:this._propertyPageName,icon:this._propertyPageIcon,definitions:new d.WatchedValue(e.definitions),visible:e.visible??new d.WatchedValue(!0).readonly()}]),this._propertyPages})):Promise.resolve(this._propertyPages)}_seriesMinTick(){const e=this._series.symbolInfo();return null!==e?e.minmov/e.pricescale:null}_updateSeriesMinTickWV(){null===this._seriesMinTickWV?this._seriesMinTickWV=new d.WatchedValue(this._seriesMinTick()):this._seriesMinTickWV.setValue(this._seriesMinTick())}_updateSessionIdOptionsWV(){if(!_)return;const e=this._series.symbolInfo();if(null===e)return;const i=(e.subsessions||[]).filter((e=>!e.private)).map((e=>({title:(0,T.translateSessionDescription)(e.description),value:e.id})));this._sessionIdOptionsWV.setValue(i)}_updateDefinitions(){null!==this._definitions&&(0,a.destroyDefinitions)(this._definitions.definitions),this._definitions=null,this._createSeriesDefinitions().then((e=>{if(this._isDestroyed)throw new Error("SeriesPropertyDefinitionsViewModel already destroyed");(0,o.ensureNotNull)(this._propertyPages)[0].definitions.setValue(e.definitions)}))}_getDefinitions(){return null===this._definitions?this._createSeriesDefinitions():Promise.resolve(this._definitions)}_unsubscribeInputsUpdate(){null!==this._inputsSubscriptions&&(this._inputsSubscriptions.forEach((e=>{e.unsubscribeAll(this)})),this._inputsSubscriptions=null)}_subscribeInputsUpdate(e,i){this._unsubscribeInputsUpdate();const t=[];e.forEach((e=>{if(void 0!==e.visible){const o=e.visible.split("==");if(2===o.length){const e=i[o[0]];-1===t.indexOf(e)&&(e.subscribe(this,this._updateDefinitions),t.push(e))}}})),t.length>0?this._inputsSubscriptions=t:this._inputsSubscriptions=null}async _createSeriesDefinitions(){const e=this._series.properties().childs(),i=this._series.getInputsProperties(),r=this._series.getInputsInfoProperties(),l=e.style.value(),y=this._series.getStyleShortName(),f=c.chartStylesWithAttachedStudies.includes(l)?null:(0,p.chartStyleStudyId)(l);let g,v=null;if(null!==f){let e;try{e=await(0,u.studyMetaInfoRepository)().findById({type:"java",studyId:f});const t=(0,o.ensureNotNull)(this._seriesMinTickWV);v=b(this._undoModel,e.id,e.inputs,i,r,t,y),this._subscribeInputsUpdate(e.inputs,i)}catch(i){C.logWarn(`Find meta info for create series definitions with error - ${(0,P.errorToString)(i)}`),e=null}if(this._isDestroyed)throw new Error("SeriesPropertyDefinitionsViewModel already destroyed")}else this._unsubscribeInputsUpdate()
;if(this._isDestroyed)throw new Error("SeriesPropertyDefinitionsViewModel already destroyed");if(c.chartStylesWithAttachedStudies.includes(l))throw new Error("unexpected chart style");{const i=(0,h.getSeriesStylePropertiesDefinitions)(this._undoModel,e,l,{seriesPriceSources:D.basePriceSources,isJapaneseChartsAvailable:true},"mainSeries");null!==v&&i.push(...v),g=(0,a.createPropertyDefinitionsGeneralGroup)(i,"generalSymbolStylesGroup",(0,p.getTranslatedChartStyleName)(l))}const S=(0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(this._undoModel,e.minTick,$),visible:(0,a.convertFromReadonlyWVToDefinitionProperty)(new d.WatchedValue(!0).ownership())},{id:`${y}SymbolMinTick`,title:E,options:new d.WatchedValue(N())}),w=(0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(this._undoModel,this._timezonePropertyObj.property,k)},{id:`${y}SymbolTimezone`,title:x,options:new d.WatchedValue(this._timezonePropertyObj.values)}),m=[...await this._seriesDataDefinitions(y),S,w];return s.enabled("intraday_inactivity_gaps")&&m.push((0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._undoModel,this._undoModel.model().properties().childs().inactivityGaps,W)},{id:`${y}InactivityGap`,title:F})),this._definitions={definitions:[g,(0,a.createPropertyDefinitionsGeneralGroup)(m,"dataModififcationGroup",n.t(null,void 0,t(848225)))]},this._definitions}async _seriesDataDefinitions(e){const i=[];if(_){const t=this._series.sessionIdProxyProperty(),o=(0,m.combineWithFilteredUpdate)(((e,i)=>!i&&(0,p.symbolHasSeveralSessions)(this._series.symbolInfo())),((e,i)=>i||!e),this._series.symbolResolvingActive().weakReference(),(0,w.createWVFromProperty)(this._series.isDWMProperty()).ownership()),r=(0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(this._undoModel,t,M),visible:(0,a.convertFromReadonlyWVToDefinitionProperty)(o.ownership())},{id:"sessionId",title:B,options:this._sessionIdOptionsWV});i.push(r);const n=(await this._model.sessions().promise()).graphicsInfo();let l=!1;n.backgrounds&&(l=void 0!==n.backgrounds.outOfSession);const s=await(l?this._createOutOfSessionDefinition(e):this._createPrePostMarketDefinition(e));i.push(s);const c=this._createElectronicSessionDefinition(e);c&&i.push(c)}return i}_createOutOfSessionDefinition(e){const i=this._model.sessions().properties().childs().sessionHighlight.childs().backgrounds.childs().outOfSession.childs();return(0,a.createColorPropertyDefinition)({color:(0,a.getColorDefinitionProperty)(this._undoModel,i.color,i.transparency,V)},{id:`${e}SymbolExtendedHoursColors`,title:O})}_createPrePostMarketDefinition(e){const i=(0,w.createWVFromGetterAndSubscription)((()=>this._series.symbolInfo()),this._series.dataEvents().symbolResolved()),t=(0,m.combineWithFilteredUpdate)(((e,i)=>!e&&!!i&&(0,p.symbolHasPreOrPostMarket)(i)&&!(0,p.isRegularSessionId)(this._series.sessionIdProxyProperty().value(),i)),((e,i)=>e||!!i),(0,
w.createWVFromProperty)(this._series.isDWMProperty()).ownership(),i.ownership()),o=this._model.sessions().properties().childs().sessionHighlight.childs(),r=o.backgrounds.childs().preMarket.childs(),n=o.backgrounds.childs().postMarket.childs();return(0,a.createTwoColorsPropertyDefinition)({color1:(0,a.getColorDefinitionProperty)(this._undoModel,r.color,r.transparency,L),color2:(0,a.getColorDefinitionProperty)(this._undoModel,n.color,n.transparency,I),visible:(0,a.convertFromReadonlyWVToDefinitionProperty)(t.ownership())},{id:`${e}SymbolExtendedHoursColors`,title:O})}_createElectronicSessionDefinition(e){return null}}}}]);