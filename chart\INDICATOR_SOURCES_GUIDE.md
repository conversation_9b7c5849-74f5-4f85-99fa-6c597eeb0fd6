# TradingView Indicator Sources Guide

## Overview

This guide explains how to use the enhanced indicator sources functionality that automatically makes indicator outputs available as data sources for other indicators, just like in the real TradingView platform.

## Problem Solved

**Before**: When adding indicators and going to settings, you only saw basic sources like:
- open, high, low, close, hl2, hlc3, ohlc4, volume, oi

**After**: Now you see all indicator outputs as sources, such as:
- RSI: RSI
- MACD: MACD, MACD: Signal, MACD: Histogram  
- Bollinger Bands: Upper Band, Bollinger Bands: Middle Band, Bollinger Bands: Lower Band
- Moving Average: MA
- And many more...

## How It Works

1. **Automatic Detection**: When you add an indicator, the system automatically detects it and adds its outputs to the available sources list.

2. **Dynamic Updates**: The sources list updates in real-time as you add or remove indicators.

3. **Smart Mapping**: The system knows the default outputs for common indicators (RSI, MACD, Bollinger Bands, etc.).

## Files Modified

### Main Implementation: `chart/index5.html`

Key changes made:
- Enhanced `sourcesConfig` object with indicator tracking
- Added `addIndicatorSource()` and `removeIndicatorSource()` methods
- Modified `IndicatorManager` to automatically register indicator sources
- Updated flexible indicator definitions to use dynamic sources

### Demo File: `chart/indicator_sources_demo.html`

A standalone demo showing the functionality in action.

## Usage

### Basic Usage

1. **Open your chart** (index5.html or indicator_sources_demo.html)

2. **Add some indicators**:
   ```javascript
   addRSI(14)           // Adds RSI indicator
   addMACD(12, 26, 9)   // Adds MACD indicator
   addBollingerBands(20, 2)  // Adds Bollinger Bands
   ```

3. **Add a Flexible indicator**:
   ```javascript
   addFlexibleSMA(20, 'close')  // Uses close price
   addFlexibleRSI(14, 'RSI: RSI')  // Uses RSI output as source!
   ```

4. **Check the sources**: When you go to the Flexible SMA/RSI settings, you'll see all indicator outputs in the source dropdown.

### Available Functions

#### Indicator Management
```javascript
// Add indicators
addRSI(period=14, overlay=false)
addMACD(fast=12, slow=26, signal=9, overlay=false)
addBollingerBands(period=20, stdDev=2)
addMovingAverage(period=20, type='SMA')  // type: 'SMA', 'EMA', 'WMA'

// Flexible indicators (can use any source)
addFlexibleSMA(period=14, source='close')
addFlexibleRSI(period=14, source='close', overbought=70, oversold=30)

// Management
listIndicators()                    // Show all current indicators
removeIndicator(id)                // Remove specific indicator
```

#### Source Management
```javascript
// View sources
getSources()                       // Show all available sources
refreshIndicatorSources()          // Refresh all indicator sources
refreshFlexibleIndicators()        // Update flexible indicator definitions

// Manual source management
addSource('custom')                // Add custom source
removeSource('volume')             // Remove a source
updateSources(['open', 'high', 'low', 'close', 'custom'])  // Set sources
```

### Example Workflow

1. **Start with basic indicators**:
   ```javascript
   addRSI(14)                      // Adds "RSI: RSI" to sources
   addMACD(12, 26, 9)             // Adds "MACD: MACD", "MACD: Signal", "MACD: Histogram"
   addBollingerBands(20, 2)       // Adds "Bollinger Bands: Upper Band", etc.
   ```

2. **Check available sources**:
   ```javascript
   getSources()  // See all sources including new indicator outputs
   ```

3. **Use indicator outputs as sources**:
   ```javascript
   // Create RSI based on Bollinger Bands Middle Band
   addFlexibleRSI(14, 'Bollinger Bands: Middle Band')
   
   // Create SMA based on RSI values
   addFlexibleSMA(10, 'RSI: RSI')
   ```

## Supported Indicators

The system automatically recognizes outputs for these indicators:

| Indicator | Outputs |
|-----------|---------|
| RSI | RSI |
| MACD | MACD, Signal, Histogram |
| Moving Average | MA |
| Moving Average Exponential | EMA |
| Moving Average Weighted | WMA |
| Bollinger Bands | Upper Band, Middle Band, Lower Band |
| Stochastic | %K, %D |
| Williams %R | %R |
| Volume | Volume |
| ATR | ATR |
| CCI | CCI |
| Momentum | Momentum |
| ROC | ROC |
| Aroon | Aroon Up, Aroon Down |
| ADX | ADX, +DI, -DI |
| Parabolic SAR | SAR |
| Ichimoku Cloud | Tenkan, Kijun, Senkou A, Senkou B |
| VWAP | VWAP |
| And many more... | |

## Demo

Open `chart/indicator_sources_demo.html` to see the functionality in action:

1. Use the buttons to add various indicators
2. Click "Show Sources" to see how the sources list grows
3. Add a Flexible SMA or RSI and check the source dropdown
4. You'll see all indicator outputs available as sources!

## Troubleshooting

### Sources not updating?
```javascript
refreshIndicatorSources()  // Manually refresh all sources
refreshFlexibleIndicators() // Update flexible indicator definitions
```

### Want to see what's happening?
```javascript
getSources()              // Shows current sources with details
listIndicators()          // Shows all current indicators
```

### Need to reset?
```javascript
// Reset to basic sources
updateSources(['open','high','low','close','hl2','hlc3','ohlc4','volume','oi'])
```

## Technical Details

- **Storage**: Indicator sources are stored in `window.sourcesConfig._indicatorSources` Map
- **Updates**: Sources are automatically updated when indicators are added/removed
- **Compatibility**: Maintains backward compatibility with existing code
- **Performance**: Minimal overhead, only tracks indicator metadata

## Benefits

1. **Just like TradingView**: Now your library behaves exactly like the real TradingView platform
2. **Advanced Analysis**: Create indicators based on other indicators
3. **Automatic**: No manual configuration needed
4. **Flexible**: Works with all existing and new indicators
5. **Real-time**: Sources update immediately when indicators change

This enhancement makes your TradingView library much more powerful and user-friendly!
