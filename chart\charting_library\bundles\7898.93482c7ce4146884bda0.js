"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7898],{429498:(t,e,s)=>{s.d(e,{changeDescription:()=>T,create:()=>y,createCrossTabSyncMiddleware:()=>I,exact:()=>d,exclude:()=>p,getListById:()=>E,getListIds:()=>v,getListsMap:()=>L,insert:()=>u,put:()=>h,reducer:()=>_,remove:()=>S,rename:()=>b,replace:()=>m,setup:()=>c,updatePersistedState:()=>g});var o=s(891442);function i(t,e){if(t.length!==e.length)return!1;for(let s=0;s<t.length;++s)if(t[s]!==e[s])return!1;return!0}const n=Symbol();const a={lists:{ids:[],byId:{}},timestamp:null};function r(t,e){t.lists.ids.push(e.id),t.lists.byId[e.id]={id:e.id,name:e.name,description:e.description,symbols:[...e.symbols],shared:e.shared,timestamp:e.timestamp,collapsedSeparators:e.collapsedSeparators,persistedState:e.persistedState,lastChangeTimestamp:e.lastChangeTimestamp}}const l=(0,o.createSlice)({name:"custom-lists",initialState:a,reducers:{setup:(t,e)=>{const{lists:s,timestamp:o}=e.payload,i=new Set(s.map((t=>t.id))),n=new Set(t.lists.ids.filter((t=>!i.has(t))));for(const e of n)delete t.lists.byId[e];t.lists.ids=[...i];for(const e of s){const s=E(t,e.id);if(s){if(s.lastChangeTimestamp>=e.lastChangeTimestamp)continue;s.name=e.name,s.description=e.description,s.symbols=[...e.symbols],s.shared=e.shared,s.timestamp=e.timestamp,s.collapsedSeparators=e.collapsedSeparators,s.persistedState=e.persistedState,void 0!==e.lastChangeTimestamp&&(s.lastChangeTimestamp=e.lastChangeTimestamp)}else t.lists.byId[e.id]={id:e.id,name:e.name,description:e.description,symbols:[...e.symbols],shared:e.shared,timestamp:e.timestamp,collapsedSeparators:e.collapsedSeparators,persistedState:e.persistedState,lastChangeTimestamp:e.lastChangeTimestamp}}t.timestamp=o},insert:(t,e)=>{const{id:s,symbols:o,before:a,actionTimestamp:r}=e.payload,{lists:l}=t,c=l.byId[s];if(void 0===c)return;if(c.lastChangeTimestamp>=r)return;const u=new Set(o);let m;if(void 0!==a&&u.has(a)){m=n;for(let t=c.symbols.indexOf(a);t>=0;--t)if(!u.has(c.symbols[t])){m=c.symbols[t];break}}const d=c.symbols.filter((t=>!u.has(t))),p=function(t,e,s){const o=void 0===s?void 0!==e?t.indexOf(e):void 0:s===n?0:t.indexOf(s)+1;return-1!==o?o:void 0}(d,a,m);void 0===p?d.push(...u):d.splice(p,0,...u),i(d,c.symbols)||(c.symbols=d,void 0!==r&&(c.lastChangeTimestamp=r))},replace:(t,e)=>{const{id:s,current:o,next:i,actionTimestamp:n}=e.payload,{lists:a}=t,r=a.byId[s];if(void 0===r)return;if(r.lastChangeTimestamp>=n)return;if(o===i)return;let l=!1,c=!1;const u=[];for(const t of r.symbols){if(t===i){c=!0;break}t===o?(u.push(i),l=!0):u.push(t)}l&&!c&&(r.symbols=u,void 0!==n&&(r.lastChangeTimestamp=n))},exact:(t,e)=>{const{id:s,symbols:o,actionTimestamp:n}=e.payload,{lists:a}=t,r=a.byId[s];if(void 0===r)return;if(r.lastChangeTimestamp>=n)return;const l=[...new Set(o)];i(l,r.symbols)||(r.symbols=l,void 0!==n&&(r.lastChangeTimestamp=n))},exclude:(t,e)=>{const{id:s,symbols:o,actionTimestamp:n}=e.payload,{lists:a}=t,r=a.byId[s];if(void 0===r)return;if(r.lastChangeTimestamp>=n)return
;const l=new Set(o),c=r.symbols.filter((t=>!l.has(t)));i(c,r.symbols)||(r.symbols=c,void 0!==n&&(r.lastChangeTimestamp=n))},remove:(t,e)=>{const{id:s}=e.payload,{lists:o}=t;if(void 0===o.byId[s])return;const i=o.ids.filter((t=>t!==s));o.ids=i,delete o.byId[s]},create:(t,e)=>{const{id:s,name:o,symbols:i,persistedState:n,actionTimestamp:a}=e.payload,{lists:l}=t;void 0===l.byId[s]&&r(t,{id:s,name:o,symbols:i,shared:!1,description:null,timestamp:null,persistedState:n,lastChangeTimestamp:a})},put:(t,e)=>{const{id:s,name:o,description:i,symbols:n,shared:a,timestamp:l,collapsedSeparators:c,persistedState:u,actionTimestamp:m}=e.payload,{lists:d}=t,p=d.byId[s];if(void 0===p)r(t,{id:s,name:o,description:i,symbols:n,shared:a,timestamp:l??null,persistedState:u,lastChangeTimestamp:m});else{if(p.lastChangeTimestamp>=m)return;d.byId[s]={id:s,name:o,description:i,shared:a,timestamp:l??null,symbols:[...n],collapsedSeparators:c,persistedState:u,lastChangeTimestamp:m??p.lastChangeTimestamp}}},rename:(t,e)=>{const{id:s,name:o,actionTimestamp:i}=e.payload,{lists:n}=t,a=n.byId[s];void 0!==a&&(a.lastChangeTimestamp>=i||a.name!==o&&(a.name=o,void 0!==i&&(a.lastChangeTimestamp=i)))},changeDescription:(t,e)=>{const{id:s,description:o,actionTimestamp:i}=e.payload,{lists:n}=t,a=n.byId[s];void 0!==a&&(a.lastChangeTimestamp>=i||a.description!==o&&(a.description=o,void 0!==i&&(a.lastChangeTimestamp=i)))},share:(t,e)=>{const{id:s,shared:o,actionTimestamp:i}=e.payload,{lists:n}=t,a=n.byId[s];void 0!==a&&(a.lastChangeTimestamp>=i||a.shared!==o&&(a.shared=o,void 0!==i&&(a.lastChangeTimestamp=i)))},updatePersistedState:(t,e)=>{const{lists:s}=t;s.ids.forEach((t=>{const o=s.byId[t],i=e.payload[t];void 0!==o&&o.persistedState&&i&&(o.persistedState={...o.persistedState,...i})}))}}}),{setup:c,insert:u,replace:m,exact:d,exclude:p,remove:S,create:y,put:h,rename:b,changeDescription:T,share:f,updatePersistedState:g}=l.actions,{reducer:_}=l;function I(t,e,s=l){return function(t){const{id:e,event:s,channel:o,filter:i}=t;return t=>(o.on(s,(s=>{const{action:o,emitter:n}=JSON.parse(s);n!==e&&i(o)&&t.dispatch({...o,payload:{...o.payload,silent:!0}})})),t=>n=>{const a=t(n);return i(n)&&(n.payload.silent||o.emit(s,JSON.stringify({action:n,emitter:e}))),a})}({id:t,channel:e,event:"custom-list-cross-tab-sync",filter:t=>function(t,e){return e.actions.setup.match(t)||e.actions.insert.match(t)||e.actions.exclude.match(t)||e.actions.exact.match(t)||e.actions.replace.match(t)||e.actions.remove.match(t)||e.actions.create.match(t)||e.actions.put.match(t)||e.actions.rename.match(t)||e.actions.changeDescription.match(t)||e.actions.share.match(t)||e.actions.updatePersistedState.match(t)}(t,s)})}function L(t){return t.lists.byId}function E(t,e){return L(t)[e]??null}function v(t){return t.lists.ids}},173850:(t,e,s)=>{s.d(e,{SEPARATOR_PREFIX:()=>o,isSeparatorItem:()=>i});const o="###";function i(t){return t.startsWith(o)}},37914:(t,e,s)=>{s.d(e,{quoteSessionAdapters:()=>r});var o=s(650151),i=s(334529),n=s(398171);class a{constructor(t,e,s="watchlist"){
this._symbolDataHandlers=new Map,this._fastSymbols=new Set,this._subscribedSymbols=new Set,this._subscriptionSet=new Set,this._cancelSubscriptionSet=new Set,this._quoteSessionDataHandler=t=>{const e=(0,o.ensureDefined)(t.symbolname),{filtered:s,keepSubscription:i}=this._applyDataFilters(t);i||this._unsubscribeSymbols([e]),this._setSymbolDataCache(e,s);const n=this._symbolDataHandlers.get(e);n&&n(s)},this._clientId=t,this._quoteSession=(0,i.getQuoteSessionInstance)(s),this._lastSymbolData=e||new Map}destroy(){const t=Array.from(this._subscribedSymbols);this._unsubscribeSymbols(t)}addFastSymbol(t){this._fastSymbols.has(t)||!this._subscribedSymbols.has(t)||(0,n.isSeparatorItem)(t)||(this._fastSymbols.add(t),this._quoteSession.setFastSymbols(this._clientId,Array.from(this._fastSymbols)))}removeFastSymbol(t){this._fastSymbols.has(t)&&(this._fastSymbols.delete(t),this._quoteSession.setFastSymbols(this._clientId,Array.from(this._fastSymbols)))}addSymbolDataHandler(t,e){(0,n.isSeparatorItem)(t)||this._symbolDataHandlers.set(t,e)}removeSymbolDataHandler(t){this._symbolDataHandlers.delete(t)}addToSubscriptionSet(t){t.forEach((t=>{(0,n.isSeparatorItem)(t)||this._subscriptionSet.add(t)}))}clearSubscriptionSet(){this._subscriptionSet.clear()}addToCancelSubscriptionSet(t){t.forEach((t=>{(0,n.isSeparatorItem)(t)||this._cancelSubscriptionSet.add(t)}))}commitSubscriptionChanges(){Array.from(this._subscriptionSet).forEach((t=>{this._cancelSubscriptionSet.has(t)&&(this._subscriptionSet.delete(t),this._cancelSubscriptionSet.delete(t))})),this._subscribeSymbols(Array.from(this._subscriptionSet)),this._subscriptionSet.clear(),this._unsubscribeSymbols(Array.from(this._cancelSubscriptionSet)),this._cancelSubscriptionSet.clear(),this._quoteSession.setFastSymbols(this._clientId,Array.from(this._fastSymbols))}getLastSymbolData(t){return this._lastSymbolData.get(t)}_subscribeSymbols(t){this._quoteSession.subscribe(this._clientId,t,this._quoteSessionDataHandler),t.forEach((t=>this._subscribedSymbols.add(t)))}_unsubscribeSymbols(t){this._quoteSession.unsubscribe(this._clientId,t,this._quoteSessionDataHandler),t.forEach((t=>{this._subscribedSymbols.delete(t)}))}_setSymbolDataCache(t,e){const s=this._lastSymbolData.get(t)?.values||{};this._lastSymbolData.set(t,{...e,values:{...s,...e.values}})}_applyDataFilters(t){return{filtered:t,keepSubscription:!0}}}const r=new class{constructor(){this._adaptersMap=new Map,this._lastSymbolData=new Map}destroy(){this._adaptersMap.forEach((t=>{t.forEach((t=>t.destroy()))})),this._lastSymbolData.clear()}get(t,e="watchlist"){let s;const o=this._adaptersMap.get(t);if(o){const i=o.get(e);i?s=i:(s=new a(t,this._lastSymbolData,e),o.set(e,s))}else{s=new a(t,this._lastSymbolData,e);const o=new Map;o.set(e,s),this._adaptersMap.set(t,o)}return s}}},219769:(t,e,s)=>{s.r(e),s.d(e,{addOrUpdateSymbolList:()=>u,initWidget:()=>a,removeSymbolList:()=>m,selectAllSymbols:()=>c,selectHotlist:()=>p,selectNextAvailableSymbol:()=>i,setSymbolLists:()=>d,showContextMenu:()=>n,updateActiveList:()=>h,updateBulkPositions:()=>y,
updatePosition:()=>S,updateWidget:()=>r,updateWidgetOptions:()=>l});var o=s(70644);const i=(t,e,s,i)=>({type:o.SELECT_NEXT_AVAILABLE_SYMBOL,widgetId:t,currentSymbol:e,keyboardAction:s,cancelSetOnChart:i}),n=(t,e,s,i)=>({type:o.SHOW_CONTEXT_MENU,symbol:e,position:b(s),widgetId:t,size:i}),a=(t,e,s,i)=>({type:o.INIT_WIDGET,options:i,id:t,columns:s,tickerType:e}),r=(t,e)=>({type:o.UPDATE_WIDGET,widgetId:t,widget:e}),l=(t,e)=>({type:o.UPDATE_WIDGET_OPTIONS,widgetId:t,options:e}),c=t=>({type:o.SELECT_ALL_SYMBOLS,widgetId:t}),u=t=>({type:o.ADD_OR_UPDATE_SYMBOL_LIST,symbolList:t}),m=t=>({type:o.REMOVE_SYMBOL_LIST,id:t}),d=t=>({type:o.SET_SYMBOL_LISTS,symbolLists:t}),p=(t,e,s,i=!1,n=!1)=>({type:o.SELECT_HOTLIST,widgetId:t,exchange:e,group:s,skipUpdateWidget:i,skipAddOrUpdateList:n}),S=(t,e)=>({type:o.UPDATE_POSITIONS,symbol:t,position:e}),y=t=>({type:o.UPDATE_BULK_POSITIONS,map:t}),h=t=>({type:o.UPDATE_ACTIVE_LIST,token:t});function b(t){const e=t.touches?.map((t=>({clientX:t.clientX,clientY:t.clientY})));return{clientX:t.clientX,clientY:t.clientY,touches:e,attachToXBy:t.attachToXBy,attachToYBy:t.attachToYBy,box:t.box}}},356752:(t,e,s)=>{s.d(e,{separatorIsCollapsedByListIdSelector:()=>m,separatorIsCollapsedSelector:()=>u});var o=s(377145),i=s(650151),n=s(254931),a=s(706474);const r=(t,e,s)=>s;function l(t){return t.collapsedSeparators}const c=(0,o.createSelector)([l,n.getCurrentViewableListByWidgetId],((t,e)=>e&&"hot"!==e.type?{...t[e.id]}:{})),u=(0,o.createSelector)([c,r],((t,e)=>S(t,e))),m=(0,o.createSelector)([l,(t,e)=>e,r],((t,e,s)=>S({...t[e]},s)));(0,o.createSelector)([l,n.getGlobalActiveSymbolList],((t,e)=>e?d(e,{...t[e.id]}):[])),(0,o.createSelector)([n.getCurrentViewableListByWidgetId,c],d),(0,o.createSelector)([n.getCurrentViewableListByWidgetId,c,r],((t,e,s)=>{if(!t||"hot"===t.type)return[];const{symbols:o}=t,n=function(t){const e=new Map;let s="";return t.forEach((t=>{(0,a.isValidSeparatorItem)(t)?(s=t,e.set(s,[])):s&&(0,i.ensureDefined)(e.get(s)).push(t)})),e}(o),r=[];return s.forEach((t=>{r.push(t);const s=n.get(t),o=S(e,t);s&&o&&r.push(...s)})),r}));function d(t,e){if(!t)return[];const{symbols:s}=t,o=[];let i=!0;return s.forEach((t=>{const s=(0,a.isValidSeparatorItem)(t);(i||s)&&o.push(t),s&&(i=!S(e,t))})),o}const p=!1;function S(t,e){return t.hasOwnProperty(e)?t[e]:p}},70644:(t,e,s)=>{s.d(e,{ADD_OR_UPDATE_SYMBOL_LIST:()=>d,INIT_WIDGET:()=>r,REMOVE_SYMBOL_LIST:()=>S,SELECT_ALL_SYMBOLS:()=>m,SELECT_HOTLIST:()=>y,SELECT_NEXT_AVAILABLE_SYMBOL:()=>i,SET_SYMBOL_LISTS:()=>p,SHOW_CONTEXT_MENU:()=>l,UPDATE_ACTIVE_LIST:()=>h,UPDATE_BULK_POSITIONS:()=>a,UPDATE_POSITIONS:()=>n,UPDATE_WIDGET:()=>c,UPDATE_WIDGET_OPTIONS:()=>u});const o=(0,s(215078).createActionTypeFactory)("WATCHLISTS"),i=o("SELECT_NEXT_AVAILABLE_SYMBOL"),n=o("UPDATE_POSITIONS"),a=o("UPDATE_BULK_POSITIONS"),r=o("INIT_WIDGET"),l=o("SHOW_CONTEXT_MENU"),c=o("UPDATE_WIDGET"),u=o("UPDATE_WIDGET_OPTIONS"),m=o("SELECT_ALL_SYMBOLS"),d=o("ADD_OR_UPDATE_SYMBOL_LIST"),p=o("SET_SYMBOL_LISTS"),S=o("REMOVE_SYMBOL_LIST"),y=o("SELECT_HOTLIST"),h=o("UPDATE_ACTIVE_LIST")},
50702:(t,e,s)=>{s.d(e,{crosstabMonotonicTimestamp:()=>r});var o=s(671945),i=s(131595);const n=(0,o.getLogger)("CrosstabMonotonicClock");let a=!1;function r(){const t=Number(i.TVLocalStorage.getItem("last-crosstab-monotonic-timestamp"))||0,e=Date.now(),s=t<e?e:t+1;return!a&&t-e>864e5&&(n.logError(`The last recorded timestamp ${t} occurred more than one day in the future.`),a=!0),i.TVLocalStorage.setItem("last-crosstab-monotonic-timestamp",String(s)),s}},548793:(t,e,s)=>{function o(t){return{type:"custom",id:t.id,name:t.name,description:t.description,symbols:t.symbols,shared:t.shared,persistedState:t.persistedState}}function i(t){return"custom"===t.type}s.d(e,{isCustomList:()=>i,makeCustomListFromRecord:()=>o})},363940:(t,e,s)=>{s.r(e),s.d(e,{addWatchlistSymbolsThunk:()=>w,createWatchlistThunk:()=>E,getActiveWatchlistThunk:()=>P,getCustomWatchlistsThunk:()=>v,putCustomWatchlistsThunk:()=>C,removeWatchlistSymbolsThunk:()=>O,removeWatchlistThunk:()=>W,renameSeparatorThunk:()=>R,renameWatchlistThunk:()=>A,replaceWatchlistSymbolsThunk:()=>D,setActiveWatchlistThunk:()=>k,symbolListRepositorySaga:()=>_,validateWatchListSymbols:()=>I});var o=s(129885),i=s(429498),n=s(609838),a=s(33290),r=s(131148),l=s(219769),c=s(671945),u=s(153055),m=s(61121),d=s(254931),p=s(979359),S=s(697169);function*y(){for(yield(0,o.put)((0,S.asAction)(P(null)));;){yield(0,o.take)([i.remove,i.setup]);const t=yield(0,o.select)();null===(0,d.getCurrentViewableListByWidgetId)(t,p.WATCHLIST_WIDGET_ID)&&(yield(0,o.put)((0,S.asAction)(P(null))))}}var h=s(440891),b=s(548793),T=s(50702),f=s(356752);const g=(0,c.getLogger)("Platform.Model.SymbolLists");function*_(){h.enabled("widget")||(yield(0,o.fork)(y))}function I(t){return new Promise((e=>{resolveSymbolNameForAllDistinct(t).then((t=>{e(t)}))}))}function L(t,e,o){(0,u.showWarning)({title:e||n.t(null,void 0,s(191056)),text:t},o)}function E(t){const e=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(s,o)=>{try{const o=(0,T.crosstabMonotonicTimestamp)(),n=await e.createWatchList(t);return null===n?null:(s((0,i.create)({id:n.id,name:n.name,symbols:n.symbols,persistedState:n.persistedState,actionTimestamp:o})),n.id)}catch(t){return L(t.message),null}}}const v=(0,m.respectLatest)((function(t){const e=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(s,o)=>{try{const o=(0,T.crosstabMonotonicTimestamp)(),n=await(0,m.respectAbort)(t,e.getCustomWatchLists()),a=Date.now();s((0,i.setup)({lists:n.map((t=>({id:t.id,name:t.name,description:t.description,symbols:t.symbols,shared:t.shared,collapsedSeparators:t.collapsedSeparators,timestamp:a,persistedState:t.persistedState,lastChangeTimestamp:o}))),timestamp:a}))}catch(t){(0,m.skipAbortError)(t)}}}));function C(t,e){const s=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(o,n)=>{try{const n=(0,T.crosstabMonotonicTimestamp)(),a=await(0,m.respectAbort)(t,s.putWatchList(e));o((0,i.put)({id:a.id,name:a.name,description:a.description,symbols:a.symbols,shared:a.shared,collapsedSeparators:a.collapsedSeparators,
persistedState:a.persistedState,actionTimestamp:n}))}catch(t){(0,m.rethrowAbortError)(t),L(t.message)}}}function A(t,e,s){const o=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(n,a)=>{n((0,i.rename)({id:e.id,name:s,actionTimestamp:(0,T.crosstabMonotonicTimestamp)()}));const r=await(0,m.respectAbort)(t,o.renameWatchList(e,s));r&&r.persistedState&&n((0,i.updatePersistedState)({[e.id]:r.persistedState}))}}function D(t,e,s,o){const n=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(o,a)=>{if("custom"!==e.type)return void g.logWarn("Trying to modify not a custom watchlist");o((0,i.exact)({id:e.id,symbols:s,actionTimestamp:(0,T.crosstabMonotonicTimestamp)()}));const r=await(0,m.respectAbort)(t,n.replaceSymbols(e,s));r&&o((0,i.updatePersistedState)({[e.id]:{symbols:r}}))}}function w(t,e,s,o){const n=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(a,r)=>{if("custom"===e.type){0,a((0,i.insert)({id:e.id,symbols:s,actionTimestamp:(0,T.crosstabMonotonicTimestamp)()}));try{const o=await(0,m.respectAbort)(t,n.addSymbols(e,s));a((0,i.updatePersistedState)({[e.id]:{symbols:o}}))}catch(t){if((0,m.isAbortError)(t))throw t;const n=e.symbols.filter((t=>!s.includes(t)));a((0,i.exact)({id:e.id,symbols:n,actionTimestamp:(0,T.crosstabMonotonicTimestamp)()})),L(t.message,void 0,o)}}else g.logWarn("Trying to modify not a custom watchlist")}}function O(t,e,s){const o=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(n,a)=>{n((0,i.exclude)({id:e.id,symbols:s,actionTimestamp:(0,T.crosstabMonotonicTimestamp)()}));const r=await(0,m.respectAbort)(t,o.removeSymbols(e,s));n((0,i.updatePersistedState)({[e.id]:{symbols:r}}))}}function W(t,e){const s=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(o,n)=>{await(0,m.respectAbort)(t,s.removeWatchList(e)),o((0,i.remove)({id:e.id}))}}function k(t,e){const s=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(o,n)=>{const{id:a}=e;o((0,l.updateActiveList)(e));const r=await(0,m.respectAbort)(t,s.setActive(e));r&&(0,b.isCustomList)(r)&&r.persistedState&&o((0,i.updatePersistedState)({[a]:r.persistedState}))}}function B(t,e,s){return async(o,n)=>{const a=(0,f.separatorIsCollapsedByListIdSelector)(n(),t,e);o((0,i.replace)({id:t,current:e,next:s,actionTimestamp:(0,T.crosstabMonotonicTimestamp)()})),a&&o(setSeparatorsCollapseLocal({listId:t,values:{[s]:!0,[e]:!1},actionTimestamp:(0,T.crosstabMonotonicTimestamp)()}))}}function R(t,e,s,o){const n=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(a,r)=>{"custom"===e.type?(a(B(e.id,s,o)),await(0,m.respectAbort)(t,n.renameSeparator(e.id,s,o).then((t=>{t&&a((0,i.updatePersistedState)({[e.id]:{symbols:t}}))})).catch(m.skipAbortError).catch((t=>{L(t.message),a(B(e.id,o,s))})))):g.logWarn("Trying to modify not a custom watchlist")}}const P=(0,m.respectLatest)((function(t){const e=(0,a.service)(r.SYMBOL_LIST_REPOSITORY_BACKEND_SERVICE);return async(s,o)=>{try{const o=(0,T.crosstabMonotonicTimestamp)(),n=await(0,
m.respectAbort)(t,e.getActive());"colored"===n.type||("custom"===n.type?(s((0,i.put)({id:n.id,name:n.name,description:n.description,symbols:n.symbols,shared:n.shared,collapsedSeparators:n.collapsedSeparators,persistedState:n.persistedState,actionTimestamp:o})),s((0,l.updateActiveList)({type:"custom",id:n.id}))):"hot"===n.type&&(s((0,l.addOrUpdateSymbolList)(n)),n.exchange&&n.group&&s((0,l.updateActiveList)({type:"hot",exchange:n.exchange,group:n.group,id:n.id}))))}catch(t){(0,m.skipAbortError)(t)}}}))},697169:(t,e,s)=>{function o(t){return t}s.d(e,{asAction:()=>o})},254931:(t,e,s)=>{s.d(e,{columnsSelector:()=>h,getCurrentViewableListByWidgetId:()=>v,getCustomListById:()=>L,getCustomLists:()=>g,getCustomListsMap:()=>I,getGlobalActiveID:()=>u,getGlobalActiveSymbolList:()=>E,getIsReadyCustomLists:()=>_,highlightedSymbolsSelector:()=>y,isLoadingSelector:()=>C,makeGetIsContainedByMultiSelection:()=>D,positionSelector:()=>A,scrollToIdSelector:()=>p,selectedSymbolsSelector:()=>d,shouldDisplayPositionsSelector:()=>f,sortingSelector:()=>S,tickerTypeSelector:()=>b,widgetOptionsSelector:()=>T,widgetSelector:()=>m});var o=s(377145),i=s(650151),n=s(429498),a=s(548793);function r(t){return t.customLists}const l=t=>t.widgets,c=(t,e)=>e,u=t=>t.activeSymbolList?.id??null,m=(0,o.createSelector)([l,c],((t,e)=>t[e]||{})),d=(0,o.createSelector)(m,(t=>t.selectedSymbols||[])),p=(0,o.createSelector)(m,(t=>t.scrollToId)),S=(0,o.createSelector)(m,(t=>t.sorting||null)),y=(0,o.createSelector)(m,(t=>t.highlightedSymbols||null)),h=(0,o.createSelector)(m,(t=>t.columns)),b=(0,o.createSelector)(m,(t=>t.tickerType)),T=(0,o.createSelector)(m,(t=>t.options||{})),f=(0,o.createSelector)([T,t=>t.isAuthenticated],((t,e)=>t.shouldDisplayPositions&&!0)),g=(0,o.createSelector)(r,(t=>(0,n.getListIds)(t).map((e=>(0,a.makeCustomListFromRecord)((0,i.ensureNotNull)((0,n.getListById)(t,e))))))),_=(0,o.createSelector)([r],(t=>null!==t.timestamp)),I=(0,o.createSelector)([r],n.getListsMap),L=(0,o.createSelector)([r,(t,e)=>e],((t,e)=>{const s=(0,n.getListById)(t,e);return null===s?null:(0,a.makeCustomListFromRecord)(s)})),E=((0,o.createSelector)([()=>null],(t=>t?t.lists.byColor:null)),(0,o.createSelector)([()=>null,r,()=>null,u],((t,e,s,o)=>{if(null===o)return null;const i=(0,n.getListById)(e,o);return null!==i?(0,a.makeCustomListFromRecord)(i):null}))),v=(0,o.createSelector)([()=>null,r,()=>null,l,c],((t,e,s,o,i)=>{const r=o[i];if(void 0===r)return null;const l=r.listId;if(null===l)return null;const c=(0,n.getListById)(e,l);return null!==c?(0,a.makeCustomListFromRecord)(c):null}));const C=(0,o.createSelector)([m,v],((t,e)=>null===e||t.isLoading||!1)),A=(0,o.createSelector)([t=>t.positions,(t,e)=>e],((t,e)=>t[e]));function D(){const t=(0,o.createSelector)([(t,e)=>e.widgetId,t=>t.widgets],((t,e)=>e[t])),e=(0,o.createSelector)(t,(t=>t.selectedSymbols||[])),s=(0,o.createSelector)(e,(t=>new Set(t)));return(0,o.createSelector)([s,(t,e)=>e.symbol],((t,e)=>t.size>1&&t.has(e)))}},607898:(t,e,s)=>{s.r(e),s.d(e,{addSymbolsThunk:()=>E,addSymbolsToCustomListThunk:()=>W,
addSymbolsToListThunk:()=>V,changeDescriptionThunk:()=>N,clearSymbolListThunk:()=>D,findInWatchlistThunk:()=>w,insertSymbolBeforeThunk:()=>B,markSymbolsThunk:()=>I,moveSymbolsToCustomListThunk:()=>k,openSectionBySymbolThunk:()=>x,removeSelectedSymbolsThunk:()=>g,removeSymbolListThunk:()=>Y,removeSymbolsThunk:()=>L,renameSymbolListThunk:()=>A,reorderSymbolsThunk:()=>T,saveListAsThunk:()=>M,selectSymbolListThunk:()=>R,sortSymbolsThunk:()=>f,updateScrollToIdThunk:()=>O,userCreateWatchlistThunk:()=>P});var o=s(650151),i=s(429498),n=s(609838),a=(s(32133),s(219769)),r=s(254931),l=s(398171),c=s(706474),u=s(979359),m=s(363940),d=s(61121),p=s(626800),S=s(153055),y=s(354364),h=(s(440891),s(50702));const b=10;function T(t,e){return(s,o)=>{const i=o(),{isMovable:n}=(0,r.widgetOptionsSelector)(i,t);if(!n)return;const a=(0,r.getCurrentViewableListByWidgetId)(i,t);if(null===a)return;if("hot"===a.type)return;const l=e;if("custom"===a.type){const t={...a,symbols:l};s((0,m.replaceWatchlistSymbolsThunk)(null,t,l))}else 0}}function f(t,e,s){return(n,l)=>{const u=l(),d=(0,r.getCurrentViewableListByWidgetId)(u,t);if(null===d)return;if("hot"===d.type)return;const{symbolsBeforeSorting:p}=(0,r.widgetSelector)(u,t),S=(0,r.sortingSelector)(u,t),y=d.symbols;let b;if(null===e)b=(0,o.ensureNotNull)(p);else{const{column:o,direction:i}=e;b=(0,c.sortSymbols)(t,y,o,i,s)}"custom"===d.type&&(n((0,i.exact)({id:d.id,symbols:b,actionTimestamp:(0,h.crosstabMonotonicTimestamp)()})),n((0,m.replaceWatchlistSymbolsThunk)(null,d,b))),n((0,a.updateWidget)(t,{symbolsBeforeSorting:null===S?[...y]:p,sortingListId:d.id,sorting:e}))}}function g(t){return(e,s)=>{const o=s(),i=(0,r.getCurrentViewableListByWidgetId)(o,t);if(null===i)return;if("hot"===i.type)return;const{isDeletable:n}=(0,r.widgetOptionsSelector)(o,t);if(!n)return;const l=(0,r.selectedSymbolsSelector)(o,t);if(0===l.length)return;const u=l[l.length-1],m=i.symbols[i.symbols.length-1],d=i.symbols.some((t=>t===u)),p=()=>{e(d&&u!==m?(0,a.selectNextAvailableSymbol)(t,u,"next",!0):(0,a.updateWidget)(t,{selectedSymbols:[]})),"custom"===i.type&&e(_(l,i)),(0,c.trackRemoveEvent)(l.length>1)};if(1===l.length)return void p();const y=document.activeElement;(0,S.showConfirm)({text:(0,c.createConfirmRemoveText)(l.length),onConfirm:({dialogClose:t})=>{p(),t()},onClose:()=>{y?.focus()}})}}function _(t,e){return(s,o)=>{if("custom"!==e.type);else{const o={...e,symbols:e.symbols.filter((e=>!t.includes(e)))};s((0,m.removeWatchlistSymbolsThunk)(null,o,t))}}}function I(t,e,s,o){return(t,e)=>{}}function L(t,e,s,o){return(i,n)=>{const a=n(),l=s?(0,r.getCustomListById)(a,s):(0,r.getCurrentViewableListByWidgetId)(a,t);if(null===l)return;if("hot"===l.type)return;const u=new Set((0,r.selectedSymbolsSelector)(a,t)),d=1===e.length&&u.has(e[0])&&!o?Array.from(u):e,p=()=>{"custom"===l.type&&i((0,m.removeWatchlistSymbolsThunk)(null,l,d)),(0,c.trackRemoveEvent)(d.length>1),i((l.id,(t,e)=>{e()}))};if(1===d.length||Boolean(s))return void p();const y=document.activeElement;(0,S.showConfirm)({text:(0,c.createConfirmRemoveText)(d.length),
onConfirm:({dialogClose:t})=>{p(),t()},onClose:()=>{y?.focus()}})}}function E(t,e,s){return(o,i)=>{const n=i(),a=(0,r.getCurrentViewableListByWidgetId)(n,t);if(null===a)return;if("hot"===a.type)return;Promise.resolve([...new Set(e)]).then((e=>{let i;"custom"===a.type&&o(C(e,a,s)),i||(i=e[e.length-1]??null),o(v(t,e,i))}))}}function v(t,e,s,o){return(i,n)=>{e=e.slice(0,b),(0,y.expandWatchlist)(),(t!==u.WATCHLIST_WIDGET_ID||n().widgets[u.WATCHLIST_WIDGET_ID])&&(s&&o&&i(x(s,t)),i((0,a.updateWidget)(t,{highlightedSymbols:[...e],scrollToId:s?{id:s}:null})),setTimeout((()=>i((0,a.updateWidget)(t,{highlightedSymbols:null}))),500))}}function C(t,e,s,o){return(i,n)=>{const a=new Set(e.symbols),r=t.filter((t=>!a.has(t)&&!a.has((0,p.safeShortName)(t))));if(r.length){const t=[...e.symbols],n=s?e.symbols.indexOf(s):-1;-1===n?t.push(...r):t.splice(n+1,0,...r);const a={...e,symbols:t};i(-1===n?(0,m.addWatchlistSymbolsThunk)(null,a,r,o):(0,m.replaceWatchlistSymbolsThunk)(null,a,t,o))}}}function A(t,e){return(s,i)=>{const n=i();{const i=(0,o.ensureNotNull)((0,r.getCustomListById)(n,t));s((0,m.renameWatchlistThunk)(null,i,e))}}}function D(t){return(e,s)=>{const o=s(),i=(0,r.getCustomListById)(o,t);if(null!==i&&0!==i.symbols.length){if("colored"===i.type){e(remove({color:i.color,symbols:i.symbols}))}else e((0,m.replaceWatchlistSymbolsThunk)(null,{...i,symbols:[]},[]));e((i.symbols,(t,e)=>{e()}))}}}function w(t,e){return v(t,[e],e,!0)}function O(t,e){return(s,o)=>{const i=o(),n=(0,r.getCurrentViewableListByWidgetId)(i,t);if(!n)return;const{symbols:l}=n;l.includes(e)&&s((0,a.updateWidget)(t,{scrollToId:{id:e}}))}}function W(t,e,s,o,i=!0){return(n,a)=>{const l=a(),c=(0,r.getCustomListById)(l,e);if(c&&(n(C(s,c,void 0,o)),(0,r.getGlobalActiveID)(l)===e&&i)){const e=s[s.length-1]??null;n(v(t,s,e))}}}function k(t,e,s){return async(o,i)=>{const n=i(),a=(0,r.getCustomListById)(n,e),c=s.filter((t=>!(0,l.isSeparatorItem)(t)));a&&o(C(c,a));const u=(0,r.getCustomListById)(n,t);u&&o((0,m.removeWatchlistSymbolsThunk)(null,u,s))}}function B(t,e,s){return(o,i)=>{const n=i(),a=(0,r.getCurrentViewableListByWidgetId)(n,t);if(null===a)return;if("hot"===a.type)return;const{symbols:l}=a;if(!l.includes(s))if("custom"===a.type){const t=l.indexOf(e),i=[...l.slice(0,t),s,...l.slice(t)],n={...a,symbols:i};o((0,m.replaceWatchlistSymbolsThunk)(null,n,i))}else 0}}const R=(0,d.respectLatest)((function(t,e){return async(t,s)=>{try{const i=e;let n;const l=s().widgets[u.WATCHLIST_WIDGET_ID];if(n=(0,o.ensureNotNull)((0,r.getCustomListById)(s(),i.id)),t((0,m.setActiveWatchlistThunk)(null,i)),!l)return void(0,y.expandWatchlist)();t((0,a.updateWidget)(u.WATCHLIST_WIDGET_ID,{isLoading:!1,listId:i.id,sorting:null,symbolsBeforeSorting:null,sortingListId:null,selectedSymbols:[],scrollToId:null})),(0,y.expandWatchlist)()}catch(t){(0,d.skipAbortError)(t)}}}));const P=(0,d.respectLatest)((function(t,e,i,a,l){return async(u,p)=>{try{if(!(0,c.canUseMultipleLists)())return;if(!e||void 0===e.name){const o=await(0,d.respectAbort)(t,(0,
c.createSaveRenameDialog)(n.t(null,void 0,s(222556)),void 0,a,l)());e={name:o,symbols:e?.symbols||[]}}0;const S=(0,m.createWatchlistThunk)(e),y=await(0,d.respectAbort)(t,u(S));if(null===y)return;if(u(R(null,{id:y,type:"custom"})),i){const t=(0,r.getCustomListById)(p(),i);if(!t)return;await u(_((0,o.ensureDefined)(e.symbols),(0,o.ensureDefined)(t)))}}catch(t){(0,d.skipAbortError)(t)}}}));function M(t,e,i=!0,a,l,d){return async(t,p)=>{if(!(0,c.canUseMultipleLists)())return;const S=await(0,c.createSaveRenameDialog)(n.t(null,void 0,s(977415)),a,l,d)();let y=await e;if(!y){y=(0,o.ensureNotNull)((0,r.getCurrentViewableListByWidgetId)(p(),u.WATCHLIST_WIDGET_ID)).symbols}const h=(0,m.createWatchlistThunk)({name:S,symbols:y}),b=await t(h);null!==b&&i&&t(R(null,{id:b,type:"custom"}))}}function N(t,e){return async(t,e)=>{}}function x(t,e){return async(t,e)=>{}}function Y(t){return async(e,s)=>{const i=s();{const s=(0,o.ensureNotNull)((0,r.getCustomListById)(i,t));await e((0,m.removeWatchlistThunk)(null,s))}}}function V(t,e,s){return o=>{o(W(t,e,s))}}},706474:(t,e,s)=>{s.d(e,{buildUniqueName:()=>b,canUseMultipleLists:()=>T,compareSymbols:()=>v,convertToSeparatorName:()=>h,createConfirmRemoveText:()=>W,createConfirmUnflagText:()=>k,createSaveRenameDialog:()=>f,createWatchList:()=>E,findNextAvailableSymbol:()=>w,getSymbolFromList:()=>D,isEqualRecords:()=>A,isValidSeparatorItem:()=>S,separatorValToDisplayVal:()=>y,sortSymbols:()=>_,toPositionRecord:()=>C,trackRemoveEvent:()=>O});var o=s(650151),i=s(609838),n=s(440891),a=s(153055),r=s(662654),l=s(37914),c=s(574266),u=s(398171),m=s(173850),d=s(32133),p=s(626800);function S(t){return(0,u.isSeparatorItem)(t)}function y(t){return t.replace(m.SEPARATOR_PREFIX,"")}function h(t){return m.SEPARATOR_PREFIX+t}function b(t,e,s=!0){const o=new Set(t),i=s?" 1":"";if(!o.has(e+i))return e+i;let n=2,a="";for(;n<=2e3&&(a=`${e} ${n}`,o.has(a));)n+=1;return a}function T(){return n.enabled("multiple_watchlists")&&!0}function f(t,e,o,n){return()=>new Promise(((r,l)=>{(0,a.showRename)({title:t,initValue:e,text:i.t(null,void 0,s(46187))+":",maxLength:128,emojiPicker:!0,onRename:({newValue:t,dialogClose:e})=>{r(t),e(),n?.()}},o)}))}function g(t){return t.replace("−","-")}function _(t,e,s,i,n){const a=function(t){const e=[],s=[];t.forEach(((t,e)=>{(0,u.isSeparatorItem)(t)&&s.push(e)}));let o=0;s.forEach((s=>{e.push({values:t.slice(o,s),separator:t[s]}),o=s+1}));const i=s[s.length-1],n=t.slice(i+1);return n.length>0&&e.push({values:n,separator:null}),e}(e);return a.forEach((e=>{e.values.sort(((e,a)=>{const u=l.quoteSessionAdapters.get(t).getLastSymbolData(e),m=l.quoteSessionAdapters.get(t).getLastSymbolData(a),d=I(u),S=I(m),y=(t,e)=>{if(!t||"flag"===s)return;var o;return"error"===t.status?void 0:n===r.TickerType.Description&&"short_name"===s?(o=t.values,(0,c.getTranslatedSymbolDescription)(o)||(0,p.safeShortName)(e)):t.values[s]},h=y(u,e),b=y(m,a);return function(t,e){const s=-1===e?-1/0:1/0;return(i,n,a,r)=>{if("rchp"!==t){if(void 0===i&&void 0===n)return 0;if(void 0===i)return-e;if(void 0===n)return e}
switch(t){case"short_name":return e*(i===n?0:(0,o.ensure)(i)<(0,o.ensure)(n)?-1:1);case"rchp":return a!==r?a>r?-1:1:(a||(i=void 0),r||(n=void 0),L(i,n,e,s));case"last_price":case"change":case"change_percent":return L(i,n,e,s);case"volume":{const t=(0,o.ensure)(i)>=1e100?s:Number(i),a=(0,o.ensure)(n)>=1e100?s:Number(n);return e*(t-a)}default:return(0,o.ensure)(i)>(0,o.ensure)(n)?-1:1}}}(s,i)(h,b,d,S)}))})),function(t){const e=[];return t.forEach((({values:t,separator:s})=>{e.push(...t),null!==s&&e.push(s)})),e}(a)}function I(t){if(!t)return!1;const e="pre_market"===t.values.current_session,s="post_market"===t.values.current_session;return e||s}function L(t,e,s,o){let i=parseFloat(g(String(t))),n=parseFloat(g(String(e)));return i=isNaN(i)?o:i,n=isNaN(n)?o:n,s*(i-n)}function E(t,e,s){return{type:"custom",id:t,name:e,description:null,symbols:s,shared:!1,persistedState:null}}function v(t,e){return t===e||(0,p.safeShortName)(t)===e}function C(t){return{symbol:t.symbol,side:t.side,qty:t.qty,avgPrice:t.avgPrice}}function A(t,e){return t===e||void 0!==t&&void 0!==e&&(t.side===e.side&&t.qty===e.qty&&t.avgPrice===e.avgPrice&&t.symbol===e.symbol)}function D(t,e){if(e.includes(t))return t;{const s=(0,p.safeShortName)(t);if(e.includes(s))return s}}function w(t,e,s){const o="next"===s;for(let s=t;o?s<e.length:s>=0;o?s++:s--)if(!(0,u.isSeparatorItem)(e[s])&&s!==t)return e[s];for(let s=o?0:e.length-1;o?s<t:s>t;o?s++:s--)if(!(0,u.isSeparatorItem)(e[s]))return e[s]}function O(t){t?(0,d.trackEvent)("Watchlist","Multi select","Remove"):(0,d.trackEvent)("Watchlist","Remove")}function W(t){return i.t(null,{plural:"Do you really want to delete {count} selected symbols?",context:"symbols_and_drawings_count",count:t,replace:{count:t.toString()}},s(54105))}function k(t){return i.t(null,{plural:"Do you really want to unflag {count} selected symbol?",context:"symbols_and_drawings_count",count:t,replace:{count:t.toString()}},s(464788))}},398171:(t,e,s)=>{s.d(e,{isSeparatorItem:()=>i});var o=s(173850);function i(t){return(0,o.isSeparatorItem)(t)}}}]);