"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[99198],{978956:(e,t,i)=>{i.d(t,{Delegate:()=>s});const r=(0,i(735566).getLogger)("Common.Delegate");function o(e){return!e.singleShot}class s{constructor(){this.fire=this._fireImpl.bind(this),this._listeners=[]}subscribe(e,t,i){this._listeners.push({object:e,member:t,singleShot:!!i,skip:!1})}unsubscribe(e,t){for(let i=0;i<this._listeners.length;++i){const r=this._listeners[i];if(r.object===e&&r.member===t){r.skip=!0,this._listeners.splice(i,1);break}}}unsubscribeAll(e){for(let t=this._listeners.length-1;t>=0;--t){const i=this._listeners[t];i.object===e&&(i.skip=!0,this._listeners.splice(t,1))}}destroy(){this._listeners=[]}_fireImpl(...e){const t=this._listeners;this._listeners=this._listeners.filter(o);const i=t.length;for(let o=0;o<i;++o){const i=t[o];if(!i.skip)try{i.member.apply(i.object||null,e)}catch(e){r.logError(`${e&&(e.stack||e.message)}`)}}}}},954992:(e,t,i)=>{function r(e,t,i){if(t(e.value()))return void i();const r=o=>{t(o)&&(e.unsubscribe(r),i())};e.subscribe(r,{callWithLast:!0})}i.d(t,{callWhen:()=>r})},659863:(e,t,i)=>{new class{constructor(e,t){this._test=e[t]={}}provide(e,t){this._test[e]=t}}(window,"qaGlobals")},175203:(e,t,i)=>{i.d(t,{telemetry:()=>c});var r=i(547465),o=i(803912),s=i(638456),a=i(314802);i(659863);const n=(0,i(735566).getLogger)("Common.Telemetry"),_={default:15e3,site:3e5},l=["before_websocket_connection_time_frame","websocket_connection_time_frame","first_series_full_time_frame","page_full_load_time_frame","page_load_time_frame"];const c=new class{constructor(){this.reportSent=new r.Delegate,this.timeCounters={series:{marks:[]},study:{},pine:{}},this._timeoutIds={},this._commonAdditionalData={cluster:null,userId:"0"},this._reportStash={}}setSessionInfo(e){const t=this._parseCluster(e);null!==t&&(this._commonAdditionalData.cluster=t)}sendReport(e,t,i){if(!this._isAbleToSendReport(t))return;const r=this._getSubserviceType(e),o=this._getHost(e,r);if(null!==o){if(i=void 0===i?{count:1}:i,this._addReportToStash(i,t,o),!this._timeoutIds[e]){const t=_[e]??_.default;this._timeoutIds[e]=setTimeout(this._sendTelemetryToService.bind(this,e,o),t)}}else n.logError(`Unable to get host for counter: ${t}, metric type: ${e}, serivce type: ${r}`)}sendChartReport(e,t,i=!0){this._sendServiceSpecifiedReport("charts",e,t,i)}sendLineToolsStorageReport(e,t,i=!0){this._sendServiceSpecifiedReport("line_tools_storage",e,t,i)}_sendServiceSpecifiedReport(e,t,i,r=!0){this._updateUserInfo(),i=void 0===i?{count:1}:i,r&&(i=this._appendCommonAdditionalInfo(i,["cluster","userId"])),this.sendReport(e,t,i)}_updateUserInfo(){const e="user"in window&&"id"in window.user?window.user.id:"0";this._commonAdditionalData.userId=String(e)}_isAbleToSendReport(e){const t=window.TELEMETRY_HOSTS,i=l.includes(e),r=Boolean(window.TradingView?.onChartPage||(0,s.onWidget)());return t&&(!i||r)}_sendTelemetryToService(e,t){if(this._reportStash.hasOwnProperty(t)){const e=this._cropParams(this._reportStash[t]),i=this._renameAllParams(e),r={
event:"report_stash",params:this._cleanAllParams(i)};n.logDebug(`Report to host: ${t}; stash: ${JSON.stringify(this._reportStash[t])}`),this.reportSent.fire(this._reportStash[t]),delete this._reportStash[t],(0,o.fetch)(`${t}/report`,{method:"POST",body:JSON.stringify(r)})}this._timeoutIds[e]=null}_getHost(e,t){const i=window.TELEMETRY_HOSTS,r=i[e]&&i[e][t];return Boolean(r)?r:null}_getSubserviceType(e){if(!["charts","site"].includes(e))return"all";let t="free";const i=window.user.is_pro;return"charts"===e&&(0,a.isOnMobileAppPage)("old")?t=i?"ios_pro":"ios_free":"charts"===e&&(0,a.isOnMobileAppPage)("new")?t=i?"android_pro":"android_free":(0,s.onWidget)()?t="widget":i&&(t="pro"),t}_parseCluster(e){let t;try{t=JSON.parse(e).session_id}catch(e){return n.logError("Could not parse cluster id (session id)"),null}const i=/(.*@)(.*)/gi.exec(t);return null!==i&&i.length>=3?i[2]:null}_appendCommonAdditionalInfo(e,t){return t.forEach((t=>{t in this._commonAdditionalData&&(e.additional=e.additional||{},e.additional[t]=this._commonAdditionalData[t])})),e}_cropParams(e){for(const t in e)e.hasOwnProperty(t)&&e[t].length>1e3&&(e.too_much_metrics_frame=e.too_much_metrics_frame??[],e.too_much_metrics_frame.push({value:e[t].length,additional:{event:t}}),delete e[t]);return e}_renameAllParams(e){const t={};for(const i in e)e.hasOwnProperty(i)&&(t[i]=[],e[i].forEach((e=>{t[i].push(this._renameEntryParams(e))})));return t}_renameEntryParams(e){const t={count:"c",value:"v",text:"t",additional:"a"};return Object.keys(e).reduce(((i,r)=>(i[t[r]]=e[r],i)),{})}_cleanAllParams(e){const t={};for(const i in e)if(e.hasOwnProperty(i)){t[i]=[];const r={c:0};e[i].forEach((e=>{const o=this._cleanEntryParams(e),s=Object.keys(o).length;1===s&&void 0!==o.c?r.c+=o.c:s>0&&t[i].push(o)})),r.c>0&&t[i].push(r),0===t[i].length&&delete t[i]}return t}_cleanEntryParams(e){const t=Object.keys(e).reduce(((t,i)=>"c"!==i&&"t"!==i||e[i]?(t[i]=e[i],t):t),{});return"c"in t||"v"in t||"t"in t?t:{}}_addReportToStash(e,t,i){i in this._reportStash||(this._reportStash[i]={}),t in this._reportStash[i]||(this._reportStash[i][t]=[]),Object.keys(e).length>0&&this._reportStash[i][t].push(e)}}},766251:(e,t,i)=>{i.d(t,{getNewPeveCloseMode:()=>o});let r=!1;function o(){return r}},235267:(e,t,i)=>{i.d(t,{QuoteSession:()=>o});var r=i(623213);i(638456);class o{constructor(e,t=(0,r.randomHash)()){this._sessionstarted=!1,this._globalHandler=null,this._chartApi=e,this._sessionid="qs_"+t}destroy(){this._sessionstarted&&(this._chartApi.quoteDeleteSession(this._sessionid),this._sessionstarted=!1)}connected(){return this._chartApi.connected()}connect(e){this._globalHandler=e,this._chartApi.createSession(this._sessionid,this),this._chartApi.connect()}disconnect(){this._chartApi.disconnect()}quoteAddSymbols(e){this._chartApi.quoteAddSymbols(this._sessionid,e)}quoteRemoveSymbols(e){this._chartApi.quoteRemoveSymbols(this._sessionid,e)}quoteFastSymbols(e){this._chartApi.quoteFastSymbols(this._sessionid,e)}quoteSetFields(e){this._chartApi.quoteSetFields(this._sessionid,e)}onMessage(e){
switch(e.method){case"connected":this._sessionstarted||(this._chartApi.quoteCreateSession(this._sessionid),this._sessionstarted=!0);break;case"disconnected":this._sessionstarted=!1}this._globalHandler?.(e)}quoteHibernateAll(){this._chartApi.quoteHibernateAll(this._sessionid)}}window.TradingView=window.TradingView||{},window.TradingView.QuoteSession=o},288533:(e,t,i)=>{e=i.nmd(e);var r=i(920057).default,o=i(290484).default,s=i(89831).PriceFormatter;const{uniq:a}=i(999102);var n=i(623213);const{normalizeUpdateMode:_}=i(778003);var l=i(144731).deepExtend;const{QUOTE_FIELDS_CACHE:c,QUOTE_FIELDS:m}=i(207815);var d,u=i(547465).Delegate,p=i(766251).getNewPeveCloseMode;function h(e,t){this.options=Object.assign({throttleTimeout:125},t),this._connected=!1,this._symbol_data={},this._subscriptions={},this.onConnect=new u,this.onDisconnect=new u,this._quoteApi=new d(window.ChartApiInstance),this._type=e||"full",this._delayUpdateFastSymbols=o(this._updateFastSymbols,250),this._throttledSymbolData={},this._formatterValuesCache={},this._waitingForFormatters={},this._snapshotValuesCache={},this._waitingForSnapshot={},this.connect()}d=i(235267).QuoteSession,h.prototype.destroy=function(){this._quoteApi.destroy(),this._quoteApi=null,this._connected=!1,this.onDisconnect.fire()},h.prototype.typeFields={},h.prototype.typeFields.simple=["base-currency-logoid","ch","chp","currency-logoid","currency_code","currency_id","base_currency_id","current_session","description","exchange","format","fractional","is_tradable","language","local_description","listed_exchange","logoid","lp","lp_time","minmov","minmove2","original_name","pricescale","pro_name","short_name","type","typespecs","update_mode","volume","variable_tick_size","value_unit_id","unit_id","measure"],h.prototype.typeFields.simpleDetailed=[].concat(h.prototype.typeFields.simple,["ask","bid","fundamentals","high_price","is_tradable","low_price","open_price","prev_close_price","rch","rchp","rtc","rtc_time","status","basic_eps_net_income","beta_1_year","earnings_per_share_basic_ttm","industry","market_cap_basic","price_earnings_ttm","sector","volume","dividends_yield","timezone"]),h.prototype.typeFields.full=[],h.prototype.typeFields.watchlist=[].concat(h.prototype.typeFields.simple,["rchp","rtc","country_code","provider_id","dividends_availability","financials_availability","earnings_availability"]),
h.prototype.typeFields.portfolio=["pro_name","short_name","exchange","listed_exchange","description","local_description","language","sector","type","typespecs","industry","currency_code","currency_id","ch","chp","logoid","currency-logoid","base-currency-logoid","earnings_per_share_forecast_next_fq","earnings_release_next_date","earnings_release_date","earnings_per_share_fq","lp","fractional","minmov","minmove2","pricescale","volume","average_volume","market_cap_calc","market_cap_basic","total_revenue","earnings_per_share_basic_ttm","price_earnings_ttm","beta_1_year","dps_common_stock_prim_issue_fy","dividends_yield","fundamental_currency_code","rates_mc","rates_fy","rates_ttm","format","value_unit_id","unit_id","measure"],h.prototype.typeFields.notes=["short_name","pro_name","logoid","currency-logoid","base-currency-logoid","symbol-primaryname","type","typespecs"],h.prototype.typeFields.estimates=["fundamental_data","type","typespecs","earnings_per_share_forecast_next_symbol_currency_fq","earnings_release_next_aligned_date","earnings_release_next_calendar_date","earnings_release_next_date"],h.prototype.typeFields.economic=["reference-last-period","lp","currency_code","value_unit_id","unit_id","measure"],h.prototype.typeFields.options=["ask","bid","lp"],h.prototype.connect=function(e){this._quoteApi.connect(this.quoteHandler.bind(this))},h.prototype.quoteHandler=function(e){var t=e.method,i=e.params;switch(t){case"connected":this._connected||(this._connected=!0,this.onConnected());break;case"quote_list_fields":break;case"quote_symbol_data":this._connected&&this.onSymbolData(i[0]);break;case"quote_completed":this._connected&&this.onSymbolData({symbolname:i[0],complete:performance.now(),values:{}});break;case"disconnected":this._connected&&(this._connected=!1,this.onDisconnect.fire())}},h.prototype.onConnected=function(){this.setFields();var e=Object.keys(this._symbol_data);e.length&&(this._quoteApi.quoteAddSymbols(e),this._delayUpdateFastSymbols()),this.onConnect.fire()},h.prototype.setFields=function(){var e=h.prototype.typeFields[this._type];e&&e.length&&this._quoteApi.quoteSetFields(e)},h.prototype.onSymbolData=function(e){try{e.status&&c.update(e,m,!1)}catch(e){}var t=e.symbolname,i=this._throttledSymbolData[t];p()&&(delete e.values.prev_close_price,void 0!==e.values.regular_close&&(e.values.prev_close_price=e.values.regular_close)),i||(i=this._throttledSymbolData[t]={fnDispatch:r(this.dipatchSymbolData.bind(this),this.options.throttleTimeout)}),i.cache?l(i.cache,e):i.cache=e,i.fnDispatch(t)},h.prototype._parseUpdateMode=function(e){_(e)},h.prototype.dipatchSymbolData=function(e){var t=this._symbol_data[e],i=this._throttledSymbolData[e].cache;if(delete this._throttledSymbolData[e].cache,this._symbol_data[e])for(var r in l(t,i),t.values&&this._parseUpdateMode(t.values),this._subscriptions){var o=this._subscriptions[r];o.has(e)&&[...o.get(e)].forEach((function(e){e(t,i)}))}},h.prototype.subscribe=function(e,t,i){this._subscriptions[e]=this._subscriptions[e]||new Map;var r=this._subscriptions[e];t=[].concat(t)
;var o=[];t.forEach((function(e){this._symbol_data[e]?r&&r.has(e)||this._symbol_data[e].subscribers_count++:(this._symbol_data[e]={subscribers_count:1},o.push(e)),r.has(e)||r.set(e,[]),r.get(e).push(i),r.get(e).fast=!0,this._symbol_data[e]&&this._symbol_data[e].values&&i(this._symbol_data[e],this._symbol_data[e])}),this),o.length&&this._connected&&(this._quoteApi.quoteAddSymbols(o),this._delayUpdateFastSymbols())},h.prototype.unsubscribe=function(e,t,i){t=[].concat(t);for(var r=this._subscriptions[e],o=[],s=0;s<t.length;s++){var a=t[s];if(r)if(r.has(a)&&i){var n=r.get(a).indexOf(i);~n&&r.get(a).splice(n,1),r.get(a).length||r.delete(a)}else r.delete(a);r&&0===r.size&&delete this._subscriptions[e],this._symbol_data.hasOwnProperty(a)&&(r&&!r.has(a)&&this._symbol_data[a].subscribers_count--,this._symbol_data[a].subscribers_count||(delete this._symbol_data[a],o.push(a)))}o.length&&this._connected&&(this._quoteApi.quoteRemoveSymbols(o),this._delayUpdateFastSymbols())},h.prototype.setFastSymbols=function(e,t){if(this._subscriptions[e])for(var i=this._subscriptions[e],r=Array.from(i.keys()),o=0;o<r.length;++o){var s=r[o];i.get(s).fast=-1!==t.indexOf(s)}this._delayUpdateFastSymbols()},h.prototype._updateFastSymbols=function(){if(this._connected){var e=this._fastSymbols();0===e.length?this._quoteApi.quoteHibernateAll():this._quoteApi.quoteFastSymbols(e)}},h.prototype._delayUpdateFastSymbols=h.prototype._updateFastSymbols,h.prototype._fastSymbols=function(){var e=[];for(var t in this._subscriptions)for(var i=this._subscriptions[t],r=Array.from(i.keys()),o=0;o<r.length;++o){var s=r[o];i.get(s).fast&&e.push(s)}return e=a(e)},h.prototype.formatter=function(e,t){var i=this;if(this._waitingForFormatters[e])return this._waitingForFormatters[e];function r(e){var i=t&&!e.fractional?1:e.minmov;return new s({priceScale:e.pricescale,minMove:i,fractional:e.fractional,minMove2:e.minmove2})}var o=new Promise((function(t,o){if(i._formatterValuesCache[e])t(r(i._formatterValuesCache[e]));else{var s=n.guid();i.subscribe(s,[e],(function(a){"error"===a.status&&(i._waitingForFormatters[e]=null,o("Quotes snapshot is not received")),function(e){return e&&null!=e.pricescale&&null!=e.minmov}(a.values)&&(i._waitingForFormatters[e]=null,i._formatterValuesCache[e]=a.values,t(r(a.values)),i.unsubscribe(s,e))}))}}));return this._waitingForFormatters[e]=o,o},h.prototype.snapshot=function(e){var t=this;if(this._waitingForSnapshot[e])return this._waitingForSnapshot[e];var i=new Promise((function(i,r){if(t._snapshotValuesCache[e])i(t._snapshotValuesCache[e]);else{var o=n.guid();t.subscribe(o,[e],(function(s){"error"===s.status&&(t._waitingForSnapshot[e]=null,r("Quotes snapshot is not received"));var a=s.values;a&&a.minmov&&a.pricescale&&a.description&&(t._waitingForSnapshot[e]=null,t._snapshotValuesCache[e]=a,i(a),t.unsubscribe(o,e))}))}}));return this._waitingForSnapshot[e]=i,i},TradingView.QuoteSessionMultiplexer=h,e&&e.exports&&(e.exports=h)},855247:(e,t,i)=>{i.d(t,{numOfDecimalPlaces:()=>o});var r=i(960521);function o(e){
return(new r.Big(e).toFixed().split(".")[1]||"").length}},781441:(e,t,i)=>{i.d(t,{formatNumber:()=>a,parseNumber:()=>_});var r=i(960521),o=i(259332),s=i(969680);function a(e,t,i,o,s){if(!Number.isFinite(e))return`${e}`;const a=-1===Math.sign(e)?"-":"";e=Math.abs(e);let n=void 0===i?e.toString():e.toFixed(i);if(n.includes("e")){if(!o)return`${a}${n.replace(".",t.decimalSign)}`;{const t=new r.Big(e);n=t.lt(1)?t.toFixed():t.toString()}}const _=n.split("."),l=_[0];let c=_[1];const m=function(e,t){let i=e.length;const r=[];for(;i>0;)r.unshift(e.slice(Math.max(i-3,0),i)),i-=3;return r.join(t)}(l,t.groupingSeparator);return void 0!==i&&(c=0===i?void 0:e.toFixed(i).slice(-i)),void 0!==s&&void 0!==c&&(c=function(e,t){let i=e.length-1;for(let r=i;r>=t&&"0"===e[r];r-=1)i-=1;return e.slice(0,i+1)}(c,s)),c?`${a}${m}${t.decimalSign}${c}`:`${a}${m}`}const n=(0,o.default)((e=>{const t=e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&");return new RegExp(t,"gm")}));function _(e,t){if(/^(NaN|[+|-]?Infinity)$/.test(e))return parseFloat(e);e=(0,s.stripLTRMarks)(e);const i=n(t.groupingSeparator);return i&&(e=e.replace(i,"")),e=e.replace(t.decimalSign,"."),/^(\+|-)?\d+(\.\d+|\.)?(e(\+|-)?\d+)?$/.test(e)?parseFloat(e):NaN}},976531:(e,t,i)=>{let r;i.d(t,{formatterOptions:()=>o,getNumberFormat:()=>c});const o={decimalSign:".",decimalSignFractional:"'"};const s={groupingSeparator:",",decimalSign:"."},a={groupingSeparator:".",decimalSign:","},n={groupingSeparator:" ",decimalSign:","},_={groupingSeparator:"",decimalSign:"."},l=new Map([["en",s],["th",s],["ja",s],["ko",s],["zh",s],["zh_TW",s],["ar",s],["he_IL",s],["ms_MY",s],["vi",s],["de",a],["es",a],["it",a],["tr",a],["pt",a],["id_ID",a],["fr",n],["pl",n],["ru",n]]);function c(e){if(e)return{decimalSign:o.decimalSign,groupingSeparator:""};const t=r||window.language||"";return l.get(t)??_}},618648:(e,t,i)=>{i.d(t,{DecimalPriceFormatterImpl:()=>c});var r=i(960521),o=i(735566),s=i(47522),a=i(781441),n=i(909456),_=i(976531);const l=(0,o.getLogger)("Chart.DecimalPriceFormatter");class c extends s.PriceFormatterImplementationBase{constructor(e){super(e);const{minMove2:t,ignoreLocaleNumberFormat:i}=e;void 0!==t&&10!==t&&0!==t&&1!==t&&l.logDebug("invalid minmove2"),this._ignoreLocaleNumberFormat=i}hasForexAdditionalPrecision(){return 10===this._minMove2}_parseUnsigned(e,t){return this._parseAsDecimal(e,t)}_formatUnsigned(e,t,i,r,o,a){const n={price:Math.abs(e),priceScale:this._priceScale,minMove:this._minMove,fractionalLength:this._fractionalLength,tailSize:t,cutFractionalByPrecision:i,ignoreLocaleNumberFormat:o,removeAllEndingZeros:a};return void 0!==this._variableMinTickData&&(Object.assign(n,(0,s.variableMinTickParamsByPrice)(!1,this._variableMinTickData,r??n.price)),this._ignoreMinMove&&(n.minMove=1)),this._formatAsDecimal(n)}_formatAsExponential(e,t){const i=Math.floor(.75*Math.log10(this._priceScale)),r=e*Math.pow(10,i),o=`e-${i}`,s=Math.log10(this._priceScale)-i;return`${r.toFixed(s).replace(".",t.decimalSign)}${o}`}_formatAsDecimal(e){
const{price:t,priceScale:i,minMove:o,fractionalLength:s=0,tailSize:l=0,cutFractionalByPrecision:c,ignoreLocaleNumberFormat:m,removeAllEndingZeros:d}=e,u=(0,_.getNumberFormat)(this._ignoreLocaleNumberFormat||m);if(t>=1e21)return t.toString().replace(".",u.decimalSign);if(i>1e15)return this._formatAsExponential(t,u);const p=Math.pow(10,l)*i/(c?1:o),h=1/p;let g;if(p>1)g=Math.floor(t);else{const e=Math.floor(Math.round(t/h)*h);g=0===Math.round((t-e)/h)?e:e+h}let S="";if(p>1){let e=c?new r.Big(t).mul(p).round(void 0,0).minus(new r.Big(g).mul(p)).toNumber():parseFloat((Math.round(t*p)-g*p).toFixed(s));e>=p&&(e-=p,g+=1),e=c?new r.Big(e).round(s,0).toNumber():parseFloat(e.toFixed(s))*o;const i=(0,n.numberToStringWithLeadingZero)(e,s+l),a=this._removeEndingZeros(i,d?i.length:l);S=a?u.decimalSign+a:a}return(0,a.formatNumber)(g,u)+S}_parseAsDecimal(e,t={}){const{ignoreLocaleNumberFormat:i}=t,r=(0,_.getNumberFormat)(this._ignoreLocaleNumberFormat||i),o=(0,a.parseNumber)(e,r);return Number.isFinite(o)?{value:o,res:!0,suggest:this.formatImpl(o)}:{error:this._formatterErrors.custom,res:!1}}}},803431:(e,t,i)=>{i.d(t,{FractionalPriceFormatterImpl:()=>l});var r=i(650151),o=i(735566),s=i(47522),a=i(909456),n=i(976531);const _=(0,o.getLogger)("Chart.FractionalPriceFormatter");class l extends s.PriceFormatterImplementationBase{constructor(e){super(e);const{minMove2:t}=e;null!=t&&t>0&&2!==t&&4!==t&&8!==t&&_.logDebug("invalid minmove2")}hasForexAdditionalPrecision(){return!1}_parseUnsigned(e){return this._minMove2?this._parseAsDoubleFractional(e):this._parseAsSingleFractional(e)}_formatUnsigned(e,t,i,o){const a={price:Math.abs(e),priceScale:this._priceScale,minMove:this._minMove,minMove2:this._minMove2,fractionalLength:(0,r.ensureDefined)(this._fractionalLength),tailSize:t};return void 0!==this._variableMinTickData&&Object.assign(a,(0,s.variableMinTickParamsByPrice)(!0,this._variableMinTickData,o??a.price)),this._formatAsFractional(a)}_parseAsSingleFractional(e){let t=s.intRegexp.exec(e);if(t){const t=parseFloat(e);return{value:t,res:!0,suggest:this.formatImpl(t)}}if(t=new RegExp("^(-?)([0-9]+)\\"+n.formatterOptions.decimalSignFractional+"([0-9]+)$").exec(e),t){const e=!!t[1],i=parseInt(t[2]),r=this._priceScale,o=this._patchFractPart(parseInt(t[3]),1,r);if(o>=r||o<0)return{error:this._formatterErrors.fraction,res:!1};let s=i+o/r;return e&&(s=-s),{value:s,res:!0,suggest:this.formatImpl(s)}}return{error:this._formatterErrors.custom,res:!1}}_parseAsDoubleFractional(e){let t=s.intRegexp.exec(e);if(t){const t=parseFloat(e);return{value:t,res:!0,suggest:this.formatImpl(t)}}if(t=new RegExp("^(-?)([0-9]+)\\"+n.formatterOptions.decimalSignFractional+"([0-9]+)\\"+n.formatterOptions.decimalSignFractional+"([0-9]+)$").exec(e),t){const e=!!t[1],i=parseInt(t[2]),r=void 0!==this._minMove2&&null!==this._minMove2?this._minMove2:NaN,o=this._priceScale/r,s=this._minMove2,a=this._patchFractPart(parseInt(t[3]),1,o),n=this._patchFractPart(parseInt(t[4]),2,s);if(a>=o||a<0)return{error:this._formatterErrors.fraction,res:!1};if(null!=s&&n>=s||n<0)return{
error:this._formatterErrors.secondFraction,res:!1};let _=null!=s?i+a/o+n/(o*s):NaN;return e&&(_=-_),{value:_,res:!0,suggest:this.formatImpl(_)}}return{error:this._formatterErrors.custom,res:!1}}_patchFractPart(e,t,i){const r={0:0,5:1},o={0:0,2:1,5:2,7:3},s={0:0,1:1,2:2,3:3,5:4,6:5,7:6,8:7};return 2===i?void 0===r[e]?-1:r[e]:4===i?void 0===o[e]?-1:o[e]:8===i&&2===t?void 0===s[e]?-1:s[e]:e}_formatAsFractional(e){const{price:t,tailSize:i,priceScale:r,minMove:o,minMove2:s,fractionalLength:_}=e,l=r/o;let c=Math.floor(t),m=i?Math.floor(t*l)-c*l:Math.round(t*l)-c*l;m===l&&(m=0,c+=1);let d="";if(i){let e=(t-c-m/l)*l;e=Math.round(e*Math.pow(10,i)),d=(0,a.numberToStringWithLeadingZero)(e,i),d=this._removeEndingZeros(d,i)}if(!_)throw new Error("_fractionalLength is not calculated");let u="";if(s){const e=m%s;m=(m-e)/s;const t=(0,a.numberToStringWithLeadingZero)(m,_),i=this._getFractPart(e,2,s);u=t+n.formatterOptions.decimalSignFractional+i}else m=this._getFractPart(m,1,r),u=(0,a.numberToStringWithLeadingZero)(m*o,_);return c.toString()+n.formatterOptions.decimalSignFractional+u+d}_getFractPart(e,t,i){const r=[0,5],o=[0,2,5,7],s=[0,1,2,3,5,6,7,8];return 2===i?void 0===r[e]?-1:r[e]:4===i?void 0===o[e]?-1:o[e]:8===i&&2===t?void 0===s[e]?-1:s[e]:e}}},47522:(e,t,i)=>{i.d(t,{PriceFormatterImplementationBase:()=>c,calculateDecimal:()=>_,intRegexp:()=>n,variableMinTickParamsByPrice:()=>l});var r=i(650151),o=i(444372),s=i(544151),a=i(969680);const n=new RegExp(/^(-?)[0-9]+$/);function _(e,t,i,r){let o=0;if(e>0&&t>0){let t=e;for(i&&r&&(t/=r);t>1;)t/=10,o++}return o}function l(e,t,i){const o=(0,r.ensureNotNull)((0,s.getMinTickData)({price:i,minTick:null,variableMinTickData:t,shouldCheckForEquality:!0})),{priceScale:a,minMove:n,minMove2:l}=o;return{priceScale:a,minMove:n,fractionalLength:_(a,n,e,l)}}class c{constructor(e){this._formatterErrors={custom:o.t(null,void 0,i(366123)),fraction:o.t(null,void 0,i(339643)),secondFraction:o.t(null,void 0,i(370784))};const{priceScale:t,minMove:r,minMove2:a,ignoreMinMove:n,variableMinTick:_,fractionalLength:l}=e;this._priceScale=t,this._minMove=r,this._minMove2=a,this._ignoreMinMove=n,this._variableMinTickData=void 0===_?void 0:(0,s.makeVariableMinTickData)({priceScale:t,minMove:r,minMove2:a},_),this._fractionalLength=l}formatImpl(e,t={}){const{signPositive:i,signNegative:r=!0,tailSize:o,cutFractionalByPrecision:s=!1,useRtlFormat:n=!0,variableMinTickDataPrice:_,ignoreLocaleNumberFormat:l,removeAllEndingZeros:c}=t;let m="";e<0?m=!1===r?"":"−":e&&!0===i&&(m="+");const d=m+this._formatUnsigned(Math.abs(e),o,s,_,l,c);return n?(0,a.forceLTRStr)(d):d}parse(e,t){return"+"===(e=(e=(0,a.stripLTRMarks)(e)).replace("−","-"))[0]&&(e=e.substring(1)),this._parseUnsigned(e,t)}_removeEndingZeros(e,t){for(let i=0;i<t&&"0"===e[e.length-1];i++)e=e.substring(0,e.length-1);return e}}},639247:(e,t,i)=>{function r(e,t,i,r,o){let s="";if(r=r?"; path="+r:"",o=o?"; domain="+o:"",i){const e=new Date;e.setTime(e.getTime()+24*i*60*60*1e3),s="; expires="+e.toUTCString()}else s="";document.cookie=e+"="+t+s+o+r}function o(e){
const t=e+"=",i=document.cookie.split(";");for(let e=0;e<i.length;e++){let r=i[e];for(;" "===r.charAt(0);)r=r.substring(1,r.length);if(0===r.indexOf(t))return r.substring(t.length,r.length)}return null}i.d(t,{get:()=>o,set:()=>r})},144731:(e,t,i)=>{function r(e,...t){return e&&"object"==typeof e?(0===t.length||t.forEach((t=>{null!=t&&"object"==typeof t&&Object.keys(t).forEach((i=>{const s=e[i],a=t[i];if(a===e)return;const n=Array.isArray(a);if(a&&(o(a)||n)){let t;t=n?s&&Array.isArray(s)?s:[]:s&&o(s)?s:{},e[i]=r(t,a)}else void 0!==a&&(e[i]=a)}))})),e):e}function o(e){if(!e||"[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);if(!t)return!0;const i=Object.hasOwnProperty.toString,r=t.hasOwnProperty("constructor")&&t.constructor;return"function"==typeof r&&i.call(r)===i.call(Object)}i.d(t,{deepExtend:()=>r})},709903:(e,t,i)=>{function r(e,t=!1){"loading"!==document.readyState?t?setTimeout((()=>e()),1):e():document.addEventListener("DOMContentLoaded",(()=>e()))}i.d(t,{whenDocumentReady:()=>r});new Promise((e=>{r(e)}))},496499:e=>{
e.exports=JSON.parse('{"adx":"widget_user_token-adx","adxD":"widget_user_token-adxD","agencialbmcombr":"widget_user_token-agencialbmcombr","aljaziracapitalD":"widget_user_token-aljaziracapitalD","aljaziracapitalRT":"widget_user_token-aljaziracapitalRT","alrajhicapitalcomD":"widget_user_token-alrajhicapitalcomD","alrajhicapitalcomRT":"widget_user_token-alrajhicapitalcomRT","ambito":"widget_user_token-ambito","artechecom":"widget_user_token-ArtecheD","atlcapital":"widget_user_token-atlcapital","bankirosru":"widget_user_token-bankirosru","bbvach":"widget_user_token-bbvach","beeksgroup":"widget_user_token-beeksgroup","belugagroupru":"widget_user_token-belugagroupru","bivacom":"widget_user_token-bivacom","bluefield":"widget_user_token-bluefield","bluelinefutures":"widget_user_token-bluelinefutures","bovespa":"widget_user_token-bmfbovespacombr","bytetravel":"widget_user_token-bytetravel","cboecanada":"widget_user_token-cboecanada","championnatbourse":"widget_user_token-championnatbourse","cincodias":"widget_user_token-cincodias","ceocaD":"widget_user_token-ceocaD","ceocaRT":"widget_user_token-ceocaRT","copees":"widget_user_token-copees","cselk":"widget_user_token-cselk","dailyfx":"widget_user_token-dailyfx","deepsearch":"widget_user_token-deepsearch","derayahcom":"widget_user_token-derayahcom","dfmaeD":"widget_user_token-dfmaeD","dfmaeRT":"widget_user_token-dfmaeRT","dolarhoy":"widget_user_token-dolarhoy","easynvestcombr":"widget_user_token-easynvestcombr","edaily":"widget_user_token-edaily","elconfidencial":"widget_user_token-elconfidencial","elespanolcom":"widget_user_token-elespanolcom","elobreroes":"widget_user_token-elobreroes","elnacionalD":"widget_user_token-ElNacionalD","elpaisfinanciero":"widget_user_token-elpaisfinanciero","elperiodicodelaenergia":"widget_user_token-elperiodicodelaenergia","euqueroinvestir":"widget_user_token-euqueroinvestircom","eurex":"widget_user_token-eurex","eurofins":"widget_user_token-eurofins","epe":"widget_user_token-epe","europapresses":"widget_user_token-europapresses","finanzasD":"widget_user_token-FinanzasD","forbescombr":"widget_user_token-forbescombr","genialinvestimentos":"widget_user_token-genialinvestimentos","healthitalia":"widget_user_token-healthitalia","hegnarno":"widget_user_token-hegnarno","hipotecariocomar":"widget_user_token-hipotecariocomar","investegatecouk":"widget_user_token-investegatecouk","investopedia":"widget_user_token-investopedia","integralyatirim":"widget_user_token-integralyatirim","ivsgroup":"widget_user_token-ivsgroup","boersenzeitung":"widget_user_token-boersenzeitung","lainformacioncom":"widget_user_token-lainformacioncom","marcopolocombr":"widget_user_token-marcopolocombr","mercadofinancierocom":"widget_user_token-mercadofinancierocom","moex":"widget_user_token-moex","moneytimescombr":"widget_user_token-moneytimescombr","monitordomercadoD":"widget_user_token-monitordomercadoD","negocios":"widget_user_token-negocios","NGX":"widget_user_token-NGX","NoticiasagricolaD":"widget_user_token-noticiasagricolaD","nsecokr":"widget_user_token-nsecokr","okdiario":"widget_user_token-okdiario","panafricanresourcescom":"widget_user_token-panafricanresourcescom","pse":"widget_user_token-pse","poder360":"widget_user_token-poder360","pseD":"widget_user_token-pseD","riyadhcapitalD":"widget_user_token-riyadhcapitalD","riyadhcapitalRT":"widget_user_token-riyadhcapitalRT","qecomqa":"widget_user_token-qecomqa","samolet":"widget_user_token-samolet","seeingmachinescom":"widget_user_token-seeingmachinescom","seudinheiro":"widget_user_token-seudinheiro","softwareag":"widget_user_token-softwareag","sogeclair":"widget_user_token-sogeclair","sgcompanyit":"widget_user_token-sgcompanyit","sharejunction":"widget_user_token-sharejunction","smartlab-custom":"widget_user_token-smartlab","smartlab":"widget_user_token-smartlab","spacemoneycombr":"widget_user_token-spacemoneycombr","stonexwdg":"widget_user_token-stonex£-!www.stonex.com","stoxio":"widget_user_token-stoxio","stonex":"widget_user_token-stonex","toroinvestimentoscom":"widget_user_token-toroinvestimentoscom","thecsecom":"widget_user_token-thecsecom","tradersclubbrasil":"widget_user_token-tradersclubbrasil","tradersclubbrasilD":"widget_user_token-tradersclubbrasilD","tradingview":"widget_user_token-tradingview","xtools":"widget_user_token-xtools","twitter":"widget_user_token-twitter","velocitycompositescom":"widget_user_token-velocitycompositescom","vocesaabrilcombr":"widget_user_token-vocesaabrilcombr","xCrtyJksp":"widget_user_token-xCrtyJksp","xpicombr":"widget_user_token-xpicombr"}')
},209044:e=>{e.exports=JSON.parse('{"cme":{"INDICATORS_ON_CHART":{"limit":99999}},"bovespa":{"INDICATORS_ON_CHART":{"limit":99999}},"qecomqa":{"INDICATORS_ON_CHART":{"limit":99999}}}')},729551:e=>{
e.exports=JSON.parse('{"free":{"CHAT_ASSISTANT":{"limit":5},"CHART_STORAGE":{"limit":1},"MULTIPLE_CHARTS":{"limit":1},"INDICATORS_ON_CHART":{"limit":2},"FUNDAMENTALS_ON_CHART":{"limit":1},"HISTORICAL_BARS":{"limit":5},"STUDY_ON_STUDY":{"limit":800,"child_limit":1},"SERVER_SIDE_ALERTS":{"overall_limit":2000,"limit":3,"complex_limit":0,"primitive_limit":3},"SCREENER_INTERVALS":{"interval":["1D","1W","1M"]},"STUDY_TEMPLATES":{"limit":1},"SIMULTANEOUS_CONNECTIONS":{"limit":1},"BACKEND_CONNECTIONS":{"limit":2},"IDEA_SOCIAL_LINKS":{"socials":["Twitter","Youtube"]},"MULTICOLOR_FLAGGED_SYMBOLS":{"limit":1},"WATCHLIST_SYMBOLS":{"limit":30},"VIDEO_IDEAS_LENGTH":{"limit":20},"CREATE_MORE_PORTFOLIOS":{"limit":1},"PORTFOLIO_TRANSACTIONS":{"limit":2000},"PORTFOLIO_HOLDINGS":{"limit":20}},"pro":{"CHAT_ASSISTANT":{"limit":5},"CHART_STORAGE":{"limit":5},"MULTIPLE_CHARTS":{"limit":2},"MARKET_DATA_LIMITS":{"limit":2,"primitive_limit":2},"CUSTOM_INTERVALS":{},"MULTIPLE_WATCHLISTS":{},"IMPORT_WATCHLISTS":{},"EXPORT_WATCHLISTS":{},"INDICATORS_ON_CHART":{"limit":5},"FUNDAMENTALS_ON_CHART":{"limit":4},"TV_PROSTUDIES":{"study_packages":["tv-chartpatterns"]},"HISTORICAL_BARS":{"limit":10},"TV_VOLUMEBYPRICE":{"study_packages":["tv-volumebyprice"]},"STUDY_ON_STUDY":{"limit":800,"child_limit":1},"TICK_BY_TICK_PUSH_DATA":{},"SERVER_SIDE_ALERTS":{"overall_limit":2000,"limit":20,"complex_limit":20,"primitive_limit":20},"SCREENER_AUTO_REFRESH":{},"SCREENER_NEW_AUTO_REFRESH":{},"SCREENER_EXPORT_DATA":{},"SCREENER_NEW_EXPORT_CSV_DATA":{},"SCREENER_NEW_SHOW_RESTRICTED_DATA":{},"SHOW_BONDS_RESTRICTED_DATA":{},"PORTFOLIO_DATA_LIMIT":{},"CREATE_MORE_PORTFOLIOS":{"limit":3},"PORTFOLIO_TRANSACTIONS":{"limit":5000},"PORTFOLIO_HOLDINGS":{"limit":50},"SCREENER_INTERVALS":{"interval":["1m","5m","15m","30m","1h","2h","4h","1D","1W","1M"]},"NO_SPONSORED_ADS":{"disable_on_lite_plan":"exclude_mobile"},"STUDY_TEMPLATES":{"limit":99999},"SIMULTANEOUS_CONNECTIONS":{"limit":1},"BACKEND_CONNECTIONS":{"limit":10},"IDC_AVAILABLE_DELAY":{},"STATUS":{"disable_on_trial":true},"BAR_REPLAY_INTRADAY":{"limit":1},"MULTIFLAGGED_SYMBOLS_LISTS":{},"SHOWS":{"disable_on_trial":true},"ALERTS_WEBHOOK":{},"DEEP_FUNDAMENTALS_HISTORY":{},"PUBLISH_PROTECTED_SCRIPTS":{"disable_on_trial":true},"IDEA_SOCIAL_LINKS":{"socials":["Twitter","Youtube"]},"EXTENDED_SOCIAL_LINKS":{"socials":["Facebook","Instagram"],"disable_on_trial":true},"MULTI_MONITOR":{},"MULTICOLOR_FLAGGED_SYMBOLS":{"limit":7},"INTRADAY_EXCHANGE":{},"VOLUME_PROFILE":{},"STREAMS_ACCESS":{"followers":10},"SMS_2FA_VERIFICATION":{"disable_on_trial":true},"SOCIAL_ACTIVITY":{"disable_on_trial":true},"WATCHLIST_SYMBOLS":{"limit":1000},"FIELDS_PERMISSIONS":{"items":["refbonds"]},"CUSTOM_RANGE_BARS":{},"FASTEST_DATA_FLOW":{},"SESSION_VOLUME_PROFILE":{},"SHOW_ETF_HOLDINGS_DATA":{}},"pro_realtime":{"extends":"pro","CHART_STORAGE":{"limit":10},"MULTIPLE_CHARTS":{"limit":4},"MARKET_DATA_LIMITS":{"limit":4,"primitive_limit":4},"INDICATORS_ON_CHART":{"limit":10},"FUNDAMENTALS_ON_CHART":{"limit":7},"TV_PROSTUDIES":{"study_packages":["tv-prostudies","tv-chartpatterns"]},"STUDY_ON_STUDY":{"limit":800,"child_limit":9},"SERVER_SIDE_ALERTS":{"overall_limit":2000,"limit":100,"complex_limit":100,"primitive_limit":100},"ALERTS_MULTICONDITIONS":{"condition_limit":5},"CHAT_ASSISTANT":{"limit":5},"CAN_EDIT_PUBLIC_CHATS":{"disable_on_trial":true},"BACKEND_CONNECTIONS":{"limit":20},"EXPORT_CHART_DATA":{},"CUSTOM_FORMULAS":{},"INTRADAY_EXOTIC_CHARTS":{},"KAGI_RENKO":{},"INTRADAY_SPREAD":{},"CUSTOM_CHATS":{},"BACKTESTING_EXPORT":{}},"pro_premium":{"extends":"pro_realtime","USE_BAR_MAGNIFIER":{},"CHART_STORAGE":{"limit":99999},"MULTIPLE_CHARTS":{"limit":8},"MARKET_DATA_LIMITS":{"limit":6,"primitive_limit":6},"INDICATORS_ON_CHART":{"limit":25},"FUNDAMENTALS_ON_CHART":{"limit":10},"CHAT_ASSISTANT":{"limit":99999},"CHART_PATTERNS":{"study_packages":["tv-chartpatterns","tv-chart_patterns"]},"HISTORICAL_BARS":{"limit":20},"STUDY_ON_STUDY":{"limit":800,"child_limit":24},"SERVER_SIDE_ALERTS":{"overall_limit":2000,"limit":400,"complex_limit":400,"primitive_limit":400,"watchlist_limit":2,"overall_watchlist_limit":5},"SIMULTANEOUS_CONNECTIONS":{"limit":2},"BACKEND_CONNECTIONS":{"limit":50},"IDEA_SIGNATURE":{"disable_on_trial":true},"PROFILE_WEBSITE_FIELD":{"disable_on_trial":true},"BAR_REPLAY_INTRADAY":{"limit":4},"ALERTS_NO_EXPIRATION":{},"PUBLISH_INVITE_ONLY_SCRIPTS":{"disable_on_trial":true},"EXPORT_CHART_DATA":{},"DEEP_HISTORY_BACKTEST":{},"ALERTS_ON_SECONDS":{},"PERMANENT_STREAM_RECORDS":{},"EXTENDED_SOCIAL_LINKS":{"socials":["Facebook","Instagram","Website"],"disable_on_trial":true},"SECONDS_INTERVALS":{},"TPO_PERIODIC":{"study_packages":["tv-volumebyprice"]},"TPO_CHART_STYLE":{},"VOLUME_CANDLES":{},"VIDEO_IDEAS_LENGTH":{"limit":60},"VOLUME_FOOTPRINT":{},"PINE_SCREENER":{}},"pro_expert":{"extends":"pro_premium","INDICATORS_ON_CHART":{"limit":30},"FUNDAMENTALS_ON_CHART":{"limit":15},"STUDY_ON_STUDY":{"limit":800,"child_limit":29},"MULTIPLE_CHARTS":{"limit":10},"MARKET_DATA_LIMITS":{"limit":12,"primitive_limit":12},"HISTORICAL_BARS":{"limit":25},"SERVER_SIDE_ALERTS":{"overall_limit":2000,"limit":600,"complex_limit":600,"primitive_limit":600,"watchlist_limit":10,"overall_watchlist_limit":20},"BACKEND_CONNECTIONS":{"limit":80},"BAR_REPLAY_INTRADAY":{"limit":6},"EXPORT_FINANCIALS_DATA":{},"TICK_INTERVALS":{},"FIRST_PRIORITY_SUPPORT":{},"BUY_PRO_DATA":{}},"pro_premium_expert":{"extends":"pro_expert","INDICATORS_ON_CHART":{"limit":50},"FUNDAMENTALS_ON_CHART":{"limit":25},"STUDY_ON_STUDY":{"limit":800,"child_limit":49},"MULTIPLE_CHARTS":{"limit":16},"MARKET_DATA_LIMITS":{"limit":99999,"primitive_limit":25},"HISTORICAL_BARS":{"limit":40},"SERVER_SIDE_ALERTS":{"overall_limit":4000,"limit":1000,"complex_limit":1000,"primitive_limit":1000,"watchlist_limit":15,"overall_watchlist_limit":30},"BACKEND_CONNECTIONS":{"limit":200},"BAR_REPLAY_INTRADAY":{"limit":10}}}')
}}]);