(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3905],{825549:d=>{d.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},510555:d=>{d.exports={wrapper:"wrapper-VB9J73Gf",focused:"focused-VB9J73Gf",readonly:"readonly-VB9J73Gf",disabled:"disabled-VB9J73Gf","size-small":"size-small-VB9J73Gf","size-medium":"size-medium-VB9J73Gf","size-large":"size-large-VB9J73Gf","font-size-small":"font-size-small-VB9J73Gf","font-size-medium":"font-size-medium-VB9J73Gf","font-size-large":"font-size-large-VB9J73Gf","border-none":"border-none-VB9J73Gf",shadow:"shadow-VB9J73Gf","border-thin":"border-thin-VB9J73Gf","border-thick":"border-thick-VB9J73Gf","intent-default":"intent-default-VB9J73Gf","intent-success":"intent-success-VB9J73Gf","intent-warning":"intent-warning-VB9J73Gf","intent-danger":"intent-danger-VB9J73Gf","intent-primary":"intent-primary-VB9J73Gf","corner-top-left":"corner-top-left-VB9J73Gf","corner-top-right":"corner-top-right-VB9J73Gf","corner-bottom-right":"corner-bottom-right-VB9J73Gf","corner-bottom-left":"corner-bottom-left-VB9J73Gf",childrenContainer:"childrenContainer-VB9J73Gf"}},861796:d=>{d.exports={autocomplete:"autocomplete-uszkUMOz",caret:"caret-uszkUMOz",icon:"icon-uszkUMOz",suggestions:"suggestions-uszkUMOz",suggestion:"suggestion-uszkUMOz",noResults:"noResults-uszkUMOz",selected:"selected-uszkUMOz",opened:"opened-uszkUMOz"}},927061:d=>{d.exports={buttonWrap:"buttonWrap-icygBqe7",desktopSize:"desktopSize-icygBqe7",drawer:"drawer-icygBqe7",menuBox:"menuBox-icygBqe7"}},98993:d=>{d.exports={button:"button-F5dN3ulE",emoji:"emoji-F5dN3ulE",emptySelect:"emptySelect-F5dN3ulE"}},539453:d=>{d.exports={emojiWrap:"emojiWrap-R2CTpmHr",emoji:"emoji-R2CTpmHr",tooltipEmoji:"tooltipEmoji-R2CTpmHr",tooltipEmojiWrap:"tooltipEmojiWrap-R2CTpmHr"}},307567:d=>{d.exports={emojiSelect:"emojiSelect-IY7RpEY6",placeholder:"placeholder-IY7RpEY6"}},805184:(d,e,u)=>{"use strict";var t,n,s;function c(d="default"){switch(d){case"default":return"primary";case"stroke":return"secondary"}}function r(d="primary"){switch(d){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function o(d="m"){switch(d){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}u.d(e,{Button:()=>l}),function(d){d.Primary="primary",d.Success="success",d.Default="default",d.Danger="danger"}(t||(t={})),function(d){d.Small="s",d.Medium="m",d.Large="l"}(n||(n={})),function(d){d.Default="default",d.Stroke="stroke"}(s||(s={}));var a=u(50959),i=u(228837);function f(d){const{intent:e,size:u,appearance:t,useFullWidth:n,icon:s,...a}=d;return{...a,color:r(e),size:o(u),variant:c(t),stretch:n}}function l(d){return a.createElement(i.SquareButton,{...f(d)})}},654936:(d,e,u)=>{"use strict";u.d(e,{InputControl:()=>E})
;var t=u(50959),n=u(497754),s=u(800417),c=u(269842),r=u(1811),o=u(525388),a=u(21778),i=u(383836),f=u(603548),l=u(34735),p=u(102691),h=u(825549),m=u.n(h);function b(d){return!(0,s.isAriaAttribute)(d)&&!(0,s.isDataAttribute)(d)}function g(d){const{id:e,title:u,role:c,tabIndex:r,placeholder:o,name:a,type:i,value:f,defaultValue:h,draggable:g,autoComplete:E,autoFocus:y,autoCapitalize:v,autoCorrect:_,maxLength:C,min:w,max:S,step:k,pattern:x,inputMode:O,onSelect:j,onFocus:M,onBlur:z,onKeyDown:B,onKeyUp:I,onKeyPress:F,onChange:V,onDragStart:D,size:A="small",className:N,inputClassName:R,disabled:P,readonly:T,containerTabIndex:U,startSlot:L,endSlot:K,reference:J,containerReference:H,onContainerFocus:G,...W}=d,q=(0,s.filterProps)(W,b),Y={...(0,s.filterAriaProps)(W),...(0,s.filterDataProps)(W),id:e,title:u,role:c,tabIndex:r,placeholder:o,name:a,type:i,value:f,defaultValue:h,draggable:g,autoComplete:E,autoFocus:y,autoCapitalize:v,autoCorrect:_,maxLength:C,min:w,max:S,step:k,pattern:x,inputMode:O,onSelect:j,onFocus:M,onBlur:z,onKeyDown:B,onKeyUp:I,onKeyPress:F,onChange:V,onDragStart:D};return t.createElement(l.ControlSkeleton,{...q,disabled:P,readonly:T,tabIndex:U,className:n(m().container,N),size:A,ref:H,onFocus:G,startSlot:L,middleSlot:t.createElement(p.MiddleSlot,null,t.createElement("input",{...Y,className:n(m().input,m()[`size-${A}`],R,L&&m()["with-start-slot"],K&&m()["with-end-slot"]),disabled:P,readOnly:T,ref:J})),endSlot:K})}function E(d){d=(0,a.useControl)(d);const{disabled:e,autoSelectOnFocus:u,tabIndex:n=0,onFocus:s,onBlur:l,reference:p,containerReference:h=null}=d,m=(0,t.useRef)(null),b=(0,t.useRef)(null),[E,y]=(0,i.useFocus)(),v=e?void 0:E?-1:n,_=e?void 0:E?n:-1,{isMouseDown:C,handleMouseDown:w,handleMouseUp:S}=(0,f.useIsMouseDown)(),k=(0,c.createSafeMulticastEventHandler)(y.onFocus,(function(d){u&&!C.current&&(0,r.selectAllContent)(d.currentTarget)}),s),x=(0,c.createSafeMulticastEventHandler)(y.onBlur,l),O=(0,t.useCallback)((d=>{m.current=d,p&&("function"==typeof p&&p(d),"object"==typeof p&&(p.current=d))}),[m,p]);return t.createElement(g,{...d,isFocused:E,containerTabIndex:v,tabIndex:_,onContainerFocus:function(d){b.current===d.target&&null!==m.current&&m.current.focus()},onFocus:k,onBlur:x,reference:O,containerReference:(0,o.useMergedRefs)([b,h]),onMouseDown:w,onMouseUp:S})}},21778:(d,e,u)=>{"use strict";u.d(e,{useControl:()=>s});var t=u(269842),n=u(383836);function s(d){const{onFocus:e,onBlur:u,intent:s,highlight:c,disabled:r}=d,[o,a]=(0,n.useFocus)(void 0,r),i=(0,t.createSafeMulticastEventHandler)(r?void 0:a.onFocus,e),f=(0,t.createSafeMulticastEventHandler)(r?void 0:a.onBlur,u);return{...d,intent:s||(o?"primary":"default"),highlight:c??o,onFocus:i,onBlur:f}}},603548:(d,e,u)=>{"use strict";u.d(e,{useIsMouseDown:()=>n});var t=u(50959);function n(){const d=(0,t.useRef)(!1),e=(0,t.useCallback)((()=>{d.current=!0}),[d]),u=(0,t.useCallback)((()=>{d.current=!1}),[d]);return{isMouseDown:d,handleMouseDown:e,handleMouseUp:u}}},1811:(d,e,u)=>{"use strict";function t(d){null!==d&&d.setSelectionRange(0,d.value.length)}
u.d(e,{selectAllContent:()=>t})},73007:(d,e,u)=>{"use strict";u.d(e,{Autocomplete:()=>m});var t=u(609838),n=u(50959),s=u(497754),c=u(515783),r=u(102691),o=u(738036),a=u(327871),i=u(8361),f=u(822312),l=u(920709),p=u(861796);function h(d,e){return""===d||-1!==e.toLowerCase().indexOf(d.toLowerCase())}class m extends n.PureComponent{constructor(d){if(super(d),this._containerInputElement=null,this._raf=null,this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this.setState({appearingWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null})))},this._handleMeasure=()=>{if(this.state.isMeasureValid||!this.props.suggestionsInPortal||!this._containerInputElement)return;const{bottom:d,left:e,width:u}=this._containerInputElement.getBoundingClientRect();this.setState({appearingWidth:u,appearingPosition:{x:e,y:d},isMeasureValid:!0})},this._setInputRef=d=>{d&&(this._inputElement=d,this.props.setupHTMLInput&&this.props.setupHTMLInput(d),this._inputElement.addEventListener("keyup",this._handleKeyUpEnter))},this._setContainerInputRef=d=>{this._containerInputElement=d},this._handleCaretClick=()=>{this.state.isOpened?(this._close(),this.props.preventOnFocusOpen&&this._focus()):this.props.preventOnFocusOpen?this._open():this._focus()},this._handleOutsideClick=()=>{const{allowUserDefinedValues:d,value:e,onChange:u}=this.props,{queryValue:t}=this.state;d?u&&t!==e&&u(t):this.setState(this._valueToQuery(e)),this._close()},this._handleFocus=d=>{this.props.preventOnFocusOpen||this._open(),this.props.onFocus&&this.props.onFocus(d)},this._handleChange=d=>{const{preventSearchOnEmptyQuery:e,allowUserDefinedValues:u,onChange:t,onSuggestionsOpen:n,onSuggestionsClose:s}=this.props;if(e&&""===d)this.setState({queryValue:d,isOpened:!1,active:void 0}),s&&s();else{const e=this._suggestions(d),t=Object.keys(e).length>0;this.setState({queryValue:d,isOpened:t,active:u?void 0:this._getActiveKeyByValue(d)}),t&&n&&n()}u&&t&&t(d)},this._handleItemClick=d=>{const e=d.currentTarget.id;this.setState({queryValue:b(this.props.source)[e]}),this.props.onChange&&this.props.onChange(e),this._close()},this._handleKeyDown=d=>{if(-1===[a.KeyCode.DownArrow,a.KeyCode.UpArrow,a.KeyCode.Enter,a.KeyCode.Escape].indexOf(d.which))return;const{allowUserDefinedValues:e,value:u,onChange:t,onSuggestionsOpen:n}=this.props,{active:s,isOpened:c,queryValue:r}=this.state;c&&(d.preventDefault(),d.stopPropagation());const o=this._suggestions(r);switch(d.which){case a.KeyCode.DownArrow:case a.KeyCode.UpArrow:const i=Object.keys(o);if(!c&&i.length&&d.which===a.KeyCode.DownArrow){this.setState({isOpened:!0,active:i[0]}),n&&n();break}let f;if(void 0===s){if(d.which===a.KeyCode.UpArrow){this._close();break}f=0}else f=i.indexOf(s)+(d.which===a.KeyCode.UpArrow?-1:1);f<0&&(f=0),f>i.length-1&&(f=i.length-1);const l=i[f];this.setState({active:l});const p=document.getElementById(l);p&&this._scrollIfNotVisible(p,this._suggestionsElement);break;case a.KeyCode.Escape:this._close(),c||this._blur();break;case a.KeyCode.Enter:let h=s
;e&&(c&&h?this.setState(this._valueToQuery(h)):h=r),void 0!==h&&(this._close(),c||this._blur(),h!==u?t&&t(h):this.setState(this._valueToQuery(h)))}},this._setSuggestionsRef=d=>{d&&(this._suggestionsElement=d)},this._scrollIfNotVisible=(d,e)=>{const u=e.scrollTop,t=e.scrollTop+e.clientHeight,n=d.offsetTop,s=n+d.clientHeight;n<=u?d.scrollIntoView(!0):s>=t&&d.scrollIntoView(!1)},!(d=>Array.isArray(d.source)||!d.allowUserDefinedValues)(d))throw new Error("allowUserDefinedProps === true cay only be used if source is array");this.state={valueFromProps:d.value,isOpened:!1,active:d.value,queryValue:b(d.source)[d.value]||(d.allowUserDefinedValues?d.value:"")}}componentDidMount(){this.props.suggestionsInPortal&&window.addEventListener("resize",this._resize)}componentDidUpdate(){this.state.isOpened&&this._handleMeasure()}componentWillUnmount(){this._inputElement&&this._inputElement.removeEventListener("keyup",this._handleKeyUpEnter),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),window.removeEventListener("resize",this._resize)}render(){const{emojiPicker:d}=this.props;return n.createElement(o.OutsideEvent,{handler:this._handleOutsideClick,click:!0},(e=>n.createElement("div",{className:s(p.autocomplete,"js-dialog-skip-escape"),ref:e},n.createElement(f.InputWithEmojiSelect,{id:this.props.id,name:this.props.name,endSlot:Object.keys(this._suggestions(this.state.queryValue)).length?n.createElement(r.EndSlot,null,n.createElement("span",{className:p.caret,onClick:this._handleCaretClick,tabIndex:-1},n.createElement(c.ToolWidgetCaret,{className:p.icon,dropped:this.state.isOpened}))):void 0,maxLength:this.props.maxLength,reference:this._setInputRef,containerReference:this._setContainerInputRef,stretch:!0,placeholder:this.props.placeholder,value:this.state.queryValue,intent:this.props.error?"danger":void 0,onChange:this._handleChange,onFocus:this._handleFocus,onBlur:this.props.onBlur,onMouseOver:this.props.onMouseOver,onMouseOut:this.props.onMouseOut,onKeyDown:this._handleKeyDown,autoComplete:"off",size:this.props.size,emojiPicker:d}),this._renderSuggestions())))}static getDerivedStateFromProps(d,e){const{allowUserDefinedValues:u,value:t,source:n}=d;if(t===e.valueFromProps&&e.isOpened)return null;const s=u?t:""===t?"":b(n)[t]||e.queryValue;return{...e,valueFromProps:t,active:t,queryValue:s}}_renderSuggestions(){return this.props.suggestionsInPortal?this.state.isOpened?this._renderPortalSuggestions():null:this._renderSuggestionsItems()}_renderPortalSuggestions(){return n.createElement(i.Portal,null,this._renderSuggestionsItems())}_focus(){this._inputElement.focus()}_blur(){this._inputElement.blur()}_open(){const{onSuggestionsOpen:d}=this.props;this._focus(),this.setState({isOpened:!0,active:this.props.value}),d&&d()}_close(){const{onSuggestionsClose:d}=this.props;this.setState({isOpened:!1,active:void 0}),d&&d()}_suggestions(d){const{filter:e=h}=this.props,u=b(this.props.source),t={};return Object.keys(u).filter((t=>e(d,u[t]))).forEach((d=>t[d]=u[d])),t}_renderSuggestionsItems(){
const d=this._suggestions(this.state.queryValue),e=Object.keys(d).map((e=>{const u=s(p.suggestion,this.state.active===e&&p.selected);return n.createElement("li",{id:e,key:e,className:u,onClick:this._handleItemClick},n.createElement(l.LeadingEmojiText,{text:d[e]}))})),c=n.createElement("li",{className:p.noResults},t.t(null,void 0,u(885888)));if(!e.length&&this.props.noEmptyText)return null;const{appearingPosition:r,appearingWidth:o}=this.state;return n.createElement("ul",{className:s(p.suggestions,this.state.isOpened&&p.opened),ref:this._setSuggestionsRef,style:{left:r&&r.x,top:r&&r.y,width:o&&o}},e.length?e:c)}_handleKeyUpEnter(d){d.which===a.KeyCode.Enter&&d.stopImmediatePropagation()}_getActiveKeyByValue(d){const{filter:e=h}=this.props,u=this._suggestions(d),t=Object.keys(u);for(const n of t)if(e(d,u[n]))return n;return t[0]}_valueToQuery(d){return{queryValue:b(this.props.source)[d]||""}}}function b(d){let e={};return Array.isArray(d)?d.forEach((d=>{e[d]=d})):e=d,e}},195680:(d,e,u)=>{"use strict";u.d(e,{useAutoSelect:()=>s});var t=u(50959),n=u(601227);function s(){const d=(0,t.useRef)(null);return(0,t.useLayoutEffect)((()=>{n.CheckMobile.iOS()||d.current&&(d.current.focus(),d.current.select())}),[]),d}},205408:(d,e,u)=>{"use strict";u.d(e,{EmojiPicker:()=>M});var t=u(50959),n=u(870122),s=u(499547),c=u(994437),r=u(611005);var o=u(624216),a=u(163694),i=u(759339),f=u(510618),l=u(930202),p=u(493173),h=u(930052);function m(d){!function(d,e){(0,t.useEffect)((()=>{const u=e||document;return u.addEventListener("scroll",d),()=>u.removeEventListener("scroll",d)}),[d])}(d,document)}var b=u(162458),g=u(996038),E=u(497754),y=u.n(E),v=u(510555);function _(d){const{children:e,highlight:u,disabled:n,reference:s,...c}=d,r=u?"primary":"default";return t.createElement("div",{...c,ref:s,className:y()(v.wrapper,v[`intent-${r}`],v["border-thin"],v["size-medium"],u&&v.highlight,u&&v.focused,n&&v.disabled),"data-role":"button"},t.createElement("div",{className:y()(v.childrenContainer,n&&v.disabled)},e),u&&t.createElement("span",{className:v.shadow}))}var C=u(99021),w=u(927061);const S=()=>null,k=(0,p.mergeThemes)(f.DEFAULT_MENU_THEME,{menuBox:w.menuBox}),x=378,O=18,j=200;function M(d){const{value:e,disabled:u,onSelect:c,onClose:f,canBeEmpty:p,renderButton:E=z}=d,y=(0,t.useRef)(null),{current:v}=(0,t.useRef)((_=e,n.getJSON("RecentlyUsedEmojis",[_]).filter((d=>d!==C.EMPTY_EMOJI))));var _;const M=(0,t.useRef)(null),[B,I]=(0,t.useState)(v),[F,V]=(0,t.useState)(!1),D=(0,t.useCallback)((()=>{V(!1),f?.()}),[f]),A=(0,t.useRef)(0);m((0,t.useCallback)((()=>{Date.now()-A.current<j||D()}),[D]));const N=(0,t.useCallback)((d=>{if(d!==C.EMPTY_EMOJI){const e=Array.from(new Set([d,...B])).slice(0,O);n.setJSON("RecentlyUsedEmojis",e),I(e)}c(d),D()}),[B,c]),R=(0,t.useMemo)((()=>p?[C.EMPTY_EMOJI,...B].slice(0,O):B),[B,p]),P=(T=R,(0,t.useMemo)((()=>{const d=(0,r.emojiGroups)();return d[0].emojis=T,d}),[T]));var T;return t.createElement(t.Fragment,null,t.createElement("div",{ref:y,className:w.buttonWrap},E({emoji:e,isOpened:F,disabled:u,onClick:function(){
if(F)return void D();u||(V(!0),A.current=Date.now())}})),t.createElement(h.MatchMedia,{rule:g.DialogBreakpoints.TabletSmall},(d=>F&&t.createElement(a.DrawerManager,null,d?t.createElement(i.Drawer,{className:w.drawer,position:"Bottom",onClose:D},t.createElement(s.EmojiList,{emojis:P,onSelect:N,height:x})):t.createElement(o.PopupMenu,{theme:k,onKeyDown:L,isOpened:!0,position:(0,b.getPopupPositioner)(y.current,{horizontalDropDirection:b.HorizontalDropDirection.FromLeftToRight,horizontalAttachEdge:b.HorizontalAttachEdge.Left}),closeOnClickOutside:!1,onClickOutside:K,onClose:S,controller:M,onOpen:U,tabIndex:-1},t.createElement(s.EmojiList,{className:w.desktopSize,emojis:P,onSelect:N,height:x}))))));function U(){M.current?.focus()}function L(d){27===(0,l.hashFromEvent)(d)&&(d.preventDefault(),d.stopPropagation(),D())}function K(d){const e=d.target;e instanceof Node&&y.current?.contains(e)||D()}}function z(d){const{emoji:e,isOpened:u,disabled:n,onClick:s}=d;return t.createElement(_,{highlight:u,disabled:n,"data-name":"emoji-picker"},t.createElement(c.EmojiWrap,{emoji:e,onClick:s}))}},920709:(d,e,u)=>{"use strict";u.d(e,{LeadingEmojiText:()=>r});var t=u(50959),n=u(574871),s=u(198412),c=u(539453);function r(d){const{text:e,textRender:u,firstSegmentOnly:r=!1}=d,{leadingEmoji:o,processedText:a}=(0,t.useMemo)((()=>(0,n.processTextWithLeadingEmoji)({text:e,textRender:u,firstSegmentOnly:r})),[e,u,r]);return o?t.createElement(t.Fragment,null,t.createElement("span",{className:c.emojiWrap}," ",t.createElement(s.EmojiItem,{className:c.emoji,emoji:o})),""!==a&&t.createElement(t.Fragment,null," ",a)):t.createElement(t.Fragment,null,a)}},574871:(d,e,u)=>{"use strict";u.d(e,{getLeadingEmojiHtml:()=>f,processTextWithLeadingEmoji:()=>i});var t=u(871645),n=u(611005),s=u(99021),c=u(440891),r=u(278765),o=u(539453);const a=c.enabled("advanced_emoji_in_titles");function i(d){const{text:e,textRender:u=d=>d,firstSegmentOnly:c=!1}=d,r=(0,t.getFirstSegmentOrCodePointString)(e),o=null!==r&&(0,n.isSupportedEmoji)(r)?r:s.EMPTY_EMOJI,i=c?r||"":e;if(!a||o===s.EMPTY_EMOJI)return{leadingEmoji:"",processedText:u(i)};return{leadingEmoji:o,processedText:u(i.replace(o,""))}}function f(d){const{processedText:e,leadingEmoji:u}=i({text:d}),n=(0,t.htmlEscape)(e);if(!u)return n;return`${function(d){const e=(0,r.getTwemojiUrl)(d,"png");return`<span class=${o.tooltipEmojiWrap}>&nbsp<img class=${o.tooltipEmoji} src=${e} decoding="async" width="12" height="12" alt="" draggable="false"/></span>`}(u)}&nbsp;${n}`}},822312:(d,e,u)=>{"use strict";u.d(e,{InputWithEmojiSelect:()=>w});var t=u(50959),n=u(650151),s=u(930202),c=u(718736),r=u(654936),o=u(440891),a=u(611005),i=u(497754),f=u.n(i),l=u(878112),p=u(898237),h=u(609838),m=u(198412),b=u(205408),g=u(99021),E=u(98993),y=u(394032);function v(d){const{emoji:e,onSelect:n,onClose:s,buttonClassName:c}=d;return t.createElement(b.EmojiPicker,{value:e,onSelect:n,onClose:s,renderButton:d=>function(d,e){const{emoji:n,onClick:s}=d;return t.createElement(p.LightButton,{className:f()(e,E.button,"apply-common-tooltip"),
title:h.t(null,void 0,u(904461)),size:"xsmall",color:"gray",variant:"ghost",onClick:s,tabIndex:0,startSlot:n===g.EMPTY_EMOJI?t.createElement(l.Icon,{className:E.emptySelect,icon:y}):t.createElement(m.EmojiItem,{className:E.emoji,emoji:n})})}(d,c),canBeEmpty:!0})}var _=u(307567);const C=o.enabled("advanced_emoji_in_titles");function w(d){const{value:e="",onChange:u,reference:o=null,emojiPicker:i=!1,...f}=d,{emoji:l,emojiLessString:p}=(0,t.useMemo)((()=>(0,a.separateEmoji)(e)),[e]),h=(0,c.useFunctionalRefObject)(o);return C&&i?t.createElement(r.InputControl,{...f,reference:h,value:p,onChange:function(d){u?.(l+d.currentTarget.value)},onKeyDown:function(e){if(d.onKeyDown?.(e),e.defaultPrevented)return;const{selectionStart:t,selectionEnd:c}=(0,n.ensureNotNull)(h.current);0===t&&0===c&&l&&8===(0,s.hashFromEvent)(e)&&(e.preventDefault(),u?.(p))},startSlot:t.createElement(v,{emoji:l,onSelect:function(d){u?.(d+p)},onClose:function(){h.current?.focus()},buttonClassName:_.emojiSelect})}):t.createElement(r.InputControl,{...f,value:e,reference:o,onChange:function(d){u?.(d.currentTarget.value)}})}},327871:(d,e,u)=>{"use strict";u.d(e,{KeyCode:()=>t,makeKeyboardListener:()=>c});var t,n=u(50959);!function(d){d[d.Enter=13]="Enter",d[d.Space=32]="Space",d[d.Backspace=8]="Backspace",d[d.DownArrow=40]="DownArrow",d[d.UpArrow=38]="UpArrow",d[d.RightArrow=39]="RightArrow",d[d.LeftArrow=37]="LeftArrow",d[d.Escape=27]="Escape",d[d.Tab=9]="Tab"}(t||(t={}));class s{constructor(){this._handlers=new Map}registerHandlers(d){Object.keys(d).forEach((e=>{const u=parseInt(e);let t=d[u];if(Array.isArray(t)||(t=[t]),this._handlers.has(u)){const d=this._handlers.get(u);d&&t.forEach((e=>d.add(e)))}else this._handlers.set(u,new Set(t))}))}unregisterHandlers(d){Object.keys(d).forEach((e=>{const u=parseInt(e);let t=d[u];if(Array.isArray(t)||(t=[t]),this._handlers.has(u)){const d=this._handlers.get(u);d&&t.forEach((e=>d.delete(e)))}}))}deleteAllHandlers(){this._handlers=new Map}registerHandler(d,e){if(this._handlers.has(d)){const u=this._handlers.get(d);u&&u.add(e)}else this._handlers.set(d,new Set([e]))}unregisterHandler(d,e){if(this._handlers.has(d)){const u=this._handlers.get(d);u&&u.delete(e)}}listen(d){if(this._handlers.has(d.keyCode)){const e=this._handlers.get(d.keyCode);e&&e.forEach((e=>e(d)))}}}function c(d){var e;return(e=class extends n.PureComponent{constructor(d){super(d),this._keyboardListener=new s,this._listener=this._keyboardListener.listen.bind(this._keyboardListener)}componentDidMount(){this._registerHandlers(this.props.keyboardEventHandlers)}componentDidUpdate(d){d.keyboardEventHandlers!==this.props.keyboardEventHandlers&&this._registerHandlers(this.props.keyboardEventHandlers)}render(){const{keyboardEventHandlers:e,...u}=this.props;return n.createElement(d,{...u,onKeyDown:this._listener})}_registerHandlers(d){d&&(this._keyboardListener.deleteAllHandlers(),this._keyboardListener.registerHandlers(d))}}).displayName=`KeyboardListener(${d.displayName??d.name??"Component"})`,e}},394032:d=>{
d.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M8 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM11 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2ZM6.2 10A3 3 0 0 0 9 12a3 3 0 0 0 2.8-2l.95.34A4 4 0 0 1 9 13a4 4 0 0 1-3.75-2.66L6.2 10Z"/><path fill="currentColor" fill-rule="evenodd" d="M1 9a8 8 0 1 1 16 0A8 8 0 0 1 1 9Zm1 0a7 7 0 1 1 14 0A7 7 0 0 1 2 9Z"/></svg>'},818438:(d,e,u)=>{"use strict";u.d(e,{default:()=>t});const t=function(){var d={base:"https://twemoji.maxcdn.com/v/13.0.1/",ext:".png",size:"72x72",className:"emoji",convert:{fromCodePoint:function(d){var e="string"==typeof d?parseInt(d,16):d;if(e<65536)return r(e);return r(55296+((e-=65536)>>10),56320+(1023&e))},toCodePoint:g},onerror:function(){this.parentNode&&this.parentNode.replaceChild(o(this.alt,!1),this)},parse:function(e,u){u&&"function"!=typeof u||(u={callback:u});return("string"==typeof e?p:l)(e,{callback:u.callback||a,attributes:"function"==typeof u.attributes?u.attributes:m,base:"string"==typeof u.base?u.base:d.base,ext:u.ext||d.ext,size:u.folder||(t=u.size||d.size,"number"==typeof t?t+"x"+t:t),className:u.className||d.className,onerror:u.onerror||d.onerror});var t},replace:b,test:function(d){u.lastIndex=0;var e=u.test(d);return u.lastIndex=0,e}},e={"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"
},u=/(?:\ud83d\udc68\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc68\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc68\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffe]|\ud83e\uddd1\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\u200d\ud83e\udd1d\u200d\ud83e\uddd1|\ud83d\udc6b\ud83c[\udffb-\udfff]|\ud83d\udc6c\ud83c[\udffb-\udfff]|\ud83d\udc6d\ud83c[\udffb-\udfff]|\ud83d[\udc6b-\udc6d])|(?:\ud83d[\udc68\udc69]|\ud83e\uddd1)(?:\ud83c[\udffb-\udfff])?\u200d(?:\u2695\ufe0f|\u2696\ufe0f|\u2708\ufe0f|\ud83c[\udf3e\udf73\udf7c\udf84\udf93\udfa4\udfa8\udfeb\udfed]|\ud83d[\udcbb\udcbc\udd27\udd2c\ude80\ude92]|\ud83e[\uddaf-\uddb3\uddbc\uddbd])|(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75]|\u26f9)((?:\ud83c[\udffb-\udfff]|\ufe0f)\u200d[\u2640\u2642]\ufe0f)|(?:\ud83c[\udfc3\udfc4\udfca]|\ud83d[\udc6e\udc70\udc71\udc73\udc77\udc81\udc82\udc86\udc87\ude45-\ude47\ude4b\ude4d\ude4e\udea3\udeb4-\udeb6]|\ud83e[\udd26\udd35\udd37-\udd39\udd3d\udd3e\uddb8\uddb9\uddcd-\uddcf\uddd6-\udddd])(?:\ud83c[\udffb-\udfff])?\u200d[\u2640\u2642]\ufe0f|(?:\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f|\ud83c\udff3\ufe0f\u200d\ud83c\udf08|\ud83c\udff4\u200d\u2620\ufe0f|\ud83d\udc15\u200d\ud83e\uddba|\ud83d\udc3b\u200d\u2744\ufe0f|\ud83d\udc41\u200d\ud83d\udde8|\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc6f\u200d\u2640\ufe0f|\ud83d\udc6f\u200d\u2642\ufe0f|\ud83e\udd3c\u200d\u2640\ufe0f|\ud83e\udd3c\u200d\u2642\ufe0f|\ud83e\uddde\u200d\u2640\ufe0f|\ud83e\uddde\u200d\u2642\ufe0f|\ud83e\udddf\u200d\u2640\ufe0f|\ud83e\udddf\u200d\u2642\ufe0f|\ud83d\udc08\u200d\u2b1b)|[#*0-9]\ufe0f?\u20e3|(?:[©®\u2122\u265f]\ufe0f)|(?:\ud83c[\udc04\udd70\udd71\udd7e\udd7f\ude02\ude1a\ude2f\ude37\udf21\udf24-\udf2c\udf36\udf7d\udf96\udf97\udf99-\udf9b\udf9e\udf9f\udfcd\udfce\udfd4-\udfdf\udff3\udff5\udff7]|\ud83d[\udc3f\udc41\udcfd\udd49\udd4a\udd6f\udd70\udd73\udd76-\udd79\udd87\udd8a-\udd8d\udda5\udda8\uddb1\uddb2\uddbc\uddc2-\uddc4\uddd1-\uddd3\udddc-\uddde\udde1\udde3\udde8\uddef\uddf3\uddfa\udecb\udecd-\udecf\udee0-\udee5\udee9\udef0\udef3]|[\u203c\u2049\u2139\u2194-\u2199\u21a9\u21aa\u231a\u231b\u2328\u23cf\u23ed-\u23ef\u23f1\u23f2\u23f8-\u23fa\u24c2\u25aa\u25ab\u25b6\u25c0\u25fb-\u25fe\u2600-\u2604\u260e\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262a\u262e\u262f\u2638-\u263a\u2640\u2642\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267b\u267f\u2692-\u2697\u2699\u269b\u269c\u26a0\u26a1\u26a7\u26aa\u26ab\u26b0\u26b1\u26bd\u26be\u26c4\u26c5\u26c8\u26cf\u26d1\u26d3\u26d4\u26e9\u26ea\u26f0-\u26f5\u26f8\u26fa\u26fd\u2702\u2708\u2709\u270f\u2712\u2714\u2716\u271d\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u2764\u27a1\u2934\u2935\u2b05-\u2b07\u2b1b\u2b1c\u2b50\u2b55\u3030\u303d\u3297\u3299])(?:\ufe0f|(?!\ufe0e))|(?:(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75\udd90]|[\u261d\u26f7\u26f9\u270c\u270d])(?:\ufe0f|(?!\ufe0e))|(?:\ud83c[\udf85\udfc2-\udfc4\udfc7\udfca]|\ud83d[\udc42\udc43\udc46-\udc50\udc66-\udc69\udc6e\udc70-\udc78\udc7c\udc81-\udc83\udc85-\udc87\udcaa\udd7a\udd95\udd96\ude45-\ude47\ude4b-\ude4f\udea3\udeb4-\udeb6\udec0\udecc]|\ud83e[\udd0c\udd0f\udd18-\udd1c\udd1e\udd1f\udd26\udd30-\udd39\udd3d\udd3e\udd77\uddb5\uddb6\uddb8\uddb9\uddbb\uddcd-\uddcf\uddd1-\udddd]|[\u270a\u270b]))(?:\ud83c[\udffb-\udfff])?|(?:\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc73\udb40\udc63\udb40\udc74\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc77\udb40\udc6c\udb40\udc73\udb40\udc7f|\ud83c\udde6\ud83c[\udde8-\uddec\uddee\uddf1\uddf2\uddf4\uddf6-\uddfa\uddfc\uddfd\uddff]|\ud83c\udde7\ud83c[\udde6\udde7\udde9-\uddef\uddf1-\uddf4\uddf6-\uddf9\uddfb\uddfc\uddfe\uddff]|\ud83c\udde8\ud83c[\udde6\udde8\udde9\uddeb-\uddee\uddf0-\uddf5\uddf7\uddfa-\uddff]|\ud83c\udde9\ud83c[\uddea\uddec\uddef\uddf0\uddf2\uddf4\uddff]|\ud83c\uddea\ud83c[\udde6\udde8\uddea\uddec\udded\uddf7-\uddfa]|\ud83c\uddeb\ud83c[\uddee-\uddf0\uddf2\uddf4\uddf7]|\ud83c\uddec\ud83c[\udde6\udde7\udde9-\uddee\uddf1-\uddf3\uddf5-\uddfa\uddfc\uddfe]|\ud83c\udded\ud83c[\uddf0\uddf2\uddf3\uddf7\uddf9\uddfa]|\ud83c\uddee\ud83c[\udde8-\uddea\uddf1-\uddf4\uddf6-\uddf9]|\ud83c\uddef\ud83c[\uddea\uddf2\uddf4\uddf5]|\ud83c\uddf0\ud83c[\uddea\uddec-\uddee\uddf2\uddf3\uddf5\uddf7\uddfc\uddfe\uddff]|\ud83c\uddf1\ud83c[\udde6-\udde8\uddee\uddf0\uddf7-\uddfb\uddfe]|\ud83c\uddf2\ud83c[\udde6\udde8-\udded\uddf0-\uddff]|\ud83c\uddf3\ud83c[\udde6\udde8\uddea-\uddec\uddee\uddf1\uddf4\uddf5\uddf7\uddfa\uddff]|\ud83c\uddf4\ud83c\uddf2|\ud83c\uddf5\ud83c[\udde6\uddea-\udded\uddf0-\uddf3\uddf7-\uddf9\uddfc\uddfe]|\ud83c\uddf6\ud83c\udde6|\ud83c\uddf7\ud83c[\uddea\uddf4\uddf8\uddfa\uddfc]|\ud83c\uddf8\ud83c[\udde6-\uddea\uddec-\uddf4\uddf7-\uddf9\uddfb\uddfd-\uddff]|\ud83c\uddf9\ud83c[\udde6\udde8\udde9\uddeb-\udded\uddef-\uddf4\uddf7\uddf9\uddfb\uddfc\uddff]|\ud83c\uddfa\ud83c[\udde6\uddec\uddf2\uddf3\uddf8\uddfe\uddff]|\ud83c\uddfb\ud83c[\udde6\udde8\uddea\uddec\uddee\uddf3\uddfa]|\ud83c\uddfc\ud83c[\uddeb\uddf8]|\ud83c\uddfd\ud83c\uddf0|\ud83c\uddfe\ud83c[\uddea\uddf9]|\ud83c\uddff\ud83c[\udde6\uddf2\uddfc]|\ud83c[\udccf\udd8e\udd91-\udd9a\udde6-\uddff\ude01\ude32-\ude36\ude38-\ude3a\ude50\ude51\udf00-\udf20\udf2d-\udf35\udf37-\udf7c\udf7e-\udf84\udf86-\udf93\udfa0-\udfc1\udfc5\udfc6\udfc8\udfc9\udfcf-\udfd3\udfe0-\udff0\udff4\udff8-\udfff]|\ud83d[\udc00-\udc3e\udc40\udc44\udc45\udc51-\udc65\udc6a\udc6f\udc79-\udc7b\udc7d-\udc80\udc84\udc88-\udca9\udcab-\udcfc\udcff-\udd3d\udd4b-\udd4e\udd50-\udd67\udda4\uddfb-\ude44\ude48-\ude4a\ude80-\udea2\udea4-\udeb3\udeb7-\udebf\udec1-\udec5\uded0-\uded2\uded5-\uded7\udeeb\udeec\udef4-\udefc\udfe0-\udfeb]|\ud83e[\udd0d\udd0e\udd10-\udd17\udd1d\udd20-\udd25\udd27-\udd2f\udd3a\udd3c\udd3f-\udd45\udd47-\udd76\udd78\udd7a-\uddb4\uddb7\uddba\uddbc-\uddcb\uddd0\uddde-\uddff\ude70-\ude74\ude78-\ude7a\ude80-\ude86\ude90-\udea8\udeb0-\udeb6\udec0-\udec2\uded0-\uded6]|[\u23e9-\u23ec\u23f0\u23f3\u267e\u26ce\u2705\u2728\u274c\u274e\u2753-\u2755\u2795-\u2797\u27b0\u27bf\ue50a])|\ufe0f/g,t=/\uFE0F/g,n=String.fromCharCode(8205),s=/[&<>'"]/g,c=/^(?:iframe|noframes|noscript|script|select|style|textarea)$/,r=String.fromCharCode
;return d;function o(d,e){return document.createTextNode(e?d.replace(t,""):d)}function a(d,e){return"".concat(e.base,e.size,"/",d,e.ext)}function i(d,e){for(var u,t,n=d.childNodes,s=n.length;s--;)3===(t=(u=n[s]).nodeType)?e.push(u):1!==t||"ownerSVGElement"in u||c.test(u.nodeName.toLowerCase())||i(u,e);return e}function f(d){return g(d.indexOf(n)<0?d.replace(t,""):d)}function l(d,e){for(var t,n,s,c,r,a,l,p,h,m,b,g,E,y=i(d,[]),v=y.length;v--;){for(s=!1,c=document.createDocumentFragment(),a=(r=y[v]).nodeValue,p=0;l=u.exec(a);){if((h=l.index)!==p&&c.appendChild(o(a.slice(p,h),!0)),g=f(b=l[0]),p=h+b.length,E=e.callback(g,e),g&&E){for(n in(m=new Image).onerror=e.onerror,m.setAttribute("draggable","false"),t=e.attributes(b,g))t.hasOwnProperty(n)&&0!==n.indexOf("on")&&!m.hasAttribute(n)&&m.setAttribute(n,t[n]);m.className=e.className,m.alt=b,m.src=E,s=!0,c.appendChild(m)}m||c.appendChild(o(b,!1)),m=null}s&&(p<a.length&&c.appendChild(o(a.slice(p),!0)),r.parentNode.replaceChild(c,r))}return d}function p(d,e){return b(d,(function(d){var u,t,n=d,c=f(d),r=e.callback(c,e);if(c&&r){for(t in n="<img ".concat('class="',e.className,'" ','draggable="false" ','alt="',d,'"',' src="',r,'"'),u=e.attributes(d,c))u.hasOwnProperty(t)&&0!==t.indexOf("on")&&-1===n.indexOf(" "+t+"=")&&(n=n.concat(" ",t,'="',u[t].replace(s,h),'"'));n=n.concat("/>")}return n}))}function h(d){return e[d]}function m(){return null}function b(d,e){return String(d).replace(u,e)}function g(d,e){for(var u=[],t=0,n=0,s=0;s<d.length;)t=d.charCodeAt(s++),n?(u.push((65536+(n-55296<<10)+(t-56320)).toString(16)),n=0):55296<=t&&t<=56319?n=t:u.push(t.toString(16));return u.join(e||"-")}}()}}]);