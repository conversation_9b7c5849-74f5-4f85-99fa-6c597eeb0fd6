(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6161],{316167:(t,e,o)=>{"use strict";o.d(e,{blendColors:()=>a,generateBlendColors:()=>n});var r=o(283873),i=o(154834),s=o(724377);const a=(t,e)=>(0,s.rgbaToString)((0,s.blendRgba)((0,s.parseRgba)(t),(0,s.parseRgba)(e)));function n(t,e){const o=(0,i.default)(t),s=Object.keys(o);for(const i of s){const s=t[i];(0,r.default)(s)?o[i]=a(s,e):o[i]=n(s,e)}return o}},734602:(t,e,o)=>{"use strict";o.d(e,{useGlobalCloseOnScrollAndCustomEvents:()=>s});var r=o(50959),i=o(370981);function s(t){(0,r.useEffect)((()=>{return t=a,window.addEventListener("scroll",t),()=>window.removeEventListener("scroll",t);var t}),[]),(0,r.useEffect)((()=>{if(t.length)return function(t,e){for(const o of t)o.subscribe(null,e);return()=>{for(const o of t)o.unsubscribe(null,e)}}(t,a)}),t)}function a(){(0,i.globalCloseMenu)()}},914090:(t,e,o)=>{"use strict";o.d(e,{TooltipPopup:()=>l});var r=o(50959),i=o(624216),s=o(370981),a=o(180185),n=o(41899);const l=(0,r.forwardRef)(((t,e)=>{const{onClose:o,onForceClose:l,onClickOutside:d,className:c,...h}=t,u=(0,r.useRef)(null);(0,r.useEffect)((()=>(s.globalCloseDelegate.subscribe(null,l),()=>{s.globalCloseDelegate.unsubscribe(null,l)})),[l]);const p=(0,r.useCallback)((e=>{t.onKeyDown?.(e),27===(0,a.hashFromEvent)(e)&&(e.preventDefault(),l())}),[l]),_=(0,r.useCallback)((()=>{u.current&&u.current.focus({preventScroll:!0})}),[]);return r.createElement(i.PopupMenu,{className:c,isOpened:!0,tabIndex:-1,reference:t=>{"function"==typeof e?e(t):(0,n.isObject)(e)&&(e.current=t),u.current=t},onClose:o,onClickOutside:d,onKeyDown:p,onOpen:_,...h},t.children)}));l.displayName="TooltipPopup"},155973:t=>{t.exports={title:"title-u3QJgF_p"}},722426:(t,e,o)=>{"use strict";o.d(e,{ToolWidgetMenuSummary:()=>a});var r=o(50959),i=o(497754),s=o(155973);function a(t){return r.createElement("div",{className:i(t.className,s.title)},t.children)}},796160:(t,e,o)=>{"use strict";var r;o.d(e,{TradedItemType:()=>r}),function(t){t[t.Position=0]="Position",t[t.Order=1]="Order"}(r||(r={}))},376161:(t,e,o)=>{"use strict";o.d(e,{TradedGroupBase:()=>Ke,isTradedGroupCustomSource:()=>je});var r=o(154834),i=o(650151),s=o(609838),a=o(114658),n=o(934135),l=o(599016),d=o(46415),c=o(796160),h=o(482303),u=o(431635),p=o(265328),_=o(333433),y=o(813108);const m=y.LineStyle.Solid,f=y.LineStyle.Dashed;var b,C,v,g=o(87502),P=o(980465),k=o(799839),T=o(885800),x=o(184114),M=o(898646),w=o(912321),S=o(463940),B=o(260449),I=o(86441),D=o(799573),L=o(374410),O=o(805033);function R(t){return t.hasOwnProperty("tp")&&t.hasOwnProperty("sl")}!function(t){t[t.Hidden=0]="Hidden",t[t.Visible=1]="Visible"}(b||(b={})),function(t){t[t.TPSLHorizontalMarin=10]="TPSLHorizontalMarin",t[t.ConfirmHorizontalMarin=11]="ConfirmHorizontalMarin"}(C||(C={}));class W extends B.ItemRenderer{constructor(){super(...arguments),this._wasTPMoved=!1,this._wasSLMoved=!1}hitTest(t,e){
const{horizontalPixelRatio:o,verticalPixelRatio:r}=e,s=this.rectWithOffsets(e),a=Math.round(t.x*o),n=Math.round(t.y*r),l=a>=s.left,d=a<=s.right,c=l&&d,h=n>=s.top&&n<=s.bottom,u={cursorType:L.PaneCursorType.Default,hideCrosshairLinesOnHover:!0,...(0,B.activeItemToHitTestResultData)({id:this._data.id,part:9}),contextMenuHandler:this._data.callbacks.onContextMenu,touchContextMenuHandler:this._data.callbacks.onContextMenu};if(this._data.disabled)return new x.HitTestResult(x.HitTarget.Custom,u);if(this._isAnchorIntersected(h,a,s))return super.hitTest(t,e);if(this._isPriceLineIntersected(a,n,e))return super.hitTest(t,e);if(!h||!c)return null;const p=Math.round(s.top/r),_=void 0!==this._data.callbacks.onMoveProjectionTP&&1===this._data.tp?.visibility;if(_&&a>=s.tp.left&&a<s.tp.left+s.tp.width){const t=Math.round(s.tp.left/o),e=Math.round(s.tp.width/o),r=t=>{this._data.callbacks.onMoveProjectionTP&&this._data.callbacks.onMoveProjectionTP(t),this._wasTPMoved=!0},a=()=>{this._wasTPMoved&&this._data.callbacks.onFinishMoveTP&&this._data.callbacks.onFinishMoveTP(),this._wasTPMoved=!1};return new x.HitTestResult(x.HitTarget.Custom,{...u,cursorType:L.PaneCursorType.Pointer,...(0,B.activeItemToHitTestResultData)({id:this._data.id,part:5}),pressedMouseMoveHandler:r,touchMoveHandler:r,mouseUpHandler:a,touchEndHandler:a,cancelMoveHandler:this._data.callbacks.onCancelMove,tooltip:{extendMargin:!0,text:(0,i.ensureDefined)(this._data.tp).title,rect:{x:t,y:p,w:e,h:this._height}}})}const y=s.sl.left+s.sl.width,m=void 0!==this._data.callbacks.onMoveProjectionSL&&1===this._data.sl?.visibility;if(m&&a>=s.sl.left&&a<y){const t=Math.round(s.sl.width/o),e=Math.round(s.sl.left/o),a=Math.round(s.bottom/r),n=t=>{this._data.callbacks.onMoveProjectionSL&&this._data.callbacks.onMoveProjectionSL(t),this._wasSLMoved=!0},l=()=>{this._wasSLMoved&&this._data.callbacks.onFinishMoveSL&&this._data.callbacks.onFinishMoveSL(),this._wasSLMoved=!1};let d,c;const h=this._data.callbacks.onClickSL;if(void 0!==h){const o=o=>{const r=o.clientX-o.localX,i=o.clientY-o.localY,s=new I.Point(e+r,a+i);h((o=>o.x>e&&o.x<=e+t&&o.y>p&&o.y<=a),s)};d=o,c=t=>{t.preventDefault(),o(t)}}return new x.HitTestResult(x.HitTarget.Custom,{...u,cursorType:h?L.PaneCursorType.Default:L.PaneCursorType.Pointer,...(0,B.activeItemToHitTestResultData)({id:this._data.id,part:6}),pressedMouseMoveHandler:n,touchMoveHandler:n,mouseUpHandler:l,touchEndHandler:l,clickHandler:d,tapHandler:c,cancelMoveHandler:this._data.callbacks.onCancelMove,tooltip:{extendMargin:!0,text:(0,i.ensureDefined)(this._data.sl).title,rect:{x:e,y:p,w:t,h:this._height}}})}const f=y-s.body.left<0?a>=y&&a<s.body.left:a>=s.body.right&&a<s.tp.left;if((_||m)&&f)return new x.HitTestResult(x.HitTarget.Custom,u);if(this._hasConfirmButton()&&(0,i.ensureDefined)(this._data.confirm)){const t=s.confirm.left+s.confirm.width;if(a>=s.confirm.left&&a<t){let t={};if(void 0!==this._data.callbacks.onMove){const e=t=>{this._data.callbacks.onMove&&this._data.callbacks.onMove(t),this._wasSourceMoved=!0},o=()=>{
this._wasSourceMoved&&this._data.callbacks.onFinishMove&&this._data.callbacks.onFinishMove(),this._wasSourceMoved=!1};t={pressedMouseMoveHandler:e,touchMoveHandler:e,mouseUpHandler:o,touchEndHandler:o,cancelMoveHandler:this._data.callbacks.onCancelMove}}return new x.HitTestResult(x.HitTarget.Custom,{...u,...t,...(0,B.activeItemToHitTestResultData)({id:this._data.id,part:7}),cursorType:L.PaneCursorType.Pointer,clickHandler:this._data.callbacks.onConfirm,tapHandler:this._data.callbacks.onConfirm})}const o=this._calcConfirmRect(e,s.borderWidth).offset;if(t-s.body.left<0?a>=t&&a<s.tp.left:a>=s.confirm.left-o&&a<s.confirm.left)return new x.HitTestResult(x.HitTarget.Custom,u)}return void 0!==this.data().informer&&a>s.informer.left&&a<s.informer.left+s.informer.width?new x.HitTestResult(x.HitTarget.Custom,{...u,cursorType:L.PaneCursorType.Default,...(0,B.activeItemToHitTestResultData)({id:this._data.id,part:9}),touchContextMenuHandler:()=>{},tooltip:{extendMargin:!0,text:(0,i.ensureDefined)(this._data.informer).title,rect:{x:s.informer.left/o,y:p,w:s.informer.width/o,h:this._height},tooltipHideDelay:D.mobiletouch?1500:0}}):super.hitTest(t,e)}draw(t,e){if(super.draw(t,e),!this._isDataVisibleInViewport(e)||!this._data.visible)return;const o=this.rectWithOffsets(e),r=o.bottom-o.top,s=o.yMid,{horizontalPixelRatio:a,verticalPixelRatio:n}=e;void 0!==this._data.informer&&(0,O.drawExclamationIcon)({ctx:t,x:o.informer.left,y:o.yMid-9*n,horizontalPixelRatio:a,verticalPixelRatio:n}),this._isTPSLVisible()&&(0!==o.tp.width&&1===(0,i.ensureDefined)(this._data.tp).visibility&&this._drawButton(t,e,r,o.borderRadius,s,o.tp,this._data.tp),0!==o.sl.width&&1===(0,i.ensureDefined)(this._data.sl).visibility&&this._drawButton(t,e,r,o.borderRadius,s,o.sl,this._data.sl)),this._hasConfirmButton()&&this._drawButton(t,e,r,o.borderRadius,s,o.confirm,this._data.confirm)}rect(t){return super.rect(t)}rectWithOffsets(t){return super.rectWithOffsets(t)}_calculateCacheRectWithOffsets(t){let e=super._calculateCacheRectWithOffsets(t);return e={...e,tp:{...e.tp,left:this._calcCoordinateWithOffset(e.tp.left,e.tp.left+e.tp.width).left},sl:{...e.sl,left:this._calcCoordinateWithOffset(e.sl.left,e.sl.left+e.sl.width).left}},void 0!==e.confirm&&(e.confirm={...e.confirm,left:this._calcCoordinateWithOffset(e.confirm.left,e.confirm.left+e.confirm.width).left}),e}_calculateCacheRect(t){const e=super._calculateCacheRect(t),{horizontalPixelRatio:o}=t,r=this._calcTPSLRect(t,e.borderWidth),i=this._calcConfirmRect(t,e.borderWidth);let s=0,a=0;const n=this._data.bodyAlignment===w.TradedGroupHorizontalAlignment.Left;n?(a=e.body.right+r.offset,s=a+r.tp-(r.tp>0?e.borderWidth:0)):(s=e.body.left-r.offset-r.sl,a=s-(r.tp>0?r.tp-e.borderWidth:0));let l=0;i.confirm>0&&(l=n?s+r.sl+i.offset:a-i.confirm-i.offset-e.borderWidth);let d=l||a||s||e.qty.left,c=e.right;return n&&(d=e.left,c=l>0?l+i.confirm:s+r.sl),n||void 0===this.data().informer||(d=d-e.informer.width-5*o,e.informer={width:e?.informer.width,left:d}),{...e,left:d,right:c,tp:{width:r.tp,left:a},sl:{width:r.sl,left:s},confirm:{
width:i.confirm,left:l}}}_drawLine(t,e){if(!this._data.callbacks.onFinishMoveSL&&!this._data.callbacks.onFinishMoveTP)return void super._drawLine(t,e);const o=this.rectWithOffsets(e),r=e.bitmapSize.width;if(t.save(),t.strokeStyle=this._data.line.color,t.lineWidth=Math.max(1,Math.floor(this._data.line.thickness*e.horizontalPixelRatio)),(0,M.setLineStyle)(t,this._data.line.style),(0,M.drawHorizontalLine)(t,o.yMid,o.body.right,r),this._data.line.extend)(0,M.drawHorizontalLine)(t,o.yMid,0,o.body.left);else{const e=1===this._data.tp?.visibility,r=1===this._data.sl?.visibility;if(e||r){const e=r?o.sl.left+o.sl.width:o.tp.left+o.tp.width;(0,M.drawHorizontalLine)(t,o.yMid,e,o.body.left)}if(this._hasConfirmButton()){const e=o.confirm.left+o.confirm.width;(0,M.drawHorizontalLine)(t,o.yMid,e,o.body.left)}}void 0!==this._data.informer&&(0,O.drawErrorNotificationBackdrop)({ctx:t,x:0,y:o.yMid,w:r,h:20,horizontalPixelRatio:e.horizontalPixelRatio,verticalPixelRatio:e.verticalPixelRatio}),t.restore()}_calcAllWidth(t,e,o,r,i,s){const a=super._calcAllWidth(t,e,o,r,i,s);if(!this._data.tp&&!this._data.sl&&!this._hasConfirmButton())return a;const n=this._calcTPSLRect(t,s),l=this._calcConfirmRect(t,s);return a+n.tp+n.sl+n.offset+l.confirm+l.offset}_calcConfirmRect(t,e){if(!this._hasConfirmButton())return{confirm:0,offset:0};const{horizontalPixelRatio:o}=t,r=2*e,s=Math.round(11*o),a=this._textWidth(this._ctxInternal,(0,i.ensureDefined)(this._data.confirm).text);return{confirm:Math.round(a*o)+(a&&r)+(a&&s),offset:Math.round(10*o)}}_calcTPSLRect(t,e){const{horizontalPixelRatio:o}=t,r=2*e,i=this._textWidth(this._ctxInternal,void 0!==this._data.tp?this._data.tp.text:""),s=Math.round(i*o)+(i&&r),a=this._textWidth(this._ctxInternal,void 0!==this._data.sl?this._data.sl.text:""),n=Math.round(a*o)+(a&&r);return{tp:s,sl:n,offset:n>0||s>0?Math.round(10*o):0}}_drawButton(t,e,o,r,i,s,a){if(void 0===a)return;const n=this.rectWithOffsets(e);(0,M.drawRoundRectWithInnerBorder)(t,s.left,n.top,s.width,o,a.backgroundColor,r),a.active.visible&&(0,M.drawRoundRectWithInnerBorder)(t,s.left,n.top,s.width,o,a.active.backgroundColor,r);const l=a.active.visible&&void 0!==a.active.borderColor?a.active.borderColor:a.borderColor;(0,M.drawRoundRectWithInnerBorder)(t,s.left,n.top,s.width,o,"transparent",r,n.borderWidth,l,a.borderStyle);const d=s.left+s.width/2;a.underlineText?this._drawUnderlinedText(t,e,a.text,a.textColor,d,i):this._drawText(t,e,a.text,a.textColor,d,i)}_isTPSLVisible(){return Boolean(this._data.tp)||Boolean(this._data.sl)}_hasConfirmButton(){return Boolean(this._data.callbacks.onConfirm&&this._data.confirm)}}class H extends B.ItemRenderer{constructor(){super(...arguments),this._left=0}hitTest(t,e){const o=this.rectWithOffsets(e),r=Math.round(t.x*e.horizontalPixelRatio),i=Math.round(t.y*e.verticalPixelRatio),s=r>=o.left,a=r<=o.right,n=s&&a;if(!(i>=o.top&&i<=o.bottom)||!n)return null;const l={cursorType:L.PaneCursorType.Default,hideCrosshairLinesOnHover:!0,...(0,B.activeItemToHitTestResultData)({id:this._data.id})}
;return this._data.disabled?new x.HitTestResult(x.HitTarget.Custom,l):new x.HitTestResult(x.HitTarget.Custom,{...l,...(0,B.activeItemToHitTestResultData)({id:this._data.id,part:9})})}applyOffset(t){const e=(0,i.ensureDefined)(this._data.drawWithTPOffset?t.projItemTpLeft:this._data.drawWithSLOffset?t.projItemSlLeft:0);this._left!==e&&(this._left=e,this.clearCache()),this._offsets=t}_calculateCacheRect(t){const{horizontalPixelRatio:e}=t;let o=super._calculateCacheRect(t);const r=2*o.borderWidth,i=this._mainTextWidth(this._ctxInternal),s=this._quantityWidth(this._ctxInternal),a=Math.round(i*e+s*e)+(i&&r)+(s&&o.borderWidth),n=this._left+a,l=o.informer.width,d=5*e;if(o={...o,left:this._left,right:n,body:{...o.body,left:this._left,right:n},text:{left:this._left+o.qty.width+o.borderWidth,width:o.text.width,rightDividerWidth:0},qty:{left:this._left+o.borderWidth,width:o.qty.width-o.borderWidth,rightDividerWidth:o.qty.rightDividerWidth},informer:{width:l,left:this._left-d-l}},this._data.bodyAlignment!==w.TradedGroupHorizontalAlignment.Left)return o;const c=o.body.left-o.text.width-Number(o.qty.rightDividerWidth);return{...o,top:o.top,bottom:o.bottom,body:{left:o.body.left-o.text.width-Number(o.qty.rightDividerWidth)-o.borderWidth,right:o.qty.left+o.qty.width+o.borderWidth},text:{left:c,width:o.text.width+Number(o.qty.rightDividerWidth),rightDividerWidth:o.qty.rightDividerWidth},qty:{left:o.qty.left,width:o.qty.width-Number(o.qty.rightDividerWidth),rightDividerWidth:0},informer:{width:l,left:c-d-l}}}_calcAllWidth(t,e,o,r,i,s){const a=2*s;return Math.round(o*t.horizontalPixelRatio)+(o&&a)}}!function(t){t[t.SpacingBetweenReverseLines=3]="SpacingBetweenReverseLines",t[t.ArrowHeight=9]="ArrowHeight",t[t.ArrowWidth=3]="ArrowWidth",t[t.ReverseButtonOffset=10]="ReverseButtonOffset",t[t.ArrowHorizontalPadding=9]="ArrowHorizontalPadding",t[t.BorderRadius=4]="BorderRadius"}(v||(v={}));class A extends W{constructor(){super(...arguments),this._bodyBorderRadius=4}hitTest(t,e){const{horizontalPixelRatio:o,verticalPixelRatio:r}=e,s=this.rectWithOffsets(e),a=Math.round(t.x*o),n=Math.round(t.y*r),l=a>=s.left,d=a<=s.right,c=l&&d,h=n>=s.top&&n<=s.bottom,u=Math.round(s.top/r);if(!h||!c)return null;const p={cursorType:L.PaneCursorType.Default,hideCrosshairLinesOnHover:!0,...(0,B.activeItemToHitTestResultData)({id:this._data.id,part:9})};if(this._data.disabled)return new x.HitTestResult(x.HitTarget.Custom,p);const _=this._hasReverseButton()&&(0,i.ensureDefined)(this._data.reverse);if(_&&1===_.visibility){const t=s.reverse.left+s.reverse.width;if(a>=s.reverse.left&&a<t){const t=Math.round(s.reverse.left/o),e=Math.round(s.reverse.width/o);return new x.HitTestResult(x.HitTarget.Custom,{...(0,B.activeItemToHitTestResultData)({id:this._data.id,part:4}),cursorType:L.PaneCursorType.Pointer,hideCrosshairLinesOnHover:!0,contextMenuHandler:this._data.callbacks.onContextMenu,touchContextMenuHandler:this._data.callbacks.onContextMenu,clickHandler:this._data.callbacks.onReverse,tapHandler:this._data.callbacks.onReverse,tooltip:{text:_.title,rect:{x:t,y:u,w:e,
h:this._height}}})}const r=this._calcReverseRect(e).offset;if(t-s.body.left<0?a>=t&&a<s.tp.left:a>=s.reverse.left-r&&a<s.reverse.left)return new x.HitTestResult(x.HitTarget.Custom,p)}return super.hitTest(t,e)}draw(t,e){if(super.draw(t,e),!this._isReverseVisible()||!this._isDataVisibleInViewport(e)||!this._data.visible)return;const o=this.rectWithOffsets(e);this._drawReverseButton(t,e,o.reverse.left,o.top,o.reverse.width,o.bottom-o.top,o.borderWidth,o.borderRadius)}rect(t){return super.rect(t)}rectWithOffsets(t){return super.rectWithOffsets(t)}_calculateCacheRectWithOffsets(t){const e=super._calculateCacheRectWithOffsets(t);return{...e,reverse:{...e.reverse,left:this._calcCoordinateWithOffset(e.reverse.left,e.reverse.left+e.reverse.width).left}}}_calculateCacheRect(t){const e=super._calculateCacheRect(t);if(!this._data?.callbacks.onReverse||!this._hasReverseButton())return{...e,reverse:{width:0,left:0}};const o=this._calcReverseRect(t),r=this._data.bodyAlignment===w.TradedGroupHorizontalAlignment.Left,i=r?e.right+o.offset:e.left-o.width-o.offset,s=r?e.left:i,a=r?i+o.width:e.right;return{...e,left:s,right:a,reverse:{width:o.width,left:i}}}_calcAllWidth(t,e,o,r,i,s){const a=super._calcAllWidth(t,e,o,r,i,s);if(!this._data?.callbacks.onReverse||!this._hasReverseButton())return a;const n=this._calcReverseRect(t);return a+n.width+n.offset}_drawLine(t,e){if(!this._data?.callbacks.onReverse)return void super._drawLine(t,e);const{verticalPixelRatio:o}=e,r=this.rectWithOffsets(e),i=e.bitmapSize.width,s=r.reverse.left+r.reverse.width;if(t.save(),t.strokeStyle=this._data.line.color,t.lineWidth=Math.max(1,Math.floor(this._data.line.thickness*o)),(0,M.setLineStyle)(t,this._data.line.style),(0,M.drawHorizontalLine)(t,r.yMid,r.body.right,i),this._isReverseVisible()&&(0,M.drawHorizontalLine)(t,r.yMid,s,r.body.left),this._data.line.extend){const e=this._isReverseVisible()?r.reverse.left:r.body.left;(0,M.drawHorizontalLine)(t,r.yMid,0,e)}t.restore()}_calcReverseRect(t){return{width:Math.round(29*t.horizontalPixelRatio),offset:Math.round(10*t.horizontalPixelRatio)}}_drawReverseButton(t,e,o,r,s,a,n,l){const d=(0,i.ensureDefined)(this._data.reverse),c=d.active;t.save(),(0,M.drawRoundRectWithInnerBorder)(t,o,r,s,a,d.backgroundColor,l,n,this._data.borderColor),c.visible&&(0,M.drawRoundRectWithInnerBorder)(t,o,r,s,a,c.backgroundColor,l,n,"transparent");const{horizontalPixelRatio:h,verticalPixelRatio:u}=e,p=Math.round(3*h),_=Math.round(3*h),y=Math.round(9*u),m=Math.round(o+(s-p)/2),f=Math.round(m+p),b=r+n+Math.round((a-y)/2-n),C=b+y,v=d.iconColor;t.lineWidth=n,(0,O.drawHalfArrow)(t,m,b,v,!1,y,_),(0,O.drawHalfArrow)(t,f,C,v,!0,y,_),t.restore()}_isReverseVisible(){return this._hasReverseButton()&&1===(0,i.ensureDefined)(this._data.reverse).visibility}_hasReverseButton(){return Boolean(this._data.callbacks.onReverse&&this._data.reverse)}}var V,G;!function(t){t[t.MinOffsetForVisibleLine=12]="MinOffsetForVisibleLine"}(V||(V={})),function(t){t.FontWeight="normal"}(G||(G={}));class N{constructor(t,e){this._textWidthCache=new S.TextWidthCache,
this._items=[],this._minMainTextWidthCache=new Map,this._itemsRectsInfoCache=null,this._font=(0,T.makeFont)(t,k.CHART_FONT_FAMILY,"","normal"),this._tradedGroupRenderersController=e}itemRenderers(){return this._items}clearCache(){this._itemsRectsInfoCache=null,this._items.forEach((t=>t.clearCache()))}clearTextCache(){this._textWidthCache.reset()}setData(t){this._data=t}clearItems(){this._items=[]}addItem(t){const e=function(t,e,o,r){if(t.type===B.ItemsRendererType.Position)return new A(t,e,o,r);if(t.type===B.ItemsRendererType.WithBracketButtons)return new W(t,e,o,r);if(t.type===B.ItemsRendererType.Projection)return new H(t,e,o,r);if(t.type===B.ItemsRendererType.Default)return new B.ItemRenderer(t,e,o,r);throw new Error("Unknown traded item renderer's type")}(t,this._textWidthCache,(0,i.ensureNotNull)(this._font),this._minTextWidth.bind(this));this._items.push(e)}applyHorizontalOffset(t,e){if(null!==this._itemsRectsInfoCache)return;const o=this._items.filter((t=>t.data().visible)).map((t=>t.rect(e)));if(0===o.length)return;let r,i,s=1/0,a=-1/0;for(const t of o){s=Math.min(s,t.yMid),a=Math.max(a,t.yMid);R(t)&&(r=t.tp.left,i=t.sl.left)}const{bitmapSize:n,horizontalPixelRatio:l}=e;let d,c,h=t.rightmostBorder,u=t.leftmostBorder;const p=h-u;if(this._data.horizontalAlignment===w.TradedGroupHorizontalAlignment.Left)h>n.width&&(c=Math.max(n.width-p,Math.round(12*l)));else if(u<0){const t=Math.round(12*l);d=n.width-p>t?p:n.width-t}if(void 0!==r&&void 0!==i){if(void 0!==c){const t=u-c;r-=t,i-=t}if(void 0!==d){const t=d-h;r+=t,i+=t}}void 0!==c&&(u=c),void 0!==d&&(h=d),this._itemsRectsInfoCache={rightmostBorder:h,leftmostBorder:u,leftmostForItemOffset:c,rightmostForItemOffset:d,yMin:s,yMax:a,projItemTpLeft:r,projItemSlLeft:i};for(const t of this._items)t.applyOffset(this._itemsRectsInfoCache)}hitTest(t,e){if(!this._paneHasVisibleItems())return null;this._tradedGroupRenderersController.alignItems(e);for(let o=this._items.length-1;o>=0;o--){const r=this._items[o].hitTest(t,e);if(null!==r)return r}return this._data.disableTouchInteraction(),this._data?.disableNoOverlap?new x.HitTestResult(x.HitTarget.MovePointBackground,{clickHandler:this._data.disableNoOverlap,tapHandler:this._data.disableNoOverlap,contextMenuHandler:this._data.disableNoOverlap,touchContextMenuHandler:this._data.disableNoOverlap}):null}drawBackground(t,e){if(this._paneHasVisibleItems()){this._tradedGroupRenderersController.alignItems(e);for(const o of this._items)o.drawBackground(t,e)}}draw(t,e){if(!this._paneHasVisibleItems())return;this._tradedGroupRenderersController.alignItems(e);for(const o of this._items)o.drawLine(t,e);const{bitmapSize:o,horizontalPixelRatio:r}=e,i=this._itemsRectsInfoCache?.rightmostBorder??null,s=this._itemsRectsInfoCache?.leftmostBorder??null,a=Math.round(12*r),n=this._data.horizontalAlignment===w.TradedGroupHorizontalAlignment.Left,l=n?s:i,d=n?-1:1;if(null!==l&&l+d*a<=o.width){const i=n?0:o.width,s=Math.round(this._data.rightPadding*r),a=l+d*(Math.min(s,i-d*l)/2);this._drawVertLine(t,e,a)
;for(const o of this._items)o.drawPointOnLine(t,e,a)}for(const o of this._items)o.draw(t,e)}clearMinTextWidthByItemId(t){this._minMainTextWidthCache.has(t)&&this._minMainTextWidthCache.delete(t)}clearMinTextWidthCache(){this._minMainTextWidthCache.clear()}_minTextWidth(t,e){if(!t.text.text)return 0;e.save(),e.font=(0,i.ensureNotNull)(this._font);const o=Math.ceil(this._textWidthCache.measureText(e,t.text.text)),r=this._minMainTextWidthCache.get(t.id);if(void 0===r||o>r){const r=Math.ceil(this._textWidthCache.measureText(e,"0"));this._minMainTextWidthCache.set(t.id,o+r)}return e.restore(),this._minMainTextWidthCache.get(t.id)??0}_drawVertLine(t,e,o){const r=this._data.vertLine;null!==r&&null!==this._itemsRectsInfoCache&&this._itemsRectsInfoCache.yMax!==this._itemsRectsInfoCache.yMin&&(t.strokeStyle=r.color,t.lineWidth=Math.max(1,Math.floor(r.thickness*e.horizontalPixelRatio)),(0,M.setLineStyle)(t,r.style),(0,M.drawVerticalLine)(t,o,this._itemsRectsInfoCache.yMin,this._itemsRectsInfoCache.yMax))}_paneHasVisibleItems(){return this._items.filter((t=>t.data().visible)).length>0}}var E,q=o(669874),F=o(431520),z=o(520985),j=o(112232),Q=o(926048),$=o(589637),U=o(316167),Y=o(52102);!function(t){t[t.PointShadowTransparency=80]="PointShadowTransparency",t[t.OverlayTransparency=50]="OverlayTransparency",t[t.ActiveColorTransparency=85]="ActiveColorTransparency"}(E||(E={}));const X=Q.colorsPalette["color-white"],K=X,J=Q.colorsPalette["color-tv-blue-50"],Z=Q.colorsPalette["color-tv-blue-100"],tt=Q.colorsPalette["color-tv-blue-200"],et=Q.colorsPalette["color-tv-blue-500"],ot=Q.colorsPalette["color-tv-blue-600"],rt=Q.colorsPalette["color-cold-gray-150"],it=Q.colorsPalette["color-cold-gray-300"],st=Q.colorsPalette["color-ripe-red-50"],at=Q.colorsPalette["color-ripe-red-100"],nt=Q.colorsPalette["color-ripe-red-100"],lt=Q.colorsPalette["color-ripe-red-500"],dt=Q.colorsPalette["color-ripe-red-600"],ct=Q.colorsPalette["color-minty-green-50"],ht=Q.colorsPalette["color-minty-green-100"],ut=Q.colorsPalette["color-minty-green-500"],pt=Q.colorsPalette["color-tan-orange-50"],_t=Q.colorsPalette["color-tan-orange-200"],yt=Q.colorsPalette["color-tan-orange-500"],mt=(0,$.generateColor)(X,50),ft=(0,U.blendColors)(yt,mt),bt=(0,U.blendColors)(ut,mt),Ct=(0,U.blendColors)(lt,mt),vt=(0,U.blendColors)(et,mt),gt=(0,$.generateColor)(tt,85),Pt=(0,$.generateColor)(nt,85),kt=(0,$.generateColor)(ht,85),Tt=(0,$.generateColor)(_t,85),xt=(0,$.generateColor)(tt,80),Mt=(0,$.generateColor)(nt,80),wt=(0,$.generateColor)(ht,80),St=(0,$.generateColor)(_t,80),Bt=(0,$.generateColor)(et,80),It={pointShadowColor:xt,labelTickColor:tt,lineColor:tt,borderBackgroundColor:X,borderColor:tt,pointBackgroundColor:X,disabledLineColor:Z,positivePlColor:bt,negativePlColor:Ct,qty:{backgroundColor:X,textColor:vt,dividerColor:J,activeColor:gt},text:{backgroundColor:X,textColor:vt,dividerColor:J,buttonTextColor:vt,activeColor:gt},close:{backgroundColor:X,iconColor:vt,activeColor:gt},confirm:{disableBackgroundColor:rt,backgroundColor:et,borderColor:et,textColor:K,disableTextColor:it,
activeColor:ot},informer:{backgroundColor:Y.errorNotificationBackdrop,iconColor:lt},anchor:{backgroundColor:X,borderColor:et,shadowColor:Bt}},Dt={pointShadowColor:Mt,labelTickColor:nt,lineColor:nt,borderBackgroundColor:X,borderColor:nt,pointBackgroundColor:X,disabledLineColor:at,positivePlColor:bt,negativePlColor:Ct,qty:{backgroundColor:X,textColor:Ct,dividerColor:st,activeColor:Pt},text:{backgroundColor:X,textColor:Ct,dividerColor:st,buttonTextColor:Ct,activeColor:Pt},close:{backgroundColor:X,iconColor:Ct,activeColor:Pt},confirm:{disableBackgroundColor:rt,backgroundColor:lt,borderColor:lt,textColor:K,disableTextColor:it,activeColor:dt},informer:{backgroundColor:Y.errorNotificationBackdrop,iconColor:lt},anchor:{backgroundColor:X,borderColor:et,shadowColor:Bt}},Lt={pointShadowColor:wt,labelTickColor:ht,lineColor:ht,borderBackgroundColor:X,borderColor:ht,pointBackgroundColor:X,positivePlColor:bt,negativePlColor:Ct,qty:{backgroundColor:X,textColor:bt,dividerColor:ct,activeColor:kt},text:{backgroundColor:X,textColor:bt,dividerColor:ct,buttonTextColor:bt,activeColor:kt},close:{backgroundColor:X,iconColor:bt,activeColor:kt},informer:{backgroundColor:Y.errorNotificationBackdrop,iconColor:lt},anchor:{backgroundColor:X,borderColor:et,shadowColor:Bt}},Ot={pointShadowColor:St,labelTickColor:_t,lineColor:_t,borderBackgroundColor:X,borderColor:_t,pointBackgroundColor:X,positivePlColor:bt,negativePlColor:Ct,qty:{backgroundColor:X,textColor:ft,dividerColor:pt,activeColor:Tt},text:{backgroundColor:X,textColor:ft,dividerColor:pt,buttonTextColor:ft,activeColor:Tt},close:{backgroundColor:X,iconColor:ft,activeColor:Tt},informer:{backgroundColor:Y.errorNotificationBackdrop,iconColor:lt},anchor:{backgroundColor:X,borderColor:et,shadowColor:Bt}},Rt={buy:{normal:It,disabled:(0,U.generateBlendColors)(It,mt)},sell:{normal:Dt,disabled:(0,U.generateBlendColors)(Dt,mt)},takeProfit:{normal:Lt,disabled:(0,U.generateBlendColors)(Lt,mt)},stopLoss:{normal:Ot,disabled:(0,U.generateBlendColors)(Ot,mt)}};var Wt;!function(t){t[t.ActiveColorTransparency=85]="ActiveColorTransparency",t[t.IconDisabledTransparency=80]="IconDisabledTransparency",t[t.PointShadowTransparency=70]="PointShadowTransparency",t[t.OverlayTransparency=50]="OverlayTransparency"}(Wt||(Wt={}));const Ht=Q.colorsPalette["color-white"],At=Q.colorsPalette["color-tv-blue-a800"],Vt=Q.colorsPalette["color-tv-blue-a700"],Gt=Q.colorsPalette["color-tv-blue-a700"],Nt=Q.colorsPalette["color-tv-blue-500"],Et=Q.colorsPalette["color-tv-blue-600"],qt=Q.colorsPalette["color-cold-gray-600"],Ft=Q.colorsPalette["color-cold-gray-750"],zt=Q.colorsPalette["color-cold-gray-900"],jt=Q.colorsPalette["color-ripe-red-500"],Qt=Q.colorsPalette["color-ripe-red-600"],$t=Q.colorsPalette["color-ripe-red-a800"],Ut=Q.colorsPalette["color-ripe-red-a800"],Yt=Q.colorsPalette["color-ripe-red-a800"],Xt=Q.colorsPalette["color-minty-green-a800"],Kt=Q.colorsPalette["color-minty-green-500"],Jt=Q.colorsPalette["color-tan-orange-a800"],Zt=Q.colorsPalette["color-tan-orange-500"],te="#0D3D41",ee="#453826",oe=(0,
$.generateColor)(zt,50),re=(0,U.blendColors)(Kt,oe),ie=(0,U.blendColors)(jt,oe),se=(0,U.blendColors)(Nt,oe),ae=(0,U.blendColors)(Zt,oe),ne=(0,$.generateColor)(Vt,85),le=((0,$.generateColor)(Vt,80),(0,$.generateColor)($t,85)),de=((0,$.generateColor)($t,80),(0,$.generateColor)(Xt,85)),ce=((0,$.generateColor)(Xt,80),(0,$.generateColor)(Jt,85)),he=((0,$.generateColor)(Jt,80),(0,$.generateColor)(Vt,70)),ue=(0,$.generateColor)($t,70),pe=(0,$.generateColor)(Xt,70),_e=(0,$.generateColor)(Jt,70),ye=(0,$.generateColor)(Nt,70),me={pointShadowColor:he,labelTickColor:Vt,lineColor:Vt,borderBackgroundColor:zt,borderColor:Vt,pointBackgroundColor:zt,disabledLineColor:Gt,positivePlColor:re,negativePlColor:ie,qty:{backgroundColor:zt,textColor:se,dividerColor:At,activeColor:ne},text:{backgroundColor:zt,textColor:se,dividerColor:At,buttonTextColor:se,activeColor:ne},close:{backgroundColor:zt,iconColor:se,activeColor:ne},confirm:{disableBackgroundColor:Ft,backgroundColor:Nt,borderColor:Nt,textColor:Ht,disableTextColor:qt,activeColor:Et},informer:{backgroundColor:Y.errorNotificationBackdrop,iconColor:jt},anchor:{backgroundColor:zt,borderColor:Nt,shadowColor:ye}},fe={pointShadowColor:ue,labelTickColor:$t,lineColor:$t,borderBackgroundColor:zt,borderColor:$t,pointBackgroundColor:zt,disabledLineColor:Ut,positivePlColor:re,negativePlColor:ie,qty:{backgroundColor:zt,textColor:ie,dividerColor:Yt,activeColor:le},text:{backgroundColor:zt,textColor:ie,dividerColor:Yt,buttonTextColor:ie,activeColor:le},close:{backgroundColor:zt,iconColor:ie,activeColor:le},confirm:{disableBackgroundColor:Ft,backgroundColor:jt,borderColor:jt,textColor:Ht,disableTextColor:qt,activeColor:Qt},informer:{backgroundColor:Y.errorNotificationBackdrop,iconColor:jt},anchor:{backgroundColor:zt,borderColor:Nt,shadowColor:ye}},be={pointShadowColor:pe,labelTickColor:Xt,lineColor:Xt,borderBackgroundColor:zt,borderColor:Xt,pointBackgroundColor:zt,positivePlColor:re,negativePlColor:ie,qty:{backgroundColor:zt,textColor:re,dividerColor:te,activeColor:de},text:{backgroundColor:zt,textColor:re,dividerColor:te,buttonTextColor:re,activeColor:de},close:{backgroundColor:zt,iconColor:re,activeColor:de},informer:{backgroundColor:Y.errorNotificationBackdrop,iconColor:jt},anchor:{backgroundColor:zt,borderColor:Nt,shadowColor:ye}},Ce={pointShadowColor:_e,labelTickColor:Jt,lineColor:Jt,borderBackgroundColor:zt,borderColor:Jt,pointBackgroundColor:zt,positivePlColor:re,negativePlColor:ie,qty:{backgroundColor:zt,textColor:ae,dividerColor:ee,activeColor:ce},text:{backgroundColor:zt,textColor:ae,dividerColor:ee,buttonTextColor:ae,activeColor:ce},close:{backgroundColor:zt,iconColor:ae,activeColor:ce},informer:{backgroundColor:Y.errorNotificationBackdrop,iconColor:jt},anchor:{backgroundColor:zt,borderColor:Nt,shadowColor:ye}},ve={buy:{normal:me,disabled:(0,U.generateBlendColors)(me,oe)},sell:{normal:fe,disabled:(0,U.generateBlendColors)(fe,oe)},takeProfit:{normal:be,disabled:(0,U.generateBlendColors)(be,oe)},stopLoss:{normal:Ce,disabled:(0,U.generateBlendColors)(Ce,oe)}};function ge(t,e,o,r={}){
const i=o()?ve:Rt;let s;const a=t.bracketType();s=null!==a?a===l.BracketType.TakeProfit?i.takeProfit:i.stopLoss:t.isBuyDirection()?i.buy:i.sell;return{...e.disabled()?s.disabled:s.normal,borderStyle:t.lineStyle(),labelBorderVisible:!0,...r,lineStyle:null===a?y.LineStyle.Dashed:y.LineStyle.Dotted}}var Pe;!function(t){t[t.HoveredColorTransparency=85]="HoveredColorTransparency",t[t.MinQtyWidth=30]="MinQtyWidth"}(Pe||(Pe={}));class ke{constructor(t,e,o,r,i,s){this._item=t,this._source=e,this._chartModel=o,this._mainItemStyle=r,this._isNoOverlapMode=i.isNoOverlapMode,this._isTouchInteractionEnabled=i.isTouchInteractionEnabled,this._displayMode=i.displayMode,this._rightPadding=i.rightPadding,this._groupLineColor=i.groupLineColor,this._y=i.y,this._externalCallbacks=s}_isProfitLossHidden(){return 0===this._displayMode}_isProfitLossShorten(){return 2!==this._displayMode}_isCloseVisible(){return 0!==this._displayMode}_minQtyWidth(){return 0===this._displayMode?30:0}_isHoveredItem(t){return this._source.hoveredItem()?.id===t.id()}_isHoveredItemPart(t,e){return this._isHoveredItem(t)&&this._source.hoveredItem()?.part===e}_isSelectedItem(t){return this._source.selectedItem()?.id===t.id()}_isHoveredItemWithButtons(t){if(!t)return!1;const{takeProfit:e,stopLoss:o,trailingStop:r}=(0,j.bracketsByType)(this._source.items().projectionBrackets);return this._isHoveredItem(t)||Boolean(e&&this._isHoveredItem(e))||Boolean(o&&this._isHoveredItem(o))||Boolean(r&&this._isHoveredItem(r))}_isHoveredStopItemStopLimit(){const t=this._source.items().main,e=this._source.items().stopLimit;return!!(t&&e&&(0,u.isLimitPartStopLimitOrderItem)(e))&&this._isHoveredItem(t)}_isActiveSource(){return this._source.isHovered()||this._source.isSelected()}_itemWithButtons(){if(!this._isNoOverlapMode)return this._source.baseItem()}_projectionBracketsForItem(t){return t!==this._itemWithButtons()?[]:this._source.items().projectionBrackets}_renderDataForTPSLButtons(t){if(t!==this._itemWithButtons())return{};const e={borderStyle:this._source.isPlaced()?t.lineStyle():(0,i.ensureDefined)(this._source.mainItem()).lineStyle()},o={},{takeProfit:r,stopLoss:s,trailingStop:a,guaranteedStop:n}=(0,j.bracketsByType)(this._source.items().projectionBrackets);if(r){const t=null===r.price(),i=this._isHoveredItemPart(r,5),s=Te(r,this._source,(()=>this._chartModel.dark().value()),e);o.tp={id:r.id(),visibility:t&&this._isMainButtonsVisible()?1:0,text:r.qtyText(),title:r.tooltip(),underlineText:!1,textColor:t?s.text.buttonTextColor:s.text.textColor,backgroundColor:s.text.backgroundColor,borderColor:s.borderColor,borderStyle:s.borderStyle,active:{visible:i,backgroundColor:s.text.backgroundColor}}}const l=s||a||n;if(l){const t=null===l.price(),r=this._isHoveredItemPart(l,6),i=Te(l,this._source,(()=>this._chartModel.dark().value()),e),s=this._hasAdditionalStopTypes();o.sl={id:l.id(),visibility:t&&this._isMainButtonsVisible()?1:0,text:l.qtyText(),title:l.tooltip(),underlineText:s,textColor:t?i.text.buttonTextColor:i.text.textColor,backgroundColor:i.text.backgroundColor,
borderColor:i.borderColor,borderStyle:i.borderStyle,active:{visible:r,backgroundColor:s?i.text.activeColor:i.text.backgroundColor}}}return o}_renderDataForLine(t){return{drawOnTop:this._isActiveSource()&&!this._isNoOverlapMode,thickness:this._source.lineWidth(),style:t.lineStyle,color:t.lineColor,extend:this._source.isLineExtended(),shouldOffsetPointerEvents:this._source.isPlusButtonVisible()}}_renderDataForQty(t){return{text:(0,F.forceLTRStr)(this._item.qtyText()),minTextWidth:this._minQtyWidth(),textColor:t.textColor,backgroundColor:t.backgroundColor,dividerColor:t.dividerColor,isDividerVisible:!0,active:{visible:this._isHoveredItemPart(this._item,1),backgroundColor:t.activeColor}}}_renderDataForText(t){return{text:this._isProfitLossHidden()?"":this._item.profitLossText(this._isProfitLossShorten()),textColor:t.textColor,backgroundColor:t.backgroundColor,dividerColor:t.dividerColor,active:{visible:!1,backgroundColor:t.activeColor}}}_renderDataForPoint(){const{takeProfit:t,stopLoss:e,trailingStop:o,guaranteedStop:r}=(0,j.bracketsByType)(this._source.items().projectionBrackets),i=e||o||r,s=this._item===this._itemWithButtons(),a=s&&t&&null===t.price()&&this._isHoveredItem(t),n=s&&i&&null===i.price()&&this._isHoveredItem(i),l=this._isHoveredItem(this._item)||a||n?this._mainItemStyle.pointShadowColor:void 0,d=this._isSelectedItem(this._item)?this._groupLineColor:this._mainItemStyle.pointBackgroundColor,c=this._groupLineColor;return{visible:this._isActiveSource(),borderStyle:m,borderColor:c,backgroundColor:d,shadowColor:l}}_renderDataForClose(t){if(this._item.supportClose()&&this._isCloseVisible())return{backgroundColor:t.backgroundColor,iconColor:t.iconColor,active:{visible:this._isHoveredItemPart(this._item,3),backgroundColor:t.activeColor}}}_renderDataForConfirm(){if(this._item!==this._itemWithConfirmButton()||!this._isMainButtonsVisible())return;(0,i.assert)((0,h.isOrderLikeItem)(this._item));const t=Te(this._item,this._source,(()=>this._chartModel.dark().value())),e=(0,i.ensureDefined)(t.confirm),o=this._isHoveredItemPart(this._item,7),r=!this._source.hasError(),s=r?e.backgroundColor:e.disableBackgroundColor,a=r?(0,i.ensureDefined)(e.borderColor):e.disableBackgroundColor,n=r?e.textColor:e.disableTextColor;return{text:this._confirmButtonText(),textColor:n,backgroundColor:s,borderColor:a,borderStyle:y.LineStyle.Solid,active:{visible:o&&r,backgroundColor:e.activeColor,borderColor:e.activeColor}}}_renderDataGeneral(t){let e=0;const o=this._item.price();if(null!==o){e=(this._chartModel.mainSeries().priceScale().isInverted()?-1:1)*o}return{y:this._y,visible:this._item.isVisible(),isTouchInteractionEnabled:this._isTouchInteractionEnabled,sortingIndex:e,id:this._item.id(),disabled:this._source.disabled(),borderBackgroundColor:this._borderBackgroundColor(t),borderColor:t.borderColor,borderStyle:t.borderStyle,bodyAlignment:this._source.horizontalAlignment(),rightPadding:this._rightPadding}}_callbacks(t=[]){const e={enableTouchInteraction:()=>this._externalCallbacks.setTouchInteractionMode(!0)},o={
label:`[${this._modeGaLabel()}]`};this._item.hasContextMenu()&&(e.onContextMenu=t=>this._item.onContextMenu(t,o));const{takeProfit:r,stopLoss:i,trailingStop:s,guaranteedStop:a}=(0,j.bracketsByType)(t),n=i||s||a;return e.onCancelMove=()=>this._source.cancelMove?.()??!1,r&&(e.onMoveProjectionTP=t=>this._source.onMove(r.id(),t),e.onFinishMoveTP=()=>this._source.onFinishMoveProjectionBracket(r.id(),{...o,event:"Add bracket from btn"})),n&&(e.onMoveProjectionSL=t=>this._source.onMove(n.id(),t),e.onFinishMoveSL=()=>this._source.onFinishMoveProjectionBracket(n.id(),{...o,event:"Add bracket from btn"}),this._hasAdditionalStopTypes()&&(e.onClickSL=(t,e)=>n.toggleType(t,e,o))),this._isNoOverlapMode&&(e.allInteractionsHandler=()=>this._externalCallbacks.setNoOverlapMode(!1)),this._isMainButtonsVisible()&&(e.onConfirm=()=>this._source.onConfirm(this._item.id(),o)),e}_isMainButtonsVisible(){return!this._source.isPlaced()&&this._source.isActiveSource()||this._isHoveredItemWithButtons(this._item)||this._isHoveredStopItemStopLimit()||this._source.isSelected()}_borderBackgroundColor(t){return t.borderStyle===y.LineStyle.Solid?"transparent":t.borderBackgroundColor}_modeGaLabel(){switch(this._displayMode){case 2:return"Standard view";case 1:return"Medium view";case 0:return"Minimal view"}}_confirmButtonText(){return this._item.confirmText(!0)}_itemWithConfirmButton(){if(!this._source.isPlaced()&&!z.isResizableDrawerEnabled)return this._itemWithButtons()}_hasAdditionalStopTypes(){return Number(this._source.isStopLossSupported())+Number(this._source.isTrailingStopSupported())+Number(this._source.isGuaranteedStopSupported())>1}}function Te(t,e,o,r={}){return(e.isPlaced()?g.getDefaultStyleForOrderItem:ge)(t,e,o,e.styleOverrides?.()?.order)}const xe=(0,q.appendEllipsis)(s.t(null,void 0,o(324927))),Me=s.t(null,void 0,o(898818)),we=s.t(null,void 0,o(759783));class Se extends ke{rendererData(){const t=(0,P.getDefaultStyleForPositionItem)(this._item.isBuyDirection(),this._source.isBlocked(),(()=>this._chartModel.dark().value()),this._item.lineStyle(),this._source.styleOverrides?.()?.position);let e;const o=this._renderDataForClose(t.close);void 0!==o&&(e={...o,title:we});const r=this._item.extractErrorMessage(),i=r?{title:r,iconColor:t.informer.iconColor,backgroundColor:t.informer.backgroundColor}:void 0;return{type:B.ItemsRendererType.Position,...this._renderDataGeneral(t),...this._renderDataForTPSLButtons(this._item),callbacks:this._callbacks(this._projectionBracketsForItem(this._item)),line:this._renderDataForLine(t),qty:{...this._renderDataForQty(t.qty),title:xe},text:{...this._renderDataForText(t.text),title:"",textColor:this._item.profitLossTextColor(),active:{visible:!1,backgroundColor:t.qty.activeColor}},point:this._renderDataForPoint(),close:e,reverse:this._renderDataForReverse(t.reverse),informer:i}}_callbacks(t=[]){const e=super._callbacks(t),o={label:`[${this._modeGaLabel()}]`};return this._item.supportClose()&&this._isCloseVisible()&&(e.onClose=()=>this._item.onClose(o)),
this._shouldReverseShown()&&(e.onReverse=()=>this._item.onReverse(o)),this._item.supportModify()&&(e.onModify=()=>this._item.onModify(o)),e}_renderDataForReverse(t){if(!this._shouldReverseShown())return;const e=this._isHoveredItemWithButtons(this._item)||this._source.isSelected();return{title:Me,visibility:e?1:0,backgroundColor:t.backgroundColor,iconColor:t.iconColor,active:{visible:this._isHoveredItemPart(this._item,4),backgroundColor:t.activeColor}}}_shouldReverseShown(){return this._item.supportReverse()&&this._source.isReverseVisible()&&this._isReverseVisible()}_isReverseVisible(){return 2===this._displayMode&&!this._isNoOverlapMode}}class Be extends ke{rendererData(){const t=this._item===this._itemWithButtons(),e=Te(this._item,this._source,(()=>this._chartModel.dark().value()));let o;const r=this._renderDataForClose(e.close);void 0!==r&&(o={...r,title:this._item.texts().close.title});const i=this._item.extractErrorMessage(),s=i?{title:i,iconColor:e.informer.iconColor,backgroundColor:e.informer.backgroundColor}:void 0;return{type:t?B.ItemsRendererType.WithBracketButtons:B.ItemsRendererType.Default,isGroupInErrorState:this._source.isInErrorState?.(),...this._renderDataGeneral(e),...this._renderDataForTPSLButtons(this._item),callbacks:this._callbacks(this._projectionBracketsForItem(this._item)),line:this._renderDataForLine(e),qty:{...this._renderDataForQty(e.qty),title:this._item.texts().qty.title},text:{...this._renderDataForText(e.text),title:this._item.profitLossTooltip()},point:this._renderDataForPoint(),close:o,confirm:this._renderDataForConfirm(),informer:s,anchor:this._renderDataForAnchor(e)}}_callbacks(t=[]){const e=this._source,o=super._callbacks(t),r={label:`[${this._modeGaLabel()}]`};return this._item.isMovingEnabled()&&!this._isNoOverlapMode&&(o.onMove=t=>e.onMove(this._item.id(),t),o.onFinishMove=()=>e.onFinishMove(this._item.id(),r)),this._item.supportModify()&&(o.onModify=()=>this._item.onModify(r)),this._item.supportClose()&&this._isCloseVisible()&&(o.onClose=()=>this._item.onClose(r)),this._item.canQtyCalcModify()&&(o.onQtyModify=(t,e)=>this._item.toggleQtyModify(t,e,r)),o}_renderDataForAnchor(t){}}class Ie extends Be{constructor(t){const{item:e,source:o,chartModel:r,mainItemStyle:i,additionalData:s,callbacks:a}=t;super(e,o,r,i,s,a),this._source=o}_callbacks(t=[]){const e=super._callbacks(t),o={label:`[${this._modeGaLabel()}]`};return this._item.supportClose()&&this._isCloseVisible()&&(e.onClose=async()=>this._source.onClose(o)),this._isNoOverlapMode||(this._item.isMovingEnabled()&&(e.onMove=t=>this._source.onMove(this._item.id(),t),e.onFinishMove=()=>this._source.onFinishMove(this._item.id(),o)),this._item.canSwitchType()&&(e.onText=(t,e)=>this._item.toggleType(t,e,o))),e}_renderDataForText(t){return{...super._renderDataForText(t),active:{visible:this._isHoveredItemPart(this._item,2),backgroundColor:t.activeColor}}}}class De extends ke{rendererData(){
const t=ge(this._item,this._source,(()=>this._chartModel.dark().value())),e=this._item.bracketType()===l.BracketType.TakeProfit,o=this._item.extractErrorMessage(),r=o?{title:o,iconColor:t.informer.iconColor,backgroundColor:t.informer.backgroundColor}:void 0,i=this._item.bracketType()===l.BracketType.StopLoss||this._item.bracketType()===l.BracketType.TrailingStop||this._item.bracketType()===l.BracketType.GuaranteedStop;return{type:B.ItemsRendererType.Projection,...this._renderDataGeneral(t),drawWithTPOffset:e,drawWithSLOffset:i,isGroupInErrorState:this._source.isInErrorState?.(),callbacks:this._callbacks(),line:this._renderDataForLine(t),qty:this._renderDataForQty(t.qty),text:{...this._renderDataForText(t.text),title:this._item.profitLossText(),active:{visible:!1,backgroundColor:t.qty.activeColor}},informer:r,point:this._renderDataForPoint(),close:this._renderDataForClose(t.close)}}}function Le(t){const{item:e,source:o,chartModel:r,mainItemStyle:i,additionalData:s,callbacks:a}=t;if((0,u.isPositionItem)(e))return new Se(e,o,r,i,s,a);if((0,u.isPreOrderItem)(e))return new Ie({item:e,source:o,chartModel:r,mainItemStyle:i,additionalData:s,callbacks:a});if((0,u.isProjectionBracketItem)(e))return new De(e,o,r,i,s,a);if((0,h.isOrderLikeItem)(e))return new Be(e,o,r,i,s,a);throw new Error("Unknown traded item type")}var Oe,Re;!function(t){t[t.NormalRightBodyPadding=64]="NormalRightBodyPadding",t[t.MobileRightBodyPadding=40]="MobileRightBodyPadding",t[t.FontSize=13]="FontSize"}(Oe||(Oe={})),function(t){t[t.Large=380]="Large",t[t.Small=280]="Small"}(Re||(Re={}));class We{constructor(t,e,o){this._prevItemTokens=new Map,this._displayMode=2,this._invalidated=!0,this._isTouchInteractionEnabled=!0,this._chartModel=t,this._source=e,this._tradedGroupRenderersController=o,this._showOrderProperty=e.showOrderProperty(),this._showPositionProperty=e.showPositionProperty(),this._renderer=new N(13,this._tradedGroupRenderersController),this._tradedGroupRenderersController.registerRenderer(this._renderer),this._source.positionDisplayMode().subscribe(this._renderer,this._renderer.clearMinTextWidthCache),this._source.bracketsDisplayMode().subscribe(this._renderer,this._renderer.clearMinTextWidthCache)}destroy(){this._tradedGroupRenderersController.removeRenderer(this._renderer),this._source.positionDisplayMode().unsubscribe(this._renderer,this._renderer.clearMinTextWidthCache),this._source.bracketsDisplayMode().unsubscribe(this._renderer,this._renderer.clearMinTextWidthCache)}update(){this._invalidated=!0}makeSureIsUpdated(t){if(this._invalidated){const e=this._displayMode;this._calculateMode(t.mediaSize.width),e!==this._displayMode&&this._renderer.clearMinTextWidthCache(),this._tradedGroupRenderersController.invalidateCache(),this._updateImpl(),this._invalidated=!1}}renderer(){return this._renderer}priceToCoordinate(t){if(null===t)return null;const e=this._chartModel.mainSeries(),o=e.firstValue();return null===o?null:e.priceScale().priceToCoordinate(t,o)}_calculateMode(t){this._displayMode=t<Re.Large?1:2}_mainItemStyle(){
const t=this._source.mainItem();return void 0===t?(0,g.getStyleForOppositeDirection)(this._source.items().brackets[0],this._source,(()=>this._chartModel.dark().value()),this._source.styleOverrides?.()?.order):(0,h.isPositionLikeItem)(t)?(0,P.getDefaultStyleForPositionItem)(t.isBuyDirection(),this._source.isBlocked(),(()=>this._chartModel.dark().value()),t.lineStyle(),this._source.styleOverrides?.()?.position):((0,i.assert)((0,h.isOrderLikeItem)(t),"Unexpected traded item type"),Te(t,this._source,(()=>this._chartModel.dark().value())))}_rightPadding(){return 2!==this._displayMode?40:64}_sortedItems(){const t=[],e=this._source.items();let o=null,r=null,i=null;const s=e.brackets.slice();if(s.push(...e.projectionBrackets),void 0!==e.stopLimit&&s.push(e.stopLimit),void 0!==e.main&&s.push(e.main),this._tradedGroupRenderersController.isNoOverlapMode())return s;for(const e of s)this._source.movingItem()?.id===e.id()?i=e:this._source.hoveredItem()?.id===e.id()?r=e:this._source.selectedItem()?.id===e.id()?o=e:t.push(e);return null!==o&&t.push(o),null!==r&&t.push(r),null!==i&&t.push(i),t}_updateImpl(){if(this._renderer.clearItems(),!this._showOrderProperty.value()&&!this._showPositionProperty.value()||this._isNoData())return;const t=this._mainItemStyle(),e=this._source.isSelected(),o=e&&this._source.isHoveredOtherTradedGroup()?(0,i.ensureDefined)(t.disabledLineColor):t.lineColor,r=this._rightPadding(),s={vertLine:null,horizontalAlignment:this._source.horizontalAlignment(),rightPadding:r,disableTouchInteraction:()=>{0}};(this._source.isHovered()||e)&&(s.vertLine={thickness:this._source.lineWidth(),style:this._source.isPlaced()?m:f,color:o}),this._tradedGroupRenderersController.isNoOverlapMode()&&(s.disableNoOverlap=()=>this._tradedGroupRenderersController.setNoOverlapMode(!1)),this._renderer.setData(s);const a=new Map;for(const e of this._sortedItems()){const i=this.priceToCoordinate(e.price());if(!e.isVisible()||null===i)continue;const s=Le({item:e,mainItemStyle:t,source:this._source,chartModel:this._chartModel,additionalData:{isNoOverlapMode:this._tradedGroupRenderersController.isNoOverlapMode(),isTouchInteractionEnabled:this._isTouchInteractionEnabled,displayMode:this._displayMode,rightPadding:r,groupLineColor:o,y:i},callbacks:{setNoOverlapMode:t=>this._tradedGroupRenderersController.setNoOverlapMode(t),setTouchInteractionMode:t=>{this._isTouchInteractionEnabled=t}}}).rendererData();this._renderer.addItem(s),a.set(`${s.id} ${e.isBuyDirection()}`,s.id)}this._prevItemTokens.forEach(((t,e)=>{a.has(e)||this._renderer.clearMinTextWidthByItemId(t)})),this._prevItemTokens=a}_isNoData(){const t=this._source.items();return void 0===t.main&&void 0===t.stopLimit&&0===t.brackets.length}}var He=o(887946);class Ae extends He.PriceAxisView{constructor(t,e,o,r){super(),this._model=t,this._data=e,this._priceScaleFormatter=o,this._styleGetter=r}itemId(){return this._data.id()}_updateRendererData(t,e,o){if(t.visible=!1,!this._data.isVisible())return;const r=this._model.mainSeries(),i=r.priceScale(),s=r.firstValue();if(null===s)return
;const a=this._data.price();if(null===a)return;const n=this._styleGetter();o.background=n.qty.backgroundColor,o.borderColor=n.labelBorderVisible?n.borderColor:void 0,o.borderStyle=n.labelBorderVisible?n.borderStyle:void 0,o.textColor=n.qty.textColor,o.coordinate=i.priceToCoordinate(a,s),t.borderVisible=n.labelBorderVisible,t.text=this._formatPrice(a),t.tickColor=n.labelTickColor,t.visible=!0}_formatPrice(t){return this._priceScaleFormatter().format(t,{cutFractionalByPrecision:false})}}var Ve,Ge=o(603164);class Ne extends Ge.PriceLineAxisView{constructor(t,e,o,r,i,s,a){super(),this._chartModel=t,this._data=e,this._source=o,this._showProperty=s,this._height=r,this._verticalPadding=i,this._styleGetter=a}itemId(){return this._data.id()}_value(){const t=this._chartModel.mainSeries(),e=t.firstValue(),o=this._data.price();if(null===o||null===e)return{noData:!0};const r=t.priceScale().priceToCoordinate(o,e);return{noData:!1,floatCoordinate:r,coordinate:r,color:"",formattedPricePercentage:"",formattedPriceAbsolute:"",formattedPriceIndexedTo100:"",text:"",index:0}}_updateRendererData(t,e,o){const r=this._styleGetter();t.linestyle=r.borderStyle,o.additionalPaddingTop=this._verticalPadding,o.additionalPaddingBottom=this._verticalPadding,super._updateRendererData(t,e,o)}_priceLineColor(t){return this._styleGetter().lineColor}_lineWidth(){return this._source.lineWidth()}_lineStyle(){return this._styleGetter().borderStyle}_backgroundAreaHeight(){return this._height}_isVisible(){return this._showProperty.value()}}function Ee(t,e,o){if((0,u.isPositionItem)(o)){const r=()=>(0,P.getDefaultStyleForPositionItem)(o.isBuyDirection(),e.isBlocked(),(()=>t.dark().value()),o.lineStyle(),e.styleOverrides?.()?.position);return{priceAxisView:new Ae(t,o,(()=>e.priceScaleFormatter()),r),priceLineAxisView:new Ne(t,o,e,19,0,e.showPositionProperty(),r)}}(0,i.assert)((0,h.isOrderLikeItem)(o),"Unexpected traded item type");const r=()=>e.isPlaced()?(0,g.getDefaultStyleForOrderItem)(o,e,(()=>t.dark().value()),e.styleOverrides?.()?.order):ge(o,e,(()=>t.dark().value()));return{priceAxisView:new Ae(t,o,(()=>e.priceScaleFormatter()),r),priceLineAxisView:new Ne(t,o,e,17,1,e.showOrderProperty(),r)}}!function(t){t[t.OrderBackgroundHeight=17]="OrderBackgroundHeight",t[t.OrderVerticalPadding=1]="OrderVerticalPadding",t[t.PositionBackgroundHeight=19]="PositionBackgroundHeight",t[t.PositionVerticalPadding=0]="PositionVerticalPadding"}(Ve||(Ve={}));class qe{constructor(t,e){this._model=t,this._source=e}updateAllViews(t){void 0!==this._views.main&&(this._views.main.priceAxisView.update(t),this._views.main.priceLineAxisView.update(t)),void 0!==this._views.stopLimit&&(this._views.stopLimit.priceAxisView.update(t),this._views.stopLimit.priceLineAxisView.update(t));for(const e of this._views.brackets)e.priceAxisView.update(t),e.priceLineAxisView.update(t);for(const e of this._views.project.brackets)e.priceAxisView.update(t),e.priceLineAxisView.update(t)}views(t,e){
const o=this._source.movingItem()||null,r=this._source.hoveredItem()||null,i=this._source.selectedItem()||null,s=null!==o?o.id:null,a=null!==r?r.id:null,n=null!==i?i.id:null,l=[],d=[];let c=null,h=null,u=null,p=null,_=null,y=null;const m=this._views.brackets.slice();m.push(...this._views.project.brackets),this._views.main&&m.push(this._views.main),this._views.stopLimit&&m.push(this._views.stopLimit);for(const t of m)t.priceAxisView.itemId()===s?(c=t.priceAxisView,h=t.priceLineAxisView):t.priceAxisView.itemId()===a?(u=t.priceAxisView,p=t.priceLineAxisView):t.priceAxisView.itemId()===n?(_=t.priceAxisView,y=t.priceLineAxisView):(l.push(t.priceAxisView),d.push(t.priceLineAxisView));return null!==_&&null!==y&&(l.push(_),d.push(y)),null!==u&&null!==p&&(l.push(u),d.push(p)),null!==c&&null!==h&&(l.push(c),d.push(h)),[l,d]}recreateViews(){const t=this._source.items(),e=t.projectionBrackets.filter((t=>null!==t.price())).map((t=>Ee(this._model,this._source,t)));this._views={brackets:t.brackets.map((t=>Ee(this._model,this._source,t))),project:{brackets:e}},void 0!==t.main&&(this._views.main=Ee(this._model,this._source,t.main)),void 0!==t.stopLimit&&(this._views.stopLimit=Ee(this._model,this._source,t.stopLimit))}}var Fe=o(391431),ze=o(90470);function je(t){return t instanceof Ke}const Qe=s.t(null,void 0,o(155304)),$e=s.t(null,void 0,o(14821)),Ue=s.t(null,void 0,o(337539));function Ye(t){return void 0!==t&&((0,h.isOrderItemRawData)(t)||(0,h.isPreOrderItemRawData)(t))&&4===t.type}const Xe={contextMenu:{cancel:s.t(null,void 0,o(719634)),edit:(0,q.appendEllipsis)(s.t(null,void 0,o(224522)))},qty:{title:(0,q.appendEllipsis)(s.t(null,void 0,o(224522)))},close:{title:s.t(null,void 0,o(719634))}};class Ke extends a.CustomSourceBase{constructor(t,e,o,r,i,s,a,n,l,c){super(t,e),this._hadBeenModifiedAllItems=!1,this._isDestroyed=!1,this._bracketStopType=d.StopType.StopLoss,this._items={brackets:[],projectionBrackets:[]},this._blockedData=null,this._isBlocked=!1,this._mainSeries=e.mainSeries(),this._properties=o,this._configFlags=r,r.supportStopLoss?this._bracketStopType=d.StopType.StopLoss:r.supportTrailingStop?this._bracketStopType=d.StopType.TrailingStop:r.supportGuaranteedStop&&(this._bracketStopType=d.StopType.GuaranteedStop),this._globalVisibility=i,this._globalVisibility.subscribe(this.redraw.bind(this)),this._callbacks=a,this._symbolDataProvider=n,this._symbolDataProvider.onUpdate().subscribe(this,(()=>{this._updateProjectionBracketsItems(),this.items(!0).forEach((t=>{t.fireProfitLossChange()})),this.redraw()})),this._qtySuggester=l,this._paneView=new We(this._model,this,c),this._tradedGroupRenderersController=c,this._priceViewsGroup=new qe(this._model,this),this._rawDataSpawn=s.spawn(),this._rawDataSpawn.subscribe(this._setData.bind(this)),this._properties.positionPL.visibility.subscribe(this,this.redraw),this._properties.bracketsPL.visibility.subscribe(this,this.redraw),this._properties.positionPL.display.subscribe(this,(()=>{this.redraw()})),this._properties.bracketsPL.display.subscribe(this,(()=>{this.redraw()})),
this._tradedGroupRenderersController.noOverlapModeChanged().subscribe(this.redraw.bind(this)),this._properties.showPositions.subscribe(this,(()=>{this._tradedGroupRenderersController.setNoOverlapMode(!1)})),this._properties.showOrders.subscribe(this,(()=>{this._tradedGroupRenderersController.setNoOverlapMode(!1)})),this._model.mainSeries().dataEvents().symbolResolved().subscribe(this,(()=>{this._tradedGroupRenderersController.setNoOverlapMode(!1)})),this._model.onSelectedSourceChanged().subscribe(this,(()=>{this._model.selection().allSources().filter((t=>!je(t))).length>0&&this._tradedGroupRenderersController.setNoOverlapMode(!1)})),this._model.hoveredSourceChanged().subscribe(this,(()=>{if(!this._symbolDataProvider.isActualSymbol())return;const t=this._model.hoveredSource();(null===t||t!==this&&je(t))&&this.redraw()}))}destroy(){this._properties.showOrders.destroy(),this._properties.showPositions.destroy(),this._properties.positionPL.visibility.unsubscribeAll(this),this._properties.positionPL.display.unsubscribeAll(this),this._properties.bracketsPL.visibility.unsubscribeAll(this),this._properties.bracketsPL.display.unsubscribeAll(this),this._model.mainSeries().dataEvents().symbolResolved().unsubscribeAll(this),this._rawDataSpawn.destroy(),this._items.main?.destroy(),this._items.stopLimit?.destroy(),this._items.brackets.forEach((t=>t.destroy())),this._items.projectionBrackets.forEach((t=>t.destroy())),this._globalVisibility.unsubscribe(),this._paneView.destroy(),this._symbolDataProvider.onUpdate().unsubscribeAll(this),this._symbolDataProvider.destroy(),this._isDestroyed=!0}isSelectionEnabled(){return!0}priceScaleFormatter(){return this._mainSeries.formatter()}showPositionProperty(){return this._properties.showPositions}showOrderProperty(){return this._properties.showOrders}bracketStopType(){return this._bracketStopType}setBracketStopType(t){this._bracketStopType=t,this._updateProjectionBracketsItems(!0),this.redraw()}isStopLossSupported(){const t=this.items().main;return void 0!==t&&(Boolean(t.data().supportStopLoss)&&((0,u.isPositionItem)(t)?this._symbolDataProvider.supportPositionBrackets():this._symbolDataProvider.supportOrderBrackets()))}isTrailingStopSupported(){const t=this.items().main;return void 0!==t&&(Boolean(t.data().supportTrailingStop)&&((0,u.isPositionItem)(t)?this._symbolDataProvider.supportPositionBrackets():this._symbolDataProvider.supportOrderBrackets()))}isGuaranteedStopSupported(){const t=this.items().main;return void 0!==t&&(Boolean(t.data().supportGuaranteedStop)&&((0,u.isPositionItem)(t)?this._symbolDataProvider.supportPositionBrackets():this._symbolDataProvider.supportOrderBrackets()))}supportRiskControlsAndInfo(){return!!this._configFlags.supportRiskControlsAndInfo}isReverseVisible(){return this._properties.showReverse.value()}isPlusButtonVisible(){return this._properties.plusButtonVisibility.value()}lineWidth(){return this._properties.lineProperties.width.value()}lineStyle(){return this._properties.lineProperties.style.value()}isLineExtended(){
return this._properties.lineProperties.extend.value()}isPositionPLVisible(){return this._properties.positionPL.visibility}positionDisplayMode(){return this._properties.positionPL.display}isBracketsPLVisible(){return this._properties.bracketsPL.visibility}bracketsDisplayMode(){return this._properties.bracketsPL.display}horizontalAlignment(){return this._properties.horizontalAlignment.value()}isBlocked(){return this._isBlocked}disabled(){return this.isBlocked()}hasError(){return!1}isConfirmButtonOnDomWidget(){return!1}setIsBlocked(t){this._isBlocked=t,this.redraw()}isHovered(){return this._model.hoveredSource()===this}isHoveredOtherTradedGroup(){return!this.isHovered()&&je(this._model.hoveredSource())}isSelected(){return this._model.selection().isSelected(this)}items(t,e){if(t){const t=[];return this._items.stopLimit&&!e?.exceptStopLimit&&t.push(this._items.stopLimit),this._items.main&&!e?.exceptMain&&t.push(this._items.main),e?.exceptBrackets||t.push(...this._items.brackets),e?.exceptProjection||t.push(...this._items.projectionBrackets),t}return this._items}baseItem(){return this._items.stopLimit??this._items.main}findItem(t){const e=this.items(!0).find((e=>e.id()===t));return(0,i.assert)(void 0!==e,`Traded item with id ${t} is not found`),e}findItemWithType(t,e){const o=e===c.TradedItemType.Position?u.isPositionItem:h.isOrderLikeItem;return this.items(!0,{exceptStopLimit:!0}).find((e=>o(e)&&e.data().id===t))}movingItem(){const t=this._model.customSourceBeingMoved()===this&&this._model.customSourceMovingHitTestData()||null;return null===t||void 0===t.activeItem?null:t.activeItem}hoveredItem(){const t=this._model.hoveredSource()===this&&this._model.lastHittestData()||null;return null===t||void 0===t.activeItem?null:t.activeItem}selectedItem(){const t=this._model.selection().isSelected(this)&&this._model.lastSelectedHittestData()||null;return null===t||void 0===t.activeItem?null:t.activeItem}priceScale(){return this._mainSeries.priceScale()}updateAllViews(){this._paneView.update(),this._priceViewsGroup.updateAllViews((0,n.sourceChangeEvent)(this.id()))}updateViewsForPane(t){this._isSourceShouldBeShownOnPane(t)&&this.updateAllViews()}paneViews(t){return this._isSourceShouldBeShownOnPane(t)?[this._paneView]:[]}priceAxisViews(t,e){if(!this._isSourceShouldBeShownOnPane(t))return[];const[o,r]=this._priceViewsGroup.views(t,e);return t.findTargetPriceAxisViews(this,e,o,r)}redraw(){this.updateAllViews(),this._isSourceShouldBeShown()&&this._model.updateSource(this)}syncData(){this._isDestroyed||(null!==this._blockedData?(this._setData(this._blockedData),this._blockedData=null):this._setData(this._rawDataSpawn.value()))}allItemsSupportMove(t){if(void 0===this._items.main)return!1;for(const t of this._items.projectionBrackets)if(null!==t.price())return!1;return!(t.length<2)&&t.every((t=>(0,h.isOrderLikeItem)(t)&&t.isMovingEnabled()))}onMove(t,e,o=!1){const r=this.findItem(t),s=r.price(),a=this.items(!0);if(o&&this.setIsBlocked(!0),e.shiftKey&&null!==r.price()&&this.allItemsSupportMove(a)){this._hadBeenModifiedAllItems=!0
;const t=(0,i.ensureNotNull)(r.calcPriceDiff(e));a.forEach((e=>{(0,u.isProjectionBracketItem)(e)||e.applyPriceDiff(t)})),this._callbacks.hideChartHint()}else r.onMove(e),this._moveOtherProjectionBracketIfNeeded(r);null===s&&null!==r.price()&&this._priceViewsGroup.recreateViews(),this.redraw()}styleOverrides(){return null}_updateStopLimit(t){const e=this._items.main,o=this._items.stopLimit;Ye(t.main)&&void 0===o?this._items.stopLimit=this._createStopLimitItem(t,e):Ye(t.main)&&void 0!==o?o.setData(this._dataForStopLimitOrder(t.main)):void 0!==o&&(this._convertStopLimitOrderToMainItem(t.main,o),this._deleteStopLimitItem())}_positionVisibilityGetter(){return this._isSourceShouldBeShown()&&this.showPositionProperty().value()}_orderVisibilityGetter(){return this._isSourceShouldBeShown()&&this.showOrderProperty().value()}_setData(t,e=!0){if(this.isBlocked())this._blockedData=t,(0,u.isPositionItem)(this._items.main)&&void 0!==t.main&&((0,i.assert)((0,h.isPositionItemRawData)(t.main)),this._items.main.setData(t.main));else{const o=t.main?.symbol??t.brackets[0]?.symbol??null;t=(0,r.default)(t),e?this._updateItems(t):this._createItems(t),this._updateProjectionBracketsItems(),this._priceViewsGroup.recreateViews(),this._symbolDataProvider.start(o)}this.redraw()}_processedBracketData(t){return t}_createStopLimitItem(t,e){let o;if(void 0!==t.main&&((0,h.isOrderItemRawData)(t.main)||(0,h.isPreOrderItemRawData)(t.main))&&((0,u.isOrderItem)(e)||(0,u.isPreOrderItem)(e))&&4===e.type()){const e=this._dataForStopLimitOrder(t.main);o=(0,p.createItem)(p.TradedGroupItemType.LimitPartStopLimitOrder,e,this,this._model,{itemExternalServices:{symbolDataProvider:this._symbolDataProvider,qtySuggester:this._qtySuggester,tradedGroupRenderersController:this._tradedGroupRenderersController},visibilityGetters:{order:this._orderVisibilityGetter.bind(this),position:this._positionVisibilityGetter.bind(this)},sourceCallbacks:this._callbacks},Xe)}return o}_deleteStopLimitItem(){this._items.stopLimit?.destroy(),delete this._items.stopLimit}_isSourceShouldBeShown(){return!!this._globalVisibility.value()&&(!!this._symbolDataProvider.isActualSymbol()&&!(window.TradingView.printing&&!(0,ze.isTradingObjVisibleOnScreenshot)()))}_getToggleStopTypeMenuHandler(){const t=[];return this.isStopLossSupported()&&t.push({title:Qe,value:d.StopType.StopLoss}),this.isTrailingStopSupported()&&t.push({title:$e,value:d.StopType.TrailingStop}),this.isGuaranteedStopSupported()&&t.push({title:Ue,value:d.StopType.GuaranteedStop}),(e,o,r,i,s)=>{const a=this.mainItem();if(e){const t=a&&(0,u.isPositionItem)(a)?"Chart Position":"Chart Order";this._callbacks.trackEvent(t,"Stop loss type menu opening",r.label??"")}if(t.length>1){const r=this._model.timeScale(),a=[r.onScroll(),r.barSpacingChanged(),this._mainSeries.onSymbolIntervalChanged()],n=t.findIndex((t=>t.value===this._bracketStopType));(0,_.updateDropdownMenu)(e,o,t,n,"Stop bracket type",a,(t=>this.setBracketStopType(t)),i,s)}}}_moveOtherProjectionBracketIfNeeded(t){const e=this._items.main;if(!(0,
u.isProjectionBracketItem)(t)||void 0===e||!e.supportOnlyPairBrackets()||this._items.projectionBrackets.length<2)return;const o=(0,i.ensureNotNull)(e.price()),r=(0,i.ensureNotNull)(t.price())-o,s=this._items.projectionBrackets.find((e=>e!==t));(0,i.ensureDefined)(s).setPrice(o-r)}_createItems(t){const e=this._createMainItem(t),o=this._createStopLimitItem(t,e);this._items={main:e,stopLimit:o,brackets:t.brackets.map((t=>this._createBracketItem(t))),projectionBrackets:[]}}_updateItems(t){void 0===this._items.main&&void 0!==t.main?this._items.main=this._createMainItem(t):void 0!==this._items.main&&void 0!==t.main?this._updateMainItem(t.main):void 0!==this._items.main&&void 0===t.main&&this._deleteMainItem(),this._updateStopLimit(t),this._updateBrackets(t.brackets)}_updateBrackets(t){const e=[];this._items.brackets.forEach((o=>{const r=t.findIndex((t=>t.id===o.data().id));-1!==r?(o.setData(this._processedBracketData(t[r])),t.splice(r,1)):e.push(o)})),e.forEach((t=>{const e=this._items.brackets.findIndex((e=>e.data().id===t.data().id));-1!==e&&(this._items.brackets[e].destroy(),this._items.brackets.splice(e,1))})),t.forEach((t=>{const e=this._createBracketItem(t);this._items.brackets.push(e)}))}_updateProjectionBracketsItems(t=!1){const e=this._items.main;if(void 0===e||!e.supportBrackets())return;const{takeProfit:o,stopLoss:r,guaranteedStop:s,trailingStop:a}=(0,j.bracketsByType)(this._items.brackets),n=r??a??s,{takeProfit:d,stopLoss:c,guaranteedStop:h,trailingStop:u}=(0,j.bracketsByType)(this._items.projectionBrackets),_=c??u??h;[[o,d],[n,_]].forEach((([t,e])=>{if(t&&e){const t=this._items.projectionBrackets.indexOf(e);this._items.projectionBrackets[t].destroy(),this._items.projectionBrackets.splice(t,1)}}));const y=e.data();if(!o&&!d){const t=(0,j.buildProjectionBracketData)("ProjectionBracket",l.BracketType.TakeProfit,y),e=(0,p.createItem)(p.TradedGroupItemType.ProjectionBracket,t,this,this._model,{itemExternalServices:{symbolDataProvider:this._symbolDataProvider,qtySuggester:this._qtySuggester,tradedGroupRenderersController:this._tradedGroupRenderersController},visibilityGetters:{order:this._orderVisibilityGetter.bind(this),position:this._positionVisibilityGetter.bind(this)},sourceCallbacks:this._callbacks},Xe);this._items.projectionBrackets.push(e)}if(!n&&!_||t){const t=(0,i.ensureNotNull)((0,Fe.getBracketTypeByStopType)(this._bracketStopType)),e=(0,j.buildProjectionBracketData)("ProjectionBracket",t,y);if(_)_.setData(e);else{const t={itemExternalServices:{symbolDataProvider:this._symbolDataProvider,qtySuggester:this._qtySuggester,tradedGroupRenderersController:this._tradedGroupRenderersController},visibilityGetters:{order:this._orderVisibilityGetter.bind(this),position:this._positionVisibilityGetter.bind(this)},sourceCallbacks:this._callbacks};0;const o=(0,p.createItem)(p.TradedGroupItemType.ProjectionBracket,e,this,this._model,t,Xe);this._items.projectionBrackets.push(o)}}}_deleteMainItem(){(0,i.ensureDefined)(this._items.main).destroy(),delete this._items.main,this._deleteStopLimitItem()}
_isSourceShouldBeShownOnPane(t){return t.containsMainSeries()}}},905717:(t,e,o)=>{"use strict";o.d(e,{BaseItem:()=>b,numericFormatters:()=>y,plSign:()=>_});var r=o(650151),i=o(609838),s=o(41899),a=o(329452),n=o(341991),l=o(912321),d=o(251940),c=o(431520),h=o(813108),u=o(599016),p=o(80118);function _(t,e){return`${t>0?"+":t<0?"−":""}${e?" ":""}`}const y={integerFormatter:(0,d.getNumericFormatter)(0),oneDecimalFormatter:(0,d.getNumericFormatter)(1),twoDecimalFormatter:(0,d.getNumericFormatter)(2)},m=i.t(null,void 0,o(169961)),f=i.t(null,void 0,o(168222));class b{constructor(t,e,o,r,i,s,n){this._contextMenu=null,this._profitLossChanged=new a.Delegate,this._mainSeries=o,this._source=e,this._symbolDataProvider=r,this._gaOrigin=i,this._infoGetters=s,this._itemCommands=n,this.setData(t)}destroy(){this._formatPlWV?.destroy(),this._plColorWV?.destroy(),this._profitLossChanged.destroy(),this._contextMenu?.hide()}data(){return this._data}texts(){return this._infoGetters.texts}price(){return this._data.price}setData(t){this._data=(0,s.clone)(t)}priceText(){return(0,r.ensureNotNull)(this._symbolDataProvider.symbolData()).priceFormatter.format((0,r.ensureNotNull)(this.price()))}supportOnlyPairBrackets(){return!1}plColorWV(){return void 0===this._plColorWV&&(this._plColorWV=(0,n.createWVFromGetterAndSubscription)((()=>this.profitLossTextColor()),this._profitLossChanged)),this._plColorWV}formatPlWV(){return void 0===this._formatPlWV&&(this._formatPlWV=(0,n.createWVFromGetterAndSubscription)((()=>this.profitLossText()),this._profitLossChanged)),this._formatPlWV}hasContextMenu(){return this.supportModify()||this.supportClose()}useMinTickFormatter(){return!0}isBuyDirection(){return 1===this._data.side}isVisible(){return this._infoGetters.visibility()}fireProfitLossChange(){this._profitLossChanged.fire()}profitLossText(t){if(!this._data)return"";const e=this._potentialProfitLoss();if(null===e)return"";let o;switch(this._infoGetters.displayMode.value()){case l.PlDisplay.Money:o=this.moneyText(e,!0);break;case l.PlDisplay.Pips:o=this.pipsText(e,!0);break;case l.PlDisplay.Percentage:o=this.percentageText(e,!0)}return(0,c.startWithLTR)(o)}percentageText(t,e){const o=_(t,e),r=this._source.baseItem(),i=r?.price()??null;if(null===i)return"";const s=100*t/i*this._data.side;return`${o}${y.twoDecimalFormatter.format(Math.abs(s))}%`}moneyText(t,e){if(!this._source.supportRiskControlsAndInfo())return"";const o=this._symbolDataProvider.pipValue();if(null===o||(0,s.isNaN)(o.sellPipValue)||(0,s.isNaN)(o.buyPipValue))return"";const i=_(t,e),a=(0,r.ensureNotNull)(this._symbolDataProvider.symbolData()),n=t/(a.pipSize||a.minTick)*this._data.side,l=this.isBuyDirection()?o.buyPipValue:o.sellPipValue,d=Math.abs((0,p.pipsToRiskInCurrency)(n,this._data.qty,l,a.priceMagnifier,a.lotSize));return`${i}${y.twoDecimalFormatter.format(d)} ${this.currency()}`}pipsText(t,e){const s=_(t,e),a=(0,
r.ensureNotNull)(this._symbolDataProvider.symbolData()),n=isFinite(a.pipSize)&&a.pipSize!==a.minTick?y.oneDecimalFormatter:y.integerFormatter,l=t*this._data.side/(a.pipSize||a.minTick),d=Math.abs(l),c=n.format(d);switch(this._symbolDataProvider.pipValueType()){case u.PipValueType.Pips:return`${s}${i.t(null,{plural:"{pips} pips",count:d},o(161156)).replace("{pips}",c)}`;case u.PipValueType.Ticks:return`${s}${i.t(null,{plural:"{ticks} ticks",count:d},o(800338)).replace("{ticks}",c)}`}return""}tryBasePlOnLast(){return!1}lineStyle(){return h.LineStyle.Solid}confirmText(t){const e=1===this._data.side?m:f;return t?e:`${e} ${this.qtyText()} @ ${this.priceText()}`}extractErrorMessage(){return this._source.extractItemErrorMessage?.(this.id())??null}_trackEventGA(t){const{origin:e=this._gaOrigin,event:o,label:i}=t;(0,r.assert)(!!o,"GA Event shouldn' be empty"),this._itemCommands.trackEvent(e,o,i)}_quantityText(t){return(0,r.ensureNotNull)(this._symbolDataProvider.symbolData()).quantityFormatter.format(t)}}},260449:(t,e,o)=>{"use strict";o.d(e,{ItemRenderer:()=>k,ItemsRendererType:()=>f,activeItemToHitTestResultData:()=>v});var r=o(650151),i=o(86441),s=o(799573),a=o(184114),n=o(374410),l=(o(161656),o(601227)),d=o(898646),c=o(912321),h=o(805033);function u(t){const{percentage:e,padding:o,bitmapSizeWidth:r,horizontalPixelRatio:i,anchorWidth:s,tradedGroupHorizontalAlignment:a}=t;return a===c.TradedGroupHorizontalAlignment.Center?_({padding:o,horizontalPixelRatio:i,bitmapSizeWidth:r,anchorWidth:s,tradedGroupHorizontalAlignment:a}):function(t){const{percentage:e,bitmapSizeWidth:o,anchorWidth:r,tradedGroupHorizontalAlignment:i}=t,{percentageModifier:s,alignmentModifier:a}=function(t,e){return p(e)?{percentageModifier:t,alignmentModifier:1}:{percentageModifier:1-t,alignmentModifier:-1}}(e,i);return o*s+r*a}({percentage:e,horizontalPixelRatio:i,bitmapSizeWidth:r,anchorWidth:s,tradedGroupHorizontalAlignment:a})}function p(t){return t===c.TradedGroupHorizontalAlignment.Center||t===c.TradedGroupHorizontalAlignment.Right}function _(t){const{padding:e,bitmapSizeWidth:o,anchorWidth:r,horizontalPixelRatio:i,tradedGroupHorizontalAlignment:s}=t,a=e*i;return p(s)?a-r:o-a-r}const y=new Map([[[341,480],t=>_({padding:32,...t})],[[481,560],t=>u({padding:32,percentage:.1,...t})],[[561,960],t=>u({padding:32,percentage:.25,...t})],[[961,Number.MAX_SAFE_INTEGER],t=>u({padding:32,percentage:.5,...t})]]);function m(t,e,o,r,i,s){t.save(),t.fillStyle=s,t.fillRect(e,o,r,i),t.restore()}var f,b;!function(t){t[t.Position=0]="Position",t[t.WithBracketButtons=1]="WithBracketButtons",t[t.Projection=2]="Projection",t[t.Default=3]="Default"}(f||(f={})),function(t){t[t.BorderWidth=1]="BorderWidth",t[t.TextHorizontalPadding=7]="TextHorizontalPadding",t[t.BodyHeight=19]="BodyHeight",t[t.BodyHeightMobile=23]="BodyHeightMobile",t[t.CloseSize=7]="CloseSize",t[t.CloseHorizontalLeftPadding=8]="CloseHorizontalLeftPadding",t[t.CloseHorizontalRightPadding=8]="CloseHorizontalRightPadding",t[t.BorderRadius=4]="BorderRadius",t[t.PointRadius=2.5]="PointRadius",
t[t.PointBorderWidth=1]="PointBorderWidth",t[t.ShadowAdditionalRadius=3]="ShadowAdditionalRadius",t[t.CenterBodyPercentagePadding=40]="CenterBodyPercentagePadding",t[t.RightBodyPercentagePadding=5]="RightBodyPercentagePadding",t[t.InformerWidth=18]="InformerWidth",t[t.InfrormerMargin=5]="InfrormerMargin",t[t.NotificationBackdropHeight=20]="NotificationBackdropHeight",t[t.PlusButtonOffset=20]="PlusButtonOffset",t[t.AnchorWidth=12]="AnchorWidth",t[t.AnchorWidthTouch=26]="AnchorWidthTouch",t[t.AnchorShadowWidth=19]="AnchorShadowWidth",t[t.AnchorShadowBorderRadius=6]="AnchorShadowBorderRadius",t[t.AnchorBorderWidthLight=1]="AnchorBorderWidthLight",t[t.AnchorBorderWidthBold=3]="AnchorBorderWidthBold"}(b||(b={}));const C=l.CheckMobile.any()||l.CheckMobile.isIPad();function v(t){return{activeItem:t,equals:e=>!!e.activeItem&&e.activeItem.id===t.id&&e.activeItem.part===t.part}}function g(t,e,o,r,s){return a=>{a.preventDefault();const n=a.clientX-a.localX,l=a.clientY-a.localY,d=new i.Point(e+n,r+l);t((t=>t.x>e&&t.x<=e+s&&t.y>o&&t.y<=r),d)}}function P(t,e){const o=Math.max(1,Math.floor(e)),r=Math.round(t*e);return r%2!=o%2?r+1:r}class k{constructor(t,e,o,r){this._height=C?23:19,this._bodyBorderRadius=4,this._font=null,this._offsets=null,this._alignedTop=null,this._wasSourceMoved=!1,this._cache=null,this._cacheWithOffset=null,this._textWidthCache=e,this._minMainTextWidthGetter=r,this._data=t,this._font=o,this._initCtx()}data(){return this._data}clearCache(){this._cache=null,this._cacheWithOffset=null}applyOffset(t){this._offsets=t}setAlignedTopCoordinate(t){this._alignedTop=t}hitTest(t,e){const o=this.rectWithOffsets(e),{horizontalPixelRatio:i,verticalPixelRatio:l}=e,d=Math.round(t.x*i),c=Math.round(t.y*l),h=d>=o.left,u=d<=o.right,p=h&&u,_=c>=o.top&&c<=o.bottom,y={cursorType:n.PaneCursorType.Default,hideCrosshairLinesOnHover:!0,...v({id:this._data.id,part:9})};if(this._data.disabled)return new a.HitTestResult(a.HitTarget.Custom,y);let m={};if(void 0!==this._data.callbacks.onMove){const t=t=>{this._data.callbacks.onMove&&this._data.callbacks.onMove(t),this._wasSourceMoved=!0},e=()=>{this._wasSourceMoved&&this._data.callbacks.onFinishMove&&this._data.callbacks.onFinishMove(),this._wasSourceMoved=!1};m={cursorType:n.PaneCursorType.Pointer,pressedMouseMoveHandler:t,touchMoveHandler:t,mouseUpHandler:e,touchEndHandler:e,cancelMoveHandler:this._data.callbacks.onCancelMove}}const f=this._data.callbacks.onContextMenu,b=this._data.callbacks.onContextMenu,C={...y,...m,hasOwnShortcutsBehaviourFor:{shiftKey:!0},contextMenuHandler:f,touchContextMenuHandler:b};if(this._isAnchorIntersected(_,d,o))return new a.HitTestResult(a.HitTarget.Custom,{...C,...m,...v({id:this._data.id,part:8}),cursorType:n.PaneCursorType.VerticalResize});if(this._isPriceLineIntersected(d,c,e))return new a.HitTestResult(a.HitTarget.Custom,{...C,...v({id:this._data.id,part:9}),cursorType:n.PaneCursorType.VerticalResize});if(!_||!p)return null;const P=Math.round(o.body.left/i),k=Math.round(o.top/l);if(d>=o.close.left&&u&&void 0!==this._data.callbacks.onClose){
const t=Math.round(o.body.right/i),e=Math.round(o.close.left/i);return new a.HitTestResult(a.HitTarget.Custom,{...C,...v({id:this._data.id,part:3}),cursorType:n.PaneCursorType.Pointer,clickHandler:this._data.callbacks.onClose,tapHandler:this._data.callbacks.onClose,tooltip:{extendMargin:!0,text:(0,r.ensureDefined)(this._data.close).title,rect:{x:e,y:k,w:t-e,h:this._height}}})}if(this._data.callbacks?.allInteractionsHandler)return new a.HitTestResult(a.HitTarget.Custom,{...y,clickHandler:this._data.callbacks.allInteractionsHandler,tapHandler:this._data.callbacks.allInteractionsHandler,contextMenuHandler:this._data.callbacks.allInteractionsHandler,touchContextMenuHandler:this._data.callbacks.allInteractionsHandler});if(d>=o.text.left&&d<o.close.left){const t=Math.round(o.text.left/i),e=Math.round(o.bottom/l),r=Math.round(o.text.width/i);if(void 0!==this._data.callbacks.onText){const o=g(this._data.callbacks.onText,t,k,e,r),i=t=>{t.preventDefault(),!0===this._data.isTouchInteractionEnabled?o(t):this._data.callbacks.enableTouchInteraction()};return new a.HitTestResult(a.HitTarget.Custom,{...C,...v({id:this._data.id,part:2}),cursorType:n.PaneCursorType.Pointer,clickHandler:o,tapHandler:i,tooltip:{extendMargin:!0,text:this._data.text.title,rect:{x:t,y:k,w:r,h:this._height}}})}const s=Math.round(o.close.left/i);return new a.HitTestResult(a.HitTarget.Custom,{...C,...v({id:this._data.id,part:2}),tooltip:{extendMargin:!0,text:this._data.text.title,rect:{x:t,y:k,w:s-t,h:this._height}}})}const T=this._data.callbacks.onQtyModify;if(d>o.qty.left&&d<o.text.left&&void 0!==T){const t=Math.round(o.qty.left/i),e=g(T,t,k,Math.round(o.bottom/l),Math.round(o.qty.width/i)),s=t=>{t.preventDefault(),!0===this._data.isTouchInteractionEnabled?e(t):this._data.callbacks.enableTouchInteraction()},d=Math.round(o.text.left/i);return new a.HitTestResult(a.HitTarget.Custom,{...y,...m,...v({id:this._data.id,part:1}),cursorType:n.PaneCursorType.Pointer,clickHandler:e,tapHandler:s,tooltip:{extendMargin:!0,text:(0,r.ensureDefined)(this._data.qty).title,rect:{x:t,y:k,w:d-t,h:this._height}}})}const x=!0===this._data.isTouchInteractionEnabled?this._data.callbacks.onModify:this._data.callbacks.enableTouchInteraction;if(d>o.qty.left&&d<o.text.left&&void 0!==this._data.callbacks.onModify){const t=Math.round(o.text.left/i);return new a.HitTestResult(a.HitTarget.Custom,{...C,...v({id:this._data.id,part:1}),cursorType:n.PaneCursorType.Pointer,clickHandler:this._data.callbacks.onModify,tapHandler:x,tooltip:{extendMargin:!0,text:(0,r.ensureDefined)(this._data.qty).title,rect:{x:P,y:k,w:t-P,h:this._height}}})}return void 0!==this.data().informer&&d>o.informer.left&&d<o.informer.left+o.informer.width?new a.HitTestResult(a.HitTarget.Custom,{...C,...v({id:this._data.id,part:9}),cursorType:n.PaneCursorType.Default,touchContextMenuHandler:()=>{},tooltip:{extendMargin:!0,text:(0,r.ensureDefined)(this._data.informer).title,rect:{x:o.informer.left/i,y:k,w:o.informer.width/i,h:this._height},tooltipHideDelay:s.mobiletouch?1500:0}}):new a.HitTestResult(a.HitTarget.Custom,C)}
drawBackground(t,e){!this._data.line.drawOnTop&&this._data.visible&&this._drawLine(t,e)}drawLine(t,e){this._isDataVisibleInViewport(e)&&this._data.line.drawOnTop&&this._data.visible&&this._drawLine(t,e)}drawPointOnLine(t,e,o){if(!this._isDataVisibleInViewport(e)||!this._data.point.visible||!this._data.visible)return;const r=this.rectWithOffsets(e),{horizontalPixelRatio:i,verticalPixelRatio:s}=e,a=Math.round(3*i),n=Math.max(1,Math.floor(1*i)),l=n/2,h=2.5*i+l,u=o+(l%1==0?0:.5),p=r.yMid+(l%1==0?0:.5);this._data.line.extend||this._data.bodyAlignment!==c.TradedGroupHorizontalAlignment.Left||(t.save(),t.strokeStyle=this._data.line.color,t.lineWidth=Math.max(1,Math.floor(this._data.line.thickness*s)),(0,d.setLineStyle)(t,this._data.line.style),(0,d.drawHorizontalLine)(t,r.yMid,u,r.left),t.restore()),t.save(),t.lineWidth=n,(0,d.setLineStyle)(t,this._data.point.borderStyle),this._data.point.shadowColor&&!this._data.disabled&&(t.fillStyle=this._data.point.shadowColor,(0,d.createCircle)(t,u,p,h+a),t.fill()),t.strokeStyle=this._data.point.borderColor,t.fillStyle=this._data.point.backgroundColor,(0,d.createCircle)(t,u,p,h),t.fill(),t.stroke(),t.restore()}draw(t,e){if(!this._data.visible)return;const o=this.rectWithOffsets(e);if(!this._isDataVisibleInViewport(e))return;const i=0!==o.qty.width,s=0!==o.text.width,a=0!==o.close.width,n=o.body.right-o.body.left,l=o.bottom-o.top,c=o.top+o.borderWidth,u=l-2*o.borderWidth,p=o.borderRadius-o.borderWidth,{verticalPixelRatio:_,horizontalPixelRatio:y}=e;void 0!==this._data.informer&&(0,h.drawExclamationIcon)({ctx:t,x:o.informer.left,y:o.yMid-9*_,horizontalPixelRatio:y,verticalPixelRatio:_}),(0,d.drawRoundRectWithInnerBorder)(t,o.body.left,o.top,n,l,this._data.borderBackgroundColor,o.borderRadius,o.borderWidth,this._data.borderColor,this._data.borderStyle);let f=o.body.left+o.borderWidth;if(i){const i=(0,r.ensureDefined)(this._data.qty);this._drawQtyWithBackground(t,e,o,!0,!a&&!s),f+=o.qty.width,o.qty.rightDividerWidth&&(m(t,f,c,o.qty.rightDividerWidth,u,i.dividerColor),f+=o.qty.rightDividerWidth)}if(s&&(this._drawTextWithBackground(t,e,o,!i,!a),f+=o.text.width,o.text.rightDividerWidth&&(m(t,f,c,o.text.rightDividerWidth,u,this._data.text.dividerColor),f+=o.text.rightDividerWidth)),a){const i=(0,r.ensureDefined)(this._data.close),s=o.body.right-f-o.borderWidth;(0,d.drawRoundRectWithInnerBorder)(t,f,c,s,u,i.backgroundColor,[0,p,p,0]),i.active.visible&&(0,d.drawRoundRectWithInnerBorder)(t,f,c,s,u,i.active.backgroundColor,[0,p,p,0]),this._drawCloseButton(t,e,f,o.top,o.close.width,l,o.borderWidth)}this._drawAnchorPoint(t,e,o)}rect(t){return null===this._cache&&(this._cache=this._calculateCacheRect(t)),this._cache}rectWithOffsets(t){return null===this._cacheWithOffset&&(this._cacheWithOffset=this._calculateCacheRectWithOffsets(t)),this._cacheWithOffset}_calculateCacheRect(t){
const{horizontalPixelRatio:e,verticalPixelRatio:o,bitmapSize:r}=t,i=Math.max(1,Math.floor(1*e)),s=this._bodyBorderRadius,a=Math.max(s,Math.floor(s*e)),n=this._quantityWidth(this._ctxInternal),l=Math.round(n*e),d=this._mainTextWidth(this._ctxInternal),h=Math.round(d*e),u=this._data.callbacks.onClose?this._closeWidth():0,p=Math.round(u*e),_=this.data().isGroupInErrorState?Math.round(18*e):0;let m=0;const f=n&&(d||u),b=d&&u;m+=f&&1,m+=b&&1;const v=l+h+p+i*m+2*i;let g=Math.round(this._height*e);const k=Math.max(1,Math.floor(e));g%2!=k%2&&(g+=1);const T=Math.round(this._data.y*o),x=Math.floor(T+k/2-g/2)-Math.floor(.5*o),M=this._data.bodyAlignment===c.TradedGroupHorizontalAlignment.Center?Math.round(40*r.width/100):Math.round(this._data.rightPadding*e);let w=M;const S=this._data.bodyAlignment===c.TradedGroupHorizontalAlignment.Left;S&&(w=r.width-M-v);let B=r.width-w,I=B-v;let D=I+l+(l&&i)+(l&&i),L=D+h+(h&&i),O=I+(l&&i);const R=_+5*e;let W=O-R;S&&this.data().isGroupInErrorState&&(I+=R,D+=R,O+=R,B+=R,L+=R,W=O-R);let H=I;void 0!==this.data().informer&&(H=S?W:I-R);const A=P(C?26:12,e),V=function(t){const{bitmapSizeWidth:e,horizontalPixelRatio:o,anchorWidth:r,tradedGroupHorizontalAlignment:i}=t;let s;for(const t of y.keys()){if(e<t[0]*o||e>t[1]*o)continue;const a=y.get(t);if(void 0!==a){s=a({bitmapSizeWidth:e,horizontalPixelRatio:o,anchorWidth:r,tradedGroupHorizontalAlignment:i});break}}return s||function(t,e){return t/2-e}(e,r)}({bitmapSizeWidth:r.width,horizontalPixelRatio:e,anchorWidth:A,tradedGroupHorizontalAlignment:this._data.bodyAlignment});return{borderWidth:i,borderRadius:a,yMid:T,top:x,bottom:x+g,left:H,right:B,body:{left:I,right:B},qty:{width:l,left:O,rightDividerWidth:f&&this._data.qty?.isDividerVisible?i:0},text:{width:h,left:D,rightDividerWidth:b?i:0},close:{width:p,left:L},informer:{width:_,left:W},anchor:{left:V,width:A}}}_calculateCacheRectWithOffsets(t){const e=this.rect(t),{left:o,right:r}=this._calcCoordinateWithOffset(e.left,e.right),{left:i,right:s}=this._calcCoordinateWithOffset(e.body.left,e.body.right);return{...e,left:o,right:r,top:this._alignedTop?this._alignedTop:e.top,bottom:this._alignedTop?this._alignedTop+(e.bottom-e.top):e.bottom,body:{...e.body,left:i,right:s},qty:{...e.qty,left:this._calcCoordinateWithOffset(e.qty.left,e.qty.left+e.qty.width).left},text:{...e.text,left:this._calcCoordinateWithOffset(e.text.left,e.text.left+e.text.width).left},close:{...e.close,left:this._calcCoordinateWithOffset(e.close.left,e.close.left+e.close.width).left}}}_calcCoordinateWithOffset(t,e){if(null!==this._cache){const o=this._offsets?.leftmostForItemOffset??null;if(null!==o){const r=t-(this._cache.left-o);return{left:r,right:r+(e-t)}}const r=this._offsets?.rightmostForItemOffset??null;if(null!==r){const o=e+(r-this._cache.right);return{left:o-(e-t),right:o}}}return{left:t,right:e}}_calcAllWidth(t,e,o,r,i,s){return e+o+r+i+2*s}_isDataVisibleInViewport(t){const e=Math.ceil(this._height/2);return this._data.y>=-e&&this._data.y<=t.mediaSize.height+e}_mainTextWidth(t){
return this._data.text.text?this._minMainTextWidthGetter(this._data,t)+14:0}_closeWidth(){return 23}_measureTextWidth(t,e){if(!e)return 0;const o=t.font;t.font=(0,r.ensureNotNull)(this._font);const i=Math.ceil(this._textWidthCache.measureText(t,e));return t.font=o,i}_textWidth(t,e){const o=this._measureTextWidth(t,e);return o?Math.round(14+o):0}_quantityWidth(t){const e=this._measureTextWidth(t,this._data.qty?.text);return Math.round(Math.max(this._height,14+e,this._data.qty?.minTextWidth??0))}_drawLine(t,e){const o=this.rectWithOffsets(e),r=e.bitmapSize.width;t.save(),t.strokeStyle=this._data.line.color,t.lineWidth=Math.max(1,Math.floor(this._data.line.thickness*e.horizontalPixelRatio)),(0,d.setLineStyle)(t,this._data.line.style),void 0!==this._data.informer&&(0,h.drawErrorNotificationBackdrop)({ctx:t,x:0,y:o.yMid,w:e.bitmapSize.width,h:20,horizontalPixelRatio:e.horizontalPixelRatio,verticalPixelRatio:e.verticalPixelRatio}),(0,d.drawHorizontalLine)(t,o.yMid,o.body.right,r),this._data.line.extend&&(0,d.drawHorizontalLine)(t,o.yMid,0,o.body.right),t.restore()}_drawQtyBackground(t,e,o,i,s){const a=(0,r.ensureDefined)(this._data.qty),n=o.bottom-o.top,l=o.top+o.borderWidth,c=n-2*o.borderWidth,h=o.borderRadius-o.borderWidth;let u=0;s?u=i?h:[0,h,h,0]:i&&(u=[h,0,0,h]),(0,d.drawRoundRectWithInnerBorder)(t,o.qty.left,l,o.qty.width,c,a.backgroundColor,u),a.active.visible&&(0,d.drawRoundRectWithInnerBorder)(t,o.qty.left,l,o.qty.width,c,a.active.backgroundColor,u)}_drawQtyWithBackground(t,e,o,i,s){const a=(0,r.ensureDefined)(this._data.qty);this._drawQtyBackground(t,e,o,i,s);const n=(o.top+o.bottom)/2,l=o.qty.left+o.qty.width/2;this._drawText(t,e,a.text,a.textColor,l,n)}_drawTextWithBackground(t,e,o,i,s){const a=o.bottom-o.top,n=o.top+o.borderWidth,l=a-2*o.borderWidth,c=o.borderRadius-o.borderWidth;let u=0;s?u=i?c:[0,c,c,0]:i&&(u=[c,0,0,c]),(0,d.drawRoundRectWithInnerBorder)(t,o.text.left,n,o.text.width,l,this._data.text.backgroundColor,u),this._data.text.active.visible&&(0,d.drawRoundRectWithInnerBorder)(t,o.text.left,n,o.text.width,l,this._data.text.active.backgroundColor,u);const p=o.top+a/2,_=o.text.left+o.text.width/2,y=(this._data.qty||this._data.text).text,m=this._getTextYMidCorrection(t,y)*e.verticalPixelRatio;(0,h.drawText)(t,e,this._data.text.text,_,p,m,this._data.text.textColor,(0,r.ensureNotNull)(this._font))}_drawText(t,e,o,i,s,a,n){const l=o.split("@").join("").toUpperCase(),d=this._getTextYMidCorrection(t,l)*e.verticalPixelRatio;(0,h.drawText)(t,e,o,s,a,d,i,(0,r.ensureNotNull)(this._font),n)}_drawUnderlinedText(t,e,o,i,s,a){const n=this._getTextYMidCorrection(t,o)*e.verticalPixelRatio,l=this._getTextWidth(t,o);(0,h.drawUnderlinedText)(t,e,o,s,a,n,i,(0,r.ensureNotNull)(this._font),l)}_isPriceLineIntersected(t,e,o){const r=this.rectWithOffsets(o),i=t>=r.left&&t<=r.right,s=t<o.bitmapSize.width-20*o.horizontalPixelRatio&&t>r.right;this._data.line.shouldOffsetPointerEvents&&r.left,!1!==this._data.line.extend||t>r.right||this._data.bodyAlignment===c.TradedGroupHorizontalAlignment.Left&&t<r.left&&r.left;return false}
_isAnchorIntersected(t,e,o){return void 0!==this.data().anchor&&t&&e>o.anchor.left&&e<o.anchor.left+o.anchor.width}_getTextYMidCorrection(t,e){const o=t.font;t.font=(0,r.ensureNotNull)(this._font);const i=this._textWidthCache.yMidCorrection(t,e);return t.font=o,i}_getTextWidth(t,e){const o=t.font;t.font=(0,r.ensureNotNull)(this._font);const i=this._textWidthCache.getMetrics(t,e);return t.font=o,i.width}_drawCloseButton(t,e,o,s,a,n,l){const c=(0,r.ensureDefined)(this._data.close);t.save(),t.lineWidth=l,t.strokeStyle=c.iconColor,t.setLineDash([]);const h=s+n,u=Math.max(1,Math.ceil(7*e.horizontalPixelRatio)),p=Math.max(1,Math.ceil(7*e.verticalPixelRatio)),_=Math.round((a-u)/2),y=Math.round((n-p)/2);(0,d.drawPoly)(t,[new i.Point(o+_,s+y),new i.Point(o+_+u,h-y)],!0),(0,d.drawPoly)(t,[new i.Point(o+_+u,s+y),new i.Point(o+_,h-y)],!0),t.restore()}_initCtx(){const t=document.createElement("canvas");t.width=0,t.height=0,this._ctxInternal=(0,r.ensureNotNull)(t.getContext("2d"))}_drawAnchorPoint(t,e,o){const{anchor:r}=this._data,{bitmapSize:i,horizontalPixelRatio:s}=e;if(void 0===r||i.width<=340*s)return;if(r.shadowColor){const e=P(12,s),i=P(19,s),a=19*s,n=o.anchor.left-(i-e)/2,l=Math.max(6,6*s);(0,d.drawRoundRectWithInnerBorder)(t,Math.round(n),Math.round(o.yMid-a/2),a,a,r.shadowColor,l,0)}const a=C?3:1;let n=Math.max(1,Math.floor(a*s));r.isSelected&&(n+=Math.max(1,Math.floor(s/2)));const l=(o.anchor.width/2+n)/2;(0,d.drawRoundRectWithInnerBorder)(t,Math.round(o.anchor.left),Math.round(o.yMid-o.anchor.width/2),o.anchor.width,o.anchor.width,r.backgroundColor,l,n,r.borderColor)}}},96829:(t,e,o)=>{"use strict";o.d(e,{OrderBaseItem:()=>P});var r=o(650151),i=o(150335),s=o(86441),a=o(41899),n=o(621327),l=o(813108),d=o(14654),c=o(912321),h=o(735248),u=o(599016),p=o(46415),_=o(525915),y=o(168873),m=o(631488),f=o(586137);var b=o(470692),C=o(112232),v=o(482303),g=o(905717);class P extends g.BaseItem{constructor(t,e,o,r,i,s,a,n="Chart Order"){super(t,e,o,r,n,i,s),this._inEdit=!1,this._isQtyModifyOpened=!1,this._isResetQtyCalcValue=!1,this._originalQty=null,this._handleQtyModifyClose=(t,e)=>{t(e)?this.applyQty(this.data().qty):this._handleQtyModifyDestroy()},this._handleQtyModifyDestroy=()=>{this.applyQty(this.data().qty),this._isQtyModifyOpened=!1,this._closeQtyCalc()},this._isBracket()&&this._source.bracketsDisplayMode().subscribe(this,this.fireProfitLossChange),a&&(this._qtyModifyCallbacks={onQtyApplyHandler:a.onQtyApplyHandler,onToggleQtyCalcHandler:a.onToggleQtyCalcHandler,qtyInfoGetter:a.qtyInfoGetter},this._qtyProvider=a.qtyProvider,this._suggestedQtySubscription=this._qtyProvider.subscribe((t=>a?.onQtyProviderUpdate?.(t)))),this._originalQty=this.qty()}destroy(){this._isQtyModifyOpened=!1,this._closeQtyCalc(),this._source.bracketsDisplayMode().unsubscribe(this,this.fireProfitLossChange),this._contextMenu?.hide(),this._suggestedQtySubscription?.unsubscribe(),super.destroy()}supportBrackets(){
return!this._isBracket()&&this._symbolDataProvider.supportOrderBrackets()&&this._symbolDataProvider.supportModifyOrderBrackets()&&this._symbolDataProvider.supportAddBracketsToExistingOrder()}canQtyCalcModify(){return Boolean(this._qtyProvider)}toggleQtyModify(t,e,o={}){this.canQtyCalcModify()&&(this._isQtyModifyOpened=!this._isQtyModifyOpened,this._toggleQtyCalc(t,e,o))}modifyQty(t){this.canQtyCalcModify()&&(t<=0?this._isResetQtyCalcValue=!0:(this._isResetQtyCalcValue=!1,(0,r.ensureDefined)(this._qtyModifyCallbacks).onQtyApplyHandler(t)))}applyQty(t){this.modifyQty(t),this._qtyProvider?.applySuggestedQty(this._data.qty),this._originalQty!==t&&this._itemCommands.trackEvent(this._gaOrigin,"Change quantity")}currency(){return this._symbolDataProvider.orderCurrency()??""}canSwitchType(){return!1}id(){return this._id}setData(t){this._inEdit||(super.setData(t),this.fireProfitLossChange(),this._id=this._calcId())}setInEdit(t){this._inEdit=t}type(){return this._data.type}bracketType(){return this._isBracket()?(0,C.getBracketType)(this._data):null}qty(){return this._data.qty}profitLossText(t){return this._data&&this._isBracket()&&this._source.isBracketsPLVisible()?.value()&&this.bracketType()!==u.BracketType.TrailingStop&&(this._source.supportRiskControlsAndInfo()||this._infoGetters.displayMode.value()!==c.PlDisplay.Money)?super.profitLossText(t):this._bracketTypeText(t)}profitLossTextColor(){if(!this._data)return"";const t=this.style();if(!this._isBracket())return t.text.textColor;const e=this._potentialProfitLoss();return null===e?t.text.textColor:(0,h.profitLossTextColor)(t,e,!1)}profitLossTooltip(){const t=this._source.mainItem(),e=t?.price();if(null==e||!this._isBracket())return"";const o=this._potentialProfitLoss();if(null===o)return`${this._bracketTypeText()}`;if(!this._source.isBracketsPLVisible()?.value()||this.bracketType()===u.BracketType.TrailingStop)return"";switch(this._infoGetters.displayMode.value()){case c.PlDisplay.Money:return`${this._bracketTypeText()} ${this.percentageText(o)} ${this.pipsText(o)}`;case c.PlDisplay.Percentage:return`${this._bracketTypeText()} ${this.moneyText(o)} ${this.pipsText(o)}`;case c.PlDisplay.Pips:return`${this._bracketTypeText()} ${this.moneyText(o)} ${this.percentageText(o)}`;default:return`${this._bracketTypeText()} ${this.pipsText(o)} ${this.percentageText(o)} ${this.moneyText(o)}`}}lineStyle(){return!1===this._source.isPlaced()||!1===this.isWorking()?l.LineStyle.Dotted:l.LineStyle.Solid}calcPriceDiff(t){const e=new s.Point(t.localX,t.localY),o=this._mainSeries.priceScale(),i=this._mainSeries.firstValue();if(null===i)return null;const a=(0,r.ensureNotNull)(this.price());return o.coordinateToPrice(e.y,i)-a}applyPriceDiff(t){this._inEdit=!0,this._data.price=this._calcPriceByDiff(t)}onMove(t){const e=this.calcPriceDiff(t);null!==e&&(this.applyPriceDiff(e),t.isTouch&&(0,r.ensureDefined)(this._itemCommands.exitTrackingMode)())}async onFinishMove(t={},e={},o=!0,i,s=!0){o&&this._source.setIsBlocked(!0);const n=(0,a.clone)(this._data),l=(0,r.ensureNotNull)(this.price())
;n.limitPrice=e.limitPrice??n.limitPrice,n.takeProfit=e.takeProfit??n.takeProfit,n.stopLoss=e.stopLoss??n.stopLoss,n.guaranteedStop=e.guaranteedStop??n.guaranteedStop,n.stopPrice?n.stopPrice=l:n.limitPrice&&(n.limitPrice=l),n.stopType===p.StopType.GuaranteedStop&&(n.guaranteedStop=l),this._addTrailingStopPipsData(n,e.trailingStop),this._trackEventGA(t);const d=await this._onFinishMoveOrder(n,i,s);return this._inEdit=!1,d}hasContextMenu(){return super.hasContextMenu()||this.isMovingEnabled()}async onContextMenu(t,e){this.fireProfitLossChange(),e=(0,y.mergeGaParams)(e,{origin:this._source.isPlaced()?"Chart Order Context Menu":"Chart Project Order Context Menu"});const o=this._composeContextMenuActions(e),r={side:(0,_.sideToText)(this._data.side),qty:this.qtyText(),price:this.priceText(),orderType:(0,_.orderTypeToText)({orderType:this._data.type}),formatPlWV:this._isBracket()?this.formatPlWV():void 0},i=await async function(t,e){const o=[],r=await(0,f.createContextMenuTitle)("Trading.OrderTitle",e);r&&o.push(r);for(const{actionId:e,label:r,onExecute:i}of t)o.push(new n.Action({actionId:e,options:{label:r,onExecute:i}}));return o}(o,r),s=this._infoGetters.noOverlapAction(e,(t=>this._trackEventGA(t)));s&&(i.push(new n.Separator),i.push(s)),this._contextMenu=await(0,f.showContextMenu)(i,t,(()=>{this._contextMenu=null}),{detail:{type:"order",id:this.id().toString()}})}_composeContextMenuActions(t){const e=[];return this.supportModifyFromContextMenu()&&e.push({actionId:"Trading.EditOrder",label:this.texts().contextMenu.edit,onExecute:()=>this.onModifyFromContextMenu(t)}),this.supportClose()&&e.push({actionId:"Trading.CancelOrder",label:this.texts().contextMenu.cancel,onExecute:()=>this.onClose(t)}),e}_calcPriceByDiff(t){const{minTick:e,variableMinTickData:o}=(0,r.ensureNotNull)(this._symbolDataProvider.symbolData()),s=t+(0,r.ensureNotNull)(this.price()),a=(0,d.getMinTick)({minTick:e,price:s,variableMinTickData:o});return(0,i.fixComputationError)(a*Math.round(s/a))}_addTrailingStopPipsData(t,e){const o=this._source.mainItem();if(void 0===o)return;const{side:i,stopPrice:s,limitPrice:a}=o.data(),n=1===i?-1:1,{pipSize:l}=(0,r.ensureNotNull)(this._symbolDataProvider.symbolData()),d=t.stopPrice||t.limitPrice;if(void 0===e||void 0===d)if((0,b.isBracketOrderRawData)(t)&&t.parentId&&t.stopType===p.StopType.TrailingStop&&void 0!==d){if((0,v.isPositionLikeItem)(o)){const{bid:e,ask:o}=(0,r.ensureNotNull)(this._symbolDataProvider.lastData()),s=1===i?e:o;return void(t.trailingStopPips=(0,m.calcPipsByPrice)(d,s,n,l))}const e=3===o.data().type?s:a;t.trailingStopPips=(0,m.calcPipsByPrice)(d,(0,r.ensureDefined)(e),n,l)}else;else t.trailingStopPips=(0,m.calcPipsByPrice)(e,d,n,l)}_bracketTypeText(t){if(!this._data)return"";if(this._isBracket()){const e=(0,C.getBracketTypeToText)(this._data.type,this._data.stopType,t);if(null!==e)return e}return t?(0,_.orderTypeToText)({orderType:this._data.type,uppercase:!0,shorten:!0}):`${(0,_.sideToText)(this._data.side)} ${(0,_.orderTypeToText)({orderType:this._data.type,uppercase:!1})}`}
_potentialProfitLoss(){const t=this._source.baseItem();if(!this._data||!t)return null;const e=this.price(),{ask:o,bid:r}=this._symbolDataProvider.lastData()||{},i=t.tryBasePlOnLast()&&3===this._data.type&&this._data.stopType===p.StopType.TrailingStop;if(!(0,a.isNumber)(e))return null;const s=i?this.isBuyDirection()?o:r:t.price();return(0,a.isNumber)(s)?(s-e)*this._data.side:null}_isBracket(){const t=this.data();if(!(0,b.isBracketOrderRawData)(t))return!1;const e=this._source.mainItem();return 1!==t.parentType||void 0!==e&&e.data().side!==this._data.side}_closeQtyCalc(){this._toggleQtyCalc((()=>!1),new s.Point(0,0))}_qtyCalcValueGetter(){return this._isResetQtyCalcValue?0:this.data().qty}_toggleQtyCalc(t,e=new s.Point(0,0),o={}){if(!this.canQtyCalcModify())return;!1===this._isQtyModifyOpened&&(this._originalQty=this.qty());const{onToggleQtyCalcHandler:i,qtyInfoGetter:a}=(0,r.ensureDefined)(this._qtyModifyCallbacks);i({isOpened:this._isQtyModifyOpened,position:e,qtyInfoGetter:a,valueGetter:()=>this._qtyCalcValueGetter(),onValueChange:t=>this.modifyQty(t),onClose:e=>this._handleQtyModifyClose(t,e),onDestroy:this._handleQtyModifyDestroy})}}},99834:(t,e,o)=>{"use strict";o.d(e,{OrderWithMenuItem:()=>a});var r=o(650151),i=o(86441),s=o(96829);class a extends s.OrderBaseItem{constructor(t,e,o,r,i,s,a,n,l){super(t,e,o,r,i,s,n,l),this._isTypeMenuOpened=!1,this._handleTypeMenuClose=(t,e)=>{t(e)||this._handleTypeMenuDestroy()},this._handleTypeMenuDestroy=()=>{this._isTypeMenuOpened=!1},this._typeMenuCallbacks=a}destroy(){this._typeMenuCallbacks?.closeDropdownMenuHandler(),super.destroy()}isMovingEnabled(){return!0}toggleType(t,e,o={}){this._typeMenuCallbacks&&(this._isTypeMenuOpened=!this._isTypeMenuOpened,this._toggleTypeMenu(t,e,o))}async onClose(t){}async onModify(t){return!1}async onModifyFromContextMenu(t){return!1}async _onFinishMoveOrder(t,e,o=!0){return!0}_toggleTypeMenu(t,e=new i.Point(0,0),o={}){(0,r.ensureDefined)(this._typeMenuCallbacks).onToggleTypeMenuHandler(this._isTypeMenuOpened,e,o,(e=>this._handleTypeMenuClose(t,e)),this._handleTypeMenuDestroy)}}},71991:(t,e,o)=>{"use strict";o.d(e,{preOrderItemId:()=>r});const r="preOrder"},482303:(t,e,o)=>{"use strict";var r;function i(t){return"onReverse"in t}function s(t){return function(t){return"isMovingEnabled"in t&&"onMove"in t&&"onFinishMove"in t}(t)&&"bracketType"in t&&"isWorking"in t}function a(t){return"Position"===t.dataType}function n(t){return"PreOrder"===t.dataType}function l(t){return"Order"===t.dataType}o.d(e,{isOrderItemRawData:()=>l,isOrderLikeItem:()=>s,isPositionItemRawData:()=>a,isPositionLikeItem:()=>i,isPreOrderItemRawData:()=>n}),function(t){t.PreOrder="PreOrder",t.Order="Order",t.Position="Position"}(r||(r={}))},265328:(t,e,o)=>{"use strict";o.d(e,{TradedGroupItemType:()=>r,createItem:()=>m});var r,i=o(650151),s=o(154834),a=o(586137),n=o(482303),l=o(28518),d=o(680813),c=o(971283),h=o(980465),u=o(87502),p=o(287782),_=o(842471);class y{constructor(t,e){this._symbol=t,this._qtySuggester=e}subscribe(t){
return this._qtySuggester.suggestedQtyChanged(this._symbol).subscribe(t)}async applySuggestedQty(t){this._qtySuggester.setQty(this._symbol,t)}}function m(t,e,o,m,f,b){const C=m.mainSeries(),v=(t,e)=>(0,a.createNoOverlapMenuAction)(f.itemExternalServices.tradedGroupRenderersController,t,e);if(t===r.Order&&(0,n.isOrderItemRawData)(e)){const{dataType:t,...r}=e,i=t=>(0,u.getDefaultStyleForOrderItem)(t,o,(()=>m.dark().value()),o.styleOverrides?.()?.order);return new c.OrderItem(r,o,C,f.itemExternalServices.symbolDataProvider,{displayMode:o.bracketsDisplayMode(),style:i,visibility:f.visibilityGetters.order,noOverlapAction:v,texts:b},{trackEvent:f.sourceCallbacks.trackEvent,exitTrackingMode:f.sourceCallbacks.exitTrackingMode},void 0,f.gaOrigin)}if(t===r.Position&&(0,n.isPositionItemRawData)(e)){const t=t=>(0,h.getDefaultStyleForPositionItem)(t.isBuyDirection(),o.isBlocked(),(()=>m.dark().value()),t.lineStyle(),o.styleOverrides?.()?.position),{dataType:r,...i}=e;return new p.PositionItem(i,o,C,f.itemExternalServices.symbolDataProvider,{displayMode:o.positionDisplayMode(),style:t,visibility:f.visibilityGetters.position,noOverlapAction:v,texts:b},{trackEvent:f.sourceCallbacks.trackEvent})}if(t===r.LimitPartStopLimitOrder&&(0,n.isOrderItemRawData)(e)){const t=t=>(0,u.getDefaultStyleForOrderItem)(t,o,(()=>m.dark().value()),o.styleOverrides?.()?.order),r={...(0,s.default)(e),price:(0,i.ensureDefined)(e.limitPrice),considerFilledQty:!1},{dataType:a,...n}=r;let l;if(f.qtyModifyCallbacks){l={qtyProvider:new y(n.symbol,f.itemExternalServices.qtySuggester),...(0,i.ensureDefined)(f.qtyModifyCallbacks)}}return new d.LimitPartStopLimitOrderItem(n,o,C,f.itemExternalServices.symbolDataProvider,{displayMode:o.bracketsDisplayMode(),style:t,visibility:f.visibilityGetters.order,noOverlapAction:v,texts:b},{trackEvent:f.sourceCallbacks.trackEvent,exitTrackingMode:f.sourceCallbacks.exitTrackingMode},l)}if(t===r.ProjectionBracket&&(0,n.isOrderItemRawData)(e)){const{dataType:t,...r}=e,i=t=>(0,u.getDefaultStyleForOrderItem)(t,o,(()=>m.dark().value()),o.styleOverrides?.()?.order);return new l.ProjectionBracketItem(r,o,C,f.itemExternalServices.symbolDataProvider,{displayMode:o.bracketsDisplayMode(),style:i,visibility:f.visibilityGetters.order,noOverlapAction:v,texts:b},{trackEvent:f.sourceCallbacks.trackEvent,exitTrackingMode:f.sourceCallbacks.exitTrackingMode},f.menuCallbacks)}if(t===r.PreOrder&&(0,n.isPreOrderItemRawData)(e)){const t=t=>(0,u.getDefaultStyleForOrderItem)(t,o,(()=>m.dark().value()),o.styleOverrides?.()?.order),{dataType:r,...s}=e,a=new y(s.symbol,f.itemExternalServices.qtySuggester);return new _.PreOrderItem(s,o,C,f.itemExternalServices.symbolDataProvider,{displayMode:o.bracketsDisplayMode(),style:t,visibility:f.visibilityGetters.order,noOverlapAction:v,texts:b},{trackEvent:f.sourceCallbacks.trackEvent,exitTrackingMode:f.sourceCallbacks.exitTrackingMode},f.menuCallbacks,{qtyProvider:a,...(0,i.ensureDefined)(f.qtyModifyCallbacks)},"Chart Place Order")}throw new Error("Unknown traded item type")}!function(t){t[t.Position=0]="Position",
t[t.Order=1]="Order",t[t.LimitPartStopLimitOrder=2]="LimitPartStopLimitOrder",t[t.ProjectionBracket=3]="ProjectionBracket",t[t.PreOrder=4]="PreOrder"}(r||(r={}))},431635:(t,e,o)=>{"use strict";o.d(e,{isLimitPartStopLimitOrderItem:()=>h,isOrderItem:()=>d,isPositionItem:()=>l,isPreOrderItem:()=>c,isProjectionBracketItem:()=>u});var r=o(28518),i=o(680813),s=o(971283),a=o(287782),n=o(842471);function l(t){return t instanceof a.PositionItem}function d(t){return t instanceof s.OrderItem}function c(t){return t instanceof n.PreOrderItem}function h(t){return t instanceof i.LimitPartStopLimitOrderItem}function u(t){return t instanceof r.ProjectionBracketItem}},680813:(t,e,o)=>{"use strict";o.d(e,{LimitPartStopLimitOrderItem:()=>n});var r=o(650151),i=o(41899),s=o(525915),a=o(971283);class n extends a.OrderItem{isWorking(){return!1}async onFinishMove(t,e={},o=!0,s){this._source.setIsBlocked(o);const a=(0,i.clone)(this.data()),n=(0,r.ensureNotNull)(a.price);a.limitPrice=n,a.takeProfit=e.takeProfit||a.takeProfit,a.stopLoss=e.stopLoss||a.stopLoss,await this._addTrailingStopPipsData(a),void 0===s&&(s=1),this._trackEventGA(t);const l=await(0,r.ensureDefined)(this._data.callbacks.moveOrder)(a.id,a,s);return this._inEdit=!1,this._source.setIsBlocked(!1),this._source.syncData(),l}profitLossText(t){return this._source.isPlaced()?this._bracketTypeText(t):(0,s.orderTypeToText)({orderType:this.data().type,uppercase:t,shorten:t})}supportClose(){return!1}_calcId(){return this.data().type}}},971283:(t,e,o)=>{"use strict";o.d(e,{OrderItem:()=>h});var r=o(650151),i=o(41899),s=o(440891),a=o(599016),n=o(168873),l=o(96829),d=o(112232);const c=s.enabled("chart_hide_close_order_button");class h extends l.OrderBaseItem{style(){return this._infoGetters.style(this)}isMovingEnabled(){return this._isBracket()?(0,d.isParentItemSupportBracketModification)(this._source,this._symbolDataProvider):this._data.supportMove}supportClose(){return!c&&Boolean(this._data.callbacks.cancelOrder)}supportModify(){return this._isBracket()?Boolean(this._data.callbacks.modifyOrder)&&(0,d.isParentItemSupportBracketModification)(this._source,this._symbolDataProvider):Boolean(this._data.callbacks.modifyOrder)}supportModifyFromContextMenu(){return Boolean(this._data.callbacks.modifyOrderFromContextMenu)}isWorking(){return 6===this._data.status}qtyText(){return super._quantityText(this._data.considerFilledQty?this._data.qty-(this._data.filledQty||0):this._data.qty)}profitLossText(t){return this._data&&this._isBracket()&&this._source.isBracketsPLVisible()?.value()&&this.bracketType()!==a.BracketType.TrailingStop?super.profitLossText(t):this._bracketTypeText(t)}async onModify(t={},e){return this._onModify(this._data.callbacks.modifyOrder)}async onModifyFromContextMenu(t={},e){return this._onModify(this._data.callbacks.modifyOrderFromContextMenu)}async onClose(t={}){this._source.isBlocked()||(this._source.setIsBlocked(!0),this._trackEventGA((0,n.mergeGaParams)(t,{event:this._source.isPlaced()?"Cancel order":"Cancel bracket"})),await(0,
r.ensureDefined)(this._data.callbacks.cancelOrder)(this._data.id),this._source.setIsBlocked(!1),this._source.syncData())}_onFinishMoveOrder(t,e,o=!0){return this._data.callbacks.moveOrder||this._data.supportModifyOrderPrice?(0,r.ensureDefined)(this._data.callbacks.moveOrder)(t.id,t,e):(0,r.ensureDefined)(this._data.callbacks.modifyOrder)(t.id,t,e,o)}_calcId(){return void 0!==this._data.parentId?(0,r.ensureNotNull)((0,d.getBracketType)(this._data)):this._data.id}async _onModify(t,e={},o){this._source.setIsBlocked(!0);const s=(0,i.clone)(this._data);await this._addTrailingStopPipsData(s),this._trackEventGA((0,n.mergeGaParams)(e,{event:this._source.isPlaced()?"Edit Order":"Edit project order"}));const a=await(0,r.ensureDefined)(t)(s.id,s,this._getOrderTicketFocusControl(),o);return this._source.setIsBlocked(!1),this._source.syncData(),a}_getOrderTicketFocusControl(){const t=this.bracketType();return null===t?3===this._data.type?2:1:t===a.BracketType.TakeProfit?3:4}}},52102:(t,e,o)=>{"use strict";o.d(e,{buyActive:()=>k,buyPointShadow:()=>w,darkTheme:()=>L,errorNotificationBackdrop:()=>I,overlay:()=>B,sellActive:()=>T,sellPointShadow:()=>S,stopLossActive:()=>M,takeProfitActive:()=>x});var r,i=o(926048),s=o(589637),a=o(619624);!function(t){t[t.ActiveColorTransparency=85]="ActiveColorTransparency",t[t.IconDisabledTransparency=80]="IconDisabledTransparency",t[t.PointShadowTransparency=70]="PointShadowTransparency",t[t.OverlayTransparency=50]="OverlayTransparency",t[t.BackdropTransparency=10]="BackdropTransparency"}(r||(r={}));const n=i.colorsPalette["color-white"],l=i.colorsPalette["color-tv-blue-a800"],d=i.colorsPalette["color-tv-blue-a700"],c=i.colorsPalette["color-tv-blue-500"],h=i.colorsPalette["color-tv-blue-600"],u=i.colorsPalette["color-cold-gray-600"],p=i.colorsPalette["color-cold-gray-750"],_=i.colorsPalette["color-cold-gray-900"],y=i.colorsPalette["color-minty-green-500"],m=i.colorsPalette["color-ripe-red-a800"],f=m,b=i.colorsPalette["color-ripe-red-500"],C=i.colorsPalette["color-ripe-red-600"],v=i.colorsPalette["color-tan-orange-500"],g=(0,s.generateColor)(y,70),P=(0,s.generateColor)(v,70),k=(0,s.generateColor)(c,85),T=(0,s.generateColor)(b,85),x=(0,s.generateColor)(y,85),M=(0,s.generateColor)(v,85),w=(0,s.generateColor)(c,70),S=(0,s.generateColor)(b,70),B=(0,s.generateColor)(_,50),I=(0,s.generateColor)(b,10),D=((0,s.generateColor)(b,80),(0,s.generateColor)(y,80),(0,s.generateColor)(v,80),(0,s.generateColor)(c,80),{text:n,buySecondary:l,buyDisabled:d,buyPrimary:c,confirmActiveBuy:h,textDisabled:u,backgroundDisabled:p,background:_,takeProfitPrimary:y,sellSecondary:m,sellDisabled:f,sellPrimary:b,confirmActiveSell:C,stopLossPrimary:v,takeProfitSecondary:"#0D3D41",stopLossSecondary:"#453826",buyActive:k,sellActive:T,takeProfitActive:x,stopLossActive:M,buyPointShadow:w,sellPointShadow:S,takeProfitPointShadow:g,stopLossPointShadow:P,overlay:B,errorNotificationBackdrop:I}),L=(0,a.generateOrderColorTheme)(D)},328969:(t,e,o)=>{"use strict";o.d(e,{buyPointShadow:()=>M,lightTheme:()=>I,overlay:()=>S,sellPointShadow:()=>w})
;var r,i=o(926048),s=o(589637),a=o(619624),n=o(52102);!function(t){t[t.PointShadowTransparency=80]="PointShadowTransparency",t[t.OverlayTransparency=50]="OverlayTransparency"}(r||(r={}));const l=i.colorsPalette["color-tv-blue-50"],d=i.colorsPalette["color-tv-blue-100"],c=i.colorsPalette["color-tv-blue-500"],h=i.colorsPalette["color-cold-gray-150"],u=i.colorsPalette["color-cold-gray-300"],p=i.colorsPalette["color-tv-blue-600"],_=i.colorsPalette["color-minty-green-50"],y=i.colorsPalette["color-minty-green-500"],m=i.colorsPalette["color-ripe-red-50"],f=i.colorsPalette["color-ripe-red-100"],b=i.colorsPalette["color-ripe-red-500"],C=i.colorsPalette["color-ripe-red-600"],v=i.colorsPalette["color-tan-orange-50"],g=i.colorsPalette["color-tan-orange-500"],P=i.colorsPalette["color-white"],k=i.colorsPalette["color-white"],T=(0,s.generateColor)(y,80),x=(0,s.generateColor)(g,80),M=(0,s.generateColor)(c,80),w=(0,s.generateColor)(b,80),S=(0,s.generateColor)(P,50),B={text:k,buySecondary:l,buyDisabled:d,buyPrimary:c,confirmActiveBuy:p,textDisabled:u,backgroundDisabled:h,background:P,takeProfitPrimary:y,sellSecondary:m,sellDisabled:f,sellPrimary:b,confirmActiveSell:C,stopLossPrimary:g,takeProfitSecondary:_,stopLossSecondary:v,buyActive:n.buyActive,sellActive:n.sellActive,takeProfitActive:n.takeProfitActive,stopLossActive:n.stopLossActive,buyPointShadow:M,sellPointShadow:w,takeProfitPointShadow:T,stopLossPointShadow:x,overlay:S,errorNotificationBackdrop:n.errorNotificationBackdrop},I=(0,a.generateOrderColorTheme)(B)},87502:(t,e,o)=>{"use strict";o.d(e,{getDefaultStyleForOrderItem:()=>l,getStyleForOppositeDirection:()=>d});var r=o(813108),i=o(599016),s=o(374551),a=o(328969),n=o(52102);function l(t,e,o,l){const d=o()?n.darkTheme:a.lightTheme,c=o()?l?.darkTheme:l?.lightTheme;let h,u;const p=t.bracketType();null!==p?(h=p===i.BracketType.TakeProfit?d.takeProfit:d.stopLoss,u=p===i.BracketType.TakeProfit?c?.takeProfit:c?.stopLoss):(h=t.isBuyDirection()?d.buy:d.sell,u=t.isBuyDirection()?c?.buy:c?.sell);const _=e.disabled()?h.disabled:h.normal,y=e.disabled()?u?.disabled:u?.normal,m=e.isPlaced()?t.lineStyle():r.LineStyle.Dotted;return(0,s.deepExtend)({},{..._,lineStyle:m,borderStyle:m,labelBorderVisible:!0},y??{})}function d(t,e,o,i){const l=o()?n.darkTheme:a.lightTheme,d=o()?i?.darkTheme:i?.lightTheme,c=t.isBuyDirection()?l.sell:l.buy,h=t.isBuyDirection()?d?.sell:d?.buy,u=e.disabled()?c.disabled:c.normal,p=e.disabled()?h?.disabled:h?.normal,_=t.isWorking()?e.lineStyle():r.LineStyle.Dotted,y=t.isWorking()?r.LineStyle.Solid:r.LineStyle.Dotted;return(0,s.deepExtend)({},{...u,lineStyle:_,borderStyle:y,labelBorderVisible:!0},p??{})}},287782:(t,e,o)=>{"use strict";o.d(e,{PositionItem:()=>P});var r=o(650151),i=o(912321),s=o(621327),a=o(440891),n=o(41899),l=o(248361),d=o(671945),c=o(525915),h=o(586137),u=o(631488),p=o(168873),_=o(905717),y=o(609838);const m=(0,o(669874).appendEllipsis)(y.t(null,void 0,o(591932))),f=y.t(null,void 0,o(383236)),b=y.t(null,void 0,o(360196));var C=o(735248);const v=(0,
d.getLogger)("Trading.Positions"),g=a.enabled("chart_hide_close_position_button");class P extends _.BaseItem{constructor(t,e,o,r,i,s){super(t,e,o,r,"Chart Position",i,s),this._source.positionDisplayMode().subscribe(this,this.fireProfitLossChange)}destroy(){this._source.positionDisplayMode().unsubscribe(this,this.fireProfitLossChange),super.destroy()}supportBrackets(){return this._symbolDataProvider.supportPositionBrackets()&&this._symbolDataProvider.supportModifyPositionBrackets()}currency(){return this._symbolDataProvider.positionCurrency()??""}profitLossTextColor(){if(!this._data)return"";const t=this._potentialProfitLoss();return null===t?"":(0,C.profitLossTextColor)(this._infoGetters.style(this),t,this._infoGetters.displayMode.value()===i.PlDisplay.Money)}id(){return this._data.id}setData(t){const e=this.data()?.pl??this.data()?.unrealizedPl;super.setData(t),this.data().pl!==e&&this.fireProfitLossChange()}qtyText(){return super._quantityText(this._data.qtyBySide)}qty(){return this._data.qtyBySide}supportOnlyPairBrackets(){return this._data.supportOnlyPairBrackets}supportReverse(){return this._symbolDataProvider.supportPositionReverse()}async onReverse(t={}){t=(0,p.mergeGaParams)(t,{event:"Reverse Position"}),await this._doActionWithBlock("reversePosition",!0,t)}supportClose(){return!g&&this._data.supportClose}async onClose(t={}){t=(0,p.mergeGaParams)(t,{event:"Close Position"}),await this._doActionWithBlock("closePosition",!0,t)}supportModify(){return this.supportBrackets()}supportModifyFromContextMenu(){return this.supportBrackets()}async onModify(t={}){return t=(0,p.mergeGaParams)(t,{event:"Edit Position"}),this._doActionWithBlock("modifyPosition",!0,t,void 0,void 0,!1)}useMinTickFormatter(){return!1}async onModifyWithBracket(t={},e={},o=!0,i,s=!0){const a=this._symbolDataProvider.lastData(),n=a?.bid??null,l=a?.ask??null;if(void 0!==e.trailingStop&&n&&l){const t=1===this._data.side?-1:1,o=1===this._data.side?n:l,{pipSize:i}=(0,r.ensureNotNull)(this._symbolDataProvider.symbolData());e.trailingStopPips=(0,u.calcPipsByPrice)(e.trailingStop,o,t,i),e.trailingStop=void 0}return this._doActionWithBlock("modifyPosition",o,t,e,i,s)}hasContextMenu(){return super.hasContextMenu()||this.supportReverse()}moneyText(t,e){return`${(0,_.plSign)(t,e)}${_.numericFormatters.twoDecimalFormatter.format(Math.abs(t))} ${this.currency()}`}async onContextMenu(t,e){this.fireProfitLossChange();const o={};e=(0,p.mergeGaParams)(e,{origin:"Chart Position Context Menu"}),this.supportClose()&&(o.closePosition=this.onClose.bind(this,e)),this.supportReverse()&&(o.reversePosition=this.onReverse.bind(this,e)),this.supportModify()&&(o.modifyPosition=this.onModify.bind(this,e));const r={side:(0,c.sideToText)(this._data.side),qty:this.qtyText(),price:this.priceText(),formatPlWV:this.formatPlWV(),plColorWV:this.plColorWV()},i=await async function(t,e){const o=[],r=await(0,h.createContextMenuTitle)("Trading.PositionTitle",e);return r&&o.push(r),void 0!==t.modifyPosition&&o.push(new s.Action({actionId:"Trading.ModifyPosition",options:{label:m,
onExecute:t.modifyPosition}})),void 0!==t.closePosition&&o.push(new s.Action({actionId:"Trading.ClosePosition",options:{label:f,onExecute:t.closePosition}})),void 0!==t.reversePosition&&o.push(new s.Action({actionId:"Trading.ReversePosition",options:{label:b,onExecute:t.reversePosition}})),o}(o,r),a=this._infoGetters.noOverlapAction(e,(t=>this._trackEventGA(t)));a&&(i.push(new s.Separator),i.push(a)),this._contextMenu=await(0,h.showContextMenu)(i,t,(()=>{this._contextMenu=null}),{detail:{type:"position",id:this.id()}})}tryBasePlOnLast(){return!0}_potentialProfitLoss(){if(!this._source.isPositionPLVisible().value()||!this._data)return null;const{pl:t,unrealizedPl:e=null,side:o,price:r}=this._data,s=t??e;if(null===s)return null;if(this._infoGetters.displayMode.value()===i.PlDisplay.Money)return s;const{trade:a,ask:l,bid:d}=this._symbolDataProvider.lastData()||{},c=(this._data.plBasedOnLast?a:this.isBuyDirection()?d:l)??r;return(0,n.isNumber)(c)?(c-r)*o:null}async _doActionWithBlock(t,e,o={},i={},s,a){if(void 0===this._data.callbacks[t])return!1;e&&this._source.setIsBlocked(!0),this._trackEventGA(o);let n=!1;try{n="modifyPosition"===t?await(0,r.ensureDefined)(this._data.callbacks.modifyPosition)(this._data.id,i,s,a):await(0,r.ensureDefined)(this._data.callbacks[t])(this._data.id)}catch(e){v.logWarn(`Try to ${t}, but got error: ${(0,l.errorToString)(e)}`)}finally{e&&(this._source.setIsBlocked(!1),this._source.syncData())}return n}}},980465:(t,e,o)=>{"use strict";o.d(e,{getDefaultStyleForPositionItem:()=>y});var r=o(813108),i=o(374551),s=o(926048),a=o(316167),n=o(52102);const l={pointShadowColor:n.buyPointShadow,lineColor:s.colorsPalette["color-tv-blue-500"],borderBackgroundColor:s.colorsPalette["color-cold-gray-900"],borderColor:s.colorsPalette["color-tv-blue-500"],pointBackgroundColor:s.colorsPalette["color-cold-gray-900"],disabledLineColor:s.colorsPalette["color-tv-blue-a700"],positivePlColor:s.colorsPalette["color-minty-green-500"],negativePlColor:s.colorsPalette["color-ripe-red-500"],qty:{backgroundColor:s.colorsPalette["color-tv-blue-500"],textColor:s.colorsPalette["color-white"],dividerColor:s.colorsPalette["color-tv-blue-500"],activeColor:s.colorsPalette["color-tv-blue-600"]},text:{backgroundColor:s.colorsPalette["color-cold-gray-900"],textColor:s.colorsPalette["color-cold-gray-600"],dividerColor:s.colorsPalette["color-tv-blue-a800"],activeColor:n.buyActive},close:{backgroundColor:s.colorsPalette["color-cold-gray-900"],iconColor:s.colorsPalette["color-tv-blue-500"],activeColor:n.buyActive},reverse:{backgroundColor:s.colorsPalette["color-cold-gray-900"],borderColor:s.colorsPalette["color-tv-blue-500"],iconColor:s.colorsPalette["color-tv-blue-500"],activeColor:n.buyActive},confirm:{disableBackgroundColor:s.colorsPalette["color-cold-gray-750"],backgroundColor:s.colorsPalette["color-tv-blue-500"],borderColor:s.colorsPalette["color-tv-blue-500"],textColor:s.colorsPalette["color-white"],disableTextColor:s.colorsPalette["color-cold-gray-600"],activeColor:s.colorsPalette["color-tv-blue-600"]},informer:{
backgroundColor:n.errorNotificationBackdrop,iconColor:s.colorsPalette["color-ripe-red-500"]}},d={pointShadowColor:n.sellPointShadow,lineColor:s.colorsPalette["color-ripe-red-500"],borderBackgroundColor:s.colorsPalette["color-cold-gray-900"],borderColor:s.colorsPalette["color-ripe-red-500"],pointBackgroundColor:s.colorsPalette["color-cold-gray-900"],disabledLineColor:s.colorsPalette["color-ripe-red-a800"],positivePlColor:s.colorsPalette["color-minty-green-500"],negativePlColor:s.colorsPalette["color-ripe-red-500"],qty:{backgroundColor:s.colorsPalette["color-ripe-red-500"],textColor:s.colorsPalette["color-white"],dividerColor:s.colorsPalette["color-ripe-red-500"],activeColor:s.colorsPalette["color-ripe-red-600"]},text:{backgroundColor:s.colorsPalette["color-cold-gray-900"],textColor:s.colorsPalette["color-cold-gray-600"],dividerColor:s.colorsPalette["color-ripe-red-a800"],activeColor:n.sellActive},close:{backgroundColor:s.colorsPalette["color-cold-gray-900"],iconColor:s.colorsPalette["color-ripe-red-500"],activeColor:n.sellActive},reverse:{backgroundColor:s.colorsPalette["color-cold-gray-900"],borderColor:s.colorsPalette["color-ripe-red-500"],iconColor:s.colorsPalette["color-ripe-red-500"],activeColor:n.sellActive},confirm:{disableBackgroundColor:s.colorsPalette["color-cold-gray-750"],backgroundColor:s.colorsPalette["color-ripe-red-500"],borderColor:s.colorsPalette["color-ripe-red-500"],textColor:s.colorsPalette["color-white"],disableTextColor:s.colorsPalette["color-cold-gray-600"],activeColor:s.colorsPalette["color-ripe-red-600"]},informer:{backgroundColor:n.errorNotificationBackdrop,iconColor:s.colorsPalette["color-ripe-red-500"]}},c={buy:{normal:l,disabled:(0,a.generateBlendColors)(l,n.overlay)},sell:{normal:d,disabled:(0,a.generateBlendColors)(d,n.overlay)}};var h=o(328969);const u={pointShadowColor:h.buyPointShadow,lineColor:s.colorsPalette["color-tv-blue-500"],borderBackgroundColor:s.colorsPalette["color-white"],borderColor:s.colorsPalette["color-tv-blue-500"],pointBackgroundColor:s.colorsPalette["color-white"],disabledLineColor:s.colorsPalette["color-tv-blue-100"],positivePlColor:s.colorsPalette["color-minty-green-500"],negativePlColor:s.colorsPalette["color-ripe-red-500"],qty:{backgroundColor:s.colorsPalette["color-tv-blue-500"],textColor:s.colorsPalette["color-white"],dividerColor:s.colorsPalette["color-tv-blue-500"],activeColor:s.colorsPalette["color-tv-blue-600"]},text:{backgroundColor:s.colorsPalette["color-white"],textColor:s.colorsPalette["color-cold-gray-300"],dividerColor:s.colorsPalette["color-tv-blue-50"],activeColor:n.buyActive},close:{backgroundColor:s.colorsPalette["color-white"],iconColor:s.colorsPalette["color-tv-blue-500"],activeColor:n.buyActive},reverse:{backgroundColor:s.colorsPalette["color-white"],borderColor:s.colorsPalette["color-tv-blue-500"],iconColor:s.colorsPalette["color-tv-blue-500"],activeColor:n.buyActive},confirm:{disableBackgroundColor:s.colorsPalette["color-cold-gray-150"],backgroundColor:s.colorsPalette["color-tv-blue-500"],borderColor:s.colorsPalette["color-tv-blue-500"],
textColor:s.colorsPalette["color-white"],disableTextColor:s.colorsPalette["color-cold-gray-300"],activeColor:s.colorsPalette["color-tv-blue-600"]},informer:{backgroundColor:n.errorNotificationBackdrop,iconColor:s.colorsPalette["color-ripe-red-500"]}},p={pointShadowColor:h.sellPointShadow,lineColor:s.colorsPalette["color-ripe-red-500"],borderBackgroundColor:s.colorsPalette["color-white"],borderColor:s.colorsPalette["color-ripe-red-500"],pointBackgroundColor:s.colorsPalette["color-white"],disabledLineColor:s.colorsPalette["color-ripe-red-100"],positivePlColor:s.colorsPalette["color-minty-green-500"],negativePlColor:s.colorsPalette["color-ripe-red-500"],qty:{backgroundColor:s.colorsPalette["color-ripe-red-500"],textColor:s.colorsPalette["color-white"],dividerColor:s.colorsPalette["color-ripe-red-500"],activeColor:s.colorsPalette["color-ripe-red-600"]},text:{backgroundColor:s.colorsPalette["color-white"],textColor:s.colorsPalette["color-cold-gray-300"],dividerColor:s.colorsPalette["color-ripe-red-50"],activeColor:n.buyActive},close:{backgroundColor:s.colorsPalette["color-white"],iconColor:s.colorsPalette["color-ripe-red-500"],activeColor:n.sellActive},reverse:{backgroundColor:s.colorsPalette["color-white"],borderColor:s.colorsPalette["color-ripe-red-500"],iconColor:s.colorsPalette["color-ripe-red-500"],activeColor:n.sellActive},confirm:{disableBackgroundColor:s.colorsPalette["color-cold-gray-150"],backgroundColor:s.colorsPalette["color-ripe-red-500"],borderColor:s.colorsPalette["color-ripe-red-500"],textColor:s.colorsPalette["color-white"],disableTextColor:s.colorsPalette["color-cold-gray-300"],activeColor:s.colorsPalette["color-ripe-red-600"]},informer:{backgroundColor:n.errorNotificationBackdrop,iconColor:s.colorsPalette["color-ripe-red-500"]}},_={buy:{normal:u,disabled:(0,a.generateBlendColors)(u,h.overlay)},sell:{normal:p,disabled:(0,a.generateBlendColors)(p,h.overlay)}};function y(t,e,o,s=r.LineStyle.Solid,a){const n=o()?c:_,l=o()?a?.darkTheme:a?.lightTheme,d=t?n.buy:n.sell,h=t?l?.buy:l?.sell,u=e?d.disabled:d.normal,p=e?h?.disabled:h?.normal;return(0,i.deepExtend)({},{...u,lineStyle:s,borderStyle:s,labelBorderVisible:!1,...p},a??{})}},842471:(t,e,o)=>{"use strict";o.d(e,{PreOrderItem:()=>d});var r=o(609838),i=o(813108),s=o(525915),a=o(99834),n=o(71991);const l=r.t(null,void 0,o(302873));class d extends a.OrderWithMenuItem{constructor(){super(...arguments),this._supportedOrderTypes=[]}qtyText(){return super._quantityText(this._data.qty)}style(){return this._infoGetters.style(this)}setSupportOrderType(t){this._supportedOrderTypes=t}canSwitchType(){return this._supportedOrderTypes.length>1}orderTypesItems(){return this._supportedOrderTypes.map((t=>({type:t,typeText:(0,s.orderTypeToText)({orderType:t})})))}isWorking(){return!0}profitLossText(t){return(0,s.orderTypeToText)({orderType:this.data().type,uppercase:t,shorten:t})}profitLossTooltip(){return l}lineStyle(){return i.LineStyle.Solid}supportClose(){return!0}supportModify(){return this._data.supportModify}supportModifyFromContextMenu(){return this._data.supportModify
}async onClose(t){this._source.onClose?.(t)}async onModify(t){return this._source.isPlaced()||(t.event="Edit project order"),this._source.onModify?.(t),!0}async onModifyFromContextMenu(t){return this.onModify(t)}supportBrackets(){return!this._isBracket()&&this._symbolDataProvider.supportOrderBrackets()}_calcId(){return n.preOrderItemId}}},28518:(t,e,o)=>{"use strict";o.d(e,{ProjectionBracketItem:()=>g});var r=o(650151),i=o(150335),s=o(86441),a=o(609838),n=o(41899),l=o(813108),d=o(912321),c=o(599016),h=o(46415),u=o(99834);const p=a.t(null,{context:"Stop loss"},o(867008)),_=a.t(null,{context:"Guaranteed stop"},o(389026)),y=a.t(null,{context:"Trailing stop"},o(335363)),m=a.t(null,{context:"Take profit"},o(607660)),f=a.t(null,void 0,o(734474)),b=a.t(null,void 0,o(221718)),C=a.t(null,void 0,o(424234)),v=a.t(null,void 0,o(154908));class g extends u.OrderWithMenuItem{style(){return this._infoGetters.style(this)}setPrice(t){const{minTick:e}=(0,r.ensureNotNull)(this._symbolDataProvider.symbolData());null!==t&&(t=(0,i.fixComputationError)(e*Math.round(t/e))),this.setData({...(0,n.clone)(this.data()),price:t})}data(){return super.data()}qtyText(){return this._bracketTypeText(!0)}tooltip(){switch(this.bracketType()){case c.BracketType.TakeProfit:return f;case c.BracketType.StopLoss:return b;case c.BracketType.GuaranteedStop:return C;case c.BracketType.TrailingStop:return v;default:throw new Error("Unknown bracket type")}}isWorking(){return!0}supportClose(){return!1}supportModify(){return!0}supportModifyFromContextMenu(){return!0}profitLossText(t){return!this._source.isBracketsPLVisible()?.value()||this.bracketType()===c.BracketType.TrailingStop||!this._source.supportRiskControlsAndInfo()&&this._infoGetters.displayMode.value()===d.PlDisplay.Money?"":super.profitLossText(t)}isMinifyMode(){return null===this.price()}bracketType(){return this.data().bracketType}async onMove(t){const e=new s.Point(t.localX,t.localY),o=this._mainSeries.priceScale(),i=this._mainSeries.firstValue();if(null===i)return;const a=o.coordinateToPrice(e.y,i);this.setPrice(a),t.isTouch&&(0,r.ensureDefined)(this._itemCommands.exitTrackingMode)()}lineStyle(){return l.LineStyle.Dotted}_isBracket(){return!0}_calcId(){return this._data.id}_bracketTypeText(t){return this._data?this.bracketType()===c.BracketType.TakeProfit?m:this._source.bracketStopType()===h.StopType.TrailingStop?y:this._source.bracketStopType()===h.StopType.GuaranteedStop?_:p:""}}},631488:(t,e,o)=>{"use strict";o.d(e,{calcPipsByPrice:()=>i,calcPriceByPips:()=>s});var r=o(960521);function i(t,e,o,i){return(0,r.Big)(t).minus(e).div(i).mul(1===o?1:-1).toNumber()}function s(t,e,o,i){return(0,r.Big)(1===o?1:-1).mul(t).mul(i).plus(e).toNumber()}},168873:(t,e,o)=>{"use strict";o.d(e,{mergeGaParams:()=>n});var r,i,s,a=o(650151);function n(t,e){(0,a.assert)(!(t.origin&&e.origin&&t.origin!==e.origin||t.event&&e.event&&t.event!==e.event),"origin and event should be only in one params object");const o={...t,...e};return t.label&&e.label&&(o.label=`${t.label} ${e.label}`),o}!function(t){
t.PlaceOrderDefault="Chart Place Order",t.OrderDefault="Chart Order",t.OrderContextMenu="Chart Order Context Menu",t.ProjectOrderContextMenu="Chart Project Order Context Menu",t.PositionDefault="Chart Position",t.PositionContextMenu="Chart Position Context Menu"}(r||(r={})),function(t){t.MoveOrder="Move Order",t.ModifyOrder="Modify order",t.AddBracket="Add bracket from btn",t.StopTypeMenuOpen="Stop loss type menu opening",t.OrderTypeMenuOpen="Order type menu opening",t.ChangeOrderType="Change order type",t.ChangeQty="Change quantity",t.CancelOrder="Cancel order",t.CancelBracket="Cancel bracket",t.ConfirmPlaceOrder="Order placed"}(i||(i={})),function(t){t.Single="single",t.Group="group",t.TakeProfit="take profit",t.StopLoss="stop loss",t.TrailingStop="trailing stop",t.GuaranteedStop="guaranteed stop",t.BothBracket="both bracket"}(s||(s={}))},112232:(t,e,o)=>{"use strict";o.d(e,{bracketsByType:()=>b,buildProjectionBracketData:()=>C,getBracketType:()=>m,getBracketTypeToText:()=>f,isParentItemSupportBracketModification:()=>v});var r=o(609838),i=o(391431),s=o(599016),a=o(46415),n=o(482303);const l=r.t(null,void 0,o(992201)),d=r.t(null,void 0,o(719702)),c=r.t(null,void 0,o(337539)),h=r.t(null,void 0,o(255739)),u=r.t(null,{context:"Trailing stop"},o(335363)),p=r.t(null,{context:"Stop loss"},o(867008)),_=r.t(null,{context:"Guaranteed stop"},o(389026)),y=r.t(null,{context:"Take profit"},o(607660));function m(t){return 3===t.type?void 0!==t.stopType?(0,i.getBracketTypeByStopType)(t.stopType):s.BracketType.StopLoss:1===t.type?s.BracketType.TakeProfit:null}function f(t,e,o){return 3===t?function(t,e){return t===a.StopType.TrailingStop?e?u:l:t===a.StopType.GuaranteedStop?e?_:c:e?p:d}(e,o):1===t?o?y:h:null}function b(t){let e,o,r,i;return t.forEach((t=>{switch((0,n.isOrderLikeItem)(t)?t.bracketType():m(t)){case s.BracketType.TakeProfit:e=t;break;case s.BracketType.TrailingStop:i=t;break;case s.BracketType.StopLoss:o=t;break;case s.BracketType.GuaranteedStop:r=t}})),{takeProfit:e,trailingStop:i,stopLoss:o,guaranteedStop:r}}function C(t,e,o){const r=(0,i.getStopTypeByBracketType)(e),a=e===s.BracketType.TakeProfit?1:3,n=-1===o.side?1:-1;let l;switch(e){case s.BracketType.TakeProfit:l=`${t}TakeProfit`;break;case s.BracketType.StopLoss:l=`${t}StopLoss`;break;case s.BracketType.GuaranteedStop:l=`${t}GuaranteedStop`;break;case s.BracketType.TrailingStop:l=`${t}TrailingStop`}return{dataType:"Order",id:l,price:null,type:a,stopType:r,side:n,bracketType:e,qty:o.qty,status:6,symbol:o.symbol,plBasedOnLast:"plBasedOnLast"in o&&o.plBasedOnLast,considerFilledQty:!1,supportModify:!1,supportModifyOrderPrice:!1,supportMove:!0,supportCancel:!1,supportStopLoss:!1,supportTrailingStop:!1,supportGuaranteedStop:!1,callbacks:{},trailingStopPips:o.trailingStopPips}}function v(t,e){const o=t.mainItem();return void 0===o?e.supportModifyBrackets():!1===t.isPlaced()||((0,n.isPositionLikeItem)(o)?e.supportModifyPositionBrackets():e.supportModifyOrderBrackets())}},586137:(t,e,o)=>{"use strict";o.d(e,{createContextMenuTitle:()=>c,createNoOverlapMenuAction:()=>l,
showContextMenu:()=>d});var r=o(609838),i=o(621327),s=o(529596),a=o(168873);const n=r.t(null,void 0,o(825586));function l(t,e,o){const r=(0,a.mergeGaParams)(e,{event:"No overlap orders and positions"});return new i.Action({actionId:"Trading.NoOverlapMode",options:{label:n,onExecute:()=>{o(r),t.setNoOverlapMode(!0)},disabled:!t.isItemsOverlap()}})}async function d(t,e,o,r){return s.ContextMenuManager.createMenu(t,{},{...r,menuName:"TradingOrderContextMenu"},o).then((t=>(t.show(e),t)))}async function c(t,e){return null}},333433:(t,e,o)=>{"use strict";o.d(e,{closeDropdownMenu:()=>C,updateDropdownMenu:()=>v});var r=o(50959),i=o(632227),s=o(86441),a=o(650151),n=o(930202),l=o(914090),d=o(734602),c=o(930052),h=o(759339),u=o(163694),p=o(924910),_=o(192063),y=o(722426);const m=new WeakMap;function f(t){const{position:e,activeItemIndex:o,title:i,onClose:s,onDestroy:a,onSelect:f,customCloseSubscriptions:b=[]}=t,[C,v]=(0,r.useState)(o);(0,d.useGlobalCloseOnScrollAndCustomEvents)(b);const g=t.items.map(((e,o)=>(m.has(e)||m.set(e,(0,p.randomHash)()),r.createElement(_.PopupMenuItem,{key:m.get(e),onClick:()=>function(e){v(e),f(t.items[e].value),a()}(o),label:e.title,isActive:o===C}))));return r.createElement(u.DrawerManager,null,r.createElement(c.MatchMedia,{rule:"(max-width: 440px)"},(t=>t?r.createElement(h.Drawer,{onClose:s,position:"Bottom"},r.createElement(y.ToolWidgetMenuSummary,null,i),g):r.createElement(l.TooltipPopup,{position:e,onClose:s,onClickOutside:P,onForceClose:a,onKeyDown:k},g))));function P(t){s(t)}function k(e){switch((0,n.hashFromEvent)(e)){case 40:e.preventDefault(),e.stopPropagation(),function(){const e=C+1>t.items.length-1?0:C+1;v(e)}();break;case 38:e.preventDefault(),e.stopPropagation(),function(){const e=C-1<0?t.items.length-1:C-1;v(e)}();break;case 13:e.preventDefault(),e.stopPropagation(),f(t.items[C].value),a()}}}const b=document.createElement("div");function C(){i.unmountComponentAtNode((0,a.ensureNotNull)(b))}function v(t,e,o,n,l,d,c,h,u){if(!t)return;const p={items:o,activeItemIndex:n,title:l,onClose:function(t){if(t){const e=t.target.getBoundingClientRect()||{left:0,top:0};h(new s.Point(t.clientX-e.left,t.clientY-e.top))}i.unmountComponentAtNode((0,a.ensureNotNull)(b))},onDestroy:function(){u(),i.unmountComponentAtNode((0,a.ensureNotNull)(b))},onSelect:c,customCloseSubscriptions:d,position:e};i.render(r.createElement(f,{...p}),b)}},805033:(t,e,o)=>{"use strict";o.d(e,{drawArrow:()=>l,drawErrorNotificationBackdrop:()=>_,drawExclamationIcon:()=>f,drawHalfArrow:()=>c,drawOutlinedArrowHead:()=>d,drawOutlinedText:()=>p,drawText:()=>h,drawUnderlinedText:()=>u});var r,i,s=o(813108),a=o(898646),n=o(652283);function l(t,e,o,r,i,s,a,n,l){t.save();const d=t.lineWidth,c=t.lineWidth%2?.5:0;t.translate(e+c,o+c),s&&t.scale(1,-1);const h=Math.round(n/2),u=h,p=h;l&&(t.strokeStyle=i,t.lineWidth=2*t.lineWidth,t.lineCap="square",t.beginPath(),t.moveTo(0,h),t.lineTo(u,0),t.lineTo(n,h),t.stroke(),t.lineCap="butt",t.beginPath(),t.moveTo(p,a+(t.lineWidth-d)/2),t.lineTo(u,0),t.stroke()),t.lineWidth=d,t.strokeStyle=r,
t.lineCap="square",t.beginPath(),t.moveTo(0,h),t.lineTo(u,0),t.lineTo(n,h),t.stroke(),t.lineCap="butt",t.beginPath(),t.moveTo(p,a),t.lineTo(u,0),t.stroke(),t.restore()}function d(t,e,o,r,i,s,a,n=90){t.save();const l=t.lineWidth,d=2*t.lineWidth,c=t.lineWidth%2?.5:0;t.translate(e+c+a/2-l,o+c),t.rotate(n*Math.PI/180);const h=Math.round(a/2);t.lineCap="square",t.lineWidth=d,t.strokeStyle=i,t.beginPath(),t.moveTo(0,h),t.lineTo(h,0),t.lineTo(a,h),t.stroke(),t.lineCap="square",t.lineWidth=l,t.strokeStyle=r,t.beginPath(),t.moveTo(0,h),t.lineTo(h,0),t.lineTo(a,h),t.stroke(),t.restore()}function c(t,e,o,r,i,s,a){t.save(),t.strokeStyle=r,t.lineJoin="miter";const n=t.lineWidth%2?.5:0,l=Math.round(s/3);t.translate(e+n,o+n),i&&t.scale(-1,-1),t.beginPath(),t.moveTo(0,s),t.lineTo(0,0),t.lineTo(-a,l),t.stroke(),t.restore()}function h(t,e,o,r,i,s,a,l,d="center"){t.save(),t.textAlign=d,t.textBaseline="middle",t.fillStyle=a,t.font=l,t.translate(r,i+s),(0,n.drawScaled)(t,e.horizontalPixelRatio,e.verticalPixelRatio,(()=>{t.fillText(o,0,0)})),t.restore()}function u(t,e,o,r,i,l,d,c,h){t.save(),t.textAlign="center",t.textBaseline="middle",t.fillStyle=d,t.font=c;const u=1,p=1,{horizontalPixelRatio:_,verticalPixelRatio:y}=(s.LineStyle.Dashed,e),m=Math.round(10*y)/2,f=Math.max(1,Math.floor(p*_)),b=h*_,C=Math.max(1,Math.floor(u*y)),v=Math.round(r-b/2),g=Math.round(i+m+C);t.strokeStyle=d,t.lineWidth=f;const P=Math.round(3*t.lineWidth),k=Math.round(3*t.lineWidth/2),T=Math.round(2*t.lineWidth);let x=Math.trunc(b/(P+T));const M=Math.trunc(b%(P+T)/2),w=[];for(w.push(k+M);x>1;)w.push(T,P),x--;w.push(T,k+M),t.setLineDash(w),(0,a.drawHorizontalLine)(t,g,v,v+Math.round(b)),t.translate(r,i+l),(0,n.drawScaled)(t,_,y,(()=>{t.fillText(o,0,0)})),t.restore()}function p(t,e,o,r,i,s,a,l,d){t.save(),t.textAlign="center",t.textBaseline="middle",t.fillStyle=a,t.font=l,t.lineJoin="round",t.translate(r,i+s),(0,n.drawScaled)(t,e.horizontalPixelRatio,e.verticalPixelRatio,(()=>{t.strokeStyle=d,t.strokeText(o,0,0),t.fillText(o,0,0)})),t.restore()}function _(t){const{ctx:e,x:o,y:r,w:i,h:s,horizontalPixelRatio:a,verticalPixelRatio:n}=t;e.fillStyle="rgba(242, 54, 69, 0.1)",e.fillRect(o,r-s*n/2,i*a,s*n)}!function(t){t.TextAlign="center",t.TextBaseline="middle",t.FontWeight="normal",t[t.DashWidth=3]="DashWidth",t[t.SpaceWidth=2]="SpaceWidth"}(r||(r={})),function(t){t[t.Up=0]="Up",t[t.Right=90]="Right",t[t.Down=180]="Down",t[t.Left=-90]="Left"}(i||(i={}));const y=new Path2D("M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16ZM9 4c-.79 0-1.38.7-1.25 1.48l.67 4.03a.59.59 0 0 0 1.16 0l.67-4.03A1.27 1.27 0 0 0 9 4Zm0 8a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"),m=18;function f(t){const{ctx:e,x:o,y:r,horizontalPixelRatio:i,verticalPixelRatio:s}=t;e.save(),e.translate(o,r),(0,n.drawScaled)(e,i,s,(()=>{e.fillStyle="white",e.beginPath(),e.arc(m/2,m/2,m/2-2,0,2*Math.PI,!0),e.fill(),e.fillStyle="rgba(242, 54, 69, 1)",e.fill(y,"evenodd")})),e.restore()}},619624:(t,e,o)=>{"use strict";o.d(e,{generateOrderColorTheme:()=>i});var r=o(316167);function i(t){
const{text:e,buySecondary:o,buyDisabled:i,buyPrimary:s,confirmActiveBuy:a,textDisabled:n,backgroundDisabled:l,background:d,takeProfitPrimary:c,sellSecondary:h,sellDisabled:u,sellPrimary:p,confirmActiveSell:_,stopLossPrimary:y,takeProfitSecondary:m,stopLossSecondary:f,buyActive:b,sellActive:C,takeProfitActive:v,stopLossActive:g,buyPointShadow:P,sellPointShadow:k,takeProfitPointShadow:T,stopLossPointShadow:x,overlay:M,errorNotificationBackdrop:w}=t,S={pointShadowColor:P,labelTickColor:s,lineColor:s,borderBackgroundColor:d,borderColor:s,pointBackgroundColor:d,disabledLineColor:i,positivePlColor:c,negativePlColor:p,qty:{backgroundColor:d,textColor:s,dividerColor:o,activeColor:b},text:{backgroundColor:d,textColor:s,dividerColor:o,buttonTextColor:s,activeColor:b},close:{backgroundColor:d,iconColor:s,activeColor:b},confirm:{disableBackgroundColor:l,backgroundColor:s,borderColor:s,textColor:e,disableTextColor:n,activeColor:a},informer:{backgroundColor:w,iconColor:p},anchor:{backgroundColor:d,borderColor:s,shadowColor:P}},B={pointShadowColor:k,labelTickColor:p,lineColor:p,borderBackgroundColor:d,borderColor:p,pointBackgroundColor:d,disabledLineColor:u,positivePlColor:c,negativePlColor:p,qty:{backgroundColor:d,textColor:p,dividerColor:h,activeColor:C},text:{backgroundColor:d,textColor:p,dividerColor:h,buttonTextColor:p,activeColor:C},close:{backgroundColor:d,iconColor:p,activeColor:C},confirm:{disableBackgroundColor:l,backgroundColor:p,borderColor:p,textColor:e,disableTextColor:n,activeColor:_},informer:{backgroundColor:w,iconColor:p},anchor:{backgroundColor:d,borderColor:s,shadowColor:P}},I={pointShadowColor:T,labelTickColor:c,lineColor:c,borderBackgroundColor:d,borderColor:c,pointBackgroundColor:d,positivePlColor:c,negativePlColor:p,qty:{backgroundColor:d,textColor:c,dividerColor:m,activeColor:v},text:{backgroundColor:d,textColor:c,dividerColor:m,buttonTextColor:c,activeColor:v},close:{backgroundColor:d,iconColor:c,activeColor:v},informer:{backgroundColor:w,iconColor:p},anchor:{backgroundColor:d,borderColor:s,shadowColor:P}},D={pointShadowColor:x,labelTickColor:y,lineColor:y,borderBackgroundColor:d,borderColor:y,pointBackgroundColor:d,positivePlColor:c,negativePlColor:p,qty:{backgroundColor:d,textColor:y,dividerColor:f,activeColor:g},text:{backgroundColor:d,textColor:y,dividerColor:f,buttonTextColor:y,activeColor:g},close:{backgroundColor:d,iconColor:y,activeColor:g},informer:{backgroundColor:w,iconColor:p},anchor:{backgroundColor:d,borderColor:s,shadowColor:P}};return{buy:{normal:S,disabled:(0,r.generateBlendColors)(S,M)},sell:{normal:B,disabled:(0,r.generateBlendColors)(B,M)},takeProfit:{normal:I,disabled:(0,r.generateBlendColors)(I,M)},stopLoss:{normal:D,disabled:(0,r.generateBlendColors)(D,M)}}}},90470:(t,e,o)=>{"use strict";o.d(e,{isTradingObjVisibleOnScreenshot:()=>i});var r=o(440891);function i(){return r.enabled("snapshot_trading_drawings")}}}]);