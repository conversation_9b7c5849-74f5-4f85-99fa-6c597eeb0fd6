(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1583],{67534:(e,t,i)=>{"use strict";i.r(t),i.d(t,{Pattern5pointsPaneView:()=>g});var n=i(936879),r=i(986226),s=i(586982),o=i(69549),a=i(454304),l=i(402359),d=i(659011),h=i(251940),c=i(184114),u=i(306099),_=i(895379),p=i(799839);class g extends _.LineSourcePaneView{constructor(e,t){super(e,t),this._abRetracement=NaN,this._bcRetracement=NaN,this._cdRetracement=NaN,this._xdRetracement=NaN,this._bcRetracementTrend=new d.TrendLineRenderer,this._xdRetracementTrend=new d.TrendLineRenderer,this._xbTrend=new d.TrendLineRenderer,this._bdTrend=new d.TrendLineRenderer,this._polylineRenderer=new u.PolygonRenderer(new c.HitTestResult(c.HitTarget.MovePoint)),this._mainTriangleRenderer=new l.TriangleRenderer,this._triangleRendererPoints234=new l.TriangleRenderer,this._xbLabelRenderer=new a.TextRenderer,this._acLabelRenderer=new a.TextRenderer,this._bdLabelRenderer=new a.TextRenderer,this._xdLabelRenderer=new a.TextRenderer,this._textRendererALabel=new a.TextRenderer,this._textRendererBLabel=new a.TextRenderer,this._textRendererCLabel=new a.TextRenderer,this._textRendererDLabel=new a.TextRenderer,this._textRendererXLabel=new a.TextRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._updateBaseData(),this._renderer=null,this._points.length<2)return;const t=this._source.properties().childs(),i=new o.CompositeRenderer,a=(e,i)=>({points:[e],text:i,color:t.textcolor.value(),vertAlign:r.VerticalAlign.Middle,horzAlign:r.HorizontalAlign.Center,font:p.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:t.bold&&t.bold.value(),italic:t.italic&&t.italic.value(),fontsize:t.fontsize.value(),backgroundColor:t.color.value(),backgroundRoundRect:4}),l=(e,i)=>({points:[e,i],color:t.color.value(),linewidth:1,linestyle:n.LINESTYLE_DOTTED,extendleft:!1,extendright:!1,leftend:s.LineEnd.Normal,rightend:s.LineEnd.Normal}),[d,c,u,_,g]=this._points,f={points:[d,c,this._points.length<3?c:u],color:"rgba(0, 0, 0, 0)",linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),fillBackground:t.fillBackground.value(),transparency:t.transparency.value()};if(this._mainTriangleRenderer.setData(f),i.append(this._mainTriangleRenderer),this._points.length>3){const e={points:[u,_,5===this._points.length?g:_],color:"rgba(0, 0, 0, 0)",linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),fillBackground:t.fillBackground.value(),transparency:t.transparency.value()};this._triangleRendererPoints234.setData(e),i.append(this._triangleRendererPoints234)}const x={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),fillBackground:!1,linestyle:n.LINESTYLE_SOLID,filled:!1};this._polylineRenderer.setData(x),i.append(this._polylineRenderer);const v=(0,h.getNumericFormatter)();if(this._points.length>=3){const e=a(d.add(u).scaled(.5),v.format(this._abRetracement));this._xbLabelRenderer.setData(e),i.append(this._xbLabelRenderer),this._xbTrend.setData(l(d,u)),
i.append(this._xbTrend)}if(this._points.length>=4){this._bcRetracementTrend.setData(l(c,_)),i.append(this._bcRetracementTrend);const e=a(c.add(_).scaled(.5),v.format(this._bcRetracement));this._acLabelRenderer.setData(e),i.append(this._acLabelRenderer)}if(this._points.length>=5){const e=a(u.add(g).scaled(.5),v.format(this._cdRetracement));this._bdLabelRenderer.setData(e),i.append(this._bdLabelRenderer),this._xdRetracementTrend.setData(l(d,g)),i.append(this._xdRetracementTrend);const t=a(d.add(g).scaled(.5),v.format(this._xdRetracement));this._xdLabelRenderer.setData(t),i.append(this._xdLabelRenderer),this._bdTrend.setData(l(u,g)),i.append(this._bdTrend)}const w=a(d,"X");c.y>d.y?(w.vertAlign=r.VerticalAlign.Bottom,w.offsetY=5):(w.vertAlign=r.VerticalAlign.Top,w.offsetY=5),this._textRendererXLabel.setData(w),i.append(this._textRendererXLabel);const R=a(c,"A");if(c.y<d.y?(R.vertAlign=r.VerticalAlign.Bottom,R.offsetY=5):(R.vertAlign=r.VerticalAlign.Top,R.offsetY=5),this._textRendererALabel.setData(R),i.append(this._textRendererALabel),this._points.length>2){const e=a(u,"B");u.y<c.y?(e.vertAlign=r.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=r.VerticalAlign.Top,e.offsetY=5),this._textRendererBLabel.setData(e),i.append(this._textRendererBLabel)}if(this._points.length>3){const e=a(_,"C");_.y<u.y?(e.vertAlign=r.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=r.VerticalAlign.Top,e.offsetY=5),this._textRendererCLabel.setData(e),i.append(this._textRendererCLabel)}if(this._points.length>4){const e=a(g,"D");g.y<_.y?(e.vertAlign=r.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=r.VerticalAlign.Top,e.offsetY=5),this._textRendererDLabel.setData(e),i.append(this._textRendererDLabel)}this.addAnchors(i),this._renderer=i}_updateBaseData(){if(this._source.points().length>=3){const[e,t,i]=this._source.points();this._abRetracement=Math.round(1e3*Math.abs((i.price-t.price)/(t.price-e.price)))/1e3}if(this._source.points().length>=4){const[,e,t,i]=this._source.points();this._bcRetracement=Math.round(1e3*Math.abs((i.price-t.price)/(t.price-e.price)))/1e3}if(this._source.points().length>=5){const[e,t,i,n,r]=this._source.points();this._cdRetracement=Math.round(1e3*Math.abs((r.price-n.price)/(n.price-i.price)))/1e3,this._xdRetracement=Math.round(1e3*Math.abs((r.price-t.price)/(t.price-e.price)))/1e3}}}},934666:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ABCDPaneView:()=>p});var n=i(986226),r=i(936879),s=i(69549),o=i(251940),a=i(659011),l=i(454304),d=i(586982),h=i(306099),c=i(184114),u=i(895379),_=i(799839);class p extends u.LineSourcePaneView{constructor(){super(...arguments),this._abRetracementTrend=new a.TrendLineRenderer,this._cdRetracementTrend=new a.TrendLineRenderer,this._polylineRenderer=new h.PolygonRenderer(new c.HitTestResult(c.HitTarget.MovePoint)),this._abLabelRenderer=new l.TextRenderer,this._cdLabelRenderer=new l.TextRenderer,this._textRendererALabel=new l.TextRenderer,this._textRendererBLabel=new l.TextRenderer,this._textRendererCLabel=new l.TextRenderer,this._textRendererDLabel=new l.TextRenderer,this._renderer=null}renderer(e){
return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._points.length<2)return void(this._renderer=null);const t=this._source.properties().childs(),i=new s.CompositeRenderer,a=(e,i)=>({points:[e],text:i,color:t.textcolor.value(),vertAlign:n.VerticalAlign.Middle,horzAlign:n.HorizontalAlign.Center,font:_.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:t.bold&&t.bold.value(),italic:t.italic&&t.italic.value(),fontsize:t.fontsize.value(),backgroundColor:t.color.value(),backgroundRoundRect:4}),l=(e,i)=>({points:[e,i],color:t.color.value(),linewidth:t.linewidth.value(),linestyle:r.LINESTYLE_DOTTED,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal}),[h,c,u,p]=this._points,g={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),linestyle:r.LINESTYLE_SOLID,fillBackground:!1,filled:!1,backcolor:"rgba(0, 0, 0, 0)"};this._polylineRenderer.setData(g),i.append(this._polylineRenderer);const f=a(h,"A");c.y>h.y?(f.vertAlign=n.VerticalAlign.Bottom,f.offsetY=5):(f.vertAlign=n.VerticalAlign.Top,f.offsetY=5),this._textRendererALabel.setData(f),i.append(this._textRendererALabel);const x=a(c,"B");if(c.y<h.y?(x.vertAlign=n.VerticalAlign.Bottom,x.offsetY=5):(x.vertAlign=n.VerticalAlign.Top,x.offsetY=5),this._textRendererBLabel.setData(x),i.append(this._textRendererBLabel),this._points.length>2){const e=a(u,"C");u.y<c.y?(e.vertAlign=n.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=n.VerticalAlign.Top,e.offsetY=5),this._textRendererCLabel.setData(e),i.append(this._textRendererCLabel)}if(this._points.length>3){const e=a(p,"D");p.y<u.y?(e.vertAlign=n.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=n.VerticalAlign.Top,e.offsetY=5),this._textRendererDLabel.setData(e),i.append(this._textRendererDLabel)}const v=(0,o.getNumericFormatter)();if(this._points.length>=3){this._abRetracementTrend.setData(l(h,u)),i.append(this._abRetracementTrend);const e=h.add(u).scaled(.5),[t,n,r]=this._source.points(),s=Math.round(1e3*Math.abs((r.price-n.price)/(n.price-t.price)))/1e3,o=a(e,v.format(s));this._abLabelRenderer.setData(o),i.append(this._abLabelRenderer)}if(this._points.length>=4){this._cdRetracementTrend.setData(l(c,p)),i.append(this._cdRetracementTrend);const e=c.add(p).scaled(.5),[,t,n,r]=this._source.points(),s=Math.round(1e3*Math.abs((r.price-n.price)/(n.price-t.price)))/1e3,o=a(e,v.format(s));this._cdLabelRenderer.setData(o),i.append(this._cdLabelRenderer)}this.addAnchors(i),this._renderer=i}}},931761:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ArcPaneView:()=>p});var n=i(204652),r=i(86441),s=i(625422),o=i(69549),a=i(144044),l=i(895379),d=i(589637),h=i(184114),c=i(767313),u=i(161656);class _ extends c.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data={...e,angleFrom:0,angleTo:Math.PI,clockwise:!1}}hitTest(e){if(null===this._data||this._data.points.length<3)return null;const t=(0,u.interactionTolerance)().curve,i=this._data.points[0],o=this._data.points[1];let a=this._data.points[2],l=(0,
n.distanceToLine)(i,o,a).distance;if(l<1)return l=(0,n.distanceToLine)(i,o,e).distance,l<t?new h.HitTestResult(h.HitTarget.MovePoint):null;const d=o.subtract(i),c=d.length(),_=i.add(o).scaled(.5);let p=a.subtract(_).normalized();a=_.add(p.scaled(l));const g=d.x/c,f=d.y/c;let x=Math.acos(g);f<0&&(x=-x);let v=(0,s.translationMatrix)(-i.x,-i.y);e=(0,s.transformPoint)(v,e),v=(0,s.rotationMatrix)(-x),e=(0,s.transformPoint)(v,e),p=(0,s.transformPoint)(v,p);const w=1-Math.sqrt(3)/2;if(v=(0,s.scalingMatrix)(1,c*w/l),e=(0,s.transformPoint)(v,e),p=(0,s.transformPoint)(v,p),e.y*p.y<0)return null;let R;R=e.y<0?new r.Point(.5*c,c*Math.sqrt(3)/2):new r.Point(.5*c,-c*Math.sqrt(3)/2);const T=e.subtract(R).length();return Math.abs(T-c)<=t?new h.HitTestResult(h.HitTarget.MovePoint):null}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=e.context,i=this._data.points[0],o=this._data.points[1];if(this._data.points.length<3)return t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,t.beginPath(),t.moveTo(i.x,i.y),t.lineTo(o.x,o.y),void t.stroke();let a=this._data.points[2];const l=(0,n.distanceToLine)(i,o,a).distance;if(l<1)return t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,t.beginPath(),t.moveTo(i.x,i.y),t.lineTo(o.x,o.y),void t.stroke();const h=o.subtract(i),c=i.add(o).scaled(.5),u=new r.Point(-h.y,h.x).normalized();a=c.add(u.scaled(l)),t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth;const _=h.length(),p=h.x/_,g=h.y/_;let f=Math.acos(p);g<0&&(f=-f);let x=this._data.points[2],v=(0,s.translationMatrix)(-c.x,-c.y);x=(0,s.transformPoint)(v,x),v=(0,s.rotationMatrix)(-f),x=(0,s.transformPoint)(v,x),v=(0,s.scalingMatrix)(1,_/(2*l)),x=(0,s.transformPoint)(v,x),x.y<0?this._data.clockwise=!0:this._data.clockwise=!1,t.save(),t.beginPath(),t.translate(i.x,i.y),t.rotate(f);const w=1-Math.sqrt(3)/2;t.scale(1,l/(_*w)),this._data.clockwise?t.arc(.5*_,_*Math.sqrt(3)/2,_,-2*Math.PI/3,-Math.PI/3,!1):t.arc(.5*_,-_*Math.sqrt(3)/2,_,Math.PI/3,2*Math.PI/3,!1),t.restore(),t.stroke(),this._data.fillBackground&&(t.fillStyle=(0,d.generateColor)(this._data.backcolor,this._data.transparency),t.fill())}}class p extends l.LineSourcePaneView{constructor(){super(...arguments),this._arcRenderer=new _,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,0===this._points.length)return;const t=this._source.properties().childs(),i={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),fillBackground:t.fillBackground.value(),transparency:t.transparency.value()};this._arcRenderer.setData(i);const d=new o.CompositeRenderer;if(this._renderer=d,d.append(this._arcRenderer),1===i.points.length)return;const h=[],c=i.points[0],u=(0,a.lineSourcePaneViewPointToLineAnchorPoint)(c,0);h.push(u);const _=i.points[1],p=(0,a.lineSourcePaneViewPointToLineAnchorPoint)(_,1);if(2===i.points.length)return void this.addAnchors(d);h.push(p);let g=i.points[2];const f=(0,
n.distanceToLine)(c,_,g).distance,x=_.subtract(c),v=c.add(_).scaled(.5),w=new r.Point(-x.y,x.x).normalized();g=v.add(w.scaled(f));const R=v.add(w.scaled(-f)),T=x.length(),m=x.x/T,y=x.y/T;let P=Math.acos(m);y<0&&(P=-P);let L=i.points[2],b=(0,s.translationMatrix)(-v.x,-v.y);L=(0,s.transformPoint)(b,L),b=(0,s.rotationMatrix)(-P),L=(0,s.transformPoint)(b,L),b=(0,s.scalingMatrix)(1,T/(2*f)),L=(0,s.transformPoint)(b,L);const S=(0,a.lineSourcePaneViewPointToLineAnchorPoint)(L.y>=0?g:R,2,(0,l.thirdPointCursorType)(c,_));h.push(S),d.append(this.createLineAnchor({points:h},0))}}},946576:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ArrowMarkPaneView:()=>_});var n,r=i(799839),s=i(895379),o=i(69549),a=i(652374),l=i(454304),d=i(184114),h=i(934026),c=i(822960);!function(e){e[e.ArrowHeadWidth=19.5]="ArrowHeadWidth",e[e.ArrowHeadHeight=12]="ArrowHeadHeight",e[e.ArrowTailWidth=10]="ArrowTailWidth",e[e.ArrowTailHeight=10]="ArrowTailHeight"}(n||(n={}));class u{constructor(){this._data=null}setData(e){this._data=e}draw(e,t){if(null!==this._data){switch(e.save(),e.fillStyle=this._data.color,this._data.direction){case"up":case"down":!function(e,t,i,n){const{horizontalPixelRatio:r,verticalPixelRatio:s}=n,o=Math.max(1,Math.floor(r))%2?.5:0,a="up"===i?1:-1,l=a*Math.round(12*s),d=(0,c.ceiledEven)(19.5*r)/2+o,h=a*Math.round(10*s),u=(0,c.ceiledEven)(10*r)/2+o,_=Math.round(t.x*r)+o,p=Math.round(t.y*s);e.beginPath(),e.moveTo(_,p),e.lineTo(_+d,p+l),e.lineTo(_+u,p+l),e.lineTo(_+u,p+l+h),e.lineTo(_-u,p+l+h),e.lineTo(_-u,p+l),e.lineTo(_-d,p+l),e.moveTo(_,p),e.fill()}(e,this._data.point,this._data.direction,t);break;case"left":case"right":!function(e,t,i,n){const{horizontalPixelRatio:r,verticalPixelRatio:s}=n,o=Math.max(1,Math.floor(r))%2?.5:0,a="left"===i?1:-1,l=a*Math.round(12*s)+o,d=(0,c.ceiledEven)(19.5*r)/2+o,h=a*Math.round(22*s)+o,u=(0,c.ceiledEven)(10*r)/2+o,_=Math.round(t.x*r)+o,p=Math.round(t.y*s)+o;e.beginPath(),e.moveTo(_,p),e.lineTo(_+l,p+d),e.lineTo(_+l,p+u),e.lineTo(_+h,p+u),e.lineTo(_+h,p-u),e.lineTo(_+l,p-u),e.lineTo(_+l,p-d),e.moveTo(_,p),e.fill()}(e,this._data.point,this._data.direction,t)}e.restore()}}hitTest(e){if(null===this._data)return null;let t,i,n,r;switch(this._data.direction){case"up":t=this._data.point.x-9.75,n=t+19.5,i=this._data.point.y,r=i+12+10;break;case"down":t=this._data.point.x-9.75,n=t+19.5,r=this._data.point.y,i=r-12-10;break;case"left":t=this._data.point.x,n=t+12+10,i=this._data.point.y-9.75,r=i+19.5;break;case"right":n=this._data.point.x,t=n-12-10,i=this._data.point.y-9.75,r=i+19.5}return e.x<t||e.x>n||e.y<i||e.y>r?null:new d.HitTestResult(d.HitTarget.MovePoint)}doesIntersectWithBox(e){return null!==this._data&&(0,h.pointInBox)(this._data.point,e)}}class _ extends s.LineSourcePaneView{constructor(){super(...arguments),this._arrowMarkRenderer=new u,this._textRenderer=new l.TextRenderer,this._renderer=null,this._anchorsOffset=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,1!==this._points.length)return
;const t=this._getSource(),i=t.properties().childs(),n=this._getModel();this._arrowMarkRenderer.setData({point:this._points[0],direction:t.direction(),color:i.arrowColor.value()}),this._renderer=new o.CompositeRenderer,this._renderer.append(this._arrowMarkRenderer),""!==i.text.value()&&i.showLabel.value()&&(this._textRenderer.setData({points:this._points,font:r.CHART_FONT_FAMILY,bold:i.bold.value(),italic:i.italic.value(),fontSize:i.fontsize.value(),text:i.text.value(),color:i.color.value(),...t.textAlignParams()}),this._renderer.append(this._textRenderer));const s=[this._anchorsOffset?this._points[0].add(this._anchorsOffset):this._points[0].clone()];this._renderer.append(new a.SelectionRenderer({points:s.map((e=>({point:e}))),bgColors:this._lineAnchorColors(s),visible:this.areAnchorsVisible(),barSpacing:n.timeScale().barSpacing(),hittestResult:d.HitTarget.MovePoint}))}}},38059:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ArrowMarkerPaneView:()=>x});var n,r=i(986226),s=i(895379),o=i(69549),a=i(454304),l=i(86441),d=i(767313),h=i(184114),c=i(161656);function u(e){if(e<92)return 18;let t=.25*e;return t=Math.min(t,106),t=Math.max(t,18),t=Math.min(t,.9*e),t}!function(e){e[e.LengthToLineWidthCoeff=.02]="LengthToLineWidthCoeff",e[e.MinLineWidth=2]="MinLineWidth",e[e.MaxLineWidth=5]="MaxLineWidth",e[e.ThresholdLineLength=92]="ThresholdLineLength",e[e.MinimalArrowLength=18]="MinimalArrowLength",e[e.MinimalLineLength=22]="MinimalLineLength",e[e.MaximumArrowLength=106]="MaximumArrowLength",e[e.LengthToArrowLengthCoeff=.25]="LengthToArrowLengthCoeff",e[e.MaximalPercentOfArrow=.9]="MaximalPercentOfArrow",e[e.ArrowWidthToHeight=1.22]="ArrowWidthToHeight",e[e.ArrowBackStep=.1]="ArrowBackStep",e[e.ArrowBackStepLengthLimit=35]="ArrowBackStepLengthLimit"}(n||(n={}));class _ extends d.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=e}setData(e){this._data=e}hitTest(e){if(this._data.points.length<2)return null;let t=this._data.points[0],i=this._data.points[1].subtract(t);const n=i.length();i=this._data.points[1].subtract(this._data.points[0]);i.length()<22&&(t=this._data.points[1].addScaled(i.normalized(),-22),i=this._data.points[1].subtract(t));const r=e.subtract(t),s=i.dotProduct(r)/n;if(s<0||s>n)return null;const o=i.scaled(1/n),a=t.addScaled(o,s),l=e.subtract(a),d=(0,c.interactionTolerance)().line,u=this._hittestGeometry(n);for(let e=u.length-2;e>=0;e--){const t=u[e];if(s>=t.x){const i=u[e+1],n=i.x-t.x,r=i.y-t.y,o=(s-t.x)/n,a=t.y+r*o;return l.length()<=a+d?new h.HitTestResult(h.HitTarget.MovePoint):null}}return l.length()<3?new h.HitTestResult(h.HitTarget.MovePoint):null}_drawImpl(e){if(this._data.points.length<2)return;const t=e.context;t.fillStyle=this._data.color,t.strokeStyle=this._data.color,t.lineJoin="round",t.lineCap="round";let i=this._data.points[1].subtract(this._data.points[0]);const n=i.length();let r=this._data.points[0];n<22&&(r=this._data.points[1].addScaled(i.normalized(),-22),i=this._data.points[1].subtract(r));const s=new l.Point(i.y,-i.x).normalized(),o=this._arrowGeometry(i.length()),a=i.normalized()
;t.lineWidth=function(e){let t=Math.round(.02*e);return t=Math.min(t,5),t=Math.max(t,2),t}(i.length()),t.beginPath(),t.moveTo(r.x,r.y);for(let e=0;e<o.length;e++){const i=o[e],n=r.addScaled(a,i.x).addScaled(s,i.y);t.lineTo(n.x,n.y)}t.lineTo(this._data.points[1].x,this._data.points[1].y);for(let e=o.length-1;e>=0;e--){const i=o[e],n=r.addScaled(a,i.x).addScaled(s,-i.y);t.lineTo(n.x,n.y)}t.lineTo(r.x,r.y),t.stroke(),t.fill()}_arrowGeometry(e){const t=u(e),i=[],n=e>=35?.1:0;return i.push(new l.Point(0,0)),i.push(new l.Point(e-t+t*n,1.22*t/4)),i.push(new l.Point(e-t,1.22*t/2)),i.push(new l.Point(e,0)),i}_hittestGeometry(e){const t=u(e),i=[];return i.push(new l.Point(0,0)),i.push(new l.Point(e-t,1.22*t/4)),i.push(new l.Point(e-t,1.22*t/2)),i.push(new l.Point(e,0)),i}}var p,g=i(338933),f=i(799839);!function(e){e[e.CircleRadius=9]="CircleRadius",e[e.AngleTolerance=.017444444444444446]="AngleTolerance"}(p||(p={}));class x extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._textRendererData={text:"",color:"",vertAlign:r.VerticalAlign.Middle,horzAlign:r.HorizontalAlign.Center,font:"",offsetX:10,offsetY:10,points:[],forceTextAlign:!0},this._arrowRendererData={points:[],color:""},this._ellipseRendererData={color:"",linewidth:0,points:[],fillBackground:!0,backcolor:"",noHitTestOnBackground:!0},this._drawAsCircle=!1,this._textRenderer=new a.TextRenderer(this._textRendererData),this._arrowRenderer=new _(this._arrowRendererData),this._ellipseRenderer=new g.EllipseRendererSimple(this._ellipseRendererData)}renderer(e){this._invalidated&&this._updateImpl(e);const t=new o.CompositeRenderer;this._drawAsCircle?t.append(this._ellipseRenderer):t.append(this._arrowRenderer);const i=this._getSource().properties().childs();return this._textRendererData.points&&this._textRendererData.points.length>0&&i.showLabel.value()&&(this._textRenderer.setData({...this._textRendererData}),t.append(this._textRenderer)),this.addAnchors(t),t}_updateImpl(e){super._updateImpl(e);const t=this._getPoints(),i=this._getSource().properties().childs();if(this._arrowRendererData.color=i.backgroundColor.value(),this._arrowRendererData.points=t,this._textRendererData.text=i.text.value(),this._textRendererData.color=i.textColor.value(),this._textRendererData.font=f.CHART_FONT_FAMILY,this._textRendererData.bold=i.bold.value(),this._textRendererData.italic=i.italic.value(),this._textRendererData.fontsize=i.fontsize.value(),t.length>=2){const e=this._getSource().points(),n=e[0].index-e[1].index,s=e[0].price-e[1].price;if(this._drawAsCircle=0===n&&Math.abs(s)<1e-8,this._textRendererData.points=[t[0]],this._drawAsCircle){this._textRendererData.horzAlign=r.HorizontalAlign.Left,this._textRendererData.vertAlign=r.VerticalAlign.Middle;const e=new l.Point(t[0].x-9,t[0].y-9),n=new l.Point(t[0].x+9,t[0].y+9);this._ellipseRendererData.points=[e,n],this._ellipseRendererData.backcolor=i.backgroundColor.value(),this._ellipseRendererData.color=i.backgroundColor.value()}else{let e=Math.atan2(t[1].x-t[0].x,t[1].y-t[0].y);e-=.017444444444444446*Math.sign(e)
;let i=r.HorizontalAlign.Center,n=r.VerticalAlign.Middle;e>-Math.PI/4&&e<Math.PI/4?n=r.VerticalAlign.Bottom:e>3*Math.PI/4||e<-3*Math.PI/4?n=r.VerticalAlign.Top:i=e<0?r.HorizontalAlign.Left:r.HorizontalAlign.Right,this._textRendererData.vertAlign=n,this._textRendererData.horzAlign=i}}}}},546673:(e,t,i)=>{"use strict";i.r(t),i.d(t,{BarsPatternPaneView:()=>R});var n=i(86441),r=i(926048),s=i(936879),o=i(589637),a=i(184114),l=i(69549),d=i(776165),h=i(586982),c=i(421537),u=i(862903),_=i(659011),p=i(457563),g=i(895379),f=i(144044),x=i(534743);const v=r.colorsPalette["color-cold-gray-500"],w={[x.LineToolBarsPatternMode.Bars]:e=>[e[2],e[3]],[x.LineToolBarsPatternMode.Line]:e=>e[4],[x.LineToolBarsPatternMode.OpenClose]:e=>[e[1],e[4]],[x.LineToolBarsPatternMode.LineOpen]:e=>e[1],[x.LineToolBarsPatternMode.LineHigh]:e=>e[2],[x.LineToolBarsPatternMode.LineLow]:e=>e[3],[x.LineToolBarsPatternMode.LineHL2]:e=>(e[2]+e[3])/2};class R extends g.LineSourcePaneView{constructor(){super(...arguments),this._vertLineRenderer1=new p.VerticalLineRenderer,this._vertLineRenderer2=new p.VerticalLineRenderer,this._medianRenderer=new _.TrendLineRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const t=this._source.priceScale(),i=this._source.ownerSource()?.firstValue()??null;if(!t||t.isEmpty()||null===i)return;const r=this._source.points(),_=this._source.pattern(),p=_.length,g=new l.CompositeRenderer;if(p>0&&2===r.length){const e=this._source.properties().childs(),l=e.mode.value(),h=e.color.value(),v=Math.abs((this._points[0].x-this._points[1].x)/(p-1)),R=this._source.getScale(),T=e=>t.priceToCoordinate(e,i)*R,[{index:m},{index:y}]=r,P=m<y?this._points[0]:this._points[1],[L,b]=this._source.points(),S=m<y?L.index:b.index,A=P.x,C=P.y-T(this._source.firstPatternPrice());if(l===x.LineToolBarsPatternMode.Bars||l===x.LineToolBarsPatternMode.OpenClose){const e=w[l];for(let t=0;t<p;t++){const i=Math.round(A+t*v+.5),r=e(_[t]).map(((e,t)=>new n.Point(i+(2*t-1),Math.round(T(e))+C))),s=new u.RectangleRenderer;s.setData({points:r,color:h,backcolor:h,linewidth:1,fillBackground:!0,transparency:10,extendLeft:!1,extendRight:!1}),g.append(s)}g.append(this.createLineAnchor({points:this._points.map(f.mapLineSourcePaneViewPointToLineAnchorPoint)},0))}else{const e=w[l],t=_.map(((t,i)=>{const n=Math.round(A+i*v+.5);return{timePointIndex:S+i,center:n,left:NaN,right:NaN,y:T(e(t))+C}}));g.append(new c.PaneRendererLine({barSpacing:v,items:t,lineColor:{type:d.ColorType.Solid,color:(0,o.generateColor)(h,10)},lineStyle:s.LINESTYLE_SOLID,lineWidth:2,hittest:new a.HitTestResult(a.HitTarget.MovePoint),simpleMode:!0,withMarkers:!1,skipHoles:!0})),g.append(this.createLineAnchor({points:this._points.map(f.mapLineSourcePaneViewPointToLineAnchorPoint)},1))}}else this._vertLineRenderer1.setData({x:this._points[0].x,color:v,linewidth:1,linestyle:s.LINESTYLE_SOLID}),g.append(this._vertLineRenderer1),this._vertLineRenderer2.setData({
x:this._points[1].x,color:v,linewidth:1,linestyle:s.LINESTYLE_SOLID}),g.append(this._vertLineRenderer2),this._medianRenderer.setData({points:this._points,color:v,linewidth:1,linestyle:s.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal}),g.append(this._medianRenderer);this._renderer=g}}},876662:(e,t,i)=>{"use strict";i.r(t),i.d(t,{BezierQuadroPaneView:()=>h,cacheIsValid:()=>d});var n=i(650151),r=i(589637),s=i(895379),o=i(69549),a=i(559447),l=i(807710);function d(e,t,i,n,r,s){return null!==e&&e.p1.x===t.x&&e.p1.y===t.y&&e.p2.x===i.x&&e.p2.y===i.y&&e.p3.x===n.x&&e.p3.y===n.y&&e.width===r&&e.height===s}class h extends s.LineSourcePaneView{constructor(){super(...arguments),this._bezierQuadroRenderer=new l.BezierQuadroRenderer,this._renderer=null,this._extendedSegmentLeftCache=null,this._extendedSegmentRightCache=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const t=this._source.properties().childs();let i=[],s=[];if(3===this._source.points().length){const r=(0,n.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[0])),o=(0,n.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[1])),a=(0,n.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[2])),l=o.subtract(r),d=a.subtract(l.scaled(.25)),h=a.add(l.scaled(.25)),{mediaSize:{width:c,height:u}}=e;t.extendLeft.value()&&(i=this._extendSegmentLeft(a,r,d,c,u)),t.extendRight.value()&&(s=this._extendSegmentRight(a,o,h,c,u))}const a=this._points.slice(),l=this._source.controlPoint();null!==l&&a.push((0,n.ensureNotNull)(this._source.pointToScreenPoint(l)));const d={points:a,color:t.linecolor.value(),lineWidth:t.linewidth.value(),lineStyle:t.linestyle.value(),leftEnd:t.leftEnd.value(),rightEnd:t.rightEnd.value(),fillBack:t.fillBackground.value(),backColor:(0,r.generateColor)(t.backgroundColor.value(),t.transparency.value()),extendLeftSegments:i,extendRightSegments:s};this._bezierQuadroRenderer.setData(d);const h=new o.CompositeRenderer;h.append(this._bezierQuadroRenderer),this.addAnchors(h),this._renderer=h}_extendSegmentLeft(e,t,i,r,s){return d(this._extendedSegmentLeftCache,e,t,i,r,s)||(this._extendedSegmentLeftCache={p1:e,p2:t,p3:i,width:r,height:s,segment:(0,a.extendQuadroBezier)(e,t,i,r,s)}),(0,n.ensureNotNull)(this._extendedSegmentLeftCache).segment}_extendSegmentRight(e,t,i,r,s){return d(this._extendedSegmentRightCache,e,t,i,r,s)||(this._extendedSegmentRightCache={p1:e,p2:t,p3:i,width:r,height:s,segment:(0,a.extendQuadroBezier)(e,t,i,r,s)}),(0,n.ensureNotNull)(this._extendedSegmentRightCache).segment}}},807710:(e,t,i)=>{"use strict";i.d(t,{BezierQuadroRenderer:()=>p,buildExtendedSegments:()=>_,hitTestExtendedPoints:()=>u});var n=i(204652),r=i(767313),s=i(586982),o=i(184114),a=i(305141),l=i(559447),d=i(659011),h=i(161656),c=i(898646);function u(e,t,i){for(const r of i)for(let i=1;i<r.length;i++){const s=r[i-1],a=r[i];if((0,
n.distanceToSegment)(s,a,e).distance<t)return new o.HitTestResult(o.HitTarget.MovePoint)}return null}function _(e,t){for(let i=0;i<t.length;i++){const n=t[i],r=n[0];e.moveTo(r.x,r.y);for(let t=1;t<n.length;t++){const i=n[t];e.lineTo(i.x,i.y)}}}class p extends r.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=e||null}setData(e){this._data=e}hitTest(e){if(null!==this._data&&3===this._data.points.length){const t=(0,h.interactionTolerance)().curve,[i,n,r]=this._data.points,s=n.subtract(i),a=r.subtract(s.scaled(.25)),d=r.add(s.scaled(.25));if((0,l.quadroBezierHitTest)(r,i,a,e,t)||(0,l.quadroBezierHitTest)(r,n,d,e,t))return new o.HitTestResult(o.HitTarget.MovePoint);let c=u(e,t,this._data.extendLeftSegments);return null===c&&(c=u(e,t,this._data.extendRightSegments)),c}return null}_drawImpl(e){if(null===this._data)return;const[t,i,n]=this._data.points,r=e.context;if(r.lineCap="round",r.strokeStyle=this._data.color,r.lineWidth=this._data.lineWidth,(0,c.setLineStyle)(r,this._data.lineStyle),2===this._data.points.length)r.beginPath(),r.moveTo(t.x,t.y),r.lineTo(i.x,i.y),r.stroke();else{const e=i.subtract(t),o=n.subtract(e.scaled(.25)),l=n.add(e.scaled(.25));this._data.fillBack&&this._data.points.length>2&&(r.fillStyle=this._data.backColor,r.beginPath(),r.moveTo(t.x,t.y),r.quadraticCurveTo(o.x,o.y,n.x,n.y),r.quadraticCurveTo(l.x,l.y,i.x,i.y),r.fill()),r.beginPath(),_(r,this._data.extendLeftSegments),r.moveTo(t.x,t.y),r.quadraticCurveTo(o.x,o.y,n.x,n.y),r.quadraticCurveTo(l.x,l.y,i.x,i.y),_(r,this._data.extendRightSegments),this._data.leftEnd===s.LineEnd.Arrow&&(0,d.drawArrow)(o,t,r,r.lineWidth,a.dpr1PixelRatioInfo),this._data.rightEnd===s.LineEnd.Arrow&&(0,d.drawArrow)(l,i,r,r.lineWidth,a.dpr1PixelRatioInfo),r.stroke()}}}},350914:(e,t,i)=>{"use strict";i.d(t,{BrushBasePaneView:()=>h});var n=i(86441),r=i(306099),s=i(652374),o=i(69549),a=i(144044),l=i(184114),d=i(895379);class h extends d.LineSourcePaneView{constructor(){super(...arguments),this._polygonRenderer=new r.PolygonRenderer,this._renderer=new o.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e);const t=Math.max(1,this._source.smooth()),i=this._points;if(0===i.length)return void this._renderer.clear();const n=[i[0]];for(let e=1;e<i.length;e++){const r=i[e].subtract(i[e-1]),s=r.length(),o=Math.min(5,Math.floor(s/t)),a=r.normalized().scaled(s/o);for(let t=0;t<o-1;t++)n.push(i[e-1].add(a.scaled(t)));n.push(i[e])}this._points=this._smoothArray(n,t);const r=this._createPolygonRendererData();if(this._polygonRenderer.setData(r),this._renderer=new o.CompositeRenderer,this._renderer.append(this._polygonRenderer),this._source.finished()&&this._addAnchors()){const e=r.points.length;if(e>0){const t=1!==e?[r.points[0],r.points[e-1]]:[r.points[0]],i=new s.SelectionRenderer({points:t.map(a.mapLineSourcePaneViewPointToLineAnchorPoint),bgColors:this._lineAnchorColors(t),visible:this.areAnchorsVisible(),hittestResult:l.HitTarget.Regular,barSpacing:this._getModel().timeScale().barSpacing()})
;this._renderer.append(i)}}}_addAnchors(){return!0}_smoothArray(e,t){if(1===e.length)return e;const i=new Array(e.length);for(let r=0;r<e.length;r++){let s=new n.Point(0,0);for(let i=0;i<t;i++){const t=Math.max(r-i,0),n=Math.min(r+i,e.length-1);s=s.add(e[t]),s=s.add(e[n])}i[r]=s.scaled(.5/t)}return i.push(e[e.length-1]),i}}},60137:(e,t,i)=>{"use strict";i.r(t),i.d(t,{BrushPaneView:()=>s});var n=i(936879),r=i(350914);class s extends r.BrushBasePaneView{_createPolygonRendererData(){const e=this._source.properties().childs(),t={points:this._points,color:e.linecolor.value(),linewidth:e.linewidth.value(),linestyle:n.LINESTYLE_SOLID,skipClosePath:!0,leftend:e.leftEnd.value(),rightend:e.rightEnd.value(),filled:!1,fillBackground:!1,backcolor:e.backgroundColor.value()};return e.fillBackground.value()&&this._model.lineBeingCreated()!==this._source&&(t.filled=!0,t.fillBackground=!0,t.transparency=e.transparency.value()),t}}},377209:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CalloutPaneView:()=>T});var n=i(86441),r=i(650151),s=i(885800),o=i(69549),a=i(799839),l=i(144044),d=i(980317),h=i(463940),c=i(184114),u=i(454304),_=i(431520),p=i(589637),g=i(652283),f=i(767313),x=i(305141),v=i(69336);class w extends f.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=null,this._textInfoCache=null,this._hitTest=e||new c.HitTestResult(c.HitTarget.MovePoint,{areaName:c.AreaName.Text})}data(){return this._data}setData(e){null===this._data||this._data.textData.lines===e.textData.lines&&this._data.textData.font===e.textData.font&&this._data.textData.maxWidth===e.textData.maxWidth||(this._textInfoCache=null),this._data=e}setCursorType(e){this._hitTest.mergeData({cursorType:e})}hitTest(e){if(null===this._data||this._data.points.length<2)return null;const t=this._data.points[0],i=this._data.points[1];if(t.subtract(e).length()<3)return new c.HitTestResult(c.HitTarget.ChangePoint);const n=(0,r.ensureNotNull)(this._calcTextSize()),s=i.x-n.totalWidth/2,o=i.y-n.totalHeight/2;return e.x>=s&&e.x<=s+n.totalWidth&&e.y>=o&&e.y<=o+n.totalHeight?this._hitTest:null}getTextInfo(){const e=(0,r.ensureNotNull)(this._data),t=(0,_.isRtl)()?"right":"left",i=e.points[1].clone(),{totalWidth:n,totalHeight:s}=(0,r.ensureNotNull)(this._calcTextSize()),o=i.x-n/2,a=i.y-s/2;return{font:e.textData.font,fontSize:e.textData.fontSize,lineHeight:Math.ceil(e.textData.fontSize),lineSpacing:0,textTop:a+10,textBottom:a+s-10,textLeft:o+10,textRight:o+n-10,textAlign:t}}positionToCoordinate(e,t){const i=(0,r.ensureNotNull)(this._data),n=this.getTextInfo(),{x:s,y:o,lineNumber:a}=(0,v.getSymbolCoordinatesInfo)({symbolPosition:t,textWidth:n.textRight-n.textLeft,textByLines:i.textData.linesIncludingHidden,lineHeight:i.textData.fontSize,font:i.textData.font,textAlign:n.textAlign});return{x:s+n.textLeft,y:o+n.textTop,lineNumber:a}}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=this._data.points[0].clone(),i=this._data.points[1].clone(),n=e.context;n.lineCap="round",n.strokeStyle=this._data.bordercolor,n.lineWidth=this._data.linewidth,n.textBaseline="bottom",
n.font=this._data.textData.font;const{textWidth:s,textHeight:o,totalWidth:a,totalHeight:l}=(0,r.ensureNotNull)(this._calcTextSize()),d=i.x-a/2,h=i.y-l/2;let c=0;const u=s+4>16,f=o+4>16;n.textAlign=(0,_.isRtl)()?"right":"left";const x=(0,g.calcTextHorizontalShift)(n,s);t.x>d+a?c=20:t.x>d&&(c=10),t.y>h+l?c+=2:t.y>h&&(c+=1),n.translate(d,h),t.x-=d,t.y-=h,i.x-=d,i.y-=h,n.beginPath(),n.moveTo(8,0),10===c?u?(n.lineTo(i.x-8,0),n.lineTo(t.x,t.y),n.lineTo(i.x+8,0),n.lineTo(a-8,0)):(n.lineTo(t.x,t.y),n.lineTo(a-8,0)):n.lineTo(a-8,0),20===c?(n.lineTo(t.x,t.y),n.lineTo(a,8)):n.arcTo(a,0,a,8,8),21===c?f?(n.lineTo(a,i.y-8),n.lineTo(t.x,t.y),n.lineTo(a,i.y+8),n.lineTo(a,l-8)):(n.lineTo(t.x,t.y),n.lineTo(a,l-8)):n.lineTo(a,l-8),22===c?(n.lineTo(t.x,t.y),n.lineTo(a-8,l)):n.arcTo(a,l,a-8,l,8),12===c?u?(n.lineTo(i.x+8,l),n.lineTo(t.x,t.y),n.lineTo(i.x-8,l),n.lineTo(8,l)):(n.lineTo(t.x,t.y),n.lineTo(8,l)):n.lineTo(8,l),2===c?(n.lineTo(t.x,t.y),n.lineTo(0,l-8)):n.arcTo(0,l,0,l-8,8),1===c?f?(n.lineTo(0,i.y+8),n.lineTo(t.x,t.y),n.lineTo(0,i.y-8),n.lineTo(0,8)):(n.lineTo(t.x,t.y),n.lineTo(0,8)):n.lineTo(0,8),0===c?(n.lineTo(t.x,t.y),n.lineTo(8,0)):n.arcTo(0,0,8,0,8),n.stroke(),n.fillStyle=(0,p.generateColor)(this._data.backcolor,this._data.transparency),n.fill(),n.translate(-d,-h),this._drawSelectionIfNeeded(n),n.fillStyle=this._data.color;let v=h+8+2+this._data.textData.fontSize;const w=d+8+2+x;for(const e of this._data.textData.lines)n.fillText(e.text,w,v),v+=this._data.textData.fontSize}_calcTextSize(){if(null===this._data||this._data.points.length<2)return null;if(null===this._textInfoCache){const e=this._data.textData.fontSize*this._data.textData.lines.length,t=this._data.textData.maxWidth,i=2*2+2*8;this._textInfoCache={textWidth:t,textHeight:e,totalWidth:t+i,totalHeight:e+i}}return this._textInfoCache}_drawSelectionIfNeeded(e){const t=(0,r.ensureNotNull)(this._data),i=t.textData.fontSize;if(t.selectionHighlight){const n=this.positionToCoordinate(!1,t.selectionHighlight.start),r=this.positionToCoordinate(!1,t.selectionHighlight.end),s=this.getTextInfo();(0,v.drawSelection)(e,x.dpr1PixelRatioInfo,{lines:t.textData.lines,selectionStart:n,selectionEnd:r,left:s.textLeft,right:s.textRight,color:t.selectionHighlight.color,font:t.textData.font,lineHeight:i})}}}let R=null;class T extends d.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._renderer=new o.CompositeRenderer,this._textWidthCache=new h.TextWidthCache,this._calloutRenderer=new w(new c.HitTestResult(c.HitTarget.MovePoint,(0,d.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,...this._calloutRenderer.getTextInfo()})),this._calloutRenderer.positionToCoordinate.bind(this._calloutRenderer,!0))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._source.calculatePoint2(),this._renderer.clear(),!this._points[0])return void this.closeTextEditor()
;if(this._points.length<2)return void this.closeTextEditor();const t=this._source.properties().childs(),i=this._points[0],s=i.x+this._source.getBarOffset()*this._model.timeScale().barSpacing(),o=new n.Point(s,this._points[1].y),a=this._fontStyle(),d=t.wordWrap.value()?t.wordWrapWidth.value():void 0,h=(0,u.wordWrap)(this._textData(),a,this._textWidthCache,!1,d),c=h.filter((e=>!e.hidden));let _;_=void 0!==d?d:c.reduce(((e,t)=>Math.max(e,function(e,t){if(null===R){const e=document.createElement("canvas");e.width=0,e.height=0,R=(0,r.ensureNotNull)(e.getContext("2d"))}return R.font=t,R.measureText(e).width}(t.text,a))),0);const p={points:[i,o],color:this._textColor(),linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),transparency:t.transparency.value(),textData:{originalText:this._textData(),linesIncludingHidden:h,lines:c,maxWidth:_,font:a,fontSize:t.fontsize.value()},bordercolor:t.bordercolor.value(),...this._inplaceTextHighlight()};if(this._calloutRenderer.setData(p),this._renderer.append(this._calloutRenderer),this._updateInplaceText(this._calloutRenderer.getTextInfo()),this._renderer.append(this.createLineAnchor({points:[(0,l.lineSourcePaneViewPointToLineAnchorPoint)(i)]},0)),void 0!==d){const e=p.points[1],t=(0,l.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(e.x+d/2+8+2,e.y),1);this._renderer.append(this.createLineAnchor({points:[t]},1))}this._calloutRenderer.setCursorType(this._textCursorType())}_fontStyle(){const e=this._source.properties().childs(),t=(e.bold.value()?"bold ":"")+(e.italic.value()?"italic ":""),i=e.fontsize.value();return(0,s.makeFont)(i,a.CHART_FONT_FAMILY,t)}}},964118:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CirclePaneView:()=>u});var n=i(986226),r=i(799839),s=i(69549),o=i(184114),a=i(863192),l=i(144044),d=i(980317),h=i(10666),c=i(717842);class u extends d.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._circleRenderer=new c.CircleRenderer,this._renderer=new s.CompositeRenderer,this._textRenderer=new h.LineToolTextRenderer(void 0,new o.HitTestResult(o.HitTarget.MovePoint,(0,d.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,rotationPoint:this._textRenderer.rotation()??void 0,...this._textRenderer.getTextInfo()})),this._textRenderer.positionToCoordinate.bind(this._textRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._points.length<2)return;const t=this._source.properties().childs(),[i,s]=this._points;this._circleRenderer.setData({center:i,radius:Math.sqrt((s.x-i.x)**2+(s.y-i.y)**2),color:t.color.value(),lineWidth:t.linewidth.value(),backColor:t.fillBackground.value()?t.backgroundColor.value():"transparent",backgroundHitTarget:this._model.selection().isSelected(this._source)?o.HitTarget.MovePoint:void 0}),this._renderer.append(this._circleRenderer);const d=this._placeHolderMode();let h=!1
;if(t.showLabel?.value()&&t.text.value()||d||this._isTextEditMode()){const{fontSize:t,bold:o,italic:l}=this._source.properties().childs(),c=i.subtract(s).length()*Math.sqrt(2);this._textRenderer.setData({points:[i],text:this._textData(),color:this._textColor(),fontSize:t.value(),font:r.CHART_FONT_FAMILY,bold:o.value(),italic:l.value(),wordWrapWidth:d?void 0:c,maxHeight:d?void 0:c,offsetX:0,offsetY:0,horzAlign:n.HorizontalAlign.Center,vertAlign:n.VerticalAlign.Middle,forceTextAlign:!0,decorator:d?a.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()});let u=!1;if(d&&this._textRenderer.measure().width>c&&(this._textRenderer.updateData({text:""}),u=this._textRenderer.measure().width>c),!u){this._textRenderer.setCursorType(this._textCursorType());const{mediaSize:{width:t,height:i}}=e;this._textRenderer.isOutOfScreen(t,i)?this.closeTextEditor():this._updateInplaceText(this._textRenderer.getTextInfo()),this._renderer.append(this._textRenderer),h=!0}}const c=[(0,l.lineSourcePaneViewPointToLineAnchorPoint)(s,1)];h||c.unshift((0,l.lineSourcePaneViewPointToLineAnchorPoint)(i,0,void 0,void 0,o.HitTarget.MovePoint)),this._renderer.append(this.createLineAnchor({points:c},0))}}},193192:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CrossLinePaneView:()=>l});var n=i(895379),r=i(888088),s=i(457563),o=i(69549),a=i(184114);class l extends n.LineSourcePaneView{constructor(e,t){super(e,t),this._renderer=null,this._horizLineRenderer=new r.HorizontalLineRenderer,this._vertLineRenderer=new s.VerticalLineRenderer,this._horizLineRenderer.setHitTest(new a.HitTestResult(a.HitTarget.MovePoint))}update(){this._invalidated=!0}renderer(e){return this._invalidated&&(this._updateImpl(e),this._invalidated=!1),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._getPoints();if(0===t.length)return;const i={color:this._getSource().lineColor(),linestyle:this._getSource().lineStyle(),linewidth:this._getSource().lineWidth(),x:t[0].x,y:t[0].y};this._horizLineRenderer.setData(i),this._horizLineRenderer.setHitTest(new a.HitTestResult(a.HitTarget.MovePoint,{snappingPrice:this._source.points()[0].price})),this._vertLineRenderer.setData(i),this._vertLineRenderer.setHitTest(new a.HitTestResult(a.HitTarget.MovePoint,{snappingIndex:this._source.points()[0].index}));const n=new o.CompositeRenderer;n.append(this._horizLineRenderer),n.append(this._vertLineRenderer),this.addAnchors(n),this._renderer=n}}},936853:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CypherPaneView:()=>r});var n=i(67534);class r extends n.Pattern5pointsPaneView{_updateBaseData(){if(this._source.points().length>=3){const[e,t,i]=this._source.points();this._abRetracement=Math.round(1e3*Math.abs((i.price-t.price)/(t.price-e.price)))/1e3}if(this._source.points().length>=4){const[e,t,,i]=this._source.points();this._bcRetracement=Math.round(1e3*Math.abs((i.price-e.price)/(t.price-e.price)))/1e3}if(this._source.points().length>=5){const[e,,t,i,n]=this._source.points();this._cdRetracement=Math.round(1e3*Math.abs((n.price-i.price)/(i.price-t.price)))/1e3,
this._xdRetracement=Math.round(1e3*Math.abs((n.price-i.price)/(e.price-i.price)))/1e3}}}},721579:(e,t,i)=>{"use strict";i.d(t,{DateAndPriceRangeBasePaneView:()=>l});var n=i(86441),r=i(799839),s=i(986226),o=i(454304),a=i(895379);class l extends a.LineSourcePaneView{constructor(){super(...arguments),this._customTextrenderer=new o.TextRenderer}_updateCustomTextRenderer(e,t){const i=this._source.properties().childs().customText.childs();if(i.visible.value()&&i.text.value().length>0){const[a,l]=this._points,d=Math.round((a.y+l.y)/2),h=new n.Point(a.x,d),c=new n.Point(l.x,d),u=h.x<c.x?h:c,_=u===h?c:h,p=s.VerticalAlign.Middle,g=s.HorizontalAlign.Center,f=new n.Point((h.x+c.x)/2,(h.y+c.y)/2),x=Math.atan((_.y-u.y)/(_.x-u.x)),v={points:[f],text:i.text.value(),color:i.color.value(),vertAlign:p,horzAlign:g,font:r.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:i.bold.value(),italic:i.italic.value(),fontsize:i.fontsize.value(),forceTextAlign:!0,angle:x};return this._customTextrenderer.setData(v),this._needLabelExclusionPath(this._customTextrenderer,s.VerticalAlign.Middle)?(0,o.getTextBoundaries)(this._customTextrenderer,t,e):null}return this._customTextrenderer.setData(null),null}}},111253:(e,t,i)=>{"use strict";i.r(t),i.d(t,{DateAndPriceRangePaneView:()=>y});var n=i(650151),r=i(86441),s=i(609838),o=i(431520),a=i(986226),l=i(454304),d=i(862903),h=i(659011),c=i(69549),u=i(251940),_=i(936879),p=i(586982),g=i(684740),f=i(799839),x=i(721579);const v=new g.TimeSpanFormatter,w=(0,u.getPercentageFormatter)(),R=(0,u.getVolumeFormatter)(),T=s.t(null,void 0,i(841643)),m=s.t(null,{context:"study"},i(424261));class y extends x.DateAndPriceRangeBasePaneView{constructor(){super(...arguments),this._distanceLineRenderer=new h.TrendLineRenderer,this._distancePriceRenderer=new h.TrendLineRenderer,this._backgroundRenderer=new d.RectangleRenderer,this._borderRenderer=new d.RectangleRenderer,this._textRenderer=new l.TextRenderer,this._renderer=new c.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._points.length<2||this._source.points().length<2)return;const t=this._source.properties().childs();t.fillBackground&&t.fillBackground.value()&&(this._backgroundRenderer.setData({points:this._points,color:"white",linewidth:0,backcolor:t.backgroundColor.value(),fillBackground:!0,transparency:t.backgroundTransparency.value(),extendLeft:!1,extendRight:!1}),this._renderer.append(this._backgroundRenderer));const[i,s]=this._points;t.drawBorder.value()&&(this._borderRenderer.setData({points:this._points,color:t.borderColor.value(),linewidth:t.borderWidth.value(),fillBackground:!1,extendLeft:!1,extendRight:!1,backcolor:""}),this._renderer.append(this._borderRenderer));const d=t.drawBorder.value()?t.borderWidth.value()/2:0,{mediaSize:{width:h,height:c}}=e,g=this._updateCustomTextRenderer(c,h),x=Math.round((i.y+s.y)/2),y=new r.Point(i.x+Math.sign(s.x-i.x)*d,x),P=new r.Point(s.x+Math.sign(i.x-s.x)*d,x);this._distanceLineRenderer.setData({points:[y,P],
color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:_.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:p.LineEnd.Normal,rightend:Math.abs(i.x-s.x)>=25*t.linewidth.value()?p.LineEnd.Arrow:p.LineEnd.Normal,excludeBoundaries:g?[g]:void 0}),this._renderer.append(this._distanceLineRenderer);const L=Math.round((i.x+s.x)/2),b=new r.Point(L,i.y+Math.sign(s.y-i.y)*d),S=new r.Point(L,s.y+Math.sign(i.y-s.y)*d);this._distancePriceRenderer.setData({points:[b,S],color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:_.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:p.LineEnd.Normal,rightend:Math.abs(b.y-S.y)>=25*t.linewidth.value()?p.LineEnd.Arrow:p.LineEnd.Normal,excludeBoundaries:g?[g]:void 0}),this._renderer.append(this._distancePriceRenderer);const A=this._source.points()[0].price,C=this._source.points()[1].price,M=this._source.points()[0].index,k=this._source.points()[1].index,B=k-M,I=(0,o.forceLTRStr)(B+""),H=this._model.timeScale().indexToUserTime(M),D=this._model.timeScale().indexToUserTime(k);let z="";if(H&&D){const e=(D.valueOf()-H.valueOf())/1e3;z=", "+(0,o.startWithLTR)(v.format(e))}const V=C-A,E=100*V/Math.abs(A),N=this._model.mainSeries().symbolInfo(),W=N&&(0,u.getPipFormatter)(N),F=(0,n.ensureNotNull)(this._source.ownerSource()).formatter(),O=(F.formatChange?.(C,A)??F.format(V))+" ("+w.format(Math.round(100*E)/100)+") "+(W?W.format(V):"");let Y,q=(0,o.forceLTRStr)(O)+"\n"+T.format({count:I})+z;if(t.showVolume.value()){const e=this._source.volume();Number.isNaN(e)||(q+=`\n${m} ${R.format(e)}`)}Y=C>A?new r.Point(.5*(i.x+s.x),s.y-2*t.fontsize.value()):new r.Point(.5*(i.x+s.x),s.y+.7*t.fontsize.value());const X={x:0,y:10},j=t.fontsize.value(),Q={points:[Y],text:q,color:t.textcolor.value(),font:f.CHART_FONT_FAMILY,offsetX:X.x,offsetY:X.y,lineSpacing:8,vertAlign:a.VerticalAlign.Middle,horzAlign:a.HorizontalAlign.Center,fontsize:j,backgroundRoundRect:4,boxPaddingHorz:.4*j+j/3,boxPaddingVert:.2*j+j/3},U=t.fillLabelBackground?.value();U&&(Q.boxShadow={shadowColor:t.shadow.value(),shadowBlur:4,shadowOffsetY:1},Q.backgroundColor=t.labelBackgroundColor.value()),this._textRenderer.setData(Q);const $=this._textRenderer.measure(),G=(0,l.calculateLabelPosition)($,i,s,X,c);this._textRenderer.setPoints([G]),this._renderer.append(this._textRenderer),this._renderer.append(this._customTextrenderer),this.addAnchors(this._renderer)}_needLabelExclusionPath(e){return e.getLinesInfo().lines.length>0}}},304988:(e,t,i)=>{"use strict";i.r(t),i.d(t,{DateRangePaneView:()=>R});var n=i(86441),r=i(609838),s=i(431520),o=i(986226),a=i(454304),l=i(862903),d=i(659011),h=i(69549),c=i(936879),u=i(586982),_=i(684740),p=i(251940),g=i(799839),f=i(721579);const x=(0,p.getVolumeFormatter)(),v=r.t(null,void 0,i(841643)),w=r.t(null,{context:"study"},i(424261));class R extends f.DateAndPriceRangeBasePaneView{constructor(){super(...arguments),this._leftBorderRenderer=new d.TrendLineRenderer,this._rightBorderRenderer=new d.TrendLineRenderer,this._distancePriceRenderer=new d.TrendLineRenderer,
this._backgroundRenderer=new l.RectangleRenderer,this._textRenderer=new a.TextRenderer,this._renderer=new h.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._points.length<2||this._source.points().length<2)return;const t=this._source.properties().childs(),i=t.extendTop.value(),r=t.extendBottom.value(),[l,d]=this._points,h=i?0:Math.min(l.y,d.y),p=r?this._height():Math.max(l.y,d.y);t.fillBackground.value()&&(this._backgroundRenderer.setData({points:[new n.Point(l.x,h),new n.Point(d.x,p)],color:"white",linewidth:0,backcolor:t.backgroundColor.value(),fillBackground:!0,transparency:t.backgroundTransparency.value(),extendLeft:!1,extendRight:!1}),this._renderer.append(this._backgroundRenderer));const f=(e,i,n)=>{e.setData({points:[i,n],color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:c.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:u.LineEnd.Normal,rightend:u.LineEnd.Normal}),this._renderer.append(e)};f(this._leftBorderRenderer,new n.Point(l.x,h),new n.Point(l.x,p)),f(this._rightBorderRenderer,new n.Point(d.x,h),new n.Point(d.x,p));const R=Math.round((l.y+d.y)/2),T=new n.Point(l.x,R),m=new n.Point(d.x,R),{mediaSize:{width:y,height:P}}=e,L=this._updateCustomTextRenderer(P,y);this._distancePriceRenderer.setData({points:[T,m],color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:c.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:u.LineEnd.Normal,rightend:Math.abs(T.x-m.x)>=15*t.linewidth.value()?u.LineEnd.Arrow:u.LineEnd.Normal,excludeBoundaries:L?[L]:void 0}),this._renderer.append(this._distancePriceRenderer);const b=this._source.points()[0].index,S=this._source.points()[1].index,A=S-b,C=this._model.timeScale().indexToUserTime(b),M=this._model.timeScale().indexToUserTime(S);let k="";if(C&&M){const e=(M.valueOf()-C.valueOf())/1e3;k=", "+(0,s.startWithLTR)((new _.TimeSpanFormatter).format(e))}const B=this._source.volume(),I=t.showVolume.value()&&!Number.isNaN(B)?`\n${w} ${x.format(B)}`:"",H=v.format({count:(0,s.forceLTRStr)(A.toString())})+k+I,D={x:0,y:10},z=t.fontsize.value(),V={text:H,color:t.textcolor.value(),font:g.CHART_FONT_FAMILY,offsetX:D.x,offsetY:D.y,lineSpacing:8,vertAlign:o.VerticalAlign.Middle,horzAlign:o.HorizontalAlign.Center,fontsize:z,backgroundRoundRect:4,boxPaddingHorz:.4*z+z/3,boxPaddingVert:.2*z+z/3},E=t.fillLabelBackground?.value();E&&(V.boxShadow={shadowColor:t.shadow.value(),shadowBlur:4,shadowOffsetY:1},V.backgroundColor=t.labelBackgroundColor.value()),this._textRenderer.setData(V);const N=this._textRenderer.measure(),W=(0,a.calculateLabelPosition)(N,l,d,D,P);this._textRenderer.setPoints([W]),this._renderer.append(this._textRenderer),this._renderer.append(this._customTextrenderer),this.addAnchors(this._renderer)}}},211025:(e,t,i)=>{"use strict";i.r(t),i.d(t,{DisjointChannelPaneView:()=>g});var n=i(650151),r=i(86441),s=i(601227),o=i(986226),a=i(59918),l=i(659011),d=i(454304),h=i(69549),c=i(374410),u=i(799839),_=i(144044),p=i(895379)
;class g extends p.LineSourcePaneView{constructor(){super(...arguments),this._trendLineRendererPoints12=new l.TrendLineRenderer,this._trendLineRendererPoints43=new l.TrendLineRenderer,this._disjointChannelRenderer=new a.DisjointChannelRenderer,this._p1LabelRenderer=new d.TextRenderer,this._p2LabelRenderer=new d.TextRenderer,this._p3LabelRenderer=new d.TextRenderer,this._p4LabelRenderer=new d.TextRenderer,this._labelTextRenderer=new d.TextRenderer,this._renderer=new h.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<2)return;const t=this._source.priceScale(),i=this._source.ownerSource()?.firstValue();if(!t||null==i)return;const[a,l,d]=this._source.points(),h=t.formatPrice(a.price,i),p=t.formatPrice(l.price,i);let g,f;if(d){g=t.formatPrice(d.price,i);const e=l.price-a.price;f=t.formatPrice(d.price+e,i)}if(this._points.length<2)return;const x=this._source.properties().childs(),[v,w]=this._points;let R,T;if(this._points.length>=3){R=(0,r.point)(w.x,this._points[2].y);const e=w.y-v.y;if(T=(0,r.point)(v.x,R.y+e),x.fillBackground.value()){const e=x.extendLeft.value(),t=x.extendRight.value();this._disjointChannelRenderer.setData({extendleft:e,extendright:t,points:[v,w,R,T],backcolor:x.backgroundColor.value(),transparency:x.transparency.value(),hittestOnBackground:s.CheckMobile.any()}),this._renderer.append(this._disjointChannelRenderer)}x.labelVisible.value()&&x.labelText.value()&&this._renderer.append(this._getLabelTextRenderer(v,w,T,R))}const m=(e,t)=>({points:[e,t],color:x.linecolor.value(),linewidth:x.linewidth.value(),linestyle:x.linestyle.value(),extendleft:x.extendLeft.value(),extendright:x.extendRight.value(),leftend:x.leftEnd.value(),rightend:x.rightEnd.value()}),y=(e,t,i,n,r,s)=>{x.showPrices.value()&&(e.setData({points:[i],text:r,color:x.textcolor.value(),horzAlign:i.x>n.x?o.HorizontalAlign.Left:o.HorizontalAlign.Right,vertAlign:o.VerticalAlign.Middle,font:u.CHART_FONT_FAMILY,offsetX:6,offsetY:0,boxPadding:0,bold:x.bold.value(),italic:x.italic.value(),fontsize:x.fontsize.value(),forceTextAlign:!0}),this._renderer.append(e),t.setData({points:[n],text:s,color:x.textcolor.value(),horzAlign:i.x<n.x?o.HorizontalAlign.Left:o.HorizontalAlign.Right,vertAlign:o.VerticalAlign.Middle,font:u.CHART_FONT_FAMILY,offsetX:6,offsetY:0,boxPadding:0,bold:x.bold.value(),italic:x.italic.value(),fontsize:x.fontsize.value(),forceTextAlign:!0}),this._renderer.append(t))};if(this._trendLineRendererPoints12.setData(m(v,w)),this._renderer.append(this._trendLineRendererPoints12),y(this._p1LabelRenderer,this._p2LabelRenderer,v,w,h,p),!R||!T)return void this.addAnchors(this._renderer);this._trendLineRendererPoints43.setData(m(T,R)),this._renderer.append(this._trendLineRendererPoints43),y(this._p3LabelRenderer,this._p4LabelRenderer,R,T,(0,n.ensureDefined)(g),(0,n.ensureDefined)(f));const P=[(0,_.lineSourcePaneViewPointToLineAnchorPoint)(v),(0,_.lineSourcePaneViewPointToLineAnchorPoint)(w),(0,
_.lineSourcePaneViewPointToLineAnchorPoint)(R,2,c.PaneCursorType.VerticalResize,!0),(0,_.lineSourcePaneViewPointToLineAnchorPoint)(T,3)];this._model.lineBeingCreated()===this._source&&P.pop(),this._renderer.append(this.createLineAnchor({points:P},0)),v&&w&&this._addAlertRenderer(this._renderer,[v,w])}_getLabelTextRenderer(e,t,i,n){const r=this._source.properties().childs();let s,a;const l=r.labelFontSize.value()/3;let d=0;switch(r.labelVertAlign.value()){case o.VerticalAlign.Bottom:e.y<i.y?(s=e,a=t):(s=i,a=n);break;case o.VerticalAlign.Top:e.y>i.y?(s=e,a=t):(s=i,a=n);break;case o.VerticalAlign.Middle:s=e.add(i).scaled(.5),a=t.add(n).scaled(.5),d=l}const h=s.x<a.x?s:a,c=h===s?a:s;let _;switch(r.labelHorzAlign.value()){case o.HorizontalAlign.Left:_=h;break;case o.HorizontalAlign.Right:_=c;break;default:_=h.add(c).scaled(.5)}return this._labelTextRenderer.setData({points:[_],color:r.labelTextColor.value(),fontSize:r.labelFontSize.value(),text:r.labelText.value(),font:u.CHART_FONT_FAMILY,bold:r.labelBold.value(),italic:r.labelItalic.value(),vertAlign:r.labelVertAlign.value(),horzAlign:r.labelHorzAlign.value(),offsetX:0,offsetY:0,boxPaddingVert:l,boxPaddingHorz:d,forceTextAlign:!0,angle:Math.atan((h.y-c.y)/(h.x-c.x))}),this._labelTextRenderer}}},116468:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ElliottLabelsPaneView:()=>v});var n=i(895379),r=i(69549),s=i(822960),o=i(589637),a=i(936879),l=i(184114),d=i(86441),h=i(934026),c=i(652283),u=i(885800);class _{constructor(e,t){this._data=e,this._hitTestResult=t}hitTest(e){const t=this._center(),i=this._data.circleRadius,n={min:new d.Point(t.x-i,t.y-i),max:new d.Point(t.x+i,t.y+i)};return(0,h.pointInBox)(e,n)?this._hitTestResult:null}draw(e,t){e.save();const{horizontalPixelRatio:i,verticalPixelRatio:n}=t,r=Math.max(1,Math.floor(i))%2/2,s=this._center(),o=Math.round(s.x*i)+r,a=Math.round(s.y*n)+r;if(this._data.showCircle){const t=Math.round(o+this._data.circleRadius*i)-o-this._data.circleBorderWidth*i/2;e.strokeStyle=this._data.color,e.lineWidth=this._data.circleBorderWidth*i,e.beginPath(),e.moveTo(o+t,a),e.arc(o,a,t,0,2*Math.PI,!1),e.stroke()}e.font=(0,u.makeFont)(this._data.fontSize,this._data.font,this._data.bold?"bold":void 0),e.textBaseline="middle",e.textAlign="center",e.fillStyle=this._data.color,(0,c.drawScaled)(e,i,n,(()=>{e.fillText(this._data.letter,o/i,a/n+.05*this._data.fontSize)})),e.restore()}_center(){const e="bottom"===this._data.vertAlign?-1:1,t=this._data.point.y+e*this._data.yOffset+e*this._data.circleRadius,i=this._data.point.x;return new d.Point(i,t)}}var p=i(799839),g=i(144044),f=i(306099);const x={4:{font:24,circle:36,circleBorderWidth:1,bold:!0},3:{font:20,circle:28,circleBorderWidth:1,bold:!1},2:{font:18,circle:22,circleBorderWidth:1,bold:!1},1:{font:16,circle:22,circleBorderWidth:1,bold:!1},0:{font:11,circle:14,circleBorderWidth:1,bold:!0}};class v extends n.LineSourcePaneView{constructor(e,t){super(e,t),this._renderer=null,this._polylineRenderer=new f.PolygonRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){
super._updateImpl(e),this._renderer=null;const t=this._source.properties().childs(),i=this._source.priceScale(),n=this._model.timeScale(),d=this._source.ownerSource()?.firstValue();if(!i||i.isEmpty()||n.isEmpty()||null==d)return;const h=new r.CompositeRenderer;if(t.showWave.value()){const e={points:this._points,color:(0,o.generateColor)(t.color.value(),0),linewidth:t.linewidth.value(),linestyle:a.LINESTYLE_SOLID,fillBackground:!1,filled:!1,backcolor:"rgba(0, 0, 0, 0)",linejoin:"round"};this._polylineRenderer.setData(e),h.append(this._polylineRenderer)}const c=this.areAnchorsVisible()?0:1;let u=1;if(this._points.length>2){const e=this._points[2],t=this._points[1];u=(0,s.sign)(e.y-t.y)}let f=0;this._model.lineBeingCreated()===this._source&&(f=1);const v=(0,o.resetTransparency)(t.color.value());for(let e=0;e<this._points.length-f;e++,u=-u){if(e<c)continue;const t=this._source.label(e);let i=t.label;const n="circle"===t.decoration;"brackets"===t.decoration&&(i="("+i+")");const r=x[t.group],s=new l.HitTestResult(l.HitTarget.ChangePoint,{pointIndex:e});h.append(new _({point:this._points[e],letter:i,color:v,font:p.CHART_FONT_FAMILY,fontSize:r.font,bold:r.bold,showCircle:n,circleRadius:r.circle/2,circleBorderWidth:r.circleBorderWidth,yOffset:10,vertAlign:1===u?"top":"bottom"},s))}const w=this._points.map(((e,t)=>(0,g.lineSourcePaneViewPointToLineAnchorPoint)(e,t)));this._model.lineBeingCreated()===this._source&&w.pop(),h.append(this.createLineAnchor({points:w},0)),this._renderer=h}}},269718:(e,t,i)=>{"use strict";i.r(t),i.d(t,{EllipsePaneView:()=>R});var n=i(204652),r=i(86441),s=i(5531),o=i(986226),a=i(589637),l=i(799839),d=i(69549),h=i(184114),c=i(863192),u=i(144044),_=i(895379),p=i(980317),g=i(10666),f=i(625422),x=i(767313),v=i(161656);class w extends x.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data={...e,angleFrom:0,angleTo:2*Math.PI,clockwise:!1}}hitTest(e){if(null===this._data||this._data.points.length<3)return null;const t=this._data.points[0],i=this._data.points[1];let s=this._data.points[2];const o=(0,n.distanceToLine)(t,i,s).distance,a=i.subtract(t),l=t.add(i).scaled(.5),d=new r.Point(-a.y,a.x).normalized();s=l.add(d.scaled(o));const c=a.length(),u=a.x/c,_=a.y/c;let p=Math.acos(u);_<0&&(p=-p);let g=(0,f.translationMatrix)(-l.x,-l.y);e=(0,f.transformPoint)(g,e);let x=(0,f.transformPoint)(g,this._data.points[2]);g=(0,f.rotationMatrix)(-p),e=(0,f.transformPoint)(g,e),x=(0,f.transformPoint)(g,x),g=(0,f.scalingMatrix)(1,c/(2*o)),e=(0,f.transformPoint)(g,e),x=(0,f.transformPoint)(g,x);const w=e.length(),R=(0,v.interactionTolerance)().curve;return Math.abs(w-.5*c)<=R?new h.HitTestResult(h.HitTarget.MovePoint):!this._data.noHitTestOnBackground&&w<=.5*c?new h.HitTestResult(this._data.backgroundHitTarget??h.HitTarget.MovePointBackground):null}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=this._data.points[0],i=this._data.points[1],s=e.context;if(this._data.points.length<3)return s.strokeStyle=this._data.color,s.lineWidth=this._data.linewidth,
s.beginPath(),s.moveTo(t.x,t.y),s.lineTo(i.x,i.y),void s.stroke();let o=this._data.points[2];const a=(0,n.distanceToLine)(t,i,o).distance;if(a<1)return s.strokeStyle=this._data.color,s.lineWidth=this._data.linewidth,s.beginPath(),s.moveTo(t.x,t.y),s.lineTo(i.x,i.y),void s.stroke();const l=i.subtract(t),d=t.add(i).scaled(.5),h=new r.Point(-l.y,l.x).normalized();o=d.add(h.scaled(a)),s.strokeStyle=this._data.color,s.lineWidth=this._data.linewidth;const c=l.length(),u=l.x/c,_=l.y/c;let p=Math.acos(u);_<0&&(p=-p);let g=this._data.points[2],x=(0,f.translationMatrix)(-d.x,-d.y);g=(0,f.transformPoint)(x,g),x=(0,f.rotationMatrix)(-p),g=(0,f.transformPoint)(x,g),x=(0,f.scalingMatrix)(1,c/(2*a)),g=(0,f.transformPoint)(x,g),g.y<0?this._data.clockwise=!0:this._data.clockwise=!1,s.save(),s.beginPath(),s.translate(d.x,d.y),s.rotate(p),s.scale(1,2*a/c),s.arc(0,0,.5*c,this._data.angleFrom,this._data.angleTo,this._data.clockwise),s.restore(),s.stroke(),s.fillStyle=this._data.backcolor,s.fill()}}class R extends p.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._ellipseRenderer=new w,this._renderer=null,this._textRenderer=new g.LineToolTextRenderer(void 0,new h.HitTestResult(h.HitTarget.MovePoint,(0,p.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,rotationPoint:this._textRenderer.rotation()??void 0,...this._textRenderer.getTextInfo()})),this._textRenderer.positionToCoordinate.bind(this._textRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const t=this._source.properties().childs(),i={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),backcolor:t.fillBackground.value()?(0,a.generateColor)(t.backgroundColor.value(),t.transparency.value()):"transparent",noHitTestOnBackground:!1,backgroundHitTarget:this._model.selection().isSelected(this._source)?h.HitTarget.MovePoint:void 0};this._ellipseRenderer.setData(i);const s=new d.CompositeRenderer;s.append(this._ellipseRenderer);const o=i.points[0],l=i.points[1];if(2===this._points.length)return this.addAnchors(s),void(this._renderer=s);let c=i.points[2];const p=(0,n.distanceToLine)(o,l,c).distance,g=l.subtract(o),f=o.add(l).scaled(.5),x=new r.Point(-g.y,g.x).normalized();c=f.add(x.scaled(p));const v=f.add(x.scaled(-p)),w=this._placeHolderMode();(t.showLabel.value()&&t.text.value()||w||this._isTextEditMode())&&this._updateTextRenderer([o,l,c,v],w,e)&&s.append(this._textRenderer);const R=(0,_.thirdPointCursorType)(o,l),T=[(0,u.lineSourcePaneViewPointToLineAnchorPoint)(o,0),(0,u.lineSourcePaneViewPointToLineAnchorPoint)(l,1),(0,u.lineSourcePaneViewPointToLineAnchorPoint)(c,2,R),(0,u.lineSourcePaneViewPointToLineAnchorPoint)(v,3,R)];s.append(this.createLineAnchor({points:T},0)),this._renderer=s}_updateTextRenderer([e,t,i,n],a,d){if(t.subtract(e).length()<1e-5||n.subtract(i).length()<1e-5)return!1;const h=(0,
s.intersectLines)((0,r.lineThroughPoints)(e,t),(0,r.lineThroughPoints)(i,n));if(!h)return!1;const{fontSize:u,bold:_,italic:p}=this._source.properties().childs(),g=Math.sqrt(2),f=e.subtract(t).length()/g;if(this._textRenderer.setData({points:[h],text:this._textData(),color:this._textColor(),fontSize:u.value(),font:l.CHART_FONT_FAMILY,bold:_.value(),italic:p.value(),wordWrapWidth:a?void 0:f,maxHeight:a?void 0:n.subtract(i).length()/g,angle:Math.atan((e.y-t.y)/(e.x-t.x)),offsetX:0,offsetY:0,horzAlign:o.HorizontalAlign.Center,vertAlign:o.VerticalAlign.Middle,forceTextAlign:!0,decorator:a?c.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()}),a&&this._textRenderer.measure().width>f&&(this._textRenderer.updateData({text:""}),this._textRenderer.measure().width>f))return!1;this._textRenderer.setCursorType(this._textCursorType());const{mediaSize:{width:x,height:v}}=d;return this._textRenderer.isOutOfScreen(x,v)?this.closeTextEditor():this._updateInplaceText(this._textRenderer.getTextInfo()),!0}}},722681:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ExecutionPaneView:()=>h});var n,r=i(86441),s=i(895379),o=i(184114),a=i(767313);!function(e){e[e.ArrowWidth=4]="ArrowWidth"}(n||(n={}));class l extends a.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=e??null}hitTest(e){if(null===this._data)return null;const t=Math.round(this._data.point.x),i=Math.round(this._data.point.y),n=this._data.arrowHeight;let r,s;if("buy"===this._data.direction?(r=i,s=i+n):(r=i-n,s=i),e.x>=t-2&&e.x<=t+2&&e.y>=r&&e.y<=s){const e=this._data.tooltip;return new o.HitTestResult(o.HitTarget.Custom,{tooltip:""!==e?{text:e,rect:{x:t,y:r,w:2,h:s-r}}:void 0})}return null}setData(e){this._data=e}_drawImpl(e){const t=e.context;if(null===this._data)return;const i=Math.round(this._data.point.x),n=Math.round(this._data.point.y);!function(e,t,i,n,r,s){e.save(),e.strokeStyle=n,e.fillStyle=n,e.translate(t-2,i),"buy"!==r&&(e.rotate(Math.PI),e.translate(-4,0)),e.beginPath(),e.moveTo(2,s),e.lineTo(2,0),e.moveTo(0,2),e.lineTo(2,0),e.lineTo(4,2),e.stroke(),e.restore()}(t,i,n,this._data.arrowColor,this._data.direction,this._data.arrowHeight);const{arrowHeight:r,arrowSpacing:s,fontHeight:o,direction:a,text:l,font:d,textColor:h}=this._data,c=function(e,t,i){if(0===t.length)return 0;e.save(),e.font=i;const n=e.measureText(t).width;return e.restore(),5+n}(t,l,d);if(0!==c){const e="buy"===a?n+r+s:n-r-s-o;!function(e,t,i,n,r,s,o,a){if(!s)return;e.save(),e.textAlign="center",e.textBaseline="middle",e.font=o,e.fillStyle=a;const l=t+n/2,d=i+r/2;e.fillText(s,l,d-1),e.restore()}(t,Math.round(i+.5-c/2),e,c,o,l,d,h)}}}var d=i(58274);class h extends s.LineSourcePaneView{constructor(){super(...arguments),this._executionRenderer=new l,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._source,i=t.points();if(0===i.length)return;const n=t.adapter(),s=t.model().timeScale(),o=t.model().paneForSource(t)?.executionsPositionController();if(!o)return
;const a=o.getXYCoordinate(n,s,i[0].index);!isFinite(a.y)||a.y<0||a.y>e.mediaSize.height||a.x<0||(this._executionRenderer.setData({point:new r.Point(a.x,a.y),arrowColor:n.getArrowColor(),arrowHeight:n.getArrowHeight(),direction:n.getDirection(),tooltip:n.getTooltip(),arrowSpacing:n.getArrowSpacing(),fontHeight:d.fontHeight(n.getFont()),text:n.getText(),textColor:n.getTextColor(),font:n.getFont()}),this._renderer=this._executionRenderer)}}},995474:(e,t,i)=>{"use strict";i.d(t,{FibHorizontalLevelsPaneViewBase:()=>C});var n=i(650151),r=i(86441),s=i(609838),o=i(986226),a=i(419395),l=i(659011),d=i(454304),h=i(863192);class c{geometry(e){const t=(0,d.fontSize)(e);return{decoratorAndTextMargin:t/3,width:Math.round(t/8),ignoreRtl:!0}}draw(e,t,i,n){const{horizontalPixelRatio:r,verticalPixelRatio:s}=t,o=Math.max(1,Math.round(r*n.decoratorWidth/2)),a=Math.round((n.textTop+n.textBottom)/2*s),l=Math.round((n.decoratorLeft+n.decoratorWidth/2)*r);e.fillStyle=i.color,e.beginPath(),e.arc(l,a,o,0,2*Math.PI),e.fill()}static instance(){return this._instance=this._instance??new c,this._instance}}c._instance=null;var u,_=i(184114),p=i(69549),g=i(586982),f=i(33549),x=i(822960);function v(e,t,i,n){if(null!==e&&t===o.VerticalAlign.Middle){const t=(0,d.getTextBoundaries)(e,i,n);if(t)return[t]}return[]}function w(e){return"left"===e?0:"center"===e?1:2}function R(e){const{labelAndTextHaveSameAlign:t,labelAndTextHaveSameVertAlign:i,labelRenderer:s,textRenderer:o,horzTextAlign:a,horzLabelsAlign:l,textMaxWidth:d}=e;if(t)return function(e){const{labelRenderer:t,textRenderer:i,horzTextAlign:n,textMaxWidth:s,extendLeft:o,extendRight:a}=e,l=T(t),d=T(i);switch(n){case"left":o?m(l,d):y(l,d);break;case"center":if(P(d,s))return{hideText:!0};!function(e,t,i,n){const s=i-n/2,o=e?.rect.width,a=t?.rect.width;e&&e.renderer.setPoint((0,r.point)(s+o/2,e.point.y));t&&t.renderer.setPoint((0,r.point)(s+o+a/2,t.point.y))}(l,d,l.rect.x+l.rect.width/2,l.rect.width+d.rect.width);break;case"right":a?y(l,d):m(l,d)}return{hideText:!1}}(e);if(null!==d){if(P(T(o),d))return{hideText:!0}}if(i){const e=s.rect(),t=o.rect(),i=(0,n.ensureNotNull)(o.point());if(w(a)<w(l)){const n=t.x+t.width+f.labelEdgeOffset-e.x;n>0&&o.setPoint((0,r.point)(i.x-n,i.y))}else{const n=e.x+e.width+f.labelEdgeOffset-t.x;n>0&&o.setPoint((0,r.point)(i.x+n,i.y))}}return{hideText:!1}}function T(e){return{renderer:e,rect:e.rect(),point:(0,n.ensureNotNull)(e.point()),horzAlign:(0,n.ensureNotNull)(e.data()).horzAlign}}function m(e,t){var i,n;t.renderer.setPoint((0,r.point)(e.rect.x+e.rect.width+(i=t.horzAlign,n=t.rect.width,"right"===i?n:0),t.point.y),0)}function y(e,t){var i,n;e.renderer.setPoint((0,r.point)(t.rect.x-(i=e.horzAlign,n=e.rect.width,"left"===i?n:0),e.point.y),0)}function P(e,t){return null!==t&&(e.rect.width>t&&(e.renderer.setData({...(0,n.ensureNotNull)(e.renderer.data()),text:""}),e.rect=e.renderer.rect(),e.rect.width>t))}function L(e){const{leftAnchorX:t,rightAnchorX:i,extendLeft:n,extendRight:r,screenWidth:s,labelRenderer:o,horzLabelsAlign:a,labelAndTextHaveSameVertAlign:l}=e;let d
;const h=l?function(e,t){return e?(0,x.clamp)(e.x+e.width,0,t)-(0,x.clamp)(e.x,0,t):0}(o?.rect(),s):0;return d=n||r?n&&r?s-h:n?i-("left"===a?h:0):s-t-("right"===a?h:0):i-t-("center"===a?h:0),d-8}!function(e){e[e.LevelHitTestTolerance=4]="LevelHitTestTolerance",e[e.CenterTextMinHorzMargins=4]="CenterTextMinHorzMargins"}(u||(u={}));const b=s.t(null,void 0,i(606060));function S(e){if(!e)return;const t=(0,n.ensureNotNull)(e.data()),i=t.vertAlign;if("middle"!==i){const s=(0,n.ensureDefined)(t.points)[0],a=("top"===i?1:-1)*((0,d.fontSize)(t)+(0,d.lineSpacing)(t));e.setData({...t,vertAlign:o.VerticalAlign.Middle,points:[(0,r.point)(s.x,s.y+a)]})}}const A=[o.HorizontalAlign.Center,o.VerticalAlign.Middle];class C extends f.LineToolPaneViewFibWithLabels{constructor(){super(...arguments),this._renderer=new p.CompositeRenderer,this._levelLineRenderers=new Map,this._lastEditableLevelIndex=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateTextWasEditable(){const e=this._isTextEditMode()?this._inplaceEditLevelIndex:null;null!==e&&this._lastEditableLevelIndex===e?super._updateTextWasEditable():this._lastEditableLevelIndex=e}_addLevels(e){const{mediaSize:t,levels:i,showLabel:s,showText:o,labelAlign:d,textAlign:u,extendLeft:p,extendRight:f,isOnScreen:x,trendLineRenderer:w}=e;let{left:T,right:m}=e;T===m&&(p&&(T-=1),f&&(m+=1));const{width:y,height:P}=t,C=this._model.hoveredSource()===this._source?this._model.lastHittestData()?.activeItem:null,M=[],k=[],B=this._model.selection().isSelected(this._source),I=this._textCursorType(),[H,D]=d;for(const e of i){const t=new r.Point(T,e.y),i=new r.Point(m,e.y),d=Boolean(e.text),w=this._isTextEditMode()&&this._inplaceEditLevelIndex===e.index,z=!d&&!w,[V,E]=z?A:u,N=H===V&&D===E,W={levelIndex:e.index,leftPoint:t,rightPoint:i,color:e.color,extendLeft:p,extendRight:f},F=e.index===C;let O,Y=null;const q=o&&(d||F&&B||w),X=q&&!d&&!w;!q||X&&(0,a.lastMouseOrTouchEventInfo)().isTouch||(O=X?h.PlusTextRendererDecorator.instance():s&&N?c.instance():void 0,Y=(0,n.ensureDefined)(this._textRenderers[e.index]),this._updateRendererLabel({...W,...w?this._inplaceTextHighlight():{},horzAlign:V,vertAlign:E,color:w?this._textColor():W.color,decorator:O},Y,w?this._textData():e.text||b),Y.setCursorType(I));const j=O===c.instance(),Q=this._updateLabelForLevel({...W,price:e.price,horzAlign:H,vertAlign:D,boxPaddingRight:j?0:void 0});let U=!1;if(null!==Y&&null!==Q){const e=D===E;U=R({labelRenderer:Q,textRenderer:Y,labelAndTextHaveSameAlign:N,labelAndTextHaveSameVertAlign:e,horzTextAlign:V,horzLabelsAlign:H,textMaxWidth:X?L({leftAnchorX:T,rightAnchorX:m,extendLeft:p,extendRight:f,screenWidth:y,horzLabelsAlign:H,labelRenderer:Q,labelAndTextHaveSameVertAlign:e}):null,extendLeft:p,extendRight:f}).hideText}if(x){S(Y),S(Q);if(!(!p&&!f&&(Y&&"center"===V&&"middle"===E&&m-4<Y.rect().x+Y.rect().width||Q&&"center"===H&&"middle"===D&&T+4>Q.rect().x))){const r=[...v(Q,D,y,P),...v(U?null:Y,E,y,P)],s={points:[t,i],color:e.color,linewidth:e.linewidth,linestyle:e.linestyle,extendleft:p,extendright:f,
leftend:g.LineEnd.Normal,rightend:g.LineEnd.Normal,excludeBoundaries:r,hitTestTolerance:4},o=(0,n.ensureDefined)(this._levelLineRenderers.has(e.index)?this._levelLineRenderers.get(e.index):this._levelLineRenderers.set(e.index,new l.TrendLineRenderer).get(e.index));o.setData(s),o.setHitTest(new _.HitTestResult(_.HitTarget.MovePoint,{snappingPrice:e.price,activeItem:e.index},e.index)),M.push(o)}}null===Q||Q.isOutOfScreen(y,P)||k.push(Q),U||null===Y||Y.isOutOfScreen(y,P)||k.push(Y)}for(const e of M)this._renderer.append(e);w&&this._renderer.append(w);for(const e of k)this._renderer.append(e);if(this._isTextEditMode()){const e=(0,n.ensureDefined)(this._textRenderers[this._inplaceEditLevelIndex]);e.isOutOfScreen(y,P)?this.closeTextEditor():this._updateInplaceText(e.getTextInfo())}}}},33549:(e,t,i)=>{"use strict";i.d(t,{LineToolPaneViewFibWithLabels:()=>c,labelEdgeOffset:()=>h});var n=i(650151),r=i(454304),s=i(184114),o=i(251940),a=i(799839),l=i(980317),d=i(10666);const h=4;class c extends l.InplaceTextLineSourcePaneView{constructor(e,t,i,a,h){super(e,t,i,a,h),this._labelsRenderers={},this._numericFormatter=(0,o.getNumericFormatter)(),this._percentageFormatter=(0,o.getPercentageFormatter)(),this._textRenderers={},this._inplaceEditLevelIndex=1;for(let t=1;t<=e.levelsCount();t++)this._labelsRenderers[t]=new r.TextRenderer(void 0,new s.HitTestResult(s.HitTarget.MovePoint,{activeItem:t}));if(i&&a&&h){for(let t=1;t<=e.levelsCount();t++){const e=new s.HitTestResult(s.HitTarget.MovePoint,{...(0,l.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,t)),activeItem:t});this._textRenderers[t]=new d.LineToolTextRenderer(void 0,e)}this._source.setAdditionalCursorData((()=>{const e=(0,n.ensureDefined)(this._textRenderers[this._inplaceEditLevelIndex]);return{color:this._source.editableTextStyle().cursorColor,...e.getTextInfo()}}),(e=>(0,n.ensureDefined)(this._textRenderers[this._inplaceEditLevelIndex]).positionToCoordinate(e)))}}_tryActivateEditMode(e,t){this._inplaceEditLevelIndex=e,super._tryActivateEditMode(e,t)}_activateEditMode(e){const t=(0,n.ensureDefined)(this._textRenderers[this._inplaceEditLevelIndex]);this._updateInplaceText(t.getTextInfo()),super._activateEditMode(e)}_updateLabelForLevel(e){const t=this._labelsRenderers[e.levelIndex];if(void 0===t)return null;const i=this._source.priceScale();if(!i)return null;const n=this._source.ownerSource()?.firstValue();if(null==n)return null;const r=this._source.properties(),s=Boolean(r.showCoeffs?.value()),o=Boolean(r.showPrices?.value());if(!s&&!o)return null;const a=r["level"+e.levelIndex].coeff.value();let l="";if(s){l+=r.coeffsAsPercents?.value()??!1?this._percentageFormatter.format(100*a,{signPositive:!1,tailSize:2}):this._numericFormatter.format(a)}return o&&(l+=" ("+i.formatPrice(e.price,n)+")"),this._updateRendererLabel(e,t,l),t}_updateRendererLabel(e,t,i){if(!i&&void 0===e.decorator)return null;const{leftPoint:n,rightPoint:s,horzAlign:o,extendLeft:l,extendRight:d,...c}=e,u=this._source.properties(),[_,p]=(0,r.getTextAlignInBox)({horzAlign:o,extendLeft:l,extendRight:d,
width:this._model.timeScale().width(),leftPoint:n,rightPoint:s});return t.setData({points:[_],text:i,horzAlign:p,offsetX:h,offsetY:0,font:a.CHART_FONT_FAMILY,fontSize:u.labelFontSize?.value()??12,...c}),t}}},404313:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibChannelPaneView:()=>u});var n=i(650151),r=i(589637),s=i(586982),o=i(69549),a=i(960491),l=i(659011),d=i(33549),h=i(454304);class c extends a.ParallelChannelRenderer{constructor(){super(...arguments),this._data=null}_drawLine(e,t,i){const{context:n,bitmapSize:r}=e,s=this._data?.excludeBoundaries;if(void 0!==s){n.save(),n.beginPath(),n.rect(0,0,r.width,r.height);for(let t=0;t<s.length;t++){const{x:i,y:r}=s[t];0!==t?n.lineTo(i*e.horizontalPixelRatio,r*e.verticalPixelRatio):n.moveTo(i*e.horizontalPixelRatio,r*e.verticalPixelRatio)}n.closePath(),n.clip("evenodd")}super._drawLine(e,t,i),void 0!==s&&n.restore()}}class u extends d.LineToolPaneViewFibWithLabels{constructor(){super(...arguments),this._baseLineRenderer=new l.TrendLineRenderer,this._lastLevelTrendRenderer=new l.TrendLineRenderer,this._renderer=null,this._norm=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e);const t=this._source.priceScale();if(null===t)return;this._renderer=null;const i=this._source.ownerSource()?.firstValue();if(null==i)return;3===this._points.length&&3===this._source.points().length&&(this._norm=this._points[2].subtract(this._points[0]));const a=new o.CompositeRenderer;if(this._points.length<2)return this.addAnchors(a),void(this._renderer=a);const l=this._source.properties().childs(),d=this._points[0],u=this._points[1];if(this._points.length<3){const e={points:[d,u],color:l.level1.childs().color.value(),linewidth:l.levelsStyle.childs().linewidth.value(),linestyle:l.levelsStyle.childs().linestyle.value(),extendleft:l.extendLeft.value(),extendright:l.extendRight.value(),leftend:s.LineEnd.Normal,rightend:s.LineEnd.Normal};return this._baseLineRenderer.setData(e),a.append(this._baseLineRenderer),this.addAnchors(a),void(this._renderer=a)}const _=this._source.levelsCount(),p=(0,n.ensureNotNull)(this._norm),{mediaSize:{width:g,height:f}}=e;for(let e=1;e<_;e++){const s=(0,n.ensureDefined)(this._source.properties().child("level"+e)).childs();if(!s.visible.value())continue;let o=null;for(let t=e+1;t<=_;t++){const e=(0,n.ensureDefined)(this._source.properties().child("level"+t)).childs();if(e.visible.value()){o=e;break}}if(!o)break;const x=p.scaled(s.coeff.value()),v=d.add(x),w=u.add(x),R=p.scaled(o.coeff.value()),T=d.add(R),m=u.add(R),y=s.color.value(),P=l.extendLeft.value(),L=l.extendRight.value(),b=l.levelsStyle.childs().linestyle.value(),S=l.levelsStyle.childs().linewidth.value(),A=t.coordinateToPrice(v.y,i),C=this._updateLabelForLevel({levelIndex:e,leftPoint:v,rightPoint:w,price:A,color:y,horzAlign:l.horzLabelsAlign.value(),vertAlign:l.vertLabelsAlign.value()});let M;null!==C&&(a.append(C),M=(0,h.getTextBoundaries)(C,g,f)??void 0);const k={line1:{color:y,lineStyle:b,lineWidth:S,points:[v,w]},line2:{color:y,lineStyle:b,lineWidth:S,
points:[T,m]},extendLeft:P,extendRight:L,backColor:(0,r.generateColor)(y,l.transparency.value(),!0),skipTopLine:!0,fillBackground:l.fillBackground.value(),hittestOnBackground:!0,excludeBoundaries:M},B=new c;B.setData(k),a.append(B)}let x=null;for(let e=_;e>=1;e--){if((0,n.ensureDefined)(this._source.properties().child("level"+e)).childs().visible.value()){x=e;break}}if(null!==x){const e=(0,n.ensureDefined)(this._source.properties().child("level"+x)).childs();if(e.visible.value()){const n=p.scaled(e.coeff.value()),r=d.add(n),o=u.add(n),c=t.coordinateToPrice(r.y,i),_=this._updateLabelForLevel({levelIndex:x,leftPoint:r,rightPoint:o,price:c,color:e.color.value(),horzAlign:l.horzLabelsAlign.value(),vertAlign:l.vertLabelsAlign.value()});let v;null!==_&&(a.append(_),v=(0,h.getTextBoundaries)(_,g,f)??void 0);const w={points:[r,o],color:e.color.value(),linewidth:l.levelsStyle.childs().linewidth.value(),linestyle:l.levelsStyle.childs().linestyle.value(),extendleft:l.extendLeft.value(),extendright:l.extendRight.value(),leftend:s.LineEnd.Normal,rightend:s.LineEnd.Normal,excludeBoundaries:v?[v]:void 0};this._lastLevelTrendRenderer.setData(w),a.append(this._lastLevelTrendRenderer)}}this.addAnchors(a),this._renderer=a}}},30339:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibCirclesPaneView:()=>u});var n=i(650151),r=i(86441),s=i(986226),o=i(33549),a=i(659011),l=i(184114),d=i(69549),h=i(338933),c=i(586982);class u extends o.LineToolPaneViewFibWithLabels{constructor(){super(...arguments),this._trendLineRenderer=new a.TrendLineRenderer,this._renderer=new d.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<2||this._points.length<2)return;const t=this._source.priceScale();if(!t||t.isEmpty()||this._model.timeScale().isEmpty())return;const[i,o]=this._points,a=i.add(o).scaled(.5),d=Math.abs(o.x-i.x),u=Math.abs(o.y-i.y),_=[],p=this._source.properties(),g=this._source.levelsCount();for(let e=1;e<=g;e++){const t=(0,n.ensureDefined)(p.child(`level${e}`)).childs();if(!t.visible.value())continue;const i=t.coeff.value(),s=t.color.value(),o=[];o.push(new r.Point(a.x-.5*d*i,a.y-.5*u*i)),o.push(new r.Point(a.x+.5*d*i,a.y+.5*u*i));const l=new r.Point(a.x,a.y+.5*u*i);_.push({color:s,points:o,labelPoint:l,linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),index:e})}const f=p.childs().fillBackground.value(),x=p.childs().transparency.value();for(let e=0;e<_.length;e++){const t=_[e];this._renderer.append(new h.EllipseRendererSimple({points:t.points,color:t.color,linewidth:t.linewidth,backcolor:t.color,wholePoints:e>0?_[e-1].points:void 0,fillBackground:f,transparency:x},new l.HitTestResult(l.HitTarget.MovePoint,void 0,t.index)));const i=this._updateLabelForLevel({levelIndex:t.index,color:t.color,price:0,vertAlign:s.VerticalAlign.Middle,horzAlign:s.HorizontalAlign.Left,leftPoint:t.labelPoint,rightPoint:t.labelPoint});null!==i&&this._renderer.append(i)}const v=p.childs().trendline.childs()
;v.visible.value()&&(this._trendLineRenderer.setData({points:[this._points[0],this._points[1]],color:v.color.value(),linewidth:v.linewidth.value(),linestyle:v.linestyle.value(),extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal}),this._renderer.append(this._trendLineRenderer)),this.addAnchors(this._renderer)}}},126878:(e,t,i)=>{"use strict";i.d(t,{fibLevelCoordinate:()=>r,fibLevelPrice:()=>s});var n=i(650151);function r(e,t,i,r,s,o){if(o)return Math.round((0,n.ensureDefined)(e.coordinate)+(0,n.ensureDefined)(t.coordinate)*i);const a=e.price+t.price*i;return r.priceToCoordinate(a,s)}function s(e,t,i,r,s,o){if(!o)return e.price+t.price*i;const a=(0,n.ensureDefined)(e.coordinate)+(0,n.ensureDefined)(t.coordinate)*i;return r.coordinateToPrice(a,s)}},324020:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibRetracementPaneView:()=>d});var n=i(86441),r=i(862903),s=i(659011),o=i(586982),a=i(126878),l=i(995474);class d extends l.FibHorizontalLevelsPaneViewBase{constructor(){super(...arguments),this._trendLineRenderer=new s.TrendLineRenderer,this._levels=[]}_tryActivateEditMode(e,t){super._tryActivateEditMode(e,t),this._source.setInplaceEditLevelIndex(e)}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<2)return;const t=this._source.priceScale();if(!t||t.isEmpty()||this._model.timeScale().isEmpty())return;const i=this._source.ownerSource()?.firstValue();if(null==i)return;const[s,l]=this._source.points(),d=this._source.properties().childs(),h=d.reverse.value();if(this._points.length<2)return;const c=this._points[0],u=this._points[1],_=Math.min(c.x,u.x),p=Math.max(c.x,u.x),g=d.fillBackground.value(),f=d.transparency.value(),x=d.extendLinesLeft.value(),v=d.extendLines.value(),w=t.isLog()&&d.fibLevelsBasedOnLogScale.value(),{mediaSize:{width:R}}=e,T=!(_>R&&!x||p<0&&!v);this._levels=[];const m=h?s.price:l.price,y=h?l.price:s.price,P=y-m,L=t.priceToCoordinate(m,i),b={price:m,coordinate:L},S={price:P,coordinate:t.priceToCoordinate(y,i)-L},A=this._source.levelsCount();for(let e=1;e<=A;e++){const n=d["level"+e].childs();if(!n||!n.visible.value())continue;const r=n.coeff.value(),s=(0,a.fibLevelCoordinate)(b,S,r,t,i,w),o=(0,a.fibLevelPrice)(b,S,r,t,i,w);this._levels.push({color:n.color.value(),text:n.text.value(),y:s,price:o,linewidth:d.levelsStyle.childs().linewidth.value(),linestyle:d.levelsStyle.childs().linestyle.value(),index:e})}if(g&&T)for(let e=0;e<this._levels.length;e++)if(e>0&&g){const t=this._levels[e-1],i={points:[new n.Point(_,this._levels[e].y),new n.Point(p,t.y)],color:this._levels[e].color,linewidth:0,backcolor:this._levels[e].color,fillBackground:!0,transparency:f,extendLeft:x,extendRight:v},s=new r.RectangleRenderer(!0);s.setData(i),this._renderer.append(s)}let C=_,M=p;C===M&&(x&&(C-=1),v&&(M+=1));const k=d.trendline.childs();let B=null;k.visible.value()&&T&&(B=this._trendLineRenderer,B.setData({points:[this._points[0],this._points[1]],color:k.color.value(),linewidth:k.linewidth.value(),linestyle:k.linestyle.value(),extendleft:!1,extendright:!1,
leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal})),this._addLevels({mediaSize:e.mediaSize,levels:this._levels,left:C,right:M,showLabel:d.showCoeffs.value()||d.showPrices.value(),showText:d.showText.value(),labelAlign:[d.horzLabelsAlign.value(),d.vertLabelsAlign.value()],textAlign:[d.horzTextAlign.value(),d.vertTextAlign.value()],extendLeft:x,extendRight:v,fontSize:d.labelFontSize.value(),isOnScreen:T,trendLineRenderer:B}),this.addAnchors(this._renderer),this._model.selection().isSelected(this._source)||this.closeTextEditor()}}},265797:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibTimeZonePaneView:()=>g});var n=i(86441),r=i(457563),s=i(454304),o=i(862903),a=i(659011),l=i(184114),d=i(69549),h=i(586982),c=i(986226),u=i(799839),_=i(144044),p=i(895379);class g extends p.LineSourcePaneView{constructor(e,t){super(e,t),this._levels=[],this._trendRenderer=new a.TrendLineRenderer,this._textRenderers=[],this._renderer=new d.CompositeRenderer;for(let t=0;t<e.levelsCount();t++)this._textRenderers.push(new s.TextRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<1)return;const t=this._model.timeScale();if(t.isEmpty())return;const[i,a]=this._source.points(),d=this._source.properties().childs(),p=i.index;if(null===t.visibleBarsStrictRange())return;this._levels=[];const g=a?a.index-i.index:1;for(let e=1;e<=this._source.levelsCount();e++){const i=d["level"+e].childs();if(!i.visible.value())continue;const n=Math.round(p+i.coeff.value()*g),r={index:e,x:t.indexToCoordinate(n),color:i.color.value(),width:i.linewidth.value(),style:i.linestyle.value(),text:String(i.coeff.value())};this._levels.push(r)}const{mediaSize:{width:f,height:x}}=e;if(d.fillBackground.value()){const e=d.transparency.value();for(let t=1;t<this._levels.length;t++){const i=this._levels[t-1],r={points:[new n.Point(this._levels[t].x,0),new n.Point(i.x,x)],color:this._levels[t].color,linewidth:0,backcolor:this._levels[t].color,fillBackground:!0,transparency:e,extendLeft:!1,extendRight:!1},s=new o.RectangleRenderer(!0);s.setData(r),this._renderer.append(s)}}let v=d.horzLabelsAlign.value();v=v===c.HorizontalAlign.Left?c.HorizontalAlign.Right:v===c.HorizontalAlign.Right?c.HorizontalAlign.Left:c.HorizontalAlign.Center;const w=d.vertLabelsAlign.value();for(let e=0;e<this._levels.length;e++){let t;const i=this._levels[e].color;if(d.showLabels.value()){let r;switch(w){case"top":r=new n.Point(this._levels[e].x,0);break;case"middle":r=new n.Point(this._levels[e].x,.5*x);break;default:r=new n.Point(this._levels[e].x,x)}const o={points:[r],text:this._levels[e].text,color:i,vertAlign:w,horzAlign:v,font:u.CHART_FONT_FAMILY,offsetX:2,offsetY:0,fontsize:12},a=this._textRenderers[e];a.setData(o),this._needLabelExclusionPath(a)&&(t=(0,s.getTextBoundaries)(a,f,x)??void 0),this._renderer.append(a)}const o={x:this._levels[e].x,color:i,linewidth:this._levels[e].width,linestyle:this._levels[e].style,excludeBoundaries:t
},a=new l.HitTestResult(l.HitTarget.MovePoint,void 0,this._levels[e].index),h=new r.VerticalLineRenderer;h.setData(o),h.setHitTest(a),this._renderer.append(h)}if(2===this._points.length){const e=d.trendline.childs(),t={points:[this._points[0],this._points[1]],color:e.color.value(),linewidth:e.linewidth.value(),linestyle:e.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal};this._trendRenderer.setData(t),this._renderer.append(this._trendRenderer)}2===this._source.points().length?this._renderer.append(this.createLineAnchor({points:this._points.map((e=>({point:e,pointIndex:e.pointIndex})))},0)):this._points.length>0&&this._renderer.append(this.createLineAnchor({points:[(0,_.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(this._points[0].x,x/2),void 0,void 0,void 0,l.HitTarget.MovePoint)]},0))}_needLabelExclusionPath(e){return"center"===this._source.properties().childs().horzLabelsAlign.value()}}},399236:(e,t,i)=>{"use strict";i.d(t,{FibWedgePaneView:()=>_});var n=i(650151),r=i(86441),s=i(986226),o=i(33549),a=i(659011),l=i(184114),d=i(69549),h=i(372082),c=i(586982),u=i(144044);class _ extends o.LineToolPaneViewFibWithLabels{constructor(){super(...arguments),this._renderer=null,this._levels=[],this._baseTrendRenderer=new a.TrendLineRenderer,this._edgeTrendRenderer=new a.TrendLineRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._levels=[],this._points.length<3)return void this._updateRenderer();const[t,i,s]=this._points,o=i.subtract(t).normalized(),a=s.subtract(t).normalized(),l=new r.Point(1,0),d=new r.Point(0,1);let h=Math.acos(o.dotProduct(l));o.dotProduct(d)<0&&(h=2*Math.PI-h);let c=Math.acos(a.dotProduct(l));if(a.dotProduct(d)<0&&(c=2*Math.PI-c),c<h&&([h,c]=[c,h]),Math.abs(h-c)>Math.PI){const e=Math.min(h,c);h=Math.max(h,c),c=e+2*Math.PI}const u=this._source.properties();for(let e=1;e<=this._source.levelsCount();e++){const r="level"+e,s=(0,n.ensureDefined)(u.child(r));if(!s.childs().visible.value())continue;const l=s.childs().coeff.value(),d=s.childs().color.value(),h=Math.abs(i.subtract(t).length()*l),c=o.add(a).scaled(.5).normalized().scaled(h),_=t.add(c);this._levels.push({coeff:l,color:d,radius:h,labelPoint:_,p1:t.add(o.scaled(h)),p2:t.add(a.scaled(h)),linewidth:s.childs().linewidth.value(),linestyle:s.childs().linestyle.value(),index:e})}this._points.length<2||this._updateRenderer(h,c)}_updateRenderer(e=NaN,t=NaN){if(this._points.length<2)return;const i=new d.CompositeRenderer,n=this._source.properties().childs(),[r,o]=this._points,a=n.trendline.childs().visible.value()?n.trendline.childs().linewidth.value():0,_=n.trendline.childs().linestyle.value();if(this._baseTrendRenderer.setData({points:[r,o],color:n.trendline.childs().color.value(),linewidth:a,linestyle:_,extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal}),i.append(this._baseTrendRenderer),this._points.length<3)return this.addAnchors(i),void(this._renderer=i)
;let p=this._points[2];const g=p.pointIndex,f=o.subtract(r).length(),x=p.subtract(r).normalized();p=r.add(x.scaled(f)),p.pointIndex=g,this._edgeTrendRenderer.setData({points:[r,p],color:n.trendline.childs().color.value(),linewidth:a,linestyle:_,extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal}),i.append(this._edgeTrendRenderer);for(let r=this._levels.length-1;r>=0;r--){const o=this._levels[r],a=new h.ArcWedgeRenderer;a.setData({center:this._points[0],radius:o.radius,prevRadius:r>0?this._levels[r-1].radius:0,color:o.color,linewidth:o.linewidth,angle1:e,angle2:t,p1:o.p1,p2:o.p2,fillBackground:n.fillBackground.value(),transparency:n.transparency.value(),color1:"",color2:""}),a.setHitTest(new l.HitTestResult(l.HitTarget.MovePoint,void 0,o.index)),i.append(a);const d=this._updateLabelForLevel({levelIndex:o.index,color:o.color,leftPoint:o.labelPoint,rightPoint:o.labelPoint,price:0,horzAlign:s.HorizontalAlign.Left,vertAlign:s.VerticalAlign.Middle});null!==d&&i.append(d)}const v=[r,o];this._model.lineBeingCreated()!==this._source&&v.push(p),i.append(this.createLineAnchor({points:v.map(u.mapLineSourcePaneViewPointToLineAnchorPoint)},0)),this._renderer=i}}},976375:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FlagMarkPaneView:()=>_});var n,r=i(895379),s=i(69549),o=i(144044),a=i(652374),l=i(184114),d=i(934026),h=i(898646),c=i(767313);!function(e){e[e.Width=20]="Width",e[e.Height=22]="Height"}(n||(n={}));class u extends c.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data)return null;const{x:t,y:i}=this._data.point;return e.x<t||e.x>t+20||e.y<i-22||e.y>i?null:new l.HitTestResult(l.HitTarget.MovePoint)}doesIntersectWithBox(e){return null!==this._data&&(0,d.pointInBox)(this._data.point,e)}_drawImpl(e){if(null===this._data)return;const t=e.context;t.translate(Math.round(this._data.point.x)-.5,Math.round(this._data.point.y-22)-.5),t.fillStyle="#4A4A4A",(0,h.drawRoundRect)(t,0,0,2,22,1),t.fill(),t.fillStyle=this._data.color,t.beginPath(),t.moveTo(6.87,0),t.bezierCurveTo(5.62,0,4.46,.23,3.32,.69),t.bezierCurveTo(3.26,.71,3.2,.75,3.15,.8),t.bezierCurveTo(3.06,.89,3,1.02,3,1.16),t.lineTo(3,1.19),t.lineTo(3,12.5),t.bezierCurveTo(3,12.8,3.3,13.02,3.59,12.93),t.bezierCurveTo(4.61,12.64,5.94,12.44,6.87,12.44),t.bezierCurveTo(8.5,12.44,10.09,12.83,11.63,13.21),t.bezierCurveTo(13.19,13.6,14.79,14,16.45,14),t.bezierCurveTo(17.59,14,18.65,13.81,19.69,13.43),t.bezierCurveTo(19.88,13.36,20,13.18,20,12.98),t.lineTo(20,1.19),t.bezierCurveTo(20,1.06,19.83,.93,19.66,.99),t.bezierCurveTo(18.63,1.38,17.58,1.56,16.45,1.56),t.bezierCurveTo(14.82,1.56,13.23,1.17,11.69,.79),t.bezierCurveTo(10.14,.4,8.53,0,6.87,0),t.closePath(),t.fill()}}class _ extends r.LineSourcePaneView{constructor(){super(...arguments),this._flagMarkRenderer=new u,this._renderer=null,this._anchorsOffset=null}setAnchors(e){this._anchorsOffset=e}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,
1!==this._points.length)return;this._flagMarkRenderer.setData({point:this._points[0],color:this._getSource().properties().childs().flagColor.value()});const t=this._getModel();this._renderer=new s.CompositeRenderer,this._renderer.append(this._flagMarkRenderer);const i=[this._anchorsOffset?this._points[0].add(this._anchorsOffset):this._points[0].clone()];this._renderer.append(new a.SelectionRenderer({points:i.map(o.mapLineSourcePaneViewPointToLineAnchorPoint),bgColors:this._lineAnchorColors(i),visible:this.areAnchorsVisible(),barSpacing:t.timeScale().barSpacing(),hittestResult:l.HitTarget.MovePoint}))}}},723062:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FlatBottomPaneView:()=>p});var n=i(650151),r=i(86441),s=i(601227),o=i(986226),a=i(59918),l=i(659011),d=i(454304),h=i(69549),c=i(799839),u=i(895379),_=i(144044);class p extends u.LineSourcePaneView{constructor(){super(...arguments),this._trendLineRendererPoints12=new l.TrendLineRenderer,this._trendLineRendererPoints43=new l.TrendLineRenderer,this._disjointChannelRenderer=new a.DisjointChannelRenderer,this._p1LabelRenderer=new d.TextRenderer,this._p2LabelRenderer=new d.TextRenderer,this._p3LabelRenderer=new d.TextRenderer,this._p4LabelRenderer=new d.TextRenderer,this._labelTextRenderer=new d.TextRenderer,this._renderer=new h.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<2)return;const t=this._source.priceScale(),i=this._source.ownerSource()?.firstValue();if(!t||null==i)return;const[a,l]=this._source.points(),d=t.formatPrice(a.price,i),h=t.formatPrice(l.price,i);let u;if(3===this._source.points().length){const e=this._source.points()[2];u=t.formatPrice(e.price,i)}if(this._points.length<2)return;const[p,g]=this._points;let f,x;const v=this._source.properties().childs();if(3===this._points.length){if(f=(0,r.point)(g.x,this._points[2].y),x=(0,r.point)(p.x,f.y),v.fillBackground.value()){const e=v.extendLeft.value(),t=v.extendRight.value();this._disjointChannelRenderer.setData({extendleft:e,extendright:t,points:[p,g,f,x],backcolor:v.backgroundColor.value(),transparency:v.transparency.value(),hittestOnBackground:s.CheckMobile.any()}),this._renderer.append(this._disjointChannelRenderer)}v.labelVisible.value()&&v.labelText.value()&&this._renderer.append(this._getLabelTextRenderer(p,g,x,f))}const w=(e,t)=>({points:[e,t],color:v.linecolor.value(),linewidth:v.linewidth.value(),linestyle:v.linestyle.value(),extendleft:v.extendLeft.value(),extendright:v.extendRight.value(),leftend:v.leftEnd.value(),rightend:v.rightEnd.value()});if(this._trendLineRendererPoints12.setData(w(p,g)),this._renderer.append(this._trendLineRendererPoints12),2===this._points.length)return void this.addAnchors(this._renderer);const R=(e,t,i,n,r,s)=>{v.showPrices.value()&&(e.setData({points:[i],text:r,color:v.textcolor.value(),horzAlign:i.x>n.x?o.HorizontalAlign.Left:o.HorizontalAlign.Right,vertAlign:o.VerticalAlign.Middle,font:c.CHART_FONT_FAMILY,offsetX:6,offsetY:0,boxPadding:0,
bold:v.bold.value(),italic:v.italic.value(),fontsize:v.fontsize.value(),forceTextAlign:!0}),this._renderer.append(e),t.setData({points:[n],text:s,color:v.textcolor.value(),horzAlign:i.x<n.x?o.HorizontalAlign.Left:o.HorizontalAlign.Right,vertAlign:o.VerticalAlign.Middle,font:c.CHART_FONT_FAMILY,offsetX:6,offsetY:0,boxPadding:0,bold:v.bold.value(),italic:v.italic.value(),fontsize:v.fontsize.value(),forceTextAlign:!0}),this._renderer.append(t))};if(R(this._p1LabelRenderer,this._p2LabelRenderer,p,g,d,h),!f||!x)return void this.addAnchors(this._renderer);this._trendLineRendererPoints43.setData(w(x,f)),this._renderer.append(this._trendLineRendererPoints43),R(this._p3LabelRenderer,this._p4LabelRenderer,f,x,(0,n.ensureDefined)(u),(0,n.ensureDefined)(u));const T=[(0,_.lineSourcePaneViewPointToLineAnchorPoint)(p),(0,_.lineSourcePaneViewPointToLineAnchorPoint)(g),(0,_.lineSourcePaneViewPointToLineAnchorPoint)(f,2),(0,_.lineSourcePaneViewPointToLineAnchorPoint)(x,3)];this._model.lineBeingCreated()===this._source&&T.pop(),this._renderer.append(this.createLineAnchor({points:T},0)),p&&g&&this._addAlertRenderer(this._renderer,[p,g])}_getLabelTextRenderer(e,t,i,n){const r=this._source.properties().childs();let s,a;const l=r.labelFontSize.value()/3;let d=0;switch(r.labelVertAlign.value()){case o.VerticalAlign.Bottom:e.y<i.y?(s=e,a=t):(s=i,a=n);break;case o.VerticalAlign.Top:e.y>i.y?(s=e,a=t):(s=i,a=n);break;case o.VerticalAlign.Middle:s=e.add(i).scaled(.5),a=t.add(n).scaled(.5),d=l}const h=s.x<a.x?s:a,u=h===s?a:s;let _;switch(r.labelHorzAlign.value()){case o.HorizontalAlign.Left:_=h;break;case o.HorizontalAlign.Right:_=u;break;default:_=h.add(u).scaled(.5)}return this._labelTextRenderer.setData({points:[_],color:r.labelTextColor.value(),fontSize:r.labelFontSize.value(),text:r.labelText.value(),font:c.CHART_FONT_FAMILY,bold:r.labelBold.value(),italic:r.labelItalic.value(),vertAlign:r.labelVertAlign.value(),horzAlign:r.labelHorzAlign.value(),offsetX:0,offsetY:0,boxPaddingVert:l,boxPaddingHorz:d,forceTextAlign:!0,angle:Math.atan((h.y-u.y)/(h.x-u.x))}),this._labelTextRenderer}}},416937:(e,t,i)=>{"use strict";i.d(t,{GannArcRenderer:()=>a});var n=i(86441),r=i(589637),s=i(184114),o=i(767313);class a extends o.MediaCoordinatesPaneRenderer{constructor(){super(),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data)return null;e=e.subtract(this._data.center);const t=this._data.edge.subtract(this._data.center),i=t.y/t.x;e=new n.Point(e.x,e.y/i);let r=this._data.point.subtract(this._data.center);r=new n.Point(r.x,r.y/i);const o=r.length(),a=e.length();let l=this._data.prevPoint.subtract(this._data.center);l=new n.Point(l.x,l.y/i);const d=l.length();return Math.abs(a-o)<5&&t.x*e.x>=0&&t.y*e.y>=0?new s.HitTestResult(s.HitTarget.MovePoint):this._data.fillBack&&a>=d&&a<=o&&t.x*e.x>=0&&t.y*e.y>=0?new s.HitTestResult(s.HitTarget.MovePointBackground):null}_drawImpl(e){if(null===this._data)return;const t=e.context;t.lineCap="butt",t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,
t.translate(this._data.center.x,this._data.center.y);const i=this._data.edge.subtract(this._data.center),s=i.y/i.x;let o=this._data.point.subtract(this._data.center);o=new n.Point(o.x,o.y/s);let a=o.length(),l=this._data.prevPoint.subtract(this._data.center);l=new n.Point(l.x,l.y/s);let d=l.length();t.scale(1,s);const h=Math.abs(this._data.edge.x-this._data.center.x);if(Math.abs(a)>h){const e=Math.sign(this._data.edge.x-this._data.center.x)*h;t.rect(0,0,e,e),t.clip()}this._data.fillBack&&(this._data.point.x<this._data.center.x&&(a=-a,d=-d),t.beginPath(),t.moveTo(d,0),t.lineTo(a,0),t.arcTo(a,a,0,a,Math.abs(a)),t.lineTo(0,d),t.arcTo(d,d,d,0,Math.abs(d)),t.fillStyle=(0,r.generateColor)(this._data.color,this._data.transparency,!0),t.fill()),t.beginPath(),this._data.point.x>this._data.center.x?t.arc(0,0,Math.abs(a),0,Math.PI/2,!1):t.arc(0,0,Math.abs(a),-Math.PI/2,-Math.PI,!0),t.scale(1,1/s),t.stroke()}}},326971:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GannComplexPaneView:()=>g});var n,r=i(86441),s=i(986226),o=i(895379),a=i(659011),l=i(454304),d=i(69549),h=i(144044),c=i(586982),u=i(936879),_=i(416937),p=i(431520);!function(e){e[e.LabelsOffset=10]="LabelsOffset",e[e.LabelsOffsetTop=8]="LabelsOffsetTop",e[e.LabelsOffsetBottom=10]="LabelsOffsetBottom",e[e.ArcMaxLevel=5]="ArcMaxLevel"}(n||(n={}));class g extends o.LineSourcePaneView{constructor(e,t){super(e,t),this._verticalLevelsRenderers=[],this._horizontalLevelsRenderers=[],this._fanRenderers=[],this._arcRenderers=[],this._priceDiffTextRenderer=new l.TextRenderer,this._indexDiffTextRenderer=new l.TextRenderer,this._ratioTextRenderer=new l.TextRenderer,this._renderer=null,this._initRenderers()}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=new d.CompositeRenderer,i=this._getPoints();if(i.length<2)return this.addAnchors(t),void(this._renderer=t);let[n,r]=i;const s=this._getSource(),o=s.isReversed();o&&([r,n]=i);const a=r.x-n.x,l=r.y-n.y,c=n,u=r,_=this._getModel(),p={barsCoordsRange:a,priceCoordsRange:l,startPoint:c,endPoint:u,p1:n,p2:r,isLabelsVisible:s.isLabelsVisible(),reversed:o};this._prepareLevels(t,p),this._prepareFanLines(t,p),this._prepareArcs(t,p),this._prepareLabels(t,p);const g=[n,r];_.lineBeingCreated()===s&&g.pop(),t.append(this.createLineAnchor({points:g.map(h.mapLineSourcePaneViewPointToLineAnchorPoint)},0)),this._renderer=t}_initRenderers(){const e=this._getSource(),t=e.levelsCount();for(let e=0;e<t;e++)this._verticalLevelsRenderers.push(new a.TrendLineRenderer),this._horizontalLevelsRenderers.push(new a.TrendLineRenderer);const i=e.fanLinesCount();for(let e=0;e<i;e++)this._fanRenderers.push(new a.TrendLineRenderer);const n=e.arcsCount();for(let e=0;e<n;e++)this._arcRenderers.push(new _.GannArcRenderer)}_prepareLevels(e,t){const{startPoint:i,endPoint:n,barsCoordsRange:s,priceCoordsRange:o}=t,a=this._getSource().levels();for(const t of a){if(!t.visible)continue;const a=t.index/5,l=i.x+a*s,d={points:[new r.Point(l,i.y),new r.Point(l,n.y)],color:t.color,linewidth:t.width,
linestyle:u.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal},h=this._verticalLevelsRenderers[t.index];h.setData(d),e.append(h);const _=i.y+a*o,p={points:[new r.Point(i.x,_),new r.Point(n.x,_)],color:t.color,linewidth:t.width,linestyle:u.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal},g=this._horizontalLevelsRenderers[t.index];g.setData(p),e.append(g)}}_prepareFanLines(e,t){const{p1:i,startPoint:n,endPoint:s,barsCoordsRange:o,priceCoordsRange:a}=t,l=this._getSource().fanLines();for(const t of l){if(!t.visible)continue;const l=t.x,d=t.y;let h,_;if(l>d){h=s.x;const e=d/l;_=n.y+e*a}else{_=s.y;const e=l/d;h=n.x+e*o}const p={points:[i,new r.Point(h,_)],color:t.color,linewidth:t.width,linestyle:u.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal},g=this._fanRenderers[t.index];g.setData(p),e.append(g)}}_prepareArcs(e,t){const{p1:i,startPoint:n,endPoint:s,barsCoordsRange:o,priceCoordsRange:a}=t;let l=i;const d=this._getSource(),h=d.isArcsBackgroundFilled(),c=d.arcsBackgroundTransparency(),u=d.arcs();for(const t of u){if(!t.visible)continue;const i=t.x/5,d=t.y/5,u=n.x+i*o,_=n.y+d*a,p={center:n,point:new r.Point(u,_),edge:s,color:t.color,linewidth:t.width,fillBack:h,transparency:c,prevPoint:l},g=this._arcRenderers[t.index];g.setData(p),e.append(g),l=p.point}}_prepareLabels(e,t){const{p1:i,p2:n,isLabelsVisible:o,reversed:a}=t;if(!o)return;const l=this._getSource(),d=l.ownerSource();let h=l.getPriceDiff(),c=l.getIndexDiff();if(null===h||null===c||null===d)return;a&&(h=-h,c=-c);const u=new r.Point(i.x,n.y),_=(0,p.forceLTRStr)(d.formatter().format(h)),g=this._getLabelData(u,_);g.horzAlign=c>0?s.HorizontalAlign.Right:s.HorizontalAlign.Left,g.vertAlign=h>0?s.VerticalAlign.Bottom:s.VerticalAlign.Top,g.offsetX=10,g.offsetY=h>0?8:10,g.forceTextAlign=!0,this._priceDiffTextRenderer.setData(g),e.append(this._priceDiffTextRenderer);const f=new r.Point(n.x,i.y),x=(0,p.forceLTRStr)(c.toString()),v=this._getLabelData(f,x);v.horzAlign=c>0?s.HorizontalAlign.Left:s.HorizontalAlign.Right,v.vertAlign=h>0?s.VerticalAlign.Top:s.VerticalAlign.Bottom,v.offsetX=10,v.offsetY=h>0?10:8,v.forceTextAlign=!0,this._indexDiffTextRenderer.setData(v),e.append(this._indexDiffTextRenderer);const w=l.getScaleRatio();if(null===w)return;const R=l.getScaleRatioFormatter(),T=(0,p.forceLTRStr)(R.format(w)),m=this._getLabelData(n,T);m.horzAlign=c>0?s.HorizontalAlign.Left:s.HorizontalAlign.Right,m.vertAlign=h>0?s.VerticalAlign.Bottom:s.VerticalAlign.Top,m.offsetX=10,m.offsetY=h>0?8:10,m.forceTextAlign=!0,this._ratioTextRenderer.setData(m),e.append(this._ratioTextRenderer)}_getLabelData(e,t){const i=this._getSource(),{textColor:n,font:r,fontSize:o,bold:a,italic:l}=i.getLabelsStyle();return{points:[e],backgroundColor:"transparent",text:t,font:r,bold:a,italic:l,fontsize:o,color:n,vertAlign:s.VerticalAlign.Top,horzAlign:s.HorizontalAlign.Center,offsetX:0,offsetY:0,backgroundRoundRect:4}}}},708192:(e,t,i)=>{"use strict";i.r(t),i.d(t,{
GannFanPaneView:()=>p});var n=i(650151),r=i(86441),s=i(986226),o=i(799839),a=i(184114),l=i(625790),d=i(69549),h=i(586982),c=i(454304),u=i(659011),_=i(895379);class p extends _.LineSourcePaneView{constructor(e,t){super(e,t),this._textRenderers=[],this._renderer=null;for(let t=0;t<e.levelsCount();t++)this._textRenderers.push(new c.TextRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._source.points().length<2)return;const t=this._source.priceScale();if(!t||t.isEmpty()||this._model.timeScale().isEmpty())return;if(this._points.length<2)return;const i=this._points[0],c=this._points[1],_=[],p=c.x-i.x,g=c.y-i.y,f=this._source.properties().childs();for(let e=1;e<=this._source.levelsCount();e++){const t="level"+e,r=(0,n.ensureDefined)(this._source.properties().child(t)).childs();if(!r.visible.value())continue;const s=r.coeff1.value(),o=r.coeff2.value(),a=s/o,l=r.color.value(),d=s+"/"+o;let h,u;s>o?(h=c.x,u=i.y+g/a):(h=i.x+p*a,u=c.y),_.push({label:d,color:l,x:h,y:u,linewidth:r.linewidth.value(),linestyle:r.linestyle.value(),index:e})}const x=new d.CompositeRenderer,v=f.fillBackground.value(),w=f.transparency.value();for(let e=0;e<_.length;e++){const t=new r.Point(_[e].x,_[e].y);if(v)if(_[e].index<4){const n={p1:i,p2:t,p3:i,p4:new r.Point(_[e+1].x,_[e+1].y),color:_[e].color,transparency:w,hittestOnBackground:!0,extendLeft:!1},s=new l.ChannelRenderer;s.setData(n),x.append(s)}else if(_[e].index>4&&e>0){const n={p1:i,p2:t,p3:i,p4:new r.Point(_[e-1].x,_[e-1].y),color:_[e].color,transparency:w,hittestOnBackground:!0,extendLeft:!1},s=new l.ChannelRenderer;s.setData(n),x.append(s)}{const n={points:[i,t],color:_[e].color,linewidth:_[e].linewidth,linestyle:_[e].linestyle,extendleft:!1,extendright:!0,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal},r=new u.TrendLineRenderer;r.setData(n),r.setHitTest(new a.HitTestResult(a.HitTarget.MovePoint,void 0,_[e].index)),x.append(r)}if(f.showLabels.value()){const i={points:[t],text:_[e].label,color:_[e].color,vertAlign:s.VerticalAlign.Middle,horzAlign:s.HorizontalAlign.Left,font:o.CHART_FONT_FAMILY,offsetX:0,offsetY:5,fontsize:12},n=this._textRenderers[e];n.setData(i),x.append(n)}}this.addAnchors(x),this._renderer=x}}},261648:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GannFixedPaneView:()=>u});var n,r=i(86441),s=i(895379),o=i(659011),a=i(144044),l=i(69549),d=i(586982),h=i(936879),c=i(416937);!function(e){e[e.ArcMaxLevel=5]="ArcMaxLevel"}(n||(n={}));class u extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._verticalLevelsRenderers=[],this._horizontalLevelsRenderers=[],this._fanRenderers=[],this._arcRenderers=[],this._renderer=null,this._initRenderers()}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._getSource(),i=this._getPoints(),n=t.getScreenPoints();if(i.length<2||n.length<2)return;const[r,o]=n;i[1]=(0,s.createLineSourcePaneViewPoint)(r,1),i[2]=(0,s.createLineSourcePaneViewPoint)(o,2)
;const d=this._getPoints(),h=new l.CompositeRenderer;if(d.length<2)return this.addAnchors(h),void(this._renderer=h);const c=d[0],u=3===d.length?d[2]:d[1],_=u.x-c.x,p=u.y-c.y,g=c,f=u,x=this._getModel(),v={barsCoordsRange:_,priceCoordsRange:p,startPoint:g,endPoint:f,p1:c,p2:u};this._prepareLevels(h,v),this._prepareFanLines(h,v),this._prepareArcs(h,v);const w=[c,d[1]];x.lineBeingCreated()===t&&w.pop(),h.append(this.createLineAnchor({points:w.map(a.mapLineSourcePaneViewPointToLineAnchorPoint)},0)),this._renderer=h}_initRenderers(){const e=this._getSource(),t=e.levelsCount();for(let e=0;e<t;e++)this._verticalLevelsRenderers.push(new o.TrendLineRenderer),this._horizontalLevelsRenderers.push(new o.TrendLineRenderer);const i=e.fanLinesCount();for(let e=0;e<i;e++)this._fanRenderers.push(new o.TrendLineRenderer);const n=e.arcsCount();for(let e=0;e<n;e++)this._arcRenderers.push(new c.GannArcRenderer)}_prepareLevels(e,t){const{startPoint:i,endPoint:n,barsCoordsRange:s,priceCoordsRange:o}=t,a=this._getSource().levels();for(const t of a){if(!t.visible)continue;const a=t.index/5,l=i.x+a*s,c={points:[new r.Point(l,i.y),new r.Point(l,n.y)],color:t.color,linewidth:t.width,linestyle:h.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},u=this._verticalLevelsRenderers[t.index];u.setData(c),e.append(u);const _=i.y+a*o,p={points:[new r.Point(i.x,_),new r.Point(n.x,_)],color:t.color,linewidth:t.width,linestyle:h.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},g=this._horizontalLevelsRenderers[t.index];g.setData(p),e.append(g)}}_prepareFanLines(e,t){const{p1:i,startPoint:n,endPoint:s,barsCoordsRange:o,priceCoordsRange:a}=t,l=this._getSource().fanLines();for(const t of l){if(!t.visible)continue;const l=t.x,c=t.y;let u,_;if(l>c){u=s.x;const e=c/l;_=n.y+e*a}else{_=s.y;const e=l/c;u=n.x+e*o}const p={points:[i,new r.Point(u,_)],color:t.color,linewidth:t.width,linestyle:h.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},g=this._fanRenderers[t.index];g.setData(p),e.append(g)}}_prepareArcs(e,t){const{p1:i,startPoint:n,endPoint:s,barsCoordsRange:o,priceCoordsRange:a}=t;let l=i;const d=this._getSource(),h=d.isArcsBackgroundFilled(),c=d.arcsBackgroundTransparency(),u=d.arcs();for(const t of u){if(!t.visible)continue;const i=t.x/5,d=t.y/5,u=n.x+i*o,_=n.y+d*a,p={center:n,point:new r.Point(u,_),edge:s,color:t.color,linewidth:t.width,fillBack:h,transparency:c,prevPoint:l},g=this._arcRenderers[t.index];g.setData(p),e.append(g),l=p.point}}}},194265:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GannSquarePaneView:()=>g});var n=i(650151),r=i(86441),s=i(986226),o=i(799839),a=i(251940),l=i(69549),d=i(144044),h=i(586982),c=i(862903),u=i(454304),_=i(659011),p=i(895379);class g extends p.LineSourcePaneView{constructor(e,t){super(e,t),this._hlevels=[],this._vlevels=[],this._hfans=[],this._vfans=[],this._renderer=new l.CompositeRenderer,this._leftLabelRenderers=[],this._rightLabelRenderers=[],this._topLabelRenderers=[],
this._bottomLabelRenderers=[];for(let t=0;t<e.hLevelsCount();t++)this._leftLabelRenderers.push(new u.TextRenderer),this._rightLabelRenderers.push(new u.TextRenderer);for(let t=0;t<e.vLevelsCount();t++)this._topLabelRenderers.push(new u.TextRenderer),this._bottomLabelRenderers.push(new u.TextRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.points(),i=this._source.ownerSource()?.firstValue()??null;if(t.length<2||null===i)return;const l=this._source.priceScale(),u=this._model.timeScale();if(!l||l.isEmpty()||u.isEmpty())return;const g=t[0],f=t[1],x=this._source.properties().childs(),v=x.reverse.value();this._hlevels=[];const w=v?g.price-f.price:f.price-g.price,R=v?f.price:g.price;for(let e=1;e<=this._source.hLevelsCount();e++){const t="hlevel"+e,r=(0,n.ensureDefined)(this._source.properties().child(t)).childs();if(!r.visible.value())continue;const s=r.coeff.value(),o=r.color.value(),a=R+s*w,d=l.priceToCoordinate(a,i);this._hlevels.push({coeff:s,color:o,y:d})}this._vlevels=[];const T=v?g.index-f.index:f.index-g.index,m=v?f.index:g.index;for(let e=1;e<=this._source.vLevelsCount();e++){const t="vlevel"+e,i=(0,n.ensureDefined)(this._source.properties().child(t)).childs();if(!i.visible.value())continue;const r=i.coeff.value(),s=i.color.value(),o=Math.round(m+r*T),a=this._model.timeScale().indexToCoordinate(o);this._vlevels.push({coeff:r,color:s,x:a})}if(this._hfans=[],this._vfans=[],x.fans.childs().visible.value())for(let e=1;e<=this._source.hLevelsCount();e++){const t=(0,n.ensureDefined)(this._source.properties().child("hlevel"+e)).childs(),r=(0,n.ensureDefined)(this._source.properties().child("vlevel"+e)).childs(),s=Math.round(m+t.coeff.value()*T),o=R+r.coeff.value()*w;this._hfans.push(u.indexToCoordinate(s)),this._vfans.push(l.priceToCoordinate(o,i))}if(this._points.length<2)return void this.addAnchors(this._renderer);const y=this._points[0],P=this._points[1],L=Math.min(y.x,P.x),b=Math.min(y.y,P.y),S=Math.max(y.x,P.x),A=Math.max(y.y,P.y),C=x.fillHorzBackground.value(),M=x.horzTransparency.value(),k=x.fillVertBackground.value(),B=x.vertTransparency.value(),I=(0,a.getNumericFormatter)();for(let e=0;e<this._hlevels.length;e++){if(e>0&&C){const t=new c.RectangleRenderer(!0);t.setData({points:[new r.Point(L,this._hlevels[e].y),new r.Point(S,this._hlevels[e-1].y)],color:this._hlevels[e].color,linewidth:0,backcolor:this._hlevels[e].color,fillBackground:!0,transparency:M,extendLeft:!1,extendRight:!1}),this._renderer.append(t)}const t=new r.Point(L,this._hlevels[e].y),i=new r.Point(S,this._hlevels[e].y),n=new _.TrendLineRenderer;if(n.setData({points:[t,i],color:this._hlevels[e].color,linewidth:x.linewidth.value(),linestyle:x.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal}),this._renderer.append(n),x.showLeftLabels.value()){const i=this._leftLabelRenderers[e];i.setData({points:[t],text:I.format(this._hlevels[e].coeff),color:this._hlevels[e].color,
vertAlign:s.VerticalAlign.Middle,horzAlign:s.HorizontalAlign.Right,font:o.CHART_FONT_FAMILY,offsetX:5,offsetY:0,fontsize:12,forceTextAlign:!0}),this._renderer.append(i)}if(x.showRightLabels.value()){const t=this._rightLabelRenderers[e];t.setData({points:[i],text:I.format(this._hlevels[e].coeff),color:this._hlevels[e].color,vertAlign:s.VerticalAlign.Middle,horzAlign:s.HorizontalAlign.Left,font:o.CHART_FONT_FAMILY,offsetX:5,offsetY:0,fontsize:12}),this._renderer.append(t)}}for(let e=0;e<this._vlevels.length;e++){const t=new r.Point(this._vlevels[e].x,b),i=new r.Point(this._vlevels[e].x,A);if(e>0&&k){const t=new c.RectangleRenderer(!0);t.setData({points:[new r.Point(this._vlevels[e-1].x,b),i],color:this._vlevels[e].color,linewidth:0,backcolor:this._vlevels[e].color,fillBackground:!0,transparency:B,extendLeft:!1,extendRight:!1}),this._renderer.append(t)}const n=new _.TrendLineRenderer;if(n.setData({points:[t,i],color:this._vlevels[e].color,linewidth:x.linewidth.value(),linestyle:x.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal}),this._renderer.append(n),x.showTopLabels.value()){const i=this._topLabelRenderers[e];i.setData({points:[t],text:I.format(this._vlevels[e].coeff),color:this._vlevels[e].color,vertAlign:s.VerticalAlign.Bottom,horzAlign:s.HorizontalAlign.Center,font:o.CHART_FONT_FAMILY,offsetX:0,offsetY:3,fontsize:12}),this._renderer.append(i)}if(x.showBottomLabels.value()){const t=this._bottomLabelRenderers[e];t.setData({points:[i],text:I.format(this._vlevels[e].coeff),color:this._vlevels[e].color,vertAlign:s.VerticalAlign.Top,horzAlign:s.HorizontalAlign.Center,font:o.CHART_FONT_FAMILY,offsetX:0,offsetY:5,fontsize:12}),this._renderer.append(t)}}const H=(e,t)=>{const i=new r.Point(L,b),n=new r.Point(S,b),s=new r.Point(L,A),o=new r.Point(S,A),a=e=>{const t=new _.TrendLineRenderer;t.setData({points:e,color:x.fans.childs().color.value(),linewidth:x.linewidth.value(),linestyle:x.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal}),this._renderer.append(t)};for(let l=0;l<e.length;++l){const d=t?A:e[l],h=t?b:e[l],c=t?e[l]:L,u=t?e[l]:S,_=new r.Point(u,d),p=new r.Point(c,d),g=new r.Point(u,h),f=new r.Point(c,h);a([s,g]),a([o,f]),a([i,_]),a([n,p])}};H(this._hfans,!0),H(this._vfans,!1);const D=(0,p.createLineSourcePaneViewPoint)(new r.Point(this._points[0].x,this._points[1].y),2),z=(0,p.createLineSourcePaneViewPoint)(new r.Point(this._points[1].x,this._points[0].y),3);this._renderer.append(this.createLineAnchor({points:[...this._points,D,z].map(d.mapLineSourcePaneViewPointToLineAnchorPoint)},0))}}},245780:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GhostFeedPaneView:()=>p});var n=i(926048),r=i(936879),s=i(41899),o=i(184114),a=i(69549),l=i(161656),d=i(586982),h=i(659011),c=i(567062),u=i(895379);const _=n.colorsPalette["color-cold-gray-500"];class p extends u.LineSourcePaneView{constructor(){super(...arguments),this._renderer=null,this._segments=[]}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){
super._updateImpl(e),this._renderer=null,this._segments=[];const t=this._source.priceScale(),i=this._source.ownerSource()?.firstValue()??null;if(this._points.length<2||null===t||t.isEmpty()||null===i)return;const n=this._source.points(),u=this._source.properties().childs(),p=u.candleStyle.childs(),g=p.upColor.value(),f=p.downColor.value(),x=p.borderUpColor.value(),v=p.borderDownColor.value();this._segments=this._source.segments().map(((e,r)=>{if(r>=this._points.length-1)return null;let s=this._points[r],o=this._points[r+1],a=n[r].price,l=n[r+1].price,d=1;s.x>o.x&&(s=this._points[r+1],o=this._points[r],a=n[r+1].price,l=n[r].price,d=-1);const h=s.x,c=this._model.timeScale().coordinateToIndex(h),u=t.priceToCoordinate(a,i),_=t.priceToCoordinate(l,i),p=e.bars(),w=(_-u)/(p.length-1),R=[];let T=d>0?0:p.length-1;const m=d>0?p.length:-1;for(let e=0;T!==m;T+=d,e++){const n=u+e*w,r=t.coordinateToPrice(n,i),s=p[T],o=s.c>=s.o;R.push({open:t.priceToCoordinate(r+s.o,i),high:t.priceToCoordinate(r+s.h,i),low:t.priceToCoordinate(r+s.l,i),close:t.priceToCoordinate(r+s.c,i),color:o?g:f,borderColor:o?x:v,hollow:!1,center:NaN,left:NaN,right:NaN,timePointIndex:c+e})}return this._model.timeScale().fillBarBorders(R),{bars:R}})).filter(s.notNull);const w=new a.CompositeRenderer;for(let e=1;e<this._points.length;e++){const t={points:[this._points[e-1],this._points[e]],color:_,linewidth:1,linestyle:r.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},i=new h.TrendLineRenderer;i.setData(t),i.setHitTest(new o.HitTestResult(o.HitTarget.MovePoint)),w.append(i)}const R=p.drawWick.value(),T=p.drawBorder.value(),m=p.borderColor.value(),y=p.wickColor.value(),P=new a.CompositeRenderer;P.setGlobalAlpha(1-u.transparency.value()/100);const L=this._model.timeScale().barSpacing();for(let e=0;e<this._segments.length;e++){const i={bars:this._segments[e].bars,barSpacing:L,wickVisible:R,bodyVisible:!0,borderVisible:T,borderColor:m,wickColor:y,barWidth:(0,l.optimalBarWidth)(L),hittest:new o.HitTestResult(o.HitTarget.MovePoint),isPriceScaleInverted:t.isInverted()};P.append(new c.PaneRendererCandles(i))}w.append(P),this.addAnchors(w),this._renderer=w}}},773271:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LineToolHeadAndShouldersPaneView:()=>f});var n=i(5531),r=i(609838),s=i(936879),o=i(986226),a=i(659011),l=i(402359),d=i(454304),h=i(69549),c=i(586982),u=i(306099),_=i(895379),p=i(799839);const g={leftShoulder:r.t(null,void 0,i(827443)),rightShoulder:r.t(null,void 0,i(514719)),head:r.t(null,void 0,i(401472))};class f extends _.LineSourcePaneView{constructor(){super(...arguments),this._trendLineRenderer=new a.TrendLineRenderer,this._triangleRendererPoints234=new l.TriangleRenderer,this._intersect1Renderer=new l.TriangleRenderer,this._intersect2Renderer=new l.TriangleRenderer,this._polyLineRenderer=new u.PolygonRenderer,this._leftShoulderLabelRenderer=new d.TextRenderer,this._headLabelRenderer=new d.TextRenderer,this._rightShoulderLabelRenderer=new d.TextRenderer,this._renderer=null}renderer(e){
return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){let t,i;super._updateImpl(e),this._renderer=null;const[r,a,l,d,u,_,f]=this._points;if(this._points.length>=5){const e=(0,n.intersectLineSegments)(l,u,r,a);if(null!==e){const i=u.subtract(l);t=l.add(i.scaled(e))}if(7===this._points.length){const e=(0,n.intersectLineSegments)(l,u,_,f);if(null!==e){const t=u.subtract(l);i=l.add(t.scaled(e))}}}if(this._points.length<2)return;const x=this._source.properties().childs(),v=new h.CompositeRenderer,w=(e,t)=>({points:[e],text:t,color:x.textcolor.value(),horzAlign:o.HorizontalAlign.Center,vertAlign:o.VerticalAlign.Middle,font:p.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:x.bold&&x.bold.value(),italic:x.italic&&x.italic.value(),fontsize:x.fontsize.value(),backgroundColor:x.color.value(),backgroundRoundRect:4}),R=(e,t,i)=>({points:[e,t,i],color:"rgba(0, 0, 0, 0)",linewidth:0,backcolor:x.backgroundColor.value(),fillBackground:x.fillBackground.value(),transparency:x.transparency.value()}),T={points:this._points,color:x.color.value(),linewidth:x.linewidth.value(),linestyle:s.LINESTYLE_SOLID,backcolor:"rgba(0, 0, 0, 0)",fillBackground:!1,filled:!1};if(this._polyLineRenderer.setData(T),v.append(this._polyLineRenderer),this._points.length>=5){let e,n,r=!1,o=!1;t?e=t:(e=l,r=!0),i?n=i:(n=u,o=!0);const a={points:[e,n],color:x.color.value(),linewidth:x.linewidth.value(),linestyle:s.LINESTYLE_DOTTED,extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal};a.extendleft=r,a.extendright=o,this._trendLineRenderer.setData(a),v.append(this._trendLineRenderer);const h=R(l,d,u);this._triangleRendererPoints234.setData(h),v.append(this._triangleRendererPoints234)}if(t){const e=R(t,a,l);this._intersect1Renderer.setData(e),v.append(this._intersect1Renderer)}if(i){const e=R(u,_,i);this._intersect2Renderer.setData(e),v.append(this._intersect2Renderer)}if(this._points.length>=2){const e=w(a,g.leftShoulder);a.y<r.y?(e.vertAlign=o.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=o.VerticalAlign.Top,e.offsetY=5),this._leftShoulderLabelRenderer.setData(e),v.append(this._leftShoulderLabelRenderer)}if(this._points.length>=4){const e=w(d,g.head);d.y<l.y?(e.vertAlign=o.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=o.VerticalAlign.Top,e.offsetY=5),this._headLabelRenderer.setData(e),v.append(this._headLabelRenderer)}if(this._points.length>=6){const e=w(_,g.rightShoulder);_.y<u.y?(e.vertAlign=o.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=o.VerticalAlign.Top,e.offsetY=5),this._rightShoulderLabelRenderer.setData(e),v.append(this._rightShoulderLabelRenderer)}this.addAnchors(v),this._renderer=v}}},318714:(e,t,i)=>{"use strict";i.r(t),i.d(t,{HighlighterPaneView:()=>s});var n=i(936879),r=i(350914);class s extends r.BrushBasePaneView{_createPolygonRendererData(){const e=this._source.properties().childs();return{points:this._points,color:e.linecolor.value(),linewidth:e.width.value(),backcolor:"rgba(0, 0, 0, 0)",fillBackground:!1,linestyle:n.LINESTYLE_SOLID,filled:!1,transparency:e.transparency.value()}}}},303262:(e,t,i)=>{
"use strict";i.r(t),i.d(t,{HorzLinePaneView:()=>p});var n=i(86441),r=i(184114),s=i(374410),o=i(454304),a=i(10666),l=i(888088),d=i(69549),h=i(863192),c=i(799839),u=i(144044),_=i(980317);class p extends _.InplaceTextLineSourcePaneView{constructor(e,t,i,n,s){super(e,t,i,n,s),this._renderer=null,this._lineRenderer=new l.HorizontalLineRenderer,this._lineRenderer.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint)),this._labelRenderer=new a.LineToolTextRenderer(void 0,new r.HitTestResult(r.HitTarget.MovePoint,(0,_.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,...this._labelRenderer.getTextInfo()})),this._labelRenderer.positionToCoordinate.bind(this._labelRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,0===this._points.length)return;const t=this._source.properties().childs(),i=new d.CompositeRenderer;let a=!0;const{mediaSize:{width:l,height:_}}=e,p=t.text.value(),g=this._isTextEditMode(),f=this._placeHolderMode();let x;if(t.showLabel?.value()&&p||f||g){const e=t.vertLabelsAlign.value(),r=t.horzLabelsAlign.value();let s=0,d=0;"left"===r?d=3:"right"===r?(d=l,s=3):d=l/2;const u=new n.Point(d,this._points[0].y);this._labelRenderer.setData({points:[u],text:this._textData(),color:this._textColor(),vertAlign:e,horzAlign:r,font:c.CHART_FONT_FAMILY,offsetX:s,offsetY:0,bold:t.bold.value(),italic:t.italic.value(),fontsize:t.fontsize.value(),forceTextAlign:!0,decorator:f?h.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()}),this._labelRenderer.setCursorType(this._textCursorType()),i.append(this._labelRenderer),this._needLabelExclusionPath(this._labelRenderer)&&(x=(0,o.getTextBoundaries)(this._labelRenderer,l,_)??void 0),a=this._labelRenderer.isOutOfScreen(l,_),a?this.closeTextEditor():this._updateInplaceText(this._labelRenderer.getTextInfo())}const v={y:this._points[0].y,color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),excludeBoundaries:x};this._lineRenderer.setData(v),this._lineRenderer.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint,{snappingPrice:this._source.points()[0].price}));const w=v.linewidth/2+1;if(a=a&&(v.y<-w||v.y>_+w),i.insert(this._lineRenderer,0),!a){if(1===this._points.length&&!this._isTextEditMode()){const e=(0,u.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(.9*l,this._points[0].y),0,s.PaneCursorType.VerticalResize,!0);i.append(this.createLineAnchor({points:[e]},0))}if(1===this._points.length){const e=new n.Point(this._model.timeScale().width()/2,this._points[0].y);this._addAlertRenderer(i,[e])}this._renderer=i}}}},462789:(e,t,i)=>{"use strict";i.r(t),i.d(t,{HorzRayPaneView:()=>f});var n,r=i(86441),s=i(69549),o=i(454304),a=i(799839),l=i(184114),d=i(895379),h=i(898646),c=i(161656),u=i(652283),_=i(731037),p=i(936879);class g extends _.BitmapCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}
hitTest(e){if(null===this._data||0===this._data.points.length)return null;if(e.x<this._data.points[0].x)return null;const t=(0,c.interactionTolerance)().line;return Math.abs(e.y-this._data.points[0].y)<=t?new l.HitTestResult(this._data.hitTestResult,{snappingPrice:this._data.snappingPrice}):null}_drawImpl(e){if(null===this._data||0===this._data.points.length)return;const{context:t,horizontalPixelRatio:i,verticalPixelRatio:n,bitmapSize:r}=e,s=r.width,o=this._data.points[0].y,a=Math.max(0,this._data.points[0].x),l=Math.max(s,this._data.points[0].x);t.lineCap=void 0===this._data.linestyle||this._data.linestyle===p.LINESTYLE_SOLID?"round":"butt",t.strokeStyle=this._data.color,t.lineWidth=Math.max(1,Math.floor(this._data.linewidth*i)),void 0!==this._data.linestyle&&(0,h.setLineStyle)(t,this._data.linestyle);const d=this._data.excludeBoundaries;void 0!==d&&(0,u.addExclusionAreaByScope)(e,d),(0,h.drawHorizontalLine)(t,Math.round(o*n),Math.round(a*i),Math.round(l*i))}}!function(e){e[e.RightOffset=3]="RightOffset"}(n||(n={}));class f extends d.LineSourcePaneView{constructor(e,t){super(e,t),this._horzRayRenderer=new g,this._labelRenderer=new o.TextRenderer,this._renderer=null,this._horzRayRenderer=new g,this._labelRenderer=new o.TextRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,0===this._points.length)return;const t=this._source.properties().childs(),i=new s.CompositeRenderer;let n,d=this._points[0].clone();if(t.showLabel.value()&&1===this._points.length){const s=t.vertLabelsAlign.value(),l=t.horzLabelsAlign.value(),h=0;let c=0;const u=t.text.value(),_=t.bold.value(),p=t.italic.value(),g=a.CHART_FONT_FAMILY,f=t.fontsize.value();if("right"===l){const e=this._labelRenderer.measure().width,t=this._model.timeScale().width();d.x+e+3>=t?d=d.add((0,r.point)(e+3,0)):(d=(0,r.point)(t,d.y),c=3)}else"center"===l&&(d=(0,r.point)((d.x+this._model.timeScale().width())/2,d.y));const x={points:[d],text:u,color:t.textcolor.value(),vertAlign:s,horzAlign:l,font:g,offsetX:c,offsetY:h,bold:_,italic:p,fontsize:f,forceTextAlign:!0};if(this._labelRenderer.setData(x),i.append(this._labelRenderer),this._needLabelExclusionPath(this._labelRenderer)){const{mediaSize:{width:t,height:i}}=e;n=(0,o.getTextBoundaries)(this._labelRenderer,t,i)??void 0}}const h={points:this._points,color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),hitTestResult:l.HitTarget.MovePoint,snappingPrice:this._source.points()[0].price,excludeBoundaries:n};this._horzRayRenderer.setData(h),i.append(this._horzRayRenderer),this.addAnchors(i),1===this._points.length&&this._addAlertRenderer(i,[h.points[0]]),this._renderer=i}}},272099:(e,t,i)=>{"use strict";i.r(t),i.d(t,{IconPaneView:()=>r});var n=i(799136);class r extends n.SvgIconPaneView{_iconColor(){return this._source.properties().childs().color.value()}}},61440:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ImagePaneView:()=>_})
;var n=i(86441),r=i(374410),s=i(69549),o=i(934026),a=i(184114),l=i(767313);class d extends l.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=e??null}hitTest(e){if(!this._data)return null;const{point:t,cssWidth:i,cssHeight:r}=this._data,s=new n.Point(t.x,t.y),l=new n.Point(t.x+i,t.y+r);return(0,o.pointInBox)(e,(0,n.box)(s,l))?new a.HitTestResult(a.HitTarget.MovePoint):null}setData(e){this._data=e}_drawImpl(e){if(!this._data)return;const{angle:t,img:i,point:n,cssWidth:r,cssHeight:s,transparency:o}=this._data,a=e.context;a.globalAlpha=(100-o)/100,Math.abs(t)<1e-4?a.drawImage(i,n.x,n.y,r,s):(a.translate(n.x,n.y),a.rotate(t),a.drawImage(i,0,0,r,s))}}var h=i(895379),c=i(144044),u=i(923317);class _ extends h.LineSourcePaneView{constructor(e,t){super(e,t),this._imageRenderer=new d}renderer(e){const t=this._source.image();if(null===t||(0,u.imageIsOversized)(t))return null;if(this._invalidated&&this._updateImpl(e),!this._points.length)return null;const i=new s.CompositeRenderer;return i.append(this._imageRenderer),this._addAnchors(i),i}_updateImpl(e){super._updateImpl(e);const t=this._source.properties(),i=this._source.image(),n=this._calculateBox();null===i||void 0===n?this._imageRenderer.setData(null):this._imageRenderer.setData({point:n[0],img:i,cssWidth:this._source.cssWidth(),cssHeight:this._source.cssHeight(),angle:0,transparency:t.childs().transparency.value()})}_addAnchors(e){const t=this._calculateBox();if(void 0===t)return;const[i,s]=t,o=[(0,c.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(i.x+1,i.y+1),0,r.PaneCursorType.DiagonalNwSeResize),(0,c.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(s.x-1,i.y+1),1,r.PaneCursorType.DiagonalNeSwResize),(0,c.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(i.x+1,s.y-1),2,r.PaneCursorType.DiagonalNeSwResize),(0,c.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(s.x-1,s.y-1),3,r.PaneCursorType.DiagonalNwSeResize)];e.append(this.createLineAnchor({points:o},0))}_calculateBox(){const e=this._source.cssWidth(),t=this._source.cssHeight(),i=this._source.originPoint(),r=this._source.dOffsetX();let s,o,[a]=this._points;if(a){switch(4!==i&&(a=new n.Point(a.x+r,a.y)),i){case 4:{const i=e/2,r=t/2;s=new n.Point(a.x-i,a.y-r),o=new n.Point(a.x+i,a.y+r);break}case 0:s=new n.Point(a.x,a.y),o=new n.Point(a.x+e,a.y+t);break;case 1:s=new n.Point(a.x-e,a.y),o=new n.Point(a.x,a.y+t);break;case 2:s=new n.Point(a.x,a.y-t),o=new n.Point(a.x+e,a.y);break;case 3:s=new n.Point(a.x-e,a.y-t),o=new n.Point(a.x,a.y)}return[s,o]}}}},600733:(e,t,i)=>{"use strict";i.r(t),i.d(t,{InplaceTextCursorPaneView:()=>d});var n=i(650151);class r{constructor(e){this._data=null,this._positionToCoordinate=e}setData(e){this._data=e}draw(e,t){const{position:i,rotationPoint:r,lineHeight:s,color:o,alpha:a}=(0,n.ensureNotNull)(this._data);if(void 0===i)return;e.save();const{horizontalPixelRatio:l,verticalPixelRatio:d}=t;if(r&&0!==r.angle){const t=r.x*l,i=r.y*d;e.translate(t,i),e.rotate(r.angle),e.translate(-t,-i)}
const h=this._positionToCoordinate(i),c=Math.round(h.x*l),u=Math.round(h.y*d),_=Math.max(Math.floor(l),1),p=Math.round(s*d);e.globalAlpha=a??1,e.fillStyle=o,e.fillRect(c,u,_,p),e.restore()}hitTest(e){return null}}var s=i(176616);const o=1e3;function a(e){return{isActive:t=>t-e<o,data:t=>{const i=Math.min(t-e,o);return{alpha:s.easingFunc.linear(i/o)<.5?0:1}}}}var l=i(714489);class d{constructor(e,t){this._renderer=null,this._animation=null,this._animationTimeout=null,this._invalidated=!0,this._model=t,this._cursorRenderer=new r((e=>this._positionToCoordinate(e)))}update(){this._invalidated=!0}renderer(){return this._continueAnimationIfNeeded(),this._invalidated&&this._updateImpl(),this._renderer}setCursorPosition(e){this._cursorPosition!==e&&(this._cursorPosition=e,null!==this._animation&&this._stopAnimation(),this._startAnimation()),this._invalidated=!0}setAdditionalCursorData(e,t){this._textAdditionalData=e,this._positionToCoordinate=t}_updateImpl(){if(this._renderer=null,this._invalidated=!1,void 0===this._cursorPosition)return void this._stopAnimation();const e={...this._textAdditionalData(),position:this._cursorPosition},t=this._animationStepData();null!==t&&(e.alpha=t.alpha),this._cursorRenderer.setData(e),this._renderer=this._cursorRenderer}_startAnimation(){this._animationTimeout=setTimeout((()=>{this._animation=a(performance.now()),this._continueAnimationIfNeeded()}),300)}_stopAnimation(){null!==this._animationTimeout&&(clearTimeout(this._animationTimeout),this._animationTimeout=null),this._animation=null}_animationStepData(){if(null===this._animation)return null;const e=performance.now();return this._animation.isActive(e)||(this._animation=a(e)),this._animation.data(e)}_continueAnimationIfNeeded(){null!==this._animation&&this._model.invalidate(l.InvalidationMask.cursor())}}},150801:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LineToolBeingCreatedPaneView:()=>c});var n=i(926048),r=i(895379),s=i(936879),o=i(586982),a=i(69549),l=i(457563),d=i(659011);const h=n.colorsPalette["color-cold-gray-500"];class c extends r.LineSourcePaneView{constructor(){super(...arguments),this._lineRenderer1=new l.VerticalLineRenderer,this._lineRenderer2=new l.VerticalLineRenderer,this._medianRenderer=new d.TrendLineRenderer,this._renderer=null}renderer(e){return this._invalidated&&(this._updateImpl(e),this._invalidated=!1),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._getPoints();if(t.length<1)return;this._renderer=new a.CompositeRenderer;const[i,n]=t;this._lineRenderer1.setData({x:i.x,color:h,linewidth:1,linestyle:s.LINESTYLE_SOLID}),this._renderer.append(this._lineRenderer1),t.length>1&&(this._lineRenderer2.setData({x:n.x,color:h,linewidth:1,linestyle:s.LINESTYLE_SOLID}),this._medianRenderer.setData({points:[i,n],color:h,linewidth:1,linestyle:s.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal}),this._renderer.append(this._lineRenderer2),this._renderer.append(this._medianRenderer))}}},989600:(e,t,i)=>{"use strict";i.d(t,{
LineToolWithRelativePriceCoordinate:()=>f,getNoDataPosition:()=>p,getSeriesPosition:()=>g,positionToCoordinate:()=>c});var n,r,s=i(650151),o=i(742769),a=i(174906);function l(e,t,i){const n=e.barFunction();switch(e.style()){case 3:case 10:case 2:case 14:case 15:return n(t.value);default:return t.value[-1===i?3:2]}}function d(e){return e>=0?1:-1}function h(e,t){return-1===d(e)!==t?-1:1}function c(e,t,i,n){const r=Math.min(t,Math.max(0,1===n?i:t-i));return i-n*Math.abs(r*e/100)}function u(e,t,i){const n=e.index();return null===n?null:{index:n,price:t.coordinateToPrice(t.height()/2,i)}}function _(e,t){const i=e.data().bars(),n=i.first(),r=i.last();if(null===n||null===r)return null;let s;const a=t.index();if(null===a){if(void 0===t.time)return null;const e=t.time(),a=n.value[0],l=r.value[0];if(e<a-86400||e>l)return null;s=i.searchByTime(e,o.PlotRowSearchMode.NearestRight,4)}else s=i.search(a);return null===s?null:{index:s.index,price:l(e,s,d(t.position()))}}function p(e,t,i,n){const r=u(e,t,n);if(null===r)return null;const s=h(e.position(),t.isInverted());return{index:r.index,price:r.price,poleStartY:t.height(),visualDirection:1,positionPointDirection:s,startsOnSeriesData:!1,priceCoordinate:t.priceToCoordinate(r.price,n),indexCoordinate:i.indexToCoordinate(r.index)}}function g(e,t,i=!0){const n=_(e,t),r=e.priceScale(),o=e.model().timeScale(),a=(0,s.ensureNotNull)(e.firstValue());if(null===n)return p(t,r,o,a);let l=0;const d=e.properties().childs(),c=r.priceToCoordinate(n.price,a),u=h(t.position(),r.isInverted());if(i)switch(e.style()){case 16:l=1===u?d.hlcAreaStyle.childs().highLineWidth.value()/2:d.hlcAreaStyle.childs().lowLineWidth.value()/2;break;case 3:l=d.areaStyle.childs().linewidth.value()/2;break;case 2:l=d.lineStyle.childs().linewidth.value()/2;break;case 14:l=d.lineWithMarkersStyle.childs().linewidth.value()/2;break;case 15:l=d.steplineStyle.childs().linewidth.value()/2;break;case 10:const e=Math.abs(100-d.baselineStyle.childs().baseLevelPercentage.value());l=r.height()*e/100>c?d.baselineStyle.childs().topLineWidth.value()/2:d.baselineStyle.childs().bottomLineWidth.value()/2;break;case 1:case 9:case 8:case 12:case 4:case 7:l=3;break;case 0:l=d.barStyle.childs().thinBars.value()?3:Math.max(3,.25*o.barSpacing());break;case 11:l=d.rangeStyle.childs().thinBars.value()?3:Math.max(3,.25*o.barSpacing());break;case 5:l=Math.max(4,.25*o.barSpacing());break;case 6:l=Math.max(5,.25*o.barSpacing())}const g=c-u*l;return{index:n.index,price:n.price,poleStartY:g,visualDirection:u,positionPointDirection:u,startsOnSeriesData:!0,priceCoordinate:c,indexCoordinate:o.indexToCoordinate(n.index)}}!function(e){e[e.Label=0]="Label",e[e.Body=1]="Body"}(n||(n={})),function(e){e[e.OneDaySeconds=86400]="OneDaySeconds"}(r||(r={}));class f extends a.InplaceTextLineDataSource{constructor(){super(...arguments),this._startMovingAnchorY=NaN}priceSource(){return this.ownerSource()}editableTextProperties(){(0,s.assert)(!1,"unexpected method call")}addPoint(e,t,i){return super.addPoint(this._updatePositionAndCorrectPoint(e),t,i)}setPoint(e,t,i,n){
n?super.setPoint(e,t,i,n):(super.setPoint(e,this._updatePositionAndCorrectPoint(t,!this.isPhantom()&&!this._allowChangeAnchorHorizontally()),i),this._syncPosition())}setPointAndChangeIndex(e,t,i){super.setPoint(e,this._updatePositionAndCorrectPoint(t),i),this._syncPosition()}startMoving(e,t,i,n){n||(this._startMovingAnchorY=(0,s.ensureNotNull)(this._anchorYCoordinate())),super.startMoving(e,t,i)}move(e,t,i,n){const r=(0,s.ensureDefined)((0,s.ensureNotNull)(this.startMovingPoint()).logical),o=(0,s.ensureDefined)(e.logical);if(!n&&0===t){const e=this._points[0],t=o.index-r.index,i=(0,s.ensureNotNull)(this.priceScale()),n=(0,s.ensure)(this.ownerSource()?.firstValue()),a=i.priceToCoordinate(o.price,n)-i.priceToCoordinate(r.price,n),l=this._startMovingAnchorY+a,d=i.coordinateToPrice(l,n);this._updatePositionAndCorrectPoint({index:e.index+t,price:d})}super.move(e,t,i),n||this._syncPosition()}_allowChangeAnchorHorizontally(){return!1}_updatePositionAndCorrectPoint(e,t=!1){t&&(e.index=this._points[0].index);const i=this.priceSource();if(null===i)return e;const n=i.priceScale(),r=i.firstValue();if(null===n||n.isEmpty()||null===r)return e;const s=n.height();let o=s/2,a=e.price>=n.coordinateToPrice(o,r)?1:-1;const d=this._model.mainSeries();if(i===d){const t=d.data().search(this._baseSeriesIndexForPoint(e));if(null!==t){const i=l(d,t,-1),s=l(d,t,1);a=e.price>=i?1:-1,o=n.priceToCoordinate(1===a?s:i,r),e.price=1===a?Math.max(s,e.price):e.price}}const h=1===(-1===a!==n.isInverted()?-1:1)?o:s-o,c=n.priceToCoordinate(e.price,r),u=Math.min(s,Math.abs(c-o)),_=Math.max(0,Math.min(100,100*u/h))*a;return this.properties().childs().position.setValue(_),e}_baseSeriesIndexForPoint(e){return e.index}_syncPosition(){const e=this.linkKey().value();null!==e&&this._syncLineStyleChanges(e,{position:this.properties().childs().position.value()})}_anchorYCoordinate(){const e=this.priceSource();if(null===e)return null;const t=e.priceScale(),i=e.firstValue();if(null===t||t.isEmpty()||null===i)return null;const n=this._model.mainSeries(),r=this.customEvent();if(null===r)return null;let s=null;if(e===n&&(s=_(n,r)),null===s&&(s=u(r,t,i)),null===s)return null;const o=r.position(),a=t.priceToCoordinate(s.price,i);return c(o,t.height(),a,h(o,t.isInverted()))}}},87894:(e,t,i)=>{"use strict";function n(e,t,i){const n=t-i;if("percentage"===e.getLineLengthUnit()){const r=Math.max(e.getLineLength()/100*t,1),s=Math.round(t-Math.min(n,r));return{right:s,left:s-i}}const r=e.getLineLength();if(r<0){const e=Math.round(Math.min(n,-1*r));return{left:e,right:e+i}}{const e=Math.round(t-Math.min(n,r));return{right:e,left:e-i}}}i.d(t,{orderLineLocation:()=>n})},330085:(e,t,i)=>{"use strict";i.r(t),i.d(t,{OrderPaneView:()=>v});var n=i(86441),r=i(895379),s=i(144044),o=i(69549),a=i(609838),l=i(898646),d=i(767313),h=i(184114),c=i(669874),u=i(58274),_=i(87894);const p=a.t(null,void 0,i(567710)),g=a.t(null,void 0,i(895931));class f extends d.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=null,this._cache={},this._data=null,this._adapter=e}setData(e){this._data=e}
hitTest(e){if(null===this._data||0===this._data.points.length)return null;const t=this._cache;if(e.y<t.top||e.y>t.bottom)return null;if(this._adapter.getBlocked()&&e.x>=t.left&&e.x<t.right)return new h.HitTestResult(h.HitTarget.Custom,{});if(this._adapter.getEditable()&&e.x>=t.left&&e.x<t.bodyRight){const e=this._adapter.hasMoveCallback()?h.HitTarget.MovePoint:h.HitTarget.Regular;return 0===this._adapter.getTooltip().length?new h.HitTestResult(e):new h.HitTestResult(e,{tooltip:{text:this._adapter.getTooltip(),forceHideOnMove:this._adapter.hasMoveCallback(),rect:{x:t.left,y:t.top,w:t.bodyRight-t.left,h:t.bottom-t.top}}})}return this._adapter.getEditable()&&e.x>=t.bodyRight&&e.x<t.quantityRight?this._adapter.hasModifyCallback()?new h.HitTestResult(h.HitTarget.Custom,{clickHandler:this._adapter.callOnModify.bind(this._adapter),tapHandler:this._adapter.callOnModify.bind(this._adapter),tooltip:{text:this._adapter.getModifyTooltip()||(0,c.appendEllipsis)(p),rect:{x:t.bodyRight,y:t.top,w:t.quantityRight-t.bodyRight,h:t.bottom-t.top}}}):new h.HitTestResult(h.HitTarget.Regular):this._adapter.getCancellable()&&e.x>=t.quantityRight&&e.x<t.right?new h.HitTestResult(h.HitTarget.Custom,{clickHandler:this._adapter.callOnCancel.bind(this._adapter),tapHandler:this._adapter.callOnCancel.bind(this._adapter),tooltip:{text:this._adapter.getCancelTooltip()||g,rect:{x:t.quantityRight,y:t.top,w:t.right-t.quantityRight,h:t.bottom-t.top}}}):null}_drawImpl(e){if(null===this._data||!this._data.points||this._data.points.length<1)return;const t=e.context,i=e.mediaSize.width,n=this._bodyWidth(t),r=this._quantityWidth(t),s=n+r+this._cancelButtonWidth(),{left:o,right:a}=(0,_.orderLineLocation)(this._adapter,i,s),l=Math.round(this._data.points[0].y),d=Math.round(l-(this._height()+1)/2);this._cache.bodyRight=o+n,this._cache.quantityRight=o+n+r,this._cache.top=d,this._cache.bottom=d+this._height(),this._cache.left=o,this._cache.right=a,this._drawLines(t,o,a,l,i);let h=!1;0!==n&&(this._drawBody(t,o,d),this._adapter.hasMoveCallback()&&this._drawMovePoints(t,o,d),this._drawBodyText(t,o,d),h=!0),0!==r&&(this._drawQuantity(t,o+n,d,h),this._drawQuantityText(t,o+n,d),h=!0),0!==this._cancelButtonWidth()&&this._drawCancelButton(t,o+n+r,d,h)}_height(){return Math.max(20,1+Math.max(u.fontHeight(this._adapter.getBodyFont()),u.fontHeight(this._adapter.getQuantityFont())))}_bodyWidth(e){if(0===this._adapter.getText().length)return 0;e.save(),e.font=this._adapter.getBodyFont();const t=e.measureText(this._adapter.getText()).width;return e.restore(),Math.round(20+t)}_getQuantity(){return this._adapter.getQuantity()}_quantityWidth(e){if(0===this._getQuantity().length)return 0;e.save(),e.font=this._adapter.getQuantityFont();const t=e.measureText(this._getQuantity()).width;return e.restore(),Math.round(Math.max(this._height(),10+t))}_cancelButtonWidth(){return this._adapter.isOnCancelCallbackPresent()?this._height():0}_drawLines(e,t,i,n,r){e.save(),e.strokeStyle=this._adapter.getLineColor(),(0,l.setLineStyle)(e,this._adapter.getLineStyle()),
e.lineWidth=this._adapter.getLineWidth(),(0,l.drawLine)(e,i,n,r,n),this._adapter.getExtendLeft()&&(0,l.drawLine)(e,0,n,t,n),e.restore()}_drawMovePoints(e,t,i){e.save(),e.strokeStyle=this._adapter.getBodyBorderColor(),e.fillStyle=this._adapter.getBodyBorderColor();const n=t+4,r=n+2,s=Math.floor((this._height()-10)/2)+1;for(let t=0;t<s;++t){const s=i+5+2*t;(0,l.drawLine)(e,n,s,r,s)}e.restore()}_drawBody(e,t,i){e.strokeStyle=this._adapter.getBodyBorderColor(),e.fillStyle=this._adapter.getBodyBackgroundColor();const n=this._bodyWidth(e),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r)}_drawBodyText(e,t,i){e.textAlign="center",e.textBaseline="middle",e.font=this._adapter.getBodyFont(),e.fillStyle=this._adapter.getBodyTextColor();const n=t+this._bodyWidth(e)/2,r=i+this._height()/2;e.fillText(this._adapter.getText(),5+n-2,r)}_drawQuantity(e,t,i,n){e.save(),e.strokeStyle=this._adapter.getQuantityBorderColor(),e.fillStyle=this._adapter.getQuantityBackgroundColor();const r=this._quantityWidth(e),s=this._height();e.fillRect(t+.5,i+.5,r-1,s-1),n&&e.clip&&(e.beginPath(),e.rect(t+.5,i-.5,r+1,s+1),e.clip()),e.strokeRect(t,i,r,s),e.restore()}_drawQuantityText(e,t,i){e.save(),e.textAlign="center",e.textBaseline="middle",e.font=this._adapter.getQuantityFont(),e.fillStyle=this._adapter.getQuantityTextColor();const n=t+this._quantityWidth(e)/2,r=i+this._height()/2;e.fillText(this._getQuantity(),n,r),e.restore()}_drawCancelButton(e,t,i,r){e.strokeStyle=this._adapter.getCancelButtonBorderColor(),e.fillStyle=this._adapter.getCancelButtonBackgroundColor();const s=this._cancelButtonWidth(),o=this._height();e.fillRect(t+.5,i+.5,s-1,o-1),this._adapter.getBlocked()&&(e.fillStyle="rgba(140, 140, 140, 0.75)",e.fillRect(t+.5,i+.5,s-1,o-1)),e.save(),r&&e.clip&&(e.beginPath(),e.rect(t+.5,i-.5,s+1,o+1),e.clip()),e.strokeRect(t,i,s,o),e.restore();const a=t+s,d=i+o;e.strokeStyle=this._adapter.getCancelButtonIconColor();const h=(this._cancelButtonWidth()-8)/2,c=(this._height()-8)/2;(0,l.drawPoly)(e,[new n.Point(t+h,i+c),new n.Point(a-h,d-c)],!0),(0,l.drawPoly)(e,[new n.Point(a-h,i+c),new n.Point(t+h,d-c)],!0)}}var x=i(652374);class v extends r.LineSourcePaneView{constructor(e,t){super(e,t),this._renderer=new o.CompositeRenderer,this._selectionRenderer=new x.SelectionRenderer,this._selectionData=null,this._adapter=e.adapter(),this._orderRenderer=new f(e.adapter()),this._renderer.append(this._orderRenderer),this._renderer.append(this._selectionRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._selectionData=null,this.isSelectedSource()&&this._points.length>0){const t=this._points[0].y,i=e.mediaSize.width-4-1,r=this._adapter.hasMoveCallback()?h.HitTarget.MovePoint:h.HitTarget.Regular,o=[new n.Point(i,t)];this._selectionData={barSpacing:this._model.timeScale().barSpacing(),points:o.map(s.mapLineSourcePaneViewPointToLineAnchorPoint),bgColors:this._lineAnchorColors(o),hittestResult:r,visible:!0}}this._orderRenderer.setData({points:this._points}),
this._selectionRenderer.setData(this._selectionData)}}},877242:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ParallelChannelPaneView:()=>g});var n=i(86441),r=i(650151),s=i(589637),o=i(799839),a=i(986226),l=i(374410),d=i(69549),h=i(144044),c=i(960491),u=i(895379),_=i(454304),p=i(441468);class g extends u.LineSourcePaneView{constructor(e,t){super(e,t),this._channelRenderers=[],this._labelTextRenderer=new _.TextRenderer,this._renderer=null,this._channelRenderers=Array.from({length:p.coeffs.length-1}).map((()=>new c.ParallelChannelRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._source.priceScale();if(!t||t.isEmpty())return;if(0===this._source.points().length)return;if(this._points.length<=1)return;const i=this._source.properties(),o=i.childs(),u=this._points[0],p=this._points[1];let g=0,f=null,x=null;3===this._points.length&&(g=this._points[2].y-this._points[0].y,f=u.add((0,n.point)(0,g)),x=p.add((0,n.point)(0,g)));const v=new d.CompositeRenderer,w=Array.from({length:this._source.levelsCount()}).map(((e,t)=>(0,r.ensureDefined)(i.child(`level${t+1}`)))).filter((e=>e.childs().visible.value()));1===w.length&&w.push(w[0]),this._channelRenderers.length!==w.length-1&&(this._channelRenderers=Array.from({length:w.length-1}).map((()=>new c.ParallelChannelRenderer)));const R=[],T=(0,s.generateColor)(o.backgroundColor.value(),o.transparency.value());for(let e=0;e<w.length-1;e++){const t=w[e],i=w[e+1];let r=u,s=p,a=u,l=p;if(3===this._points.length){const e=g*t.childs().coeff.value(),o=g*i.childs().coeff.value();r=u.add((0,n.point)(0,e)),s=p.add((0,n.point)(0,e)),a=u.add((0,n.point)(0,o)),l=p.add((0,n.point)(0,o))}const d={line1:{color:t.childs().color.value(),lineStyle:t.childs().lineStyle.value(),lineWidth:t.childs().lineWidth.value(),points:[r,s]},line2:null===a||null===l?void 0:{color:i.childs().color.value(),lineStyle:i.childs().lineStyle.value(),lineWidth:i.childs().lineWidth.value(),points:[a,l]},skipTopLine:e!==w.length-2,extendLeft:o.extendLeft.value(),extendRight:o.extendRight.value(),fillBackground:o.fillBackground.value(),backColor:T,hittestOnBackground:!0};R.push(d)}let m=null;{let t=u,r=p,s=u,l=p;if(3===this._points.length&&w.length>0){const e=o.labelVertAlign.value()===a.VerticalAlign.Middle,i=g*(e?0:w[0].childs().coeff.value()),d=g*(e?1:w[w.length-1].childs().coeff.value());t=u.add((0,n.point)(0,i)),r=p.add((0,n.point)(0,i)),s=u.add((0,n.point)(0,d)),l=p.add((0,n.point)(0,d))}if(m=this._getLabelTextRenderer(t,r,s,l),m&&i.childs().labelVertAlign.value()===a.VerticalAlign.Middle){const{mediaSize:t}=e,i=(0,_.getTextBoundaries)(m,t.width,t.height)??void 0;R.forEach((e=>{e.excludeBoundaries=i}))}}R.forEach(((e,t)=>{this._channelRenderers[t].setData(e),v.append(this._channelRenderers[t])})),m&&v.append(m);const y=[];u&&y.push((0,h.lineSourcePaneViewPointToLineAnchorPoint)(u)),p&&y.push((0,h.lineSourcePaneViewPointToLineAnchorPoint)(p)),f&&x&&(y.push((0,h.lineSourcePaneViewPointToLineAnchorPoint)(f,2)),y.push((0,
h.lineSourcePaneViewPointToLineAnchorPoint)(x,3)),y.push((0,h.lineSourcePaneViewPointToLineAnchorPoint)(f.add(x).scaled(.5),4,l.PaneCursorType.VerticalResize,!0)),y.push((0,h.lineSourcePaneViewPointToLineAnchorPoint)(y[0].point.add(y[1].point).scaled(.5),5,l.PaneCursorType.VerticalResize,!0)));const P=3===this._points.length&&!f;if(this._model.lineBeingCreated()!==this._source||P||(y.pop(),y.pop()),v.append(this.createLineAnchor({points:y},0)),this._points.length>=2){const e=this._points;this._addAlertRenderer(v,[e[0],e[1]],o.level1.childs().color.value())}this._renderer=v}_getLabelTextRenderer(e,t,i,n){const r=this._source.properties().childs();if(!r.labelVisible.value()||!r.labelText.value())return null;let s,l;const d=r.labelFontSize.value()/3;let h=0;switch(r.labelVertAlign.value()){case a.VerticalAlign.Bottom:!i||!n||e.y<i.y?(s=e,l=t):(s=i,l=n);break;case a.VerticalAlign.Top:!i||!n||e.y>i.y?(s=e,l=t):(s=i,l=n);break;case a.VerticalAlign.Middle:i&&n?(s=e.add(i).scaled(.5),l=t.add(n).scaled(.5)):(s=e,l=t),h=d}const c=s.x<l.x?s:l,u=c===s?l:s;let _,p;switch(r.labelHorzAlign.value()){case a.HorizontalAlign.Left:p=c;break;case a.HorizontalAlign.Right:p=u;break;default:p=c.add(u).scaled(.5)}switch(r.labelVertAlign.value()){case a.VerticalAlign.Top:_=a.VerticalAlign.Top;break;case a.VerticalAlign.Bottom:_=a.VerticalAlign.Bottom;break;case a.VerticalAlign.Middle:_=a.VerticalAlign.Middle}return this._labelTextRenderer.setData({points:[p],color:r.labelTextColor.value(),fontSize:r.labelFontSize.value(),text:r.labelText.value(),font:o.CHART_FONT_FAMILY,bold:r.labelBold.value(),italic:r.labelItalic.value(),vertAlign:_,horzAlign:r.labelHorzAlign.value(),offsetX:0,offsetY:0,boxPaddingVert:d,boxPaddingHorz:h,forceTextAlign:!0,angle:Math.atan((c.y-u.y)/(c.x-u.x))}),this._labelTextRenderer}}},441468:(e,t,i)=>{"use strict";i.d(t,{LineToolParallelChannelPropertiesImpl:()=>S,coeffs:()=>x});var n=i(154834),r=i(916738),s=i(650151),o=i(442470),a=i(111982),l=i(936879),d=i(986226),h=i(999710),c=i(811199),u=i(792535),_=i(915780),p=i(981856),g=i(171721),f=i(702054);const x=[-.25,0,.25,.5,.75,1,1.25];const v={intervalsVisibilities:{...c.intervalsVisibilitiesDefaults},...function(){const e={};return x.forEach(((t,i)=>{e[`level${i+1}`]={visible:[1,3,5].includes(i),coeff:t,lineStyle:3===i?l.LINESTYLE_DASHED:l.LINESTYLE_SOLID,lineWidth:[1,5].includes(i)?2:1}})),e}(),extendLeft:!1,extendRight:!1,fillBackground:!0,transparency:80,labelVisible:!1,labelHorzAlign:d.HorizontalAlign.Left,labelVertAlign:d.VerticalAlign.Bottom,labelFontSize:14,labelBold:!1,labelItalic:!1,version:2};const w={...function(){const e={};for(let t=0;t<x.length;t++)e[`level${t+1}`]={color:"#2962ff"};return e}(),labelTextColor:o.colors.colorTvBlue500,backgroundColor:o.colors.colorTvBlue500Alpha20},R=new Map([[a.StdTheme.Light,w],[a.StdTheme.Dark,w]]),T="linetoolparallelchannel",m=(0,u.extractAllPropertiesKeys)((0,s.ensureDefined)(R.get(a.StdTheme.Light))),y=(0,
u.extractAllPropertiesKeys)(v),P=[...m,...y,"labelText"],L=[...m,...y,...h.commonLineToolPropertiesStateKeys,"labelText"];function b(e){const{linecolor:t,linestyle:i,linewidth:s,showMidline:o,midlinecolor:l,midlinestyle:d,midlinewidth:h,...c}=e;return{...(0,r.default)((0,n.default)(v),R.get(f.watchedTheme.value())??R.get(a.StdTheme.Light)),...c,level4:{visible:o,color:l,lineStyle:d,lineWidth:h,coeff:.5},level2:{color:t,lineStyle:i,lineWidth:s,visible:!0,coeff:0},level6:{color:t,lineStyle:i,lineWidth:s,visible:!0,coeff:1}}}class S extends _.LineDataSourceProperty{constructor(e){1===(e.state&&"version"in e.state?e.state.version:1)&&e.state&&(e.state=b(e.state),e.state=(0,u.extractState)(e.state,e.allStateKeys)),super(e),this.addChild("linesColors",new p.LineToolColorsProperty(x.map(((e,t)=>(0,s.ensureDefined)(this.child(`level${t+1}`)).childs().color)))),this.addChild("linesWidths",new p.LineToolWidthsProperty(x.map(((e,t)=>(0,s.ensureDefined)(this.child(`level${t+1}`)).childs().lineWidth))))}applyTemplate(e){1===("version"in e?e.version:1)&&(e=b(e),e=(0,u.extractState)(e,this._templateKeys)),super.applyTemplate(e)}static create(e,t){return new this({defaultName:T,factoryDefaultsSupplier:()=>(0,g.factoryDefaultsForCurrentTheme)(v,R),nonThemedDefaultsKeys:y,themedDefaultsKeys:m,templateKeys:P,allStateKeys:L,state:t,theme:e})}_userSettings(){let e=(0,u.createDefaultsState)(!0,T,[],null);return 1===(e&&"version"in e?e.version:1)&&(e=b(e)),(0,u.extractState)((0,n.default)(e),this._allDefaultsKeys,this._excludedDefaultsKeys)}}},918128:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PathPaneView:()=>o});var n=i(306099),r=i(69549),s=i(895379);class o extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._polygonRenderer=new n.PolygonRenderer,this._renderer=new r.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.properties().childs(),i={points:this._points,color:t.lineColor.value(),linewidth:t.lineWidth.value(),linestyle:t.lineStyle.value(),leftend:t.leftEnd.value(),rightend:t.rightEnd.value(),filled:!1,backcolor:"",fillBackground:!1,transparency:0};this._polygonRenderer.setData(i),this._renderer.append(this._polygonRenderer),this.addAnchors(this._renderer)}}},231286:(e,t,i)=>{"use strict";i.r(t),i.d(t,{InsidePitchforkLinePaneView:()=>g,PitchforkLinePaneView:()=>u,SchiffPitchfork2LinePaneView:()=>p,SchiffPitchforkLinePaneView:()=>_});var n=i(650151),r=i(86441),s=i(184114),o=i(625790),a=i(69549),l=i(144044),d=i(586982),h=i(659011),c=i(895379);class u extends c.LineSourcePaneView{constructor(){super(...arguments),this._medianRenderer=new h.TrendLineRenderer,this._sideRenderer=new h.TrendLineRenderer,this._renderer=null,this._medianPoint=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._points.length;if(0===t)return;const[i,n,r]=this._points;this._medianPoint=(0,
l.lineSourcePaneViewPointToLineAnchorPoint)(3===t?n.add(r).scaled(.5):2===t?n:i,3),this._updateRenderer()}_updateRenderer(){if(this._points.length<2)return;if(!this._medianPoint)return;const e=this._source.properties(),t=e.childs().median.childs(),i=new a.CompositeRenderer,n={points:[this._points[0],this._medianPoint.point],color:t.color.value(),linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};if(this._medianRenderer.setData(n),i.append(this._medianRenderer),this._points.length<3)return this.addAnchors(i),void(this._renderer=i);const r={points:[this._points[1],this._points[2]],color:t.color.value(),linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};this._sideRenderer.setData(r),i.append(this._sideRenderer);const l=this._points[2].subtract(this._points[1]).scaled(.5),c=this._medianPoint.point.subtract(this._points[0]);let u=0;const _=e.childs().fillBackground.value(),p=e.childs().transparency.value();for(let t=0;t<=8;t++){const n="level"+t,r=e.childs()[n];if(r.childs().visible.value()){const n=this._medianPoint.point.addScaled(l,r.childs().coeff.value()),a=n.add(c),g=this._medianPoint.point.addScaled(l,-r.childs().coeff.value()),f=g.add(c);if(_){{const t=this._medianPoint.point.addScaled(l,u),s={p1:n,p2:a,p3:t,p4:t.add(c),color:r.childs().color.value(),transparency:p,hittestOnBackground:!0,extendLeft:e.childs().extendLines.value()},d=new o.ChannelRenderer;d.setData(s),i.append(d)}{const t=this._medianPoint.point.addScaled(l,-u),n={p1:g,p2:f,p3:t,p4:t.add(c),color:r.childs().color.value(),transparency:p,hittestOnBackground:!0,extendLeft:e.childs().extendLines.value()},s=new o.ChannelRenderer;s.setData(n),i.append(s)}}u=r.childs().coeff.value();const x={points:[n,a],color:r.childs().color.value(),linewidth:r.childs().linewidth.value(),linestyle:r.childs().linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},v=new h.TrendLineRenderer;v.setData(x),v.setHitTest(new s.HitTestResult(s.HitTarget.MovePoint,void 0,t)),i.append(v);const w={points:[g,f],color:r.childs().color.value(),linewidth:r.childs().linewidth.value(),linestyle:r.childs().linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},R=new h.TrendLineRenderer;R.setData(w),R.setHitTest(new s.HitTestResult(s.HitTarget.MovePoint,void 0,t)),i.append(R)}}this.addAnchors(i),this._renderer=i}}class _ extends u{constructor(){super(...arguments),this._modifiedBase=null,this._backSideRenderer=new h.TrendLineRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateRenderer(){if(this._points.length<2)return;this._calcMofifiedBase();const e=this._source.properties(),t=new a.CompositeRenderer,i=e.childs().median.childs();{const e={points:[this._points[0],this._points[1]],color:i.color.value(),
linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};if(this._backSideRenderer.setData(e),t.append(this._backSideRenderer),!this._medianPoint||!this._modifiedBase)return this.addAnchors(t),void(this._renderer=t)}{const n={points:[this._modifiedBase,this._medianPoint.point],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};if(this._medianRenderer.setData(n),t.append(this._medianRenderer),this._points.length<3)return this.addAnchors(t),void(this._renderer=t)}{const e={points:[this._points[1],this._points[2]],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};this._sideRenderer.setData(e),t.append(this._sideRenderer)}{const i=this._points[2].subtract(this._points[1]).scaled(.5),r=this._medianPoint.point.subtract(this._modifiedBase);let a=0;const l=e.childs().fillBackground.value(),c=e.childs().transparency.value();for(let u=0;u<=8;u++){const _="level"+u,p=(0,n.ensureDefined)(e.child(_));if(p.childs().visible.value()){const n=this._medianPoint.point.addScaled(i,p.childs().coeff.value()),_=n.add(r),g=this._medianPoint.point.addScaled(i,-p.childs().coeff.value()),f=g.add(r);if(l){const s=this._medianPoint.point.addScaled(i,a);{const i={p1:n,p2:_,p3:s,p4:s.add(r),color:p.childs().color.value(),transparency:c,hittestOnBackground:!0,extendLeft:e.childs().extendLines.value()},a=new o.ChannelRenderer;a.setData(i),t.append(a)}{const n=this._medianPoint.point.addScaled(i,-a),s={p1:g,p2:f,p3:n,p4:n.add(r),color:p.childs().color.value(),transparency:c,hittestOnBackground:!0,extendLeft:e.childs().extendLines.value()},l=new o.ChannelRenderer;l.setData(s),t.append(l)}}a=p.childs().coeff.value();const x={points:[n,_],color:p.childs().color.value(),linewidth:p.childs().linewidth.value(),linestyle:p.childs().linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},v=new h.TrendLineRenderer;v.setData(x),v.setHitTest(new s.HitTestResult(s.HitTarget.MovePoint,void 0,u)),t.append(v);const w={points:[g,f],color:p.childs().color.value(),linewidth:p.childs().linewidth.value(),linestyle:p.childs().linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},R=new h.TrendLineRenderer;R.setData(w),R.setHitTest(new s.HitTestResult(s.HitTarget.MovePoint,void 0,u)),t.append(R)}}}this.addAnchors(t),this._renderer=t}_calcMofifiedBase(){this._points.length>1&&(this._modifiedBase=this._points[0].add(this._points[1]).scaled(.5))}}class p extends _{_calcMofifiedBase(){if(this._points.length>2){const e=this._points[0].x,t=.5*(this._points[0].y+this._points[1].y),i=new r.Point(e,t);this._modifiedBase=i}}}class g extends u{constructor(){super(...arguments),
this._backSideRenderer=new h.TrendLineRenderer,this._centerRenderer=new h.TrendLineRenderer,this._modifiedBase=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateRenderer(){if(this._points.length>1&&(this._modifiedBase=this._points[0].add(this._points[1]).scaled(.5)),this._points.length<2)return;const e=new a.CompositeRenderer;if(!this._medianPoint||!this._modifiedBase)return void this.addAnchors(e);const t=this._source.properties(),i=t.childs().median.childs();if(3===this._points.length){const t={points:[this._modifiedBase,this._points[2]],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};this._medianRenderer.setData(t),e.append(this._medianRenderer)}{const t={points:[this._points[0],this._points[1]],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};if(this._backSideRenderer.setData(t),e.append(this._backSideRenderer),this._points.length<3)return this.addAnchors(e),void(this._renderer=e)}{const t={points:[this._points[1],this._points[2]],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};this._sideRenderer.setData(t),e.append(this._sideRenderer)}{const r=this._points[2].subtract(this._points[1]).scaled(.5),a=this._points[2].subtract(this._modifiedBase);let l=0;const c=t.childs().fillBackground.value(),u=t.childs().transparency.value(),_={points:[this._medianPoint.point,this._medianPoint.point.add(a)],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:t.childs().extendLines.value(),extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};this._centerRenderer.setData(_),e.append(this._centerRenderer);for(let i=0;i<=8;i++){const _="level"+i,p=(0,n.ensureDefined)(t.child(_)).childs();if(p.visible.value()){const n=this._medianPoint.point.addScaled(r,p.coeff.value()),_=n.add(a),g=this._medianPoint.point.addScaled(r,-p.coeff.value()),f=g.add(a);if(c){{const i=this._medianPoint.point.addScaled(r,l),s={p1:n,p2:_,p3:i,p4:i.add(a),color:p.color.value(),transparency:u,hittestOnBackground:!0,extendLeft:t.childs().extendLines.value()},d=new o.ChannelRenderer;d.setData(s),e.append(d)}{const i=this._medianPoint.point.addScaled(r,-l),n={p1:g,p2:f,p3:i,p4:i.add(a),color:p.color.value(),transparency:u,hittestOnBackground:!0,extendLeft:t.childs().extendLines.value()},s=new o.ChannelRenderer;s.setData(n),e.append(s)}}l=p.coeff.value();const x={points:[n,_],color:p.color.value(),linewidth:p.linewidth.value(),linestyle:p.linestyle.value(),extendleft:t.childs().extendLines.value(),extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},v=new h.TrendLineRenderer;v.setData(x),v.setHitTest(new s.HitTestResult(s.HitTarget.MovePoint,void 0,i)),e.append(v);const w={points:[g,f],color:p.color.value(),
linewidth:p.linewidth.value(),linestyle:p.linestyle.value(),extendleft:t.childs().extendLines.value(),extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},R=new h.TrendLineRenderer;R.setData(w),R.setHitTest(new s.HitTestResult(s.HitTarget.MovePoint,void 0,i)),e.append(R)}}}this.addAnchors(e),this._renderer=e}}},375869:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PolylinePaneView:()=>o});var n=i(306099),r=i(69549),s=i(895379);class o extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._polygonRenderer=new n.PolygonRenderer,this._renderer=new r.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.properties().childs(),i={points:this._points,color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),filled:t.filled.value(),backcolor:t.backgroundColor.value(),fillBackground:t.fillBackground.value(),transparency:t.transparency.value()};this._polygonRenderer.setData(i),this._renderer.append(this._polygonRenderer),this.addAnchors(this._renderer)}}},735468:(e,t,i)=>{"use strict";var n=i(895379).LineSourcePaneView,r=i(58274),s=i(184114).HitTestResult,o=i(184114).HitTarget,a=i(431520),l=i(669874).appendEllipsis,d=i(767313).MediaCoordinatesPaneRenderer,h=i(87894).orderLineLocation;const{setLineStyle:c,drawLine:u,drawPoly:_}=i(898646),{LINESTYLE_SOLID:p}=i(936879);class g extends d{constructor(e,t){super(),this._data=null,this._cache=e,this._adapter=t}setData(e){this._data=e}_height(){return Math.max(20,1+Math.max(r.fontHeight(this._adapter.getBodyFont()),r.fontHeight(this._adapter.getQuantityFont())))}_bodyWidth(e){if(0===this._adapter.getText().length)return 0;e.save(),e.font=this._adapter.getBodyFont();var t=e.measureText(this._adapter.getText()).width;return e.restore(),Math.round(10+t)}_getQuantity(){return this._adapter.getQuantity()}_quantityWidth(e){if(0===this._getQuantity().length)return 0;e.save(),e.font=this._adapter.getQuantityFont();var t=e.measureText(this._getQuantity()).width;return e.restore(),Math.round(Math.max(this._height(),10+t))}_reverseButtonWidth(){return this._adapter.isOnReverseCallbackPresent()?this._height():0}_closeButtonWidth(){return this._adapter.isOnCloseCallbackPresent()?this._height():0}_drawLines(e,t,i,n,r){e.save(),e.strokeStyle=this._adapter.getLineColor(),c(e,this._adapter.getLineStyle()),e.lineWidth=this._adapter.getLineWidth(),u(e,i,n,r,n),this._adapter.getExtendLeft()&&u(e,0,n,t,n),e.restore()}_drawBody(e,t,i){e.strokeStyle=this._adapter.getBodyBorderColor(),e.fillStyle=this._adapter.getBodyBackgroundColor();var n=this._bodyWidth(e),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r)}_drawBodyText(e,t,i){e.save(),e.textAlign="center",e.textBaseline="middle",e.font=this._adapter.getBodyFont(),e.fillStyle=this._adapter.getBodyTextColor();var n=t+this._bodyWidth(e)/2,r=i+this._height()/2;e.fillText(this._adapter.getText(),n,r),e.restore()}_drawQuantity(e,t,i){e.strokeStyle=this._adapter.getQuantityBorderColor(),
e.fillStyle=this._adapter.getQuantityBackgroundColor();var n=this._quantityWidth(e),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r)}_drawQuantityText(e,t,i){e.save(),e.textAlign="center",e.textBaseline="middle",e.font=this._adapter.getQuantityFont(),e.fillStyle=this._adapter.getQuantityTextColor();var n=t+this._quantityWidth(e)/2,r=i+this._height()/2;e.fillText(a.startWithLTR(this._getQuantity()+""),n,r),e.restore()}_drawReverseButton(e,t,i){e.save(),e.strokeStyle=this._adapter.getReverseButtonBorderColor(),e.fillStyle=this._adapter.getReverseButtonBackgroundColor();var n=this._reverseButtonWidth(),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r),e.strokeStyle=this._adapter.getReverseButtonIconColor();var s=function(e,t){c(e,p),u(e,0,0,0,t),u(e,-1,1,1,1),u(e,-2,2,2,2)},o=t+Math.round((this._reverseButtonWidth()-6)/2),a=i+5;e.save(),e.translate(o,a),s(e,10),e.translate(6,10),e.rotate(Math.PI),s(e,10),e.restore(),this._adapter._blocked&&(e.fillStyle="rgba(140, 140, 140, 0.75)",e.fillRect(t+.5,i+.5,n-1,r-1)),e.restore()}_drawCloseButton(e,t,i){e.save(),e.strokeStyle=this._adapter.getCloseButtonBorderColor(),e.fillStyle=this._adapter.getCloseButtonBackgroundColor();var n=this._closeButtonWidth(),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r);var s=t+n,o=i+r;e.strokeStyle=this._adapter.getCloseButtonIconColor();var a=(this._closeButtonWidth()-8)/2,l=(this._height()-8)/2;_(e,[{x:t+a,y:i+l},{x:s-a,y:o-l}],!0),_(e,[{x:s-a,y:i+l},{x:t+a,y:o-l}],!0),this._adapter._blocked&&(e.fillStyle="rgba(140, 140, 140, 0.75)",e.fillRect(t+.5,i+.5,n-1,r-1)),e.restore()}_drawImpl(e){if(null===this._data||!this._data.points||this._data.points.length<1)return;var t=e.context,i=this._data.width,n=this._bodyWidth(t),r=this._quantityWidth(t),s=this._reverseButtonWidth(t),o=n+r+s+this._closeButtonWidth();const{left:a,right:l}=h(this._adapter,i,o);var d=Math.round(this._data.points[0].y),c=Math.round(d-(this._height()+1)/2);this._cache.bodyRight=a+n,this._cache.quantityRight=this._cache.bodyRight+r,this._cache.reverseButtonRight=this._cache.quantityRight+s,this._cache.top=c,this._cache.bottom=c+this._height(),this._cache.left=a,this._cache.right=l,this._drawLines(t,a,l,d,i),0!==n&&(this._drawBody(t,a,c),this._drawBodyText(t,a,c)),0!==r&&(this._drawQuantity(t,this._cache.bodyRight,c),this._drawQuantityText(t,this._cache.bodyRight,c)),0!==s&&this._drawReverseButton(t,this._cache.quantityRight,c),0!==this._closeButtonWidth()&&this._drawCloseButton(t,this._cache.reverseButtonRight,c)}hitTest(e){return null===this._data||0===this._data.points.length||e.y<this._cache.top||e.y>this._cache.bottom||e.x<this._cache.left||this._cache.right<e.x?null:this._adapter._blocked?new s(o.Custom,{}):e.x>=this._cache.bodyRight&&e.x<this._cache.quantityRight&&this._adapter._onModifyCallback?new s(o.Custom,{clickHandler:this._adapter.callOnModify.bind(this._adapter),tapHandler:this._adapter.callOnModify.bind(this._adapter),tooltip:{text:this._adapter.getProtectTooltip()||l(i.i18next(null,void 0,i(324927))),
rect:{x:this._cache.bodyRight,y:this._cache.top,w:this._cache.quantityRight-this._cache.bodyRight,h:this._cache.bottom-this._cache.top}}}):e.x>=this._cache.quantityRight&&e.x<this._cache.reverseButtonRight?new s(o.Custom,{clickHandler:this._adapter.callOnReverse.bind(this._adapter),tapHandler:this._adapter.callOnReverse.bind(this._adapter),tooltip:{text:this._adapter.getReverseTooltip()||i.i18next(null,void 0,i(898818)),rect:{x:this._cache.quantityRight,y:this._cache.top,w:this._cache.reverseButtonRight-this._cache.quantityRight,h:this._cache.bottom-this._cache.top}}}):e.x>=this._cache.reverseButtonRight&&e.x<this._cache.right?new s(o.Custom,{clickHandler:this._adapter.callOnClose.bind(this._adapter),tapHandler:this._adapter.callOnClose.bind(this._adapter),tooltip:{text:this._adapter.getCloseTooltip()||i.i18next(null,void 0,i(759783)),rect:{x:this._cache.reverseButtonRight,y:this._cache.top,w:this._cache.right-this._cache.reverseButtonRight,h:this._cache.bottom-this._cache.top}}}):new s(o.Custom,{clickHandler:function(){},tapHandler:function(){},tooltip:{text:this._adapter.getTooltip(),rect:{x:this._cache.left,y:this._cache.top,w:this._cache.bodyRight-this._cache.left,h:this._cache.bottom-this._cache.top}}})}}t.PositionPaneView=class extends n{constructor(e,t){super(e,t),this._rendererCache={},this._renderer=new g(this._rendererCache,e._adapter)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer.setData({points:this._points,width:this._model.timeScale().width()}),this._renderer}}},13236:(e,t,i)=>{"use strict";var n,r;i.d(t,{AlertStatus:()=>n,Direction:()=>r}),function(e){e[e.Waiting=0]="Waiting",e[e.Success=1]="Success",e[e.Failure=2]="Failure"}(n||(n={})),function(e){e[e.Up=1]="Up",e[e.Down=2]="Down"}(r||(r={}))},696625:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PredictionPaneView:()=>I});var n,r=i(283873),s=i(650151),o=i(609838),a=i(35089),l=i(589637),d=i(431520),h=i(389952),c=i(251940),u=i(7888),_=i(684740),p=i(28964),g=i(69549),f=i(895379),x=i(86441),v=i(822960),w=i(652283),R=i(799839),T=i(885800),m=i(184114),y=i(898646),P=i(767313),L=i(463940),b=i(13236);!function(e){e[e.TargetFontSize1=14]="TargetFontSize1",e[e.TargetFontSize2=11]="TargetFontSize2",e[e.SourceFontSize1=12]="SourceFontSize1",e[e.SourceFontSize2=10]="SourceFontSize2",e[e.ArrowOffset=6]="ArrowOffset",e[e.ArrowHeight=5]="ArrowHeight",e[e.ArrowWidth=5]="ArrowWidth",e[e.Radius=3]="Radius",e[e.LabelsLeftOffset=4]="LabelsLeftOffset",e[e.LabelsTopOffset=3]="LabelsTopOffset",e[e.LabelsBoxLineWidth=2]="LabelsBoxLineWidth",e[e.LabelsBoxWidthDelta=15]="LabelsBoxWidthDelta",e[e.LabelsMinLeftOffset=20]="LabelsMinLeftOffset",e[e.LabelsCircleRadius=3]="LabelsCircleRadius",e[e.StartLabelTopOffset=2]="StartLabelTopOffset"}(n||(n={}));const S=(0,T.makeFont)(14,R.CHART_FONT_FAMILY,"normal"),A=(0,T.makeFont)(14,R.CHART_FONT_FAMILY,"bold"),C=(0,T.makeFont)(11,R.CHART_FONT_FAMILY,"normal"),M=(0,T.makeFont)(12,R.CHART_FONT_FAMILY,"normal"),k=(0,T.makeFont)(10,R.CHART_FONT_FAMILY,"normal");class B extends P.MediaCoordinatesPaneRenderer{constructor(){
super(...arguments),this._data=null,this._sourceWidth=void 0,this._sourceHeight=void 0,this._sourceRectLeftOffset=void 0,this._targetWidth=void 0,this._targetHeight=void 0,this._targetRectLeftOffset=void 0,this._target1TextWidthCache=new L.TextWidthCache,this._target1BoldTextWidthCache=new L.TextWidthCache,this._target2TextWidthCache=new L.TextWidthCache,this._source1TextWidthCache=new L.TextWidthCache,this._source2TextWidthCache=new L.TextWidthCache}setData(e){this._data=e}hitTest(e){if(null===this._data||this._data.points.length<2)return null;const t=this._data.points[0],i=this._data.points[1].subtract(t),n=e.subtract(t),r=Math.abs(i.x),s=Math.abs(i.y),o=(0,v.sign)(i.y)*(s-s*Math.sqrt(1-n.x*n.x/(r*r)));if(Math.abs(o-n.y)<3)return new m.HitTestResult(m.HitTarget.MovePoint);const a=this._targetLabelHitTest(e);return a||this._sourceLabelHitTest(e)}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=e.context;t.lineCap="butt",t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth;const i=this._data.points[0],n=this._data.points[1],r=n.subtract(i),s=Math.abs(r.x),o=Math.abs(r.y);let a,l,d;r.y<0?(a=Math.PI/2,l=r.x>0?0:Math.PI,d=1):(a=-Math.PI/2,l=r.x>0?0:-Math.PI,d=-1),t.beginPath(),t.ellipse(i.x,n.y,s,o,0,a,l,a>l),t.stroke(),this._drawTargetLabel(t,e.mediaSize),this._drawStartLabel(t,e.mediaSize);const h=Math.max(8,4*this._data.linewidth);let c;if(Math.abs(r.x)<1||Math.abs(r.y)<1)c=Math.atan(r.x/r.y);else{let e=0,t=Math.PI/2,i=(e+t)/2,n=0,a=0;if(r.length()>h){let r=0;for(;10!==r;){n=s*Math.sin(i),a=o*(1-Math.cos(i));const l=Math.sqrt((n-s)*(n-s)+(a-o)*(a-o));if(Math.abs(l-h)<1)break;l>h?e=i:t=i,i=(e+t)/2,r++}}c=Math.atan((s-n)/(o-a)),r.x*r.y<0&&(c=-c)}t.fillStyle=this._data.color,t.save(),t.beginPath(),t.translate(n.x,n.y),t.rotate(-c),t.moveTo(0,0),t.lineTo(-h/2,d*h),t.lineTo(h/2,d*h),t.lineTo(0,0),t.restore(),t.fill()}_drawBalloon(e,t,i,n,r,s=20){e.beginPath();if(r===b.Direction.Down){const r=new x.Point(t.x-s,t.y-6-5-n);return e.moveTo(r.x+3,r.y),e.lineTo(r.x+i-3,r.y),e.arcTo(r.x+i,r.y,r.x+i,r.y+3,3),e.lineTo(r.x+i,r.y+n-3),e.arcTo(r.x+i,r.y+n,r.x+i-3,r.y+n,3),e.lineTo(r.x+s+5,r.y+n),e.lineTo(r.x+s,r.y+n+5),e.lineTo(r.x+s-5,r.y+n),e.lineTo(r.x+3,r.y+n),e.arcTo(r.x,r.y+n,r.x,r.y+n-3,3),e.lineTo(r.x,r.y+3),e.arcTo(r.x,r.y,r.x+3,r.y,3),r}{const r=new x.Point(t.x-s,t.y+6+5+n);return e.moveTo(r.x+3,r.y),e.lineTo(r.x+i-3,r.y),e.arcTo(r.x+i,r.y,r.x+i,r.y-3,3),e.lineTo(r.x+i,r.y-n+3),e.arcTo(r.x+i,r.y-n,r.x+i-3,r.y-n,3),e.lineTo(r.x+s+5,r.y-n),e.lineTo(r.x+s,r.y-n-5),e.lineTo(r.x+s-5,r.y-n),e.lineTo(r.x+3,r.y-n),e.arcTo(r.x,r.y-n,r.x,r.y-n+3,3),e.lineTo(r.x,r.y-3),e.arcTo(r.x,r.y,r.x+3,r.y,3),new x.Point(r.x,r.y-n)}}_drawTargetLabel(e,t){if(null===this._data)return;e.save();const n=this._data.targetLine1,r=this._data.targetLine2,s=this._data.targetLine3,a=this._data.targetLine4;e.font=S;const l=this._target1TextWidthCache.measureText(e,n),h=this._target1TextWidthCache.measureText(e,r),c=this._target1TextWidthCache.measureText(e," ");e.font=C
;const u=this._target2TextWidthCache.measureText(e,s),_=this._target2TextWidthCache.measureText(e,a),p=this._target2TextWidthCache.measureText(e," "),g=this._data.clockWhite&&this._data.clockWhite.width||0;this._targetWidth=Math.max(l+h+c,u+_+g+2*p)+8+4,this._targetHeight=38;const f=this._data.points[1],x=f.x+this._targetWidth-t.width+5;this._targetRectLeftOffset=Math.max(20,Math.min(this._targetWidth-15,x));const v=this._data.direction===b.Direction.Up?b.Direction.Down:b.Direction.Up,R=this._drawBalloon(e,f,this._targetWidth,this._targetHeight,v,this._targetRectLeftOffset);e.fillStyle=this._data.targetBackColor,e.fill(),e.lineWidth=2,e.strokeStyle=this._data.targetStrokeColor,e.stroke(),e.beginPath(),e.arc(f.x,f.y,3,0,2*Math.PI,!1),e.fillStyle=this._data.centersColor,e.fill(),e.textBaseline="top",e.fillStyle=this._data.targetTextColor;const T=2+R.x+4,m=2+R.y+3,P=this._targetWidth-8-4;e.font=S,e.textAlign=(0,d.isRtl)()?"right":"left";const L=(0,w.calcTextHorizontalShift)(e,P-h-c);e.fillText(n,T+L,m);const M=(0,w.calcTextHorizontalShift)(e,P-l);e.fillText(r,T+l+c+M,m),e.font=C;const k=m+14+3,B=(0,w.calcTextHorizontalShift)(e,P-_-g-p);e.fillText(s,T+B,k);const I=(0,w.calcTextHorizontalShift)(e,P-u-p-g-_);this._data.clockWhite&&e.drawImage(this._data.clockWhite,T+u+p+I,k+1);const H=(0,w.calcTextHorizontalShift)(e,P-u-g);if(e.fillText(a,T+u+g+2*p+H,k),!this._data.status)return void e.restore();let D,z,V,E;if(e.font=A,this._data.status===b.AlertStatus.Success)D=o.t(null,void 0,i(890250)),z=this._data.successBackground,V=this._data.successTextColor,E=this._data.successIcon;else D=o.t(null,void 0,i(47545)),z=this._data.failureBackground,V=this._data.failureTextColor,E=this._data.failureIcon;const N=18,W=this._target1BoldTextWidthCache.measureText(e,D),F=Math.round((this._targetWidth-W)/2),O=(0,w.calcTextHorizontalShift)(e,W);e.fillStyle=z,this._data.direction===b.Direction.Up?((0,y.drawRoundRect)(e,R.x-1,R.y-N-2,this._targetWidth+2,N,5),e.fill(),e.fillStyle=V,e.fillText(D,R.x+F+O,R.y-N+1),E&&e.drawImage(E,R.x+F-E.width-4,R.y-N-2+Math.abs(N-E.height)/2)):((0,y.drawRoundRect)(e,R.x-1,R.y+this._targetHeight+2,this._targetWidth+2,N,5),e.fill(),e.fillStyle=V,e.fillText(D,R.x+F+O,R.y+this._targetHeight+5),E&&e.drawImage(E,R.x+F-E.width-4,R.y+this._targetHeight+10-Math.abs(N-E.height)/2)),e.restore()}_drawStartLabel(e,t){if(null===this._data)return;e.save();e.font=M;const i=this._source1TextWidthCache.measureText(e,this._data.sourceLine1);e.font=k;const n=this._source2TextWidthCache.measureText(e,this._data.sourceLine2);this._sourceWidth=Math.max(i,n)+6+4,this._sourceHeight=32;const r=this._data.points[0],s=r.x+this._sourceWidth-t.width+5;this._sourceRectLeftOffset=Math.max(20,Math.min(this._sourceWidth-15,s));const o=this._drawBalloon(e,r,this._sourceWidth,this._sourceHeight,this._data.direction,this._sourceRectLeftOffset);e.fillStyle=this._data.sourceBackColor,e.fill(),e.lineWidth=2,e.strokeStyle=this._data.sourceStrokeColor,e.stroke(),e.textAlign=(0,d.isRtl)()?"right":"left",e.textBaseline="top",
e.fillStyle=this._data.sourceTextColor;const a=(0,w.calcTextHorizontalShift)(e,this._sourceWidth-6-4),l=2+o.x+3+a,h=2+o.y+2;e.font=M,e.fillText(this._data.sourceLine1,l,h),e.font=k,e.fillText(this._data.sourceLine2,l,h+12+2),e.beginPath(),e.arc(r.x,r.y,3,0,2*Math.PI,!1),e.fillStyle=this._data.centersColor,e.fill(),e.restore()}_targetLabelHitTest(e){if(null===this._data||void 0===this._targetWidth||void 0===this._targetHeight||void 0===this._targetRectLeftOffset)return null;let t=this._targetHeight+5;this._data.status&&(t+=24);const i=this._data.direction===b.Direction.Up?-1:1,n=this._data.points[1],r=n.x-this._targetRectLeftOffset,s=n.y+3*i,o=n.y+i*(t+3),a=Math.min(s,o),l=Math.max(s,o);return e.x>=r&&e.x<=r+this._targetWidth&&e.y>=a&&e.y<=l?new m.HitTestResult(m.HitTarget.MovePoint):null}_sourceLabelHitTest(e){if(null===this._data||void 0===this._sourceHeight||void 0===this._sourceWidth||void 0===this._sourceRectLeftOffset)return null;const t=this._data.direction===b.Direction.Up?1:-1,i=this._data.points[0],n=i.x-this._sourceRectLeftOffset,r=i.y+3*t,s=i.y+(3+this._sourceHeight+5)*t,o=Math.min(r,s),a=Math.max(r,s);return e.x>=n&&e.x<=n+this._sourceWidth&&e.y>=o&&e.y<=a?new m.HitTestResult(m.HitTarget.MovePoint):null}}class I extends f.LineSourcePaneView{constructor(e,t){super(e,t),this._clockWhite=null,this._successIcon=null,this._failureIcon=null,this._pendingIcons=3,this._predictionRenderer=new B,this._renderer=new g.CompositeRenderer;const n=()=>{this._pendingIcons-=1,0===this._pendingIcons&&this._source.model().updateSource(this._source)};(0,a.getImage)("prediction-clock-white",i(999620)).then((e=>{this._clockWhite=e,n()})),(0,a.getImage)("prediction-success-white",i(614012)).then((e=>{this._successIcon=e,n()})),(0,a.getImage)("prediction-failure-white",i(888249)).then((e=>{this._failureIcon=e,n()}))}iconsReady(){return 0===this._pendingIcons}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.points();if(t.length<2)return;if(!this._source.priceScale())return;const n=(0,s.ensureNotNull)(this._source.ownerSource()).formatter(),a=t[1],g=t[0],f=(0,d.forceLTRStr)(n.format(a.price)),x=a.price-g.price,v=x/Math.abs(g.price)*100,w=(0,d.forceLTRStr)(`${n.format(x)} (${(0,c.getPercentageFormatter)().format(v)})`),R=this._model.timeScale();let T=R.indexToUserTime(g.index),m=R.indexToUserTime(a.index);g.time&&a.time&&(T=(0,r.default)(g.time)?new Date(Date.parse(g.time)):new Date(1e3*g.time),m=(0,r.default)(a.time)?new Date(Date.parse(a.time)):new Date(1e3*a.time));const y=this._model.mainSeries().isDWM(),P=p.Interval.parse(this._model.mainSeries().interval()),L=P.isSeconds()||P.isTicks();let b="",S="";if(m&&T){const e=y?"":`  ${new u.TimeFormatter(L?u.hourMinuteSecondFormat:u.hourMinuteFormat).format(m)}`;S=`${(new h.DateFormatter).format(m)}${e}`;const t=(m.valueOf()-T.valueOf())/1e3;b=`${o.t(null,{context:"dates"},i(771682))} ${(0,d.startWithLTR)((new _.TimeSpanFormatter).format(t))}`}const A=n.format(g.price);let C=""
;const M=R.indexToUserTime(g.index);if(M){const e=y?"":` ${new u.TimeFormatter(L?u.hourMinuteSecondFormat:u.hourMinuteFormat).format(M)}`;C=`${(new h.DateFormatter).format(M)}${e}`}const k=this._model.lineBeingCreated()!==this._source&&this._model.lineBeingEdited()!==this._source&&!this._model.sourcesBeingMoved().includes(this._source),B=this._source.properties().childs(),I=B.transparency.value(),H={points:this._points,color:B.linecolor.value(),linewidth:B.linewidth.value(),targetLine1:w,targetLine2:b,targetLine3:f,targetLine4:S,status:B.status.value(),targetBackColor:(0,l.generateColor)(B.targetBackColor.value(),I),targetStrokeColor:(0,l.generateColor)(B.targetStrokeColor.value(),I),targetTextColor:B.targetTextColor.value(),sourceBackColor:(0,l.generateColor)(B.sourceBackColor.value(),I),sourceStrokeColor:(0,l.generateColor)(B.sourceStrokeColor.value(),I),sourceTextColor:B.sourceTextColor.value(),successBackground:(0,l.generateColor)(B.successBackground.value(),I),successTextColor:B.successTextColor.value(),failureBackground:(0,l.generateColor)(B.failureBackground.value(),I),failureTextColor:B.failureTextColor.value(),intermediateBackColor:B.intermediateBackColor.value(),intermediateTextColor:B.intermediateTextColor.value(),sourceLine1:A,sourceLine2:C,direction:this._source.direction(),clockWhite:this._clockWhite,successIcon:this._successIcon,failureIcon:this._failureIcon,finished:k,centersColor:this._model.backgroundCounterColor().value()};this._predictionRenderer.setData(H),this._renderer.append(this._predictionRenderer),this.addAnchors(this._renderer)}}},883303:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PriceLabelPaneView:()=>x});var n=i(885800),r=i(799839),s=i(144044),o=i(184114),a=i(69549),l=i(652374),d=i(895379),h=i(934026),c=i(86441),u=i(652283),_=i(589637),p=i(431520),g=i(767313);class f extends g.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._measureCache=null,this._data=null,this._points=null}setData(e){this._data=e,this._points=e.points}hitTest(e){if(null===this._data||null===this._points||0===this._points.length||!this._measureCache)return null;const t=this._points[0].x-this._measureCache.tailLeft,i=this._points[0].y-(this._measureCache.innerHeight+this._measureCache.tailHeight),n=(0,c.box)(new c.Point(t,i),new c.Point(t+this._measureCache.innerWidth,i+this._measureCache.innerHeight));return(0,h.pointInBox)(e,n)?new o.HitTestResult(o.HitTarget.MovePoint):null}_drawImpl(e){if(null===this._data||null===this._points||0===this._points.length)return;const t=e.context;t.font=this._data.font;const i=t.measureText(this._data.label),n=this._data.fontSize,r=10,s=5,o=i.width+2*r,a=n+2*s,l=this._points[0].x- -9,d=this._points[0].y-(a+15),h=(0,u.calcTextHorizontalShift)(t,i.width);this._measureCache={innerWidth:o,innerHeight:a,tailLeft:-9,tailHeight:15},t.textAlign=(0,p.isRtl)()?"right":"left",t.translate(l,d),t.beginPath(),t.moveTo(12,a),t.lineTo(-9,a+15),t.lineTo(5,a),t.lineTo(3,a),t.arcTo(0,a,0,0,3),t.lineTo(0,3),t.arcTo(0,0,o,0,3),t.lineTo(o-3,0),t.arcTo(o,0,o,a,3),t.lineTo(o,a-3),
t.arcTo(o,a,0,a,3),t.lineTo(12,a),t.fillStyle=(0,_.generateColor)(this._data.backgroundColor,this._data.transparency),t.fill(),t.strokeStyle=this._data.borderColor,t.lineWidth=2,t.stroke(),t.closePath(),t.textBaseline="alphabetic",t.fillStyle=this._data.color,t.fillText(this._data.label,r+h,a/2+Math.floor(.35*this._data.fontSize)),t.beginPath(),t.arc(-9,a+15,2.5,0,2*Math.PI,!1),t.fillStyle=(0,_.generateColor)(this._data.borderColor,this._data.transparency),t.fill(),t.strokeStyle=this._data.chartBgColor,t.lineWidth=1,t.stroke(),t.closePath()}}class x extends d.LineSourcePaneView{constructor(){super(...arguments),this._renderer=new a.CompositeRenderer,this._priceLabelRenderer=new f}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.points(),i=this._source.priceScale(),a=this._source.ownerSource();if(0===t.length||!i||i.isEmpty()||!a)return;const d=t[0].price,h=a.firstValue(),c=i.formatPrice(d,h??0),u=this._source.properties().childs(),_=u.fontsize.value(),p={points:this._points,borderColor:u.borderColor.value(),backgroundColor:u.backgroundColor.value(),color:u.color.value(),font:(0,n.makeFont)(_,r.CHART_FONT_FAMILY,"",u.fontWeight.value()),fontSize:_,transparency:u.transparency.value(),chartBgColor:this._model.backgroundColor().value(),label:c};this._priceLabelRenderer.setData(p),this._renderer.append(this._priceLabelRenderer),this._renderer.append(new l.SelectionRenderer({points:p.points.map(s.mapLineSourcePaneViewPointToLineAnchorPoint),bgColors:this._lineAnchorColors(p.points),visible:this.areAnchorsVisible(),barSpacing:this._model.timeScale().barSpacing(),hittestResult:o.HitTarget.MovePoint}))}}},437948:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PriceRangePaneView:()=>x});var n=i(650151),r=i(86441),s=i(431520),o=i(986226),a=i(454304),l=i(862903),d=i(659011),h=i(69549),c=i(251940),u=i(936879),_=i(586982),p=i(799839),g=i(721579);const f=(0,c.getPercentageFormatter)();class x extends g.DateAndPriceRangeBasePaneView{constructor(){super(...arguments),this._topBorderRenderer=new d.TrendLineRenderer,this._bottomBorderRenderer=new d.TrendLineRenderer,this._distanceRenderer=new d.TrendLineRenderer,this._backgroundRenderer=new l.RectangleRenderer,this._labelRenderer=new a.TextRenderer,this._renderer=new h.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._points.length<2||this._source.points().length<2)return;const t=this._source.properties().childs(),i=t.extendLeft.value(),l=t.extendRight.value(),[d,h]=this._points,g=Math.min(d.x,h.x),x=Math.max(d.x,h.x);t.fillBackground.value()&&(this._backgroundRenderer.setData({points:[new r.Point(g,d.y),new r.Point(x,h.y)],color:"white",linewidth:0,backcolor:t.backgroundColor.value(),fillBackground:!0,transparency:t.backgroundTransparency.value(),extendLeft:i,extendRight:l}),this._renderer.append(this._backgroundRenderer));const v=(e,n,r)=>{e.setData({points:[n,r],
color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:u.LINESTYLE_SOLID,extendleft:i,extendright:l,leftend:_.LineEnd.Normal,rightend:_.LineEnd.Normal}),this._renderer.append(e)};let w=g,R=x;w===R&&(i&&(w-=1),l&&(R+=1)),v(this._topBorderRenderer,new r.Point(w,d.y),new r.Point(R,d.y)),v(this._bottomBorderRenderer,new r.Point(w,h.y),new r.Point(R,h.y));const T=Math.round((d.x+h.x)/2),m=new r.Point(T,d.y),y=new r.Point(T,h.y),{mediaSize:{width:P,height:L}}=e,b=this._updateCustomTextRenderer(L,P);this._distanceRenderer.setData({points:[m,y],color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:u.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:_.LineEnd.Normal,rightend:Math.abs(m.y-y.y)>=15*t.linewidth.value()?_.LineEnd.Arrow:_.LineEnd.Normal,excludeBoundaries:b?[b]:void 0}),this._renderer.append(this._distanceRenderer);const S=this._source.points()[0].price,A=this._source.points()[1].price,C=A-S,M=100*C/Math.abs(S),k=this._model.mainSeries().symbolInfo(),B=k&&(0,c.getPipFormatter)(k),I=(0,n.ensureNotNull)(this._source.ownerSource()).formatter(),H=I.formatChange?.(A,S)??I.format(C),D=(0,s.forceLTRStr)(H+" ("+f.format(M)+") "+(B?B.format(C):""));let z;z=A>S?new r.Point(.5*(d.x+h.x),h.y-2*t.fontsize.value()):new r.Point(.5*(d.x+h.x),h.y+.7*t.fontsize.value());const V={x:0,y:10},E=t.fontsize.value(),N={points:[z],text:D,color:t.textcolor.value(),font:p.CHART_FONT_FAMILY,offsetX:V.x,offsetY:V.y,lineSpacing:8,vertAlign:o.VerticalAlign.Middle,horzAlign:o.HorizontalAlign.Center,fontsize:E,backgroundRoundRect:4,boxPaddingHorz:.4*E+E/3,boxPaddingVert:.2*E+E/3},W=t.fillLabelBackground?.value();W&&(N.boxShadow={shadowColor:t.shadow.value(),shadowBlur:4,shadowOffsetY:1},N.backgroundColor=t.labelBackgroundColor.value()),this._labelRenderer.setData(N);const F=this._labelRenderer.measure(),O=(0,a.calculateLabelPosition)(F,d,h,V,L);this._labelRenderer.setPoints([O]),this._renderer.append(this._labelRenderer),this._renderer.append(this._customTextrenderer),this.addAnchors(this._renderer)}_needLabelExclusionPath(e){return e.getLinesInfo().lines.length>0}}},579788:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ProjectionLinePaneView:()=>d});var n=i(372082),r=i(399236),s=i(659011),o=i(69549),a=i(586982),l=i(895379);class d extends r.FibWedgePaneView{constructor(e,t){super(e,t),this._arcWedgeRenderer=new n.ArcWedgeRenderer,this._baseTrendRenderer=new s.TrendLineRenderer,this._edgeTrendRenderer=new s.TrendLineRenderer,this._arcWedgeRenderer=new n.ArcWedgeRenderer}_getPoints(){if(this._points.length<3)return this._points;const e=this._points,[t,i]=e;let n=e[2];const r=n.pointIndex,s=i.subtract(t).length(),o=n.subtract(t).normalized().scaled(s);return n=(0,l.createLineSourcePaneViewPoint)(t.add(o),r),[t,i,n]}_updateRenderer(e=NaN,t=NaN){if(this._points.length<2)return;const i=new o.CompositeRenderer,n=this._source.properties().childs(),[r,s,l]=this._getPoints(),d=n.trendline.childs().color.value(),h=n.linewidth.value(),c=n.trendline.childs().linestyle.value();if(this._baseTrendRenderer.setData({points:[r,s],color:d,linewidth:h,
linestyle:c,extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal}),i.append(this._baseTrendRenderer),this._points.length<3)return this.addAnchors(i),void(this._renderer=i);this._edgeTrendRenderer.setData({points:[r,l],color:d,linewidth:h,linestyle:c,extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal}),i.append(this._edgeTrendRenderer);const u=this._levels[0];this._arcWedgeRenderer.setData({center:this._points[0],radius:u.radius,prevRadius:0,color:d,color1:n.color1.value(),color2:n.color2.value(),linewidth:h,angle1:e,angle2:t,p1:u.p1,p2:u.p2,fillBackground:n.fillBackground.value(),transparency:n.transparency.value(),gradient:!0}),i.append(this._arcWedgeRenderer),this.addAnchors(i),this._renderer=i}}},246906:(e,t,i)=>{"use strict";i.r(t),i.d(t,{RectanglePaneView:()=>x});var n=i(86441),r=i(650151),s=i(589637),o=i(986226),a=i(799839),l=i(862903),d=i(69549),h=i(374410),c=i(184114),u=i(454304),_=i(863192),p=i(980317),g=i(10666),f=i(144044);class x extends p.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._rectangleRenderer=new l.RectangleRenderer,this._renderer=null,this._textRenderer=new g.LineToolTextRenderer(void 0,new c.HitTestResult(c.HitTarget.MovePoint,(0,p.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,rotationPoint:this._textRenderer.rotation()??void 0,...this._textRenderer.getTextInfo()})),this._textRenderer.positionToCoordinate.bind(this._textRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._textRenderer.setData(null),this._points.length<2)return;const t=this._getSource().properties().childs(),i=t.extendLeft.value(),l=t.extendRight.value(),h={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),backcolor:t.fillBackground.value()?(0,s.generateColor)(t.backgroundColor.value(),t.transparency.value()):"transparent",fillBackground:!0,extendLeft:i,extendRight:l,backgroundHitTarget:this._model.selection().isSelected(this._source)?c.HitTarget.MovePoint:void 0},p=new d.CompositeRenderer;p.append(this._rectangleRenderer);const[g,f]=this._points,{mediaSize:{width:x,height:v}}=e,w=this._placeHolderMode();let R=!1;const T=this._rectangleRenderer.visibleRectSegment(e.mediaSize);if(null!==T&&(t.showLabel?.value()&&t.text.value()||w||this._isTextEditMode())){const e=Math.min(g.x,f.x),s=Math.max(g.x,f.x),d=Math.min(g.y,f.y),h=Math.max(g.y,f.y),c=t.horzLabelsAlign.value();let m,y;const P=t.fontSize.value()/3;let L=0;const[b]=(0,u.getTextAlignInBox)({horzAlign:c,extendLeft:i,extendRight:l,width:x,leftPoint:(0,n.point)(e,d),rightPoint:(0,n.point)(s,h)});switch(t.vertLabelsAlign.value()){case o.VerticalAlign.Middle:y=(d+h)/2,m=o.VerticalAlign.Middle,L=P;break;case o.VerticalAlign.Top:y=h,m=o.VerticalAlign.Top;break;case o.VerticalAlign.Bottom:y=d,m=o.VerticalAlign.Bottom}
const S=c===o.HorizontalAlign.Left&&i||c===o.HorizontalAlign.Right&&l;let A,C;m===o.VerticalAlign.Middle&&(A=(l?x:s)-(i?0:e)-2*L,C=h-d),this._textRenderer.setData({points:[new n.Point(b.x,y)],text:this._textData(),color:this._textColor(),fontSize:t.fontSize.value(),font:a.CHART_FONT_FAMILY,bold:t.bold.value(),italic:t.italic.value(),horzAlign:c,vertAlign:m,wordWrapWidth:w?void 0:A,maxHeight:w?void 0:C,offsetX:0,offsetY:0,boxPaddingVert:P,boxPaddingHorz:L,forceTextAlign:!0,forceCalculateMaxLineWidth:!0,decorator:w?_.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()});const M=this._textRenderer.measure().width;if(S){const[e,t]=T,i=t.x-e.x-M;i<0&&this._textRenderer.setData({...(0,r.ensureNotNull)(this._textRenderer.data()),offsetX:i})}let k=!1;void 0!==A&&w&&M>A&&(this._textRenderer.updateData({text:""}),k=M>A),k||(this._textRenderer.setCursorType(this._textCursorType()),this._textRenderer.isOutOfScreen(x,v)?this.closeTextEditor():this._updateInplaceText(this._textRenderer.getTextInfo()),p.append(this._textRenderer),R=!0)}if(t.middleLine.childs().showLine.value()){let e;R&&"middle"===t.vertLabelsAlign.value()&&(0,u.needTextExclusionPath)(this._textRenderer)&&(e=(0,u.getTextBoundaries)(this._textRenderer,x,v)??void 0);const{lineColor:i,lineWidth:n,lineStyle:r}=t.middleLine.state();h.middleLine={lineColor:i,lineWidth:n,lineStyle:r,excludeBoundaries:e}}this._rectangleRenderer.setData(h),this._addAnchors(g,f,p),this._renderer=p}_addAnchors(e,t,i){const r=e.x-t.x,s=e.y-t.y,o=Math.sign(r*s),a=o<0?h.PaneCursorType.DiagonalNeSwResize:h.PaneCursorType.DiagonalNwSeResize,l=o<0?h.PaneCursorType.DiagonalNwSeResize:h.PaneCursorType.DiagonalNeSwResize,d=[(0,f.lineSourcePaneViewPointToLineAnchorPoint)(e,void 0,a),(0,f.lineSourcePaneViewPointToLineAnchorPoint)(t,void 0,a),(0,f.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(e.x,t.y),2,l),(0,f.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(t.x,e.y),3,l),(0,f.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(e.x,.5*(e.y+t.y)),4,h.PaneCursorType.HorizontalResize,!0),(0,f.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(t.x,.5*(e.y+t.y)),5,h.PaneCursorType.HorizontalResize,!0),(0,f.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(.5*(e.x+t.x),e.y),6,h.PaneCursorType.VerticalResize,!0),(0,f.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(.5*(e.x+t.x),t.y),7,h.PaneCursorType.VerticalResize,!0)];i.append(this.createLineAnchor({points:d},0))}}},245603:(e,t,i)=>{"use strict";i.r(t),i.d(t,{RegressionTrendPaneView:()=>x});var n=i(650151),r=i(144044),s=i(589637),o=i(184114),a=i(69549),l=i(960491),d=i(454304),h=i(659011),c=i(652374),u=i(86441),_=i(986226),p=i(586982),g=i(799839);var f=i(895379);class x extends f.LineSourcePaneView{constructor(e,t){super(e,t),this._data=null,this._pearsonsLabelRenderer=new d.TextRenderer,this._renderer=null,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._data=function(e,t){const i={lines:[],pearsons:null}
;if(!t.properties().visible.value())return i;const r=e.timeScale(),s=t.priceScale(),o=e.mainSeries().firstBar();if(!s||s.isEmpty()||r.isEmpty()||!o)return i;const a=t.startIndex(),l=t.endIndex();if(null===a||null===l)return i;const d=[t.baseLine(),t.downLine(),t.upLine()],h=Math.round(r.indexToCoordinate(a)),c=Math.round(r.indexToCoordinate(l)),f=t.properties(),x=[f.styles.baseLine,f.styles.downLine,f.styles.upLine],v=o[4];for(let e=0;e<d.length;e++){if(1&~x[e].display.value())continue;const t=(0,n.ensureNotNull)(d[e]).startPrice,r=(0,n.ensureNotNull)(d[e]).endPrice;if(void 0===t||void 0===r)continue;const o=s.priceToCoordinate(t,v),a=s.priceToCoordinate(r,v),l={points:[new u.Point(h,o),new u.Point(c,a)],color:x[e].color.value(),linewidth:x[e].linewidth.value(),linestyle:x[e].linestyle.value(),extendleft:!1,extendright:f.styles.extendLines.value(),leftend:p.LineEnd.Normal,rightend:p.LineEnd.Normal};i.lines.push(l)}const w=(0,n.ensureNotNull)(t.downLine());if(f.styles.showPearsons.value()&&void 0!==w.startPrice){const e=s.priceToCoordinate(w.startPrice,v),n=new u.Point(h,e);i.pearsons={points:[n],text:""+t.pearsons(),color:f.styles.downLine.color.value(),vertAlign:_.VerticalAlign.Top,horzAlign:_.HorizontalAlign.Center,font:g.CHART_FONT_FAMILY,offsetX:0,offsetY:4,fontsize:12}}return i}(this._model,this._source),this._renderer=null;const t=new a.CompositeRenderer;let i=[];const d=[this._data.lines[1],this._data.lines[0],this._data.lines[2]].filter((e=>!!e)),f=this._source.properties().childs().styles.childs().transparency.value();for(let e=1;e<d.length;e++){const i=d[e].color,n=d[e].linewidth,r=d[e].linestyle,a=d[e].extendright,h={line1:{color:i,lineStyle:r,lineWidth:n,points:[d[e].points[0],d[e].points[1]]},line2:{color:i,lineStyle:r,lineWidth:n,points:[d[e-1].points[0],d[e-1].points[1]]},extendLeft:!1,extendRight:a,backColor:(0,s.generateColor)(d[e].color,f),skipLines:!0,fillBackground:!0},c=new l.ParallelChannelRenderer(new o.HitTestResult(o.HitTarget.Regular));c.setData(h),t.append(c)}const x=this._getTransparencyResetLines();for(let e=0;e<d.length;e++){const n=new h.TrendLineRenderer;n.setData(x[e]),n.setHitTest(new o.HitTestResult(o.HitTarget.Regular)),t.append(n),0!==e&&(i=i.concat(d[e].points))}this._data.pearsons&&(this._data.pearsons.color=(0,s.resetTransparency)(this._data.pearsons.color),this._pearsonsLabelRenderer.setData(this._data.pearsons),t.append(this._pearsonsLabelRenderer)),this._data.lines.length>=1&&t.append(new c.SelectionRenderer({points:i.map(r.mapLineSourcePaneViewPointToLineAnchorPoint),bgColors:this._lineAnchorColors(i),visible:this.areAnchorsVisible(),hittestResult:o.HitTarget.Regular,barSpacing:this._model.timeScale().barSpacing()})),this._renderer=t}_getTransparencyResetLines(){return(0,n.ensureNotNull)(this._data).lines.map((e=>({...e,color:(0,s.resetTransparency)(e.color)})))}}},986971:(e,t,i)=>{"use strict";i.r(t),i.d(t,{RiskRewardPaneView:()=>M})
;var n=i(86441),r=i(650151),s=i(609838),o=i(895379),a=i(986226),l=i(659011),d=i(454304),h=i(862903),c=i(184114),u=i(69549),_=i(251940),p=i(589637),g=i(586982),f=i(735725),x=i(431520),v=i(374410),w=i(44258),R=i(799839),T=i(936879),m=i(144044);const y=s.t(null,void 0,i(176250)),P=s.t(null,{context:"line_tool_position"},i(818741)),L=s.t(null,{context:"line_tool_position"},i(772630)),b=s.t(null,void 0,i(936825)),S=s.t(null,void 0,i(479463)),A=s.t(null,void 0,i(872274)),C=s.t(null,void 0,i(810780));class M extends o.LineSourcePaneView{constructor(){super(...arguments),this._entryLineRenderer=new l.TrendLineRenderer,this._stopLineRenderer=new l.TrendLineRenderer,this._targetLineRenderer=new l.TrendLineRenderer,this._positionLineRenderer=new l.TrendLineRenderer,this._fullStopBgRenderer=new h.RectangleRenderer,this._stopBgRenderer=new h.RectangleRenderer,this._fullTargetBgRenderer=new h.RectangleRenderer,this._targetBgRenderer=new h.RectangleRenderer,this._stopLabelRenderer=new d.TextRenderer,this._middleLabelRenderer=new d.TextRenderer,this._profitLabelRenderer=new d.TextRenderer,this._renderer=new u.CompositeRenderer}isLabelVisible(){return this.isHoveredSource()||this.isSelectedSource()||this._source.properties().childs().alwaysShowStats.value()}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._model.timeScale(),i=this._source.priceScale();if(!i||i.isEmpty()||t.isEmpty())return;const s=this._source.points();if(s.length<2||this._points.length<2)return;const o=(0,r.ensureNotNull)(this._source.ownerSource()),a=o?.barsProvider().bars();if(!a||a.isEmpty())return;if(null===a.last())return;const l=4===s.length,d=this._source.lastBarData();if(!d)return;const h=d.closePrice,c=this._source.stopPrice(),u=this._source.profitPrice(),p=this._source.calculatePL(h),g=o.symbolSource().symbolInfo();if(!g)return;const x=o.firstValue();if(null===x)return;const w=this._points[f.RiskRewardPointIndex.Entry].y,R=i.priceToCoordinate(c,x),T=i.priceToCoordinate(u,x),y=i.priceToCoordinate(d.closePrice,x),P=t.indexToCoordinate(d.index),L=this._points[f.RiskRewardPointIndex.Entry].x,b=this._points[f.RiskRewardPointIndex.ActualEntry]?this._points[f.RiskRewardPointIndex.ActualEntry].x:this._points[f.RiskRewardPointIndex.Close].x,S=this._points[f.RiskRewardPointIndex.ActualClose]?this._points[f.RiskRewardPointIndex.ActualClose].x:this._points[f.RiskRewardPointIndex.Close].x,A=this._points[f.RiskRewardPointIndex.Close].x,C=this._source.entryPrice(),M=this._source.stopPrice(),k=this._source.profitPrice(),B={pl:p,isClosed:l,entryLevel:w,stopLevel:R,profitLevel:T,closeLevel:y,closeBar:P,left:L,entryX:b,right:S,edge:A,entryPrice:C,stopPrice:M,profitPrice:k,currentPrice:h},{mediaSize:{width:I,height:H}}=e;let D=A<-5||L>I+5;if(this._createBackgroundRenderers(B,this._renderer),this._createLinesRenderers(B,this._renderer),this._createLabelsRenderers(B,this._renderer,g,(0,_.getPipFormatter)(g)),
D=[this._profitLabelRenderer,this._stopLabelRenderer,this._middleLabelRenderer].reduce(((e,t)=>e&&t.isOutOfScreen(I,H)),D),D)return;const[z]=this._points,V=[(0,m.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(L,z.y),0,void 0,void 0,void 0,C),(0,m.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(A,z.y),1,v.PaneCursorType.HorizontalResize,!0,void 0,void 0,d.index),(0,m.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(L,R),2,v.PaneCursorType.VerticalResize,!0,void 0,M),(0,m.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(L,T),3,v.PaneCursorType.VerticalResize,!0,void 0,k)];this._renderer.append(this.createLineAnchor({points:V},0))}_createBackgroundRenderers(e,t){const i=this._source.properties().childs();{const t={points:[new n.Point(e.left,e.entryLevel),new n.Point(e.edge,e.stopLevel)],color:"white",linewidth:0,backcolor:i.stopBackground.value(),fillBackground:!0,transparency:i.stopBackgroundTransparency.value(),extendLeft:!1,extendRight:!1,backgroundHitTarget:c.HitTarget.MovePoint};this._fullStopBgRenderer.setData(t),this._renderer.append(this._fullStopBgRenderer)}if(e.pl<0&&e.entryX!==e.right){const t=new n.Point(e.entryX,e.entryLevel),r=new n.Point(e.right,e.closeLevel),s=.01*i.stopBackgroundTransparency.value(),o=100-100*(1-s*s*s),a={points:[t,r],color:"white",linewidth:0,backcolor:i.stopBackground.value(),fillBackground:!0,transparency:o,extendLeft:!1,extendRight:!1,backgroundHitTarget:c.HitTarget.MovePoint};this._stopBgRenderer.setData(a),this._renderer.append(this._stopBgRenderer)}{const t={points:[new n.Point(e.left,e.entryLevel),new n.Point(e.edge,e.profitLevel)],color:"white",linewidth:0,backcolor:i.profitBackground.value(),fillBackground:!0,transparency:i.profitBackgroundTransparency.value(),extendLeft:!1,extendRight:!1,backgroundHitTarget:c.HitTarget.MovePoint};this._fullTargetBgRenderer.setData(t),this._renderer.append(this._fullTargetBgRenderer)}if(e.pl>0&&e.entryX!==e.right){const t=new n.Point(e.entryX,e.entryLevel),r=new n.Point(e.right,e.closeLevel),s=.01*i.profitBackgroundTransparency.value(),o=100-100*(1-s*s*s),a={points:[t,r],color:"white",linewidth:0,backcolor:i.profitBackground.value(),fillBackground:!0,transparency:o,extendLeft:!1,extendRight:!1,backgroundHitTarget:c.HitTarget.MovePoint};this._targetBgRenderer.setData(a),this._renderer.append(this._targetBgRenderer)}}_createLinesRenderers(e,t){const i=this._source.properties().childs(),r=(e,t,n,r)=>{const s={points:[t,n],color:r??i.linecolor.value(),linewidth:i.linewidth.value(),linestyle:T.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:g.LineEnd.Normal,rightend:g.LineEnd.Normal};e.setData(s),this._renderer.append(e)};if(this._points[f.RiskRewardPointIndex.ActualEntry]){const t={points:[this._points[f.RiskRewardPointIndex.ActualEntry],e.isClosed?this._points[f.RiskRewardPointIndex.ActualClose]:new n.Point(e.closeBar,e.closeLevel)],color:this._source.properties().childs().linecolor.value(),linewidth:1,linestyle:T.LINESTYLE_DASHED,extendleft:!1,extendright:!1,leftend:g.LineEnd.Normal,rightend:g.LineEnd.Arrow}
;this._positionLineRenderer.setData(t),this._renderer.append(this._positionLineRenderer)}{const t=new n.Point(e.left,this._points[f.RiskRewardPointIndex.Entry].y),i=new n.Point(e.edge,this._points[f.RiskRewardPointIndex.Entry].y);r(this._entryLineRenderer,t,i)}{const t=new n.Point(e.left,e.stopLevel),s=new n.Point(e.edge,e.stopLevel);r(this._stopLineRenderer,t,s,i.stopBackground.value())}{const t=new n.Point(e.left,e.profitLevel),s=new n.Point(e.edge,e.profitLevel);r(this._targetLineRenderer,t,s,i.profitBackground.value())}}_addCenterLabel(e,t,i){const n=this._source.properties().childs(),r={font:R.CHART_FONT_FAMILY,offsetX:3,horzAlign:"center",backgroundRoundRect:4,boxPaddingHorz:4,points:[i.p],text:i.txt,color:n.textcolor.value(),offsetY:i.offsetY,vertAlign:i.vertAlign,backgroundColor:(0,p.resetTransparency)(i.color),fontsize:n.fontsize.value(),borderColor:i.border};return t.setData(r),e.append(t),r}_creareMiddleLabel(e,t,i){const{entryPrice:s,profitPrice:o,stopPrice:l,currentPrice:d,pl:h,left:c,edge:u,isClosed:p}=e,g=Math.abs(s-o)/Math.abs(s-l),f=this._source.properties().childs(),x=(0,r.ensureNotNull)(this._source.ownerSource()),v=new n.Point((c+u)/2,Math.round(this._points[0].y));let R="",T="";const m=(0,_.getNumericFormatter)().format(Math.round(100*g)/100);if(this._points[1]){const e=x.formatter();if(e.formatChange){const t=Math.max(d,s),i=Math.min(d,s);T=h>=0?e.formatChange(t,i):e.formatChange(i,t)}else T=e.format(h)}const S=f.qty.value()/f.lotSize.value(),A="futures"===i.type||(0,w.hasCryptoTypespec)(i.typespecs||[])?Math.round(1e3*S)/1e3:Math.floor(S);if(f.compact.value())R+=T?T+" ~ ":"",R+=A+"\n",R+=m;else{const e=p?L:P;R+=T?y.format({status:e,pnl:T})+", ":"",R+=C.format({qty:""+A})+"\n",R+=b.format({ratio:m})+" "}let M=f.linecolor.value();return h<0?M=f.stopBackground.value():h>0&&(M=f.profitBackground.value()),this._addCenterLabel(t,this._middleLabelRenderer,{p:v,txt:R,color:M,vertAlign:a.VerticalAlign.Middle,offsetY:0,border:"white"})}_createStopLabel(e,t,i){const{stopPrice:s,entryPrice:o,left:l,edge:d,stopLevel:h}=e,c=this._source.properties().childs(),u=(0,r.ensureNotNull)(this._source.ownerSource()),p=Math.abs(s-o),g=Math.round(1e4*p/o)/100,f=new n.Point((l+d)/2,h);let v="";const w=u.formatter(),R=w.formatChange?.(Math.max(s,o),Math.min(s,o))??w.format(p),T=(0,_.getPercentageFormatter)(),m=T.format(g);return v=c.compact.value()?R+" ("+m+") "+c.amountStop.value():S.format({stopChange:(0,x.forceLTRStr)(R),stopChangePercent:(0,x.forceLTRStr)(T.format(g)),stopChangePip:i?(0,x.forceLTRStr)(i.format(p)):"",amount:(0,x.forceLTRStr)(""+c.amountStop.value())}),this._addCenterLabel(t,this._stopLabelRenderer,{p:f,txt:v,color:c.stopBackground.value(),vertAlign:o<s?a.VerticalAlign.Bottom:a.VerticalAlign.Top,offsetY:0})}_createTargetLabel(e,t,i){const{profitPrice:s,entryPrice:o,stopPrice:l,left:d,edge:h,profitLevel:c}=e,u=this._source.properties().childs(),p=(0,r.ensureNotNull)(this._source.ownerSource()),g=Math.abs(s-o),f=Math.round(1e4*g/o)/100,v=new n.Point((d+h)/2,c);let w=""
;const R=p.formatter(),T=R.formatChange?.(Math.max(s,o),Math.min(s,o))??R.format(g),m=(0,_.getPercentageFormatter)(),y=m.format(f);return w=u.compact.value()?T+" ("+y+") "+u.amountTarget.value():A.format({profitChange:T,profitChangePercent:(0,x.forceLTRStr)(m.format(f)),profitChangePip:i?(0,x.forceLTRStr)(i.format(g)):"",amount:(0,x.forceLTRStr)(""+u.amountTarget.value())}),this._addCenterLabel(t,this._profitLabelRenderer,{p:v,txt:w,color:u.profitBackground.value(),vertAlign:o<l?a.VerticalAlign.Top:a.VerticalAlign.Bottom,offsetY:0})}_createLabelsRenderers(e,t,i,s){if(!this.isLabelVisible())return;const o=this._creareMiddleLabel(e,t,i),a=this._createStopLabel(e,t,s),l=this._createTargetLabel(e,t,s),d=[this._profitLabelRenderer,this._stopLabelRenderer,this._middleLabelRenderer].reduce(((e,t)=>Math.max(e,t.measure().width)),0),h=e.edge-e.left,c=this._anchorRadius();if(h-d-c<=8&&(l&&(l.offsetY+=c+8,this._profitLabelRenderer.setData(l)),a&&(a.offsetY+=c+8,this._stopLabelRenderer.setData(a)),o)){let t;if(this._source.priceScale()?.isLog()){const i=Math.abs(this._points[0].y-e.stopLevel);t=Math.abs(this._points[0].y-e.profitLevel)>i?-1:1}else{const i=Math.abs(e.stopPrice-e.entryPrice);t=Math.abs(e.profitPrice-e.entryPrice)>i?-1:1}const i=e.profitLevel<e.stopLevel?1:-1,s=(0,r.ensureDefined)(o.points)[0].add(new n.Point(0,i*t*(.5*this._middleLabelRenderer.measure().height+c+8)));o.points=[s],this._middleLabelRenderer.setData(o)}}}},735725:(e,t,i)=>{"use strict";var n;i.d(t,{RiskRewardPointIndex:()=>n}),function(e){e[e.Entry=0]="Entry",e[e.Close=1]="Close",e[e.ActualEntry=2]="ActualEntry",e[e.ActualClose=3]="ActualClose"}(n||(n={}))},17802:(e,t,i)=>{"use strict";i.r(t),i.d(t,{RotatedRectanglePaneView:()=>u});var n=i(86441),r=i(204652),s=i(936879),o=i(895379),a=i(659011),l=i(306099),d=i(69549),h=i(586982),c=i(144044);class u extends o.LineSourcePaneView{constructor(){super(...arguments),this._poligonRenderer=new l.PolygonRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;let t=0;if(3===this._points.length&&(t=(0,r.distanceToLine)(this._points[0],this._points[1],this._points[2]).distance),0===this._points.length)return;const i=new d.CompositeRenderer,l=this._source.properties(),[u,_]=this._points,p=[];if(p.push((0,c.lineSourcePaneViewPointToLineAnchorPoint)(u)),_&&p.push((0,c.lineSourcePaneViewPointToLineAnchorPoint)(_)),2===this._points.length){const e={points:this._points,color:l.childs().color.value(),linewidth:1,linestyle:s.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal},t=new a.TrendLineRenderer;t.setData(e),i.append(t)}else if(3===this._points.length){const e=_.subtract(u),r=new n.Point(e.y,-e.x).normalized().scaled(t),a=r.scaled(-1),d=u.add(r),h=_.add(r),g=u.add(a),f={points:[d,h,_.add(a),g],color:l.childs().color.value(),linewidth:l.childs().linewidth.value(),linestyle:s.LINESTYLE_SOLID,filled:!0,backcolor:l.childs().backgroundColor.value(),
fillBackground:l.childs().fillBackground.value(),transparency:l.childs().transparency.value()};this._poligonRenderer.setData(f),i.append(this._poligonRenderer);const x=(0,o.thirdPointCursorType)(u,_);p.push(...f.points.map((e=>(0,c.lineSourcePaneViewPointToLineAnchorPoint)(e,2,x))))}i.append(this.createLineAnchor({points:p},0)),this._renderer=i}}},476336:(e,t,i)=>{"use strict";i.r(t),i.d(t,{Constants:()=>u,SignpostItemIndex:()=>I,SignpostPaneView:()=>H});var n=i(86441),r=i(926048),s=i(986226),o=i(650151),a=i(844287),l=i(278765);class d{constructor(){this._cache=new Map}getEmoji(e){return this._cache.has(e)||this._cache.set(e,this._fetchEmoji(e)),(0,o.ensureDefined)(this._cache.get(e))}static instance(){return this._instance||=new d}static clear(){this._instance=null}async _fetchEmoji(e){try{return(await(0,a.fetch)((0,l.getTwemojiUrl)(e,"svg"))).text()}catch(t){throw this._cache.has(e)&&this._cache.delete(e),t}}}d._instance=null;var h,c,u,_=i(184114),p=i(374410),g=i(69549),f=i(313835),x=i(989600),v=i(980317),w=i(10666),R=i(144044),T=i(799839),m=i(204652),y=i(934026),P=i(161656),L=i(419395);function b(e){return void 0===e?0:80}function S(e){return e.poleStartY}function A(e){const{inverseAnchorPosition:t,anchorCoordinates:i}=e;if(t)return i.y;const{labelHeight:n,plate:r,direction:s}=e;return i.y+(n+b(r))*s}function C(e){const{inverseAnchorPosition:t,anchorCoordinates:i,direction:n}=e;return t?i.y+b(e.plate)*n:i.y+e.labelHeight*n}!function(e){e[e.HitTestTolerance=3]="HitTestTolerance",e[e.HitTestToleranceTouch=20]="HitTestToleranceTouch",e[e.ShadowYOffset=1]="ShadowYOffset",e[e.ShadowBlur=4]="ShadowBlur"}(h||(h={})),function(e){e[e.CircleRadius=35]="CircleRadius",e[e.CircleBorderWidth=1]="CircleBorderWidth",e[e.LabelOffset=10]="LabelOffset",e[e.LabelVertPadding=6]="LabelVertPadding"}(c||(c={}));class M{constructor(e){this._data=null,this._phantomMode=Boolean(e)}setData(e){this._data=e}hitTest(e){if(null===this._data)return null;const[t,i]=function(e,t){const i=(0,L.lastMouseOrTouchEventInfo)().isTouch?20:3,{anchorCoordinates:r,plate:s}=e,o=(0,m.distanceToSegment)(new n.Point(r.x,S(e)),new n.Point(r.x,A(e)),t).distance<i;let a=!1;if(!o&&void 0!==s){const s=e.inverseAnchorPosition?e.anchorCoordinates.y+35*e.direction:e.anchorCoordinates.y+(e.labelHeight+10+35)*e.direction;a=(0,y.pointInCircle)(t,new n.Point(r.x,s),35+i)}return[o,a]}(this._data,e);if(t||i){const e={hideCrosshairLinesOnHover:!0,activeItem:this._data.itemIndex};return i&&(e.areaName=_.AreaName.Style),new _.HitTestResult(_.HitTarget.MovePoint,e)}return null}draw(e,t){if(null===this._data)return;e.save(),this._phantomMode&&(e.globalAlpha=.5);const{poleColor:i,emojiRadius:n,plate:r}=this._data,{horizontalPixelRatio:s,verticalPixelRatio:o}=t,a=Math.max(1,Math.floor(s)),l=a%2?.5:0;e.beginPath(),e.strokeStyle=i,e.lineWidth=a;const d=Math.round(this._data.anchorCoordinates.x*s)+l;e.moveTo(d,Math.round(S(this._data)*o)),e.lineTo(d,Math.round(A(this._data)*o)),void 0!==r&&(e.moveTo(d,Math.round(C(this._data)*o)),e.lineTo(d,Math.round(function(e){const t=e.plate?10:0
;return e.inverseAnchorPosition?C(e)-t*e.direction:C(e)+t*e.direction}(this._data)*o))),e.stroke(),void 0!==r&&function(e,t,i,n,r){const{horizontalPixelRatio:s,verticalPixelRatio:o}=r,{circleBorderColor:a,circleBackgroundColor:l}=i;e.strokeStyle=a,e.fillStyle=l;const d=(0,P.fillScaledRadius)(35,s),h=Math.round(t.anchorCoordinates.x*s),c=t.inverseAnchorPosition?Math.round(t.anchorCoordinates.y*o)+Math.round(35*o)*t.direction:Math.round(t.anchorCoordinates.y*o)+Math.round((t.labelHeight+10+35)*o)*t.direction,u=h+Math.max(1,Math.floor(s))%2/2,_=c+Math.max(1,Math.floor(o))%2/2;if(e.shadowOffsetY=1,e.shadowColor=i.shadowColor,e.shadowBlur=4,e.beginPath(),e.arc(u,_,d,0,2*Math.PI,!0),e.closePath(),e.fill(),e.shadowColor="transparent",t.svgRenderer){const i=2*(0,P.fillScaledRadius)(n,s);t.svgRenderer.render(e,{targetViewBox:{x:u-i/2,y:_-i/2,width:i,height:i}})}const p=Math.round(1*s),g=(0,P.strokeScaledRadius)(35,s,p);if(e.lineWidth=p,e.beginPath(),e.arc(u,_,g,0,2*Math.PI,!0),e.closePath(),e.stroke(),i.outsideBorderWidth){e.save();const t=Math.round(i.outsideBorderWidth*s),n=g+p/2+t/2;e.lineWidth=t,e.strokeStyle=i.outsideBorderColor,e.beginPath(),e.arc(u,_,n,0,2*Math.PI,!0),e.closePath(),e.stroke(),e.restore()}}(e,this._data,r,n,t),e.restore()}}!function(e){e[e.EmojiRadius=16]="EmojiRadius",e[e.LabelFontSize=12]="LabelFontSize",e[e.LabelBorderRadius=4]="LabelBorderRadius",e[e.LabelLineSpacing=3]="LabelLineSpacing",e[e.LabelHorzPadding=8]="LabelHorzPadding",e[e.LabelWordWrapWidth=134]="LabelWordWrapWidth",e[e.AdditionalTopBottomSpace=2]="AdditionalTopBottomSpace",e[e.CalculationEpsilon=1e-10]="CalculationEpsilon",e[e.SourceLabelBorderWidth=1]="SourceLabelBorderWidth"}(u||(u={}));const k={circleBorderColor:(0,r.getHexColorByName)("color-cold-gray-900"),labelBackgroundColor:(0,r.getHexColorByName)("color-cold-gray-900"),labelBorderColor:(0,r.getHexColorByName)("color-cold-gray-800"),labelTextColor:(0,r.getHexColorByName)("color-cold-gray-200"),poleColor:(0,r.getHexColorByName)("color-cold-gray-500"),shadowColor:"rgba(0,0,0,0.4)",selectionColor:(0,r.getHexColorByName)("color-tv-blue-500")},B={circleBorderColor:(0,r.getHexColorByName)("color-white"),labelBackgroundColor:(0,r.getHexColorByName)("color-white"),labelBorderColor:(0,r.getHexColorByName)("color-cold-gray-150"),labelTextColor:(0,r.getHexColorByName)("color-cold-gray-900"),poleColor:(0,r.getHexColorByName)("color-cold-gray-500"),shadowColor:"rgba(0,0,0,0.2)",selectionColor:(0,r.getHexColorByName)("color-tv-blue-500")};var I;!function(e){e[e.Label=0]="Label",e[e.Body=1]="Body"}(I||(I={}));class H extends v.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._renderer=new g.CompositeRenderer,this._emojiCache=null,this._destroyed=!1,this._signpostRenderer=new M(e.isPhantom()),this._labelRenderer=new w.LineToolTextRenderer(void 0,new _.HitTestResult(_.HitTarget.MovePoint,(0,v.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,
...this._labelRenderer.getTextInfo()})),this._labelRenderer.positionToCoordinate.bind(this._labelRenderer)),e.properties().childs().emoji.subscribe(this,this._updateEmoji),this._updateEmoji()}destroy(){this._source.properties().childs().emoji.unsubscribeAll(this),this._destroyed=!0,super.destroy()}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._getBasePosition();if(!t)return;const i=this._updateLabelTextRenderer(e,t);if(this._updateTimelineRenderer(t,i),this._renderer.append(this._signpostRenderer),this._renderer.append(this._labelRenderer),!this._source.isPhantom()){const[e]=this._points,t=(0,R.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(e.x,i.y),e.pointIndex,p.PaneCursorType.VerticalResize,!0);this._renderer.append(this.createLineAnchor({points:[t]},0))}}_updateTimelineRenderer(e,t){const{poleStartY:i,inverse:n,direction:r}=e,s=this._source.properties().childs(),o=this._getCurrentColorTheme(),a={emojiRadius:16,poleColor:o.poleColor,svgRenderer:this._emojiCache?.emojiSvgRenderer,poleStartY:i,itemIndex:1,anchorCoordinates:t,direction:r,inverseAnchorPosition:n,labelHeight:this._labelRenderer.measure().height};s.showImage.value()&&(a.plate={circleBackgroundColor:s.backgroundsColors.value(),outsideBorderWidth:0,circleBorderColor:o.circleBorderColor,shadowColor:o.shadowColor,outsideBorderColor:o.selectionColor}),this._signpostRenderer.setData(a)}async _updateEmoji(){const e=this._source.properties().childs().emoji.value();if(this._emojiCache?.emoji===e)return;const t=this._emojiCache={emoji:e},i=await d.instance().getEmoji(e);this._destroyed||this._emojiCache.emoji!==e||(t.emojiSvgRenderer=(0,f.svgRenderer)(i),this.update(),this._model.updateSource(this._source))}_updateLabelTextRenderer(e,t){const i=this._source.properties().childs(),r=this._getCurrentColorTheme(),{positionPointDirection:o,indexCoordinate:a,priceCoordinate:l,inverse:d,direction:h}=t,{height:c,width:u}=e.mediaSize;let _=(0,x.positionToCoordinate)(i.position.value(),c,l,o);_>=-1e-10&&_<=c+1e-10&&(_=Math.min(c-2,Math.max(2,_)));const p={...this._inplaceTextHighlight(),text:this._textData(),fontSize:i.fontSize.value(),bold:i.bold.value(),italic:i.italic.value(),offsetX:0,offsetY:0,points:[new n.Point(a,_)],forceCalculateMaxLineWidth:!0,vertAlign:-1===o?s.VerticalAlign.Bottom:s.VerticalAlign.Top,horzAlign:s.HorizontalAlign.Center,horzTextAlign:s.HorizontalAlign.Center,font:T.CHART_FONT_FAMILY,backgroundRoundRect:4,lineSpacing:3,boxPaddingVert:6,boxPaddingHorz:8,wordWrapWidth:134,color:this._textColor(),borderColor:r.labelBorderColor,borderWidth:1,backgroundColor:r.labelBackgroundColor};this._labelRenderer.setData(p);const g=this._labelRenderer.measure().height;let f={x:a,y:_};if(d){if(i.showImage.value()){const e=80;this._labelRenderer.setData({...p,points:[new n.Point(a,_+e*h)]})}}else{const{poleStartY:e}=t,i=1===h?Math.min(e-g,_):Math.max(e+g,_);_!==i&&(this._labelRenderer.setData({...p,points:[new n.Point(a,i)]}),f={x:a,y:i})}
return this._labelRenderer.setCursorType(this._textCursorType()),this._labelRenderer.isOutOfScreen(u,c)?this.closeTextEditor():this._updateInplaceText(this._labelRenderer.getTextInfo()),f}_getCurrentColorTheme(){return this._model.dark().value()?k:B}_getBasePosition(){const e=this._source.ownerSource();if(!e)return null;const t=e.priceScale(),i=this._model.timeScale(),n=e.firstValue();if(i.isEmpty()||!t||t.isEmpty()||!n)return null;const r=this._model.mainSeries(),s=this._source.customEvent(),o=e===r?(0,x.getSeriesPosition)(r,s):(0,x.getNoDataPosition)(s,t,i,n);if(!o)return null;const{visualDirection:a,positionPointDirection:l}=o,d=a!==l;return{...o,inverse:d,direction:a*(d?-1:1)}}}},583749:(e,t,i)=>{"use strict";i.r(t),i.d(t,{SineLinePaneView:()=>c});var n,r=i(86441),s=i(895379),o=i(69549),a=i(767313),l=i(184114),d=i(898646);!function(e){e[e.Tolerance=3]="Tolerance",e[e.SegmentsPerHalfPeriod=30]="SegmentsPerHalfPeriod"}(n||(n={}));class h extends a.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=e}hitTest(e){const t=(e.x-this._data.point.x)*Math.PI/this._data.width;let i=Math.sin(t-Math.PI/2)*this._data.height/2;return i=this._data.point.y+i+this._data.height/2,Math.abs(i-e.y)<=3?new l.HitTestResult(l.HitTarget.MovePoint):null}_drawImpl(e){const t=e.context;t.strokeStyle=this._data.color,t.lineWidth=this._data.lineWidth,(0,d.setLineStyle)(t,this._data.lineStyle),t.beginPath(),t.moveTo(this._data.point.x,this._data.point.y);const i=Math.max(1,this._data.width/30),n=e.mediaSize.width-this._data.point.x+i;for(let e=1;e<=n;e+=i){const i=e*Math.PI/this._data.width,n=Math.sin(i-Math.PI/2)*this._data.height/2;t.lineTo(this._data.point.x+e,this._data.point.y+n+this._data.height/2)}t.stroke()}}class c extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._renderer=new o.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){const{mediaSize:{height:t}}=e;if(super._updateImpl(e),this._renderer.clear(),this._points.length<2)return;const[i,n]=this._source.points();if(0===2*Math.abs(i.index-n.index))return void this.addAnchors(this._renderer);const[s,o]=this._points,a=Math.abs(s.x-o.x),l=o.y-s.y,d=this._source.properties().childs(),c=d.linewidth.value();if(s.y<-c&&o.y<-c||s.y>t+c&&o.y>t+c)return;const u=2*a,_=s.x>0?s.x-Math.ceil(s.x/u)*u:s.x+Math.floor(-s.x/u)*u,p={point:new r.Point(_,s.y),width:a,height:l,color:d.linecolor.value(),lineWidth:d.linewidth.value(),lineStyle:d.linestyle.value()};this._renderer.append(new h(p)),this.addAnchors(this._renderer)}}},2735:(e,t,i)=>{"use strict";i.r(t),i.d(t,{StickerPaneView:()=>r});var n=i(799136);class r extends n.SvgIconPaneView{_iconColor(){return null}}},932150:(e,t,i)=>{"use strict";i.r(t),i.d(t,{StudyLineDataSourceAnchorsPaneView:()=>s});var n=i(895379),r=i(144044);class s extends n.LineSourcePaneView{renderer(e){return this._invalidated&&(this._updateImpl(e),this._invalidated=!1),this.createLineAnchor({points:this._getPoints().map(r.mapLineSourcePaneViewPointToLineAnchorPoint)},0)}}},844071:(e,t,i)=>{
"use strict";i.r(t),i.d(t,{TextPaneView:()=>p});var n=i(650151),r=i(86441),s=i(986226),o=i(799839),a=i(374410),l=i(69549),d=i(652374),h=i(184114),c=i(144044),u=i(980317),_=i(10666);class p extends u.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r,s,o,a,l,d,c){super(e,t,l,d,c),this._noSelection=!1,this._renderer=null,this._offsetX=i,this._offsetY=n,this._vertAlign=r,this._horzAlign=s,this._forceTextAlign=Boolean(o),this._noSelection=!1,this._renderer=null,this._recalculateSourcePointsOnFirstUpdate=a,this._textRenderer=new _.LineToolTextRenderer(void 0,new h.HitTestResult(h.HitTarget.MovePoint,(0,u.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,...this._textRenderer.getTextInfo()})),this._textRenderer.positionToCoordinate.bind(this._textRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}disableSelection(){this._noSelection=!0}isEditMode(){return!this._getModel().readOnly()}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._getSource(),i=t.priceScale();if(!i||i.isEmpty())return;const u=t.properties().childs(),_=this._getModel(),p={text:this._textData(),color:this._textColor(),fontSize:u.fontsize.value(),boxPadding:u.fontsize.value()/6,font:o.CHART_FONT_FAMILY,vertAlign:this._vertAlign||s.VerticalAlign.Top,horzAlign:this._horzAlign||s.HorizontalAlign.Left,offsetX:this._offsetX||0,offsetY:this._offsetY||0,forceTextAlign:this._forceTextAlign,...this._inplaceTextHighlight()};p.points=t.isFixed()?[(0,n.ensureDefined)(t.fixedPoint())]:this._points,u.fillBackground&&u.fillBackground.value()&&(p.backgroundColor=u.backgroundColor.value()),u.drawBorder&&u.drawBorder.value()&&(p.borderColor=u.borderColor.value()),u.wordWrap&&u.wordWrap.value()&&(p.wordWrapWidth=u.wordWrapWidth.value()),p.bold=u.bold&&u.bold.value(),p.italic=u.italic&&u.italic.value();_.selection().isSelected(t)&&(p.highlightBorder=!0,p.highlightBorderColor=u.color.value()),!t.isFixed()&&this._model.isSnapshot()&&(p.scale=_.timeScale().barSpacingScaleRatio()),this._textRenderer.setData(p),this._textRenderer.setCursorType(this._textCursorType());const{mediaSize:{width:g,height:f}}=e;if(this._textRenderer.isOutOfScreen(g,f))return void this.closeTextEditor();const x=1===p.points.length;if(this._updateInplaceText(this._textRenderer.getTextInfo()),x&&void 0!==this._recalculateSourcePointsOnFirstUpdate){this._renderer=null;const e=this._textRenderer.measure();return this._recalculateSourcePointsOnFirstUpdate(e.width,e.height),void(this._recalculateSourcePointsOnFirstUpdate=void 0)}if(!x||this._noSelection)this._renderer=this._textRenderer;else{const e=new l.CompositeRenderer;e.append(this._textRenderer);const t=p.points[0].clone(),i=this._textRenderer.measure(),n=i.width,s=i.height;if(p.wordWrapWidth){const i=[(0,c.lineSourcePaneViewPointToLineAnchorPoint)(new r.Point(t.x+n,t.y+s/2),1,a.PaneCursorType.HorizontalResize,!0)];e.append(this.createLineAnchor({points:i},1))}if(!this._isTextEditMode()){
const i=new r.Point(t.x+n/2,t.y+s);i.pointIndex=0,e.append(new d.SelectionRenderer({points:[(0,c.lineSourcePaneViewPointToLineAnchorPoint)(i)],bgColors:this._lineAnchorColors([i]),visible:this.areAnchorsVisible(),hittestResult:h.HitTarget.MovePoint,barSpacing:_.timeScale().barSpacing()}))}this._renderer=e}}}},10666:(e,t,i)=>{"use strict";i.d(t,{LineToolTextRenderer:()=>r});var n=i(454304);class r extends n.TextRenderer{getTextInfo(){const e=this._getInternalData(),t=this.fontStyle(),i=this._getFontInfo();return{...e,lineHeight:this.lineHeight(),lineSpacing:this.lineSpacing(),font:t,fontSize:i.fontSize,centerRotation:this.centerTextRotation()??void 0}}setCursorType(e){this._hitTest.data()?.cursorType!==e&&this._hitTest.mergeData({cursorType:e})}}},203414:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LineToolThreeDrivesPaneView:()=>_});var n=i(986226),r=i(936879),s=i(69549),o=i(454304),a=i(251940),l=i(659011),d=i(586982),h=i(306099),c=i(895379),u=i(799839);class _ extends c.LineSourcePaneView{constructor(){super(...arguments),this._retrace1LabelRenderer=new o.TextRenderer,this._retrace12LabelRenderer=new o.TextRenderer,this._polyLineRenderer=new h.PolygonRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;let t=NaN,i=NaN;if(this._source.points().length>=4){const[,e,i,n]=this._source.points();t=Math.round(100*Math.abs((n.price-i.price)/(i.price-e.price)))/100}if(this._source.points().length>=6){const[,,,e,t,n]=this._source.points();i=Math.round(100*Math.abs((n.price-t.price)/(t.price-e.price)))/100}if(this._points.length<2)return;const o=this._source.properties().childs(),h=new s.CompositeRenderer,c=(e,t)=>({points:[e],text:t,color:o.textcolor.value(),vertAlign:n.VerticalAlign.Middle,horzAlign:n.HorizontalAlign.Center,font:u.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:o.bold&&o.bold.value(),italic:o.italic&&o.italic.value(),fontsize:o.fontsize.value(),backgroundColor:o.color.value(),backgroundRoundRect:4}),_=(e,t)=>({points:[e,t],color:o.color.value(),linewidth:o.linewidth.value(),linestyle:r.LINESTYLE_DOTTED,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal}),p={points:this._points,color:o.color.value(),linewidth:o.linewidth.value(),linestyle:r.LINESTYLE_SOLID,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal,backcolor:"rgba(0, 0, 0, 0)",fillBackground:!1,filled:!1};this._polyLineRenderer.setData(p),h.append(this._polyLineRenderer);const g=(0,a.getNumericFormatter)();if(!isNaN(t)){const e=new l.TrendLineRenderer;e.setData(_(this._points[1],this._points[3])),h.append(e);const i=c(this._points[1].add(this._points[3]).scaled(.5),g.format(t));this._retrace1LabelRenderer.setData(i),h.append(this._retrace1LabelRenderer)}if(!isNaN(i)){const e=new l.TrendLineRenderer;e.setData(_(this._points[3],this._points[5])),h.append(e);const t=c(this._points[5].add(this._points[3]).scaled(.5),g.format(i));this._retrace12LabelRenderer.setData(t),h.append(this._retrace12LabelRenderer)}this.addAnchors(h),
this._renderer=h}}},15056:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TimeCyclesPaneView:()=>c});var n=i(86441),r=i(69549),s=i(895379),o=i(589637),a=i(184114),l=i(898646),d=i(767313);class h extends d.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data||e.y>this._data.point.y)return null;if(e.x<this._data.point.x||e.x>this._data.point.x+this._data.width)return null;const t=new n.Point(this._data.point.x+this._data.width/2,this._data.point.y);let i=e.subtract(t);const r=this._data.height/this._data.width;i=new n.Point(i.x,i.y/r);const s=i.length();return Math.abs(s-this._data.width/2)<3?new a.HitTestResult(a.HitTarget.MovePoint):null}_drawImpl(e){if(null===this._data)return;const t=e.context;t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,(0,l.setLineStyle)(t,this._data.linestyle),t.save(),t.translate(this._data.point.x+1,this._data.point.y),t.scale(this._data.width,this._data.height),t.beginPath(),t.arc(.5,0,.5,Math.PI,0,!1),t.restore(),t.stroke(),this._data.fillBackground&&(t.fillStyle=(0,o.generateColor)(this._data.backcolor,this._data.transparency),t.fill())}}class c extends s.LineSourcePaneView{constructor(){super(...arguments),this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const t=this._source.points(),i=t[0],s=t[1],o=Math.min(i.index,s.index),a=Math.max(i.index,s.index),l=a-o,d=this._points[0],c=this._points[1],u=Math.abs(d.x-c.x),_=new r.CompositeRenderer,p=this._source.properties().childs(),g=this._model.timeScale();if(0===l)return;let f=Math.min(d.x,c.x);const x=[];for(let e=o;f>-u;e-=l)f=g.indexToCoordinate(e),x.push(f);f=Math.max(d.x,c.x);for(let e=a;f<g.width();e+=l)f=g.indexToCoordinate(e),x.push(f);for(let e=0;e<x.length;e++){const t={point:new n.Point(x[e],d.y),width:u,height:u,color:p.linecolor.value(),linewidth:p.linewidth.value(),linestyle:p.linestyle.value(),fillBackground:p.fillBackground.value(),backcolor:p.backgroundColor.value(),transparency:p.transparency.value()},i=new h;i.setData(t),_.append(i)}this.addAnchors(_),this._renderer=_}}},323920:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TrendAnglePaneView:()=>T});var n=i(650151),r=i(86441),s=i(5531),o=i(934026),a=i(431520),l=i(986226),d=i(184114),h=i(586982),c=i(893714),u=i(454304),_=i(659011),p=i(652374),g=i(799839),f=i(144044),x=i(767313);class v extends x.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){return null}_drawImpl(e){if(null===this._data)return;const t=e.context;t.translate(this._data.point.x,this._data.point.y),t.strokeStyle=this._data.color,t.setLineDash([1,2]);const i=this._data.size;t.beginPath(),t.moveTo(0,0),t.lineTo(i,0),t.arc(0,0,i,0,-this._data.angle,this._data.angle>0),t.stroke()}}var w,R=i(229492);!function(e){e[e.DistanceBetweenAngleAndPoint=50]="DistanceBetweenAngleAndPoint",e[e.AngleTextOffsetY=0]="AngleTextOffsetY",
e[e.AngleTextOffsetX=5]="AngleTextOffsetX"}(w||(w={}));class T extends R.TrendToolWithStatsPaneView{constructor(e,t){super(e,t),this._secondPoint=null,this._trendRenderer=new _.TrendLineRenderer,this._angleRenderer=new v,this._angleLabelRenderer=new u.TextRenderer}_getPointsForStats(){return[this._points[0],(0,n.ensureNotNull)(this._middlePoint),(0,n.ensureNotNull)(this._secondPoint)]}_updateImpl(e){this._renderer.clear(),super._updateImpl(e);const t=this._source,i=t.angle();if(this._points.length>0&&null!==i){const e=Math.cos(i),n=-Math.sin(i),s=new r.Point(e,n);this._secondPoint=this._points[0].addScaled(s,t.distance()),this._middlePoint=this._source.calcMiddlePoint(this._points[0],this._secondPoint)}this._invalidated=!1;const n=this._source.priceScale(),u=this._model.timeScale();if(!n||n.isEmpty()||u.isEmpty())return;if(null===this._model.timeScale().visibleBarsStrictRange())return;if(this._source.points().length<2)return;if(this._points.length<2||null===this._secondPoint)return;const _=this._points[0],x=this._points[1],v=this._source.properties().childs();v.showBarsRange.value()||v.showPriceRange.value()||v.showPercentPriceRange.value()||v.showPipsPriceRange.value()||(this._label=null,this._labelData&&(this._labelData.text=""));const w=v.linecolor.value(),R={points:[_,this._secondPoint],color:w,linewidth:v.linewidth.value(),linestyle:v.linestyle.value(),extendleft:v.extendLeft.value(),extendright:v.extendRight.value(),leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal};this._trendRenderer.setData(R),this._renderer.append(this._trendRenderer);const T=(0,r.box)(new r.Point(0,0),new r.Point(e.mediaSize.width,e.mediaSize.height));let m=!1;v.statsPosition.value()===c.StatsPosition.Auto&&(m=(0,r.equalPoints)(_,x)?!(0,o.pointInBox)(_,T):null===(0,s.intersectLineSegmentAndBox)((0,r.lineSegment)(_,x),T));(this.isHoveredSource()||this.isSelectedSource()||v.alwaysShowStats.value())&&!m&&2===this._points.length&&this._renderer.append(this._updateAndReturnStatsRenderer(e));const y=(this.isHoveredSource()||this.isSelectedSource())&&v.showMiddlePoint.value();this._middlePoint&&this._renderer.append(new p.SelectionRenderer({points:[(0,f.lineSourcePaneViewPointToLineAnchorPoint)(this._middlePoint,0)],bgColors:this._lineAnchorColors([this._middlePoint]),color:w,visible:y&&this.areAnchorsVisible(),hittestResult:d.HitTarget.Regular,barSpacing:0}));const P={point:_,angle:t.angle()??0,color:w,size:50};this._angleRenderer.setData(P),this._renderer.append(this._angleRenderer);const L=180*P.angle/Math.PI,b=Math.round(100*L)/100+"º",S={points:[new r.Point(_.x+50,_.y)],text:(0,a.forceLTRStr)(b),color:w,horzAlign:l.HorizontalAlign.Left,font:g.CHART_FONT_FAMILY,offsetX:5,offsetY:0,bold:v.bold.value(),italic:v.italic.value(),fontsize:v.fontsize.value(),vertAlign:l.VerticalAlign.Middle};this._angleLabelRenderer.setData(S),this._renderer.append(this._angleLabelRenderer),R.points.length>=2&&this._addAlertRenderer(this._renderer,R.points),this._renderer.append(this.createLineAnchor({points:[(0,f.lineSourcePaneViewPointToLineAnchorPoint)(_),(0,
f.lineSourcePaneViewPointToLineAnchorPoint)(this._secondPoint,1)]},0))}}},6649:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TrendBasedFibExtensionPaneView:()=>d});var n=i(86441),r=i(862903),s=i(659011),o=i(586982),a=i(126878),l=i(995474);class d extends l.FibHorizontalLevelsPaneViewBase{constructor(){super(...arguments),this._trendLineRendererPoints12=new s.TrendLineRenderer,this._trendLineRendererPoints23=new s.TrendLineRenderer,this._rectangleRenderers={},this._levels=[]}_tryActivateEditMode(e,t){this._source.setInplaceEditLevelIndex(e),super._tryActivateEditMode(e,t)}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._points.length<2)return;const[t,i]=this._points,s=this._source.properties().childs();if(3===this._source.points().length){const e=this._source.priceScale();if(!e||e.isEmpty()||this._model.timeScale().isEmpty())return;const t=this._source.ownerSource()?.firstValue();if(null==t)return;const[i,n,r]=this._source.points();let o=!1;s.reverse&&s.reverse.value()&&(o=s.reverse.value()),this._levels=[];const l=o?i.price:n.price,d=o?n.price:i.price,h=l-d;let c,u,_;const p=e.isLog()&&s.fibLevelsBasedOnLogScale.value();if(p){c=e.priceToCoordinate(l,t);u=c-e.priceToCoordinate(d,t),_=e.priceToCoordinate(r.price,t)}const g={price:r.price,coordinate:_},f={price:h,coordinate:u},x=this._source.levelsCount();for(let i=1;i<=x;i++){const n=s["level"+i].childs();if(!n.visible.value())continue;const r=n.coeff.value(),o=n.color.value(),l=n.text.value(),d=(0,a.fibLevelCoordinate)(g,f,r,e,t,p),h=(0,a.fibLevelPrice)(g,f,r,e,t,p);this._levels.push({color:o,price:h,y:d,linewidth:s.levelsStyle.childs().linewidth.value(),linestyle:s.levelsStyle.childs().linestyle.value(),index:i,text:l})}}const l=s.trendline.childs();if(l.visible.value()){const e={points:[t,i],color:l.color.value(),linewidth:l.linewidth.value(),linestyle:l.linestyle.value(),extendleft:!1,extendright:!1,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal};this._trendLineRendererPoints12.setData(e),this._renderer.append(this._trendLineRendererPoints12)}if(this._points.length<3)return void this.addAnchors(this._renderer);let d=null;const h=this._points[2];l.visible.value()&&(d=this._trendLineRendererPoints23,d.setData({points:[i,h],color:l.color.value(),linewidth:l.linewidth.value(),linestyle:l.linestyle.value(),extendleft:!1,extendright:!1,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal}));const c=Math.min(h.x,i.x),u=Math.max(h.x,i.x),_=s.fillBackground.value(),p=s.transparency.value(),g=s.extendLinesLeft.value(),f=s.extendLines.value();if(_)for(let e=0;e<this._levels.length;e++)if(e>0&&_){const t=this._levels[e-1],i={points:[new n.Point(c,this._levels[e].y),new n.Point(u,t.y)],color:this._levels[e].color,linewidth:0,backcolor:this._levels[e].color,fillBackground:!0,transparency:p,extendLeft:g,extendRight:f};this._rectangleRenderers.hasOwnProperty(e)||(this._rectangleRenderers[e]=new r.RectangleRenderer(!0));const s=this._rectangleRenderers[e];s.setData(i),this._renderer.append(s)}this._addLevels({mediaSize:e.mediaSize,levels:this._levels,left:c,right:u,
showLabel:s.showCoeffs.value()||s.showPrices.value(),showText:s.showText.value(),labelAlign:[s.horzLabelsAlign.value(),s.vertLabelsAlign.value()],textAlign:[s.horzTextAlign.value(),s.vertTextAlign.value()],extendLeft:g,extendRight:f,fontSize:s.labelFontSize.value(),isOnScreen:!0,trendLineRenderer:d}),this.addAnchors(this._renderer),this._model.selection().isSelected(this._source)||this.closeTextEditor()}}},871650:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TrendBasedFibTimePaneView:()=>p});var n=i(86441),r=i(986226),s=i(457563),o=i(454304),a=i(862903),l=i(659011),d=i(184114),h=i(69549),c=i(586982),u=i(799839),_=i(895379);class p extends _.LineSourcePaneView{constructor(e,t){super(e,t),this._trendLineRendererPoints12=new l.TrendLineRenderer,this._trendLineRendererPoints23=new l.TrendLineRenderer,this._textRenderers=[],this._renderer=new h.CompositeRenderer,this._levels=[];for(let t=0;t<e.levelsCount();t++)this._textRenderers.push(new o.TextRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.properties().childs();if(3===this._source.points().length){const e=this._model.timeScale();if(e.isEmpty())return;const[i,n,r]=this._source.points();if(this._levels=[],n.index===i.index)return;const s=n.index-i.index,o=r.index;if(null===e.visibleBarsStrictRange())return;for(let i=1;i<=this._source.levelsCount();i++){const n=t["level"+i].childs();if(!n.visible.value())continue;const r=n.coeff.value(),a=n.color.value(),l=Math.round(o+r*s),d={x:e.indexToCoordinate(l),coeff:r,color:a,linewidth:n.linewidth.value(),linestyle:n.linestyle.value(),index:i,text:String(r)};this._levels.push(d)}}if(this._points.length<2)return;const i=new h.CompositeRenderer,[l,_]=this._points,p=t.trendline.childs();if(p.visible.value()){const e={points:[l,_],color:p.color.value(),linewidth:p.linewidth.value(),linestyle:p.linestyle.value(),extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal};this._trendLineRendererPoints12.setData(e),i.append(this._trendLineRendererPoints12)}if(this._points.length<3)return this.addAnchors(i),void(this._renderer=i);const g=this._points[2];if(p.visible.value()){const e={points:[_,g],color:p.color.value(),linewidth:p.linewidth.value(),linestyle:p.linestyle.value(),extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal};this._trendLineRendererPoints23.setData(e),i.append(this._trendLineRendererPoints23)}const{mediaSize:{width:f,height:x}}=e;if(t.fillBackground.value()){const e=t.transparency.value();for(let t=1;t<this._levels.length;t++){const r=this._levels[t-1],s={points:[new n.Point(r.x,0),new n.Point(this._levels[t].x,x)],color:this._levels[t].color,linewidth:0,backcolor:this._levels[t].color,fillBackground:!0,transparency:e,extendLeft:!1,extendRight:!1},o=new a.RectangleRenderer(!0);o.setData(s),i.append(o)}}let v=t.horzLabelsAlign.value();v=v===r.HorizontalAlign.Left?r.HorizontalAlign.Right:v===r.HorizontalAlign.Right?r.HorizontalAlign.Left:r.HorizontalAlign.Center
;const w=t.vertLabelsAlign.value(),R=t.showCoeffs.value();for(let e=0;e<this._levels.length;e++){let t;if(R){let r;switch(w){case"top":r=new n.Point(this._levels[e].x,0);break;case"middle":r=new n.Point(this._levels[e].x,.5*x);break;default:r=new n.Point(this._levels[e].x,x)}const s={points:[r],text:this._levels[e].text,color:this._levels[e].color,vertAlign:w,horzAlign:v,font:u.CHART_FONT_FAMILY,offsetX:2,offsetY:0,fontsize:12},a=this._textRenderers[e];a.setData(s),this._needLabelExclusionPath(a)&&(t=(0,o.getTextBoundaries)(a,f,x)??void 0),i.append(a)}const r={x:this._levels[e].x,color:this._levels[e].color,linewidth:this._levels[e].linewidth,linestyle:this._levels[e].linestyle,excludeBoundaries:t},a=new d.HitTestResult(d.HitTarget.MovePoint,void 0,this._levels[e].index),l=new s.VerticalLineRenderer;l.setData(r),l.setHitTest(a),i.append(l)}this.addAnchors(i),this._renderer=i}_needLabelExclusionPath(e){return"center"===this._source.properties().childs().horzLabelsAlign.value()}}},818134:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TrendLinePaneView:()=>x});var n=i(650151),r=i(86441),s=i(934026),o=i(5531),a=i(454304),l=i(10666),d=i(863192),h=i(893714),c=i(652374),u=i(659011),_=i(799839),p=i(184114),g=i(229492),f=i(980317);class x extends g.TrendToolWithStatsPaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._trendRenderer=new u.TrendLineRenderer,this._labelRenderer=new l.LineToolTextRenderer(void 0,new p.HitTestResult(p.HitTarget.MovePoint,(0,f.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,rotationPoint:this._labelRenderer.rotation()??void 0,...this._labelRenderer.getTextInfo()})),this._labelRenderer.positionToCoordinate.bind(this._labelRenderer))}_getPointsForStats(){return[this._points[0],(0,n.ensureNotNull)(this._middlePoint),this._points[1]]}_updateImpl(e){this._renderer.clear(),this._invalidated=!1;const t=this._source.priceScale(),i=this._model.timeScale();if(!t||t.isEmpty()||i.isEmpty())return;const n=this._model.timeScale().visibleBarsStrictRange();if(null===n)return;const l=this._source.points();if(l.length<2)return;const u=l[0],g=l[1],f=this._source.properties().childs();if(u.index<n.firstBar()&&g.index<n.firstBar()&&!f.extendLeft.value()&&!f.extendRight.value())return;if(super._updateImpl(e),this._points.length<2)return;f.showPriceRange.value()||f.showPercentPriceRange.value()||f.showPipsPriceRange.value()||f.showBarsRange.value()||f.showDateTimeRange.value()||f.showDistance.value()||f.showAngle.value()||(this._label=null,this._labelData&&(this._labelData.text=""));const x=this._points[0],v=this._points[1],{mediaSize:{width:w,height:R}}=e;let T;const m=f.text.value(),y=this._isTextEditMode(),P=this._placeHolderMode(!0);if(f.showLabel?.value()&&m||P||y){const e=x.x<v.x?x:v,t=e===x?v:x,i=f.vertLabelsAlign.value(),n=f.horzLabelsAlign.value();let s;s="left"===n?e.clone():"right"===n?t.clone():new r.Point((x.x+v.x)/2,(x.y+v.y)/2);const o=Math.atan((t.y-e.y)/(t.x-e.x));this._labelRenderer.setData({
points:[s],text:this._textData(),color:this._textColor(),vertAlign:i,horzAlign:n,font:_.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:f.bold.value(),italic:f.italic.value(),fontsize:f.fontsize.value(),forceTextAlign:!0,angle:o,decorator:P?d.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()}),this._labelRenderer.setCursorType(this._textCursorType()),this._renderer.append(this._labelRenderer),this._needLabelExclusionPath(this._labelRenderer)&&(T=(0,a.getTextBoundaries)(this._labelRenderer,w,R)??void 0),this._labelRenderer.isOutOfScreen(w,R)?this.closeTextEditor():this._updateInplaceText(this._labelRenderer.getTextInfo())}const L=f.linecolor.value(),b={points:this._points,color:L,linewidth:f.linewidth.value(),linestyle:f.linestyle.value(),extendleft:f.extendLeft.value(),extendright:f.extendRight.value(),leftend:f.leftEnd.value(),rightend:f.rightEnd.value(),excludeBoundaries:T?[T]:void 0};this._trendRenderer.setData(b),this._renderer.insert(this._trendRenderer,0);const S=(0,r.box)(new r.Point(0,0),new r.Point(w,R));let A=!1;f.statsPosition.value()===h.StatsPosition.Auto&&(A=(0,r.equalPoints)(x,v)?!(0,s.pointInBox)(x,S):null===(0,o.intersectLineSegmentAndBox)((0,r.lineSegment)(x,v),S));if(((this.isHoveredSource()||this.isSelectedSource())&&this.isEditMode()||f.alwaysShowStats.value())&&!A&&2===this._points.length&&this._renderer.append(this._updateAndReturnStatsRenderer(e)),this._middlePoint&&!y){const e=(this.isHoveredSource()||this.isSelectedSource())&&f.showMiddlePoint.value();this._renderer.append(new c.SelectionRenderer({points:[{point:this._middlePoint}],bgColors:this._lineAnchorColors([this._middlePoint]),color:L,visible:e&&this.areAnchorsVisible(),hittestResult:p.HitTarget.Regular,barSpacing:0}))}this.addAnchors(this._renderer),b.points.length>=2&&this._addAlertRenderer(this._renderer,b.points)}}},229492:(e,t,i)=>{"use strict";i.d(t,{TrendToolWithStatsPaneView:()=>V});var n=i(650151),r=i(5531),s=i(86441),o=i(609838),a=i(431520),l=i(986226),d=i(69549),h=i(251940),c=i(684740),u=i(327714),_=i(934026),p=i(926048),g=i(885800),f=i(454304),x=i(184114),v=i(898646),w=i(652283),R=i(463940),T=i(313835),m=i(763929),y=i(342057),P=i(901195);let L=null;const b=(0,p.getHexColorByName)("color-cold-gray-50"),S=(0,p.getHexColorByName)("color-cold-gray-800"),A={angle:(0,T.svgRenderer)(m),priceRange:(0,T.svgRenderer)(y),barsRange:(0,T.svgRenderer)(P)},C=18;class M{constructor(e){this._data=null,this._preRenderedData=null,this._textWidthCache=new R.TextWidthCache,this._hittest=e||new x.HitTestResult(x.HitTarget.MovePoint)}setData(e){this._data=e,this._preRenderedData=null}draw(e,t){if(!this._data||null===this._data.text)return;const i=this._preRender();if(!i)return;const{horizontalPixelRatio:n,verticalPixelRatio:r}=t,{point:s,horzAlign:o,doNotAlignText:a,backgroundRoundRect:d,backgroundColor:h,color:c,lineSpacing:u,icons:_,isDark:p}=this._data,{font:g,lineHeight:f,lines:x,padding:R}=i,T=Math.round(s.x*n),m=Math.round(s.y*r),y=Math.round(i.rectSize.width*n),P=Math.round(i.rectSize.height*r)
;if(o!==l.HorizontalAlign.Right&&o!==l.HorizontalAlign.Center||!0!==a&&(e.textAlign=o===l.HorizontalAlign.Right?"end":"center"),d?((0,v.drawRoundRect)(e,T,m,y,P,d*n),e.fillStyle=h,e.fill(),e.globalAlpha=1):(e.fillStyle=h,e.fillRect(T,m,y,P),e.globalAlpha=1),_){const i=Math.max(0,(f-C)/2),o=Math.round((s.x+R.left)*n);let a=s.y+R.top+i;for(const i of _){const n=Math.round(a*r);this._drawIcon(e,o,n,A[i],Boolean(p),t),a+=f+u}}const L=i.textPointOffset,b=s.x+L.x;let S=s.y+L.y;e.fillStyle=c,e.textBaseline="middle",e.font=g;const M=Math.round(f/2);(0,w.drawScaled)(e,n,r,(()=>{for(const t of x)e.fillText(t,b,S+M),S+=f+u}))}hitTest(e){const t=this._data,i=this._preRender();if(!t||!i)return null;const n=(0,s.point)(t.point.x+i.rectSize.width,t.point.y+i.rectSize.height);return(0,_.pointInBox)(e,(0,s.box)(t.point,n))?this._hittest:null}rectSize(){const e=this._preRender();return e?(0,u.size)({width:e.rectSize.width,height:e.rectSize.height}):null}updatePoint(e){this._data&&(this._data.point=e)}_preRender(){if(!this._data)return null;if(this._preRenderedData)return this._preRenderedData;const{fontSize:e=12,text:t,wordWrapWidth:i,paddingBottom:n,paddingTop:r,paddingLeft:s,paddingRight:o,icons:a,bold:l,italic:d,lineSpacing:h}=this._data,c=(0,g.makeFont)(e,this._data.font,d?"italic":void 0,l?"bold":void 0),_=function(){if(null!==L)return L;const e=(0,w.createDisconnectedCanvas)(document,(0,u.size)({width:0,height:0}));return L=(0,w.getPrescaledContext2D)(e),L}(),p={x:0,y:0};_.textBaseline="middle",_.font=c;const x=null===t?[]:(0,f.wordWrap)(t,c,this._textWidthCache,!0,i).map((e=>e.text));let v=0;if(i)v=i;else for(const e of x)v=Math.max(v,_.measureText(e).width);const R={top:r,right:o,bottom:n,left:s},T=Math.max(e,a?.length?C:0),m=T*x.length+h*(x.length-1),y={width:v+R.left+R.right,height:m+R.top+R.bottom};if(p.x+=R.left,p.y+=R.top,a){const t=void 0!==this._data.textPadding?this._data.textPadding:e/2;p.x+=C+t,y.width+=C+t}return y.width%2!=0&&y.width++,this._preRenderedData={rectSize:y,padding:R,textPointOffset:p,lines:x,lineHeight:T,font:c},this._preRenderedData}_drawIcon(e,t,i,n,r,s){e.fillStyle=r?b:S,n.render(e,{targetViewBox:{x:t,y:i,width:Math.round(C*s.horizontalPixelRatio),height:Math.round(C*s.verticalPixelRatio)},doNotApplyColors:!0})}}var k,B,I,H=i(799839),D=i(893714),z=i(980317);!function(e){e[e.Offset=8]="Offset",e[e.FontSize=12]="FontSize",e[e.LineSpacing=8]="LineSpacing",e[e.RectRadius=4]="RectRadius",e[e.TextPadding=12]="TextPadding",e[e.PaddingTopBottom=12]="PaddingTopBottom",e[e.PaddingLeftRight=12]="PaddingLeftRight"}(k||(k={})),function(e){e.Background="#f2f2f2e6",e.Text="#1a1a1a"}(B||(B={})),function(e){e.Background="#464646e6",e.Text="#FFFFFF"}(I||(I={}));class V extends z.InplaceTextLineSourcePaneView{constructor(){super(...arguments),this._renderer=new d.CompositeRenderer,this._labelData=null,this._label=null,this._statsRenderer=new M}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateAndReturnStatsRenderer(e){this._statsRenderer.setData(this._statLabelData())
;const t=this._statsRenderer.rectSize();if(null!==t){const i=this._targetRect(e,t);this._statsRenderer.updatePoint((0,s.point)(i.left,i.top))}return this._statsRenderer}_targetRect(e,t){const i=this._source.properties().childs().statsPosition.value(),n=this._getPointsForStats(),o=i===D.StatsPosition.Auto?D.StatsPosition.Center:i;let a=n[o].x+12,l=n[o].y;const d=this._points[1].y<this._points[0].y&&this._points[1].x<this._points[0].x||this._points[1].y>this._points[0].y&&this._points[1].x>this._points[0].x;d?l-=12+t.height:l+=12;const{mediaSize:h}=e;return i!==D.StatsPosition.Auto||(0,s.equalPoints)(n[D.StatsPosition.Left],n[D.StatsPosition.Right])||(a<0?a=0:a+t.width>h.width&&(a=h.width-t.width),l<0?l=0:l+t.height>h.height&&(l=h.height-t.height),(0,r.intersectLineSegmentAndBox)((0,s.lineSegment)(n[D.StatsPosition.Left],n[D.StatsPosition.Right]),(0,s.box)((0,s.point)(a,l),(0,s.point)(a+t.width,l+t.height)))&&(l=d?n[o].y+12:n[o].y-12-t.height,a=Math.min(n[D.StatsPosition.Center].x,h.width)-t.width)),{left:Math.floor(a),top:Math.floor(l),width:t.width,height:t.height}}_priceRange(){const[e,t]=this._source.points(),i=this._source.properties().childs(),r=i.showPriceRange.value(),s=i.showPercentPriceRange.value(),o=i.showPipsPriceRange.value(),a=(0,n.ensureNotNull)(this._source.ownerSource());let l;if(this._source.priceScale()&&(r||s||o)){const i=[],n=t.price-e.price;if(r||s){const o=n/Math.abs(e.price),l=[];if(r){const i=a.formatter(),r=i.formatChange?.(t.price,e.price)??i.format(n);l.push(r)}if(s){const e=(0,h.getPercentageFormatter)().format(100*o);l.push(r?`(${e})`:e)}i.push(l.join(" "))}const d=this._model.mainSeries().symbolInfo(),c=d&&(0,h.getPipFormatter)(d);o&&c&&i.push(c.format(n)),l=i.join(", ")}return l}_statLabelData(){const[e,t]=this._source.points(),r=this._source.properties().childs(),s=[];let d,u,_,p,g;const f=this._priceRange();void 0!==f&&s.push("priceRange");const x=r.showBarsRange.value(),v=r.showDateTimeRange&&r.showDateTimeRange.value(),w=r.showDistance&&r.showDistance.value(),R=r.showAngle&&r.showAngle.value();if(R||w){const i=(0,n.ensureNotNull)(this._source.pointToScreenPoint(e));p=(0,n.ensureNotNull)(this._source.pointToScreenPoint(t)).subtract(i),g=Math.round(1e5*p.length())/1e5}if(x||v||w){if(d="",x&&(_=t.index-e.index,d+=o.t(null,void 0,i(841643)).format({count:(0,a.forceLTRStr)(String(_))})),v){const i=this._model.timeScale().indexToUserTime(e.index),n=this._model.timeScale().indexToUserTime(t.index);if(i&&n){const e=(n.valueOf()-i.valueOf())/1e3,t=(0,a.startWithLTR)((new c.TimeSpanFormatter).format(e));t&&(d+=x?" ("+t+")":t)}}w&&(d&&(d+=", "),d+=o.t(null,void 0,i(744994)).format({number:(0,a.forceLTRStr)((0,h.getNumericFormatter)().format(Math.round(Number(g))))})),d&&s.push("barsRange")}if(R){let e;if(void 0!==g&&g>0&&void 0!==p&&(p=p.normalized(),e=Math.acos(p.x),p.y>0&&(e=-e)),"number"==typeof e&&!isNaN(e)){const t=180*e/Math.PI;u=Math.round(100*t)/100+"º",s.push("angle")}}this._label=[(0,a.forceLTRStr)(f),d,u].filter((e=>null!=e)).join("\n")||null
;const T=this._model.dark().value(),m=T?"#464646e6":"#f2f2f2e6",y=T?"#FFFFFF":"#1a1a1a",P={point:this._points[1],text:this._label,color:y,isDark:T,font:H.CHART_FONT_FAMILY,fontSize:12,lineSpacing:8,backgroundColor:m,backgroundRoundRect:4,paddingLeft:12,paddingRight:12,paddingTop:12,paddingBottom:12,textPadding:12,doNotAlignText:!0,icons:s,bold:!1,italic:!1,lines:[],wordWrapWidth:0};return this._points[1].y<this._points[0].y&&(P.vertAlign=l.VerticalAlign.Bottom),this._points[1].x<this._points[0].x&&(P.horzAlign=l.HorizontalAlign.Right),this._labelData=P,P}}},971344:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LineToolTrianglePatternPaneView:()=>p});var n=i(650151),r=i(86441),s=i(936879),o=i(986226),a=i(69549),l=i(659011),d=i(402359),h=i(454304),c=i(586982),u=i(895379),_=i(799839);class p extends u.LineSourcePaneView{constructor(){super(...arguments),this._trendLineRendererPoints01=new l.TrendLineRenderer,this._trendLineRendererPoints12=new l.TrendLineRenderer,this._trendLineRendererPoints23=new l.TrendLineRenderer,this._intersectionRenderer=new d.TriangleRenderer,this._aLabelRenderer=new h.TextRenderer,this._bLabelRenderer=new h.TextRenderer,this._cLabelRenderer=new h.TextRenderer,this._dLabelRenderer=new h.TextRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const[t,i,l,d]=this._points;let h,u,p;if(4===this._points.length){if(Math.abs(l.x-t.x)<1||Math.abs(d.x-i.x)<1)return;let e=Math.min(t.x,i.x);e=Math.min(e,l.x),e=Math.min(e,d.x);const n=(l.y-t.y)/(l.x-t.x),s=t.y+(e-t.x)*n,o=(d.y-i.y)/(d.x-i.x),a=i.y+(e-i.x)*o;if(Math.abs(n-o)<1e-6)return;u=new r.Point(e,s),p=new r.Point(e,a);const c=(i.y-t.y+(t.x*n-i.x*o))/(n-o);if(c<e){let e=Math.max(t.x,i.x);e=Math.max(e,l.x),e=Math.max(e,d.x),u=new r.Point(e,t.y+(e-t.x)*n),p=new r.Point(e,i.y+(e-i.x)*o)}const _=t.y+(c-t.x)*n;h=new r.Point(c,_)}if(this._points.length<2)return;const g=this._source.properties().childs(),f=new a.CompositeRenderer,x=(e,t)=>({points:[e],text:t,color:g.textcolor.value(),vertAlign:o.VerticalAlign.Middle,horzAlign:o.HorizontalAlign.Center,font:_.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:g.bold&&g.bold.value(),italic:g.italic&&g.italic.value(),fontsize:g.fontsize.value(),backgroundColor:g.color.value(),backgroundRoundRect:4}),v=(e,t)=>({points:[e,t],color:g.color.value(),linewidth:g.linewidth.value(),linestyle:s.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal});if(this._trendLineRendererPoints01.setData(v(t,i)),f.append(this._trendLineRendererPoints01),this._points.length>=3&&(this._trendLineRendererPoints12.setData(v(i,l)),f.append(this._trendLineRendererPoints12)),4===this._points.length&&(this._trendLineRendererPoints23.setData(v(l,d)),f.append(this._trendLineRendererPoints23),h)){const e={points:[(0,n.ensureDefined)(u),(0,n.ensureDefined)(p),h],color:g.color.value(),linewidth:g.linewidth.value(),backcolor:g.backgroundColor.value(),fillBackground:g.fillBackground.value(),
transparency:g.transparency.value(),linestyle:s.LINESTYLE_DOTTED};this._intersectionRenderer.setData(e),f.append(this._intersectionRenderer)}const w=x(t,"A");i.y>t.y?(w.vertAlign=o.VerticalAlign.Bottom,w.offsetY=5):(w.vertAlign=o.VerticalAlign.Top,w.offsetY=5),this._aLabelRenderer.setData(w),f.append(this._aLabelRenderer);const R=x(i,"B");if(i.y<t.y?(R.vertAlign=o.VerticalAlign.Bottom,R.offsetY=5):(R.vertAlign=o.VerticalAlign.Top,R.offsetY=5),this._bLabelRenderer.setData(R),f.append(this._bLabelRenderer),this._points.length>2){const e=x(l,"C");l.y<i.y?(e.vertAlign=o.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=o.VerticalAlign.Top,e.offsetY=5),this._cLabelRenderer.setData(e),f.append(this._cLabelRenderer)}if(this._points.length>3){const e=x(d,"D");d.y<l.y?(e.vertAlign=o.VerticalAlign.Bottom,e.offsetY=5):(e.vertAlign=o.VerticalAlign.Top,e.offsetY=5),this._dLabelRenderer.setData(e),f.append(this._dLabelRenderer)}this.addAnchors(f),this._renderer=f}}},764366:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TrianglePaneView:()=>o});var n=i(69549),r=i(402359),s=i(895379);class o extends s.LineSourcePaneView{constructor(){super(...arguments),this._renderer=new n.CompositeRenderer,this._triangleRenderer=new r.TriangleRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.properties().childs(),i={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),fillBackground:t.fillBackground.value(),transparency:t.transparency.value()};this._triangleRenderer.setData(i),this._renderer.append(this._triangleRenderer),this.addAnchors(this._renderer)}}},363039:(e,t,i)=>{"use strict";i.r(t),i.d(t,{VertLinePaneView:()=>g});var n=i(86441),r=i(986226),s=i(69549),o=i(454304),a=i(374410),l=i(457563),d=i(863192),h=i(144044),c=i(799839),u=i(184114),_=i(980317),p=i(10666);class g extends _.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r,s){super(e,t,n,r,s),this._lineRenderer=new l.VerticalLineRenderer,this._renderer=null,this._pane=i,this._labelRenderer=new p.LineToolTextRenderer(void 0,new u.HitTestResult(u.HitTarget.MovePoint,(0,_.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null))))}additionalCursorData(){const e=this._labelRenderer.getTextInfo();return{color:this._source.editableTextStyle().cursorColor,lineSpacing:e.lineSpacing,lineHeight:e.lineHeight,rotationPoint:this._labelRenderer.rotation()??void 0}}positionToCoordinate(e){return this._labelRenderer.positionToCoordinate(e)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_validatePriceScale(){return!0}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,0===this._points.length)return;const{mediaSize:{width:t,height:i}}=e,l=this._source.properties().childs(),_=new s.CompositeRenderer,p=l.text.value(),g=this._isTextEditMode(),f=this._placeHolderMode();let x,v=!0;if(1===this._points.length){const e=new n.Point(this._points[0].x,i/2);this._addAlertRenderer(_,[e])}
if(this._source.model().paneForSource(this._source)===this._pane&&(l.showLabel?.value()&&p||f||g)){let e=0,s=5,a=r.HorizontalAlign.Center,h=r.VerticalAlign.Middle;const u=this._points[0].x;let p=0;switch(l.vertLabelsAlign.value()){case"top":p=i;break;case"middle":p=i/2;break;case"bottom":p=0}if("horizontal"===l.textOrientation.value()){switch(l.horzLabelsAlign.value()){case"left":a=r.HorizontalAlign.Right;break;case"right":a=r.HorizontalAlign.Left;break;case"center":a=r.HorizontalAlign.Center}switch(l.vertLabelsAlign.value()){case"top":h=r.VerticalAlign.Bottom;break;case"middle":h=r.VerticalAlign.Middle;break;case"bottom":h=r.VerticalAlign.Top}}else{switch(e=-Math.PI/2,s=0,l.horzLabelsAlign.value()){case"left":h=r.VerticalAlign.Bottom;break;case"right":h=r.VerticalAlign.Top;break;case"center":h=r.VerticalAlign.Middle}switch(l.vertLabelsAlign.value()){case"top":a=r.HorizontalAlign.Left;break;case"middle":a=r.HorizontalAlign.Center;break;case"bottom":a=r.HorizontalAlign.Right}}this._labelRenderer.setData({points:[new n.Point(u,p)],text:this._textData(),color:this._textColor(),vertAlign:h,horzAlign:a,font:c.CHART_FONT_FAMILY,offsetX:s,offsetY:0,bold:l.bold.value(),italic:l.italic.value(),fontsize:l.fontsize.value(),forceTextAlign:!0,angle:e,decorator:f?d.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()}),this._labelRenderer.setCursorType(this._textCursorType()),_.append(this._labelRenderer),this._needLabelExclusionPath(this._labelRenderer)&&(x=(0,o.getTextBoundaries)(this._labelRenderer,t,i)??void 0),v=this._labelRenderer.isOutOfScreen(t,i),v?this.closeTextEditor():this._updateInplaceText(this._labelRenderer.getTextInfo())}const w={x:this._points[0].x,color:l.linecolor.value(),linewidth:l.linewidth.value(),linestyle:l.linestyle.value(),excludeBoundaries:x},R=w.linewidth/2+1;if(v=v&&(w.x<-R||w.x>t+R),this._lineRenderer.setData(w),this._lineRenderer.setHitTest(new u.HitTestResult(u.HitTarget.MovePoint,{snappingIndex:this._source.points()[0].index})),_.insert(this._lineRenderer,0),!v){if(1===this._points.length&&!this._isTextEditMode()){const e=[(0,h.lineSourcePaneViewPointToLineAnchorPoint)(new n.Point(this._points[0].x,.9*i),0,a.PaneCursorType.HorizontalResize,!0,void 0,void 0,this._source.points()[0].index)];_.append(this.createLineAnchor({points:e},0))}this._renderer=_}}_needLabelExclusionPath(e){const t=this._source.properties().childs(),i="horizontal"===t.textOrientation.value(),n=t.text.value();if(i)return""!==n.trim();if("center"!==t.horzLabelsAlign.value())return!1;const r=e.getLinesInfo().lines;if(r.length%2==0)return!1;if(""===r[Math.floor(r.length/2)].text.trim())return!1;return!0}}},372082:(e,t,i)=>{"use strict";i.d(t,{ArcWedgeRenderer:()=>a});var n,r=i(184114),s=i(589637),o=i(767313);!function(e){e[e.HitTestTolerance=4]="HitTestTolerance"}(n||(n={}));class a extends o.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null,this._hitTest=new r.HitTestResult(r.HitTarget.MovePoint),this._backHitTest=new r.HitTestResult(r.HitTarget.MovePointBackground)}setData(e){
this._data=e}setHitTest(e){this._hitTest=e}hitTest(e){if(null===this._data)return null;const t=e.subtract(this._data.center),i=t.length();if(Math.abs(i-this._data.radius)<=4){const t=e.subtract(this._data.p1).length(),i=e.subtract(this._data.p2).length();if(Math.max(t,i)<=this._data.p1.subtract(this._data.p2).length())return this._hitTest}if(this._data.fillBackground&&i<=this._data.radius){const e=this._data.p1.subtract(this._data.center).normalized(),i=this._data.p2.subtract(this._data.center).normalized(),n=t.normalized(),r=e.dotProduct(i),s=n.dotProduct(e),o=n.dotProduct(i);if(s>=r&&o>=r)return this._backHitTest}return null}_drawImpl(e){if(null===this._data)return;const t=e.context;if(t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,t.beginPath(),t.arc(this._data.center.x,this._data.center.y,this._data.radius,this._data.angle1,this._data.angle2),t.stroke(),this._data.fillBackground){if(t.arc(this._data.center.x,this._data.center.y,this._data.prevRadius,this._data.angle2,this._data.angle1,!0),this._data.gradient){const e=t.createRadialGradient(this._data.center.x,this._data.center.y,this._data.prevRadius,this._data.center.x,this._data.center.y,this._data.radius);e.addColorStop(0,(0,s.generateColor)(this._data.color1,this._data.transparency)),e.addColorStop(1,(0,s.generateColor)(this._data.color2,this._data.transparency)),t.fillStyle=e}else t.fillStyle=(0,s.generateColor)(this._data.color,this._data.transparency,!0);t.fill()}}}},559447:(e,t,i)=>{"use strict";i.d(t,{cubicBezierHitTest:()=>l,extendQuadroBezier:()=>d,quadroBezierHitTest:()=>o});var n,r=i(204652),s=i(822960);function o(e,t,i,n,s){const o=i.subtract(e).length()+i.subtract(t).length(),a=Math.max(3/o,.02);let l;for(let o=0;;o+=a){o>1&&(o=1);const a=e.scaled((1-o)*(1-o)),d=i.scaled(2*o*(1-o)),h=t.scaled(o*o),c=a.add(d).add(h);if(void 0!==l){if((0,r.distanceToSegment)(c,l,n).distance<s)return!0}else if(c.subtract(n).length()<s)return!0;if(l=c,1===o)break}return!1}function a(e,t,i,n,r){r=(0,s.clamp)(r,0,1);const o=e.scaled((1-r)*(1-r)*(1-r)),a=t.scaled(3*(1-r)*(1-r)*r),l=i.scaled(3*(1-r)*r*r),d=n.scaled(r*r*r);return o.add(a).add(l).add(d)}function l(e,t,i,n,s,o){const l=t.subtract(e).length()+i.subtract(t).length()+n.subtract(i).length(),d=Math.max(3/l,.02);let h;for(let l=0;;l+=d){const d=a(e,t,i,n,l);if(void 0!==h){if((0,r.distanceToSegment)(d,h,s).distance<o)return!0}else if(d.subtract(s).length()<o)return!0;if(h=d,l>=1)break}return!1}function d(e,t,i,n,r){const s=i.subtract(e).length()+i.subtract(t).length();if(!s)return[];const o=function(e,t,i,n,r){const s=[],o=h(e.y,t.y,i.y,0).concat(h(e.y,t.y,i.y,r));for(let r=0;r<o.length;r++){const a=c(e.x,t.x,i.x,o[r]);a>=0&&a<=n&&s.push(o[r])}const a=h(e.x,t.x,i.x,0).concat(h(e.x,t.x,i.x,n));for(let n=0;n<a.length;n++){const o=c(e.y,t.y,i.y,a[n]);o>=0&&o<=r&&s.push(a[n])}return s}(e,t,i,n,r).filter((e=>e>1)).sort(((e,t)=>e-t));t.x>=0&&t.x<=n&&t.y>=0&&t.y<=r&&o.unshift(1);const a=3/s,l=[];for(let n=0;n<o.length-1;n+=2){let r=a,s=o[n],d=o[n+1]+r;const h=[];for(;s<=d;){
const n=e.scaled((1-s)*(1-s)),o=i.scaled(2*s*(1-s)),a=t.scaled(s*s),l=n.add(o).add(a);if(h.length>0){h[h.length-1].subtract(l).length()<2&&(d+=r,r*=2)}h.push(l),s+=r}h.length>0&&l.push(h)}return l}function h(e,t,i,n){const r=[],s=e-2*i+t,o=2*i-2*e,a=e-n;if(Math.abs(s)>1e-8){const e=o*o-4*s*a;e>=0&&(r.push((-o+Math.sqrt(e))/(2*s)),r.push((-o-Math.sqrt(e))/(2*s)))}else r.push(-a/o);return r}function c(e,t,i,n){return(1-n)*(1-n)*e+2*(1-n)*n*i+n*n*t}!function(e){e[e.MaxHitTestSegments=50]="MaxHitTestSegments"}(n||(n={}))},625790:(e,t,i)=>{"use strict";i.d(t,{ChannelRenderer:()=>c});var n=i(650151),r=i(86441),s=i(934026),o=i(204652),a=i(184114),l=i(898646),d=i(589637),h=i(767313);class c extends h.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e,t){if(null===this._data||!this._data.hittestOnBackground)return null;const i=this._visiblePolygon(t.mediaSize);return null!==i&&(0,s.pointInPolygon)(e,i)?new a.HitTestResult(a.HitTarget.MovePointBackground):null}_drawImpl(e){if(null===this._data)return;const t=e.context,i=this._visiblePolygon(e.mediaSize);if(null!==i){t.beginPath(),t.moveTo(i[0].x,i[0].y);for(let e=1;e<i.length;e++)t.lineTo(i[e].x,i[e].y);t.fillStyle=(0,d.generateColor)(this._data.color,this._data.transparency,!0),t.fill()}}_visiblePolygon(e){const t=(0,n.ensureNotNull)(this._data),i=t.p1,s=t.p2,a=t.p3,d=t.p4;if((0,r.equalPoints)(i,s)||(0,r.equalPoints)(a,d)||(0,o.distanceToLine)(i,s,a).distance<1e-6&&(0,o.distanceToLine)(i,s,d).distance<1e-6)return null;if(e.width<=0||e.height<=0)return null;let h=[new r.Point(0,0),new r.Point(e.width,0),new r.Point(e.width,e.height),new r.Point(0,e.height)];return h=(0,l.clipPolygonByEdge)(h,i,s,[d,a]),h=(0,l.clipPolygonByEdge)(h,d,a,[i,s]),(0,r.equalPoints)(a,i)||t.extendLeft||(h=(0,l.clipPolygonByEdge)(h,a,i,[s,d])),h}}},59918:(e,t,i)=>{"use strict";i.d(t,{DisjointChannelRenderer:()=>p});var n=i(650151),r=i(86441),s=i(934026),o=i(204652),a=i(5531),l=i(936879),d=i(184114),h=i(960491),c=i(898646),u=i(589637),_=i(767313);class p{constructor(){this._parallelChannelRenderer=new h.ParallelChannelRenderer,this._disjointChannelIntersectionRenderer=new g,this._selectedRenderer=this._disjointChannelIntersectionRenderer}setData(e){if(e.points.length<4)return;const[t,i,n,s]=e.points;if((0,r.equalPoints)(t,i)||(0,r.equalPoints)(n,s)||(0,o.distanceToLine)(t,i,n).distance<1e-6&&(0,o.distanceToLine)(t,i,s).distance<1e-6)this._selectedRenderer=null;else{null!==(0,a.intersectLines)((0,r.lineThroughPoints)(t,i),(0,r.lineThroughPoints)(n,s))?(this._disjointChannelIntersectionRenderer.setData(e),this._selectedRenderer=this._disjointChannelIntersectionRenderer):(this._parallelChannelRenderer.setData({line1:{color:"rgba(0,0,0,0)",lineStyle:l.LINESTYLE_SOLID,lineWidth:0,points:[t,i]},line2:{color:"rgba(0,0,0,0)",lineStyle:l.LINESTYLE_SOLID,lineWidth:0,points:[s,n]},extendLeft:e.extendleft,extendRight:e.extendright,skipLines:!0,fillBackground:!0,backColor:(0,u.generateColor)(e.backcolor,e.transparency),
hittestOnBackground:e.hittestOnBackground}),this._selectedRenderer=this._parallelChannelRenderer)}}hitTest(e,t){return null!==this._selectedRenderer?this._selectedRenderer.hitTest(e,t):null}draw(e,t){null!==this._selectedRenderer&&this._selectedRenderer.draw(e,t)}}class g extends _.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e,t){if(null===this._data||!this._data.hittestOnBackground)return null;for(const i of this._visiblePolygons(t.mediaSize))if((0,s.pointInPolygon)(e,i))return new d.HitTestResult(d.HitTarget.MovePointBackground);return null}_drawImpl(e){if(null===this._data||this._data.points.length<4)return;const t=e.context;t.fillStyle=(0,u.generateColor)(this._data.backcolor,this._data.transparency);for(const i of this._visiblePolygons(e.mediaSize)){t.beginPath(),t.moveTo(i[0].x,i[0].y);for(let e=1;e<i.length;e++)t.lineTo(i[e].x,i[e].y);t.fill()}}_visiblePolygons(e){const t=(0,n.ensureNotNull)(this._data),[i,s,o,l]=t.points;if(e.width<=0||e.height<=0)return[];const d=(0,a.intersectLines)((0,r.lineThroughPoints)(i,s),(0,r.lineThroughPoints)(o,l));if(null===d)return[];const h=[new r.Point(0,0),new r.Point(e.width,0),new r.Point(e.width,e.height),new r.Point(0,e.height)],u=[];{let e=h;const n=i.subtract(s).add(d),r=l.subtract(o).add(d);e=(0,c.clipPolygonByEdge)(e,d,n,[r,r]),e=x(e,t),e=(0,c.clipPolygonByEdge)(e,r,d,[n,n]),null!==e&&u.push(e)}{let e=h;const n=s.subtract(i).add(d),r=o.subtract(l).add(d);e=(0,c.clipPolygonByEdge)(e,d,n,[r,r]),e=x(e,t),e=(0,c.clipPolygonByEdge)(e,r,d,[n,n]),null!==e&&u.push(e)}return u}}function f(e,t,i){return null!==e?(0,a.intersectPolygonAndHalfplane)(e,(0,r.halfplaneThroughPoint)((n=t,(0,r.line)(1,0,-n)),new r.Point(i,0))):null;var n}function x(e,t){const[i,n]=t.points;return t.extendleft||(e=f(e,i.x,n.x)),t.extendright||(e=f(e,n.x,i.x)),e}},338933:(e,t,i)=>{"use strict";i.d(t,{EllipseRendererSimple:()=>h});var n,r=i(184114),s=i(822960),o=i(86441),a=i(589637),l=i(767313),d=i(898646);!function(e){e[e.HitTestTolerance=3]="HitTestTolerance"}(n||(n={}));class h extends l.MediaCoordinatesPaneRenderer{constructor(e,t,i){super(),this._data=e,this._hitTest=t||new r.HitTestResult(r.HitTarget.MovePoint),this._backgroundHitTest=i||new r.HitTestResult(r.HitTarget.MovePointBackground)}hitTest(e){if(this._data.points.length<2)return null;const t=this._data.points[0],i=this._data.points[1],n=.5*Math.abs(t.x-i.x),r=Math.abs(t.x-i.x),a=Math.abs(t.y-i.y),l=t.add(i).scaled(.5);let d=e.subtract(l);if(r<1||a<1)return null;const h=(i.y-t.y)/(i.x-t.x);d=new o.Point(d.x,d.y/h);let c=d.x*d.x+d.y*d.y-n*n;return c=(0,s.sign)(c)*Math.sqrt(Math.abs(c/n)),Math.abs(c)<3?this._hitTest:this._data.fillBackground&&!this._data.noHitTestOnBackground&&c<3?this._backgroundHitTest:null}_drawImpl(e){const t=e.context;t.lineCap="butt",t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,void 0!==this._data.linestyle&&(0,d.setLineStyle)(t,this._data.linestyle)
;const i=this._data.points[0],n=this._data.points[1],r=Math.abs(i.x-n.x),s=Math.abs(i.y-n.y),o=i.add(n).scaled(.5);if(r<1||s<1)return;let l=0;if(this._data.wholePoints){const e=this._data.wholePoints[0],t=this._data.wholePoints[1];l=Math.abs(e.x-t.x)}t.save(),t.translate(o.x,o.y),t.scale(1,s/r),t.beginPath(),t.arc(0,0,r/2,0,2*Math.PI,!1),t.restore(),t.stroke(),this._data.fillBackground&&(this._data.wholePoints&&(t.translate(o.x,o.y),t.scale(1,s/r),t.arc(0,0,l/2,0,2*Math.PI,!0)),t.fillStyle=(0,a.generateColor)(this._data.backcolor,this._data.transparency,!0),t.fill())}}},773348:(e,t,i)=>{"use strict";i.d(t,{intersectLineWithViewport:()=>o});var n=i(86441);function r(e,t,i){return e>=t&&e<=i?e:null}function s(e,t,i,n){return Math.sign(e.x-t.x)===Math.sign(i.x-n.x)&&Math.sign(e.y-t.y)===Math.sign(i.y-n.y)}function o(e,t,i,o,a,l,d){const h=e.x>=0&&e.x<=a&&e.y>=0&&e.y<=l,c=t.x>=0&&t.x<=a&&t.y>=0&&t.y<=l;if(h&&c&&!i&&!o)return[e,t];if(e.x<0&&t.x<0&&(e.x<t.x?!o:!i)||e.x>a&&t.x>a&&(e.x<t.x?!i:!o)||e.y<0&&t.y<0&&(e.y<t.y?!o:!i)||e.y>l&&t.y>l&&(e.y<t.y?!i:!o))return null;const u=[];if(e.x===t.x){if(e.x<0||e.x>a)return null;e.y<t.y?u.push(new n.Point(e.x,0===d?0:e.y<0?e.y%d:-(d-e.y%d)),new n.Point(t.x,l)):u.push(new n.Point(e.x,0===d?l:e.y>l?l+(e.y-l)%d:l+(d-(l-e.y)%d)),new n.Point(t.x,0))}else if(e.y===t.y){if(e.y<0||e.y>l)return null;e.x<t.x?u.push(new n.Point(0===d?0:e.x<0?e.x%d:-(d-e.x%d),e.y),new n.Point(a,t.y)):u.push(new n.Point(0===d?a:e.x>a?a+(e.x-a)%d:a+(d-(a-e.x)%d),e.y),new n.Point(0,t.y))}else{const s=(t.y-e.y)/(t.x-e.x),o=e.y-s*e.x;let h=0,c=0;const _=r(o,0,l);if(null!==_)if(d>0&&(e.x<=0||i&&e.x<t.x)){const t=e.x<=0?Math.sqrt(Math.pow(0-e.x,2)+Math.pow(_-e.y,2))%d:d-Math.sqrt(Math.pow(0-e.x,2)+Math.pow(_-e.y,2))%d;h=Math.cos(Math.atan(s))*t,c=s*h,u.push(new n.Point(-h,_-c))}else u.push(new n.Point(0,_));const p=r(s*a+o,0,l);if(null!==p)if(d>0&&(e.x>=a||i&&e.x>t.x)){const t=e.x>=a?Math.sqrt(Math.pow(e.x-a,2)+Math.pow(e.y-p,2))%d:d-Math.sqrt(Math.pow(e.x-a,2)+Math.pow(e.y-p,2))%d;h=Math.cos(Math.atan(s))*t,c=s*h,u.push(new n.Point(a+h,p+c))}else u.push(new n.Point(a,p));const g=r(-o/s,0,a);if(null!==g&&(0!==g||0!==_))if(d>0&&(e.y<=0||i&&e.y<t.y)){const t=e.y<=0?Math.sqrt(Math.pow(e.x-g,2)+Math.pow(e.y-0,2))%d:d-Math.sqrt(Math.pow(e.x-g,2)+Math.pow(e.y-0,2))%d;h=Math.cos(Math.atan(s))*t,c=s*h,u.push(new n.Point(g-Math.sign(s)*h,-Math.sign(s)*c))}else u.push(new n.Point(g,0));const f=r((l-o)/s,0,a);if(null!==f&&(0!==f||p!==l))if(d>0&&(e.y>=l||i&&e.y>t.y)){const t=e.y>=l?Math.sqrt(Math.pow(e.x-f,2)+Math.pow(e.y-l,2))%d:d-Math.sqrt(Math.pow(e.x-f,2)+Math.pow(e.y-l,2))%d;h=Math.cos(Math.atan(s))*t,c=s*h,u.push(new n.Point(f+Math.sign(s)*h,l+Math.sign(s)*c))}else u.push(new n.Point(f,l))}if(u.length<1)return null;if(u.length<2&&u.push(u[0]),!i&&h){return[e,s(u[0],u[1],e,t)?u[1]:u[0]]}if(!o&&c){return[s(u[0],u[1],e,t)?u[0]:u[1],t]}return s(u[0],u[1],e,t)?[u[0],u[1]]:[u[1],u[0]]}},960491:(e,t,i)=>{"use strict";i.d(t,{ParallelChannelRenderer:()=>x});const n=function(e,t){for(var i,n=-1,r=e.length;++n<r;){var s=t(e[n])
;void 0!==s&&(i=void 0===i?s:i+s)}return i};var r=i(876402);const s=function(e){return e&&e.length?n(e,r.default):0};var o=i(934026),a=i(86441),l=i(204652),d=i(5531),h=i(936879),c=i(184114),u=i(161656),_=i(898646),p=i(773348),g=i(652283),f=i(731037);class x extends f.BitmapCoordinatesPaneRenderer{constructor(e,t){super(),this._data=null,this._backgroundPolygon=null,this._clippedLines=new Map,this._hittestResult=e||new c.HitTestResult(c.HitTarget.MovePoint),this._backHittestResult=t||new c.HitTestResult(c.HitTarget.MovePointBackground)}setData(e){this._data=e,this._backgroundPolygon=null,this._clippedLines.clear()}hitTest(e,t){if(null===this._data)return null;const{line1:i,line2:n,middleLine:r}=this._data,s=t.mediaSize,a=(0,u.interactionTolerance)().line;for(const t of[i,n,r]){if(!t)continue;const i=this._getClippedLine(t,this._data,s);if(i){if((0,l.distanceToSegment)(i.points[0],i.points[1],e).distance<=a)return this._hittestResult}}if(this._data.hittestOnBackground&&this._data.fillBackground){const t=this._getBackgroundPolygon(this._data,s);if(t.length>0&&(0,o.pointInPolygon)(e,t))return this._backHittestResult}return null}_drawImpl(e){if(null===this._data)return;const{line1:t,line2:i,middleLine:n,skipLines:r,skipTopLine:s,fillBackground:o,backColor:a}=this._data,l=e.context;l.lineCap="round";const d=this._data;if(o&&i){const t=this._getBackgroundPolygon(this._data,e.mediaSize);if(t.length>0){l.beginPath();{const i=t[0].x*e.horizontalPixelRatio,n=t[0].y*e.verticalPixelRatio;l.moveTo(i,n)}for(let i=1;i<t.length;i++){const n=t[i].x*e.horizontalPixelRatio,r=t[i].y*e.verticalPixelRatio;l.lineTo(n,r)}l.fillStyle=a,l.fill()}}void 0!==d.excludeBoundaries&&(l.save(),(0,g.addExclusionArea)(l,e,d.excludeBoundaries)),r||this._drawLine(e,t,this._data),r||s||!i||this._drawLine(e,i,this._data),n&&!this._data.skipLines&&this._drawLine(e,n,this._data),void 0!==d.excludeBoundaries&&l.restore()}_drawLine(e,t,i){const n=this._getClippedLine(t,i,e.mediaSize);if(!n)return;const r=e.context;r.strokeStyle=n.color,r.lineWidth=r.lineWidth=Math.max(1,Math.floor(n.lineWidth*e.horizontalPixelRatio)),r.lineCap=n.lineStyle===h.LINESTYLE_SOLID?"round":"butt",(0,_.setLineStyle)(r,n.lineStyle),r.strokeStyle=n.color;const[s,o]=n.points,a=s.x*e.horizontalPixelRatio,l=s.y*e.verticalPixelRatio,d=o.x*e.horizontalPixelRatio,c=o.y*e.verticalPixelRatio;(0,_.drawPixelPerfectLine)(r,a,l,d,c)}_getClippedLine(e,t,i){let n=this._clippedLines.get(e);if(void 0===n){const{lineWidth:r,lineStyle:o,points:[a,l]}=e;let d=!1,h=!1;a.x!==l.x&&({extendLeft:d,extendRight:h}=t);const c=(0,p.intersectLineWithViewport)(a,l,d,h,i.width,i.height,s((0,_.computeDashPattern)(r,o)));n=null==c?null:{...e,points:c},this._clippedLines.set(e,n)}return n}_getBackgroundPolygon(e,t){return this._backgroundPolygon||(this._backgroundPolygon=this._getBackgroundPolygonImpl(e,t)??[]),this._backgroundPolygon}_getBackgroundPolygonImpl(e,t){if(void 0===e.line2)return null;const[i,n]=e.line1.points,[r,s]=e.line2.points;if((0,a.equalPoints)(i,n)||(0,a.equalPoints)(r,s)||(0,
l.distanceToLine)(i,n,r).distance<1e-6||(0,l.distanceToLine)(i,n,s).distance<1e-6)return null;if(t.width<=0||t.height<=0)return null;let o=[new a.Point(0,0),new a.Point(t.width,0),new a.Point(t.width,t.height),new a.Point(0,t.height)];return o=v(o,i,n,s),e.extendRight||(o=v(o,n,s,r)),o=v(o,s,r,i),e.extendLeft||(o=v(o,r,i,n)),o}}function v(e,t,i,n){return null!==e?(0,d.intersectPolygonAndHalfplane)(e,(0,a.halfplaneThroughPoint)((0,a.lineThroughPoints)(t,i),n)):null}},306099:(e,t,i)=>{"use strict";i.d(t,{PolygonRenderer:()=>p});var n=i(934026),r=i(204652),s=i(659011),o=i(586982),a=i(184114),l=i(589637),d=i(767313),h=i(161656),c=i(898646),u=i(936879),_=i(305141);class p extends d.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=null,this._backHittest=new a.HitTestResult(a.HitTarget.MovePointBackground),this._points=[],this._hittest=e??new a.HitTestResult(a.HitTarget.MovePoint)}setData(e){this._data=e,this._points=e.points}hitTest(e){if(null===this._data||this._data.disableInteractions)return null;const t=Math.max((0,h.interactionTolerance)().line,Math.ceil(this._data.linewidth/2)),i=this._points.length;if(1===i){return(0,n.pointInCircle)(e,this._points[0],t)?this._hittest:null}for(let n=1;n<i;n++){const i=this._points[n-1],s=this._points[n];if((0,r.distanceToSegment)(i,s,e).distance<=t)return this._hittest}if(this._data.filled&&this._data.fillBackground&&i>0){const n=this._points[0],s=this._points[i-1];if((0,r.distanceToSegment)(n,s,e).distance<=t)return this._hittest}return this._data.filled&&this._data.fillBackground&&(0,n.pointInPolygon)(e,this._data.points)?this._backHittest:null}_drawImpl(e){const t=e.context,i=this._points.length;if(null===this._data||0===i)return;const n=t.globalAlpha;try{if(void 0!==this._data.globalAlpha&&(t.globalAlpha=this._data.globalAlpha),1===i)return void this._drawPoint(t,this._points[0],this._data.linewidth/2,this._data.color);t.beginPath();const e=this._data.linestyle===u.LINESTYLE_SOLID?"round":"butt",n=this._data.linecap??e;t.lineCap=n,t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,t.lineJoin=this._data.linejoin??"round",(0,c.setLineStyle)(t,this._data.linestyle);const r=this._points[0];t.moveTo(r.x,r.y);for(const e of this._points)t.lineTo(e.x,e.y);if(this._data.filled&&this._data.fillBackground&&(t.fillStyle=(0,l.generateColor)(this._data.backcolor,this._data.transparency),t.fill()),this._data.filled&&!this._data.skipClosePath&&t.closePath(),i>1){if(this._data.leftend===o.LineEnd.Arrow){const e=this._correctArrowPoints(this._points[1],this._points[0],t.lineWidth,n);(0,s.drawArrow)(e[0],e[1],t,t.lineWidth,_.dpr1PixelRatioInfo)}if(this._data.rightend===o.LineEnd.Arrow){const e=this._correctArrowPoints(this._points[i-2],this._points[i-1],t.lineWidth,n);(0,s.drawArrow)(e[0],e[1],t,t.lineWidth,_.dpr1PixelRatioInfo)}}this._data.linewidth>0&&t.stroke()}finally{t.globalAlpha=n}}_drawPoint(e,t,i,n){0!==i&&(e.beginPath(),e.fillStyle=n,e.arc(t.x,t.y,i,0,2*Math.PI,!0),e.fill(),e.closePath())}_correctArrowPoints(e,t,i,n){const r=t.subtract(e),s=r.length()
;if("butt"===n||s<1)return[e,t];const o=s+i/2;return[e,r.scaled(o/s).add(e)]}}},863192:(e,t,i)=>{"use strict";i.d(t,{PlusTextRendererDecorator:()=>r});var n=i(454304);class r{geometry(e){const t=(0,n.fontSize)(e);return{decoratorAndTextMargin:t/3,width:Math.round(.8*t),ignoreRtl:!1}}draw(e,t,i,n){const{horizontalPixelRatio:r,verticalPixelRatio:s}=t,o=Math.max(1,Math.round(r*n.decoratorWidth/8)),a=o%2/2,l=Math.round((n.textTop+n.textBottom)/2*s)+a,d=Math.round((n.decoratorLeft+n.decoratorWidth/2)*r)+a,h=Math.round(n.decoratorWidth*r);e.strokeStyle=i.color,e.lineWidth=o;let c=h/2;d%2/2!=c%2/2&&(c+=.5),e.beginPath(),e.moveTo(d-c,l),e.lineTo(d+c,l),e.moveTo(d,l-c),e.lineTo(d,l+c),e.stroke()}static instance(){return this._instance=this._instance??new r,this._instance}}r._instance=null},402359:(e,t,i)=>{"use strict";i.d(t,{TriangleRenderer:()=>_});var n=i(86441),r=i(204652),s=i(934026),o=i(767313),a=i(184114),l=i(589637),d=i(161656),h=i(898646),c=i(936879),u=i(773348);class _ extends o.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data||this._data.points.length<2)return null;const[t,i]=this._data.points;let n=(0,r.distanceToSegment)(t,i,e);const o=(0,d.interactionTolerance)().line;if(n.distance<=o)return new a.HitTestResult(a.HitTarget.MovePoint);if(3!==this._data.points.length)return null;const l=this._data.points[2];return n=(0,r.distanceToSegment)(i,l,e),n.distance<=o?new a.HitTestResult(a.HitTarget.MovePoint):(n=(0,r.distanceToSegment)(l,t,e),n.distance<=o?new a.HitTestResult(a.HitTarget.MovePoint):this._data.fillBackground&&(0,s.pointInTriangle)(e,t,i,l)?new a.HitTestResult(a.HitTarget.MovePointBackground):null)}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=e.context,i=(this._data.linestyle??c.LINESTYLE_SOLID)===c.LINESTYLE_SOLID?"round":"butt";t.lineCap=i,t.lineJoin="round",t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,void 0!==this._data.linestyle&&(0,h.setLineStyle)(t,this._data.linestyle);const[r,s,o=s]=this._data.points,{mediaSize:a}=e;if(this._data.fillBackground&&Math.abs((r.x-o.x)*(s.y-o.y)-(s.x-o.x)*(r.y-o.y))>1e-10){let e=[new n.Point(0,0),new n.Point(a.width,0),new n.Point(a.width,a.height),new n.Point(0,a.height)];if(e=(0,h.clipPolygonByEdge)(e,r,s,[s,o]),e=(0,h.clipPolygonByEdge)(e,s,o,[o,r]),e=(0,h.clipPolygonByEdge)(e,o,r,[r,s]),e&&e.length>1){t.save(),t.beginPath(),t.moveTo(e[0].x,e[0].y);for(let i=1;i<e.length;i++)t.lineTo(e[i].x,e[i].y);t.fillStyle=(0,l.generateColor)(this._data.backcolor,this._data.transparency),t.fill(),t.restore()}}const d=[],_=t.getLineDash().reduce(((e,t)=>e+t),0);[[r,s],[s,o],[o,r]].forEach((([e,t])=>{const i=(0,u.intersectLineWithViewport)(e,t,!1,!1,a.width,a.height,_);i&&d.push(i)})),d.length&&(t.beginPath(),d.forEach((([e,i])=>{t.moveTo(e.x,e.y),t.lineTo(i.x,i.y)})),t.stroke())}}},171721:(e,t,i)=>{"use strict";i.d(t,{factoryDefaultsForCurrentTheme:()=>l});var n=i(916738),r=i(154834),s=i(650151),o=i(702054),a=i(111982);function l(e,t){
const i=o.watchedTheme.value()??a.StdTheme.Light,l=(0,r.default)(e);return(0,n.default)(l,(0,s.ensureDefined)(t.get(i))),l}},763929:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M8.55 2.78 2.7 15H15v-1h-4.01a6.2 6.2 0 0 0-1.36-3.95 7.94 7.94 0 0 0-2.57-1.83l2.4-5-.91-.44ZM6.63 9.12 4.29 14H10a5.18 5.18 0 0 0-1.12-3.3 6.93 6.93 0 0 0-2.24-1.58Z"/></svg>'},901195:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M2 3v2H1v9h1v2h1v-2h1V5H3V3H2Zm6.2 4.5-.35.35L6.71 9h4.58l-1.14-1.15-.36-.35.71-.7.35.35 2 2 .36.35-.36.35-2 2-.35.36-.7-.71.35-.35L11.29 10H6.71l1.14 1.15.36.35-.71.7-.35-.35-2-2-.36-.35.36-.35 2-2 .35-.36.7.71ZM3 6H2v7h1V6Zm12-2.5V3h1v2h1v9h-1v2h-1v-2h-1V5h1V3.5ZM15 6h1v7h-1V6Z"/></svg>'},342057:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3 2h11v1H3V2Zm5.5 1.8.35.35 2 2 .36.35-.71.7-.35-.35L9 5.71v6.58l1.15-1.14.35-.36.7.71-.35.35-2 2-.35.36-.35-.36-2-2-.36-.35.71-.7.35.35L8 12.29V5.71L6.85 6.85l-.35.36-.7-.71.35-.35 2-2 .35-.36ZM3.5 16H3v-1h11v1H3.5Z"/></svg>'},999620:(e,t,i)=>{"use strict";e.exports=i.p+"prediction-clock-white.c4675d37769f1df4c9ec.png"},888249:(e,t,i)=>{"use strict";e.exports=i.p+"prediction-failure-white.a838a6689f951970e715.png"},614012:(e,t,i)=>{"use strict";e.exports=i.p+"prediction-success-white.2fb9966b4c0f3529a2ea.png"}}]);