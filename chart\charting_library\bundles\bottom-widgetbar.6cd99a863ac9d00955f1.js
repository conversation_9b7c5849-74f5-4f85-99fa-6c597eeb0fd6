(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4193],{470676:t=>{t.exports={}},492575:t=>{t.exports={}},65160:(t,e,i)=>{"use strict";function s(t){const{paddingTop:e,paddingBottom:i}=window.getComputedStyle(t);return[e,i].reduce(((t,e)=>t-Number((e||"").replace("px",""))),t.clientHeight)}function n(t,e=!1){const i=getComputedStyle(t),s=[i.height];return"border-box"!==i.boxSizing&&s.push(i.paddingTop,i.paddingBottom,i.borderTopWidth,i.borderBottomWidth),e&&s.push(i.marginTop,i.marginBottom),s.reduce(((t,e)=>t+(parseFloat(e)||0)),0)}function a(t,e=!1){const i=getComputedStyle(t),s=[i.width];return"border-box"!==i.boxSizing&&s.push(i.paddingLeft,i.paddingRight,i.borderLeftWidth,i.borderRightWidth),e&&s.push(i.marginLeft,i.marginRight),s.reduce(((t,e)=>t+(parseFloat(e)||0)),0)}i.d(e,{contentHeight:()=>s,outerHeight:()=>n,outerWidth:()=>a})},496818:(t,e,i)=>{"use strict";i.d(e,{Draggable:()=>a,PointerBackend:()=>r});var s=i(650151),n=i(821205);i(492575);class a{constructor(t){this._helper=null,this._handleDragStart=t=>{if(null!==this._helper)return;const e=this._source;e.classList.add("ui-draggable-dragging");const[i,s]=[(0,n.outerWidth)(e),(0,n.outerHeight)(e)];this._helper={startTop:parseFloat(e.style.top)||0,startLeft:parseFloat(e.style.left)||0,nextTop:null,nextLeft:null,raf:null,size:[i,s],containment:this._containment instanceof HTMLElement?[parseInt(getComputedStyle(this._containment).borderLeftWidth)+parseInt(getComputedStyle(this._containment).paddingLeft),parseInt(getComputedStyle(this._containment).borderTopWidth)+parseInt(getComputedStyle(this._containment).paddingTop),this._containment.offsetWidth-parseInt(getComputedStyle(this._containment).borderRightWidth)-parseInt(getComputedStyle(this._containment).paddingRight)-parseInt(getComputedStyle(e).marginLeft)-parseInt(getComputedStyle(e).marginRight)-i,this._containment.offsetHeight-parseInt(getComputedStyle(this._containment).borderBottomWidth)-parseInt(getComputedStyle(this._containment).paddingBottom)-parseInt(getComputedStyle(e).marginTop)-parseInt(getComputedStyle(e).marginBottom)-s]:"window"===this._containment?[window.scrollX,window.scrollY,window.scrollX+document.documentElement.offsetWidth-i,window.scrollY+document.documentElement.offsetHeight-s]:null},this._start?.()},this._handleDragMove=t=>{if(null===this._helper)return;const{current:e,initial:i}=t.detail,s=this._source,n=this._helper.nextTop,a=this._helper.nextLeft,r="y"===this._axis||!1===this._axis||0!==e.movementY;if(r){const t=this._helper.startTop;isFinite(t)&&(this._helper.nextTop=e.clientY-i.clientY+t)}const o="x"===this._axis||!1===this._axis||0!==e.movementY;if(o){const t=this._helper.startLeft;isFinite(t)&&(this._helper.nextLeft=e.clientX-i.clientX+t)}if(null!==this._helper.containment){const[t,e,i,s]=this._helper.containment;r&&this._helper.nextTop&&(this._helper.nextTop=Math.min(this._helper.nextTop,s),this._helper.nextTop=Math.max(this._helper.nextTop,e)),o&&this._helper.nextLeft&&(this._helper.nextLeft=Math.min(this._helper.nextLeft,i),
this._helper.nextLeft=Math.max(this._helper.nextLeft,t))}null!==this._helper.raf||n===this._helper.nextTop&&a===this._helper.nextLeft||(this._helper.raf=requestAnimationFrame((()=>{null!==this._helper&&(null!==this._helper.nextTop&&(s.style.top=this._helper.nextTop+"px",this._helper.nextTop=null),null!==this._helper.nextLeft&&(s.style.left=this._helper.nextLeft+"px",this._helper.nextLeft=null),this._helper.raf=null)}))),this._drag?.()},this._handleDragStop=t=>{if(null===this._helper)return;this._source.classList.remove("ui-draggable-dragging"),this._helper=null,this._stop?.()};const e=this._source=t.source;e.classList.add("ui-draggable");const i=this._handle=(t.handle?e.querySelector(t.handle):null)??e;i.classList.add("ui-draggable-handle"),this._start=t.start,this._stop=t.stop,this._drag=t.drag,this._backend=new r({handle:i,onDragStart:this._handleDragStart,onDragMove:this._handleDragMove,onDragStop:this._handleDragStop}),this._axis=t.axis??!1,this._containment=t.containment}destroy(){const t=this._source;t.classList.remove("ui-draggable"),t.classList.remove("ui-draggable-dragging");this._handle.classList.remove("ui-draggable-handle"),this._backend.destroy(),null!==this._helper&&(this._helper.raf&&cancelAnimationFrame(this._helper.raf),this._helper=null)}}class r{constructor(t){this._pointerStarted=!1,this._initial=null,this._handlePointerDown=t=>{if(null!==this._initial||0!==t.button)return;if(!(t.target instanceof Element&&this._handle.contains(t.target)))return;if(this._initial=t,!this._distance&&(this._pointerStart(),!this._pointerStarted))return;t.preventDefault();const e=this._getEventTarget();e.addEventListener("pointermove",this._handlePointerMove),e.addEventListener("pointerup",this._handlePointerUp),e.addEventListener("pointercancel",this._handlePointerUp),e.addEventListener("lostpointercapture",this._handleLostPointerCapture)},this._handleLostPointerCapture=t=>{this._getEventTarget()===t.target&&this._handlePointerUp(t)},this._handlePointerMove=t=>{if(null!==this._initial&&this._initial.pointerId===t.pointerId)if(this._pointerStarted)this._pointerDrag(t);else if(this._pointerDistanceMet(t)){if(this._pointerStart(),this._pointerStarted)return void this._pointerDrag(t);this._handlePointerUp(t)}},this._handlePointerUp=t=>{if(null===this._initial||this._initial.pointerId!==t.pointerId)return;t.preventDefault();const e=this._getEventTarget();e.removeEventListener("pointermove",this._handlePointerMove),e.removeEventListener("pointerup",this._handlePointerUp),e.removeEventListener("pointercancel",this._handlePointerUp),e.removeEventListener("lostpointercapture",this._handlePointerUp),this._pointerStarted&&(this._pointerStarted=!1,e.releasePointerCapture(this._initial.pointerId),this._dispatchEvent(this._createEvent("pointer-drag-stop",t))),this._initial=null};const e=this._handle=t.handle;this._onDragStart=t.onDragStart,this._onDragMove=t.onDragMove,this._onDragStop=t.onDragStop,this._distance=t.distance??0,this._rootElement=t.rootElement,e.style.touchAction="none",
e.addEventListener("pointerdown",this._handlePointerDown)}destroy(){const t=this._handle;t.style.touchAction="",t.removeEventListener("pointerdown",this._handlePointerDown),t.removeEventListener("pointermove",this._handlePointerMove),t.removeEventListener("pointerup",this._handlePointerUp),t.removeEventListener("pointercancel",this._handlePointerUp),t.removeEventListener("lostpointercapture",this._handlePointerUp),null!==this._initial&&(t.releasePointerCapture(this._initial.pointerId),this._initial=null),this._pointerStarted=!1}_pointerStart(){if(!this._initial)return;const t=this._getEventTarget();this._dispatchEvent(this._createEvent("pointer-drag-start",this._initial))?(this._pointerStarted=!0,t.setPointerCapture(this._initial.pointerId)):this._initial=null}_pointerDrag(t){t.preventDefault(),this._dispatchEvent(this._createEvent("pointer-drag-move",t))}_pointerDistanceMet(t){return!this._initial||!this._distance||Math.max(Math.abs(this._initial.clientX-t.clientX),Math.abs(this._initial.clientY-t.clientY))>=this._distance}_getEventTarget(){return this._rootElement??this._handle}_dispatchEvent(t){switch(t.type){case"pointer-drag-start":this._onDragStart(t);break;case"pointer-drag-move":this._onDragMove(t);break;case"pointer-drag-stop":this._onDragStop(t)}return!t.defaultPrevented}_createEvent(t,e){return(0,s.assert)(null!==this._initial),new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:{backend:this,initial:this._initial,current:e}})}}},821205:(t,e,i)=>{"use strict";i.d(e,{contentHeight:()=>n.contentHeight,html:()=>a,outerHeight:()=>n.outerHeight,outerWidth:()=>n.outerWidth,position:()=>o});var s=i(650151),n=i(65160);function a(t,e){return void 0===e||(null===e&&(t.innerHTML=""),"string"!=typeof e&&"number"!=typeof e||(t.innerHTML=String(e))),t}function r(t){if(!t.getClientRects().length)return{top:0,left:0};const e=t.getBoundingClientRect(),i=(0,s.ensureNotNull)(t.ownerDocument.defaultView);return{top:e.top+i.pageYOffset,left:e.left+i.pageXOffset}}function o(t){const e=getComputedStyle(t);let i,s={top:0,left:0};if("fixed"===e.position)i=t.getBoundingClientRect();else{i=r(t);const e=t.ownerDocument;let n=t.offsetParent||e.documentElement;for(;n&&(n===e.body||n===e.documentElement)&&"static"===getComputedStyle(n).position;)n=n.parentElement;n&&n!==t&&1===n.nodeType&&(s=r(n),s.top+=parseFloat(getComputedStyle(n).borderTopWidth),s.left+=parseFloat(getComputedStyle(n).borderLeftWidth))}return{top:i.top-s.top-parseFloat(e.marginTop),left:i.left-s.left-parseFloat(e.marginLeft)}}},233546:(t,e,i)=>{"use strict";i.r(e),i.d(e,{Bottomwidgetbar:()=>g});var s=i(650151),n=i(240534),a=i(870122),r=i(68176),o=i(739359),h=i(553155),l=i(821205),d=i(496818);i(470676);class g{constructor(t,e){this._draggable=null,this._inited=!1,this._widgetContents=new WeakMap,this._minHeight=30,this._paddingTop=o.footerWidgetHeight,this.open=()=>{"minimized"===this._mode.value()&&this._mode.setValue("normal")},this.close=()=>{this._mode.setValue("minimized")},this.toggleMaximize=()=>{switch(this._mode.value()){case"minimized":case"normal":
this._trackResizeClick("maximize","on"),this._mode.setValue("maximized");break;case"maximized":this._trackResizeClick("maximize","off"),this._mode.setValue("normal")}},this.toggleMinimize=()=>{switch(this._mode.value()){case"minimized":this._trackResizeClick("show"),this.open();break;case"normal":case"maximized":this._trackResizeClick("hide"),this._trackBacktestTabHide(),this.close()}},this._mode=new n.WatchedValue,this._isOverlap=new n.WatchedValue,this._isVisible=new n.WatchedValue,this._activeWidget=new n.WatchedValue,this._widgets={},this._widgetsProperties={},this._normalHeight=new n.WatchedValue,this._actualHeight=new n.WatchedValue,this._userSettings=e,this._options=t||{},this._config=t.config,this._enabledWidgets=(t&&t.widgets||[]).filter((t=>t&&Boolean(this._config[t]))),this._init(t)}activeWidget(){return this._activeWidget.readonly()}footerWidgetContainer(){return this._footerWidgetContainer}autoOpenBacktest(t){if("backtesting"!==this.activeWidget().value()){const e=setTimeout((()=>{this._annoyingBacktestAutoOpenEventMetadata=void 0}),r.reportAnnoyingBacktestAutoOpenTimeoutMs);this._annoyingBacktestAutoOpenEventMetadata={timeout:e,isPublic:t}}return this.toggleWidget("backtesting",!0)}toggleWidget(t,e,i){return new Promise((s=>{"boolean"!=typeof t&&"backtesting"===t||this._trackBacktestTabHide(),"boolean"==typeof t?t?this.open():this.close():this.isWidgetEnabled(t)&&(e?this.open():this.activeWidget().value()===t&&"minimized"!==this.mode().value()?this.close():i||this.open(),this._activeWidget.setValue(t)),s()}))}getWidgetByName(t){return this._widgets[t]||null}mode(){return this._mode.readonly()}setMode(t){this._mode.setValue(t)}isOverlap(){return this._isOverlap.readonly()}setNormalHeight(t){this._normalHeight.setValue(t)}destroy(){this._draggable&&(this._draggable.destroy(),this._draggable=null)}isWidgetEnabled(t){return this._enabledWidgets.includes(t)}activateTradingTab(){return this.toggleWidget("paper_trading",!0).then((()=>{const t=this.getWidgetByName("paper_trading");t&&t.activate&&t.activate()}))}activateScriptEditorTab(t,e){this.toggleWidget("scripteditor",!0).then((()=>{const i=this._config.scripteditor;i&&i.ctor.open(t,e)}))}activateReplayTradingTab(){this.toggleWidget("replay_trading",!0).then((()=>{this.getWidgetByName("replay_trading")?.activate?.()}))}turnOffMaximize(){"maximized"===this._mode.value()&&this.toggleMaximize()}enabledWidgets(){return this._enabledWidgets.map((t=>this._config[t]))}activeWidgetName(){return this._activeWidget.value()}isVisible(){return this._isVisible.readonly()}_init(t){if(!this._enabledWidgets.length||this._inited)return;this._createLayout(this._options.resizerBridge.container.value());const e=()=>{let t=0;switch(this._mode.value()){case"normal":t=this._normalHeight.value();break;case"maximized":t=this._resizerAvailHeight()}const e={min:Math.min(this._getPaddingBoxHeight(t),this._getPaddingBoxHeight(this._minHeight)),max:this._getPaddingBoxHeight(t)};this._options.resizerBridge.negotiateHeight(e)},i=()=>{const i=this._activeWidget.value()
;if("minimized"!==this._mode.value()){let e;if(this._container.classList.remove("js-hidden"),this._container.style.height=this._actualHeight.value()+"px",Array.from(this._container.querySelectorAll(".bottom-widgetbar-content"),(t=>{t.classList.add("js-hidden")})),Object.keys(this._widgets).forEach((t=>{if(t===i)return;const e=this._widgets[t];e&&e.hide&&e.hide()})),this._widgets[i]){const t=this._widgets[i];t&&this._widgetContents.has(t)&&(e=(0,s.ensureDefined)(this._widgetContents.get(t)),e.classList.remove("js-hidden"),t.show&&t.show())}else{e=document.createElement("div"),e.className="bottom-widgetbar-content "+i,this._container.append(e),this._initWidgetProperties(i);const s=new n.WatchedValue;this._activeWidget.subscribe((t=>{s.setValue(t===i)}),{callWithLast:!0});const a=()=>{this.toggleWidget(i,!0)},r=this._config[i];if(r){const n=r.ctor;this._widgets[i]=new n({container:e,bottomAreaContainer:this._container,height:this._actualHeight.readonly(),width:this._options.resizerBridge.width,bottomToolbarMode:this.mode(),visible:s.readonly(),properties:this._widgetsProperties[i],activate:a,close:()=>this.close(),chartWidgetCollection:t.chartWidgetCollection||null,originalStandalone:"screener"===i,backtestingStrategyDispatcher:this._options.backtestingStrategyDispatcher,studyMarket:this._options.studyMarket})}const o=this._widgets[i];o&&(this._widgetContents.set(o,e),o.disabled&&o.disabled.subscribe((t=>{this._loadingOverlay.classList.toggle("js-hidden",!t)})))}}else{const t=this._widgets[i];t&&this._widgetContents.has(t)&&this._widgetContents.get(t)?.classList.add("js-hidden")}this._normalHeight.unsubscribe(e),this._normalHeight.subscribe(e),this._mode.unsubscribe(e),this._mode.subscribe(e),this._options.resizerBridge.availHeight.unsubscribe(e),this._options.resizerBridge.availHeight.subscribe(e),e(),this._save()};this._mode.subscribe(i),this._activeWidget.subscribe(i),this._normalHeight.subscribe((()=>{this._save()})),this._mode.subscribe((()=>{this._save()})),this._options.resizerBridge.height.subscribe((()=>{const t=this._options.resizerBridge.height.value(),e=Math.max(this._getContentBoxHeight(t),1);this._container.style.height=e+"px",this._actualHeight.setValue(e)}),{callWithLast:!0});const{mode:a}=this._userSettings;this._mode.setValue(a),this._registerWidgets(),this._loadState(),this._subscribeCheckOverlap(),this._subscribeVisibility(),this._inited=!0}_getContentBoxHeight(t){return t-this._paddingTop}_getPaddingBoxHeight(t){return t+this._paddingTop}_subscribeCheckOverlap(){this._checkOverlap(),this._actualHeight.subscribe((()=>{this._checkOverlap()})),this._mode.subscribe((()=>{this._checkOverlap()})),this._options.resizerBridge.availHeight.subscribe((()=>{this._checkOverlap()})),this._options.topAreaResizer?.height.subscribe((()=>{this._checkOverlap()}))}_updateVisibility(){this._isVisible.setValue(this._options.resizerBridge.visible.value())}_subscribeVisibility(){this._updateVisibility(),this._options.resizerBridge.visible.subscribe((()=>{this._updateVisibility()}))}_createLayout(t){t.innerHTML="",
this._footerWidgetContainer=document.createElement("div"),this._footerWidgetContainer.style.height=o.footerWidgetHeight+"px",t.append(this._footerWidgetContainer),this._container=document.createElement("div"),this._container.id="bottom-area",t.append(this._container);const e=document.createElement("div");e.className="bottom-widgetbar-handle",this._container.append(e),e.addEventListener("contextmenu",(t=>{t.preventDefault()}));let i=null;this._draggable=new d.PointerBackend({handle:e,onDragStart:t=>{const e=this._mode.value(),s=this._normalHeight.value(),n="minimized"===this._mode.value()?0:(0,l.contentHeight)(this._container);i={startMode:e,lastNormalHeight:s,startHeight:n}},onDragMove:t=>{if(null===i)return;const{startHeight:e}=i,{initial:s,current:n}=t.detail;let a,r=e-(n.pageY-s.pageY);r<=0?(r=e,a=!0):a=!1,r=Math.max(r,this._minHeight),isFinite(r)&&("minimized"===this._mode.value()!==a?a?(this._mode.setValue("minimized"),this.setNormalHeight(Math.max(e,this._minHeight))):(this._mode.setValue("normal"),this.setNormalHeight(r)):this._normalHeight.value()!==r&&(r>=this._getContentBoxHeight(this._resizerAvailHeight())?(this._mode.setValue("maximized"),this.setNormalHeight(e)):(this._mode.setValue("normal"),this.setNormalHeight(r))))},onDragStop:()=>{if(null===i)return;const{lastNormalHeight:t,startMode:e}=i;i=null,t>0&&"normal"!==this._mode.value()&&"normal"!==e&&this.setNormalHeight(t),this._save()}}),this._loadingOverlay=document.createElement("div"),this._loadingOverlay.className="bottom-widgetbar-loading-overlay js-hidden",this._container.append(this._loadingOverlay)}_checkOverlap(){const t=this._getPaddingBoxHeight(this._actualHeight.value()),e=this._options.topAreaResizer?.height.value()??0,i=e+(e?4:0)+4,s=this._mode.value();this._isOverlap.setValue("maximized"===s||"normal"===s&&this._resizerAvailHeight()-t<=300+i)}_resizerAvailHeight(){return this._options.resizerBridge.availHeight.value()}_initWidgetProperties(t){const e="bottom"+t+"widget",i=new n.WatchedValue(a.getJSON(e,null));i.subscribe((t=>{t?a.setJSON(e,t):a.remove(e)})),this._widgetsProperties[t]=i}_loadState(){const t=this._userSettings;this._activeWidget.setValue(this.isWidgetEnabled(t.activeWidget)?t.activeWidget:this._enabledWidgets[0]),this.setNormalHeight(Math.max(t.height,this._minHeight))}_save(){this._inited&&(0,h.setUserSettings)({activeWidget:this._activeWidget.value(),height:this._normalHeight.value(),mode:this._mode.value()})}_registerWidgets(){this._enabledWidgets.forEach((t=>{const e=this._config[t];if(!e||"function"!=typeof e.onRegister)return;e.onRegister.call(null,{name:t,bottomToolbarMode:this._mode,chartWidgetCollection:this._options.chartWidgetCollection,activate:()=>{this.toggleWidget(t,!0)}})}))}_trackResizeClick(t,e){0}_trackBacktestTabHide(){0}}}}]);