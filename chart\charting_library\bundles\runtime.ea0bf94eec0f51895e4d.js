(()=>{"use strict";var e,a,c,d,f,b={},t={};function o(e){var a=t[e];if(void 0!==a)return a.exports;var c=t[e]={id:e,loaded:!1,exports:{}};return b[e].call(c.exports,c,c.exports,o),c.loaded=!0,c.exports}o.m=b,o.c=t,o._plural={ar:(e,a=6,c=(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11&&e%100<=99?4:5))=>null==e?0:+c,cs:(e,a=4,c=(1==e?0:e>=2&&e<=4?1:0!=e?2:3))=>null==e?0:+c,ru:(e,a=3,c=(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2))=>null==e?0:+c,ro:(e,a=3,c=(1==e?0:e%100>19||e%100==0&&0!=e?2:1))=>null==e?0:+c,pl:(e,a=3,c=(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2))=>null==e?0:+c,pt:(e,a=2,c=1!=e)=>null==e?0:+c,de:(e,a=2,c=1!=e)=>null==e?0:+c,en:(e,a=2,c=1!=e)=>null==e?0:+c,es:(e,a=2,c=1!=e)=>null==e?0:+c,sv:(e,a=2,c=1!=e)=>null==e?0:+c,it:(e,a=2,c=1!=e)=>null==e?0:+c,tr:(e,a=2,c=1!=e)=>null==e?0:+c,el:(e,a=2,c=1!=e)=>null==e?0:+c,fr:(e,a=2,c=e>1)=>null==e?0:+c,fa:(e,a=1,c=0)=>null==e?0:+c,ja:(e,a=1,c=0)=>null==e?0:+c,ko:(e,a=1,c=0)=>null==e?0:+c,th:(e,a=1,c=0)=>null==e?0:+c,vi:(e,a=1,c=0)=>null==e?0:+c,zh:(e,a=1,c=0)=>null==e?0:+c,he_IL:(e,a=4,c=(1==e?0:2==e?1:e>10&&e%10==0?2:3))=>null==e?0:+c,ca_ES:(e,a=2,c=1!=e)=>null==e?0:+c,nl_NL:(e,a=2,c=1!=e)=>null==e?0:+c,hu_HU:(e,a=2,c=1!=e)=>null==e?0:+c,id_ID:(e,a=1,c=0)=>null==e?0:+c,ms_MY:(e,a=1,c=0)=>null==e?0:+c,zh_TW:(e,a=1,c=0)=>null==e?0:+c},e=[],o.O=(a,c,d,f)=>{if(!c){var b=1/0;for(r=0;r<e.length;r++){for(var[c,d,f]=e[r],t=!0,l=0;l<c.length;l++)(!1&f||b>=f)&&Object.keys(o.O).every((e=>o.O[e](c[l])))?c.splice(l--,1):(t=!1,f<b&&(b=f));if(t){e.splice(r--,1);var n=d();void 0!==n&&(a=n)}}return a}f=f||0;for(var r=e.length;r>0&&e[r-1][2]>f;r--)e[r]=e[r-1];e[r]=[c,d,f]},o.n=e=>{var a=e&&e.__esModule?()=>e.default:()=>e;return o.d(a,{a}),a},c=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,o.t=function(e,d){if(1&d&&(e=this(e)),8&d)return e;if("object"==typeof e&&e){if(4&d&&e.__esModule)return e;if(16&d&&"function"==typeof e.then)return e}var f=Object.create(null);o.r(f);var b={};a=a||[null,c({}),c([]),c(c)];for(var t=2&d&&e;"object"==typeof t&&!~a.indexOf(t);t=c(t))Object.getOwnPropertyNames(t).forEach((a=>b[a]=()=>e[a]));return b.default=()=>e,o.d(f,b),f},o.d=(e,a)=>{for(var c in a)o.o(a,c)&&!o.o(e,c)&&Object.defineProperty(e,c,{enumerable:!0,get:a[c]})},o.f={},o.e=e=>Promise.all(Object.keys(o.f).reduce(((a,c)=>(o.f[c](e,a),a)),[])),
o.u=e=>2970===e?"__LANG__.2970.192d64e32a7edae012d7.js":1904===e?"__LANG__.1904.b23c76f1dd4da22ffcf5.js":2603===e?"__LANG__.2603.f9d2a02c886ec7640135.js":4662===e?"__LANG__.4662.4bb596a7e9afdc8b047c.js":1445===e?"__LANG__.1445.f90a91ddb479cd36c697.js":8370===e?"__LANG__.8370.8f53ecb203cb7ef46f74.js":680===e?"__LANG__.680.004b61cbed26bd77c2f5.js":1303===e?"__LANG__.1303.85ed512a480b68e0e954.js":3357===e?"__LANG__.3357.bbbbecde6b116988df8a.js":3597===e?"__LANG__.3597.e523d19bd75d77e51448.js":2530===e?"__LANG__.2530.69b98008475b554dc8d1.js":1388===e?"__LANG__.1388.44680b15327eb6017097.js":8189===e?"__LANG__.8189.619732a30f4aaf1ca1a4.js":3334===e?"__LANG__.3334.ddbe38e387f787af8520.js":6822===e?"__LANG__.6822.ee9307d05514c90ba8af.js":2486===e?"__LANG__.2486.219e89abf5c3b9fee3bf.js":4040===e?"__LANG__.4040.12183e243d8f517a3ae3.js":6703===e?"__LANG__.6703.0d40cfa5035033bf7dc5.js":6838===e?"__LANG__.6838.43cf9361811d753c5fb9.js":6406===e?"__LANG__.6406.366787d455b91eb80f27.js":2199===e?"__LANG__.2199.5dbcea5dfbfa18bc6170.js":8323===e?"__LANG__.8323.bc847f77a4f96c97facc.js":6342===e?"__LANG__.6342.96ca6cec41634eba0dcf.js":9179===e?"__LANG__.9179.1bb17110d4007c8fb7ef.js":7714===e?"__LANG__.7714.1a8283b330cb49205281.js":9003===e?"__LANG__.9003.00b84b215d3e32b59129.js":6290===e?"__LANG__.6290.8c8962074815f27fe82c.js":8940===e?"__LANG__.8940.cfff4a15faffdc7ab31d.js":6645===e?"__LANG__.6645.7b806e40e2fb49a44342.js":3355===e?"__LANG__.3355.facc72a29bf285f691b8.js":3181===e?"__LANG__.3181.bfad1bfa49a63ea9b078.js":2787===e?"__LANG__.2787.63f25d012ea723b25a13.js":9494===e?"__LANG__.9494.8b4e20a348e167d9a183.js":1427===e?"__LANG__.1427.8fb2101cf2ba270b3f73.js":7952===e?"__LANG__.7952.7657c47534779a121a11.js":1308===e?"__LANG__.1308.893693c9557da5c4f480.js":6760===e?"__LANG__.6760.860d9c4b8c4354852c78.js":6155===e?"__LANG__.6155.902e4cad125dcf6cd148.js":3367===e?"__LANG__.3367.674b971147ade9d32d55.js":2303===e?"__LANG__.2303.16f389155cefdb0999bd.js":1432===e?"__LANG__.1432.6b95710723f48373866c.js":8763===e?"__LANG__.8763.7dd95105967da9e3fa87.js":7850===e?"__LANG__.7850.3aa6145854192b1a33fb.js":7122===e?"__LANG__.7122.1ed01f53abba8ce41796.js":4543===e?"__LANG__.4543.aba71075f3aa3b09c89e.js":5111===e?"__LANG__.5111.0e84f778015dc1f067f1.js":2342===e?"__LANG__.2342.600743c9212728ae9f49.js":5402===e?"__LANG__.5402.8ae961dd947b51d68d03.js":4821===e?"__LANG__.4821.a10db9c3614cc8c6daa2.js":1748===e?"__LANG__.1748.5deaeeeb1d49322b6eb6.js":8936===e?"__LANG__.8936.517cb11cd80606a1e559.js":4109===e?"__LANG__.4109.d3f265b75b625841c189.js":9093===e?"__LANG__.9093.f8cd5dd5ccc0e0bd7bde.js":8975===e?"__LANG__.8975.1103e4b8aa7c7a60ddc4.js":8541===e?"__LANG__.8541.792ac32e47fb46a0b2de.js":8622===e?"__LANG__.8622.9181e2b364297c860b4f.js":6778===e?"__LANG__.6778.227232a064c35e39434c.js":5683===e?"__LANG__.5683.2479c3853762abb2e273.js":7328===e?"__LANG__.7328.36c4de383398c00d3ce7.js":7690===e?"__LANG__.7690.6d79ca989ab1377db6ba.js":4962===e?"__LANG__.4962.a3f22bee213fb4ec46dd.js":6173===e?"__LANG__.6173.98c05959b9e5f8d38113.js":7172===e?"__LANG__.7172.b33b2cca5d9415fefde5.js":1067===e?"__LANG__.1067.39a6f6638bd5cbc0e3a9.js":4829===e?"__LANG__.4829.73fb317fcfa2da965c86.js":5127===e?"__LANG__.5127.00183b016a522bc4e6d6.js":7226===e?"__LANG__.7226.d75fdbcca0c2b8998e4d.js":1086===e?"__LANG__.1086.3d9c7d1f9cb08cdca9e5.js":8255===e?"__LANG__.8255.91b3072e0d5c6e4e6cff.js":1419===e?"__LANG__.1419.91fe5198ce64bef2e9ff.js":6196===e?"__LANG__.6196.72431ed9a14689ee90f8.js":4166===e?"__LANG__.4166.0cd6694df5821f20d252.js":1430===e?"__LANG__.1430.023429d60459435abc6d.js":323===e?"__LANG__.323.92f432a7ab66f51447cc.js":8245===e?"__LANG__.8245.ac34788df822d93e0814.js":1715===e?"__LANG__.1715.894f5ee9f65ad7b86c27.js":5862===e?"__LANG__.5862.f16dc2a97817a6f80f10.js":1629===e?"__LANG__.1629.0d588f55a69735ed0951.js":9333===e?"__LANG__.9333.16a64a3f98967431ee21.js":1025===e?"__LANG__.1025.96f5d0769a3499cd18c3.js":9722===e?"__LANG__.9722.7947af770da881c26ffd.js":8703===e?"__LANG__.8703.759ac7d8524a85b761b5.js":({
92:"chart-screenshot-hint",139:"get-error-card",319:"line-tool-table",341:"line-tool-schiff-pitchfork2",360:"demonstration-highlighter",380:"line-tool-price-note",507:"study-pane-views",569:"line-tool-arrow-mark",574:"line-tool-horizontal-ray",607:"study-property-pages-with-definitions",620:"tablecontext-menu",688:"line-tool-callout",731:"add-compare-dialog",906:"line-tool-fib-speed-resistance-fan",925:"line-tool-extended",961:"line-tool-path",1026:"symbol-list-service",1155:"line-tool-5points-pattern",1196:"watchlist-widget",1205:"data-window-widget",1277:"line-tool-balloon",1282:"line-tool-vertical-line",1313:"line-tool-pitch-fan",1455:"line-tool-date-and-price-range",1470:"line-tool-arrow-marker",1506:"line-tool-fib-timezone",1583:"lt-pane-views",1584:"context-menu-renderer",1652:"trading-groups",1702:"manage-drawings-dialog",1713:"line-tool-sine-line",1754:"symbol-search-dialog",1859:"go-to-date-dialog-impl",1890:"line-tools-icons",1963:"line-tool-gann-complex",2050:"line-tool-parallel-channel",2077:"change-interval-dialog",2087:"line-tool-highlighter",2183:"study-inputs-pane-views",2277:"line-tool-risk-reward-long",2283:"line-tool-fib-channel",2306:"floating-toolbars",2312:"line-tool-text",2377:"hammerjs",2413:"custom-themes-api",2650:"trading-custom-sources",2704:"currency-label-menu",2816:"line-tool-fib-circles",2878:"drawing-toolbar",2985:"data-exportdialog",3005:"header-toolbar",3030:"new-confirm-inputs-dialog",3168:"partially-closing-dialog-renderer",3177:"line-tool-templates-list",3248:"line-tool-note",3314:"line-tool-fib-wedge",3378:"line-tool-head-and-shoulders",3383:"line-tool-triangle-pattern",3555:"price-scale-mode-buttons-renderer",3566:"create-confirm-dialog",3596:"general-property-page",3710:"line-tool-fib-speed-resistance-arcs",3723:"line-tool-fib-retracement",3809:"trading-custom-widgets",3866:"line-tool-poly-line",3945:"line-tool-projection",3966:"line-tool-comment",3989:"confirm-widget",4010:"trading-informer",4013:"custom-intervals-add-dialog",4015:"line-tool-rotated-rectangle",4079:"series-pane-views",4193:"bottom-widgetbar",4201:"line-tool-horizontal-line",4273:"line-tool-date-range",4389:"take-chart-image-impl",4598:"delete-locked-line-confirm-dialog-content",4602:"line-tool-three-drivers",4604:"toasts-groupable-layer-chart",4627:"dom-panel",4664:"news-description-dialog",4665:"share-chart-to-social-utils",4674:"line-tool-signpost",4731:"line-tool-trend-based-fib-extension",4862:"object-tree-dialog",4876:"widgetbar",4934:"line-tool-ray",4981:"line-tool-gann-fan",5001:"partially-closing-dialog",5009:"load-chart-dialog",5031:"object-tree-panel",5055:"line-tool-pitchfork",5093:"chart-widget-gui",5122:"line-tool-brush",5142:"trading",5206:"line-tool-bars-pattern",5231:"line-tool-image",5248:"library-studies",5283:"line-tool-abcd",5500:"line-tool-anchored-vwap",5516:"restricted-toolset",5529:"line-tool-emoji",5551:"favorite-drawings-api",5565:"ichart-storage",5592:"chart-text-editor-renderer",5598:"lt-stickers-atlas",5639:"currency-label-menu-events",5695:"line-tool-volume-profile",
5967:"line-tool-arc",6112:"leverage-dialog",6124:"chart-storage-library-http",6166:"chart-event-hint",6265:"new-edit-object-dialog",6336:"line-tool-gann-fixed",6432:"line-tool-triangle",6456:"study-market",6477:"line-tool-price-range",6484:"line-tool-price-label",6631:"study-template-dialog",6740:"line-tool-cypher-pattern",6748:"line-tool-circle",6768:"line-tool-risk-reward-short",6780:"source-properties-editor",6991:"symbol-details",7038:"insert-image-dialog",7078:"general-chart-properties-dialog",7102:"spinner-renderer",7127:"line-tool-trend-based-fib-time",7129:"line-tools-synchronizer",7175:"line-tool-schiff-pitchfork",7203:"line-tool-cross-line",7260:"chart-bottom-toolbar",7271:"compare-model",7488:"line-tool-info-line",7502:"order-widget",7539:"studies",7563:"line-tool-ghost-feed",7648:"show-theme-save-dialog",7660:"line-tool-ellipse",7707:"footer-widget",7806:"line-tool-icon",7987:"lt-icons-atlas",8020:"user-defined-bars-marks-tooltip",8061:"line-tool-bezier-quadro",8090:"line-tool-fib-spiral",8179:"terminal-configset",8313:"chart-storage-external-adapter",8334:"line-tool-time-cycles",8354:"trading-account-manager",8372:"line-tool-trend-angle",8422:"line-tool-rectangle",8468:"line-tool-inside-pitchfork",8537:"lt-property-pages-with-definitions",8607:"line-tool-arrow",8643:"full-tooltips-popup",8673:"line-tool-trend-line",8751:"position-widget",8820:"line-tool-flag-mark",8890:"simple-dialog",8949:"line-tool-sticker",9014:"line-tool-bezier-cubic",9039:"lollipop-tooltip-renderer",9123:"line-tool-text-note",9310:"line-tool-flat-bottom",9374:"symbol-info-dialog-impl",9445:"line-tool-cyclic-lines",9478:"line-tool-gann-square",9498:"export-data",9534:"line-tool-prediction",9581:"line-tool-disjoint-channel",9685:"series-icons-map",9748:"line-tool-regression-trend",9754:"global-search-dialog",9790:"favorite-indicators"}[e]||e)+"."+{92:"9f3a96d769a1e33cd974",139:"a031f39f01dfc4896f5c",195:"8aece1bcec74f8aa0198",218:"5f3e0e6cd93fad5e3d2e",303:"7582f8d82219189bbca4",319:"fa8073658bb0379600d2",341:"e1ee5cc06d4b96c112cf",360:"0f62595712186cfed817",380:"e7fc870ef1557fb0fe52",422:"9335b7df4365e97cbb6a",427:"8e4ad2884751c78b5ebd",507:"c62fc52b9a2dc4005c4b",569:"cdabaea3ceba1986ad59",574:"6b1efa2c567e2d2e94a1",579:"393c691a96a64fc53998",601:"bc292726a701d789f4a8",607:"12ebbd1ccbc8d7a778cd",620:"e4793114015e643731ce",643:"b5b634495eec3f1b3c14",688:"fb9fc0c52e9dbc7efa99",731:"5c5e616ea16bb43dcec2",779:"4eec9138a97bf5ff8a8a",906:"e7ff1b13ad1ed9feb702",925:"c9b3d0e1d83498eae69d",959:"2526af36a4d043499f8f",961:"39b0569dcf42c20f177b",1013:"3451ea713c61a96ee779",1026:"ff72f86845c1d337fda3",1072:"be3a8a7d09f34e875a1e",1119:"d9afdd012b225f041951",1135:"2aac538ef3cf279f4179",1155:"a670a6a2b633e6d886b9",1191:"7eaa73b7c4e196f5e964",1196:"c7bd684646cd360803e2",1205:"997c077e13963f88220b",1249:"f802ed1afc02c9377008",1252:"c5de90e9c298e410878e",1277:"7eef6d45cf434452b091",1282:"c137d9210c73fac3be5f",1298:"ef9292896ac7193ea04a",1313:"390f14217b9f85109946",1356:"ee67c6639700159aaa19",1369:"55535c8c5abd6cb2ce32",1380:"b8f0eb82e1be478fd395",
1389:"db354d1ffbbec704515e",1390:"ffdc2b9032c730265212",1442:"b2c013cd0d71ae9198e7",1455:"051d4023295865c19ed3",1456:"b82a647336b03fd9960f",1470:"f140aff21efe59ce3ae6",1506:"e26f7c7af46bc0477cd1",1551:"e2284fc49aff27da83f2",1553:"eaf0cb371a2c4fa90bbf",1575:"ae53d2376a75e39a6cd1",1583:"0e3618964c1d64330263",1584:"a115d5ac19a33e492b8f",1652:"9659877d7d96e0bb027b",1702:"5acacbee8873756d69cf",1707:"f878ba5fe294b456fea8",1713:"318633c2943533aaeb73",1728:"f96ad36b13219e7ef1d8",1737:"a88f1ff138fca90aaa14",1754:"f3e926b328070c8036b4",1818:"12d242eb48e189785eb0",1833:"a12ca3d3e3276fb1b461",1859:"c90797e0c6ad86ea2772",1880:"48440da13e05c4d67c22",1890:"b492c92d44816b2cfc80",1906:"434b4be8cda98af43453",1933:"510498d67c9b20798afa",1963:"82ff4389abc01f316340",2050:"e2e289455ec8e4f97d55",2077:"a8ba0df4c4a77167cc2a",2082:"ff8d192d75a2c67cf0b3",2087:"9d67f91df2e9e003a983",2092:"621da691114cb288b6ae",2106:"5841680469b77f217f8f",2115:"cb1d82540a4e22e13942",2183:"73d08de4bceec5b1970d",2197:"9e441b34254e9ebf8757",2227:"b3ce716bc6a1eebf1a92",2234:"0999ea5b233814caaf59",2277:"bb9d22eb3ee52471cf76",2283:"72b63d79a4fea16b18bd",2306:"435f06c307950384d090",2312:"08f5c911752b28df4417",2371:"d7c78ac916c18b933246",2377:"4e17378884ca530926b7",2413:"e053a3d6404e6543f692",2433:"06b575c44f467c97bacf",2440:"e433d90edf053f8432de",2444:"140908ac04f81fa39b26",2520:"e0c09329ef8bb4340a46",2538:"13f4bc31d9a146bc25c4",2564:"d2a3fd8f9cddcf5264c3",2568:"65fa1645cc65bdee4324",2590:"e15027d82972de8a15c2",2645:"baf3a9982a43bef47743",2650:"8d00ec8d822b402ca34b",2666:"d98c2d1c448b72ede17b",2704:"5e4a65f682c315c97785",2736:"066be336f75b34bccc10",2743:"62851cdb0362a5ba14a0",2816:"7a88ae741a0532837677",2849:"75381d5d0610025d8369",2864:"3c2bc645c073a6505d89",2878:"9f682ebb059580da885e",2882:"a515feeb52d6ecd8ba6a",2947:"22749f163c00ada76842",2985:"8525d1f6cf241ad40fb2",3005:"33da2545627b9608b949",3010:"b6767e7ba0734f8ea43b",3030:"1ad7716cf548a0a317ad",3054:"6323f377d9660accde3b",3062:"7321e53c467a675b6e1b",3168:"079aa6634c9c5d93379f",3177:"21e19d1b710af56bb4a7",3248:"643c572b18f008e5c4c5",3251:"3c8acb079b74b035c388",3285:"152d712e7a2875eeb239",3314:"f69d1cfc1b31057f9e47",3362:"cc61c557540cab7abba0",3368:"7f1c0dc74919a5203b62",3378:"7d97fe23d4f1bbb107e2",3383:"15b9de096fb124b528c0",3420:"ed0d8fd1c83a5414d0d9",3450:"6298d8acc05971e75477",3473:"8d98004e4d555038212d",3524:"abd2bb0b35b9b303f8ae",3555:"f005deb14baec3edb33f",3565:"d55a0e750cec96d42af2",3566:"87b53e347f2649a7321e",3596:"b2b045afa1ff86d18bcd",3693:"eba6a9041db303dd8bdd",3703:"34314901d1086db0e25e",3710:"f199b848f18447881ed8",3723:"942a6518e6c4115bee55",3782:"5f5c74a8b0873224181b",3799:"57b13fc53ae3cdf9b02f",3805:"42495629e38f02c4d148",3809:"fce5afddf1d90d616bc0",3828:"dd3b3ab9b1e9037c5d53",3847:"4a2226b0147b5a279dee",3866:"20a65de4446962385acd",3905:"5e651f8ebf55269e0eda",3945:"64d31eed98c776c8d7f2",3953:"074939cda643555b9fa0",3966:"29f25249f31b85635bd5",3989:"66cf3a8256ac3ddf2e71",4010:"1a37f780bbfd1085e9c6",4013:"d76a6a740a6ac3246ab1",4015:"fa7177ca6371f9ebf508",4057:"debdf4bb4205c8a4ca75",
4066:"f51f4a04f2ec9db826e7",4079:"3e9df0950a7091909d2b",4106:"39a4b0e746d3a222c47b",4125:"89d571e6090f973fad1f",4193:"6cd99a863ac9d00955f1",4201:"d02170d5828cc866a82d",4256:"264ea6b0e6a4742d480c",4273:"63003d5e71e1c5954569",4333:"5833c0eb2dffcbc3ab13",4353:"7cc84e240b93d6e7e35f",4389:"42287d91c9035ba0c0d7",4510:"37dc6aabfa87acb3281a",4524:"3aeb32cd32e5da655aa5",4556:"681e1cb78cccc175cab8",4570:"2c8d882e1ef1f67d91f4",4582:"c07ece0ff51161c30ccc",4590:"36be502561204866828e",4598:"0ef3463857331a35a126",4600:"09e4c4cc50200b9e6850",4602:"b38cf49208597c28709f",4604:"a858a2d486d8da454954",4627:"81d8dd298ff835119bb5",4664:"a52c8cefcc4170215723",4665:"62684128a22a69dd5f27",4674:"da6be0084097c1e613bd",4731:"691d80ce70d6c1438802",4752:"40d8fcf4aaa1d9b70107",4797:"e09f148a092fd587e782",4862:"4a11d9f8c1610348f9d1",4876:"d988faecd0403223478e",4920:"f895b624c7bb2824550c",4934:"7541369ac5b368ddbcd1",4938:"a4f9a9b63b46f8f0a563",4952:"1e54c133b22993b96a57",4981:"50223e2599e30aea9969",5001:"edec6fb5a2d26dfac97e",5008:"ba6bb65418eeb62cac11",5009:"baf3981812a2cb6bda5d",5031:"09f8e9c19caa65eeba5b",5055:"ecf74ac3bdf7e8c01a6e",5058:"99cc5cca8222cb165435",5083:"46b0c5fd2a360051603e",5093:"373398f680e71823f0f1",5122:"d570e2ffd586b10fc876",5142:"c5c3491397695482c030",5206:"b606b15fb3f27a7139ea",5231:"4d04f2b79bf1118ef7a9",5234:"86778413e5ab5c5b97f7",5248:"fca0ee09201de497a9cb",5283:"d01d71c718f2a009181c",5299:"3e7112e6038e6b10fda4",5323:"f0f2e4cd72aef7561792",5375:"40c7f0a13dfd0b073241",5387:"1fb6a179200ed9f9d598",5446:"34d25f33d4a5377fc9b5",5476:"778f33a85e34c613bb7e",5480:"872f4f12e150c051ec76",5500:"feb636b22ba9682d91ee",5516:"6f81af351cfbad2af083",5529:"0242475ba0a76b7939f4",5551:"ba616fcfb21d72d7c603",5565:"7610363593b90aad5c6b",5579:"30bff8dc3806b9475644",5592:"52b6d0d014826597d257",5598:"3723bd39677b1dcaf1c2",5621:"21d9d121061db779b4b9",5639:"10ee734f18789b1b3a9f",5695:"00a8e42b7c4f151f31a8",5758:"c737f2f4fa99891d8880",5789:"f84eb4cc44a8ece5086b",5816:"84b2a7136a85cfa2abde",5826:"ebf365249c45db1ffdaf",5881:"b059eb75e9541901b30b",5891:"825e526d0a17a439e2bd",5967:"c3e0937be7591b93a5f3",5975:"b7cae9993c5dd48e5a93",6050:"bd16be8a5be7241a9d50",6112:"c11b2c654c9f8c692a4f",6124:"8480de7178466bea2b66",6161:"10c4a7de17f463d1d76e",6164:"d777d634896ff9f161e6",6166:"b3213e45c9027ee69e75",6190:"ba40b4eca0e5787a7131",6212:"f2b0954f9876e5dbec45",6243:"3ef58960653f2f0d3449",6262:"50bf96239e95bd5526f8",6265:"4d64cf5237fd0734deb8",6316:"f4ad5200fc2f7cfac880",6336:"45540c12529aac3103a1",6432:"8e20271f2de181c14482",6445:"35b87669babb3c0df14e",6456:"958531c4a183b19a7315",6477:"57fc6d730c3936a6eccc",6484:"3345899386830cd21aa9",6489:"bc05b17c8a898e58bbac",6524:"b01a0f001e095133f923",6605:"77b25fb5983b92366ef7",6631:"3e845268088620556c3b",6710:"04d48637cfbf4dd81f30",6740:"1d195fc4890a7f9e4e2b",6748:"7f2ab7bd5170ca4fa98b",6750:"d51335589dbaf2643e2c",6752:"4ece1a3fc62c728db6ee",6768:"4d6333ef644e3196831f",6780:"900e24a9cf927086b5b6",6821:"8a62a4afbb845b041ef9",6828:"edd9a42c8123c0c72113",6842:"d8d23575b4bae4029458",6847:"a20c2744b4ec26c282c2",
6881:"c2c62b9fc890de346917",6898:"f753c8c58a80d1e8b237",6983:"8f585d509864372ddb6f",6991:"b77e4d320c1dbbfb0ea5",7e3:"21314dd7f29723dd7570",7001:"2e9740e3dd29f52fbc39",7038:"7e2f48e68e7184d0d3dd",7078:"f1e0baa3f7c3ba8b7a81",7102:"20676b1f5bb8e3a5388e",7125:"f506fee64d0fbd3aac4a",7127:"3f335968fad8f29dfbc7",7129:"667b607302351f23fe7b",7159:"9cf6001375236b4ae5ae",7175:"2f2ee803b13a7f826747",7203:"114f2d41da4e2ca7f677",7204:"4ce39b0fb7cfe7b4e3a4",7259:"c51f432500f7e107aa27",7260:"fd38479e975a1ac4fdd8",7271:"17fcdaff82dd9646d47f",7280:"cb8e5e464328782bc90d",7335:"f7017c32a7d4a05f3652",7384:"0cdb4857f1460f4d9a53",7403:"9a5d289733609ece1fe7",7414:"ea5ed4ee44831da2a4a3",7415:"6d9e5497dea3ae9ce05b",7431:"8bb2d0defdb7e0386794",7435:"320e6ee2a20ef22f4c89",7488:"8965aac5f9f59654f9c8",7502:"1d82e2e1e9cb3f946588",7539:"df9d03d5d71d89aed43c",7563:"c3c0348ac2b7c986be31",7572:"55da2195e08bfa006905",7648:"08f7e98705f191217dd8",7660:"7bbfa48a6e6496965b0f",7707:"08780bf0181c58031ae2",7762:"98a895505c293bb738d3",7806:"3cce74adf9ace2d0a51b",7819:"92b63cba3a9c5e61f4a0",7849:"2f7a84cee992a2647356",7854:"ac317400d306dab09101",7898:"93482c7ce4146884bda0",7935:"4b1b34f77a2daa9c9b93",7987:"9be7ec5119b485e074ed",8020:"5ed365d69bbe08c5ca0d",8061:"e4b6c72098bc06127927",8090:"aa7d11906c58b59b98c5",8146:"f36074923242659c4bb9",8179:"abbe3b2ddf1adcad2530",8185:"fefab7a1a5f120cf5ab9",8222:"87a78213ec9f0d1770b0",8257:"e182f402a058e5d88be8",8313:"be0fcd2667b4f0a36e03",8334:"fd3155eb606914b2cc52",8354:"1b2c5c3fc6d9600dc0fb",8357:"7d87dcb5fa338aab4b82",8372:"99db412a62f6a30d7d93",8402:"2756e58e899131679cc9",8422:"06d9fa4548f1b0ae5e87",8468:"58f89f2158d436238406",8473:"0958453a81590d368e16",8511:"ebf1c606bff9c570835b",8524:"1f8a90c800e6e2b3b5dd",8537:"f4646c40b1d992a958d0",8544:"fb27137673cba35e671e",8607:"790bbdafab88d5925df8",8643:"0bf10e2e1346ce96b36a",8666:"36efe49307baf278bd9f",8673:"47587f2a98e7aebf8ede",8687:"4bb18f37c96aa9314d64",8692:"eed8394a9b1af7348f9a",8739:"532c79a446ac6bc80f1c",8751:"b36b02fe1cc4cfc34e5a",8820:"bf9d612db929e52430bd",8859:"ae2af6f78341c0095774",8890:"53830f75346c6ed8c553",8949:"15ac522d373de72bc5bf",8985:"d4599a0cccc3494e753e",9014:"645dcda81a279752cafe",9039:"f3c2dd7476dfeb41ded5",9063:"045a88bcd33365db0b45",9088:"e5b04ee7028204603d5e",9123:"343ef931a7b4346f3ebe",9162:"374ec9b96865aca6ea07",9216:"dce4735e29d10fe9a604",9221:"9ecb1eb50b85e7001b40",9255:"218770777b8f819418ad",9258:"d5d85e4484e2c6e1716a",9296:"5dae32e37069d21f713e",9310:"fbe73477437c21959759",9325:"3eb0347ac3afd65e4e78",9362:"06fa529ffafc631dcf1e",9374:"23e8feddd0326a1feabb",9388:"1bbc6cc7fb8cfbfe0079",9393:"264afbc1624dbf8fbd70",9445:"48c6042e7ef1e6803c72",9478:"e226bd81b316c3c0432f",9481:"ef84307c90c7f4780c09",9498:"8d301f6cc78c2926d6ab",9531:"3f5141a6d86859d2739a",9534:"9e38a052b51bea1ec07f",9561:"37623a78cf46aa593608",9581:"a07796f98000e84c9927",9608:"d4f33f9e72384b833e5c",9642:"60344d01ebd7c95ec690",9654:"d38201d2782cc90eb9f5",9685:"869f58e0b1a1f923901a",9704:"1bfe00ea1b3f1d064e68",9748:"9f55a32459b6ee203c90",9753:"1c410653234b85ae211e",
9754:"be3beae2e53101d0e894",9766:"14ec8ca9f791a864f57a",9790:"3d92a991466d2490ac31",9824:"97ceb3a02f099bcee23a",9937:"e9709e88abb24d2bb653",9967:"7ea4b09ac3ec84715844",9977:"33cfa81f34341c7e5e31"}[e]+".js",o.miniCssF=e=>e+"."+{303:"e9b6e523eb66dacf92bd",422:"bc23975c348a4f41dea4",579:"50ef343d165189abd0ba",601:"b6bc31b7ce369908d97d",643:"5e08138e265dd49437ea",779:"b84315f4350430cdb348",1013:"7a29c300c9788022d8a3",1072:"67a2846c0506e2e592be",1135:"a33c76f5549cc7102e61",1249:"7f5f09a1d671819fafa9",1252:"788a58021829bdae27fc",1356:"e2248aecf1ad4e148d65",1380:"5ba7850c7061f2d26173",1389:"9ce134321e5f7ed1c4ad",1390:"626f0a194297d6d23002",1551:"0163412c2c2bbec3323e",1575:"60bc11558d66aa690217",1707:"fbb750fd312778403036",1728:"f5824899227309b419bc",1737:"51511f925000b99093e3",1818:"eb704f72062b07c6506d",1833:"1e1cad103085069c69fc",1906:"40ce159ad2a7f4f15d5c",1933:"a40665ade21837a1b5e2",2082:"acf5e91ffffdb18a2cf1",2092:"4b97d1e084a7f81e17f2",2106:"3418d3509b4f6a41c2eb",2115:"d4ce023e54009adf69b3",2197:"3c275591170ccafa3bbe",2234:"be3e2002889c3507ea77",2371:"3fc94a1aa7a1e03d91e4",2440:"8620e9f557ec49b4b3d7",2444:"3360c6e677bffb470a53",2520:"884e337706189fe0160c",2564:"cac75529a7aa17227aa7",2568:"3586586cffe61c1d9c0f",2590:"4a004f1a3b71d5105cb2",2645:"768a1ffd7fc8890420df",2666:"d7dd4a59f33a2f52cf86",2736:"c88f0ff04a966a2fb2df",2743:"a9087bf79796576ba8f5",2849:"d2135536510367775ff1",2864:"f4dde91fd09337ffbc3c",2882:"79e0c6817a49cfff8476",3062:"553fc65e7c6f62255d16",3362:"2476428ae07d34323af5",3368:"a16ef892e23997d1eec3",3420:"0353e321d943562b4486",3473:"e93220a4080eb191e0fd",3524:"ad0b2bd39908845c1ac9",3565:"f3d891b275768e3e0a5f",3693:"44308eb6167ec63939d4",3703:"fbb750fd312778403036",3782:"f5f264bb4298497c0f97",3799:"8fe69f925e501b5d9dea",3828:"b87cd06c9d4e7baa6eda",3953:"7146f14703227db023af",4057:"400f57042c58b6946a7c",4066:"eba4ddabfe5309662e8a",4106:"8577632fdab29ee53ddf",4125:"839d0d8af876d7e15703",4256:"1d39c3c8dc435b82e4c6",4353:"9eade1db5c4beb02071a",4510:"35411b67165343bc4949",4524:"d6f97e44b70e5e01f7d4",4556:"cff39ef77f0827e16b81",4582:"e8408ec9a76ca48745ba",4600:"362fa6a7ab1f3e3b06c4",4752:"e86f4005171a6af35829",4797:"ecf541f2301446a3089c",4920:"d7c8574fa306aeadd297",4938:"a07e1da10e414c329264",5008:"0c0c7a9db0a3b15c8103",5058:"2776c62444167c12b233",5083:"a9187278de5ab079b6d2",5299:"dfdc35657b863723458e",5323:"05b152d9c203bbe1e861",5375:"132067dc73c5743d825f",5387:"6dc6a5ec31f9d97dc855",5446:"1f5883a75f4d7d613e20",5480:"62c8085685951c0bc278",5621:"1dd4b4462472082c1269",5758:"7723043bc419795133c2",5816:"44efac501e6cf354a70b",5826:"c70dad3fc1bd0fd0f22d",5881:"429d5b055ce817909b34",5891:"24783caa1cacf6c87546",5975:"a3e49a7b95404261d4eb",6164:"3d692610247dc24d7b64",6190:"ef0bd3010b1c05e755bc",6243:"1cbe83f8e15217dddb9b",6262:"cce9c2d0878310b9ee03",6316:"4c1aab38ac483c2022bb",6445:"d2b2f28850f8c828a4d7",6489:"9070beae9e8cef21670a",6524:"6b06e3c46a3a62c62d7c",6605:"487abc54552f72e14d11",6710:"9171de773f9f829118f4",6750:"de7185fa4470c6688792",6752:"a496d8cf9d524daacf28",6821:"bc3c30c395044c25b76b",
6828:"3bcf3e2b04d67b8ca804",6842:"f6f5a9b5ab991d9de920",6847:"f6b63c197359a8b78abb",6983:"cadd6c828bd2c0930e97",7001:"d167a8793031fa802176",7159:"27d77fe8d8dff58ceb0a",7204:"b3dcbd58906265276dd8",7280:"7c6e45398eccc266b2a0",7384:"aefeb9db8e938fbfb1dc",7414:"8b6b93d5fc5e20706c25",7431:"ef930db5ef4b533a7904",7435:"45bbca649d592877886a",7572:"eeafd67dac93bb21fca3",7762:"c7ba43013335f9bc0a67",7819:"ec12fcdd26208efd2be5",7849:"2e870d1d6307ad5e1004",7854:"77fde9897992fb67135b",7935:"5553a5da7a4e4bd90438",8146:"4982384e389f11dd6517",8185:"3f7aa75dce6efc9bcaeb",8222:"a31543d092246fd03dd2",8257:"0e03bab72eb086ffb4ee",8357:"8fc764ff0ed9b29a01ab",8473:"bd22fdbcb9baef45b965",8544:"16c0fd7539d08ad5ffd3",8666:"d1d8147337bae3fa6372",8692:"1372368151d36104b74b",8739:"46bc02e3f69ed5c4c004",8859:"4e8179db16fddc59e8fa",8985:"10034c1258391b9f008c",9063:"0563208c1bb41cf158b9",9162:"7dd9df5592fa3bfd0827",9221:"80dee957187879f81fd2",9255:"574882a5338ad0774967",9258:"127a4c4030a8c2724b98",9296:"dff3cd88e89f770d4753",9325:"f2e1edd6097be38e73da",9362:"cc83d3aee74a110c4071",9388:"e55a351c0813be7d0c20",9393:"98129bb676c42540f954",9481:"63a2692b383c39f8a3d1",9531:"9edf4ac64b0547e8a926",9561:"892974d637b862abee04",9608:"d8e66f4c4d6c2e5e9e51",9642:"60f4b13d8eb012aa4791",9654:"e02f2476bd5db34eaf55",9704:"004638546a58c07591ff",9753:"b7b1c5568e3084458241",9766:"631d966499ce5a6b82a9",9824:"b4ab54cb2a5928dcd2d8",9967:"ea707572e7ac5e0fd485",9977:"d6dee9346a53b63a68b0"}[e]+".css",o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a),d={},f="tradingview:",o.l=(e,a,c,b)=>{if(d[e])d[e].push(a);else{var t,l;if(void 0!==c)for(var n=document.getElementsByTagName("script"),r=0;r<n.length;r++){var i=n[r];if(i.getAttribute("src")==e||i.getAttribute("data-webpack")==f+c){t=i;break}}t||(l=!0,(t=document.createElement("script")).charset="utf-8",t.timeout=120,o.nc&&t.setAttribute("nonce",o.nc),t.setAttribute("data-webpack",f+c),t.src=e,0!==t.src.indexOf(window.location.origin+"/")&&(t.crossOrigin="anonymous")),d[e]=[a];var _=(a,c)=>{t.onerror=t.onload=null,clearTimeout(s);var f=d[e];if(delete d[e],t.parentNode&&t.parentNode.removeChild(t),f&&f.forEach((e=>e(c))),a)return a(c)},s=setTimeout(_.bind(null,void 0,{type:"timeout",target:t}),12e4);t.onerror=_.bind(null,t.onerror),t.onload=_.bind(null,t.onload),l&&document.head.appendChild(t)}},o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e;o.g.importScripts&&(e=o.g.location+"");var a=o.g.document;if(!e&&a&&(a.currentScript&&(e=a.currentScript.src),!e)){var c=a.getElementsByTagName("script");c.length&&(e=c[c.length-1].src)}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),o.p=e})(),
o.g.location&&o.p.startsWith(o.g.location.origin)&&(o.p=o.p.slice(o.g.location.origin.length)),(()=>{const e=o.u;o.u=a=>e(a).replace("__LANG__",o.g.language)})(),o.p=o.g.WEBPACK_PUBLIC_PATH||o.p;var l=o.e,n=Object.create(null);function r(e,a){return l(e).catch((function(){return new Promise((function(c){var d=function(){self.removeEventListener("online",d,!1),!1===navigator.onLine?self.addEventListener("online",d,!1):c(a<2?r(e,a+1):l(e))};setTimeout(d,a*a*1e3)}))}))}o.e=function(e){if(!n[e]){n[e]=r(e,0);var a=function(){delete n[e]};n[e].then(a,a)}return n[e]},(()=>{if("undefined"!=typeof document){var e=e=>new Promise(((a,c)=>{var d=o.miniCssF(e),f=o.p+d;if(((e,a)=>{for(var c=document.getElementsByTagName("link"),d=0;d<c.length;d++){var f=(t=c[d]).getAttribute("data-href")||t.getAttribute("href");if("stylesheet"===t.rel&&(f===e||f===a))return t}var b=document.getElementsByTagName("style");for(d=0;d<b.length;d++){var t;if((f=(t=b[d]).getAttribute("data-href"))===e||f===a)return t}})(d,f))return a();((e,a,c,d,f)=>{var b=document.createElement("link");b.rel="stylesheet",b.type="text/css",b.onerror=b.onload=c=>{if(b.onerror=b.onload=null,"load"===c.type)d();else{var t=c&&("load"===c.type?"missing":c.type),o=c&&c.target&&c.target.href||a,l=new Error("Loading CSS chunk "+e+" failed.\n("+o+")");l.code="CSS_CHUNK_LOAD_FAILED",l.type=t,l.request=o,b.parentNode&&b.parentNode.removeChild(b),f(l)}},b.href=a,0!==b.href.indexOf(window.location.origin+"/")&&(b.crossOrigin="anonymous"),c?c.parentNode.insertBefore(b,c.nextSibling):document.head.appendChild(b)})(e,f,null,a,c)})),a={3666:0};o.f.miniCss=(c,d)=>{a[c]?d.push(a[c]):0!==a[c]&&{303:1,422:1,579:1,601:1,643:1,779:1,1013:1,1072:1,1135:1,1249:1,1252:1,1356:1,1380:1,1389:1,1390:1,1551:1,1575:1,1707:1,1728:1,1737:1,1818:1,1833:1,1906:1,1933:1,2082:1,2092:1,2106:1,2115:1,2197:1,2234:1,2371:1,2440:1,2444:1,2520:1,2564:1,2568:1,2590:1,2645:1,2666:1,2736:1,2743:1,2849:1,2864:1,2882:1,3062:1,3362:1,3368:1,3420:1,3473:1,3524:1,3565:1,3693:1,3703:1,3782:1,3799:1,3828:1,3953:1,4057:1,4066:1,4106:1,4125:1,4256:1,4353:1,4510:1,4524:1,4556:1,4582:1,4600:1,4752:1,4797:1,4920:1,4938:1,5008:1,5058:1,5083:1,5299:1,5323:1,5375:1,5387:1,5446:1,5480:1,5621:1,5758:1,5816:1,5826:1,5881:1,5891:1,5975:1,6164:1,6190:1,6243:1,6262:1,6316:1,6445:1,6489:1,6524:1,6605:1,6710:1,6750:1,6752:1,6821:1,6828:1,6842:1,6847:1,6983:1,7001:1,7159:1,7204:1,7280:1,7384:1,7414:1,7431:1,7435:1,7572:1,7762:1,7819:1,7849:1,7854:1,7935:1,8146:1,8185:1,8222:1,8257:1,8357:1,8473:1,8544:1,8666:1,8692:1,8739:1,8859:1,8985:1,9063:1,9162:1,9221:1,9255:1,9258:1,9296:1,9325:1,9362:1,9388:1,9393:1,9481:1,9531:1,9561:1,9608:1,9642:1,9654:1,9704:1,9753:1,9766:1,9824:1,9967:1,9977:1}[c]&&d.push(a[c]=e(c).then((()=>{a[c]=0}),(e=>{throw delete a[c],e})))}}})(),o.i18next=(e,a={},c,d=o.g.language)=>{if(null===e){if(Array.isArray(c))return c[void 0===a.count?0:o._plural[d](a.count)].replace(/{(\w+)}/g,((e,c)=>void 0!==(a.replace||a)[c]?(a.replace||a)[c]:e));if("object"==typeof c){if(o.g.customTranslateFunction){
const e=o.g.customTranslateFunction(c.en[o._plural.en(a.count)],c.en[0],c[o.g.language]?c[o.g.language][o._plural[o.g.language](a.count)]:void 0);if(null!==e)return e.replace(/{(\w+)}/g,((e,c)=>void 0!==(a.replace||a)[c]?(a.replace||a)[c]:e))}return c[o.g.language]?o.i18next(null,a,c[o.g.language]):o.i18next(null,a,c.en,"en")}}else if(c&&e){const d=`${e}${a.context?`_${a.context}`:""}`;if(c[d])return o.i18next(null,a,c[d])}return"number"==typeof e?e.toString():"string"!=typeof e?"":e},(()=>{var e={3666:0,4986:0};o.f.j=(a,c)=>{var d=o.o(e,a)?e[a]:void 0;if(0!==d)if(d)c.push(d[2]);else if(/^(1(3(56|80|89|90)|7(07|28|37)|(01|83|93)3|072|135|249|252|551|575|818|906)|2(1(06|15|97)|44[04]|5(20|64|68|90)|8(49|64|82)|082|092|234|371|645|666|736|743)|3(36[28]|7(03|82|99)|(0|47|69|95)3|062|420|524|565|666|828)|4(5(10|24|56|82)|9(20|38|86)|(06|10|25)6|057|125|22|353|600|752|797)|5(0(08|58|83)|3(23|75|87)|8(16|26|81|91)|299|446|480|621|758|79|975)|6(4(3|45|89)|7(10|50|52)|8(21|28|42|47)|01|164|190|243|262|316|524|605|983)|7(4(14|31|35)|8(19|49|54)|001|159|204|280|384|572|762|79|935)|8([19]85|[23]57|146|222|473|544|666|692|739|859)|9(2(21|55|58|96)|3(25|62|88|93)|6(08|42|54)|7(04|53|66)|(48|53|56)1|063|162|824|967|977))$/.test(a))e[a]=0;else{var f=new Promise(((c,f)=>d=e[a]=[c,f]));c.push(d[2]=f);var b=o.p+o.u(a),t=new Error;o.l(b,(c=>{if(o.o(e,a)&&(0!==(d=e[a])&&(e[a]=void 0),d)){var f=c&&("load"===c.type?"missing":c.type),b=c&&c.target&&c.target.src;t.message="Loading chunk "+a+" failed.\n("+f+": "+b+")",t.name="ChunkLoadError",t.type=f,t.request=b,d[1](t)}}),"chunk-"+a,a)}},o.O.j=a=>0===e[a];var a=(a,c)=>{var d,f,[b,t,l]=c,n=0;if(b.some((a=>0!==e[a]))){for(d in t)o.o(t,d)&&(o.m[d]=t[d]);if(l)var r=l(o)}for(a&&a(c);n<b.length;n++)f=b[n],o.o(e,f)&&e[f]&&e[f][0](),e[f]=0;return o.O(r)},c=self.webpackChunktradingview=self.webpackChunktradingview||[];c.forEach(a.bind(null,0)),c.push=a.bind(null,c.push.bind(c))})(),(()=>{const{miniCssF:e}=o;o.miniCssF=a=>self.document&&"rtl"===self.document.dir?e(a).replace(/\.css$/,".rtl.css"):e(a)})()})();