(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3596],{729330:(e,i,t)=>{"use strict";t.r(i),t.d(i,{ChartPropertyDefinitionsViewModel:()=>Do});var o=t(650151),n=t(609838),r=t(440891),l=t(912321),a=t(344557),s=t(240534),c=t(798346),d=t(363127),p=t(281738),u=t(599501),h=t(10792),v=t(71119),g=t(314254),y=t(272047),P=t(162172),f=t(419617),b=t(341991),w=t(3265),m=t(658895);const D=new y.TranslatedString("change symbol description visibility",n.t(null,void 0,t(288167))),S=new y.TranslatedString("change symbol legend format",n.t(null,void 0,t(300902))),T=new y.TranslatedString("change open market status visibility",n.t(null,void 0,t(196227))),C=new y.TranslatedString("change chart values visibility",n.t(null,void 0,t(479637))),L=new y.TranslatedString("change last day change visibility",n.t(null,void 0,t(66307))),k=new y.TranslatedString("change bar change visibility",n.t(null,void 0,t(927426))),_=new y.TranslatedString("change indicator arguments visibility",n.t(null,void 0,t(878310))),V=new y.TranslatedString("change indicator titles visibility",n.t(null,void 0,t(463050))),O=new y.TranslatedString("change indicator values visibility",n.t(null,void 0,t(749583))),R=new y.TranslatedString("change legend background visibility",n.t(null,void 0,t(514246))),E=new y.TranslatedString("change legend background transparency",n.t(null,void 0,t(391873))),W=new y.TranslatedString("change volume values visibility",n.t(null,void 0,t(796201))),M=new y.TranslatedString("change symbol field visibility",n.t(null,void 0,t(412050))),A=n.t(null,void 0,t(314876)),x=n.t(null,void 0,t(570198)),G=n.t(null,void 0,t(845639)),I=n.t(null,void 0,t(672423)),F=n.t(null,void 0,t(810842)),N=n.t(null,void 0,t(937644)),B=n.t(null,void 0,t(429854)),H=n.t(null,void 0,t(614119)),Z=n.t(null,void 0,t(660092)),j=n.t(null,void 0,t(379468)),z=n.t(null,void 0,t(825765)),U=n.t(null,void 0,t(384684)),K=r.enabled("symbol_info_price_source"),J=r.enabled("show_symbol_logos")&&r.enabled("show_symbol_logo_in_legend");var q=t(794941),Q=t(365343),Y=t(293029),X=t(692415),$=t(687378)
;const ee=new y.TranslatedString("change symbol labels visibility",n.t(null,void 0,t(147074))),ie=new y.TranslatedString("change symbol last value visibility",n.t(null,void 0,t(467453))),te=new y.TranslatedString("change symbol last value mode",n.t(null,void 0,t(746066))),oe=new y.TranslatedString("change bid and ask labels visibility",n.t(null,void 0,t(669362))),ne=new y.TranslatedString("change bid and ask lines visibility",n.t(null,void 0,t(952919))),re=new y.TranslatedString("change bid line color",n.t(null,void 0,t(517919))),le=new y.TranslatedString("change ask line color",n.t(null,void 0,t(298407))),ae=new y.TranslatedString("change pre/post market price label visibility",n.t(null,void 0,t(530870))),se=new y.TranslatedString("change pre/post market price lines visibility",n.t(null,void 0,t(791978))),ce=new y.TranslatedString("change pre market line color",n.t(null,void 0,t(96114))),de=new y.TranslatedString("change post market line color",n.t(null,void 0,t(628075))),pe=new y.TranslatedString("change high and low price labels visibility",n.t(null,void 0,t(524226))),ue=new y.TranslatedString("change high and low price lines visibility",n.t(null,void 0,t(180692))),he=new y.TranslatedString("change high and low price line color",n.t(null,void 0,t(361407))),ve=new y.TranslatedString("change high and low price line width",n.t(null,void 0,t(239581))),ge=(new y.TranslatedString("change indicators and financials name labels visibility",n.t(null,void 0,t(735111))),new y.TranslatedString("change indicators name labels visibility",n.t(null,void 0,t(824893)))),ye=(new y.TranslatedString("change indicators and financials value labels visibility",n.t(null,void 0,t(171161))),new y.TranslatedString("change indicators value labels visibility",n.t(null,void 0,t(64729)))),Pe=new y.TranslatedString("change no overlapping labels",n.t(null,void 0,t(561557))),fe=new y.TranslatedString("change countdown to bar close visibility",n.t(null,void 0,t(839383))),be=new y.TranslatedString("change currency label visibility",n.t(null,void 0,t(264003))),we=new y.TranslatedString("change scale modes buttons visibility",n.t(null,void 0,t(69023))),me=new y.TranslatedString("change unit label visibility",n.t(null,void 0,t(551250))),De=new y.TranslatedString("change currency and unit labels visibility",n.t(null,void 0,t(963119))),Se=new y.TranslatedString("change plus button visibility",n.t(null,void 0,t(796379))),Te=new y.TranslatedString("toggle lock scale",n.t(null,void 0,t(949695))),Ce=new y.TranslatedString("change price to bar ratio",n.t(null,void 0,t(702509))),Le=new y.TranslatedString("change date format",n.t(null,void 0,t(643109))),ke=new y.TranslatedString("change time hours format",n.t(null,void 0,t(439754))),_e=(new y.TranslatedString("change day of week on labels",n.t(null,void 0,t(730418))),
new y.TranslatedString("change save chart left edge position when changing interval",n.t(null,void 0,t(677408)))),Ve=new y.TranslatedString("change price line visibility",n.t(null,void 0,t(108662))),Oe=new y.TranslatedString("change price line color",n.t(null,void 0,t(787861))),Re=new y.TranslatedString("change price line width",n.t(null,void 0,t(429353))),Ee=new y.TranslatedString("change average close price label visibility",n.t(null,void 0,t(876852))),We=new y.TranslatedString("change average close price line visibility",n.t(null,void 0,t(1022))),Me=new y.TranslatedString("change average close price line color",n.t(null,void 0,t(947026))),Ae=new y.TranslatedString("change average close price line width",n.t(null,void 0,t(143231))),xe=new y.TranslatedString("change previous close price line visibility",n.t(null,void 0,t(958419))),Ge=new y.TranslatedString("change previous close price line color",n.t(null,void 0,t(569814))),Ie=new y.TranslatedString("change previous close price line width",n.t(null,void 0,t(613660))),Fe=new y.TranslatedString("change symbol previous close value visibility",n.t(null,void 0,t(904729))),Ne=n.t(null,void 0,t(399709)),Be=n.t(null,void 0,t(351514)),He=n.t(null,void 0,t(903554)),Ze=n.t(null,void 0,t(658589)),je=n.t(null,void 0,t(362142)),ze=n.t(null,void 0,t(595481)),Ue=n.t(null,void 0,t(968650)),Ke=n.t(null,void 0,t(683811)),Je=n.t(null,void 0,t(878082)),qe=n.t(null,void 0,t(960904)),Qe=n.t(null,void 0,t(614180)),Ye=n.t(null,void 0,t(76473)),Xe=n.t(null,void 0,t(583140)),$e=n.t(null,void 0,t(381849)),ei=n.t(null,void 0,t(726204)),ii=n.t(null,void 0,t(33564)),ti=n.t(null,void 0,t(3015)),oi=n.t(null,void 0,t(371566)),ni=n.t(null,void 0,t(230042)),ri=n.t(null,void 0,t(314017)),li=n.t(null,void 0,t(935082)),ai=n.t(null,void 0,t(584838)),si=n.t(null,void 0,t(205591)),ci=n.t(null,void 0,t(893965)),di=(n.t(null,void 0,t(542357)),n.t(null,void 0,t(408852))),pi=r.enabled("show_average_close_price_line_and_label"),ui=[{value:q.PriceAxisLastValueMode.LastPriceAndPercentageValue,title:n.t(null,void 0,t(827632))},{value:q.PriceAxisLastValueMode.LastValueAccordingToScale,title:n.t(null,void 0,t(731218))}];var hi=t(406722),vi=t(22982),gi=t(235918);const yi=new y.TranslatedString("change sessions breaks visibility",n.t(null,void 0,t(360067))),Pi=new y.TranslatedString("change sessions breaks color",n.t(null,void 0,t(633895))),fi=new y.TranslatedString("change sessions breaks width",n.t(null,void 0,t(728175))),bi=new y.TranslatedString("change sessions breaks style",n.t(null,void 0,t(621641))),wi=n.t(null,void 0,t(366707))
;const mi=new y.TranslatedString("change chart background color",n.t(null,void 0,t(42803))),Di=new y.TranslatedString("change chart background type",n.t(null,void 0,t(641382))),Si=new y.TranslatedString("change vert grid lines color",n.t(null,void 0,t(771805))),Ti=new y.TranslatedString("change horz grid lines color",n.t(null,void 0,t(221133))),Ci=new y.TranslatedString("change grid lines visibility",n.t(null,void 0,t(173844))),Li=new y.TranslatedString("change scales text color",n.t(null,void 0,t(976131))),ki=new y.TranslatedString("change scales font size",n.t(null,void 0,t(927792))),_i=new y.TranslatedString("change scales lines color",n.t(null,void 0,t(194997))),Vi=new y.TranslatedString("change pane separators color",n.t(null,void 0,t(152203))),Oi=new y.TranslatedString("change crosshair color",n.t(null,void 0,t(192885))),Ri=new y.TranslatedString("change crosshair width",n.t(null,void 0,t(250544))),Ei=new y.TranslatedString("change crosshair style",n.t(null,void 0,t(868418))),Wi=new y.TranslatedString("change symbol watermark visibility",n.t(null,void 0,t(573227))),Mi=new y.TranslatedString("change symbol watermark color",n.t(null,void 0,t(878995))),Ai=new y.TranslatedString("change navigation buttons visibility",n.t(null,void 0,t(503311))),xi=new y.TranslatedString("change pane buttons visibility",n.t(null,void 0,t(718378))),Gi=new y.TranslatedString("change top margin",n.t(null,void 0,t(674883))),Ii=new y.TranslatedString("change bottom margin",n.t(null,void 0,t(532094))),Fi=new y.TranslatedString("change right margin",n.t(null,void 0,t(82946))),Ni=new y.TranslatedString("change right margin percentage",n.t(null,void 0,t(579545))),Bi=n.t(null,void 0,t(379468)),Hi=n.t(null,void 0,t(683594)),Zi=n.t(null,void 0,t(808402)),ji=n.t(null,void 0,t(761900)),zi=n.t(null,void 0,t(260798)),Ui=n.t(null,void 0,t(968662)),Ki=n.t(null,void 0,t(770320)),Ji=n.t(null,void 0,t(156982)),qi=n.t(null,void 0,t(682894)),Qi=n.t(null,void 0,t(374622)),Yi=n.t(null,void 0,t(241571)),Xi=n.t(null,void 0,t(851019)),$i=n.t(null,void 0,t(253263)),et=n.t(null,void 0,t(697118)),it=n.t(null,void 0,t(327567)),tt=n.t(null,void 0,t(50421)),ot=n.t(null,void 0,t(586235)),nt=n.t(null,void 0,t(527377)),rt=n.t(null,{context:"unit"},t(480587));async function lt(e,i,o,l,a,d,p,u,h,v){const g=[],y=[],P=[],f=[],w=[],m=(0,c.createColorPropertyDefinition)({color:(0,c.getColorDefinitionProperty)(e,i.background,null,mi),gradientColor1:(0,c.getColorDefinitionProperty)(e,i.backgroundGradientStartColor,null,mi),gradientColor2:(0,c.getColorDefinitionProperty)(e,i.backgroundGradientEndColor,null,mi),type:(0,c.convertToDefinitionProperty)(e,i.backgroundType,Di)},{id:"chartBackground",title:Bi,noAlpha:!0}),D=i.vertGridProperties.childs(),S=i.horzGridProperties.childs(),T=(0,c.createOptionalTwoColorsPropertyDefinition)({option:(0,c.convertToDefinitionProperty)(e,i.gridLinesMode,Ci),color1:(0,c.getColorDefinitionProperty)(e,D.color,null,Si),color2:(0,c.getColorDefinitionProperty)(e,S.color,null,Ti)},{id:"gridLines",title:Ui,options:new s.WatchedValue([{title:Hi,
value:"both"},{title:Zi,value:"vert"},{title:ji,value:"horz"},{title:zi,value:"none"}]),color1Visible:v.vertLinesVisible,color2Visible:v.horzLinesVisible}),C=(0,b.createWVFromGetterAndSubscription)((()=>1!==e.model().panes().length),e.model().panesCollectionChanged()),L=(0,c.createLinePropertyDefinition)({visible:(0,c.convertFromReadonlyWVToDefinitionProperty)(C.ownership()),color:(0,c.getColorDefinitionProperty)(e,i.separatorColor,null,Vi)},{id:"paneSeparators",title:qi}),k=i.crossHairProperties.childs(),_=(0,c.createLinePropertyDefinition)({color:(0,c.getColorDefinitionProperty)(e,k.color,k.transparency,Oi),width:(0,c.convertToDefinitionProperty)(e,k.width,Ri),style:(0,c.convertToDefinitionProperty)(e,k.style,Ei)},{id:"crossHair",title:Qi});g.push(m,T);{const i=function(e){const i=e.model().sessions().properties().childs().sessionHighlight.childs().vertlines.childs().sessBreaks.childs(),t=(0,hi.combineProperty)((e=>!e),e.mainSeries().isDWMProperty().weakReference());return(0,gi.createLinePropertyDefinition)({visible:(0,vi.makeProxyDefinitionProperty)(t.ownership()),checked:(0,c.convertToDefinitionProperty)(e,i.visible,yi),color:(0,c.getColorDefinitionProperty)(e,i.color,null,Pi),width:(0,c.convertToDefinitionProperty)(e,i.width,fi),style:(0,c.convertToDefinitionProperty)(e,i.style,bi)},{id:"sessionBeaks",title:wi})}(e);g.push(i)}if(g.push(L,_),null!==o){const i=(0,c.createColorPropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,o.visibility,Wi),color:(0,c.getColorDefinitionProperty)(e,o.color,null,Mi)},{id:"watermark",title:Yi});g.push(i)}const V=(0,c.createTextPropertyDefinition)({color:(0,c.getColorDefinitionProperty)(e,l.textColor,null,Li),size:(0,c.convertToDefinitionProperty)(e,l.fontSize,ki)},{id:"scalesText",title:Ki}),O=(0,c.createLinePropertyDefinition)({color:(0,c.getColorDefinitionProperty)(e,l.lineColor,null,_i)},{id:"scalesLine",title:Ji});y.push(V,O);const R=(0,c.createOptionsPropertyDefinition)({option:(0,c.convertToDefinitionProperty)(e,d.property,Ai)},{id:"navButtons",title:Xi,options:new s.WatchedValue(d.values)}),E=(0,c.createOptionsPropertyDefinition)({option:(0,c.convertToDefinitionProperty)(e,p.property,xi)},{id:"paneButtons",title:$i,options:new s.WatchedValue(p.values)});P.push(R,E);const W=(0,c.createNumberPropertyDefinition)({value:(0,c.convertToDefinitionProperty)(e,i.topMargin,Gi,[Q.floor])},{type:0,id:"paneTopMargin",title:et,min:new s.WatchedValue(0),max:new s.WatchedValue(25),step:new s.WatchedValue(1),unit:new s.WatchedValue("%")}),M=(0,c.createNumberPropertyDefinition)({value:(0,c.convertToDefinitionProperty)(e,i.bottomMargin,Ii,[Q.floor])},{type:0,id:"paneBottomMargin",title:it,min:new s.WatchedValue(0),max:new s.WatchedValue(25),step:new s.WatchedValue(1),unit:new s.WatchedValue("%")});if(f.push(W,M),r.enabled("chart_property_page_right_margin_editor")){const i={value:(0,c.convertFromWVToDefinitionProperty)(e,a.value,Fi,[Q.floor])},t={type:0,id:"paneRightMargin",title:tt,min:a.min,max:a.max,step:new s.WatchedValue(1),unit:new s.WatchedValue(rt)}
;if(r.enabled("show_percent_option_for_right_margin")){const o=(0,c.createNumberPropertyDefinition)({...i,checked:(0,c.convertFromWVToDefinitionProperty)(e,h,Ni,[e=>!e,e=>!e])},{...t,title:nt}),n=(0,c.createNumberPropertyDefinition)({checked:(0,c.convertFromWVToDefinitionProperty)(e,h,Ni),value:(0,c.convertFromWVToDefinitionProperty)(e,u,Ni,[Q.floor])},{type:0,id:"paneRightMarginPercentage",title:ot,min:new s.WatchedValue(0),max:new s.WatchedValue(99),step:new s.WatchedValue(1),unit:new s.WatchedValue("%")});w.push(o),w.push(n)}else{const e=(0,c.createNumberPropertyDefinition)(i,t);f.push(e)}}const A=[(0,c.createPropertyDefinitionsGeneralGroup)(g,"chartBasicStylesAppearanceGroup",n.t(null,void 0,t(815153))),(0,c.createPropertyDefinitionsGeneralGroup)(y,"scalesAppearanceGroup",n.t(null,void 0,t(693968))),(0,c.createPropertyDefinitionsGeneralGroup)(P,"buttonsAppearanceGroup",n.t(null,void 0,t(332744))),(0,c.createPropertyDefinitionsGeneralGroup)(f,"marginsAppearanceGroup",n.t(null,void 0,t(570937)))];return w.length>0&&A.push((0,c.createPropertyDefinitionsGeneralGroup)(w,"rightMarginsAppearanceGroup",n.t(null,void 0,t(562532)))),{definitions:A}}var at=t(726757),st=t(922635),ct=t(389952),dt=t(814887),pt=t(972535),ut=t(509715),ht=t(865211);const vt=n.t(null,void 0,t(550913));const gt=new y.TranslatedString("change brackets PL visibility",n.t(null,void 0,t(825133))),yt=new y.TranslatedString("change brackets PL display mode",n.t(null,void 0,t(767967))),Pt=new y.TranslatedString("change positions PL visibility",n.t(null,void 0,t(237145))),ft=new y.TranslatedString("change positions PL display mode",n.t(null,void 0,t(13796))),bt=new y.TranslatedString("change reverse button visibility",n.t(null,void 0,t(97489))),wt=new y.TranslatedString("change order confirmation state",n.t(null,void 0,t(178204))),mt=new y.TranslatedString("change play sound on order execution",n.t(null,void 0,t(898093))),Dt=new y.TranslatedString("change notifications state",n.t(null,void 0,t(273925))),St=new y.TranslatedString("change positions visibility",n.t(null,void 0,t(651557))),Tt=new y.TranslatedString("change orders visibility",n.t(null,void 0,t(378980))),Ct=new y.TranslatedString("change executions visibility",n.t(null,void 0,t(888984))),Lt=new y.TranslatedString("change executions labels visibility",n.t(null,void 0,t(125842))),kt=new y.TranslatedString("change extend lines left",n.t(null,void 0,t(100249))),_t=new y.TranslatedString("change position trading objects on chart",n.t(null,void 0,t(802583))),Vt=(new y.TranslatedString("change trading objects visibility on screenshots",n.t(null,void 0,t(551336))),
n.t(null,void 0,t(563654))),Ot=n.t(null,void 0,t(353682)),Rt=n.t(null,void 0,t(752107)),Et=n.t(null,void 0,t(652280)),Wt=n.t(null,void 0,t(865669)),Mt=n.t(null,void 0,t(953802)),At=n.t(null,void 0,t(254402)),xt=n.t(null,void 0,t(75237)),Gt=n.t(null,void 0,t(826072)),It=n.t(null,void 0,t(53679)),Ft=n.t(null,void 0,t(109880)),Nt=n.t(null,void 0,t(407616)),Bt=n.t(null,void 0,t(256143)),Ht=n.t(null,void 0,t(270285)),Zt=n.t(null,void 0,t(824916)),jt=n.t(null,void 0,t(411626)),zt=n.t(null,void 0,t(224197)),Ut=n.t(null,void 0,t(50421)),Kt=n.t(null,void 0,t(548342)),Jt=n.t(null,void 0,t(528147)),qt=(n.t(null,void 0,t(537330)),[{value:l.TradedGroupHorizontalAlignment.Left,title:jt},{value:l.TradedGroupHorizontalAlignment.Center,title:zt},{value:l.TradedGroupHorizontalAlignment.Right,title:Ut}]),Qt=[{value:!1,title:Ft},{value:!0,title:Nt}];function Yt(e,i,t,o){const n=i.positionPL.childs(),l=(0,c.createOptionsPropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,n.visibility,Pt),option:(0,c.convertToDefinitionProperty)(e,n.display,ft)},{id:"positionPLDisplay",title:Ht,options:o}),a=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showReverse,bt)},{id:"reverseButton",title:pt.mobiletouch?Rt:Ot}),d=[],p=function(e,i){if(null===i||!r.enabled("buy_sell_buttons"))return null;const t=e.mainSeries().dataEvents(),o=(0,b.createWVFromGetterAndSubscriptions)((()=>"economic"===e.mainSeries().symbolInfo()?.type),[t.symbolResolved(),t.symbolError()]),n=(0,m.combine)(((e,i)=>e||i),o.ownership(),e.model().isInReplay().weakReference());return(0,c.createCheckablePropertyDefinition)({disabled:(0,c.convertFromReadonlyWVToDefinitionProperty)(n.ownership()),checked:(0,c.convertToDefinitionProperty)(e,(0,ut.getBuySellButtonsVisibility)(e.model()),ht.undoShowBuySellButtonsVisibility)},{id:"tradingSellBuyPanel",title:vt})}(e,t,e.model().properties()),u=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertFromWVToDefinitionProperty)(e,t.noConfirmEnabled,wt)},{id:"tradingConfirmEnabled",title:Gt});let h;h=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertFromWVToDefinitionProperty)(e,t.orderExecutedSoundParams.enabled,mt)},{id:"tradingSoundEnabled",title:Bt});const v=(0,c.createOptionsPropertyDefinition)({option:(0,c.convertFromWVToDefinitionProperty)(e,t.showOnlyRejectionNotifications,Dt)},{id:"tradingNotifications",title:It,options:new s.WatchedValue(Qt)});null!==p&&d.push(p),d.push(u),d.push(h),d.push(v);const g=(0,c.createPropertyDefinitionsGeneralGroup)(d,"generalVisibilityGroup",Kt),y=[],P=(0,c.createCheckableSetPropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showPositions,St)},{id:"tradingPositions",title:Vt},[l,a]);y.push(P);const f=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showOrders,Tt)},{id:"tradingOrders",title:Et});y.push(f);{const t=i.bracketsPL.childs(),n=(0,c.createOptionsPropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,t.visibility,gt),option:(0,c.convertToDefinitionProperty)(e,t.display,yt)
},{id:"bracketsPLDisplay",title:Wt,options:o});y.push(n)}const w=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showExecutionsLabels,Lt)},{id:"tradingExecutionsLables",title:xt}),D=(0,c.createCheckableSetPropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showExecutions,Ct)},{id:"tradingExecutions",title:At},[w]);y.push(D),y.push((0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.extendLeft,kt)},{id:"extendLeftTradingLine",title:Mt}),(0,c.createOptionsPropertyDefinition)({option:(0,c.convertToDefinitionProperty)(e,i.horizontalAlignment,_t)},{id:"positionPLDisplay",title:Zt,options:new s.WatchedValue(qt)}));return{definitions:[g,(0,c.createPropertyDefinitionsGeneralGroup)(y,"appearanceVisibilityGroup",Jt)]}}var Xt=t(599016),$t=t(155561),eo=t(853078),io=t(782038),to=t(384806),oo=t(887717),no=t(407621),ro=t(808021),lo=t(760339),ao=t(575709),so=t(520037);const co={symbol:{bold:ro,default:eo},legend:{bold:lo,default:io},scales:{bold:ao,default:to},canvas:{bold:t(897660),default:no},trading:{bold:so,default:oo}};const po=n.t(null,void 0,t(595481)),uo=n.t(null,void 0,t(928715)),ho=n.t(null,void 0,t(990275)),vo=n.t(null,void 0,t(923739)),go=(n.t(null,void 0,t(69808)),n.t(null,void 0,t(276495))),yo=(n.t(null,void 0,t(336620)),n.t(null,void 0,t(808249))),Po=n.t(null,void 0,t(394408)),fo=n.t(null,void 0,t(824821)),bo=r.enabled("chart_property_page_trading");let wo=null;bo&&(wo=(0,a.tradingService)());const mo=[{id:"symbol-text-source-description",value:"description",title:n.t(null,void 0,t(178734))},{id:"symbol-text-source-ticker",value:"ticker",title:n.t(null,void 0,t(905791))},{id:"symbol-text-source-ticker-and-description",value:"ticker-and-description",title:n.t(null,void 0,t(716816))}];r.enabled("symbol_info_long_description")&&mo.push({id:"symbol-text-source-long-description",value:"long-description",title:n.t(null,void 0,t(589315))});class Do{constructor(e,i,t){this._propertyPages=null,this._maxRightOffsetPropertyObject=null,this._defaultRightOffsetPercentageWatchedValue=null,this._useRightOffsetPercentageWatchedValue=null,this._profitLossOptions=null,this._isDestroyed=!1,this._availableDateFormatValues=null,this._undoModel=e,this._model=this._undoModel.model(),this._series=this._model.mainSeries(),this._chartWidgetProperties=i,this._options=t,this._seriesPropertyDefinitionViewModel=this._createSeriesViewModel();const o=this._chartWidgetProperties.childs().paneProperties.childs(),n=(0,b.createWVFromProperty)(o.gridLinesMode);this._gridColorsVisibilities={gridLinesMode:n,vertLinesVisible:(0,m.combine)((e=>"both"===e||"vert"===e),n.weakReference()),horzLinesVisible:(0,m.combine)((e=>"both"===e||"horz"===e),n.weakReference())},this._legendPropertyPage=this._createLegendPropertyPage(),this._scalesPropertyPage=this._createScalesPropertyPage(),this._appearancePropertyPage=this._createAppearancePropertyPage(),this._tradingPropertyPage=this._createTradingPropertyPage(),this._eventsPropertyPage=this._createEventsPropertyPage(),
this._alertsPropertyPage=this._createAlertsPropertyPage(),this._series.onStyleChanged().subscribe(this,this._updateDefinitions),this._series.priceScaleChanged().subscribe(this,this._updateDefinitions)}destroy(){null!==this._propertyPages&&this._propertyPages.filter(((e,i)=>0!==i)).forEach((e=>{(0,c.destroyDefinitions)(e.definitions.value())})),this._seriesPropertyDefinitionViewModel.destroy(),this._pipValueTypeSubscription?.unsubscribe(),this._availableDateFormatValues?.destroy(),this._series.onStyleChanged().unsubscribe(this,this._updateDefinitions),this._series.priceScaleChanged().unsubscribe(this,this._updateDefinitions);(0,o.ensureNotNull)(this._model.timeScale()).maxRightOffsetChanged().unsubscribeAll(this),this._gridColorsVisibilities.vertLinesVisible.destroy(),this._gridColorsVisibilities.horzLinesVisible.destroy(),this._gridColorsVisibilities.gridLinesMode.destroy(),this._isDestroyed=!0}async propertyPages(){if(null===this._propertyPages){const e=await this._seriesPropertyDefinitionViewModel.propertyPages();if(this._isDestroyed)throw new Error("ChartPropertyDefinitionsViewModel already destroyed");if(null===this._propertyPages){this._propertyPages=[...e],this._propertyPages.push(this._legendPropertyPage,this._scalesPropertyPage,await this._appearancePropertyPage),null!==this._tradingPropertyPage&&this._propertyPages.push(this._tradingPropertyPage);const i=await this._alertsPropertyPage;i&&this._propertyPages.push(i);const t=await this._eventsPropertyPage;t&&this._propertyPages.push(t)}return this._propertyPages}return Promise.resolve(this._propertyPages)}_updatePlDisplayOptions(e){(0,o.ensureNotNull)(this._profitLossOptions).setValue(function(e){return bo?[{value:l.PlDisplay.Money,title:yo},{value:l.PlDisplay.Pips,title:e===Xt.PipValueType.Pips?Po:fo},{value:l.PlDisplay.Percentage,title:"%"}]:[]}(e))}_updateDefinitions(){(0,c.destroyDefinitions)(this._scalesPropertyPage.definitions.value());const e=this._createScalesDefinitions();this._scalesPropertyPage.definitions.setValue(e.definitions)}_createSeriesViewModel(){const e={property:this._model.properties().childs().timezone,values:$t.availableTimezones.map((e=>({value:e.id,title:e.title})))};return new v.SeriesPropertyDefinitionsViewModel(this._series,this._undoModel,"symbol",po,(0,o.ensureDefined)(co.symbol),e)}_createLegendPropertyPage(){const e=this._chartWidgetProperties.childs().paneProperties.childs().legendProperties.childs(),i={property:this._series.properties().childs().statusViewStyle.childs().symbolTextSource,values:mo},r=function(e,i,o,r){const l=[],a=[];if(J){const t=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showLogo,T)},{id:"showLogo",title:A});a.push(t)}const d=(0,c.createOptionsPropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showSeriesTitle,D),option:(0,c.convertToDefinitionProperty)(e,o.property,S)},{id:"symbolTextSource",title:x,options:new s.WatchedValue(o.values)});if(a.push(d),null!==r){const i=(0,m.combineWithFilteredUpdate)(((i,t)=>"market"===i&&!(0,
P.isEconomicSymbol)(e.mainSeries().symbolInfo())),((e,i)=>null!==e),e.mainSeries().marketStatusModel().status().weakReference(),e.mainSeries().symbolResolvingActive().weakReference()),t=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,r,T),visible:(0,c.convertFromReadonlyWVToDefinitionProperty)(i.ownership())},{id:"showOpenMarketStatus",title:z});a.push(t)}const p=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showSeriesOHLC,C)},{id:"ohlcTitle",title:G});if(a.push(p),!f.alwaysShowLastPriceAndLastDayChange){const t=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showBarChange,k),visible:(0,c.makeProxyDefinitionProperty)((0,b.combineProperty)((e=>12!==e&&20!==e),e.mainSeries().properties().childs().style.weakReference()).ownership())},{id:"barChange",title:F});a.push(t)}if(a.push((0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showVolume,W),visible:(0,c.makeProxyDefinitionProperty)((0,b.combineProperty)((e=>20!==e),e.mainSeries().properties().childs().style.weakReference()).ownership())},{id:"barVolume",title:N})),f.lastDayChangeAvailable||f.alwaysShowLastPriceAndLastDayChange){const t=f.alwaysShowLastPriceAndLastDayChange?i.showBarChange:i.showLastDayChange,o=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,t,L),visible:(0,c.makeProxyDefinitionProperty)((0,b.combineProperty)((e=>12!==e&&20!==e),e.mainSeries().properties().childs().style.weakReference()).ownership())},{id:"lastDayChange",title:I});a.push(o)}if(K){const t=(0,m.combineWithFilteredUpdate)((()=>e.model().symbolSources().some((e=>void 0!==e.symbolInfo()?.price_source_id))),(e=>!e),e.model().symbolSourceResolvingActive().weakReference(),(0,b.createWVFromGetterAndSubscription)((()=>e.model().symbolSources().length),e.model().symbolSourceCollectionChanged()).ownership());a.push((0,c.createCheckablePropertyDefinition)({disabled:(0,c.convertFromReadonlyWVToDefinitionProperty)(e.model().symbolSourceResolvingActive().weakReference()),checked:(0,c.convertToDefinitionProperty)(e,i.showPriceSource,M),visible:(0,c.convertFromReadonlyWVToDefinitionProperty)(t.ownership())},{id:"priceSource",title:U}))}l.push((0,c.createPropertyDefinitionsGeneralGroup)(a,"seriesLegendVisibilityGroup",n.t(null,void 0,t(595481))));const u=[],h=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showStudyArguments,_)},{id:"studyArguments",title:H}),v=(0,c.createCheckableSetPropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showStudyTitles,V)},{id:"studyTitles",title:B},[h]),g=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showStudyValues,O)},{id:"studyValues",title:Z}),y=(0,b.createWVFromGetterAndSubscription)((()=>e.model().priceDataSources().some((e=>!(0,w.isActingAsSymbolSource)(e)&&e.showInObjectTree()))),e.model().dataSourceCollectionChanged());u.push(v,g),l.push((0,
c.createPropertyDefinitionsGeneralGroup)(u,"studiesLegendVisibilityGroup",n.t(null,void 0,t(184549)),y));const q=[],Q=(0,c.createTransparencyPropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showBackground,R),transparency:(0,c.convertToDefinitionProperty)(e,i.backgroundTransparency,E)},{id:"legendBgTransparency",title:j});return q.push(Q),l.push((0,c.createPropertyDefinitionsGeneralGroup)(q,"generalLegendGroup")),{definitions:l}}(this._undoModel,e,i,this._options.marketStatusWidgetEnabled?g.showMarketOpenStatusProperty:null);return(0,d.createPropertyPage)(r,"legend",uo,(0,o.ensureDefined)(co.legend))}_createScalesPropertyPage(){const e=this._createScalesDefinitions();return(0,d.createPropertyPage)(e,"scales",ho,(0,o.ensureDefined)(co.scales))}_createScalesDefinitions(){const e=this._chartWidgetProperties.childs().scalesProperties.childs(),i={property:this._model.properties().childs().priceScaleSelectionStrategyName,values:(0,h.allPriceScaleSelectionStrategyInfo)().map((e=>({value:e.name,title:e.title})))};null===this._availableDateFormatValues&&(this._availableDateFormatValues=new s.WatchedValue(function(e=!1){const i=new Date(Date.UTC(1997,8,29));return at.availableDateFormats.map((t=>({value:t,title:new ct.DateFormatter(t,e).format(i)})))}()).spawn());const o={property:dt.timeHoursFormatProperty,values:[{value:"24-hours",title:n.t(null,void 0,t(305797))},{value:"12-hours",title:n.t(null,void 0,t(231882))}]},l=this._model.mainSeriesScaleRatioProperty();return function(e,i,t,o){const n=o.seriesPriceScale.properties().childs(),l=[],a=[];if(o.currencyConversionEnabled||o.unitConversionEnabled){const i=o.currencyConversionEnabled&&o.unitConversionEnabled?ti:o.currencyConversionEnabled?$e:ii,t=o.currencyConversionEnabled&&o.unitConversionEnabled?De:o.currencyConversionEnabled?be:me,n=(0,c.createOptionsPropertyDefinition)({option:(0,c.convertToDefinitionProperty)(e,(0,X.currencyUnitVisibilityProperty)(),t)},{id:"scalesCurrencyUnit",title:i,options:new s.WatchedValue((0,X.currencyUnitVisibilityOptions)())});a.push(n)}const d=(0,c.createOptionsPropertyDefinition)({option:(0,c.convertToDefinitionProperty)(e,(0,$.autoLogButtonsVisibilityProperty)(),we)},{id:"autoLogButtonsVisibility",title:ei,options:new s.WatchedValue((0,$.autoLogButtonsVisibilityOptions)())});a.push(d);const p=(0,c.createNumberPropertyDefinition)({checked:(0,c.getLockPriceScaleDefinitionProperty)(e,n.lockScale,o.seriesPriceScale,Te),value:(0,c.getScaleRatioDefinitionProperty)(e,o.mainSeriesScaleRatioProperty,Ce,[(0,Q.limitedPrecision)(7),e=>e])},{id:"lockScale",title:ri,min:new s.WatchedValue(o.mainSeriesScaleRatioProperty.getMinValue()),max:new s.WatchedValue(o.mainSeriesScaleRatioProperty.getMaxValue()),step:new s.WatchedValue(o.mainSeriesScaleRatioProperty.getStepChangeValue())}),u=(0,c.createOptionsPropertyDefinition)({option:(0,c.getPriceScaleSelectionStrategyDefinitionProperty)(e,o.scalesPlacementPropertyObj.property)},{id:"scalesPlacement",title:ni,options:new s.WatchedValue(o.scalesPlacementPropertyObj.values)});a.push(p,u),
l.push((0,c.createPropertyDefinitionsGeneralGroup)(a,"scalesPriceScaleGroup",li));const h=[],v=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,n.alignLabels,Pe)},{id:"noOverlappingLabels",title:Ze});if(h.push(v),e.crosshairSource().isMenuEnabled()){const i=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,Y.addPlusButtonProperty,Se)},{id:"addPlusButton",title:oi,solutionId:void 0});h.push(i)}if(o.countdownEnabled){const t=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showCountdown,fe)},{id:"countdown",title:Xe});h.push(t)}if(o.seriesHasClosePrice){const o=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,t.showSymbolLabels,ee)},{id:"symbolNameLabel",title:Ne}),n=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,t.showSeriesLastValue,ie)},{id:"symbolValueLabel",title:Be}),r=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showPriceLine,Ve)},{id:"symbolLine",title:He}),l=(0,c.createLinePropertyDefinition)({color:(0,c.getColorDefinitionProperty)(e,i.priceLineColor,null,Oe),width:(0,c.convertToDefinitionProperty)(e,i.priceLineWidth,Re)},{id:"SymbolLastValuePriceLine",title:""}),a=(0,c.createOptionsPropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,t.showSeriesLastValue,ie),option:(0,c.convertToDefinitionProperty)(e,t.seriesLastValueMode,te)},{id:"symbolLastValueLabel",title:Be,options:new s.WatchedValue(ui)});h.push((0,c.createPropertyDefinitionsCheckableListOptionsGroup)([o,n,r],void 0,[l,a],"symbolCheckableListGroup",ze))}if(pi){const t=i.highLowAvgPrice.childs(),o=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,t.averageClosePriceLabelVisible,Ee)},{id:"averageClosePriceLabel",title:Be}),n=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,t.averageClosePriceLineVisible,We)},{id:"averageCloseLine",title:He}),r=(0,c.createLinePropertyDefinition)({color:(0,c.getColorDefinitionProperty)(e,t.averagePriceLineColor,null,Me),width:(0,c.convertToDefinitionProperty)(e,t.averagePriceLineWidth,Ae)},{id:"averageClosePriceLine",title:""});h.push((0,c.createPropertyDefinitionsCheckableListOptionsGroup)([o,n],void 0,[r],"avgCloseCheckableListGroup",Ue))}if(o.seriesHasClosePrice){const o=(0,b.combineProperty)((e=>!e),e.mainSeries().isDWMProperty().weakReference()),n=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,t.showSeriesPrevCloseValue,Fe),visible:(0,c.makeProxyDefinitionProperty)(o.ownership())},{id:"symbolPrevCloseValue",title:Be}),r=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,i.showPrevClosePriceLine,xe),visible:(0,c.makeProxyDefinitionProperty)(o.ownership())},{id:"symbolPrevCloseLine",title:He}),l=(0,c.createLinePropertyDefinition)({color:(0,c.getColorDefinitionProperty)(e,i.prevClosePriceLineColor,null,Ge),width:(0,
c.convertToDefinitionProperty)(e,i.prevClosePriceLineWidth,Ie)},{id:"prevClosePriceLine",title:""});h.push((0,c.createPropertyDefinitionsCheckableListOptionsGroup)([n,r],void 0,[l],"prevCloseCheckableListGroup",Ke))}let g,y;if(g=(0,c.createCheckablePropertyDefinition)({visible:(0,c.convertFromReadonlyWVToDefinitionProperty)((0,b.createWVFromGetterAndSubscription)((()=>e.model().priceDataSources().some((e=>!(0,w.isActingAsSymbolSource)(e)&&e.showInObjectTree()))),e.model().dataSourceCollectionChanged()).ownership()),checked:(0,c.convertToDefinitionProperty)(e,t.showStudyPlotLabels,ge)},{id:"studyNameLabel",title:Ne}),y=(0,c.createCheckablePropertyDefinition)({visible:(0,c.convertFromReadonlyWVToDefinitionProperty)((0,b.createWVFromGetterAndSubscription)((()=>e.model().priceDataSources().some((e=>!(0,w.isActingAsSymbolSource)(e)&&e.showInObjectTree()))),e.model().dataSourceCollectionChanged()).ownership()),checked:(0,c.convertToDefinitionProperty)(e,t.showStudyLastValue,ye)},{id:"studyLastValueLabel",title:Be}),h.push((0,c.createPropertyDefinitionsCheckableListOptionsGroup)([g,y],void 0,[],"studiesCheckableListGroup",Je)),r.enabled("pre_post_market_price_line")){const o=e.model().mainSeries(),n=(0,m.combineWithFilteredUpdate)(((e,i)=>i),((e,i)=>!e),o.symbolResolvingActive().weakReference(),(0,b.createWVFromProperty)(o.isPrePostMarketPricesAvailableProperty()).ownership()),r=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,t.showPrePostMarketPriceLabel,ae),visible:(0,c.convertFromReadonlyWVToDefinitionProperty)(n.ownership())},{id:"prePostMarketPriceLabel",title:Be}),l=i.prePostMarket,a=(0,c.createCheckablePropertyDefinition)({visible:(0,c.convertFromReadonlyWVToDefinitionProperty)(n.ownership()),checked:(0,c.convertToDefinitionProperty)(e,l.childs().visible,se)},{id:"prePostMarketPriceLine",title:He}),s=(0,c.createTwoColorsPropertyDefinition)({visible:(0,c.convertFromReadonlyWVToDefinitionProperty)(n.ownership()),color1:(0,c.getColorDefinitionProperty)(e,l.childs().preMarketColor,null,ce),color2:(0,c.getColorDefinitionProperty)(e,l.childs().postMarketColor,null,de)},{id:"prePostMarketColors",title:""});h.push((0,c.createPropertyDefinitionsCheckableListOptionsGroup)([r,a],void 0,[s],"prePostMarketCheckableListGroup",qe))}const P=i.highLowAvgPrice.childs(),f=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,P.highLowPriceLabelsVisible,pe)},{id:"highLowPriceLabels",title:Be}),D=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,P.highLowPriceLinesVisible,ue)},{id:"highLowPriceLine",title:He}),S=(0,c.createLinePropertyDefinition)({color:(0,c.getColorDefinitionProperty)(e,P.highLowPriceLinesColor,null,he),width:(0,c.convertToDefinitionProperty)(e,P.highLowPriceLinesWidth,ve)},{id:"highLowLineColors",title:""});if(h.push((0,c.createPropertyDefinitionsCheckableListOptionsGroup)([f,D],void 0,[S],"hiLowCheckableListGroup",Qe)),e.model().hasCustomSource("bidask")){const o=(0,c.createCheckablePropertyDefinition)({checked:(0,
c.convertToDefinitionProperty)(e,t.showBidAskLabels,oe)},{id:"bidAskLabels",title:Be}),n=i.bidAsk,r=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,n.childs().visible,ne)},{id:"bidAskLines",title:He}),l=(0,c.createTwoColorsPropertyDefinition)({color1:(0,c.getColorDefinitionProperty)(e,n.childs().bidLineColor,null,re),color2:(0,c.getColorDefinitionProperty)(e,n.childs().askLineColor,null,le)},{id:"bidAskLinesColors",title:""});h.push((0,c.createPropertyDefinitionsCheckableListOptionsGroup)([o,r],void 0,[l],"bidAskCheckableListGroup",Ye))}l.push((0,c.createPropertyDefinitionsGeneralGroup)(h,"scalesLabelsLineGroup",je));const T=[];if(r.enabled("scales_date_format")){const i=(0,c.createOptionsPropertyDefinition)({option:(0,c.convertToDefinitionProperty)(e,o.dateFormatPropertyObj.property,Le)},{id:"dateFormat",title:ai,options:o.dateFormatPropertyObj.values});T.push(i)}if(r.enabled("scales_time_hours_format")){const i=(0,c.createOptionsPropertyDefinition)({option:(0,c.convertToDefinitionProperty)(e,o.timeHoursFormatPropertyObj.property,ke)},{id:"timeHoursFormat",title:si,options:new s.WatchedValue(o.timeHoursFormatPropertyObj.values)});T.push(i)}{const i=(0,c.createCheckablePropertyDefinition)({checked:(0,c.convertToDefinitionProperty)(e,o.saveLeftEdgeProperty,_e)},{id:"saveLeftChartEdge",title:di});T.push(i)}return T.length>0&&l.push((0,c.createPropertyDefinitionsGeneralGroup)(T,"scalesTimeScaleGroup",ci)),{definitions:l}}(this._undoModel,this._series.properties().childs(),e,{disableSeriesPrevCloseValueProperty:this._series.isDWMProperty(),seriesHasClosePrice:this._series.hasClosePrice(),seriesPriceScale:this._series.priceScale(),mainSeriesScaleRatioProperty:l,scalesPlacementPropertyObj:i,dateFormatPropertyObj:{property:st.dateFormatProperty,values:this._availableDateFormatValues},timeHoursFormatPropertyObj:o,currencyConversionEnabled:this._options.currencyConversionEnabled,unitConversionEnabled:this._options.unitConversionEnabled,countdownEnabled:this._options.countdownEnabled,withWeekdayProperty:void 0,saveLeftEdgeProperty:e.saveLeftEdge})}_createMaxOffsetPropertyObject(){const e=(0,o.ensureNotNull)(this._model.timeScale()),i=new s.WatchedValue(Math.floor(e.maxRightOffset()));e.maxRightOffsetChanged().subscribe(this,(e=>{i.setValue(Math.floor(e))})),this._maxRightOffsetPropertyObject={value:e.defaultRightOffset(),min:new s.WatchedValue(0),max:i}}_createDefaultRightOffsetPercentageWatchedValue(){const e=(0,o.ensureNotNull)(this._model.timeScale());this._defaultRightOffsetPercentageWatchedValue=e.defaultRightOffsetPercentage()}_createUseRightOffsetPercentageWatchedValue(){const e=(0,o.ensureNotNull)(this._model.timeScale());this._useRightOffsetPercentageWatchedValue=e.usePercentageRightOffset()}async _createAppearancePropertyPage(){const e=this._chartWidgetProperties.childs(),i=e.paneProperties.childs(),t=e.scalesProperties.childs(),n=this._model.watermarkSource();let r=null;null!==n&&(r=n.properties().childs());const l={property:p.property(),values:p.availableValues()},a={
property:u.property(),values:u.availableValues()};null===this._maxRightOffsetPropertyObject&&this._createMaxOffsetPropertyObject(),null===this._defaultRightOffsetPercentageWatchedValue&&this._createDefaultRightOffsetPercentageWatchedValue(),null===this._useRightOffsetPercentageWatchedValue&&this._createUseRightOffsetPercentageWatchedValue();const s=(0,o.ensureNotNull)(this._maxRightOffsetPropertyObject),c=(0,o.ensureNotNull)(this._defaultRightOffsetPercentageWatchedValue),h=(0,o.ensureNotNull)(this._useRightOffsetPercentageWatchedValue),v=await lt(this._undoModel,i,r,t,s,l,a,c,h,this._gridColorsVisibilities);return(0,d.createPropertyPage)(v,"canvas",vo,(0,o.ensureDefined)(co.canvas))}_createTradingPropertyPage(){if(!bo)return null;if(null===wo)return null;this._profitLossOptions=new s.WatchedValue,this._pipValueTypeSubscription=wo.pipValueType().subscribe(this._updatePlDisplayOptions.bind(this));const e=this._model.properties().childs().tradingProperties.childs(),i=Yt(this._undoModel,e,wo,this._profitLossOptions);return(0,d.createPropertyPage)(i,"trading",go,(0,o.ensureDefined)(co.trading))}async _createEventsPropertyPage(){return null}_createAlertsPropertyPage(){return null}}},599016:(e,i,t)=>{"use strict";var o,n,r,l,a,s,c,d,p,u,h,v,g,y,P,f,b,w,m,D,S,T,C,L,k,_,V;t.d(i,{AccountType:()=>S,BracketType:()=>y,DisconnectType:()=>T,OrderOrPositionMessageType:()=>D,PipValueType:()=>C,RestrictionType:()=>L,TradingEntityType:()=>h}),function(e){e[e.CONNECTED=1]="CONNECTED",e[e.CONNECTING=2]="CONNECTING",e[e.DISCONNECTED=3]="DISCONNECTED",e[e.ERROR=4]="ERROR"}(o||(o={})),function(e){e[e.Connected=1]="Connected",e[e.Connecting=2]="Connecting",e[e.Disconnected=3]="Disconnected",e[e.Error=4]="Error"}(n||(n={})),function(e){e[e.LIMIT=1]="LIMIT",e[e.MARKET=2]="MARKET",e[e.STOP=3]="STOP",e[e.STOPLIMIT=4]="STOPLIMIT"}(r||(r={})),function(e){e[e.Limit=1]="Limit",e[e.Market=2]="Market",e[e.Stop=3]="Stop",e[e.StopLimit=4]="StopLimit"}(l||(l={})),function(e){e[e.BUY=1]="BUY",e[e.SELL=-1]="SELL"}(a||(a={})),function(e){e[e.Buy=1]="Buy",e[e.Sell=-1]="Sell"}(s||(s={})),function(e){e[e.CANCELED=1]="CANCELED",e[e.FILLED=2]="FILLED",e[e.INACTIVE=3]="INACTIVE",e[e.PLACING=4]="PLACING",e[e.REJECTED=5]="REJECTED",e[e.WORKING=6]="WORKING"}(c||(c={})),function(e){e[e.ALL=0]="ALL",e[e.CANCELED=1]="CANCELED",e[e.FILLED=2]="FILLED",e[e.INACTIVE=3]="INACTIVE",e[e.REJECTED=5]="REJECTED",e[e.WORKING=6]="WORKING"}(d||(d={})),function(e){e[e.Canceled=1]="Canceled",e[e.Filled=2]="Filled",e[e.Inactive=3]="Inactive",e[e.Placing=4]="Placing",e[e.Rejected=5]="Rejected",e[e.Working=6]="Working"}(p||(p={})),function(e){e[e.All=0]="All",e[e.Canceled=1]="Canceled",e[e.Filled=2]="Filled",e[e.Inactive=3]="Inactive",e[e.Rejected=5]="Rejected",e[e.Working=6]="Working"}(u||(u={})),function(e){e[e.Order=1]="Order",e[e.Position=2]="Position"}(h||(h={})),function(e){e[e.ORDER=1]="ORDER",e[e.POSITION=2]="POSITION"}(v||(v={})),function(e){e[e.Order=1]="Order",e[e.Position=2]="Position",e[e.IndividualPosition=3]="IndividualPosition"}(g||(g={})),function(e){
e[e.StopLoss=0]="StopLoss",e[e.TakeProfit=1]="TakeProfit",e[e.TrailingStop=2]="TrailingStop",e[e.GuaranteedStop=3]="GuaranteedStop"}(y||(y={})),function(e){e[e.LIMITPRICE=1]="LIMITPRICE",e[e.STOPPRICE=2]="STOPPRICE",e[e.TAKEPROFIT=3]="TAKEPROFIT",e[e.STOPLOSS=4]="STOPLOSS"}(P||(P={})),function(e){e[e.LimitPrice=1]="LimitPrice",e[e.StopPrice=2]="StopPrice",e[e.TakeProfit=3]="TakeProfit",e[e.StopLoss=4]="StopLoss",e[e.Quantity=5]="Quantity"}(f||(f={})),function(e){e[e.ERROR=0]="ERROR",e[e.SUCCESS=1]="SUCCESS"}(b||(b={})),function(e){e[e.Error=0]="Error",e[e.Success=1]="Success"}(w||(w={})),function(e){e[e.Demo=1]="Demo",e[e.Real=0]="Real"}(m||(m={})),function(e){e.Information="information",e.Warning="warning",e.Error="error"}(D||(D={})),function(e){e.Demo="demo",e.Live="live"}(S||(S={})),function(e){e[e.LogOut=0]="LogOut",e[e.FailedRestoring=1]="FailedRestoring",e[e.Offline=2]="Offline",e[e.APIError=3]="APIError",e[e.TwoFactorRequired=4]="TwoFactorRequired",e[e.CancelAuthorization=5]="CancelAuthorization",e[e.TimeOutForAuthorization=6]="TimeOutForAuthorization",e[e.OauthError=7]="OauthError",e[e.BrokenConnection=8]="BrokenConnection",e[e.Reconnect=9]="Reconnect",e[e.FailedSignIn=10]="FailedSignIn"}(T||(T={})),function(e){e[e.None=0]="None",e[e.Pips=1]="Pips",e[e.Ticks=2]="Ticks"}(C||(C={})),function(e){e.Halted="HALTED",e.NotShortable="NOT-SHORTABLE",e.HardToBorrow="HARD-TO-BORROW"}(L||(L={})),function(e){e[e.Limit=1]="Limit",e[e.Stop=2]="Stop"}(k||(k={})),function(e){e.Disallowed="disallowed",e.Allowed="allowed",e.AllowedWithWarning="allowed_with_warning"}(_||(_={})),function(e){e.PlaceOrder="place_order",e.ModifyOrder="modify_order",e.CancelOrder="cancel_order",e.ModifyPosition="modify_position",e.ClosePosition="close_position",e.ModifyIndividualPosition="modify_individual_position",e.CloseIndividualPosition="close_individual_position",e.CloseNetPosition="close_net_position"}(V||(V={}))},509715:(e,i,t)=>{"use strict";function o(e){return e.properties().childs().paneProperties.childs().legendProperties.childs().showTradingButtons}t.d(i,{getBuySellButtonsVisibility:()=>o})},865211:(e,i,t)=>{"use strict";t.d(i,{setBuySellButtonsVisibility:()=>a,undoShowBuySellButtonsVisibility:()=>l});var o=t(609838),n=t(272047),r=t(509715);const l=new n.TranslatedString("change buy/sell buttons visibility",o.t(null,void 0,t(605598)));function a(e,i){e.setProperty((0,r.getBuySellButtonsVisibility)(e.model()),i,l)}},808021:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M12 7h-.75V4h-1.5v3H9a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h.75v3h1.5v-3H12a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1ZM9.5 19.5v-11h2v11h-2Zm8-3v-5h2v5h-2Zm.24-6.5H17a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h.75v3h1.5v-3H20a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1h-.76V7h-1.5v3Z"/></svg>'},897660:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-width="1.5" d="m7.5 16.5-1 1v4h4l1-1m-4-4 9-9m-9 9 4 4m0 0 9-9m-4-4 .59-.59a2 2 0 0 1 2.82 0L21.1 8.1a2 2 0 0 1 0 2.82l-.59.59m-4-4 2 2 2 2"/></svg>'},575709:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-width="1.5" d="M10.5 20.5a2 2 0 1 1-2-2m2 2a2 2 0 0 0-2-2m2 2h14m-16-2v-14m16 16L21 17m3.5 3.5L21 24M8.5 4.5 12 8M8.5 4.5 5 8"/></svg>'},760339:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M7 7.5h14a.5.5 0 0 1 0 1H7a.5.5 0 0 1 0-1ZM5 8c0-1.1.9-2 2-2h14a2 2 0 1 1 0 4H7a2 2 0 0 1-2-2Zm13 5H6v1.5h12V13ZM6 17h12v1.5H6V17Zm12 4H6v1.5h12V21Z"/></svg>'},520037:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m17.53 15.08.45.33.44-.33 6.65-4.92a2.3 2.3 0 0 0 .42-3.3 2.35 2.35 0 0 0-3.23-.4l-4.28 3.18-4.29-3.18a2.35 2.35 0 0 0-3.22.4 2.3 2.3 0 0 0 .42 3.3l6.64 4.92Zm6.64-6.1-6.2 4.59-6.19-4.6a.83.83 0 0 1-.15-1.18.85.85 0 0 1 1.17-.15l4.73 3.51.45.33.44-.33 4.74-3.5a.85.85 0 0 1 1.16.14c.3.37.23.9-.15 1.19Zm-13.7 3.94-.45-.33-.44.33-6.65 4.92a2.3 2.3 0 0 0-.42 3.3 2.35 2.35 0 0 0 3.23.4l4.28-3.18 4.29 3.18c1 .75 2.44.57 3.22-.4a2.3 2.3 0 0 0-.42-3.3l-6.64-4.92Zm-6.64 6.1 6.2-4.59 6.19 4.6c.38.27.45.81.15 1.18a.85.85 0 0 1-1.17.15l-4.73-3.51-.45-.33-.44.33-4.74 3.5a.85.85 0 0 1-1.16-.14.83.83 0 0 1 .15-1.19Z"/></svg>'},853078:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M11 4h-1v3H8.5a.5.5 0 0 0-.5.5v13a.5.5 0 0 0 .5.5H10v3h1v-3h1.5a.5.5 0 0 0 .5-.5v-13a.5.5 0 0 0-.5-.5H11V4ZM9 8v12h3V8H9Zm10-1h-1v3h-1.5a.5.5 0 0 0-.5.5v7a.5.5 0 0 0 .5.5H18v3h1v-3h1.5a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.5-.5H19V7Zm-2 10v-6h3v6h-3Z"/></svg>'},407621:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M16.73 6.56a2.5 2.5 0 0 1 3.54 0l1.17 1.17a2.5 2.5 0 0 1 0 3.54l-.59.58-9 9-1 1-.14.15H6v-4.7l.15-.15 1-1 9-9 .58-.59Zm2.83.7a1.5 1.5 0 0 0-2.12 0l-.23.24 3.29 3.3.23-.24a1.5 1.5 0 0 0 0-2.12l-1.17-1.17Zm.23 4.24L16.5 8.2l-8.3 8.3 3.3 3.3 8.3-8.3Zm-9 9L7.5 17.2l-.5.5V21h3.3l.5-.5Z"/></svg>'},384806:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M10.5 20.5a2 2 0 1 1-2-2m2 2a2 2 0 0 0-2-2m2 2h14m-16-2v-14m16 16L21 17m3.5 3.5L21 24M8.5 4.5L12 8M8.5 4.5L5 8"/></svg>'},782038:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M7 7h14a1 1 0 1 1 0 2H7a1 1 0 0 1 0-2ZM5 8c0-1.1.9-2 2-2h14a2 2 0 1 1 0 4H7a2 2 0 0 1-2-2Zm13 5H6v1h12v-1Zm0 4H6v1h12v-1ZM6 21h12v1H6v-1Z"/></svg>'},887717:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M12.8441 8.61921C13.232 8.13425 13.9481 8.07567 14.4097 8.49112L18.1651 11.871L18.4996 12.172L18.8341 11.871L22.5896 8.49121C23.0512 8.07582 23.7672 8.13438 24.1551 8.61927C24.5188 9.07382 24.4567 9.73484 24.0147 10.1137L18.4996 14.8409L12.9845 10.1137C12.5425 9.73482 12.4804 9.07379 12.8441 8.61921ZM15.0787 7.74783C14.1896 6.94765 12.8104 7.06048 12.0632 7.99452C11.3628 8.87007 11.4824 10.1432 12.3338 10.8729L18.1742 15.879L18.4996 16.158L18.825 15.879L24.6655 10.8729C25.5168 10.1432 25.6364 8.87006 24.936 7.99454C24.1888 7.06061 22.8097 6.94781 21.9207 7.7479L18.4996 10.8267L15.0787 7.74783ZM15.1551 18.8798C14.7672 19.3647 14.0511 19.4233 13.5895 19.0078L9.83409 15.628L9.49962 15.3269L9.16514 15.6279L5.4096 19.0077C4.94802 19.4231 4.23205 19.3646 3.84411 18.8797C3.48044 18.4251 3.54256 17.7641 3.98455 17.3853L9.49961 12.6581L15.0147 17.3853C15.4567 17.7641 15.5188 18.4252 15.1551 18.8798ZM12.9205 19.7511C13.8096 20.5513 15.1888 20.4385 15.936 19.5044C16.6364 18.6289 16.5168 17.3557 15.6655 16.626L9.82501 11.6199L9.49961 11.341L9.17421 11.6199L3.33376 16.626C2.48244 17.3557 2.3628 18.6289 3.06327 19.5044C3.81047 20.4383 5.1895 20.5512 6.07854 19.7511L9.4996 16.6723L12.9205 19.7511Z"/></svg>'}}]);