"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3378],{600303:(e,r,t)=>{t.r(r),t.d(r,{LineToolHeadAndShoulders:()=>s});var o=t(792535),n=t(889868),i=t(981856);class s extends n.LineDataSource{constructor(e,r,o,n){super(e,r??s.createProperties(e.backgroundTheme().spawnOwnership()),o,n),Promise.all([t.e(6290),t.e(6881),t.e(5579),t.e(1583)]).then(t.bind(t,773271)).then((r=>{this._setPaneViews([new r.LineToolHeadAndShouldersPaneView(this,e)])}))}pointsCount(){return 7}name(){return"Head and Shoulders"}static createProperties(e,r){const t=new o.DefaultProperty({defaultName:"linetoolheadandshoulders",state:r,theme:e});return this._configureProperties(t),t}_getPropertyDefinitionsViewModelClass(){return Promise.all([t.e(6406),t.e(8511),t.e(5234),t.e(4590),t.e(8537)]).then(t.bind(t,809863)).then((e=>e.PatternWithBackgroundDefinitionViewModel))}static _configureProperties(e){super._configureProperties(e),e.addChild("linesColors",new i.LineToolColorsProperty([e.childs().color])),e.addChild("textsColors",new i.LineToolColorsProperty([e.childs().textcolor]))}}}}]);