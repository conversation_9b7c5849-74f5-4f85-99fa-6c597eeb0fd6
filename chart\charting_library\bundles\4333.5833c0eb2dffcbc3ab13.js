(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4333],{497754:(e,t)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var l=typeof r;if("string"===l||"number"===l)e.push(r);else if(Array.isArray(r)&&r.length){var o=i.apply(null,r);o&&e.push(o)}else if("object"===l)for(var a in r)n.call(r,a)&&r[a]&&e.push(a)}}return e.join(" ")}e.exports?(i.default=i,e.exports=i):void 0===(r=function(){return i}.apply(t,[]))||(e.exports=r)}()},188317:e=>{e.exports={pills:"pills-PVWoXu5j",primary:"primary-PVWoXu5j",gray:"gray-PVWoXu5j",selected:"selected-PVWoXu5j",grouped:"grouped-PVWoXu5j",active:"active-PVWoXu5j",disableActiveOnTouch:"disableActiveOnTouch-PVWoXu5j",disableActiveStateStyles:"disableActiveStateStyles-PVWoXu5j",withGrouped:"withGrouped-PVWoXu5j","quiet-primary":"quiet-primary-PVWoXu5j",green:"green-PVWoXu5j",red:"red-PVWoXu5j",blue:"blue-PVWoXu5j",secondary:"secondary-PVWoXu5j",ghost:"ghost-PVWoXu5j"}},601538:e=>{e.exports={lightButton:"lightButton-bYDQcOkp",link:"link-bYDQcOkp",ltr:"ltr-bYDQcOkp",rtl:"rtl-bYDQcOkp","typography-regular16px":"typography-regular16px-bYDQcOkp","typography-medium16px":"typography-medium16px-bYDQcOkp","typography-regular14px":"typography-regular14px-bYDQcOkp","typography-semibold14px":"typography-semibold14px-bYDQcOkp","typography-semibold16px":"typography-semibold16px-bYDQcOkp",content:"content-bYDQcOkp",visuallyHidden:"visuallyHidden-bYDQcOkp",nowrap:"nowrap-bYDQcOkp",ellipsisContainer:"ellipsisContainer-bYDQcOkp",textWrapContainer:"textWrapContainer-bYDQcOkp",textWrapWithEllipsis:"textWrapWithEllipsis-bYDQcOkp",slot:"slot-bYDQcOkp",caret:"caret-bYDQcOkp",activeCaret:"activeCaret-bYDQcOkp",xsmall:"xsmall-bYDQcOkp",withStartSlot:"withStartSlot-bYDQcOkp",withEndSlot:"withEndSlot-bYDQcOkp",noContent:"noContent-bYDQcOkp",wrap:"wrap-bYDQcOkp",small:"small-bYDQcOkp",medium:"medium-bYDQcOkp"}},959189:(e,t,r)=>{"use strict";function n(e,t){return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}r.d(t,{isIconOnly:()=>n})},898237:(e,t,r)=>{"use strict";r.d(t,{LightAnchorButton:()=>m,LightButton:()=>i.LightButton});var n=r(418920),i=r(943158),l=r(50959),o=r(591365),a=r(273388),s=r(854797),c=r(601538),u=r.n(c),p=r(188317),d=r.n(p);function m(e){const{className:t,isSelected:r,children:i,iconOnly:c,ellipsis:p,showCaret:m,forceDirection:y,endSlot:h,startSlot:g,color:f,variant:x,reference:b,size:v,enableActiveStateStyles:S,renderComponent:C=o.CustomComponentDefaultLink,typography:w,textWrap:O=!1,maxLines:k,style:D={},isActive:W,isPills:E,...A}=e,L=O?k??2:1,P=L>0?{...D,"--ui-lib-light-button-content-max-lines":L}:D;return l.createElement(C,{...A,className:(0,n.useLightButtonClasses)({...d(),...u()},{className:t,isSelected:r,isActive:W,isPills:E,children:i,iconOnly:c,showCaret:m,forceDirection:y,color:f,variant:x,size:v,enableActiveStateStyles:S,typography:w,textWrap:O,isLink:!0,endSlot:h,startSlot:g}),reference:(0,a.isomorphicRef)(b),style:P},l.createElement(s.LightButtonContent,{
showCaret:m,isActiveCaret:m&&(E||W||r),iconOnly:c,ellipsis:p,textWrap:O,endSlot:h,startSlot:g},i))}r(15378)},418920:(e,t,r)=>{"use strict";r.d(t,{useLightButtonClasses:()=>c});var n=r(50959),i=r(497754),l=r(234539),o=r(959189),a=r(380327);const s=n.createContext({isInButtonGroup:!1,isGroupPrimary:!1}),c=(e,t,r)=>{const c=(0,n.useContext)(l.CustomBehaviourContext),{className:u,isSelected:p,children:d,showCaret:m,forceDirection:y,iconOnly:h,color:g="gray",variant:f="primary",size:x="medium",enableActiveStateStyles:b=c.enableActiveStateStyles,typography:v,isLink:S=!1,textWrap:C,isPills:w,isActive:O,startSlot:k,endSlot:D}=t,W=e[`typography-${((e,t,r)=>{if(r){const e=r.replace(/^\D+/g,"");return t?`semibold${e}`:r}switch(e){case"xsmall":return t?"semibold14px":"regular14px";case"small":case"medium":return t?"semibold16px":"regular16px";default:return""}})(x,p||w,v||void 0)}`],E=(0,n.useContext)(a.ControlGroupContext),{isInButtonGroup:A,isGroupPrimary:L}=(0,n.useContext)(s);return i(u,e.lightButton,S&&e.link,O&&e.active,p&&e.selected,(0,o.isIconOnly)(d,h)&&e.noContent,!!k&&e.withStartSlot,(m||!!D)&&e.withEndSlot,r&&e.withGrouped,y&&e[y],e[L?"primary":f],e[L?"gray":g],e[x],W,!b&&e.disableActiveStateStyles,E.isGrouped&&e.grouped,C&&e.wrap,A&&e.disableActiveOnTouch,w&&e.pills)}},854797:(e,t,r)=>{"use strict";r.d(t,{LightButtonContent:()=>d});var n=r(50959),i=r(497754),l=r(601198),o=r(959189),a=r(878112),s=r(602948),c=r(601538),u=r.n(c);const p=e=>n.createElement(a.Icon,{className:i(u().caret,e&&u().activeCaret),icon:s});function d(e){const{showCaret:t,iconOnly:r,ellipsis:a=!0,textWrap:s,tooltipText:c,children:d,endSlot:m,startSlot:y,isActiveCaret:h}=e;[m,t].filter((e=>!!e));return n.createElement(n.Fragment,null,y&&n.createElement("span",{className:i(u().slot,u().startSlot)},y),!(0,o.isIconOnly)(d,r)&&n.createElement("span",{className:i(u().content,!s&&u().nowrap,"apply-overflow-tooltip","apply-overflow-tooltip--check-children-recursively","apply-overflow-tooltip--allow-text"),"data-overflow-tooltip-text":c??(0,l.getTextForTooltip)(d)},s||a?n.createElement(n.Fragment,null,n.createElement("span",{className:i(!s&&a&&u().ellipsisContainer,s&&u().textWrapContainer,s&&a&&u().textWrapWithEllipsis)},d),n.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},d)):n.createElement(n.Fragment,null,d,n.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},d))),m&&n.createElement("span",{className:i(u().slot,u().endSlot)},m),t&&p(h))}},943158:(e,t,r)=>{"use strict";r.d(t,{LightButton:()=>p});var n=r(50959),i=r(380327),l=r(418920),o=r(854797),a=r(601538),s=r.n(a),c=r(188317),u=r.n(c);function p(e){const{isGrouped:t}=n.useContext(i.ControlGroupContext),{reference:r,className:a,isSelected:c,children:p,iconOnly:d,ellipsis:m,showCaret:y,forceDirection:h,endSlot:g,startSlot:f,color:x,variant:b,size:v,enableActiveStateStyles:S,typography:C,textWrap:w=!1,maxLines:O,style:k={},isPills:D,isActive:W,tooltipText:E,role:A,...L}=e,P=w?O??2:1,Q=P>0?{...k,"--ui-lib-light-button-content-max-lines":P}:k
;return n.createElement("button",{...L,className:(0,l.useLightButtonClasses)({...u(),...s()},{className:a,isSelected:c,children:p,iconOnly:d,showCaret:y,forceDirection:h,endSlot:g,startSlot:f,color:x,variant:b,size:v,enableActiveStateStyles:S,typography:C,textWrap:w,isPills:D,isActive:W},t),ref:r,style:Q,role:A},n.createElement(o.LightButtonContent,{showCaret:y,isActiveCaret:y&&(D||W||c),iconOnly:d,ellipsis:m,textWrap:w,tooltipText:E,endSlot:g,startSlot:f},p))}},15378:(e,t,r)=>{"use strict";var n,i,l,o;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(n||(n={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(i||(i={})),function(e){e.Brand="brand",e.Blue="blue",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(l||(l={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(o||(o={}))},380327:(e,t,r)=>{"use strict";r.d(t,{ControlGroupContext:()=>n});const n=r(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},409245:(e,t,r)=>{"use strict";function n(e){const{reference:t,...r}=e;return{...r,ref:t}}r.d(t,{renameRef:()=>n})},591365:(e,t,r)=>{"use strict";r.d(t,{CustomComponentDefaultLink:()=>l});var n=r(50959),i=r(409245);function l(e){return n.createElement("a",{...(0,i.renameRef)(e)})}n.PureComponent},234539:(e,t,r)=>{"use strict";r.d(t,{CustomBehaviourContext:()=>n});const n=(0,r(50959).createContext)({enableActiveStateStyles:!0});n.displayName="CustomBehaviourContext"},718736:(e,t,r)=>{"use strict";r.d(t,{useFunctionalRefObject:()=>l});var n=r(50959),i=r(855393);function l(e){const t=(0,n.useMemo)((()=>function(e){const t=r=>{e(r),t.current=r};return t.current=null,t}((e=>{a.current(e)}))),[]),r=(0,n.useRef)(null),l=t=>{if(null===t)return o(r.current,t),void(r.current=null);r.current!==e&&(r.current=e,o(r.current,t))},a=(0,n.useRef)(l);return a.current=l,(0,i.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return a.current(t.current),()=>a.current(null)}),[e]),t}function o(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},855393:(e,t,r)=>{"use strict";r.d(t,{useIsomorphicLayoutEffect:()=>i});var n=r(50959);function i(e,t){("undefined"==typeof window?n.useEffect:n.useLayoutEffect)(e,t)}},183787:(e,t,r)=>{"use strict";r.d(t,{Icon:()=>i});var n=r(50959);const i=n.forwardRef(((e,t)=>{const{icon:r="",title:i,ariaLabel:l,ariaLabelledby:o,ariaHidden:a,...s}=e,c=!!(i||l||o);return n.createElement("span",{role:"img",...s,ref:t,"aria-label":l,"aria-labelledby":o,"aria-hidden":a||!c,title:i,dangerouslySetInnerHTML:{__html:r}})}))},878112:(e,t,r)=>{"use strict";r.d(t,{Icon:()=>n.Icon});var n=r(183787)},601198:(e,t,r)=>{"use strict";r.d(t,{getTextForTooltip:()=>o});var n=r(50959)
;const i=e=>(0,n.isValidElement)(e)&&Boolean(e.props.children),l=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",o=e=>Array.isArray(e)||(0,n.isValidElement)(e)?n.Children.toArray(e).reduce(((e,t)=>{let r="";return r=(0,n.isValidElement)(t)&&i(t)?o(t.props.children):(0,n.isValidElement)(t)&&!i(t)?"":l(t),e.concat(r)}),"").trim():l(e)},273388:(e,t,r)=>{"use strict";function n(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function i(e){return n([e])}r.d(t,{isomorphicRef:()=>i,mergeRefs:()=>n})},602948:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.92 7.83 9 12.29l5.08-4.46-1-1.13L9 10.29l-4.09-3.6-.99 1.14Z"/></svg>'}}]);