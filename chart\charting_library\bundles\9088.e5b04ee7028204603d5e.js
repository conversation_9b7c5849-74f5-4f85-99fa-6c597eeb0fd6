(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9088],{953633:e=>{e.exports={wrapper:"wrapper-k5swolgQ",text:"text-k5swolgQ"}},739390:(e,t,n)=>{"use strict";n.d(t,{OfflineScreen:()=>u,renderOfflineScreen:()=>s});var r=n(50959),o=n(632227),i=n(609838),a=n(953633);function u(){return r.createElement("div",{className:a.wrapper},r.createElement("p",{className:a.text},i.t(null,void 0,n(905301))))}function s(e){o.render(r.createElement(u,null),e)}},599016:(e,t,n)=>{"use strict";var r,o,i,a,u,s,c,d,l,p,f,T,S,y,m,g,k,v,E,P,O,b,I,C,h,B,D;n.d(t,{AccountType:()=>O,BracketType:()=>y,DisconnectType:()=>b,OrderOrPositionMessageType:()=>P,PipValueType:()=>I,RestrictionType:()=>C,TradingEntityType:()=>f}),function(e){e[e.CONNECTED=1]="CONNECTED",e[e.CONNECTING=2]="CONNECTING",e[e.DISCONNECTED=3]="DISCONNECTED",e[e.ERROR=4]="ERROR"}(r||(r={})),function(e){e[e.Connected=1]="Connected",e[e.Connecting=2]="Connecting",e[e.Disconnected=3]="Disconnected",e[e.Error=4]="Error"}(o||(o={})),function(e){e[e.LIMIT=1]="LIMIT",e[e.MARKET=2]="MARKET",e[e.STOP=3]="STOP",e[e.STOPLIMIT=4]="STOPLIMIT"}(i||(i={})),function(e){e[e.Limit=1]="Limit",e[e.Market=2]="Market",e[e.Stop=3]="Stop",e[e.StopLimit=4]="StopLimit"}(a||(a={})),function(e){e[e.BUY=1]="BUY",e[e.SELL=-1]="SELL"}(u||(u={})),function(e){e[e.Buy=1]="Buy",e[e.Sell=-1]="Sell"}(s||(s={})),function(e){e[e.CANCELED=1]="CANCELED",e[e.FILLED=2]="FILLED",e[e.INACTIVE=3]="INACTIVE",e[e.PLACING=4]="PLACING",e[e.REJECTED=5]="REJECTED",e[e.WORKING=6]="WORKING"}(c||(c={})),function(e){e[e.ALL=0]="ALL",e[e.CANCELED=1]="CANCELED",e[e.FILLED=2]="FILLED",e[e.INACTIVE=3]="INACTIVE",e[e.REJECTED=5]="REJECTED",e[e.WORKING=6]="WORKING"}(d||(d={})),function(e){e[e.Canceled=1]="Canceled",e[e.Filled=2]="Filled",e[e.Inactive=3]="Inactive",e[e.Placing=4]="Placing",e[e.Rejected=5]="Rejected",e[e.Working=6]="Working"}(l||(l={})),function(e){e[e.All=0]="All",e[e.Canceled=1]="Canceled",e[e.Filled=2]="Filled",e[e.Inactive=3]="Inactive",e[e.Rejected=5]="Rejected",e[e.Working=6]="Working"}(p||(p={})),function(e){e[e.Order=1]="Order",e[e.Position=2]="Position"}(f||(f={})),function(e){e[e.ORDER=1]="ORDER",e[e.POSITION=2]="POSITION"}(T||(T={})),function(e){e[e.Order=1]="Order",e[e.Position=2]="Position",e[e.IndividualPosition=3]="IndividualPosition"}(S||(S={})),function(e){e[e.StopLoss=0]="StopLoss",e[e.TakeProfit=1]="TakeProfit",e[e.TrailingStop=2]="TrailingStop",e[e.GuaranteedStop=3]="GuaranteedStop"}(y||(y={})),function(e){e[e.LIMITPRICE=1]="LIMITPRICE",e[e.STOPPRICE=2]="STOPPRICE",e[e.TAKEPROFIT=3]="TAKEPROFIT",e[e.STOPLOSS=4]="STOPLOSS"}(m||(m={})),function(e){e[e.LimitPrice=1]="LimitPrice",e[e.StopPrice=2]="StopPrice",e[e.TakeProfit=3]="TakeProfit",e[e.StopLoss=4]="StopLoss",e[e.Quantity=5]="Quantity"}(g||(g={})),function(e){e[e.ERROR=0]="ERROR",e[e.SUCCESS=1]="SUCCESS"}(k||(k={})),function(e){e[e.Error=0]="Error",e[e.Success=1]="Success"}(v||(v={})),function(e){e[e.Demo=1]="Demo",e[e.Real=0]="Real"}(E||(E={})),function(e){e.Information="information",e.Warning="warning",e.Error="error"}(P||(P={})),
function(e){e.Demo="demo",e.Live="live"}(O||(O={})),function(e){e[e.LogOut=0]="LogOut",e[e.FailedRestoring=1]="FailedRestoring",e[e.Offline=2]="Offline",e[e.APIError=3]="APIError",e[e.TwoFactorRequired=4]="TwoFactorRequired",e[e.CancelAuthorization=5]="CancelAuthorization",e[e.TimeOutForAuthorization=6]="TimeOutForAuthorization",e[e.OauthError=7]="OauthError",e[e.BrokenConnection=8]="BrokenConnection",e[e.Reconnect=9]="Reconnect",e[e.FailedSignIn=10]="FailedSignIn"}(b||(b={})),function(e){e[e.None=0]="None",e[e.Pips=1]="Pips",e[e.Ticks=2]="Ticks"}(I||(I={})),function(e){e.Halted="HALTED",e.NotShortable="NOT-SHORTABLE",e.HardToBorrow="HARD-TO-BORROW"}(C||(C={})),function(e){e[e.Limit=1]="Limit",e[e.Stop=2]="Stop"}(h||(h={})),function(e){e.Disallowed="disallowed",e.Allowed="allowed",e.AllowedWithWarning="allowed_with_warning"}(B||(B={})),function(e){e.PlaceOrder="place_order",e.ModifyOrder="modify_order",e.CancelOrder="cancel_order",e.ModifyPosition="modify_position",e.ClosePosition="close_position",e.ModifyIndividualPosition="modify_individual_position",e.CloseIndividualPosition="close_individual_position",e.CloseNetPosition="close_net_position"}(D||(D={}))},46415:(e,t,n)=>{"use strict";var r,o,i,a,u;n.d(t,{ConnectWarningMessageDisplayType:()=>a,StopType:()=>r}),function(e){e[e.StopLoss=0]="StopLoss",e[e.TrailingStop=1]="TrailingStop",e[e.GuaranteedStop=2]="GuaranteedStop"}(r||(r={})),function(e){e.Stocks="stocks",e.Futures="futures",e.Forex="forex",e.Crypto="crypto",e.Others="others"}(o||(o={})),function(e){e.Symbol="symbol"}(i||(i={})),function(e){e[e.PopUp=0]="PopUp",e[e.Notification=1]="Notification"}(a||(a={})),function(e){e.Quantity="qty",e.OrderSide="side",e.Price="price",e.Duration="duration",e.Brackets="brackets",e.StopLossType="slType"}(u||(u={}))},525915:(e,t,n)=>{"use strict";n.d(t,{getErrorCauses:()=>C,isFinalOrderStatus:()=>O,makeNonTradableSymbolText:()=>I,orderStatusToText:()=>m,orderTypeToText:()=>v,positionSideToText:()=>b,roundToStepByPriceTypeAndSide:()=>E,roundUpToPowerOf10:()=>P,sideToText:()=>k});var r=n(609838),o=n(960521),i=n(671945),a=n(599016);(0,i.getLogger)("Trading.Utils");var u,s=n(46415),c=(n(41899),n(209595),n(404357));!function(e){e[e.Full=1]="Full",e[e.Short=2]="Short"}(u||(u={}));const d={2:{},1:{}},l={2:{},1:{}},p={},f={},T={};let S=!1;(0,i.getLogger)("Trading.Utils");var y;!function(e){e[e.Unauthorized=401]="Unauthorized",e[e.Forbidden=403]="Forbidden",e[e.TooManyRequests=429]="TooManyRequests"}(y||(y={}));r.t(null,void 0,n(947870)),r.t(null,void 0,n(282478));function m(e){return g(),T[e]}function g(){S||(S=!0,d[2][2]=r.t(null,{context:"Market order"},n(66133)),d[2][1]=r.t(null,{context:"Limit order"},n(852506)),d[2][3]=r.t(null,{context:"order"},n(952240)),d[2][4]=r.t(null,{context:"Stop limit order"},n(108701)),d[1][2]=r.t(null,void 0,n(490138)),d[1][1]=r.t(null,void 0,n(790258)),d[1][3]=r.t(null,{context:"order"},n(679953)),d[1][4]=r.t(null,void 0,n(264073)),l[2][a.BracketType.TakeProfit]=r.t(null,{context:"Take profit order"},n(615678)),l[2][a.BracketType.StopLoss]=r.t(null,{
context:"Stop loss order"},n(54331)),l[2][a.BracketType.TrailingStop]=r.t(null,{context:"Trailing stop order"},n(454129)),l[2][a.BracketType.GuaranteedStop]=r.t(null,{context:"Guaranteed stop order"},n(697459)),l[1][a.BracketType.TakeProfit]=r.t(null,void 0,n(255739)),l[1][a.BracketType.StopLoss]=r.t(null,void 0,n(719702)),l[1][a.BracketType.TrailingStop]=r.t(null,void 0,n(992201)),l[1][a.BracketType.GuaranteedStop]=r.t(null,void 0,n(337539)),p[1]=r.t(null,{context:"trading"},n(418083)),p[-1]=r.t(null,{context:"trading"},n(327232)),f[1]=r.t(null,{context:"trading"},n(90737)),f[-1]=r.t(null,{context:"trading"},n(193865)),T[2]=r.t(null,void 0,n(417672)),T[1]=r.t(null,void 0,n(542451)),T[6]=r.t(null,void 0,n(654565)),T[3]=r.t(null,void 0,n(956995)),T[4]=r.t(null,void 0,n(840882)),T[5]=r.t(null,void 0,n(218136)))}function k(e,t){g();const n=p[e];return t?n.toUpperCase():n}function v(e){const{orderType:t,uppercase:n,shorten:r,parentId:o,stopType:i}=e;g();const u=r?2:1;let c=d,p=t;if(void 0!==o){if(c=l,3===t)switch(i){case s.StopType.TrailingStop:p=a.BracketType.TrailingStop;break;case s.StopType.GuaranteedStop:p=a.BracketType.GuaranteedStop;break;default:p=a.BracketType.StopLoss}1===t&&(p=a.BracketType.TakeProfit)}return n?c[u][p].toUpperCase():c[u][p]}function E(e,t,n,r){const i=(0,o.Big)(e).div(t);return 1===n&&1===r||2===n&&-1===r?i.round(0,0).mul(t).toNumber():1===n&&-1===r||2===n&&1===r?i.round(0,3).mul(t).toNumber():0}r.t(null,void 0,n(679453));function P(e){const t=Math.ceil(Math.log10(e));return(0,o.Big)(10).pow(t).toNumber()}function O(e){return-1!==[2,1,5].indexOf(e)}function b(e){return g(),f[e]}function I(e,t){return r.t(null,void 0,n(41553)).replace("{symbol}",e).replace("{broker}",t)}a.BracketType.StopLoss,r.t(null,void 0,n(719702)),a.BracketType.TakeProfit,r.t(null,void 0,n(255739)),a.BracketType.TrailingStop,r.t(null,void 0,n(992201)),a.BracketType.GuaranteedStop,r.t(null,void 0,n(337539)),a.BracketType.StopLoss,a.BracketType.TakeProfit,a.BracketType.TrailingStop,a.BracketType.GuaranteedStop;new Set(["date","dateOrDateTime","default","fixed","variablePrecision","formatQuantity","formatPrice","formatPriceForexSup","integerSeparated","localDate","localDateOrDateTime","percentage","pips","profit","profitInInstrumentCurrency","side","positionSide","status","symbol","text","type","marginPercent","empty"]);function C(e){return(0,c.isUserFriendlyError)(e)&&void 0!==e.cause?[...C(e.cause),e.cause]:[e]}var h;!function(e){e[e.Success=200]="Success",e[e.Error=500]="Error"}(h||(h={}))},28033:(e,t,n)=>{"use strict";n.d(t,{checkIsDOMAvailable:()=>o});var r=n(440891);function o(){return r.enabled("dom_widget")&&!0}},391431:(e,t,n)=>{"use strict";n.d(t,{addAsciiDotIfTextDoesNotEndWithSentenceEndingMark:()=>re,adjustSavedCustomFieldsValues:()=>U,alignToMinTick:()=>ie,applyRounding:()=>Z,bottomTradingTabClassName:()=>T,brokersListFromPlans:()=>y,checkIsExistingPosition:()=>J,convertActionDescriptionsToActions:()=>k,executionText:()=>g,filterDurationsByOrderType:()=>F,filterDurationsBySymbolDurations:()=>x,
findDurationMetaInfo:()=>A,formatValue:()=>$,getAsk:()=>Q,getBid:()=>q,getBracketTypeByStopType:()=>ue,getCryptoBalanceValue:()=>ee,getCurrency:()=>O,getDefaultOrderType:()=>G,getLast:()=>H,getOrderDuration:()=>M,getOrderPrice:()=>te,getPriceStep:()=>j,getQuotePrice:()=>K,getStopTypeByBracketType:()=>ae,getSymbolNameOverFullname:()=>se,getTimestamp:()=>W,isBatsQuotes:()=>D,isBrokerSupportOrderModification:()=>I,isDefined:()=>L,isMintickMultiple:()=>_,isModifyOrderSupported:()=>C,isModifyPositionBracketsSupported:()=>h,isMoveOrderSupported:()=>B,isNoQuotes:()=>V,isOAuthAuthType:()=>m,isOrderActive:()=>v,isOrderTypeAllowed:()=>oe,makeBrokerSideMaintananceFeatureToggleName:()=>E,makeDatePlus24UTCHours:()=>N,makeInitialOrderDuration:()=>R,makeMaintananceFeatureToggleName:()=>P,makeOrderDuration:()=>w,orderStatusToText:()=>f.orderStatusToText,parseValue:()=>X,roundToStepRequired:()=>z,safeSplitSymbol:()=>ce});var r=n(960521),o=n(650151),i=n(41899),a=n(440891),u=(n(601227),n(46415)),s=n(599016),c=n(737891),d=n(621327),l=n(14654),p=n(989546),f=n(525915);const T="js-bottom-trading-tab",S=p.defaultQuantityFormatter;function y(e,t){const n=new Map(e.map((e=>[e.id,e]))),r=new Map(t.map((e=>[e.slug_name,e]))),i=[{metainfo:(0,o.ensureDefined)(n.get("Paper"))}];return t.forEach((e=>{n.has(e.slug_name)&&i.push({metainfo:(0,o.ensureDefined)(n.get(e.slug_name)),brokerPlan:e})})),e.forEach((e=>{r.has(e.id)||"Paper"===e.id||i.push({metainfo:e})})),i}function m(e){return void 0!==e&&["oauth","oauth2-implicit-flow","oauth2-code-flow"].includes(e)}function g(e,t){const n=(0,f.sideToText)(e.side)+" "+S.format(e.qty)+" @ "+t.format(e.price);return n.substring(0,1).toUpperCase()+n.substring(1).toLowerCase()}function k(e,t){return e?e.map((e=>"-"===e.text||e.separator?new d.Separator:new c.ActionWithStandardIcon({actionId:"Trading.CustomActionId",options:{name:e.name,checkable:e.checkable,checked:e.checked,disabled:void 0!==e.enabled&&!e.enabled,label:e.text,statName:e.statName,icon:e.icon,iconId:e.iconId,shortcutHint:e.shortcutHint,onExecute:n=>{const r=n.getState();t?.(),e.action({checkable:r.checkable,checked:r.checked,enabled:!r.disabled,text:r.label})}}}))):[]}function v(e){return 6===e||3===e}function E(e){return`${e}-brokers-side-maintenance`.toLowerCase()}function P(e){return`${e}-maintenance`.toLowerCase()}function O(e,t){return!t&&e.currencySign||e.currency||""}function b(e,t){if(void 0===e.parentId)return!1;const n=t.supportTrailingStop&&t.supportModifyTrailingStop||e.stopType!==u.StopType.TrailingStop,r=1===e.parentType?t.supportModifyOrderBrackets:t.supportModifyPositionBrackets;return Boolean(t.supportModifyBrackets&&r&&n)}function I(e){return e.supportModifyOrderPrice||e.supportEditAmount||e.supportModifyBrackets||!1}function C(e,t){const n=2!==e.type&&void 0===e.parentId&&I(t),r=b(e,t);return n||r}function h(e){return Boolean(e.supportModifyBrackets&&e.supportModifyPositionBrackets)}function B(e,t){const n=void 0===e.parentId&&t.supportModifyOrderPrice,r=b(e,t);return Boolean(n||r)}function D(e){
return"BATS"===e.originalName?.split(":")[0]}function L(e){return null!=e}function N(){const e=new Date;return e.setUTCHours(e.getUTCHours()+24),e}function A(e,t){return e.find((e=>e.value===t))}function M(e){const{orderDuration:t,orderType:n,savedDuration:r,orderDurations:o,symbolDurations:i}=e;if(void 0!==t)return t;const a=function(e){const{duration:t,orderDurations:n,orderType:r,symbolDurations:o}=e;if(null===t||void 0===n)return null;const i=x(n,o),a=F(i,r??null),u=A(a,t.type);if(void 0===u)return null;if(void 0!==t.datetime&&(u.hasDatePicker||u.hasTimePicker)){const e=864e5,n=u.hasTimePicker?t.datetime<Date.now():Math.floor((t.datetime-Date.now())/e)<0;t.datetime=n?N().getTime():t.datetime}return t}({duration:r,orderType:n,orderDurations:o,symbolDurations:i});return null!==a?{...a}:R(n,o,i)}function R(e,t,n){if(void 0===t)return null;const r=F(x(t,n),e);if(0===r.length)return null;return w(r.find((e=>e.default))??r[0])}function w(e){const t={type:e.value};return Boolean(e.hasTimePicker||e.hasDatePicker)&&(t.datetime=W(N())),t}function x(e,t){return 0===e.length||void 0===t||0===t.length?e:e.filter((({value:e})=>t.includes(e)))}function F(e,t){const n=[1,3,4];return e.filter((e=>{const r=e.supportedOrderTypes??n;return null===t||r.includes(t)}))}function W(e){return e.valueOf()}function U(e,t){if(void 0===t.customFields)return{};const n={};return t.customFields.forEach((t=>{const r="ComboBox"===t.inputType;if(r&&t.forceUserEnterInitialValue)return;const o=r?t.items[0].value:t.value,i=e[t.id]??o;void 0!==i&&(n[t.id]=i)})),n}function G(e){return e.supportLimitOrders?1:e.supportMarketOrders?2:e.supportStopLimitOrders?4:e.supportStopOrders?3:void 0}function _(e,t){if(0===t)return!1;const n=Math.round(1e15*t)/1e15,o=new r.Big(e),i=new r.Big(n);return o.mod(i).eq(0)}function Q(e){return(0,i.isNumber)(e.ask)?e.ask:(0,i.isNumber)(e.trade)?e.trade:0}function q(e){return(0,i.isNumber)(e.bid)?e.bid:(0,i.isNumber)(e.trade)?e.trade:0}function H(e){return(0,i.isNumber)(e.trade)?e.trade:0}function V(e){return null===e||void 0===e.ask||void 0===e.bid}function K(e,t){return 1===t?Q(e):q(e)}function j(e){const{priceType:t,minTick:n,price:r,variableMinTickData:o,limitPriceStep:i,stopPriceStep:a}=e;return 1===t&&void 0!==i?i:2===t&&void 0!==a?a:void 0!==o&&void 0!==r?(0,l.getMinTick)({minTick:n,price:r,variableMinTickData:o}):n}function z(e){const{priceType:t,minTick:n,limitPriceStep:r,stopPriceStep:o}=e;return 1===t&&void 0!==r?r!==n:2===t&&void 0!==o&&o!==n}function J(e){return void 0!==e&&(0!==e.qty||0!==e.longQty||0!==e.shortQty)}var Y;function $(e,t){return null!==e?t.format(e):""}function X(e,t){return t.parse?t.parse(e):{res:!1,error:"Formatter does not support parse"}}function Z(e,t){if(null===e)return null;const n=t.format(e);if(t.parse){const e=t.parse(n);if(e.res)return e.value}return parseFloat(n)}function ee({balance:e,...t}){return null===e?null:function(e){const{side:t,isExistingOrder:n,qty:o,orderPrice:i}=e;return n&&o&&i?-1===t?new r.Big(o):new r.Big(o).mul(i):new r.Big(0)}({...t}).plus(e.available).toNumber()}
function te(e,t){switch(e.type){case 1:case 4:return e.limitPrice;case 3:return e.stopPrice;default:return K(t,e.side)}}!function(e){e.DesktopWeb="Desktop Web",e.DesktopApp="Desktop App",e.IPhoneWeb="iPhone Web",e.IPhoneApp="iPhone App",e.IPadWeb="iPad Web",e.IPadApp="iPad App",e.AndroidWeb="Android Web",e.AndroidApp="Android App",e.UnknownMobileWeb="Unknown Mobile Web"}(Y||(Y={}));const ne={visible:[".","｡","。","!","?","？","！"],invisible:["︀","︁"]};function re(e){const t=e.trim(),n=t.slice(-1),r=ne.invisible.includes(n)?t.charAt(t.length-2):n;return ne.visible.includes(r)?t:t+"."}function oe(e,t){return void 0===t||0===t.length||t.includes(e)}function ie(e,t){return t>1?e:(0,r.Big)(e).div(t).round(0,1).mul(t).toNumber()}function ae(e){return e===s.BracketType.TrailingStop?u.StopType.TrailingStop:e===s.BracketType.StopLoss?u.StopType.StopLoss:e===s.BracketType.GuaranteedStop?u.StopType.GuaranteedStop:void 0}function ue(e){switch(e){case u.StopType.StopLoss:return s.BracketType.StopLoss;case u.StopType.TrailingStop:return s.BracketType.TrailingStop;case u.StopType.GuaranteedStop:return s.BracketType.GuaranteedStop}}function se(e){return a.enabled("prefer_symbol_name_over_fullname")?ce(e)[1]:e}function ce(e){const[t,n]=e.split(":");return[t??"",n??t]}},404357:(e,t,n)=>{"use strict";n.d(t,{UserFriendlyError:()=>a,getErrorMessage:()=>s,getLoggerMessage:()=>u,isUserFriendlyError:()=>i});var r=n(609838),o=n(871645);function i(e){return e instanceof a}class a extends Error{constructor({detailedErrorMessage:e,userFriendlyMessage:t,cause:n}){super(t),this.name="UserFriendlyError",this.detailedErrorMessage=e,this.cause=n}}function u(e){return e instanceof a?(0,o.removeTags)(e.detailedErrorMessage):s(e)}function s(e){if(void 0===e)return r.t(null,void 0,n(411768));let t;return t=e instanceof Error?e.message:"object"==typeof e?JSON.stringify(e):e.toString(),(0,o.removeTags)(t)}}}]);