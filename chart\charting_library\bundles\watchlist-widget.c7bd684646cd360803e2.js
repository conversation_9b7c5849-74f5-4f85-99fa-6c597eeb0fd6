(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1196,9937,4333],{497754:(e,t)=>{var n;!function(){"use strict";var s={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)&&n.length){var r=o.apply(null,n);r&&e.push(r)}else if("object"===i)for(var l in n)s.call(n,l)&&n[l]&&e.push(l)}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},188317:e=>{e.exports={pills:"pills-PVWoXu5j",primary:"primary-PVWoXu5j",gray:"gray-PVWoXu5j",selected:"selected-PVWoXu5j",grouped:"grouped-PVWoXu5j",active:"active-PVWoXu5j",disableActiveOnTouch:"disableActiveOnTouch-PVWoXu5j",disableActiveStateStyles:"disableActiveStateStyles-PVWoXu5j",withGrouped:"withGrouped-PVWoXu5j","quiet-primary":"quiet-primary-PVWoXu5j",green:"green-PVWoXu5j",red:"red-PVWoXu5j",blue:"blue-PVWoXu5j",secondary:"secondary-PVWoXu5j",ghost:"ghost-PVWoXu5j"}},601538:e=>{e.exports={lightButton:"lightButton-bYDQcOkp",link:"link-bYDQcOkp",ltr:"ltr-bYDQcOkp",rtl:"rtl-bYDQcOkp","typography-regular16px":"typography-regular16px-bYDQcOkp","typography-medium16px":"typography-medium16px-bYDQcOkp","typography-regular14px":"typography-regular14px-bYDQcOkp","typography-semibold14px":"typography-semibold14px-bYDQcOkp","typography-semibold16px":"typography-semibold16px-bYDQcOkp",content:"content-bYDQcOkp",visuallyHidden:"visuallyHidden-bYDQcOkp",nowrap:"nowrap-bYDQcOkp",ellipsisContainer:"ellipsisContainer-bYDQcOkp",textWrapContainer:"textWrapContainer-bYDQcOkp",textWrapWithEllipsis:"textWrapWithEllipsis-bYDQcOkp",slot:"slot-bYDQcOkp",caret:"caret-bYDQcOkp",activeCaret:"activeCaret-bYDQcOkp",xsmall:"xsmall-bYDQcOkp",withStartSlot:"withStartSlot-bYDQcOkp",withEndSlot:"withEndSlot-bYDQcOkp",noContent:"noContent-bYDQcOkp",wrap:"wrap-bYDQcOkp",small:"small-bYDQcOkp",medium:"medium-bYDQcOkp"}},811362:e=>{e.exports={wrapper:"wrapper-GZajBGIm",input:"input-GZajBGIm",view:"view-GZajBGIm",danger:"danger-GZajBGIm"}},4052:e=>{e.exports={box:"box-ywH2tsV_",noOutline:"noOutline-ywH2tsV_",disabled:"disabled-ywH2tsV_","intent-danger":"intent-danger-ywH2tsV_",checked:"checked-ywH2tsV_",check:"check-ywH2tsV_",icon:"icon-ywH2tsV_",dot:"dot-ywH2tsV_",disableActiveStyles:"disableActiveStyles-ywH2tsV_"}},865592:e=>{e.exports={checkbox:"checkbox-vyj6oJxw",reverse:"reverse-vyj6oJxw",label:"label-vyj6oJxw",baseline:"baseline-vyj6oJxw"}},78217:e=>{e.exports={pair:"pair-ocURKVwI",xxceptionallysmalldonotusebrv1023:"xxceptionallysmalldonotusebrv1023-ocURKVwI",xxxxsmall:"xxxxsmall-ocURKVwI",xxxsmall:"xxxsmall-ocURKVwI",xxsmall:"xxsmall-ocURKVwI",xsmall:"xsmall-ocURKVwI",small:"small-ocURKVwI",medium:"medium-ocURKVwI",large:"large-ocURKVwI",xlarge:"xlarge-ocURKVwI",xxlarge:"xxlarge-ocURKVwI",xxxlarge:"xxxlarge-ocURKVwI",logo:"logo-ocURKVwI",skeleton:"skeleton-ocURKVwI",empty:"empty-ocURKVwI"}},456057:e=>{e.exports={logo:"logo-PsAlMQQF",hidden:"hidden-PsAlMQQF",
xxceptionallysmalldonotusebrv1023:"xxceptionallysmalldonotusebrv1023-PsAlMQQF",xxxsmall:"xxxsmall-PsAlMQQF",xxsmall:"xxsmall-PsAlMQQF",xsmall:"xsmall-PsAlMQQF",small:"small-PsAlMQQF",medium:"medium-PsAlMQQF",large:"large-PsAlMQQF",xlarge:"xlarge-PsAlMQQF",xxlarge:"xxlarge-PsAlMQQF",xxxlarge:"xxxlarge-PsAlMQQF",skeleton:"skeleton-PsAlMQQF",letter:"letter-PsAlMQQF"}},504665:e=>{e.exports={loader:"loader-UL6iwcBa",static:"static-UL6iwcBa",item:"item-UL6iwcBa","tv-button-loader":"tv-button-loader-UL6iwcBa",medium:"medium-UL6iwcBa",small:"small-UL6iwcBa",black:"black-UL6iwcBa",white:"white-UL6iwcBa",gray:"gray-UL6iwcBa",primary:"primary-UL6iwcBa"}},55679:e=>{e.exports={wrapper:"wrapper-TJ9ObuLF",animated:"animated-TJ9ObuLF",pulsation:"pulsation-TJ9ObuLF"}},485862:e=>{e.exports={disableSelfPositioning:"disableSelfPositioning-dYiqkKAE"}},489928:e=>{e.exports={}},972525:e=>{e.exports={}},492575:e=>{e.exports={}},896108:e=>{e.exports={"tablet-normal-breakpoint":"(max-width: 768px)","small-height-breakpoint":"(max-height: 360px)","tablet-small-breakpoint":"(max-width: 440px)"}},979566:e=>{e.exports={container:"container-M1mz4quA",pairContainer:"pairContainer-M1mz4quA",logo:"logo-M1mz4quA",hidden:"hidden-M1mz4quA"}},510555:e=>{e.exports={wrapper:"wrapper-VB9J73Gf",focused:"focused-VB9J73Gf",readonly:"readonly-VB9J73Gf",disabled:"disabled-VB9J73Gf","size-small":"size-small-VB9J73Gf","size-medium":"size-medium-VB9J73Gf","size-large":"size-large-VB9J73Gf","font-size-small":"font-size-small-VB9J73Gf","font-size-medium":"font-size-medium-VB9J73Gf","font-size-large":"font-size-large-VB9J73Gf","border-none":"border-none-VB9J73Gf",shadow:"shadow-VB9J73Gf","border-thin":"border-thin-VB9J73Gf","border-thick":"border-thick-VB9J73Gf","intent-default":"intent-default-VB9J73Gf","intent-success":"intent-success-VB9J73Gf","intent-warning":"intent-warning-VB9J73Gf","intent-danger":"intent-danger-VB9J73Gf","intent-primary":"intent-primary-VB9J73Gf","corner-top-left":"corner-top-left-VB9J73Gf","corner-top-right":"corner-top-right-VB9J73Gf","corner-bottom-right":"corner-bottom-right-VB9J73Gf","corner-bottom-left":"corner-bottom-left-VB9J73Gf",childrenContainer:"childrenContainer-VB9J73Gf"}},138772:e=>{e.exports={button:"button-jpZsjw5k",withIcon:"withIcon-jpZsjw5k","a11y-text":"a11y-text-jpZsjw5k",buttonIcon:"buttonIcon-jpZsjw5k"}},434625:e=>{e.exports={footer:"footer-dwINHZFL"}},755705:e=>{e.exports={footer:"footer-kcB9V_Ib",shortcuts:"shortcuts-kcB9V_Ib",buttonGroup:"buttonGroup-kcB9V_Ib",button:"button-kcB9V_Ib",withIcon:"withIcon-kcB9V_Ib",icon:"icon-kcB9V_Ib"}},725479:e=>{e.exports={container:"container-u7Ufi_N7",leftSlot:"leftSlot-u7Ufi_N7",rightSlot:"rightSlot-u7Ufi_N7",rightSlotSeparator:"rightSlotSeparator-u7Ufi_N7"}},899316:e=>{e.exports={button:"button-g115eYH4",hover:"hover-g115eYH4",isOpened:"isOpened-g115eYH4",inner:"inner-g115eYH4"}},583315:e=>{e.exports={node:"node-lhJq7vpW"}},710069:e=>{e.exports={removeButton:"removeButton-Tf8QRdrk"}},491148:e=>{e.exports={separator:"separator-eCC6Skn5",innerWrapper:"innerWrapper-eCC6Skn5",
selected:"selected-eCC6Skn5",removeButton:"removeButton-eCC6Skn5",small:"small-eCC6Skn5",highlighted:"highlighted-eCC6Skn5",firstItem:"firstItem-eCC6Skn5",label:"label-eCC6Skn5",renaming:"renaming-eCC6Skn5",hasExpand:"hasExpand-eCC6Skn5",renamableInput:"renamableInput-eCC6Skn5",touch:"touch-eCC6Skn5",expandHandle:"expandHandle-eCC6Skn5",hovered:"hovered-eCC6Skn5",disabled:"disabled-eCC6Skn5"}},29754:e=>{e.exports={wrap:"wrap-g71rrBCn",scrollable:"scrollable-g71rrBCn",separator:"separator-g71rrBCn",content:"content-g71rrBCn",empty:"empty-g71rrBCn",centered:"centered-g71rrBCn",emptyIcon:"emptyIcon-g71rrBCn",separatorDragPreview:"separatorDragPreview-g71rrBCn"}},628616:e=>{e.exports={}},831615:e=>{e.exports={}},486267:e=>{e.exports={icon:"icon-BHdu7XsL",long:"long-BHdu7XsL"}},861321:e=>{e.exports={wrapper:"wrapper-bIGHJRgI",description:"description-bIGHJRgI",dot:"dot-bIGHJRgI",marketOpen:"marketOpen-bIGHJRgI",marketPre:"marketPre-bIGHJRgI",marketPost:"marketPost-bIGHJRgI",marketClose:"marketClose-bIGHJRgI",marketHoliday:"marketHoliday-bIGHJRgI"}},533924:e=>{e.exports={symbol:"symbol-RsFlttSS",tileView:"tileView-RsFlttSS",small:"small-RsFlttSS",firstRow:"firstRow-RsFlttSS",selected:"selected-RsFlttSS",removeButton:"removeButton-RsFlttSS",active:"active-RsFlttSS",flag:"flag-RsFlttSS",indicators:"indicators-RsFlttSS",blackBorder:"blackBorder-RsFlttSS",highlighted:"highlighted-RsFlttSS",invalid:"invalid-RsFlttSS",cell:"cell-RsFlttSS",inner:"inner-RsFlttSS",logo:"logo-RsFlttSS",symbolName:"symbolName-RsFlttSS",tileSymbolName:"tileSymbolName-RsFlttSS",warningIcon:"warningIcon-RsFlttSS",notes:"notes-RsFlttSS",positions:"positions-RsFlttSS",symbolNameText:"symbolNameText-RsFlttSS",tileNameContent:"tileNameContent-RsFlttSS",description:"description-RsFlttSS",last:"last-RsFlttSS",change:"change-RsFlttSS",changeInPercents:"changeInPercents-RsFlttSS",volume:"volume-RsFlttSS",prePostMarket:"prePostMarket-RsFlttSS",prePostMarketNoPrice:"prePostMarketNoPrice-RsFlttSS",tileItem:"tileItem-RsFlttSS",tileChange:"tileChange-RsFlttSS",hidden:"hidden-RsFlttSS",tileChanges:"tileChanges-RsFlttSS",displayContents:"displayContents-RsFlttSS",tileWrap:"tileWrap-RsFlttSS",ellipsis:"ellipsis-RsFlttSS",plus:"plus-RsFlttSS",minus:"minus-RsFlttSS",firstItem:"firstItem-RsFlttSS",lastItem:"lastItem-RsFlttSS",flexCell:"flexCell-RsFlttSS",highlightUp:"highlightUp-RsFlttSS",highlightDown:"highlightDown-RsFlttSS",overlayEnd:"overlayEnd-RsFlttSS",touch:"touch-RsFlttSS"}},303230:e=>{e.exports={wrap:"wrap-evmjQ0gK",columnsMenu:"columnsMenu-evmjQ0gK",checkbox:"checkbox-evmjQ0gK",hovered:"hovered-evmjQ0gK",mobile:"mobile-evmjQ0gK"}},592952:e=>{e.exports={columnHeader:"columnHeader-k67yvGbk",small:"small-k67yvGbk",label:"label-k67yvGbk",accessible:"accessible-k67yvGbk",sortable:"sortable-k67yvGbk",sortActivated:"sortActivated-k67yvGbk",flagSortable:"flagSortable-k67yvGbk",flagWrap:"flagWrap-k67yvGbk",sortArrow:"sortArrow-k67yvGbk",visible:"visible-k67yvGbk",desc:"desc-k67yvGbk"}},385839:e=>{e.exports={placeholder:"placeholder-qp5yBxtk",wrap:"wrap-qp5yBxtk",
handle:"handle-qp5yBxtk",active:"active-qp5yBxtk",separator:"separator-qp5yBxtk"}},652420:e=>{e.exports={wrap:"wrap-Nz1M1_XP",menu:"menu-Nz1M1_XP",tableHeader:"tableHeader-Nz1M1_XP",columnHeader:"columnHeader-Nz1M1_XP",readonly:"readonly-Nz1M1_XP",symbolName:"symbolName-Nz1M1_XP",last:"last-Nz1M1_XP",change:"change-Nz1M1_XP",changeInPercents:"changeInPercents-Nz1M1_XP",volume:"volume-Nz1M1_XP",prePostMarket:"prePostMarket-Nz1M1_XP",prePostMarketNoPrice:"prePostMarketNoPrice-Nz1M1_XP",label:"label-Nz1M1_XP",firstItem:"firstItem-Nz1M1_XP",lastItem:"lastItem-Nz1M1_XP",mobileItem:"mobileItem-Nz1M1_XP",sortingMenu:"sortingMenu-Nz1M1_XP",resetContainer:"resetContainer-Nz1M1_XP",reset:"reset-Nz1M1_XP",resetIcon:"resetIcon-Nz1M1_XP"}},46857:e=>{e.exports={item:"item-mcATxxXd"}},776126:e=>{e.exports={item:"item-E2qCgOMz",withIcon:"withIcon-E2qCgOMz"}},724669:e=>{e.exports={container:"container-mQBvegEO",headerMenuContent:"headerMenuContent-mQBvegEO",watchlistMenu:"watchlistMenu-mQBvegEO",title:"title-mQBvegEO",widgetBtnText:"widgetBtnText-mQBvegEO",lockIcon:"lockIcon-mQBvegEO",alertIcon:"alertIcon-mQBvegEO",widgetBtn:"widgetBtn-mQBvegEO",widgetBtnIcon:"widgetBtnIcon-mQBvegEO",caret:"caret-mQBvegEO",titleRow:"titleRow-mQBvegEO",flag:"flag-mQBvegEO",headerButton:"headerButton-mQBvegEO",disabled:"disabled-mQBvegEO",settingsMenu:"settingsMenu-mQBvegEO",mobileBtn:"mobileBtn-mQBvegEO",buttonWrap:"buttonWrap-mQBvegEO",menuWrap:"menuWrap-mQBvegEO",columnsTitle:"columnsTitle-mQBvegEO",small:"small-mQBvegEO",loaderWrapper:"loaderWrapper-mQBvegEO",loader:"loader-mQBvegEO",switcherMobileLabel:"switcherMobileLabel-mQBvegEO",widgetbarWidgetHeaderLeftSlot:"widgetbarWidgetHeaderLeftSlot-mQBvegEO",widgetbarWidgetHeaderRightSlot:"widgetbarWidgetHeaderRightSlot-mQBvegEO"}},629319:e=>{e.exports={toolbox:"toolbox-MeieydGw"}},518174:e=>{e.exports={toolbox:"toolbox-iUEJczsW"}},324295:e=>{e.exports={item:"item-Pho75f2H",active:"active-Pho75f2H",toolboxCount:"toolboxCount-Pho75f2H",count:"count-Pho75f2H",countVisible:"countVisible-Pho75f2H",title:"title-Pho75f2H",flag:"flag-Pho75f2H",small:"small-Pho75f2H",removeButton:"removeButton-Pho75f2H",toolboxItem:"toolboxItem-Pho75f2H",touch:"touch-Pho75f2H"}},842530:e=>{e.exports={watchlist:"watchlist-__KRxuOy",list:"list-__KRxuOy"}},927061:e=>{e.exports={buttonWrap:"buttonWrap-icygBqe7",desktopSize:"desktopSize-icygBqe7",drawer:"drawer-icygBqe7",menuBox:"menuBox-icygBqe7"}},98993:e=>{e.exports={button:"button-F5dN3ulE",emoji:"emoji-F5dN3ulE",emptySelect:"emptySelect-F5dN3ulE"}},539453:e=>{e.exports={emojiWrap:"emojiWrap-R2CTpmHr",emoji:"emoji-R2CTpmHr",tooltipEmoji:"tooltipEmoji-R2CTpmHr",tooltipEmojiWrap:"tooltipEmojiWrap-R2CTpmHr"}},307567:e=>{e.exports={emojiSelect:"emojiSelect-IY7RpEY6",placeholder:"placeholder-IY7RpEY6"}},667797:e=>{e.exports={menuWrap:"menuWrap-Kq3ruQo8",isMeasuring:"isMeasuring-Kq3ruQo8",scrollWrap:"scrollWrap-Kq3ruQo8",momentumBased:"momentumBased-Kq3ruQo8",menuBox:"menuBox-Kq3ruQo8",isHidden:"isHidden-Kq3ruQo8"}},509059:e=>{e.exports={"tablet-small-breakpoint":"(max-width: 440px)",
item:"item-jFqVJoPk",hovered:"hovered-jFqVJoPk",isDisabled:"isDisabled-jFqVJoPk",isActive:"isActive-jFqVJoPk",shortcut:"shortcut-jFqVJoPk",toolbox:"toolbox-jFqVJoPk",withIcon:"withIcon-jFqVJoPk","round-icon":"round-icon-jFqVJoPk",icon:"icon-jFqVJoPk",labelRow:"labelRow-jFqVJoPk",label:"label-jFqVJoPk",showOnHover:"showOnHover-jFqVJoPk","disclosure-item-circle-logo":"disclosure-item-circle-logo-jFqVJoPk",showOnFocus:"showOnFocus-jFqVJoPk"}},700238:e=>{e.exports={separator:"separator-QjUlCDId",small:"small-QjUlCDId",normal:"normal-QjUlCDId",large:"large-QjUlCDId"}},35990:e=>{e.exports={button:"button-iLKiGOdQ",hovered:"hovered-iLKiGOdQ",disabled:"disabled-iLKiGOdQ",focused:"focused-iLKiGOdQ",active:"active-iLKiGOdQ",hidden:"hidden-iLKiGOdQ"}},491989:e=>{e.exports={renameInput:"renameInput-sZgG7O9F"}},149128:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},959189:(e,t,n)=>{"use strict";function s(e,t){return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}n.d(t,{isIconOnly:()=>s})},898237:(e,t,n)=>{"use strict";n.d(t,{LightAnchorButton:()=>m,LightButton:()=>o.LightButton});var s=n(418920),o=n(943158),i=n(50959),r=n(591365),l=n(273388),a=n(854797),d=n(601538),u=n.n(d),c=n(188317),h=n.n(c);function m(e){const{className:t,isSelected:n,children:o,iconOnly:d,ellipsis:c,showCaret:m,forceDirection:p,endSlot:f,startSlot:g,color:v,variant:b,reference:_,size:S,enableActiveStateStyles:y,renderComponent:w=r.CustomComponentDefaultLink,typography:x,textWrap:C=!1,maxLines:E,style:k={},isActive:T,isPills:I,...M}=e,N=C?E??2:1,L=N>0?{...k,"--ui-lib-light-button-content-max-lines":N}:k;return i.createElement(w,{...M,className:(0,s.useLightButtonClasses)({...h(),...u()},{className:t,isSelected:n,isActive:T,isPills:I,children:o,iconOnly:d,showCaret:m,forceDirection:p,color:v,variant:b,size:S,enableActiveStateStyles:y,typography:x,textWrap:C,isLink:!0,endSlot:f,startSlot:g}),reference:(0,l.isomorphicRef)(_),style:L},i.createElement(a.LightButtonContent,{showCaret:m,isActiveCaret:m&&(I||T||n),iconOnly:d,ellipsis:c,textWrap:C,endSlot:f,startSlot:g},o))}n(15378)},418920:(e,t,n)=>{"use strict";n.d(t,{useLightButtonClasses:()=>d});var s=n(50959),o=n(497754),i=n(234539),r=n(959189),l=n(380327);const a=s.createContext({isInButtonGroup:!1,isGroupPrimary:!1}),d=(e,t,n)=>{const d=(0,s.useContext)(i.CustomBehaviourContext),{className:u,isSelected:c,children:h,showCaret:m,forceDirection:p,iconOnly:f,color:g="gray",variant:v="primary",size:b="medium",enableActiveStateStyles:_=d.enableActiveStateStyles,typography:S,isLink:y=!1,textWrap:w,isPills:x,isActive:C,startSlot:E,endSlot:k}=t,T=e[`typography-${((e,t,n)=>{if(n){const e=n.replace(/^\D+/g,"");return t?`semibold${e}`:n}switch(e){case"xsmall":return t?"semibold14px":"regular14px";case"small":case"medium":return t?"semibold16px":"regular16px";default:return""}})(b,c||x,S||void 0)}`],I=(0,s.useContext)(l.ControlGroupContext),{isInButtonGroup:M,isGroupPrimary:N}=(0,s.useContext)(a);return o(u,e.lightButton,y&&e.link,C&&e.active,c&&e.selected,(0,
r.isIconOnly)(h,f)&&e.noContent,!!E&&e.withStartSlot,(m||!!k)&&e.withEndSlot,n&&e.withGrouped,p&&e[p],e[N?"primary":v],e[N?"gray":g],e[b],T,!_&&e.disableActiveStateStyles,I.isGrouped&&e.grouped,w&&e.wrap,M&&e.disableActiveOnTouch,x&&e.pills)}},854797:(e,t,n)=>{"use strict";n.d(t,{LightButtonContent:()=>h});var s=n(50959),o=n(497754),i=n(601198),r=n(959189),l=n(878112),a=n(602948),d=n(601538),u=n.n(d);const c=e=>s.createElement(l.Icon,{className:o(u().caret,e&&u().activeCaret),icon:a});function h(e){const{showCaret:t,iconOnly:n,ellipsis:l=!0,textWrap:a,tooltipText:d,children:h,endSlot:m,startSlot:p,isActiveCaret:f}=e;[m,t].filter((e=>!!e));return s.createElement(s.Fragment,null,p&&s.createElement("span",{className:o(u().slot,u().startSlot)},p),!(0,r.isIconOnly)(h,n)&&s.createElement("span",{className:o(u().content,!a&&u().nowrap,"apply-overflow-tooltip","apply-overflow-tooltip--check-children-recursively","apply-overflow-tooltip--allow-text"),"data-overflow-tooltip-text":d??(0,i.getTextForTooltip)(h)},a||l?s.createElement(s.Fragment,null,s.createElement("span",{className:o(!a&&l&&u().ellipsisContainer,a&&u().textWrapContainer,a&&l&&u().textWrapWithEllipsis)},h),s.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},h)):s.createElement(s.Fragment,null,h,s.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},h))),m&&s.createElement("span",{className:o(u().slot,u().endSlot)},m),t&&c(f))}},943158:(e,t,n)=>{"use strict";n.d(t,{LightButton:()=>c});var s=n(50959),o=n(380327),i=n(418920),r=n(854797),l=n(601538),a=n.n(l),d=n(188317),u=n.n(d);function c(e){const{isGrouped:t}=s.useContext(o.ControlGroupContext),{reference:n,className:l,isSelected:d,children:c,iconOnly:h,ellipsis:m,showCaret:p,forceDirection:f,endSlot:g,startSlot:v,color:b,variant:_,size:S,enableActiveStateStyles:y,typography:w,textWrap:x=!1,maxLines:C,style:E={},isPills:k,isActive:T,tooltipText:I,role:M,...N}=e,L=x?C??2:1,A=L>0?{...E,"--ui-lib-light-button-content-max-lines":L}:E;return s.createElement("button",{...N,className:(0,i.useLightButtonClasses)({...u(),...a()},{className:l,isSelected:d,children:c,iconOnly:h,showCaret:p,forceDirection:f,endSlot:g,startSlot:v,color:b,variant:_,size:S,enableActiveStateStyles:y,typography:w,textWrap:x,isPills:k,isActive:T},t),ref:n,style:A,role:M},s.createElement(r.LightButtonContent,{showCaret:p,isActiveCaret:p&&(k||T||d),iconOnly:h,ellipsis:m,textWrap:x,tooltipText:I,endSlot:g,startSlot:v},c))}},15378:(e,t,n)=>{"use strict";var s,o,i,r;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(s||(s={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(o||(o={})),function(e){e.Brand="brand",e.Blue="blue",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(i||(i={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",
e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(r||(r={}))},408323:(e,t,n)=>{"use strict";n.d(t,{CheckboxInput:()=>d});var s=n(50959),o=n(497754),i=n(800417),r=n(611031),l=n(811362),a=n.n(l);function d(e){const t=o(a().wrapper,e.className);return s.createElement("span",{className:t,title:e.title,style:e.style},s.createElement("input",{id:e.id,tabIndex:e.tabIndex,className:o(e.intent&&a()[e.intent],a().input),type:"checkbox",name:e.name,checked:e.checked,disabled:e.disabled,value:e.value,autoFocus:e.autoFocus,role:e.role,onChange:function(){e.onChange?.(e.value)},ref:e.reference,"aria-required":e["aria-required"],"aria-describedby":e["aria-describedby"],"aria-invalid":e["aria-invalid"],...(0,i.filterDataProps)(e)}),s.createElement(r.CheckboxView,{className:a().view,indeterminate:e.indeterminate,checked:e.checked,disabled:e.disabled,intent:e.intent,tabIndex:e.tabIndex}))}},611031:(e,t,n)=>{"use strict";n.d(t,{CheckboxView:()=>d});var s=n(50959),o=n(497754),i=n(878112),r=n(465890),l=n(4052),a=n.n(l);function d(e){const{indeterminate:t,checked:n,tabIndex:l,className:d,disabled:u,disableActiveStyles:c,intent:h,hideIcon:m,...p}=e,f=t||!n||m?"":r,g=o(a().box,a()[`intent-${h}`],!t&&a().check,!!t&&a().dot,-1===l&&a().noOutline,d,n&&a().checked,u&&a().disabled,c&&a().disableActiveStyles);return s.createElement("span",{className:g,...p},s.createElement(i.Icon,{icon:f,className:a().icon}))}},302946:(e,t,n)=>{"use strict";n.d(t,{Checkbox:()=>d,CheckboxView:()=>u.CheckboxView});var s=n(50959),o=n(497754),i=n(230789),r=n(408323),l=n(865592),a=n.n(l);class d extends s.PureComponent{render(){const{inputClassName:e,labelClassName:t,...n}=this.props,i=o(this.props.className,a().checkbox,{[a().reverse]:Boolean(this.props.labelPositionReverse),[a().baseline]:Boolean(this.props.labelAlignBaseline)}),l=o(a().label,t,{[a().disabled]:this.props.disabled});let d=null;return this.props.label&&(d=s.createElement("span",{className:l,title:this.props.title},this.props.label)),s.createElement("label",{className:i},s.createElement(r.CheckboxInput,{...n,className:e}),d)}}d.defaultProps={value:"on"};(0,i.makeSwitchGroupItem)(d);var u=n(611031)},108937:(e,t,n)=>{"use strict";n.d(t,{getBlockStyleClasses:()=>l,getLogoStyleClasses:()=>a});var s=n(497754),o=n(92318),i=n(78217),r=n.n(i);function l(e,t){return s(r().pair,r()[e],t)}function a(e,t=2,n=!0){return s(r().logo,r()[e],r().skeleton,o.skeletonTheme.wrapper,!n&&r().empty,1===t&&s(o.skeletonTheme.animated))}},185934:(e,t,n)=>{"use strict";n.d(t,{getStyleClasses:()=>l,isCircleLogoWithUrlProps:()=>a});var s=n(497754),o=n(92318),i=n(456057),r=n.n(i);function l(e,t=2,n){return s(r().logo,r()[e],n,0===t||1===t?s(o.skeletonTheme.wrapper,r().skeleton):r().letter,1===t&&o.skeletonTheme.animated)}function a(e){return"logoUrl"in e&&null!==e.logoUrl&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},380327:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>s});const s=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,
isLeft:!0}})},409245:(e,t,n)=>{"use strict";function s(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>s})},591365:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>i});var s=n(50959),o=n(409245);function i(e){return s.createElement("a",{...(0,o.renameRef)(e)})}s.PureComponent},234539:(e,t,n)=>{"use strict";n.d(t,{CustomBehaviourContext:()=>s});const s=(0,n(50959).createContext)({enableActiveStateStyles:!0});s.displayName="CustomBehaviourContext"},383836:(e,t,n)=>{"use strict";n.d(t,{useFocus:()=>o});var s=n(50959);function o(e,t){const[n,o]=(0,s.useState)(!1);(0,s.useEffect)((()=>{t&&n&&o(!1)}),[t,n]);const i={onFocus:(0,s.useCallback)((function(t){void 0!==e&&e.current!==t.target||o(!0)}),[e]),onBlur:(0,s.useCallback)((function(t){void 0!==e&&e.current!==t.target||o(!1)}),[e])};return[n,i]}},718736:(e,t,n)=>{"use strict";n.d(t,{useFunctionalRefObject:()=>i});var s=n(50959),o=n(855393);function i(e){const t=(0,s.useMemo)((()=>function(e){const t=n=>{e(n),t.current=n};return t.current=null,t}((e=>{l.current(e)}))),[]),n=(0,s.useRef)(null),i=t=>{if(null===t)return r(n.current,t),void(n.current=null);n.current!==e&&(n.current=e,r(n.current,t))},l=(0,s.useRef)(i);return l.current=i,(0,o.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return l.current(t.current),()=>l.current(null)}),[e]),t}function r(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},975228:(e,t,n)=>{"use strict";n.d(t,{hoverMouseEventFilter:()=>i,useAccurateHover:()=>r,useHover:()=>o});var s=n(50959);function o(){const[e,t]=(0,s.useState)(!1);return[e,{onMouseOver:function(e){i(e)&&t(!0)},onMouseOut:function(e){i(e)&&t(!1)}}]}function i(e){return!e.currentTarget.contains(e.relatedTarget)}function r(e){const[t,n]=(0,s.useState)(!1);return(0,s.useEffect)((()=>{const t=t=>{if(null===e.current)return;const s=e.current.contains(t.target);n(s)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},855393:(e,t,n)=>{"use strict";n.d(t,{useIsomorphicLayoutEffect:()=>o});var s=n(50959);function o(e,t){("undefined"==typeof window?s.useEffect:s.useLayoutEffect)(e,t)}},525388:(e,t,n)=>{"use strict";n.d(t,{useMergedRefs:()=>i});var s=n(50959),o=n(273388);function i(e){return(0,s.useCallback)((0,o.mergeRefs)(e),e)}},778199:(e,t,n)=>{"use strict";function s(e,t,n,s,o){function i(o){if(e>o.timeStamp)return;const i=o.target;void 0!==n&&null!==t&&null!==i&&i.ownerDocument===s&&(t.contains(i)||n(o))}return o.click&&s.addEventListener("click",i,!1),o.mouseDown&&s.addEventListener("mousedown",i,!1),o.touchEnd&&s.addEventListener("touchend",i,!1),o.touchStart&&s.addEventListener("touchstart",i,!1),()=>{s.removeEventListener("click",i,!1),s.removeEventListener("mousedown",i,!1),s.removeEventListener("touchend",i,!1),s.removeEventListener("touchstart",i,!1)}}n.d(t,{addOutsideEventListener:()=>s})},908783:(e,t,n)=>{"use strict";n.d(t,{useOutsideEvent:()=>r});var s=n(50959),o=n(855393),i=n(778199);function r(e){
const{click:t,mouseDown:n,touchEnd:r,touchStart:l,handler:a,reference:d}=e,u=(0,s.useRef)(null),c=(0,s.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,o.useIsomorphicLayoutEffect)((()=>{const e={click:t,mouseDown:n,touchEnd:r,touchStart:l},s=d?d.current:u.current;return(0,i.addOutsideEventListener)(c.current,s,a,document,e)}),[t,n,r,l,a]),d||u}},664332:(e,t,n)=>{"use strict";n.d(t,{useResizeObserver:()=>r});var s=n(50959),o=n(855393),i=n(718736);function r(e,t=[]){const{callback:n,ref:r=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),l=(0,s.useRef)(null),a=(0,s.useRef)(n);a.current=n;const d=(0,i.useFunctionalRefObject)(r),u=(0,s.useCallback)((e=>{d(e),null!==l.current&&(l.current.disconnect(),null!==e&&l.current.observe(e))}),[d,l]);return(0,o.useIsomorphicLayoutEffect)((()=>(l.current=new ResizeObserver(((e,t)=>{a.current(e,t)})),d.current&&u(d.current),()=>{l.current?.disconnect()})),[d,...t]),u}},183787:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>o});var s=n(50959);const o=s.forwardRef(((e,t)=>{const{icon:n="",title:o,ariaLabel:i,ariaLabelledby:r,ariaHidden:l,...a}=e,d=!!(o||i||r);return s.createElement("span",{role:"img",...a,ref:t,"aria-label":i,"aria-labelledby":r,"aria-hidden":l||!d,title:o,dangerouslySetInnerHTML:{__html:n}})}))},878112:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>s.Icon});var s=n(183787)},234404:(e,t,n)=>{"use strict";n.d(t,{Loader:()=>a});var s,o=n(50959),i=n(497754),r=n(504665),l=n.n(r);function a(e){const{className:t,size:n="medium",staticPosition:s,color:r="black"}=e,a=i(l().item,l()[r],l()[n]);return o.createElement("span",{className:i(l().loader,s&&l().static,t)},o.createElement("span",{className:a}),o.createElement("span",{className:a}),o.createElement("span",{className:a}))}!function(e){e.Medium="medium",e.Small="small"}(s||(s={}))},823030:(e,t,n)=>{"use strict";n.d(t,{SubmenuContext:()=>o,SubmenuHandler:()=>i});var s=n(50959);const o=s.createContext(null);function i(e){const[t,n]=(0,s.useState)(null),i=(0,s.useRef)(null),r=(0,s.useRef)(new Map);return(0,s.useEffect)((()=>()=>{null!==i.current&&clearTimeout(i.current)}),[]),s.createElement(o.Provider,{value:{current:t,setCurrent:function(e){null!==i.current&&(clearTimeout(i.current),i.current=null);null===t?n(e):i.current=setTimeout((()=>{i.current=null,n(e)}),100)},registerSubmenu:function(e,t){return r.current.set(e,t),()=>{r.current.delete(e)}},isSubmenuNode:function(e){return Array.from(r.current.values()).some((t=>t(e)))}}},e.children)}},730654:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>d,PortalContext:()=>u});var s=n(50959),o=n(632227),i=n(925931),r=n(801808),l=n(481564),a=n(682925);class d extends s.PureComponent{constructor(){super(...arguments),this._uuid=(0,i.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",
e.style.pointerEvents=this.props.pointerEvents||"";const t=this.props.className;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute(l.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(l.FOCUS_TRAP_DATA_ATTRIBUTE,"true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),o.createPortal(s.createElement(u.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,r.getRootOverlapManager)():this.context}}d.contextType=a.SlotContext;const u=s.createContext(null)},92318:(e,t,n)=>{"use strict";n.d(t,{skeletonTheme:()=>o});var s=n(55679);const o=s},682925:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>o,SlotContext:()=>i});var s=n(50959);class o extends s.Component{shouldComponentUpdate(){return!1}render(){return s.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const i=s.createContext(null)},672511:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>a});var s=n(50959),o=n(497754),i=n(843442),r=(n(715216),n(485862)),l=n.n(r);function a(e){const{ariaLabel:t,ariaLabelledby:n,className:r,style:a,size:d,id:u,disableSelfPositioning:c}=e;return s.createElement("div",{className:o(r,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${i.spinnerSizeMap[d||i.DEFAULT_SIZE]}`,c&&l().disableSelfPositioning),style:a,role:"progressbar",id:u,"aria-label":t,"aria-labelledby":n})}},230789:(e,t,n)=>{"use strict";n.d(t,{SwitchGroup:()=>r,makeSwitchGroupItem:()=>l});var s=n(50959),o=n(755883);const i=(0,s.createContext)({getName:()=>"",getValues:()=>[],getOnChange:()=>o.default,subscribe:o.default,unsubscribe:o.default});class r extends s.PureComponent{constructor(e){super(e),this._subscriptions=new Set,this._getName=()=>this.props.name,this._getValues=()=>this.props.values,this._getOnChange=()=>this.props.onChange,this._subscribe=e=>{this._subscriptions.add(e)},this._unsubscribe=e=>{this._subscriptions.delete(e)},this.state={switchGroupContext:{getName:this._getName,getValues:this._getValues,getOnChange:this._getOnChange,subscribe:this._subscribe,unsubscribe:this._unsubscribe}}}render(){return s.createElement(i.Provider,{value:this.state.switchGroupContext},this.props.children)}componentDidUpdate(e){this._notify(this._getUpdates(this.props.values,e.values))}_notify(e){this._subscriptions.forEach((t=>t(e)))}_getUpdates(e,t){return[...t,...e].filter((n=>t.includes(n)?!e.includes(n):e.includes(n)))}}function l(e){var t;return t=class extends s.PureComponent{constructor(){super(...arguments),this._onChange=e=>{this.context.getOnChange()(e)},this._onUpdate=e=>{e.includes(this.props.value)&&this.forceUpdate()}}componentDidMount(){this.context.subscribe(this._onUpdate)}render(){return s.createElement(e,{...this.props,name:this._getName(),onChange:this._onChange,checked:this._isChecked()})}componentWillUnmount(){this.context.unsubscribe(this._onUpdate)}_getName(){return this.context.getName()}_isChecked(){return this.context.getValues().includes(this.props.value)}},
t.contextType=i,t}},800417:(e,t,n)=>{"use strict";function s(e){return i(e,r)}function o(e){return i(e,l)}function i(e,t){const n=Object.entries(e).filter(t),s={};for(const[e,t]of n)s[e]=t;return s}function r(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function l(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>o,filterDataProps:()=>s,filterProps:()=>i,isAriaAttribute:()=>l,isDataAttribute:()=>r})},65160:(e,t,n)=>{"use strict";function s(e){const{paddingTop:t,paddingBottom:n}=window.getComputedStyle(e);return[t,n].reduce(((e,t)=>e-Number((t||"").replace("px",""))),e.clientHeight)}function o(e,t=!1){const n=getComputedStyle(e),s=[n.height];return"border-box"!==n.boxSizing&&s.push(n.paddingTop,n.paddingBottom,n.borderTopWidth,n.borderBottomWidth),t&&s.push(n.marginTop,n.marginBottom),s.reduce(((e,t)=>e+(parseFloat(t)||0)),0)}function i(e,t=!1){const n=getComputedStyle(e),s=[n.width];return"border-box"!==n.boxSizing&&s.push(n.paddingLeft,n.paddingRight,n.borderLeftWidth,n.borderRightWidth),t&&s.push(n.marginLeft,n.marginRight),s.reduce(((e,t)=>e+(parseFloat(t)||0)),0)}n.d(t,{contentHeight:()=>s,outerHeight:()=>o,outerWidth:()=>i})},601198:(e,t,n)=>{"use strict";n.d(t,{getTextForTooltip:()=>r});var s=n(50959);const o=e=>(0,s.isValidElement)(e)&&Boolean(e.props.children),i=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",r=e=>Array.isArray(e)||(0,s.isValidElement)(e)?s.Children.toArray(e).reduce(((e,t)=>{let n="";return n=(0,s.isValidElement)(t)&&o(t)?r(t.props.children):(0,s.isValidElement)(t)&&!o(t)?"":i(t),e.concat(n)}),"").trim():i(e)},273388:(e,t,n)=>{"use strict";function s(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function o(e){return s([e])}n.d(t,{isomorphicRef:()=>o,mergeRefs:()=>s})},801808:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>r,getRootOverlapManager:()=>a});var s=n(650151),o=n(481564);class i{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class r{constructor(e=document){this._storage=new i,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e);if(void 0!==n)return n;this.registerWindow(e);const s=this._document.createElement("div");if(s.style.position=t.position,s.style.zIndex=this._index.toString(),s.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(s);else if(t.index<=0)this._container.insertBefore(s,this._container.firstChild);else{const e=this._container.childNodes[t.index]
;this._container.insertBefore(s,e)}}else"reverse"===t.direction?this._container.insertBefore(s,this._container.firstChild):this._container.appendChild(s);return this._windows.set(e,s),++this._index,s}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveLastWindowToTop(){const e=this._storage.getItems(),t=e[e.length-1];t&&this.moveToTop(t)}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,n)=>{e.hasAttribute(o.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(o.FOCUS_TRAP_DATA_ATTRIBUTE,e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const l=new WeakMap;function a(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,s.ensureDefined)(l.get(t));{const t=new r(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return l.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}var d;!function(e){e[e.BaseZindex=150]="BaseZindex"}(d||(d={}))},269842:(e,t,n)=>{"use strict";function s(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>s})},609970:(e,t,n)=>{"use strict";n.d(t,{AbstractIndicator:()=>l});var s=n(671945),o=n(924910),i=n(334529);n(744471);const r=(0,s.getLogger)("GUI.Blocks.AbstractIndicator");class l{constructor(e){this._classSuffix="",this._quoteSessionPrefix="abstract-indicator",this._shortMode=!1,this._showTooltip=!0,this._subscribed=!1,this._tooltipType="custom",this._lastTooltipText="",this._quoteSession=e.quoteSession}getValue(){return this._value}getTooltipText(){return this._labelMap[this._value]||""}getLabel(){return this._labelMap[this._value]||""}getElement(){return this._el}update(e,t){this._updateValue(e,t),this._render()}setTooltipEnabled(e=!1){this._showTooltip!==e&&(this._showTooltip=e,this._renderTooltip())}enableShortMode(){!0!==this._shortMode&&(this._shortMode=!0,this._render())}disableShortMode(){!1!==this._shortMode&&(this._shortMode=!1,this._render())}isShortModeEnabled(){return this._shortMode}start(){!this._subscribed&&this._symbolName&&(this._quoteSession||(this._quoteSession=(0,i.getQuoteSessionInstance)("simple")),this._quoteSession.subscribe(this._getQuoteSessionId(),this._symbolName,this.update.bind(this)),this._subscribed=!0)}stop(){this._subscribed&&this._quoteSession&&this._symbolName&&(this._quoteSession.unsubscribe(this._getQuoteSessionId(),this._symbolName),this._subscribed=!1)}setSessionStatusIcon(e){this._sessionStatusIcon!==e&&(this._sessionStatusIcon=e,this._render())}_init(e){this._el=e.el?e.el:document.createElement("span"),this._el.innerHTML="",this._classMap=e.classMap,this._iconClassMap=e.iconClassMap,this._labelMap=e.labelMap,this._showTooltip=e.showTooltip,
this._classSuffix=e.classSuffix,this._symbolName=e.symbol,this._sessionStatusIcon=e.sessionStatusIcon,this._onValueChange=e.onValueChange,e.tooltipType&&(this._tooltipType=e.tooltipType),this._quoteSessionGUID=(0,o.guid)(),!0===e.short&&this.enableShortMode(),e.data&&this._updateValue(e.data)}_clearClasses(){Object.values(this._classMap).map((e=>{this._el.classList.remove(`${e}`),this._el.classList.remove(`${e}${this._classSuffix}`)}))}_render(){this._renderClasses(),this._renderTooltip(),this._renderLabel()}_renderLabel(){this._el.textContent=this.getLabel()}_updateValue(e,t){const n=this._getValueFromData(e);(t||n!==this._value)&&(this._value=n,this._onValueChange?.(this._value))}_renderClasses(){const e=this._el.classList;e.add(this._componentClass,this._componentClass+this._classSuffix);const t=this._classMap[this._value];for(const n in this._classMap){const s=this._classMap[n];s&&(s===t?(e.add(s,s+this._classSuffix),this._sessionStatusIcon&&e.add(s+"__withIcon")):(e.remove(s,s+this._classSuffix),this._sessionStatusIcon&&e.remove(s+"__withIcon")))}!t&&this._value&&r.logWarn(`no className for status ${this._value}`)}_renderTooltip(){const e=this._showTooltip?this.getTooltipText():"";e!==this._lastTooltipText&&(this._lastTooltipText=e,this._el.setAttribute("title",e),"custom"===this._tooltipType&&this._el.classList.toggle("apply-common-tooltip",this._showTooltip))}_getQuoteSessionId(){return`${this._quoteSessionPrefix}.${this._quoteSessionGUID}`}}},678100:(e,t,n)=>{"use strict";n.d(t,{DataModeIndicator:()=>d});var s,o=n(609838),i=(n(68212),n(489928),n(609970));!function(e){e.Delayed="delayed",e.DelayedStreaming="delayed_streaming",e.EndOfDay="endofday",e.Snapshot="snapshot",e.Realtime="realtime",e.Connecting="connecting",e.Loading="loading",e.Invalid="invalid",e.Forbidden="forbidden",e.Streaming="streaming"}(s||(s={}));const r={connecting:"tv-data-mode--connecting",delayed:"tv-data-mode--delayed",delayed_streaming:"tv-data-mode--delayed",endofday:"tv-data-mode--endofday",forbidden:"tv-data-mode--forbidden",realtime:"tv-data-mode--realtime",snapshot:"tv-data-mode--snapshot",loading:"tv-data-mode--loading",replay:"tv-data-mode--replay"};function l(){return{connecting:o.t(null,{context:"data_mode_connecting_letter"},n(167040)),delayed:o.t(null,{context:"data_mode_delayed_letter"},n(800919)),delayed_streaming:o.t(null,{context:"data_mode_delayed_streaming_letter"},n(933088)),endofday:o.t(null,{context:"data_mode_end_of_day_letter"},n(918400)),forbidden:o.t(null,{context:"data_mode_forbidden_letter"},n(514149)),realtime:o.t(null,{context:"data_mode_realtime_letter"},n(650940)),snapshot:o.t(null,{context:"data_mode_snapshot_letter"},n(756757)),loading:"",replay:o.t(null,{context:"data_mode_replay_letter"},n(745540))}}const a={streaming:"realtime"};class d extends i.AbstractIndicator{constructor(e){super(e),this._quoteSessionPrefix="data-mode-indicator",this._componentClass="tv-data-mode",this._init(e)}getLabel(){return!0===this._shortMode?this._shortLabelMap[this._value]||"":super.getLabel()}setMode(e,t){this.update({
values:{update_mode:e,update_mode_seconds:t}})}hide(){this._el.classList.add("i-hidden")}show(){this._el.classList.remove("i-hidden")}getTooltipText(){let e="";const t=this.getValue();if(""===t)return e;switch(t){case"delayed":e=o.t(null,void 0,n(369539));break;case"delayed_streaming":e=o.t(null,void 0,n(167476));break;default:e=this._labelMap[t]||e}return["delayed","delayed_streaming"].includes(t)&&(e=e.format({number:String(Math.round(this._modeInterval/60))})),e}_init(e={}){const t=Object.assign({},{classMap:r,classSuffix:"",data:{values:{update_mode:"connecting"}},labelMap:{connecting:o.t(null,void 0,n(366891)),delayed:o.t(null,void 0,n(739688)),delayed_streaming:o.t(null,void 0,n(739688)),endofday:o.t(null,void 0,n(328304)),forbidden:o.t(null,void 0,n(909161)),realtime:o.t(null,void 0,n(303058)),snapshot:o.t(null,void 0,n(988408)),loading:"",replay:o.t(null,void 0,n(38822))},modeInterval:600,short:!1,shortLabelMap:l(),showTooltip:!0,tooltipType:"custom"},e);this._modeInterval=t.modeInterval||600,this._shortLabelMap=t.shortLabelMap||l(),super._init(t),this._render()}_getValueFromData(e){let t;return t=void 0!==e.values&&void 0!==e.values.update_mode?e.values.update_mode:this.getValue(),a[t]||t}_updateValue(e,t){void 0!==e.values&&void 0!==e.values.update_mode_seconds&&(this._modeInterval=e.values.update_mode_seconds),super._updateValue(e,t)}}},496818:(e,t,n)=>{"use strict";n.d(t,{Draggable:()=>i,PointerBackend:()=>r});var s=n(650151),o=n(821205);n(492575);class i{constructor(e){this._helper=null,this._handleDragStart=e=>{if(null!==this._helper)return;const t=this._source;t.classList.add("ui-draggable-dragging");const[n,s]=[(0,o.outerWidth)(t),(0,o.outerHeight)(t)];this._helper={startTop:parseFloat(t.style.top)||0,startLeft:parseFloat(t.style.left)||0,nextTop:null,nextLeft:null,raf:null,size:[n,s],containment:this._containment instanceof HTMLElement?[parseInt(getComputedStyle(this._containment).borderLeftWidth)+parseInt(getComputedStyle(this._containment).paddingLeft),parseInt(getComputedStyle(this._containment).borderTopWidth)+parseInt(getComputedStyle(this._containment).paddingTop),this._containment.offsetWidth-parseInt(getComputedStyle(this._containment).borderRightWidth)-parseInt(getComputedStyle(this._containment).paddingRight)-parseInt(getComputedStyle(t).marginLeft)-parseInt(getComputedStyle(t).marginRight)-n,this._containment.offsetHeight-parseInt(getComputedStyle(this._containment).borderBottomWidth)-parseInt(getComputedStyle(this._containment).paddingBottom)-parseInt(getComputedStyle(t).marginTop)-parseInt(getComputedStyle(t).marginBottom)-s]:"window"===this._containment?[window.scrollX,window.scrollY,window.scrollX+document.documentElement.offsetWidth-n,window.scrollY+document.documentElement.offsetHeight-s]:null},this._start?.()},this._handleDragMove=e=>{if(null===this._helper)return;const{current:t,initial:n}=e.detail,s=this._source,o=this._helper.nextTop,i=this._helper.nextLeft,r="y"===this._axis||!1===this._axis||0!==t.movementY;if(r){const e=this._helper.startTop
;isFinite(e)&&(this._helper.nextTop=t.clientY-n.clientY+e)}const l="x"===this._axis||!1===this._axis||0!==t.movementY;if(l){const e=this._helper.startLeft;isFinite(e)&&(this._helper.nextLeft=t.clientX-n.clientX+e)}if(null!==this._helper.containment){const[e,t,n,s]=this._helper.containment;r&&this._helper.nextTop&&(this._helper.nextTop=Math.min(this._helper.nextTop,s),this._helper.nextTop=Math.max(this._helper.nextTop,t)),l&&this._helper.nextLeft&&(this._helper.nextLeft=Math.min(this._helper.nextLeft,n),this._helper.nextLeft=Math.max(this._helper.nextLeft,e))}null!==this._helper.raf||o===this._helper.nextTop&&i===this._helper.nextLeft||(this._helper.raf=requestAnimationFrame((()=>{null!==this._helper&&(null!==this._helper.nextTop&&(s.style.top=this._helper.nextTop+"px",this._helper.nextTop=null),null!==this._helper.nextLeft&&(s.style.left=this._helper.nextLeft+"px",this._helper.nextLeft=null),this._helper.raf=null)}))),this._drag?.()},this._handleDragStop=e=>{if(null===this._helper)return;this._source.classList.remove("ui-draggable-dragging"),this._helper=null,this._stop?.()};const t=this._source=e.source;t.classList.add("ui-draggable");const n=this._handle=(e.handle?t.querySelector(e.handle):null)??t;n.classList.add("ui-draggable-handle"),this._start=e.start,this._stop=e.stop,this._drag=e.drag,this._backend=new r({handle:n,onDragStart:this._handleDragStart,onDragMove:this._handleDragMove,onDragStop:this._handleDragStop}),this._axis=e.axis??!1,this._containment=e.containment}destroy(){const e=this._source;e.classList.remove("ui-draggable"),e.classList.remove("ui-draggable-dragging");this._handle.classList.remove("ui-draggable-handle"),this._backend.destroy(),null!==this._helper&&(this._helper.raf&&cancelAnimationFrame(this._helper.raf),this._helper=null)}}class r{constructor(e){this._pointerStarted=!1,this._initial=null,this._handlePointerDown=e=>{if(null!==this._initial||0!==e.button)return;if(!(e.target instanceof Element&&this._handle.contains(e.target)))return;if(this._initial=e,!this._distance&&(this._pointerStart(),!this._pointerStarted))return;e.preventDefault();const t=this._getEventTarget();t.addEventListener("pointermove",this._handlePointerMove),t.addEventListener("pointerup",this._handlePointerUp),t.addEventListener("pointercancel",this._handlePointerUp),t.addEventListener("lostpointercapture",this._handleLostPointerCapture)},this._handleLostPointerCapture=e=>{this._getEventTarget()===e.target&&this._handlePointerUp(e)},this._handlePointerMove=e=>{if(null!==this._initial&&this._initial.pointerId===e.pointerId)if(this._pointerStarted)this._pointerDrag(e);else if(this._pointerDistanceMet(e)){if(this._pointerStart(),this._pointerStarted)return void this._pointerDrag(e);this._handlePointerUp(e)}},this._handlePointerUp=e=>{if(null===this._initial||this._initial.pointerId!==e.pointerId)return;e.preventDefault();const t=this._getEventTarget();t.removeEventListener("pointermove",this._handlePointerMove),t.removeEventListener("pointerup",this._handlePointerUp),
t.removeEventListener("pointercancel",this._handlePointerUp),t.removeEventListener("lostpointercapture",this._handlePointerUp),this._pointerStarted&&(this._pointerStarted=!1,t.releasePointerCapture(this._initial.pointerId),this._dispatchEvent(this._createEvent("pointer-drag-stop",e))),this._initial=null};const t=this._handle=e.handle;this._onDragStart=e.onDragStart,this._onDragMove=e.onDragMove,this._onDragStop=e.onDragStop,this._distance=e.distance??0,this._rootElement=e.rootElement,t.style.touchAction="none",t.addEventListener("pointerdown",this._handlePointerDown)}destroy(){const e=this._handle;e.style.touchAction="",e.removeEventListener("pointerdown",this._handlePointerDown),e.removeEventListener("pointermove",this._handlePointerMove),e.removeEventListener("pointerup",this._handlePointerUp),e.removeEventListener("pointercancel",this._handlePointerUp),e.removeEventListener("lostpointercapture",this._handlePointerUp),null!==this._initial&&(e.releasePointerCapture(this._initial.pointerId),this._initial=null),this._pointerStarted=!1}_pointerStart(){if(!this._initial)return;const e=this._getEventTarget();this._dispatchEvent(this._createEvent("pointer-drag-start",this._initial))?(this._pointerStarted=!0,e.setPointerCapture(this._initial.pointerId)):this._initial=null}_pointerDrag(e){e.preventDefault(),this._dispatchEvent(this._createEvent("pointer-drag-move",e))}_pointerDistanceMet(e){return!this._initial||!this._distance||Math.max(Math.abs(this._initial.clientX-e.clientX),Math.abs(this._initial.clientY-e.clientY))>=this._distance}_getEventTarget(){return this._rootElement??this._handle}_dispatchEvent(e){switch(e.type){case"pointer-drag-start":this._onDragStart(e);break;case"pointer-drag-move":this._onDragMove(e);break;case"pointer-drag-stop":this._onDragStop(e)}return!e.defaultPrevented}_createEvent(e,t){return(0,s.assert)(null!==this._initial),new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:{backend:this,initial:this._initial,current:t}})}}},670086:(e,t,n)=>{"use strict";n.d(t,{FragmentMap:()=>o});var s=n(50959);function o(e){if(e.map){return s.Children.toArray(e.children).map(e.map)}return e.children}},868333:(e,t,n)=>{"use strict";var s;n.d(t,{LogoSize:()=>s,getLogoUrlResolver:()=>r}),function(e){e[e.Medium=0]="Medium",e[e.Large=1]="Large"}(s||(s={}));class o{getSymbolLogoUrl(e){return e}getCountryFlagUrl(){return""}getCryptoLogoUrl(e){return e}getProviderLogoUrl(e){return e}getSourceLogoUrl(e){return e}getBlockchainContractLogoUrl(e){return e}}let i;function r(){return i||(i=new o),i}},285089:(e,t,n)=>{"use strict";n.d(t,{setFixedBodyState:()=>a});var s=n(735922);const o=()=>!window.matchMedia("(min-width: 768px)").matches,i=()=>!window.matchMedia("(min-width: 1280px)").matches;let r=0,l=!1;function a(e){const{body:t}=document,n=t.querySelector(".widgetbar-wrap");if(e&&1==++r){const e=(0,s.getCSSProperty)(t,"overflow"),o=(0,s.getCSSPropertyNumericValue)(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&((0,s.setStyle)(n,"right",`${(0,s.getScrollbarWidth)()}px`),
t.style.paddingRight=`${o+(0,s.getScrollbarWidth)()}px`,l=!0),t.classList.add("i-no-scroll")}else if(!e&&r>0&&0==--r&&(t.classList.remove("i-no-scroll"),l)){(0,s.setStyle)(n,"right","0px");let e=0;e=n?(a=(0,s.getContentWidth)(n),o()?0:i()?45:Math.min(Math.max(a,45),450)):0,t.scrollHeight<=t.clientHeight&&(e-=(0,s.getScrollbarWidth)()),t.style.paddingRight=(e<0?0:e)+"px",l=!1}var a}},821205:(e,t,n)=>{"use strict";n.d(t,{contentHeight:()=>o.contentHeight,html:()=>i,outerHeight:()=>o.outerHeight,outerWidth:()=>o.outerWidth,position:()=>l});var s=n(650151),o=n(65160);function i(e,t){return void 0===t||(null===t&&(e.innerHTML=""),"string"!=typeof t&&"number"!=typeof t||(e.innerHTML=String(t))),e}function r(e){if(!e.getClientRects().length)return{top:0,left:0};const t=e.getBoundingClientRect(),n=(0,s.ensureNotNull)(e.ownerDocument.defaultView);return{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}}function l(e){const t=getComputedStyle(e);let n,s={top:0,left:0};if("fixed"===t.position)n=e.getBoundingClientRect();else{n=r(e);const t=e.ownerDocument;let o=e.offsetParent||t.documentElement;for(;o&&(o===t.body||o===t.documentElement)&&"static"===getComputedStyle(o).position;)o=o.parentElement;o&&o!==e&&1===o.nodeType&&(s=r(o),s.top+=parseFloat(getComputedStyle(o).borderTopWidth),s.left+=parseFloat(getComputedStyle(o).borderLeftWidth))}return{top:n.top-s.top-parseFloat(t.marginTop),left:n.left-s.left-parseFloat(t.marginLeft)}}},996038:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>o});var s=n(896108);const o={SmallHeight:s["small-height-breakpoint"],TabletSmall:s["tablet-small-breakpoint"],TabletNormal:s["tablet-normal-breakpoint"]}},789749:(e,t,n)=>{"use strict";n.d(t,{CachedLogo:()=>h});var s=n(50959),o=n(497754),i=n.n(o),r=n(361701),l=n(439067),a=n(41192),d=n(35089),u=n(855393),c=n(979566);function h(e){const{className:t,placeholderLetter:n,url1:o,url2:h,size:f="xxxsmall"}=e,g=(0,s.useRef)(null),v=(0,s.useRef)(null),b=(0,s.useRef)(null),_=(0,s.useRef)(null),S=(0,s.useRef)(null),y=(0,s.useRef)(null);return(0,u.useIsomorphicLayoutEffect)((()=>{const e=void 0===o?[]:void 0===h?[o]:[o,h],t=y.current=(n=e,Promise.all(n.map((e=>(0,d.getImage)(`symbol_logo_${e}`,e,p).then((e=>e.cloneNode()))))));var n;t.catch((()=>[])).then((e=>{if(t===y.current)switch(e.length){case 0:b.current?.classList.add(c.hidden),v.current?.classList.add(r.hiddenCircleLogoClass),g.current?.classList.remove(r.hiddenCircleLogoClass);break;case 1:m(v.current,e[0]),b.current?.classList.add(c.hidden),v.current?.classList.remove(r.hiddenCircleLogoClass),g.current?.classList.add(r.hiddenCircleLogoClass);break;case 2:m(_.current,e[0]),m(S.current,e[1]),b.current?.classList.remove(c.hidden),v.current?.classList.add(r.hiddenCircleLogoClass),g.current?.classList.add(r.hiddenCircleLogoClass)}}))}),[o,h]),s.createElement("span",{className:i()(t,c.container)},s.createElement("span",{ref:b,className:i()(c.pairContainer,c.hidden)},s.createElement("span",{className:(0,a.getBlockStyleClasses)(f)},s.createElement("span",{ref:S,className:i()(c.logo,(0,
a.getLogoStyleClasses)(f))}),s.createElement("span",{ref:_,className:i()(c.logo,(0,a.getLogoStyleClasses)(f))}))),s.createElement("span",{ref:v,className:i()(c.logo,r.hiddenCircleLogoClass,(0,l.getStyleClasses)(f))}),s.createElement("span",{ref:g,className:i()(c.logo,(0,l.getStyleClasses)(f))},s.createElement(r.CircleLogo,{size:f,placeholderLetter:n})))}function m(e,t){e&&(e.innerHTML="",e.appendChild(t))}function p(e){e.crossOrigin="",e.decoding="async"}},190410:(e,t,n)=>{"use strict";n.d(t,{DialogHeaderContext:()=>s});const s=n(50959).createContext({setHideClose:()=>{}})},366171:(e,t,n)=>{"use strict";n.d(t,{SymbolSearchDialogFooter:()=>l});var s=n(50959),o=n(497754),i=n.n(o),r=n(434625);function l(e){const{className:t,children:n}=e;return s.createElement("div",{className:i()(r.footer,t)},n)}},912015:(e,t,n)=>{"use strict";n.d(t,{isPlatformMobile:()=>o});var s=n(69111);n(440891),n(601227);function o(){return!(0,s.isOnMobileAppPage)("any")&&(window.matchMedia("(min-width: 602px) and (min-height: 445px)").matches,!1)}},856105:(e,t,n)=>{"use strict";n.d(t,{showWatchlistSearchDialog:()=>P});var s=n(50959),o=n(650151),i=n(609838),r=n(972535),l=n(743766),a=n(113060);function d(e){const{targetSymbol:t,store:n,children:o,handleTargetSymbolRemoved:i}=e,[r,d]=(0,s.useState)(null),[c,h]=(0,s.useState)(t),[m,p]=(0,s.useState)(null),f=(0,s.useRef)(null),g=(0,s.useMemo)((()=>({selectedAction:r,setSelectedAction:e=>d(e),isSpreadOrMultipleMode:u,addAfter:c,clearTargetSymbol:v,highlighted:m,highlight:b})),[r,d,c,m,p]);return(0,s.useEffect)((()=>()=>{null!==f.current&&clearTimeout(f.current)}),[]),s.createElement(l.Provider,{store:n},s.createElement(a.SymbolSearchWatchlistContext.Provider,{value:g},o));function v(){h(void 0),i()}function b(e){p(e),null!==f.current&&clearTimeout(f.current),f.current=setTimeout((()=>p(null)),500)}}function u(e,t){const n=!!t.current&&t.current.value.includes(",");return e||n}function c(e,t,n){return o=>s.createElement(d,{...o,store:e,targetSymbol:n,handleTargetSymbolRemoved:t})}var h=n(743717),m=n(406047),p=n(254931),f=n(979359),g=n(607898);function v(e,t){const n=(0,p.getCurrentViewableListByWidgetId)(e,f.WATCHLIST_WIDGET_ID),{fullSymbolName:s}=t;return null===n||void 0===s?{}:{existInWatchlist:n.symbols.includes(s)}}function b(e){return(0,m.bindActionCreators)({addToWatchlist:g.addSymbolsThunk,removeFromWatchlist:g.removeSymbolsThunk,findInWatchlist:g.findInWatchlistThunk},e)}function _(e){return(0,m.bindActionCreators)({addToWatchlist:g.addSymbolsThunk,removeFromWatchlist:g.removeSymbolsThunk,findInWatchlist:g.findInWatchlistThunk},e)}var S=n(549928),y=n(497754),w=n.n(y),x=n(944080),C=n(180185),E=n(366171),k=n(878112),T=n(138772);function I(e){const{icon:t,title:n,className:o,iconClassName:i,a11yLabel:r}=e,l=Boolean(t);return s.createElement("kbd",{className:y(T.button,l&&T.withIcon,o)},l?s.createElement(s.Fragment,null,s.createElement(k.Icon,{"aria-hidden":!0,className:y(T.buttonIcon,i),icon:t}),s.createElement("span",{className:T["a11y-text"]
},n)):r?s.createElement(s.Fragment,null,s.createElement("span",{"aria-hidden":!0},n),s.createElement("span",{className:T["a11y-text"]},r)):n)}var M=n(72952),N=n(755705);function L(e){const{searchRef:t,searchSpreads:r}=(0,o.ensureNotNull)((0,s.useContext)(x.SymbolSearchItemsDialogContext)),{isSpreadOrMultipleMode:l}=(0,o.ensureNotNull)((0,s.useContext)(a.SymbolSearchWatchlistContext)),d=l(r,t);return s.createElement(E.SymbolSearchDialogFooter,{className:N.footer},s.createElement("div",{className:N.shortcuts},d?s.createElement("div",{className:N.buttonGroup},s.createElement(I,{title:"Enter",className:N.button})):s.createElement(s.Fragment,null,s.createElement("div",{className:N.buttonGroup},u(),"+",s.createElement(I,{title:i.t(null,void 0,n(739283)),className:N.button})),i.t(null,void 0,n(599137)),s.createElement("div",{className:N.buttonGroup},u(),"+",s.createElement(I,{title:"Enter",className:N.button})))),s.createElement("div",{className:N.text},i.t(null,void 0,n(788402))));function u(){return C.isMacKeyboard?s.createElement(I,{title:"Shift",icon:M,className:w()(N.button,N.withIcon),iconClassName:N.icon}):s.createElement(I,{title:"Shift",className:N.button})}}n(912015);const A=!1;let D=null;function P(e){const{fullscreen:t,onSearchComplete:a,targetSymbol:d}=e;let u=!1;const m={dialogTitle:i.t(null,void 0,n(218542)),fullscreen:t,onSearchComplete:e=>u?a(e):a(e,d)},p=()=>u=!0;(0,h.initSymbolListService)().then((e=>{const t=D=(0,S.loadNewSymbolSearch)().then((a=>{if(t!==D)return;const{showSymbolSearchItemsDialog:u,Components:h}=a;var f,g;u({...m,symbolTypeFilter:R,searchInitiationPoint:"watchlist",wrapper:c(e.store,p,d),dialog:(g=(0,o.ensureNotNull)(h.SymbolSearchWatchlistDialog),(0,l.connect)(null,_)(g)),contentItem:(f=(0,o.ensureNotNull)(h.SymbolSearchWatchlistDialogContentItem),(0,l.connect)(v,b)(f)),placeholder:A?void 0:i.t(null,void 0,n(508573)),footer:r.mobiletouch?void 0:s.createElement(L)})}))}))}function R(e){return e}},944080:(e,t,n)=>{"use strict";n.d(t,{SymbolSearchItemsDialogContext:()=>s});const s=n(50959).createContext(null)},227570:(e,t,n)=>{"use strict";n.d(t,{useActiveDescendant:()=>i});var s=n(50959),o=n(718736);function i(e,t=[]){const[n,i]=(0,s.useState)(!1),r=(0,o.useFunctionalRefObject)(e);return(0,s.useLayoutEffect)((()=>{const e=r.current;if(null===e)return;const t=e=>{switch(e.type){case"active-descendant-focus":i(!0);break;case"active-descendant-blur":i(!1)}};return e.addEventListener("active-descendant-focus",t),e.addEventListener("active-descendant-blur",t),()=>{e.removeEventListener("active-descendant-focus",t),e.removeEventListener("active-descendant-blur",t)}}),t),[r,n]}},276630:(e,t,n)=>{"use strict";n.r(t),n.d(t,{watchListSaga:()=>$});var s=n(129885),o=n(746212),i=n(344557),r=n(650151),l=n(642659),a=n(329452);class d extends l.BrokerService{constructor(e){super(e,"PositionsWatcher"),this.positionsUpdate=new a.Delegate,this._symbols=[],this._symbolPositions={}}positions(e){return-1!==this._symbols.indexOf(e)?this._symbolPositions[e]:[]}setSymbols(e){
const t=Object.keys(this._symbolPositions);this._symbols.forEach((n=>{-1===e.indexOf(n)&&-1!==t.indexOf(n)&&delete this._symbolPositions[n]}));const n=[];e.forEach((e=>{-1===this._symbols.indexOf(e)&&(n.push(e),this._symbolPositions[e]=[])})),this._symbols=e,n.length&&this._requestPositions(n)}stopService(){this._clearPositions();(0,r.ensure)(this.activeBroker()).positionUpdate.unsubscribe(this,this._positionUpdate)}startService(){this._symbols&&this._requestPositions(this._symbols);(0,r.ensure)(this.activeBroker()).positionUpdate.subscribe(this,this._positionUpdate)}_clearPositions(){Object.keys(this._symbolPositions).forEach((e=>{this._symbolPositions[e].length&&(this._symbolPositions[e]=[],this.positionsUpdate.fire(e,[]))}))}_positionUpdate(e){const t=e.symbol;if(-1===this._symbols.indexOf(t))return;const n=this._symbolPositions[t],s=n.reduce(((t,n,s)=>n.id===e.id?s:t),-1),o=0===e.qty;-1!==s&&o?n.splice(s,1):-1!==s||o?n[s]=e:n.push(e),this.positionsUpdate.fire(t,n)}_requestPositions(e){const t=this.activeBroker();t&&e.length&&t.positions().then((t=>{t.forEach((t=>{-1!==e.indexOf(t.symbol)&&this._positionUpdate(t)}))}))}}var u=n(219769),c=n(254931),h=n(706474);const m=(0,n(671945).getLogger)("Platform.Model.Watchlist");function*p(e){const t=(n=e,(0,o.eventChannel)((e=>{const t={};return n.positionsUpdate.subscribe(t,((t,n)=>{const[s]=n;e((0,u.updatePosition)(t,s?(0,h.toPositionRecord)(s):void 0))})),()=>n.positionsUpdate.unsubscribeAll(t)})));var n;try{for(;;){const e=yield(0,s.take)(t),n=(0,c.positionSelector)(yield(0,s.select)(),e.symbol);(!n||!e.position||e.position&&n.side!==e.position.side)&&(yield(0,s.put)(e))}}finally{t.close()}}function*f(e){const t=yield(0,s.call)(i.waitTradingService),n=new d(t);yield(0,s.fork)(p,n);let o=null;function*r(){const t=(0,c.getCurrentViewableListByWidgetId)(yield(0,s.select)(),e);if(o!==t){const e=null===t?[]:t.symbols;m.logNormal(`Setting watched positions: ${e}`),n.setSymbols(e);const i={};for(const t of e){const[e]=n.positions(t);i[t]=e?(0,h.toPositionRecord)(e):void 0}yield(0,s.put)((0,u.updateBulkPositions)(i)),o=t}}yield(0,s.call)(r),yield(0,s.takeEvery)("*",r)}var g=n(70644),v=n(979359),b=n(530401),_=n(688401),S=n(363940),y=n(697169),w=n(354364);function*x(){yield(0,s.takeLatest)(g.INIT_WIDGET,(function*(e){yield(0,s.call)(E)}))}function*C(){0}function*E(){const e=(0,c.getGlobalActiveID)(yield(0,s.select)());null===e&&(yield(0,s.put)((0,y.asAction)((0,S.getActiveWatchlistThunk)(null)))),yield(0,s.put)((0,u.updateWidget)(v.WATCHLIST_WIDGET_ID,{listId:e}));const t=_.linking.proSymbol.value();t&&(yield(0,s.put)((0,u.updateWidget)(v.WATCHLIST_WIDGET_ID,{scrollToId:{id:t}})))}function*k(){let e=(0,c.getGlobalActiveID)(yield(0,s.select)());for(;;){yield(0,s.take)("*");const t=(0,c.getGlobalActiveID)(yield(0,s.select)());e!==t&&(e=t,yield(0,s.put)((0,u.updateWidget)(v.WATCHLIST_WIDGET_ID,{listId:t,selectedSymbols:[]})))}}function*T(){yield(0,s.takeEvery)(g.UPDATE_WIDGET,I),yield(0,s.takeEvery)(g.UPDATE_WIDGET_OPTIONS,M)}function*I(e){const t=new b.WatchlistWidgetViewState
;void 0!==e.widget.columns&&(t.setColumns(e.widget.columns),(0,w.expandWatchlist)()),void 0!==e.widget.tickerType&&((0,w.setInitialTickerType)(e.widget.tickerType),(0,w.expandWatchlist)())}function*M(e){const t=new b.WatchlistWidgetViewState;void 0!==e.options.isLogoEnabled&&(t.setIsLogoEnabled(e.options.isLogoEnabled),(0,w.expandWatchlist)()),void 0!==e.options.isTableView&&(t.setIsTableView(e.options.isTableView),(0,w.expandWatchlist)())}var N,L=n(429498),A=n(626800),D=n(607898),P=n(609838),R=n(311804),W=n(621327),O=n(856105),B=n(398171),F=n(677299),V=(n(41899),n(833366));!function(e){e.Add="add-action",e.Move="move-action",e.AddSelected="add-selected-action",e.MoveSelected="move-selected-action"}(N||(N={}));class H{constructor(e){this._onMoveSymbolsToList=new a.Delegate,this._onAddSymbolsToList=new a.Delegate,this._onMoveSymbolsToNewList=new a.Delegate,this._onAddSymbolsToNewList=new a.Delegate,this._onCreateSection=new a.Delegate,this._onAddSymbols=new a.Delegate,this._onRemoveSymbol=new a.Delegate,this._addEconomyActions=!1,this._isAddToCompareAllowed=!0,this._isAddNoteAllowed=!0,this._getLists=null,this._lists=[],this._transferActions=[],this._service=null,this._trackEvent=null,this._chartWidgetCollection=(0,R.chartWidgetCollectionService)(),this._allowExtendTimeScale=Boolean(TVSettings.getBool("showAddSymbolDialog.extendCheckboxState")),this._detailsWebviewController=null,this._colorManager=null,this._getTransferActionByType=e=>()=>{},this._updateList=(e,t)=>{},this._onTransferActionExecute=(e,t)=>{},this._getIsChecked=e=>!1,this._getIndeterminate=e=>!1,this._mapListsToActions=e=>[],this._createFinancialsAction=()=>null,this._createDetailsAction=()=>null;const{...t}=e;this._params=t}setWidgetId(e){this._widgetId=e}setList(e){this._list=e}setSymbol(e){this._symbol=e}setSelectedSymbols(e){this._selectedSymbols=new Set(e)}setShortName(e){this._shortName=e}setListGetter(e){this._getLists=e}setMenuSize(e){this._menuSize=e}setTickerUrl(e){this._tickerUrl=e}setAddEconomyActions(e){this._addEconomyActions=e}setTrackEventCb(e){this._trackEvent=e}setIsAddingSymbolsAllowed(e){this._isAddingSymbolsAllowed=e}setIsAddToCompareAllowed(e){this._isAddToCompareAllowed=e}setIsAddNoteAllowed(e){this._isAddNoteAllowed=e}onMoveSymbolsToList(){return this._onMoveSymbolsToList}onAddSymbolsToList(){return this._onAddSymbolsToList}onMoveSymbolsToNewList(){return this._onMoveSymbolsToNewList}onAddSymbolsToNewList(){return this._onAddSymbolsToNewList}onCreateSection(){return this._onCreateSection}onAddSymbols(){return this._onAddSymbols}onRemoveSymbol(){return this._onRemoveSymbol}getContextMenuActions(){return this._getLibraryContextMenuActions()}_getTVContextMenuActions(){return[]}_getLibraryContextMenuActions(){const e=[],t=this._selectedSymbols.size<=1||!this._selectedSymbols.has(this._symbol);return this._isAddingSymbolsAllowed&&this._list&&e.push(this._createAddSymbolAction()),this._isAddToCompareAllowed&&(e.length>0&&!this._params.isMultipleListAllowed&&e.push(new W.Separator),
t?e.push(this._createAddSymbolToCompareAction()):this._selectedSymbols.size<=10&&e.push(this._createAddSelectionToCompareAction())),e}_createUnflagAllSymbolsAction(){return null}_createNewSectionAction(){return null}_createAddSymbolAction(){return new W.Action({actionId:"Watchlist.AddSymbol",options:{label:P.t(null,void 0,n(218542)),icon:V,disabled:!1,onExecute:()=>{(0,O.showWatchlistSearchDialog)({fullscreen:1===this._menuSize,targetSymbol:this._symbol,onSearchComplete:(e,t)=>{(0,F.runOrSigninWithFeature)((()=>{const n=e.map((e=>e.symbol));this._onAddSymbols.fire(n,t)}),{feature:"watchList",source:"add symbol to watchlist"})}})}}})}_createUnflagSymbolAction(){return null}_createFlagSymbolAction(){return[]}_createFlagAllSelectedAction(){return[]}_createUnflagAllSelectedAction(){return null}_createTextNotesAction(){return null}_createAddSymbolToCompareAction(){return new W.Action({actionId:"Watchlist.AddSymbolToCompare",options:{onExecute:()=>{this._chartWidgetCollection?.activeChartWidget.value().addCompareAsOverlay(this._symbol,this._allowExtendTimeScale)},label:P.t(null,{replace:{symbol:this._shortName}},n(958471))}})}_createAddSelectionToCompareAction(){return new W.Action({actionId:"Watchlist.AddSelectedSymbolsToCompare ",options:{onExecute:async()=>{const e=Array.from(this._selectedSymbols).filter((e=>!(0,B.isSeparatorItem)(e)));for(const t of e)await(this._chartWidgetCollection?.activeChartWidget.value().addCompareAsOverlay(t,this._allowExtendTimeScale))},label:P.t(null,void 0,n(687186))}})}_createNewListAction(e,t){return new W.Action({actionId:"Watchlist.Create",options:{label:appendEllipsis(P.t(null,void 0,n(222556))),onExecute:()=>{t()},icon:1===this._menuSize?addToListIcon:void 0}})}_getCreateNewListActionByType(e){return null}_createSymbolTransferAction(e,t,n){return null}_updateAction(e,t,n){e.update(t),n&&e.custom()?.update(n)}_createRemoveSymbolAction(){return null}_createOpenSymbolAction(){return null}_createEconomyActions(){return[]}_createAddSymbolToAction(){return null}_createAddSelectionToAction(){return null}}var U=n(529596),z=n(440891);function*j(e){const t=(0,o.eventChannel)((t=>{const n=()=>{t((0,D.updateScrollToIdThunk)(e,_.linking.proSymbol.value()))};return _.linking.proSymbol.subscribe(n),()=>_.linking.proSymbol.unsubscribe(n)}));try{for(;;)yield(0,s.put)(yield(0,s.take)(t))}finally{t.close()}}function*G(e){yield(0,s.takeLatest)(g.SELECT_NEXT_AVAILABLE_SYMBOL,(function*(t){const{widgetId:n,currentSymbol:o,keyboardAction:i,cancelSetOnChart:r}=t,l=(0,c.getCurrentViewableListByWidgetId)(yield(0,s.select)(),n);if(null===l)return;const a=l.symbols,d=(0,h.findNextAvailableSymbol)(a.indexOf(o),a,i);d&&(yield(0,s.put)((0,u.updateWidget)(n,{selectedSymbols:[d]})),r||_.linking.setSymbolAndLogInitiator(d,e))}))}function*Q(e,t){{let n=null;try{const i=z.enabled("multiple_watchlists"),l=new H({isMultipleListAllowed:i,componentLogName:t}),a=n=(0,o.eventChannel)((e=>{const t={};return l.onAddSymbols().subscribe(t,((t,n)=>e((0,D.addSymbolsThunk)(v.WATCHLIST_WIDGET_ID,t,n)))),
l.onCreateSection().subscribe(t,((t,n)=>e((0,D.insertSymbolBeforeThunk)(v.WATCHLIST_WIDGET_ID,t,n)))),l.onCreateSection().subscribe(t,((t,n)=>e((0,D.findInWatchlistThunk)(v.WATCHLIST_WIDGET_ID,n)))),l.onRemoveSymbol().subscribe(t,((t,n,s)=>e((0,D.removeSymbolsThunk)(t,[n],s)))),i&&(l.onAddSymbolsToList().subscribe(t,((t,n,s,o)=>e((0,D.addSymbolsToCustomListThunk)(t,n,s,void 0,o)))),l.onMoveSymbolsToList().subscribe(t,((t,n,s)=>e((0,D.moveSymbolsToCustomListThunk)(t,n,s)))),l.onAddSymbolsToNewList().subscribe(t,(t=>e((0,D.userCreateWatchlistThunk)(null,{symbols:t})))),l.onMoveSymbolsToNewList().subscribe(t,((t,n)=>e((0,D.userCreateWatchlistThunk)(null,{symbols:n},t))))),()=>{l.onAddSymbolsToList().unsubscribeAll(t),l.onMoveSymbolsToList().unsubscribeAll(t),l.onAddSymbolsToNewList().unsubscribeAll(t),l.onMoveSymbolsToNewList().unsubscribeAll(t),l.onCreateSection().unsubscribeAll(t),l.onRemoveSymbol().unsubscribeAll(t)}}));yield(0,s.takeEvery)(a,(function*(e){yield(0,s.put)(e)}));const d=yield(0,s.takeLatest)(g.SHOW_CONTEXT_MENU,(function*(t){const{symbol:n,position:o,widgetId:i,size:a}=t;if(e!==i)return;const{isDeletable:d}=(0,c.widgetOptionsSelector)(yield(0,s.select)(),i),u=(0,c.selectedSymbolsSelector)(yield(0,s.select)(),i),h=(0,r.ensureNotNull)((0,c.widgetSelector)(yield(0,s.select)(),i).listId);let m;m=(0,c.getCustomListById)(yield(0,s.select)(),h);const p=yield(0,s.fork)((function*(){return yield(0,s.put)((0,y.asAction)((0,S.getCustomWatchlistsThunk)(null))),yield(0,s.take)(L.setup),(0,c.getCustomLists)(yield(0,s.select)())})),f=p.toPromise();l.setWidgetId(i),l.setListGetter((()=>f)),l.setList((0,r.ensureNotNull)(m)),l.setSelectedSymbols(u),l.setSymbol(n),l.setShortName((0,A.safeShortName)(n)),l.setMenuSize(a),l.setIsAddingSymbolsAllowed(d),yield(0,s.call)(U.ContextMenuManager.showMenu,l.getContextMenuActions(),o,{mode:1===a?"drawer":void 0,returnFocus:!0,takeFocus:!0},void 0,(()=>{l.setListGetter(null),p.cancel()}))}));yield(0,s.join)(d)}finally{null!==n&&n.close()}}}function*q(e){yield(0,s.takeLatest)(g.SELECT_ALL_SYMBOLS,(function*(t){const{widgetId:n}=t;if(e!==n)return;const o=(0,c.getCurrentViewableListByWidgetId)(yield(0,s.select)(),n);null!==o&&(yield(0,s.put)((0,u.updateWidget)(n,{selectedSymbols:o.symbols})))}))}var J=n(601227);function*$(e){yield(0,s.fork)(x),yield(0,s.fork)(k),yield(0,s.fork)(q,v.WATCHLIST_WIDGET_ID),yield(0,s.fork)(Q,v.WATCHLIST_WIDGET_ID,e),yield(0,s.fork)(j,v.WATCHLIST_WIDGET_ID),yield(0,s.fork)(G,e),(0,J.onWidget)()||(yield(0,s.fork)(f,v.WATCHLIST_WIDGET_ID),yield(0,s.fork)(C)),yield(0,s.fork)(T)}},25091:(e,t,n)=>{"use strict";n.r(t),n.d(t,{WatchListApi:()=>m});var s=n(650151),o=n(740662),i=n(706474),r=n(924910),l=n(671945),a=n(254931),d=n(363940),u=n(595241);const c=(0,l.getLogger)("WatchList.Api");function h(e){return{id:e.id,symbols:e.symbols,title:e.name}}class m{constructor(e){this._store=e}defaultList(){return(0,u.defaultWatchlistSymbols)()}getList(e){const t=this._store.getState();if(!e){const e=this.getActiveListId();return e?(0,s.ensureNotNull)((0,
a.getCustomListById)(t,e)).symbols:null}const n=(0,a.getCustomListById)(t,e);return null===n?null:n.symbols}getActiveListId(){return(0,a.getGlobalActiveID)(this._store.getState())}setActiveList(e){this._store.dispatch((0,d.setActiveWatchlistThunk)(null,{id:e,type:"custom"}))}getAllLists(){const e=(0,a.getCustomLists)(this._store.getState()),t={};return e.forEach((e=>{t[e.id]=h(e)})),0===Object.keys(t).length?null:t}setList(e){const t=this.getActiveListId();t&&(c.logWarn("`setList` is deprecated. Use `updateList` instead"),this.updateList(t,e))}updateList(e,t){const n=(0,a.getCustomListById)(this._store.getState(),e);null!==n&&this._store.dispatch((0,d.replaceWatchlistSymbolsThunk)(null,n,t))}renameList(e,t){const n=(0,a.getCustomListById)(this._store.getState(),e);null!==n&&this._store.dispatch((0,d.renameWatchlistThunk)(null,n,t))}createList(e,t=[]){if(void 0===e)return null;let n=(0,r.randomHash)();for(;null!==this.getList(n);)n=(0,r.randomHash)();const s=(0,i.createWatchList)(n,e,t);return this._store.dispatch((0,d.createWatchlistThunk)(s)),h(s)}saveList(e){const{id:t,title:n,symbols:s}=e,o=(0,a.getCustomListsMap)(this._store.getState()),r=(0,i.createWatchList)(t,n,s);return void 0!==o[t]?(this._store.dispatch((0,d.putCustomWatchlistsThunk)(null,r)),!1):(this._store.dispatch((0,d.createWatchlistThunk)(r)),!0)}deleteList(e){const t=(0,a.getCustomListById)(this._store.getState(),e);null!==t&&this._store.dispatch((0,d.removeWatchlistThunk)(null,t))}onListChanged(){return o.onListChanged}onActiveListChanged(){return o.onActiveListChanged}onListAdded(){return o.onListAdded}onListRemoved(){return o.onListRemoved}onListRenamed(){return o.onListRenamed}}},399244:(e,t,n)=>{"use strict";n.r(t),n.d(t,{WatchList:()=>Rt});var s=n(743766),o=n(406047),i=n(50959),r=n(650151),l=n(609838),a=n(354364),d=n(180185),u=n(497754),c=n.n(u),h=n(132455),m=n(102478),p=n(28466),f=n(626800),g=n(583315);function v(e){const{id:t,className:n}=e;return i.createElement("div",{className:c()(g.node,n)},(0,f.safeShortName)(t))}var b=n(733393),_=n(219769),S=n(535842),y=n(37914),w=n(744471),x=n(972535),C=n(688401),E=n(574266),k=n(706474),T=n(871645),I=n(861321);const M={market:"marketOpen",pre_market:"marketPre",post_market:"marketPost",out_of_session:"marketClose",holiday:"marketHoliday"},N={market:l.t(null,void 0,n(241410)),pre_market:l.t(null,void 0,n(545265)),post_market:l.t(null,void 0,n(178104)),out_of_session:l.t(null,void 0,n(762464)),holiday:l.t(null,void 0,n(987845))},L={delayed_streaming:l.t(null,void 0,n(739688)),pulsed:l.t(null,void 0,n(739688)),streaming:l.t(null,void 0,n(303058)),endofday:l.t(null,void 0,n(328304))};function A(e){const t=N[e];return`<span class="${I[M[e]]}">${(0,T.htmlEscape)(t)}</span>`}var D=n(398171),P=n(800296),R=n(607898),W=n(460925),O=n(710069);const B=(0,s.connect)(null,(function(e){return(0,o.bindActionCreators)({removeSymbol:R.removeSymbolsThunk},e)}))((function(e){return i.createElement(P.ListItemButton,{onClick:function(t){const{widgetId:n,removeSymbol:s,symbolName:o}=e;t.preventDefault(),s(n,[o])},
className:c()(e.className,O.removeButton),icon:W})}));var F=n(57174),V=n(310238);function H(e){if(3===e.nodeType&&e.data.trim())return e;if(e.childNodes)for(let t=e.childNodes.length;t--;){const n=H(e.childNodes[t]);if(n)return n}return null}function U(e,t){const n=t.parentNode;if(!n)return;const s=t.nextSibling;s?n.insertBefore(e,s):n.appendChild(e)}function z(e){const t=H(e);if(t){const e=t.data;if(t.parentNode&&t.parentNode.tagName&&"sup"===t.parentNode.tagName.toLowerCase())return;const n=/^([^]*)(\S)(\s*)$/.exec(e);if(n){t.data=n[1];const e=document.createElement("sup");e.textContent=n[2],U(e,t),n[3]&&U(document.createTextNode(n[3]),e)}}}var j=n(533924);class G extends i.PureComponent{constructor(){super(...arguments),this._ref=null,this._highlightTimeout=null,this._context=null,this._values=null,this._handleRef=e=>{this._ref=e}}componentWillUnmount(){this._highlightTimeout&&clearTimeout(this._highlightTimeout)}render(){const{isHidden:e,className:t,ellipsis:n}=this.props;return i.createElement("span",{className:u(t,j.cell,j.last,e&&j.hidden)},i.createElement("span",{translate:"no",className:u(j.inner,n&&j.ellipsis),ref:this._handleRef}))}update(e){const{values:t}=e,n=this._values;if(void 0===t.last_price||null===this._ref)return;const s=null===n||n.pricescale!==t.pricescale||n.minmov!==t.minmov||n.fractional!==t.fractional||n.minmove2!==t.minmove2,o=null===n||n.last_price!==t.last_price;if(!s&&!o)return;this._values={...t};const{format:i,pricescale:r=100,minmov:l=1,fractional:a,minmove2:d,variable_tick_size:u}=t;let c=null;c=V.customFormatters?.priceFormatterFactory?.(null,"")??null,null==c&&(c=null);const h=c??new F.LastChangeFormatter({format:i,priceScale:r,minMove:l,fractional:a,minMove2:d,variableMinTick:u}),m=t.last_price,p=h.format(t.last_price),f=null!==this._context?this._context.currentText:null,g=null!==this._context?this._context.currentValue:null,v=null===c&&h.hasForexAdditionalPrecision(),b=this._context={forexMode:v,currentValue:m,currentText:p,previousValue:g,previousText:f};this._ref.innerText=p,this._processHighlight(this._ref,b),v&&z(this._ref)}_processNumericDiff(e){const{previousValue:t,previousText:n,currentValue:s,currentText:o,forexMode:i}=e;if(null===t||null===n||null===this._ref)return;const r=u(s>t&&j.plus,s<t&&j.minus),l=Math.min(n.length,o.length);let a=0;for(;a<l&&n.charAt(a)===o.charAt(a);)a++;const d=o.slice(0,a),c=o.slice(a);c.length&&r?this._ref.innerHTML=d+`<span class="${r}">${c}</span>`:this._ref.innerText=o,i&&z(this._ref)}_processHighlight(e,t){const{previousValue:n,currentValue:s}=t;null!==n&&n!==s&&(null!==this._highlightTimeout&&(clearTimeout(this._highlightTimeout),this._highlightTimeout=null),e.classList.toggle(j.highlightUp,s>n),e.classList.toggle(j.highlightDown,s<n),this._highlightTimeout=setTimeout((()=>{e.classList.remove(j.highlightUp,j.highlightDown),this._processNumericDiff(t)}),500))}}var Q=n(150335);const q=new(n(563223).NumericFormatter);class J extends i.PureComponent{constructor(){super(...arguments),this._ref=null,this._handleRef=e=>{this._ref=e}}render(){
const{isHidden:e,className:t}=this.props;return i.createElement("span",{className:u(t,j.cell,j.change,e&&j.hidden)},i.createElement("span",{translate:"no",className:j.inner,ref:this._handleRef}))}update(e){const{values:t}=e,n=t.change;if(!(0,Q.isNumber)(n)||null===this._ref)return;const{last_price:s,format:o,pricescale:i=100,minmov:l=1,fractional:a,minmove2:d,type:u,variable_tick_size:c}=t;let h=null;h=V.customFormatters?.priceFormatterFactory?.(null,"")??null,null==h&&(h=null);const m=h??new F.LastChangeFormatter({format:o,priceScale:i,minMove:l,fractional:a,minMove2:d,variableMinTick:c});this._ref.innerText="spread"!==u||a?m.formatChange?m.formatChange((0,r.ensureDefined)(s),(0,r.ensureDefined)(s)-n):"":q.format(n),0!==n&&(this._ref.classList.remove(j.plus,j.minus),this._ref.classList.add(n>0?j.plus:j.minus))}}const $=new(n(276735).PercentageFormatter);class K extends i.PureComponent{constructor(){super(...arguments),this._ref=null,this._handleRef=e=>{this._ref=e}}render(){const{isHidden:e,className:t}=this.props;return i.createElement("span",{className:u(t,j.cell,j.changeInPercents,e&&j.hidden)},i.createElement("span",{translate:"no",className:j.inner,ref:this._handleRef}))}update(e){const{values:t}=e,n=t.change_percent;(0,Q.isNumber)(n)&&null!==this._ref&&(this._ref.innerText=$.format(n),0!==n&&(this._ref.classList.remove(j.plus,j.minus),this._ref.classList.add(n>0?j.plus:j.minus)))}}const X=new(n(459895).VolumeFormatter);class Y extends i.PureComponent{constructor(){super(...arguments),this._ref=null,this._handleRef=e=>{this._ref=e}}render(){const{isHidden:e,className:t}=this.props;return i.createElement("span",{className:u(t,j.cell,j.volume,e&&j.hidden)},i.createElement("span",{translate:"no",className:j.inner,ref:this._handleRef}))}update(e){const{values:t}=e,n=t.volume;void 0===n||null===this._ref||Math.abs(n)>=1e100||(this._ref.innerText=X.format(n),this._ref.setAttribute("data-value",`${n}`))}}var Z=n(678100),ee=(n(972525),n(609970));const te={invalid:"tv-market-status--invalid",market:"tv-market-status--market",out_of_session:"tv-market-status--out-of-session",post_market:"tv-market-status--post-market",pre_market:"tv-market-status--pre-market",loading:"tv-market-status--loading",holiday:"tv-market-status--holiday",replay:"tv-market-status--replay",delisted:"tv-market-status--delisted"},ne={invalid:"tv-market-status__icon--invalid",market:"tv-market-status__icon--market",out_of_session:"tv-market-status__icon--out-of-session",post_market:"tv-market-status__icon--post-market",pre_market:"tv-market-status__icon--pre-market",loading:"tv-market-status__icon--loading",holiday:"tv-market-status__icon--holiday",replay:"tv-market-status__icon--replay",delisted:"tv-market-status__icon--delisted"};class se extends ee.AbstractIndicator{constructor(e){super(e),this._quoteSessionPrefix="market-status-indicator",this._componentClass="tv-market-status",this._extraTitle="",this._init(e)}setStatus(e,t){const n={values:{current_session:"delisted"===this.getValue()?"delisted":e}};this.update(n,t)}getTooltipText(){
let e=super.getTooltipText();return""===e||""!==this._extraTitle&&(e=`${e}, ${this._extraTitle}`),e}setExtraTitle(e){this._extraTitle=e}reset(){this._clearClasses(),this._labelEl.textContent="",this._extraTitle="",this._el.setAttribute("title",""),this._value=""}enableShortMode(e=!0){void 0!==this._labelEl&&this._labelEl.classList.add("i-hidden"),super.enableShortMode()}disableShortMode(){void 0!==this._labelEl&&this._labelEl.classList.remove("i-hidden"),super.disableShortMode()}_renderLabel(){this._labelEl.textContent=this.getLabel()}_getValueFromData(e){return e.values?.typespecs?.includes("discontinued")?"delisted":void 0!==e.values?.current_session?e.values.current_session:this.getValue()}_render(){this._renderLabelElement(),this._sessionStatusIcon?this._renderIconElement():this._renderDotElement(),super._render()}_init(e){const t=Object.assign({},function(){const e={invalid:l.t(null,void 0,n(652969)),market:l.t(null,void 0,n(241410)),out_of_session:l.t(null,void 0,n(762464)),post_market:l.t(null,void 0,n(673897)),pre_market:l.t(null,void 0,n(236018)),loading:l.t(null,void 0,n(786726)),holiday:l.t(null,void 0,n(987845)),delisted:l.t(null,void 0,n(254602)),replay:""};return{classMap:te,iconClassMap:ne,classSuffix:"",data:{},extraTitle:"",labelMap:e,short:!1,showTooltip:!0,tooltipType:"custom"}}(),e);super._init(t),this.setExtraTitle(t.extraTitle),this._render()}_renderLabelElement(){void 0===this._labelEl&&(this._labelEl=document.createElement("span"),this._labelEl.classList.add(`${this._componentClass}__label`),this._labelEl.classList.add(`${this._componentClass}__label${this._classSuffix}`),this._el.appendChild(this._labelEl))}_renderDotElement(){this._el.contains(this._iconEl)&&this._el.removeChild(this._iconEl),void 0===this._dotEl&&(this._dotEl=document.createElement("span"),this._dotEl.classList.add(`${this._componentClass}__dot`),this._dotEl.classList.add(`${this._componentClass}__dot${this._classSuffix}`),this._el.appendChild(this._dotEl))}_renderIconElement(){this._el.contains(this._dotEl)&&this._el.removeChild(this._dotEl),void 0===this._iconEl&&this._value&&(this._iconEl=document.createElement("span"),this._iconEl.classList.add(`${this._componentClass}__icon`),this._iconEl.classList.add(`${this._componentClass}__icon${this._classSuffix}`),this._el.appendChild(this._iconEl))}}var oe=n(878112),ie=n(563140),re=n(531340),le=n(486267);function ae(e,t){const s=y.quoteSessionAdapters.get(e).getLastSymbolData(t.symbol);if(!s)return"";const o=s.values,{format:i,pricescale:r=100,minmov:a=1,fractional:d,minmove2:u,variable_tick_size:c}=o,h=new F.LastChangeFormatter({format:i,priceScale:r,minMove:a,fractional:d,minMove2:u,variableMinTick:c});return`${1===t.side?l.t(null,void 0,n(728257)):l.t(null,void 0,n(13009))} ${Math.abs(t.qty)} @ ${h.format(t.avgPrice)}`}var de=n(254931);const ue=(0,s.connect)((function(e,t){return{position:(0,de.positionSelector)(e,t.symbolName)}}))((function(e){const{position:t,className:n,widgetId:s}=e;if(void 0===t)return null;const o=1===t.side;return i.createElement(oe.Icon,{
className:u("apply-common-tooltip",le.icon,o&&le.long,n),icon:o?ie:re,title:ae(s,t)})}));var ce=n(662654),he=n(789749),me=n(440891);n(831615),n(628616);const pe=(e,t)=>({classSuffix:"--for-symbol-list",el:e,short:!0,symbol:t,manualUpdate:!0,tooltipType:"custom"}),fe=me.enabled("prefer_quote_short_name");class ge extends i.PureComponent{constructor(){super(...arguments),this._nameRef=null,this._descRef=null,this._marketStatusIndicator=null,this._dataModeIndicator=null,this._description=null,this._handleNameContentSet=e=>{if(null===this._nameRef)return;const{tickerType:t,symbolName:n,isTileView:s}=this.props,o={[ce.TickerType.Description]:()=>this._description||(0,f.safeShortName)(n),[ce.TickerType.Ticker]:()=>fe&&e?.short_name?e?.short_name:(0,f.safeShortName)(n)};this._nameRef.textContent=o[s?ce.TickerType.Ticker:t]()},this._handleDescContentSet=()=>{null!==this._descRef&&this.props.isTileView&&(this._descRef.textContent=this._description)},this._handleNameRef=e=>{this._nameRef=e},this._handleDescRef=e=>{this._descRef=e},this._handleMarketStatusRef=e=>{const{symbolName:t}=this.props;if(null!==e){const n=e=>{"delisted"===e?this._marketStatusIndicator?.setSessionStatusIcon(!0):this._marketStatusIndicator?.setSessionStatusIcon(!1)};this._marketStatusIndicator=new se({...pe(e,t),onValueChange:n})}},this._handleDataModeRef=e=>{const{symbolName:t}=this.props;null!==e&&(this._dataModeIndicator=new Z.DataModeIndicator(pe(e,t)))}}componentDidMount(){this._handleNameContentSet(),this._handleDescContentSet()}componentDidUpdate(e){null!==this._nameRef&&(e.tickerType===this.props.tickerType||this.props.isTileView||this._handleNameContentSet())}render(){const{symbolName:e,className:t,logoUrls:n,isTileView:s}=this.props,o=s?"xsmall":"xxxsmall";return i.createElement("div",{className:c()(t,j.symbolName,s&&j.tileSymbolName)},i.createElement("span",{className:c()(j.cell,j.flexCell)},n&&i.createElement(he.CachedLogo,{key:o,className:j.logo,url1:n[0],url2:n[1],placeholderLetter:(0,f.safeShortName)(e)[0],size:o}),i.createElement("div",{className:c()(j.displayContents,s&&j.tileWrap)},i.createElement("div",{className:c()(j.flexCell,s&&j.tileNameContent)},this._renderContent()),s&&i.createElement("div",{className:j.description,ref:this._handleDescRef},this._description))))}update(e){if(null===this._nameRef)return;const{values:t}=e;this._marketStatusIndicator&&this._marketStatusIndicator.update({values:t}),this._dataModeIndicator&&("economic"===t.type&&"endofday"===t.update_mode?this._dataModeIndicator.hide():(this._dataModeIndicator.show(),this._dataModeIndicator.update({values:t}))),this._description=(0,E.getTranslatedSymbolDescription)(t),this._handleNameContentSet(t),this._handleDescContentSet()}_renderContent(){const{isInvalid:e,symbolName:t,shouldDisplayPositions:n,widgetId:s,warningText:o}=this.props;return i.createElement(i.Fragment,null,i.createElement("span",{className:c()(j.inner,j.symbolNameText),ref:this._handleNameRef}),!e&&i.createElement(i.Fragment,null,!1,!o&&i.createElement(i.Fragment,null,i.createElement("span",{
ref:this._handleDataModeRef}),i.createElement("span",{ref:this._handleMarketStatusRef}),n&&i.createElement(ue,{widgetId:s,className:j.positions,symbolName:t}))),!1)}}var ve=n(601227),be=n(670086),_e=n(346827);class Se extends i.PureComponent{constructor(e){super(e),this._injectListClasses=(e,t)=>{if(!i.isValidElement(e))return e;const n=["short_name","last_price","change","change_percent","volume","rchp"].map(((e,t)=>[t,e])).filter((([e,t])=>this.props.columns.includes(t))),[s]=n[0];t===s&&(e=i.cloneElement(e,{className:u(e.props.className,j.firstItem)}));const[o]=n[n.length-1];return t===o&&(e=i.cloneElement(e,{className:u(e.props.className,j.lastItem)})),e},this._lastPrice=i.createRef(),this._change=i.createRef(),this._changeInPercents=i.createRef(),this._volume=i.createRef(),this._prePostMarket=i.createRef(),this._symbolName=i.createRef()}render(){const{id:e,columns:t,tickerType:n,widgetId:s,isDeletable:o,shouldDisplayPositions:r,hasLogo:l,logoUrls:a,isInvalid:d,isActive:c,warningText:h}=this.props,{size:m}=this.context;return i.createElement(i.Fragment,null,i.createElement("div",{className:u(j.indicators,c&&j.active,_e.setNewBlackColor&&j.blackBorder)},!(0,ve.onWidget)()&&!1),i.createElement(be.FragmentMap,{map:this._injectListClasses},i.createElement(ge,{symbolName:e,shouldDisplayPositions:r,ref:this._symbolName,warningText:h,isInvalid:d,widgetId:s,tickerType:n,logoUrls:l?a:void 0,isTileView:!1}),i.createElement(G,{ref:this._lastPrice,isHidden:!t.includes("last_price")}),i.createElement(J,{ref:this._change,isHidden:!t.includes("change")}),i.createElement(K,{ref:this._changeInPercents,isHidden:!t.includes("change_percent")}),i.createElement(Y,{ref:this._volume,isHidden:!t.includes("volume")}),!1),o&&i.createElement("div",{className:j.overlayEnd},i.createElement(B,{className:j.removeButton,widgetId:s,symbolName:e})))}update(e){null!==this._symbolName.current&&this._symbolName.current.update(e),null!==this._lastPrice.current&&this._lastPrice.current.update(e),null!==this._change.current&&this._change.current.update(e),null!==this._changeInPercents.current&&this._changeInPercents.current.update(e),null!==this._volume.current&&this._volume.current.update(e)}}Se.contextType=S.SizeContext;var ye,we=n(306858),xe=n(868333);function Ce(e){if(!e)return!1;const{complete:t,values:n}=e,{"currency-logoid":s,"base-currency-logoid":o,logoid:i}=n;return Boolean(i||s&&o||t)}function Ee(e){const{values:t}=e;return(0,we.removeUsdFromCryptoPairLogos)((0,we.resolveLogoUrls)(t,xe.LogoSize.Medium))}!function(e){e.Pending="pending",e.Resolved="resolved"}(ye||(ye={}));const ke=me.enabled("show_symbol_logos");class Te extends i.PureComponent{constructor(e){super(e),this._observer=null,this._ref=null,this._raf=null,this._onData=e=>{this._data=e,this._resolvedName=e.values.pro_name,null===this._raf&&(this._raf=requestAnimationFrame((()=>{const{id:e}=this.props,{logoUrls:t}=this.state,n=(0,r.ensureDefined)(this._data),{symbolname:s,status:o,errmsg:i}=n;if(e===s){if(this._ref&&this._ref.setAttribute("data-status","resolved"),
ke&&0===t.length&&Ce(n)){const e=Ee(n);e.length>0&&this.setState({logoUrls:e})}"error"===o&&i&&i.toLowerCase().includes("continuous limit")?this.setState({warningText:null}):"error"===o?this.setState({isInvalid:!0}):"sf_data"===o?this.setState({warningText:null}):(null!==this._symbolItem.current&&this._symbolItem.current.update(n),null!==this._symbolTileItem.current&&this._symbolTileItem.current.update(n))}this._handleCustomTooltipSetter("error"===o,n),this._raf=null})))},this._handleCustomTooltipSetter=(e,t)=>{if(null===this._ref)return;if(e)return void this._ref.setAttribute("data-tooltip","");const n=function(e){const{description:t,exchange:n,updateMode:s,marketStatus:o}=e,i=t?`<div class="${I.description}">${(0,T.htmlEscape)(t)}</div>`:null,r=`<span class="${I.dot}">•</span>`,l=[n?`<span>${(0,T.htmlEscape)(n)}</span>`:null,s?`<span>${(0,T.htmlEscape)(L[s])}</span>`:null,o?A(o):null].filter((e=>null!==e)).join(r),a=i?[i,l].join(""):l;return`<div class="${I.wrapper}">${a}</div>`}(this._getSymbolTooltipData(t));n&&this._ref.getAttribute("data-tooltip")!==n&&this._ref.setAttribute("data-tooltip",n)},this._handleRef=e=>{if(null!==e){const t=.25;this._observer=new IntersectionObserver((e=>{e[e.length-1].intersectionRatio<t?this._onInvisible():this._onVisible()}),{threshold:[0,.25,.5,.75,1],root:null,rootMargin:"0px"}),this._observer.observe(e)}this._ref=e},this._handleContextMenu=e=>{e.preventDefault(),this._showContextMenu(e,0)},this._handleSmallContextMenu=e=>{this._showContextMenu(e,1)},this._showContextMenu=(e,t)=>{e.persist();const{showContextMenu:n,id:s,widgetId:o}=this.props;n(o,s,e,t)},this._onSymbolChanged=e=>{const t=this._computeActive(e);t!==this.state.isActive&&this.setState({isActive:t}),(0,w.hide)()},this._onAliasesChange=e=>{if(!e||!e.length||this.state.isActive)return;const t=e.some((e=>this._computeActive(e)));t&&this.setState({isActive:t})},this._symbolItem=i.createRef(),this._symbolTileItem=i.createRef();const t=this._getLastSymbolData();this._resolvedName=t?.values.pro_name;const n=ke&&Ce(t)?Ee((0,r.ensureDefined)(t)):[];this.state={isInvalid:!1,warningText:null,isActive:this._computeActive(C.linking.proSymbol.value()),logoUrls:n}}componentDidMount(){const{id:e,widgetId:t}=this.props;null!==this._ref&&this._ref.setAttribute("data-status","pending");const n=y.quoteSessionAdapters.get(t),s=this._getLastSymbolData();s&&this._onData(s),n.addSymbolDataHandler(e,this._onData),C.linking.proSymbol.subscribe(this._onSymbolChanged),C.linking.symbolNamesList.subscribe(this._onAliasesChange,{callWithLast:!0})}componentDidUpdate(e){this._data&&e.typeView!==this.props.typeView&&this._onData(this._data)}componentWillUnmount(){const{id:e,widgetId:t}=this.props;y.quoteSessionAdapters.get(t).removeSymbolDataHandler(e),this._observer&&this._observer.disconnect(),C.linking.proSymbol.unsubscribe(this._onSymbolChanged),C.linking.symbolNamesList.unsubscribe(this._onAliasesChange),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null)}render(){
const{id:e,columns:t,tickerType:n,isSelected:s,isHighlighted:o,widgetId:r,isDeletable:l,enableContextMenu:a,shouldDisplayPositions:d,isContainedByMultiSelection:c,isFirstListItem:h,isLogoEnabled:m,typeView:p}=this.props,{isInvalid:g,warningText:v,isActive:b,logoUrls:_}=this.state,{size:S}=this.context,y=b?c:s,w=(0,D.isSeparatorItem)(e)||g,C="tile"===p,E={widgetId:r,isDeletable:l,shouldDisplayPositions:d,hasLogo:(C||m)&&ke,logoUrls:_,isInvalid:g,isActive:b,warningText:v,id:e};return i.createElement("div",{className:u(j.symbol,C&&j.tileView,1===S&&j.small,y&&j.selected,b&&j.active,_e.setNewBlackColor&&j.blackBorder,x.mobiletouch&&j.touch,o&&j.highlighted,w&&j.invalid,h&&j.firstRow,"common-tooltip-html","common-tooltip-vertical","apply-common-tooltip"),ref:this._handleRef,"data-tooltip-delay":1500,"data-symbol-full":e,"data-symbol-short":(0,f.safeShortName)(e),"data-active":b,"data-selected":s,onClick:1===S&&a?this._handleSmallContextMenu:void 0,onContextMenu:0===S&&a?this._handleContextMenu:void 0},i.createElement(Se,{...E,ref:this._symbolItem,columns:t,tickerType:n}))}_onVisible(){const{id:e,widgetId:t}=this.props;y.quoteSessionAdapters.get(t).addFastSymbol(e)}_onInvisible(){const{id:e,widgetId:t}=this.props;y.quoteSessionAdapters.get(t).removeFastSymbol(e)}_getSymbolTooltipData(e){const{values:t}=e;let n;n=t.exchange||null;const s="economic"===t.type&&"endofday"===t.update_mode?null:t.update_mode||null;return{description:(0,E.getTranslatedSymbolDescription)(t)||null,exchange:n,updateMode:s,marketStatus:t.current_session||null}}_computeActive(e){if(void 0===e)return!1;let t=(0,k.compareSymbols)(e,this.props.id);return!t&&this._resolvedName&&(t=(0,k.compareSymbols)(e,this._resolvedName)),t}_getLastSymbolData(){const{id:e,widgetId:t}=this.props;return y.quoteSessionAdapters.get(t).getLastSymbolData(e)}}Te.contextType=S.SizeContext;const Ie=(0,s.connect)((function(){const e=(0,de.makeGetIsContainedByMultiSelection)();return(t,n)=>{const{isDeletable:s,enableContextMenu:o,isLogoEnabled:i}=(0,de.widgetOptionsSelector)(t,n.widgetId),r=(0,de.highlightedSymbolsSelector)(t,n.widgetId);return{isDeletable:s,enableContextMenu:o,isContainedByMultiSelection:e(t,{widgetId:n.widgetId,symbol:n.id}),isLogoEnabled:i,shouldDisplayPositions:(0,de.shouldDisplayPositionsSelector)(t,n.widgetId),columns:(0,de.columnsSelector)(t,n.widgetId),tickerType:(0,de.tickerTypeSelector)(t,n.widgetId),isHighlighted:!!r&&r.includes(n.id)}}}),(function(e){return(0,o.bindActionCreators)({showContextMenu:_.showContextMenu},e)}))(Te);var Me=n(975598),Ne=n(32133),Le=n(302946),Ae=n(303230);const De=[{column:"last_price",label:l.t(null,void 0,n(440846))},{column:"change",label:l.t(null,void 0,n(537276))},{column:"change_percent",label:l.t(null,void 0,n(923599))},{column:"volume",label:l.t(null,void 0,n(937644))}];function Pe(e){const{columns:t,updateWidget:n,widgetId:s}=e,{size:o}=(0,i.useContext)(S.SizeContext),r=1===o;return i.createElement(i.Fragment,null,De.map((e=>{const{column:n,label:s}=e;return i.createElement(Le.Checkbox,{"data-role":"menuitem",key:n,
className:c()(Ae.checkbox,r&&Ae.mobile),name:n,label:s,checked:t.includes(n),onChange:l(n)})})));function l(e){return()=>{const o=[...t];o.includes(e)?o.splice(o.indexOf(e),1):o.push(e),n(s,{columns:o})}}}var Re=n(715336);const We=(0,s.connect)((function(e,t){return{columns:(0,de.columnsSelector)(e,t.widgetId)}}),(function(e){return(0,o.bindActionCreators)({updateWidget:_.updateWidget},e)}))((function(e){const{columns:t,updateWidget:n,className:s,widgetId:o}=e,{size:r}=(0,i.useContext)(S.SizeContext),l=1===r;return i.createElement("div",{className:c()(Ae.wrap,s)},i.createElement(Me.AccessibleMenuButton,{content:i.createElement(oe.Icon,{className:Ae.icon,icon:Re}),arrow:!1,className:Ae.columnsMenu,onClose:function(){(0,Ne.trackEvent)("Watchlist","Change columns",t.join(", "))},isDrawer:l,drawerPosition:"Bottom"},i.createElement(Pe,{widgetId:o,columns:t,updateWidget:n})))}));var Oe=n(522224);const Be=i.createContext({});var Fe=n(431520),Ve=n(496818),He=n(385839);const Ue=(0,Fe.isRtl)();function ze(e){const{column:t}=e,{columnResizeDelegate:n,columnResizeStopDelegate:s}=(0,i.useContext)(Be),o=(0,i.useRef)(null);return(0,i.useEffect)((()=>{const e=new Ve.PointerBackend({handle:(0,r.ensureNotNull)(o.current),onDragStart:()=>{o.current?.classList.add(He.active)},onDragMove:e=>{const{current:s,initial:o}=e.detail,i=s.pageX-o.pageX;n?.fire(t,i*(Ue?1:-1))},onDragStop:()=>{o.current?.classList.remove(He.active),s?.fire()}});return()=>{e.destroy()}}),[t,n,s]),i.createElement("div",{className:He.placeholder},i.createElement("div",{ref:o,className:He.wrap},i.createElement("div",{className:He.handle}),i.createElement("div",{className:He.separator})))}var je=n(913042),Ge=n(975361),Qe=n(119850),qe=n(592952);const Je=(0,s.connect)((function(e,t){return{sorting:(0,de.sortingSelector)(e,t.widgetId)}}),(function(e){return(0,o.bindActionCreators)({sortSymbols:R.sortSymbolsThunk},e)}))((function(e){const{widgetId:t,isSortable:s,tickerType:o,column:a,className:d,sortSymbols:c,label:h,sorting:m,reference:f}=e,[g,v]=(0,i.useState)(!1),b=(0,i.useRef)(null),_=(0,i.useRef)(null),y=f??_,[x,C]=(0,Oe.useHover)(),{size:E}=(0,i.useContext)(S.SizeContext),k=(0,i.useContext)(p.CloseDelegateContext),T=1===E,{columnResizeDelegate:I,requestColumnInitWidthDelegate:M,setColumnInitWidthDelegate:N}=(0,i.useContext)(Be),L=(0,i.useMemo)((()=>function(e,t,s,o){if(!t)return"";const i=l.t(null,void 0,n(800074)).format({columnLabel:s});if(null!==e&&e.column===o&&1===e.direction)return`${i} ${Qe}`;return`${i} ${Ge}`}(m,s,h,a)),[m,s,h,a]);return(0,i.useEffect)((()=>{const e=e=>{e===a&&N?.fire(a,(0,r.ensureNotNull)(b.current).getBoundingClientRect().width)};return M?.subscribe(null,e),()=>M?.unsubscribe(null,e)}),[M,N,a]),(0,i.useEffect)((()=>(k.subscribe(null,w.hide),()=>k.unsubscribe(null,w.hide))),[]),(0,i.useEffect)((()=>{s&&(x?(0,w.showOnElement)((0,r.ensureNotNull)(y.current),{content:{type:"html",data:L}}):(0,w.hide)())}),[x,L]),(0,i.useEffect)((()=>{if(m?.column!==a||!s)return;v(!0);const e=setTimeout((()=>v(!1)),5e3);return()=>{clearTimeout(e),v(!1)}
}),[m,a,s]),i.createElement(i.Fragment,null,i.createElement("span",{ref:b,key:a,className:u(qe.columnHeader,d,T&&qe.small)},s&&i.createElement(oe.Icon,{className:u(qe.sortArrow,g&&qe.visible,1!==m?.direction&&qe.desc),icon:je}),i.createElement("button",{...C,type:"button",ref:y,tabIndex:s?0:-1,className:u(qe.label,s&&qe.sortable,g&&qe.sortActivated,qe.accessible),onClick:s?function(){let e={column:a,direction:1};null!==m&&m.column===a&&1===m.direction&&(e={...m,direction:-1});c(t,e,o);const n=e?1===e.direction?"ascending":"descending":"default";(0,Ne.trackEvent)("Watchlist","Sort",`Sort by ${a} ${n}`)}:void 0,"data-column-type":a},h)),I&&i.createElement(ze,{column:a}))}));var $e=n(565631),Ke=n(672197),Xe=n(652420);const Ye=(0,s.connect)((function(e,t){const{shouldDisplayColumnsMenu:n,isTableView:s,isSortable:o}=(0,de.widgetOptionsSelector)(e,t.widgetId),i=(0,de.getCurrentViewableListByWidgetId)(e,t.widgetId);return{shouldDisplayColumnsMenu:n,isTableView:s,isSortable:o,columns:(0,de.columnsSelector)(e,t.widgetId),tickerType:(0,de.tickerTypeSelector)(e,t.widgetId),hasFlagColumn:"custom"===i?.type&&o,sorting:(0,de.sortingSelector)(e,t.widgetId)}}))((function(e){const{columns:t,widgetId:o,shouldDisplayColumnsMenu:r,tickerType:a,hasFlagColumn:d,sorting:u,isTableView:h,isSortable:m}=e,p=(0,s.useDispatch)(),{size:f}=(0,i.useContext)(S.SizeContext),g=(0,i.useRef)(null),v=(0,i.useMemo)((()=>!0),[f,h]),b=(0,i.useMemo)((()=>function(e,t,s){const o=[{column:"short_name",label:{[ce.TickerType.Ticker]:l.t(null,void 0,n(595481)),[ce.TickerType.Description]:l.t(null,void 0,n(178734))}[t],className:Xe.symbolName},{column:"last_price",label:l.t(null,void 0,n(440846)),className:Xe.last},{column:"change",label:l.t(null,void 0,n(762888)),className:Xe.change},{column:"change_percent",label:l.t(null,void 0,n(729369)),className:Xe.changeInPercents},{column:"volume",label:l.t(null,{context:"study"},n(424261)),className:Xe.volume}];0;return o.filter((t=>e.includes(t.column)||s&&"flag"===t.column))}(v?t:["short_name"],v?a:ce.TickerType.Ticker,d)),[t,a,d,v]);return i.createElement("div",{className:Xe.wrap},r&&i.createElement(We,{widgetId:o,className:Xe.menu}),i.createElement("div",{className:Xe.tableHeader},b.map(((e,t)=>i.createElement(Je,{reference:0===t?g:null,widgetId:o,isSortable:m&&v,key:e.column,tickerType:a,label:e.label,column:e.column,className:c()(e.className,0===t&&Xe.firstItem,v&&t===b.length-1&&Xe.lastItem,!v&&Xe.mobileItem)}))),!v&&m&&i.createElement(SortingMenu,{className:Xe.sortingMenu,widgetId:o})),v&&m&&Boolean(u)&&i.createElement("div",{className:Xe.resetContainer},i.createElement($e.AccessibleIconButton,{icon:i.createElement(oe.Icon,{icon:Ke,className:Xe.resetIcon}),className:c()(Xe.reset,"apply-common-tooltip"),onClick:function(){g.current?.focus(),p((0,R.sortSymbolsThunk)(o,null,a)),(0,Ne.trackEvent)("Watchlist","Sort","Sort by custom order")},tooltip:l.t(null,void 0,n(667239))})))}));var Ze=n(409174),et=n(117601),tt=n(621327),nt=n(529596),st=n(856105),ot=n(677299),it=(n(615309),n(347531),
n(748040)),rt=n(993544),lt=n(833366),at=n(491148);var dt=n(356752),ut=n(363940);n(50702);function ct(e,t,n){return async(s,o)=>{const i=o();if(t===n)return;const r=(0,de.getCurrentViewableListByWidgetId)(i,e);if(null===r)return;if("hot"===r.type)return;const{symbols:l}=r,a=l.filter(D.isSeparatorItem),d=(0,k.buildUniqueName)(a,n,!1);l.includes(d)||"custom"===r.type&&await s((0,ut.renameSeparatorThunk)(null,r,t,d))}}function ht(e,t,n){return async(e,t)=>{}}const mt=(0,s.connect)((function(){const e=(0,de.makeGetIsContainedByMultiSelection)();return(t,n)=>{const{isDeletable:s,enableContextMenu:o}=(0,de.widgetOptionsSelector)(t,n.widgetId),i=(0,de.highlightedSymbolsSelector)(t,n.widgetId);return{isCollapsed:(0,dt.separatorIsCollapsedSelector)(t,n.widgetId,n.id),isContainedByMultiSelection:e(t,{widgetId:n.widgetId,symbol:n.id}),isDeletable:s,enableContextMenu:o,isHighlighted:!!i&&i.includes(n.id)}}}),(function(e){return(0,o.bindActionCreators)({renameSymbol:ct,removeSymbol:R.removeSymbolsThunk,addSymbols:R.addSymbolsThunk,showContextMenu:_.showContextMenu,setSeparatorCollapse:ht},e)}))((function(e){const{isCollapsed:t,widgetId:s,id:o,isSelected:r,removeSymbol:a,renameSymbol:u,enableContextMenu:h,isDeletable:m,isContainedByMultiSelection:p,isFirstListItem:f,showContextMenu:g,addSymbols:v,isHighlighted:b,setSeparatorCollapse:_,className:y}=e,{size:w}=(0,i.useContext)(S.SizeContext),[C,E]=(0,i.useState)(!1),[T,I]=(0,i.useState)(!1),M=!1,N=m&&!0,L=(0,k.separatorValToDisplayVal)(o);return i.createElement("div",{className:c()(at.separator,1===w&&at.small,f&&at.firstItem,r&&at.selected,b&&at.highlighted,x.mobiletouch&&at.touch,y),onClick:function(e){if(I(!1),T||e.isDefaultPrevented())return;if([d.Modifiers.Mod,d.Modifiers.Shift].includes((0,d.modifiersFromEvent)(e)))return;if(1===w&&!C)return void function(e){e.persist(),D(e)}(e);r&&m&&E(!0)},onMouseDown:function(){C&&I(!0)},onKeyDown:function(e){if(!C)return;(0,d.hashFromEvent)(e)!==d.Modifiers.Mod+65&&46!==(0,d.hashFromEvent)(e)&&8!==(0,d.hashFromEvent)(e)&&40!==(0,d.hashFromEvent)(e)&&38!==(0,d.hashFromEvent)(e)||e.stopPropagation()},onContextMenu:0===w&&h?function(e){if(e.preventDefault(),e.persist(),C)return;if(p)return void g(s,o,e,0);D(e)}:void 0,"data-collapsed":void 0},i.createElement("div",{className:c()(at.innerWrapper,M,C&&at.renaming)},i.createElement(et.Renamable,{inputClassName:at.renamableInput,name:L,isRenaming:C,onSubmit:function(e){A();const t=e.trim().toUpperCase();if(0===t.length)return;const n=(0,k.convertToSeparatorName)(t);u(s,o,n)},onClose:A,size:"xxsmall"},i.createElement(i.Fragment,null,M,i.createElement("span",{className:at.label},L),N&&i.createElement(B,{className:at.removeButton,widgetId:s,symbolName:o})))));function A(){E(!1)}function D(e){if(!m)return;const t=[new tt.Action({actionId:"Watchlist.RenameSection",options:{label:l.t(null,void 0,n(606321)),icon:it,onExecute:()=>{E(!0)}}})];N&&t.push(new tt.Action({actionId:"Watchlist.RemoveSection",options:{label:l.t(null,void 0,n(63706)),icon:rt,onExecute:()=>{a(s,[o])}}})),
nt.ContextMenuManager.showMenu([...t,new tt.Separator,new tt.Action({actionId:"Watchlist.AddSymbolToSection",options:{label:l.t(null,void 0,n(218542)),icon:lt,onExecute:()=>{(0,st.showWatchlistSearchDialog)({fullscreen:1===w,targetSymbol:o,onSearchComplete:e=>{(0,ot.runOrSigninWithFeature)((()=>{const t=e.map((e=>e.symbol));v(s,t,o)}),{feature:"watchList",source:"add symbol to watchlist"})}})}}})],e,{mode:1===w?"drawer":void 0,returnFocus:!0,takeFocus:!0})}}));var pt=n(329452);const ft=32;var gt=n(822960),vt=n(29754);const bt={32:"next",[32+d.Modifiers.Shift]:"previous"},_t=x.mobiletouch?"touch":"native",St=["flag","short_name","last_price","change","change_percent","volume","rchp"];function yt(e){return Array.from(e).sort(((e,t)=>St.indexOf(e)-St.indexOf(t)))}function wt(e,t,n,s){let o=16,i=ft;const r=yt(t);return r.forEach(((t,l)=>{const a=Math.max(ft,n-o-ft*(r.length-l-1));t===e&&(i=a);const d=s.get(t);if(d){const e=(0,gt.clamp)(d*n,ft,a);o+=e}})),xt(i)}function xt(e){return Math.round(10*e)/10}function Ct(e){const{dropType:t,boundBox:n}=e,{top:s,bottom:o,left:i}=(0,r.ensureDefined)(n);return[i,"before"===t||"inside"===t?s+.5:o-.5]}function Et(e){return i.createElement("div",{className:vt.empty},i.createElement("div",{className:vt.centered},e.children))}function kt(e){return(0,k.isValidSeparatorItem)(e)}function Tt(e,t){const{listId:n}=e.widgets[t];return null!==n?"custom":null}const It=(0,s.connect)((function(e,t){const{isMovable:n,isTableView:s}=(0,de.widgetOptionsSelector)(e,t.widgetId),o=(0,de.getCurrentViewableListByWidgetId)(e,t.widgetId);return{columns:(0,de.columnsSelector)(e,t.widgetId),isMovable:n,isTableView:s,isLoading:(0,de.isLoadingSelector)(e,t.widgetId),selectedSymbols:(0,de.selectedSymbolsSelector)(e,t.widgetId),scrollToId:(0,de.scrollToIdSelector)(e,t.widgetId),listType:Tt(e,t.widgetId),isHotlist:Boolean("hot"===o?.type)}}),(function(e){return(0,o.bindActionCreators)({updateWidget:_.updateWidget,selectNextAvailableSymbol:_.selectNextAvailableSymbol},e)}))((function(e){const{columns:t,displaySymbols:n,allSymbols:s=n,icon:o,isLoading:l,noSymbolsPlaceholder:c,selectedSymbols:f,updateWidget:g,selectNextAvailableSymbol:_,onKeyDown:w,isMovable:x,widgetId:E,scrollToId:T,listType:I,isHotlist:M,viewState:N,isTableView:L,...A}=e,D=(0,i.useMemo)((()=>function(e){const t=[{id:a.ROOT_ID,children:e,level:0}];for(const n of e)t.push({id:n,level:1,children:[],parentId:a.ROOT_ID,isSticky:kt(n)});return t}(n)),[n]),P=(0,i.useContext)(p.CloseDelegateContext),{size:R}=(0,i.useContext)(S.SizeContext),W=1===R,O=(0,i.useMemo)((()=>!0),[W,L]),B=(0,i.useRef)(performance.now()),F=(0,i.useRef)(null),V=(0,i.useCallback)((e=>(0,k.isValidSeparatorItem)(e.id)?i.createElement(mt,{...e,className:e.isStuckNode&&vt.separator,widgetId:E}):i.createElement(Ie,{...e,widgetId:E,typeView:O?"table":"tile"})),[E,O]),H=(0,i.useCallback)((e=>i.createElement(v,{...e,widgetId:E,id:(0,k.isValidSeparatorItem)(e.id)?(0,k.separatorValToDisplayVal)(e.id):e.id,className:u((0,k.isValidSeparatorItem)(e.id)&&vt.separatorDragPreview)})),[E]),U=(0,
i.useCallback)(((e,t)=>{switch(t.type){case"node":return(0,k.isValidSeparatorItem)(t.node.props.id)?36:O?30:42;case"separator":return 13}}),[O]),z=(0,i.useCallback)(((e,t)=>{0!==e.button||0!==(0,d.modifiersFromEvent)(e)||(0,k.isValidSeparatorItem)(t)||(1===e.nativeEvent.detail&&C.linking.setSymbolAndLogInitiator(t,M?"hotlist":"watchlist"),2===e.nativeEvent.detail&&C.linking.getChartWidget()?.chartWidgetCollection().setSymbolAll(t))}),[]),j=(0,i.useCallback)((e=>{(0,k.isValidSeparatorItem)(e)||C.linking.setSymbolAndLogInitiator(e,M?"hotlist":"watchlist")}),[]),G=(0,i.useCallback)(((e,t)=>t&&1===e.length&&(0,k.isValidSeparatorItem)(e[0])?_(E,e[0],t):g(E,{selectedSymbols:e})),[E,_,g]),[Q,q]=(0,i.useState)(void 0),[J,$]=(0,i.useState)((function(){return N?.getColumnsPercentages()??new Map}));(0,i.useEffect)((()=>{const e=y.quoteSessionAdapters.get(E);return e.addToSubscriptionSet(s),e.commitSubscriptionChanges(),C.linking.proSymbol.subscribe(se),()=>{e.addToCancelSubscriptionSet(s),C.linking.proSymbol.unsubscribe(se)}}),[s]),(0,i.useEffect)((()=>()=>{const e=y.quoteSessionAdapters.get(E);e.clearSubscriptionSet(),e.commitSubscriptionChanges()}),[]);const[K,X]=(0,i.useState)((()=>({height:0,width:0}))),Y=(0,m.useResizeObserver)((e=>{const[t]=e,{width:n,height:s}=t.contentRect;X((e=>e.width===n&&e.height===s?e:{height:s,width:n}))})),Z=(0,i.useRef)(null),ee=(0,i.useMemo)((()=>Boolean(N?.canResizeColumns()&&O)?{requestColumnInitWidthDelegate:new pt.Delegate,setColumnInitWidthDelegate:new pt.Delegate,columnResizeStartDelegate:new pt.Delegate,columnResizeDelegate:new pt.Delegate,columnResizeStopDelegate:new pt.Delegate}:{}),[N,O]),te=function(e,t,n,s){const o={},i=yt(e);!s&&t<=200&&(o["--tv-symbol-list-column-change-display"]="none");return Array.from(n.entries()).forEach((([s,r])=>{if(!i.includes(s))return;if(i.indexOf(s)===i.length-1)return void("change_percent"!==s&&"volume"!==s||(o[`--tv-symbol-list-column-${s}-flex`]="1 1"));const l=wt(s,e,t,n);o[`--tv-symbol-list-column-${s}-flex`]=`0 0 ${(0,gt.clamp)(xt(r*t),ft,l)}px`})),o}(t,K.width,J,O),ne=(0,i.useCallback)(((e,t)=>{const{requestColumnInitWidthDelegate:n,setColumnInitWidthDelegate:s}=ee;let o=!1;return t.forEach((t=>{if(!e.has(t)){o=!0;const i=(t,n)=>e.set(t,n/K.width);s?.subscribe(null,i,!0),n?.fire(t),s?.unsubscribeAll(null)}})),Array.from(e.keys()).forEach((n=>{-1===t.indexOf(n)&&(o=!0,e.delete(n))})),o}),[K,ee]);return(0,i.useEffect)((()=>{if(J.size>0&&N?.canResizeColumns()&&O){const e=new Map(J);ne(e,t)&&($(e),N.saveColumnsPercentages?.(e))}}),[t,J,ne,N,O]),(0,i.useEffect)((()=>{const{columnResizeDelegate:e}=ee,n=(e,n)=>{$((s=>{const o=new Map(s);ne(o,t);const i=n=>(0,gt.clamp)(n,ft,wt(e,t,K.width,o));null===F.current&&(F.current=i((0,r.ensureDefined)(o.get(e))*K.width));const l=i(F.current-n);return o.set(e,l/K.width),o}))};return e?.subscribe(null,n),()=>e?.unsubscribe(null,n)}),[t,K,ee,ne]),(0,i.useEffect)((()=>{const{columnResizeStopDelegate:e}=ee,t=()=>{F.current=null,N?.saveColumnsPercentages?.(J)};return e?.subscribe(null,t),()=>e?.unsubscribe(null,t)
}),[J,ee,N]),i.createElement(Be.Provider,{value:ee},i.createElement("div",{className:vt.wrap,style:te,onContextMenu:Ze.preventDefaultForContextMenu},i.createElement(Ye,{widgetId:E}),i.createElement("div",{ref:Y,className:vt.content},i.createElement("div",{className:u(vt.scrollable,0===n.length&&vt.noData),onMouseDown:function(e){if(!(e.target instanceof Element)||null===Z.current)return;const{activeElement:t}=document;e.target===Z.current&&(Z.current.contains(t)&&e.preventDefault(),ie())},onKeyDown:function(e){27===(0,d.hashFromEvent)(e)&&(e.preventDefault(),ie());if(1===f.length&&Q?.id!==f[0]&&Q?.id===C.linking.proSymbol.value()){if(40===(0,d.hashFromEvent)(e))return void C.linking.setSymbolAndLogInitiator(f[0],M?"hotlist":"watchlist");if(38===(0,d.hashFromEvent)(e))return void _(E,f[0],"previous")}w&&w(e)},"data-name":"symbol-list-wrap",onScrollCapture:function(){if(B.current+500>performance.now())return;P.fire()}},!l&&n.length>0&&0!==K.width&&i.createElement(b.Tree,{...A,key:String(I),height:K.height,width:K.width,navigationKeys:bt,nodeRenderer:V,drag:_t,dragPreviewRenderer:H,nodes:D,onClick:W?void 0:z,selectedIds:f,onSelect:G,readOnly:!x,rowHeight:U,scrollToId:T,onKeyboardSelect:j,dropLayerTransform:Ct,lastFocusedNodeObject:Q,outerRef:function(e){Z.current=e},lastSyncTimestampRef:B}),l&&i.createElement(h.Spinner,null),!l&&c&&0===n.length&&i.createElement(Et,null,i.createElement("div",null,o&&i.createElement(oe.Icon,{icon:o,className:vt.emptyIcon}),i.createElement("div",null,c)))))));function se(e){const t=(0,k.getSymbolFromList)(e,n);t&&!(0,k.isValidSeparatorItem)(t)&&(G([t]),q({id:t}))}function ie(){se(C.linking.proSymbol.value())}}));var Mt,Nt=n(918989),Lt=n(842530);function At(e,t){return Boolean(t[e])}function Dt(e){return new Map(e.map(((e,t)=>[e,t])))}function Pt(e,t){return new Map(e.map(((n,s)=>{if(!(0,k.isValidSeparatorItem)(n))return 3;if(At(n,t))return 2;const o=s<e.length-1&&(0,k.isValidSeparatorItem)(e[s+1]),i=s===e.length-1;return o||i?0:1})).map(((t,n)=>[e[n],t])))}!function(e){e[e.EmptySeparator=0]="EmptySeparator",e[e.OpenSeparator=1]="OpenSeparator",e[e.CollapsedSeparator=2]="CollapsedSeparator",e[e.Symbol=3]="Symbol"}(Mt||(Mt={}));const Rt=(0,s.connect)((function(){const e=[],t=(t,n)=>{const s=(0,de.getCurrentViewableListByWidgetId)(t,n);return null===s?e:s.symbols},n=(e,n)=>t(e,n);return(e,s)=>({symbols:t(e,s.widgetId),displaySymbols:n(e,s.widgetId),collapsedSeparatorsState:(s.widgetId,{}),current:(0,de.getCurrentViewableListByWidgetId)(e,s.widgetId)})}),(function(e){return(0,o.bindActionCreators)({reorderSymbols:R.reorderSymbolsThunk,removeSelectedSymbols:R.removeSelectedSymbolsThunk,markSymbol:R.markSymbolsThunk,selectAllSymbols:_.selectAllSymbols},e)}))((function(e){const{symbols:t,displaySymbols:s,collapsedSeparatorsState:o,reorderSymbols:u,widgetId:c,removeSelectedSymbols:h,markSymbol:m,selectAllSymbols:p,viewState:f,current:g,accessibleScope:v}=e,[b,_]=(0,i.useState)([]),S=(0,i.useRef)({displaySymbols:s,collapsedSeparatorsState:o,indexMap:Dt(s),itemTypeMap:Pt(s,o)});(0,
i.useEffect)((()=>()=>{0}),[]),(0,i.useEffect)((()=>{S.current={displaySymbols:s,collapsedSeparatorsState:o,indexMap:Dt(s),itemTypeMap:Pt(s,o)}}),[s,o]);const y=Boolean(!1);return i.createElement("div",{className:Lt.watchlist},null,i.createElement("div",{className:Lt.list},i.createElement(It,{widgetId:c,onMove:function(e){u(c,e[a.ROOT_ID].children)},onDrop:function(e){const{detail:{nodes:t}}=e;t.length>1?(0,Ne.trackEvent)("Watchlist","Multi select","Drag"):(0,Ne.trackEvent)("Watchlist","Drag")},displaySymbols:t,allSymbols:t,onKeyDown:function(e){const t=(0,d.hashFromEvent)(e);46!==t&&8!==t||(e.preventDefault(),h(c));t===d.Modifiers.Alt+13&&(e.preventDefault(),(0,ot.runOrSigninWithFeature)((()=>m(c,void 0,void 0,!0)),{feature:"flaggedSymbolsColors",source:"Set symbol color"}),(0,Ne.trackEvent)("Watchlist","Alt+Enter"));t===d.Modifiers.Mod+65&&(e.preventDefault(),p(c),(0,Ne.trackEvent)("Watchlist","Ctrl+A"));27===t&&(0,Ne.trackEvent)("Watchlist","Esc")},noSymbolsPlaceholder:y?l.t(null,void 0,n(578799)):l.t(null,void 0,n(109415)),icon:Nt,viewState:f,canMove:function(e,t,n){return!0;const{displaySymbols:s,collapsedSeparatorsState:o,indexMap:i,itemTypeMap:l}=S.current;if("inside"===n)return!1;const[,a,d,u]=[0,1,2,3].map((t=>e.some((e=>l.get(e.id)===t))));if(d&&(a||u))return!1;if(d&&!a&&!u){const e=t.id;if(At(e,o))return!0;const l=(0,r.ensureDefined)(i.get(e));if(l===s.length-1&&"after"===n)return!0;return!(!(l<s.length-1&&(0,k.isValidSeparatorItem)(s[l+1]))||"after"!==n)||(0,k.isValidSeparatorItem)(e)&&"before"===n}return!0},accessibleScope:v})))}))},61844:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Header:()=>ye});var s=n(406047),o=n(743766),i=n(50959),r=n(497754),l=n.n(r),a=n(650151),d=n(609838),u=n(706474),c=n(192063),h=n(46305),m=n(153055);function p(e,t,n){(0,i.useRef)(null);(0,i.useEffect)((()=>()=>{0}),[e.current,t,n])}var f=n(972535),g=n(32133),v=n(535842),b=n(493173),_=n(72621),S=n(920709),y=n(324295),w=n(629319),x=n(518174);const C=(0,b.mergeThemes)(c.DEFAULT_POPUP_MENU_ITEM_THEME,w),E=(0,b.mergeThemes)(c.DEFAULT_POPUP_MENU_ITEM_THEME,x);var k=n(607898);const T=(0,o.connect)(null,{removeWatchList:k.removeSymbolListThunk,selectList:k.selectSymbolListThunk})((function(e){const{className:t,watchList:s,selectList:o,removeWatchList:r,hideRemoveButton:c,isActive:b,isSmallSize:w,onSelect:x}=e,{size:k}=(0,i.useContext)(v.SizeContext),{color:T=null}=s,I=s.symbols.length-s.symbols.filter(u.isValidSeparatorItem).length,M=(0,i.useRef)(null),N=!c&&null===T&&!b;p(M,T,y.flag);let L=s.name;return L||(L=d.t(null,void 0,n(512504))),i.createElement(h.AccessibleMenuItem,{key:s.id,theme:w?E:C,className:l()(t,y.item,b&&y.active,1===k&&y.small,!N&&y.countVisible),isActive:b,label:i.createElement("div",null,i.createElement("span",{className:y.title,ref:M},i.createElement(S.LeadingEmojiText,{text:L})),w&&i.createElement("span",{className:y.count},d.t(null,{plural:"{count} symbols",count:I},n(170352)))),toolbox:i.createElement(i.Fragment,null,N&&i.createElement(_.RemoveButton,{
className:l()(y.removeButton,y.toolboxItem,f.touch&&y.touch,w&&y.small),isActive:b,onClick:function(){const e=s.name||getColoredListTitle((0,a.ensureDefined)(s.color)),t=d.t(null,void 0,n(930058)).format({name:e});(0,m.showConfirm)({text:t,onConfirm:async({dialogClose:e})=>{await r(s.id),e()}}),(0,g.trackEvent)("Watchlist","Popup menu","Remove list")}}),i.createElement("span",{className:l()(y.toolboxCount,y.toolboxItem,(w||f.touch)&&y.touch)},I)),onClick:function(){if(!b){if("custom"!==s.type)return;o(null,{id:s.id,type:"custom"}),x?.(),(0,g.trackEvent)("Watchlist","Popup menu","Change active list")}}})}));var I=n(917850),M=n(230553),N=n(531600),L=n(354364);function A(e){const{isLoading:t,list:n,current:s,isSmallSize:o,isReadonly:r,onSelect:l}=e,a=(0,i.useContext)(M.MenuContext);(0,i.useEffect)((()=>{a&&a.update()}),[n]);const d=[],u=[];for(const e of n)d.push(e);d.sort(L.sortComparator);const c=[[],d];return u.length&&c.push(u),i.createElement(i.Fragment,null,t&&i.createElement(N.ToolWidgetMenuSpinner,null),!t&&c.map(((e,t,n)=>e.length>0&&i.createElement(i.Fragment,{key:t},e.map((e=>i.createElement(T,{key:e.id,watchList:e,isActive:null!==s&&s.id===e.id,hideRemoveButton:1===d.length||r||!1,isSmallSize:o,onSelect:l}))),t!==n.length-1&&0!==e.length&&i.createElement(I.PopupMenuSeparator,null)))))}var D=n(679458),P=n(975598),R=n(899316);const W=(0,b.mergeThemes)(D.DEFAULT_TOOL_WIDGET_MENU_THEME,{button:R.button,hover:R.hover,isOpened:R.isOpened});function O(e){const{className:t,content:n,...s}=e;return i.createElement(P.AccessibleMenuButton,{...s,theme:W,className:l()(t),content:i.createElement("span",{className:l()(R.inner)},n)})}var B=n(398171),F=n(28466),V=n(117601),H=n(601227),U=(n(577512),n(440891)),z=n(190410),j=n(669874),G=n(565631),Q=n(856105),q=n(190266);const J=n(725479);function $(e){const{leftSlot:t,rightSlot:n,withRightSlotSeparator:s,theme:o=J}=e;return i.createElement("div",{className:o.container},i.createElement("div",{className:o.leftSlot},t),i.createElement("div",{className:r(o.rightSlot,s&&o.rightSlotSeparator)},n))}var K=n(688401),X=n(82498);var Y,Z=n(878112),ee=n(845437),te=n(636296),ne=n(748040),se=n(323595),oe=n(808757),ie=n(133736),re=n(776126),le=n(46857),ae=n(833366),de=n(388140),ue=n(611443),ce=n.n(ue),he=n(724669);!function(e){e.Create="create",e.Share="share",e.Copy="copy",e.Rename="rename",e.Clear="clear",e.AddSection="add-section",e.Import="import",e.Export="export",e.Separator="separator",e.OpenList="open-list",e.Watchlists="watchlists",e.Tableview="tableview",e.Recents="recents",e.CreateAlert="create-alert"}(Y||(Y={}));const me=(0,b.mergeThemes)(J,{leftSlot:he.widgetbarWidgetHeaderLeftSlot,rightSlot:he.widgetbarWidgetHeaderRightSlot}),pe=U.enabled("multiple_watchlists"),fe=void 0,ge=(0,b.mergeThemes)(c.DEFAULT_POPUP_MENU_ITEM_THEME,re),ve=(0,b.mergeThemes)(c.DEFAULT_POPUP_MENU_ITEM_THEME,le);var be=n(219769),_e=n(254931),Se=n(363940);const ye=(0,o.connect)((function(){const e=(e,t)=>{const n=(0,_e.getCurrentViewableListByWidgetId)(e,t);return null===n?null:"custom"===n.type?{type:n.type,
id:n.id,name:n.name,description:n.description,symbols:n.symbols,shared:n.shared,persistedState:n.persistedState}:"hot"===n.type?{type:n.type,id:n.id,name:n.name,description:null,symbols:n.symbols,shared:!1,persistedState:null,exchange:n.exchange,group:n.group}:null},t=e=>{const t=[];return t.push(...(0,_e.getCustomLists)(e)),t};return(n,s)=>{const{enableAddSymbolsByAnon:o,shouldDisplaySymbolSearch:i,isDeletable:r,isLogoEnabled:l,isTableView:a}=(0,_e.widgetOptionsSelector)(n,s.widgetId);return{isLoading:!(0,_e.getIsReadyCustomLists)(n),enableAddSymbolsByAnon:o,shouldDisplaySymbolSearch:i,isLogoEnabled:l,isTableView:a,list:t(n),current:e(n,s.widgetId),columns:(0,_e.columnsSelector)(n,s.widgetId),tickerType:(0,_e.tickerTypeSelector)(n,s.widgetId),isReadonly:!r}}}),(function(e){const t={createNewWatchList:k.userCreateWatchlistThunk,saveListAs:k.saveListAsThunk,renameWatchList:k.renameSymbolListThunk,clearWatchList:k.clearSymbolListThunk,getWatchLists:()=>(0,Se.getCustomWatchlistsThunk)(null),addSymbols:k.addSymbolsThunk,insertSymbolBefore:k.insertSymbolBeforeThunk,scrollToSymbol:k.findInWatchlistThunk,updateWidget:be.updateWidget,updateWidgetOptions:be.updateWidgetOptions};return(0,s.bindActionCreators)(t,e)}))((function(e){const{isLoading:t,list:s,current:o,getWatchLists:l,createNewWatchList:c,saveListAs:f,enableAddSymbolsByAnon:b,shouldDisplaySymbolSearch:_,renameWatchList:y,clearWatchList:w,addSymbols:x,insertSymbolBefore:C,widgetId:E,isReadonly:k,scrollToSymbol:T,shareList:M,isLogoEnabled:N,isTableView:D}=e,{size:P}=(0,i.useContext)(v.SizeContext),R=(0,i.useContext)(F.CloseDelegateContext),{setHideClose:W}=(0,i.useContext)(z.DialogHeaderContext),J=(0,i.useRef)(null),[Y,re]=(0,i.useState)(!1),[le,ue]=(0,i.useState)([]),[be,_e]=(0,i.useState)([]),Se=(0,i.useRef)([]),ye=(0,i.useRef)(void 0),we=(0,i.useRef)(null);we.current=function(){const e=Se.current;Se.current=[],ye.current=void 0,!1;b?je(e):(0,q.runOrSignInWithPromo)("watchList",{source:"add symbol to watchlist"},(()=>je(e)))};const[xe,Ce]=(0,i.useState)(!1);p(J,o?.color??null,he.flag);const Ee=!1,ke=function(e,t,n){return function(){async function t(){try{const t=await(0,X.importSymbolsFromFile)();if(!t)return;e(null,t),n?.()}catch(e){return}}U.enabled("watchlist_import_export")&&t()}}(c),Te=function(){const e=d.t(null,void 0,n(512504));if(!o)return e;0;return o.name||e}(),Ie=1===P,Me=Ie?ve:ge,Ne=(0,i.useMemo)((()=>{if(!le.length)return le;const e=!!le.find((e=>"hot"!==e.type));return s.length&&e?le.filter((e=>"hot"===e.type||("color"===e.type?s.find((t=>t.id===e.id&&(favoritesService().isFavorite(e)||o?.id===e.id||t.color===ListColor.Red||t.symbols.length>0))):Boolean(s.find((t=>t.id===e.id&&t.type===e.type)))))):le}),[s,le,xe,o]);(0,i.useRef)(null);const Le=(0,i.useRef)(null),Ae=(0,i.useRef)(null);function De(){}return i.createElement("div",{className:r(he.container,Ie&&he.mobile),onClick:function(){xe||Ie||R.fire()}},i.createElement(V.Renamable,{name:Te,isRenaming:Y,onSubmit:function(e){Be(!1),null!==o&&y(o.id,e)},onClose:function(){Be(!1)},maxLength:128,
emojiPicker:!0},i.createElement($,{leftSlot:(0,H.onWidget)()?i.createElement("div",{className:he.title},d.t(null,void 0,n(512504)),Ee):i.createElement(i.Fragment,null,i.createElement(O,{className:r(Ie&&he.mobileBtn),onOpen:function(){l();Je()},onClose:$e,menuClassName:he.watchlistMenu,content:i.createElement("div",{className:he.headerMenuContent},i.createElement(i.Fragment,null,i.createElement("span",{ref:J}),i.createElement("span",{className:r(he.titleRow)},i.createElement(S.LeadingEmojiText,{text:Te})),Ee,k?i.createElement(Z.Icon,{icon:ce(),className:he.lockIcon}):null)),isDrawer:Ie,"data-name":"watchlists-button",menuDataName:"active-watchlist-menu"},qe((function(){let e;e=k?["watchlists"]:["create","copy","rename",U.enabled("watchlist_import_export")?"import":null,U.enabled("watchlist_import_export")?"export":null,"clear",U.enabled("watchlist_sections")?"add-section":null,"separator","watchlists"];return e.filter((e=>null!==e))})))),rightSlot:_&&i.createElement("div",{className:he.buttonWrap},k?null:i.createElement(G.AccessibleIconButton,{tooltip:d.t(null,void 0,n(218542)),className:he.headerButton,icon:ae,onClick:function(){(0,Q.showWatchlistSearchDialog)({fullscreen:Ie,onSearchComplete:e=>{(0,q.runOrSignInWithPromo)("watchList",{source:"add symbol to watchlist"},(()=>{for(const t of e)ze(t.symbol)}))}}),(0,g.trackEvent)("GUI","SS","watchlist")},"data-name":"add-symbol-button","data-role":"button",isDisabled:!1}),!1,false),withRightSlotSeparator:Ie,theme:me})));function Pe(){function e(){const{name:e,color:t,symbols:n}=(0,a.ensureNotNull)(o),s=e||t||"watchlist";(0,L.exportList)(s,n)}U.enabled("watchlist_import_export")&&e()}function Re(){0}function We(){null!==o&&((0,m.showConfirm)({text:d.t(null,void 0,n(624439)),onConfirm:({dialogClose:e})=>{w(o.id),e()}}),Ge("Clear list"))}function Oe(){o&&(0,q.runOrSignIn)((()=>{const e=K.linking.symbol.value(),t=Boolean(o.symbols.find((t=>t===e))),n=(0,u.buildUniqueName)(o.symbols.filter(B.isSeparatorItem),(0,u.convertToSeparatorName)("SECTION"));t?(C(E,e,n),T(E,n)):ze(n),Ie&&R.fire()}),{source:"Add new section"})}function Be(e){re(e),W(e)}function Fe(){(()=>{Be(!0)})(),Ge("Rename")}function Ve(){Ue((()=>f(e.widgetId,Promise.resolve((0,a.ensureNotNull)(o).symbols))),"saveAs"),Ge("Make a copy")}function He(){R.fire(),Ue((()=>c(null)),"new"),Ge("Create new list")}function Ue(e,t){e()}function ze(e){Se.current.push(e),ye.current||(ye.current=setTimeout((()=>{null!==we.current&&we.current()}),0))}function je(e){x(E,Array.from(new Set(e)))}function Ge(e){(0,g.trackEvent)("Watchlist","Popup menu",e)}function Qe(){}function qe(e){const l=e();return l.length?i.createElement(i.Fragment,null,l.map(((e,l)=>function(e,l){if(!o)return null;const a=e+l;switch(e){case"create":return pe?i.createElement(h.AccessibleMenuItem,{key:e,theme:Me,icon:ee,label:(0,j.appendEllipsis)(d.t(null,void 0,n(222556))),onClick:He}):null;case"recents":return i.createElement("div",{key:e},i.createElement(ToolWidgetMenuSummary,{className:r(he.columnsTitle,Ie&&he.small)
},d.t(null,void 0,n(907448))),i.createElement(RecentlyUsedSymbolLists,{recents:Ne,favorites:be,onSelectList:Ie?()=>R.fire():void 0,theme:Me}));case"add-section":return i.createElement(h.AccessibleMenuItem,{key:e,theme:Me,icon:de,label:d.t(null,void 0,n(598104)),onClick:Oe,isDisabled:!1});case"clear":return i.createElement(h.AccessibleMenuItem,{key:e,theme:Me,icon:ie,label:d.t(null,void 0,n(798450)),onClick:We,isDisabled:k});case"import":return pe?i.createElement(h.AccessibleMenuItem,{key:e,theme:Me,icon:se,label:(0,j.appendEllipsis)(d.t(null,void 0,n(795754))),onClick:ke}):null;case"export":return pe?i.createElement(h.AccessibleMenuItem,{key:e,theme:ge,icon:oe,label:d.t(null,void 0,n(352321)),onClick:Pe}):null;case"copy":return pe?i.createElement(h.AccessibleMenuItem,{key:e,theme:Me,icon:te,label:(0,j.appendEllipsis)(d.t(null,void 0,n(340115))),onClick:Ve}):null;case"share":return i.createElement(i.Fragment,{key:e},i.createElement(MenuItemSwitcher,{checked:Boolean(o.shared),label:d.t(null,void 0,n(984239)),value:"share-switcher",onChange:De,theme:Ie?fe:void 0,disabled:!1}),o.shared&&i.createElement(CopyLabel,{onClick:Qe,ref:Le}));case"rename":return i.createElement(h.AccessibleMenuItem,{key:e,theme:Me,icon:ne,label:d.t(null,void 0,n(606321)),onClick:Fe,isDisabled:!1});case"open-list":return i.createElement(h.AccessibleMenuItem,{key:e,theme:Me,icon:folderIcon,label:(0,j.appendEllipsis)(d.t(null,void 0,n(545362))),onClick:Re,shortcut:humanReadableHash(Modifiers.Shift+87)});case"separator":return i.createElement(I.PopupMenuSeparator,{key:a});case"watchlists":return i.createElement(A,{key:e,isLoading:t,list:s,current:o,isSmallSize:Ie,isReadonly:k,onSelect:Ie?()=>R.fire():void 0});case"tableview":return i.createElement(MenuItemSwitcher,{key:e,checked:D,label:d.t(null,void 0,n(770542)),value:"table-view-switcher",onChange:Ke,theme:Ie?fe:void 0});case"create-alert":return null}}(e,l)))):null}function Je(){Ce(!0)}function $e(){Ce(!1)}function Ke(t){e.updateWidgetOptions(E,{isTableView:t}),Ae.current?.update(),Ge(t?"Table view on":"Table view off")}}))},82498:(e,t,n)=>{"use strict";n.d(t,{importSymbolsFromFile:()=>r});var s=n(787382),o=n(440891),i=n(153055);async function r(){if(!o.enabled("watchlist_import_export"))return null;const e=await async function(){const e=document.createElement("input");return e.type="file",e.accept="text/plain",new Promise(((t,n)=>{e.addEventListener("change",(()=>{t(e.files?e.files[0]:null),function(e){if(!e)return;e.value=""}(e)}),{once:!0}),e.addEventListener("cancel",(()=>n())),e.click()}))}();return e?async function(e){return new Promise((t=>{const o=250;if(e.size/1024>o)return(0,i.showWarning)({title:s.t(null,void 0,n(317299)),text:s.t(null,{replace:{fileLimit:String(o)}},n(316750))}),t(null);const r=new FileReader;r.onload=()=>{var o;"string"!=typeof(o=r.result)||/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(o)||/^[\r\n\t,]*$/.test(o)?(0,i.showWarning)({title:s.t(null,void 0,n(108492)),text:s.t(null,void 0,n(372163))}):t({name:e.name.replace(/^(.+)\..+$/,"$1"),
symbols:String(r.result).toUpperCase().split(/[\t\r\n,]+/).filter(Boolean)})},r.readAsText(e)}))}(e):null}},113060:(e,t,n)=>{"use strict";n.d(t,{SymbolSearchWatchlistContext:()=>i});var s,o=n(50959);!function(e){e[e.Add=0]="Add",e[e.Find=1]="Find",e[e.Remove=2]="Remove"}(s||(s={}));const i=o.createContext(null)},190266:(e,t,n)=>{"use strict";n.d(t,{runOrSignIn:()=>s,runOrSignInWithPromo:()=>o});function s(e,t){e()}function o(e,t,n){n()}},306858:(e,t,n)=>{"use strict";n.d(t,{removeUsdFromCryptoPairLogos:()=>r,resolveLogoUrls:()=>i});var s=n(868333);const o=(0,s.getLogoUrlResolver)();function i(e,t=s.LogoSize.Medium){const n=e.logoid,i=e["base-currency-logoid"],r=e["currency-logoid"],l=n&&o.getSymbolLogoUrl(n,t);if(l)return[l];const a=i&&o.getSymbolLogoUrl(i,t),d=r&&o.getSymbolLogoUrl(r,t);return a&&d?[a,d]:a?[a]:d?[d]:[]}function r(e){return 2!==e.length?e:function(e){return e.some((e=>l(e)))}(e)&&!function(e){return e.some((e=>e.includes("country")&&!l(e)))}(e)?e.filter((e=>!l(e))):e}function l(e){return!1}},41192:(e,t,n)=>{"use strict";n.d(t,{getBlockStyleClasses:()=>s.getBlockStyleClasses,getLogoStyleClasses:()=>s.getLogoStyleClasses});var s=n(108937)},361701:(e,t,n)=>{"use strict";n.d(t,{CircleLogo:()=>l,hiddenCircleLogoClass:()=>r});var s=n(50959),o=n(185934),i=n(456057);const r=n.n(i)().hidden;function l(e){const t=(0,o.isCircleLogoWithUrlProps)(e),[n,i]=(0,s.useState)(0),r=(0,s.useRef)(null),l=(0,o.getStyleClasses)(e.size,n,e.className),a=e.alt??e.title??"",d=t?a[0]:e.placeholderLetter;return(0,s.useEffect)((()=>i(r.current?.complete??!t?2:1)),[t]),t&&3!==n?s.createElement("img",{ref:r,className:l,crossOrigin:"",src:e.logoUrl,alt:a,title:e.title,loading:e.loading,onLoad:()=>i(2),onError:()=>i(3),"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):s.createElement("span",{className:l,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},d)}},439067:(e,t,n)=>{"use strict";n.d(t,{getStyleClasses:()=>s.getStyleClasses});var s=n(185934)},205408:(e,t,n)=>{"use strict";n.d(t,{EmojiPicker:()=>M});var s=n(50959),o=n(870122),i=n(499547),r=n(994437),l=n(611005);var a=n(624216),d=n(163694),u=n(759339),c=n(510618),h=n(930202),m=n(493173),p=n(930052);function f(e){!function(e,t){(0,s.useEffect)((()=>{const n=t||document;return n.addEventListener("scroll",e),()=>n.removeEventListener("scroll",e)}),[e])}(e,document)}var g=n(162458),v=n(996038),b=n(497754),_=n.n(b),S=n(510555);function y(e){const{children:t,highlight:n,disabled:o,reference:i,...r}=e,l=n?"primary":"default";return s.createElement("div",{...r,ref:i,className:_()(S.wrapper,S[`intent-${l}`],S["border-thin"],S["size-medium"],n&&S.highlight,n&&S.focused,o&&S.disabled),"data-role":"button"},s.createElement("div",{className:_()(S.childrenContainer,o&&S.disabled)},t),n&&s.createElement("span",{className:S.shadow}))}var w=n(99021),x=n(927061);const C=()=>null,E=(0,m.mergeThemes)(c.DEFAULT_MENU_THEME,{menuBox:x.menuBox}),k=378,T=18,I=200;function M(e){const{value:t,disabled:n,onSelect:r,onClose:c,canBeEmpty:m,renderButton:b=N}=e,_=(0,
s.useRef)(null),{current:S}=(0,s.useRef)((y=t,o.getJSON("RecentlyUsedEmojis",[y]).filter((e=>e!==w.EMPTY_EMOJI))));var y;const M=(0,s.useRef)(null),[L,A]=(0,s.useState)(S),[D,P]=(0,s.useState)(!1),R=(0,s.useCallback)((()=>{P(!1),c?.()}),[c]),W=(0,s.useRef)(0);f((0,s.useCallback)((()=>{Date.now()-W.current<I||R()}),[R]));const O=(0,s.useCallback)((e=>{if(e!==w.EMPTY_EMOJI){const t=Array.from(new Set([e,...L])).slice(0,T);o.setJSON("RecentlyUsedEmojis",t),A(t)}r(e),R()}),[L,r]),B=(0,s.useMemo)((()=>m?[w.EMPTY_EMOJI,...L].slice(0,T):L),[L,m]),F=(V=B,(0,s.useMemo)((()=>{const e=(0,l.emojiGroups)();return e[0].emojis=V,e}),[V]));var V;return s.createElement(s.Fragment,null,s.createElement("div",{ref:_,className:x.buttonWrap},b({emoji:t,isOpened:D,disabled:n,onClick:function(){if(D)return void R();n||(P(!0),W.current=Date.now())}})),s.createElement(p.MatchMedia,{rule:v.DialogBreakpoints.TabletSmall},(e=>D&&s.createElement(d.DrawerManager,null,e?s.createElement(u.Drawer,{className:x.drawer,position:"Bottom",onClose:R},s.createElement(i.EmojiList,{emojis:F,onSelect:O,height:k})):s.createElement(a.PopupMenu,{theme:E,onKeyDown:U,isOpened:!0,position:(0,g.getPopupPositioner)(_.current,{horizontalDropDirection:g.HorizontalDropDirection.FromLeftToRight,horizontalAttachEdge:g.HorizontalAttachEdge.Left}),closeOnClickOutside:!1,onClickOutside:z,onClose:C,controller:M,onOpen:H,tabIndex:-1},s.createElement(i.EmojiList,{className:x.desktopSize,emojis:F,onSelect:O,height:k}))))));function H(){M.current?.focus()}function U(e){27===(0,h.hashFromEvent)(e)&&(e.preventDefault(),e.stopPropagation(),R())}function z(e){const t=e.target;t instanceof Node&&_.current?.contains(t)||R()}}function N(e){const{emoji:t,isOpened:n,disabled:o,onClick:i}=e;return s.createElement(y,{highlight:n,disabled:o,"data-name":"emoji-picker"},s.createElement(r.EmojiWrap,{emoji:t,onClick:i}))}},920709:(e,t,n)=>{"use strict";n.d(t,{LeadingEmojiText:()=>l});var s=n(50959),o=n(574871),i=n(198412),r=n(539453);function l(e){const{text:t,textRender:n,firstSegmentOnly:l=!1}=e,{leadingEmoji:a,processedText:d}=(0,s.useMemo)((()=>(0,o.processTextWithLeadingEmoji)({text:t,textRender:n,firstSegmentOnly:l})),[t,n,l]);return a?s.createElement(s.Fragment,null,s.createElement("span",{className:r.emojiWrap}," ",s.createElement(i.EmojiItem,{className:r.emoji,emoji:a})),""!==d&&s.createElement(s.Fragment,null," ",d)):s.createElement(s.Fragment,null,d)}},574871:(e,t,n)=>{"use strict";n.d(t,{getLeadingEmojiHtml:()=>c,processTextWithLeadingEmoji:()=>u});var s=n(871645),o=n(611005),i=n(99021),r=n(440891),l=n(278765),a=n(539453);const d=r.enabled("advanced_emoji_in_titles");function u(e){const{text:t,textRender:n=e=>e,firstSegmentOnly:r=!1}=e,l=(0,s.getFirstSegmentOrCodePointString)(t),a=null!==l&&(0,o.isSupportedEmoji)(l)?l:i.EMPTY_EMOJI,u=r?l||"":t;if(!d||a===i.EMPTY_EMOJI)return{leadingEmoji:"",processedText:n(u)};return{leadingEmoji:a,processedText:n(u.replace(a,""))}}function c(e){const{processedText:t,leadingEmoji:n}=u({text:e}),o=(0,s.htmlEscape)(t);if(!n)return o
;return`${function(e){const t=(0,l.getTwemojiUrl)(e,"png");return`<span class=${a.tooltipEmojiWrap}>&nbsp<img class=${a.tooltipEmoji} src=${t} decoding="async" width="12" height="12" alt="" draggable="false"/></span>`}(n)}&nbsp;${o}`}},522224:(e,t,n)=>{"use strict";n.d(t,{hoverMouseEventFilter:()=>s.hoverMouseEventFilter,useAccurateHover:()=>s.useAccurateHover,useHover:()=>s.useHover});var s=n(975228)},822312:(e,t,n)=>{"use strict";n.d(t,{InputWithEmojiSelect:()=>x});var s=n(50959),o=n(650151),i=n(930202),r=n(718736),l=n(654936),a=n(440891),d=n(611005),u=n(497754),c=n.n(u),h=n(878112),m=n(898237),p=n(609838),f=n(198412),g=n(205408),v=n(99021),b=n(98993),_=n(394032);function S(e){const{emoji:t,onSelect:o,onClose:i,buttonClassName:r}=e;return s.createElement(g.EmojiPicker,{value:t,onSelect:o,onClose:i,renderButton:e=>function(e,t){const{emoji:o,onClick:i}=e;return s.createElement(m.LightButton,{className:c()(t,b.button,"apply-common-tooltip"),title:p.t(null,void 0,n(904461)),size:"xsmall",color:"gray",variant:"ghost",onClick:i,tabIndex:0,startSlot:o===v.EMPTY_EMOJI?s.createElement(h.Icon,{className:b.emptySelect,icon:_}):s.createElement(f.EmojiItem,{className:b.emoji,emoji:o})})}(e,r),canBeEmpty:!0})}var y=n(307567);const w=a.enabled("advanced_emoji_in_titles");function x(e){const{value:t="",onChange:n,reference:a=null,emojiPicker:u=!1,...c}=e,{emoji:h,emojiLessString:m}=(0,s.useMemo)((()=>(0,d.separateEmoji)(t)),[t]),p=(0,r.useFunctionalRefObject)(a);return w&&u?s.createElement(l.InputControl,{...c,reference:p,value:m,onChange:function(e){n?.(h+e.currentTarget.value)},onKeyDown:function(t){if(e.onKeyDown?.(t),t.defaultPrevented)return;const{selectionStart:s,selectionEnd:r}=(0,o.ensureNotNull)(p.current);0===s&&0===r&&h&&8===(0,i.hashFromEvent)(t)&&(t.preventDefault(),n?.(m))},startSlot:s.createElement(S,{emoji:h,onSelect:function(e){n?.(e+m)},onClose:function(){p.current?.focus()},buttonClassName:y.emojiSelect})}):s.createElement(l.InputControl,{...c,value:t,reference:a,onChange:function(e){n?.(e.currentTarget.value)}})}},930052:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>o});var s=n(50959);class o extends s.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addEventListener("change",this._handleChange)}_unsubscribe(e){e.removeEventListener("change",this._handleChange)}}},230553:(e,t,n)=>{"use strict";n.d(t,{MenuContext:()=>s});const s=n(50959).createContext(null)},510618:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_MENU_THEME:()=>g,Menu:()=>b})
;var s=n(50959),o=n(497754),i=n.n(o),r=n(650151),l=n(822960),a=n(409174),d=n(753327),u=n(370981),c=n(801808),h=n(926032),m=n(823030),p=n(230553),f=n(667797);const g=f;var v;!function(e){e[e.IndentFromWindow=0]="IndentFromWindow"}(v||(v={}));class b extends s.PureComponent{constructor(e){super(e),this._containerRef=null,this._scrollWrapRef=null,this._raf=null,this._scrollRaf=null,this._scrollTimeout=void 0,this._manager=new c.OverlapManager,this._hotkeys=null,this._scroll=0,this._handleContainerRef=e=>{this._containerRef=e,this.props.reference&&("function"==typeof this.props.reference&&this.props.reference(e),"object"==typeof this.props.reference&&(this.props.reference.current=e))},this._handleScrollWrapRef=e=>{this._scrollWrapRef=e,"function"==typeof this.props.scrollWrapReference&&this.props.scrollWrapReference(e),"object"==typeof this.props.scrollWrapReference&&(this.props.scrollWrapReference.current=e)},this._handleCustomRemeasureDelegate=()=>{this._resizeForced(),this._handleMeasure()},this._handleMeasure=({callback:e,forceRecalcPosition:t}={})=>{if(this.state.isMeasureValid&&!t)return;const{position:n}=this.props,s=(0,r.ensureNotNull)(this._containerRef);let o=s.getBoundingClientRect();const i=document.documentElement.clientHeight,a=document.documentElement.clientWidth,d=this.props.closeOnScrollOutsideOffset??0;let u=i-0-d;const c=o.height>u;if(c){(0,r.ensureNotNull)(this._scrollWrapRef).style.overflowY="scroll",o=s.getBoundingClientRect()}const{width:h,height:m}=o,p="function"==typeof n?n({contentWidth:h,contentHeight:m,availableWidth:a,availableHeight:i}):n,f=p?.indentFromWindow?.left??0,g=a-(p.overrideWidth??h)-(p?.indentFromWindow?.right??0),v=(0,l.clamp)(p.x,f,Math.max(f,g)),b=(p?.indentFromWindow?.top??0)+d,_=i-(p.overrideHeight??m)-(p?.indentFromWindow?.bottom??0);let S=(0,l.clamp)(p.y,b,Math.max(b,_));if(p.forbidCorrectYCoord&&S<p.y&&(u-=p.y-S,S=p.y),t&&void 0!==this.props.closeOnScrollOutsideOffset&&p.y<=this.props.closeOnScrollOutsideOffset)return void this._handleGlobalClose(!0);const y=p.overrideHeight??(c?u:void 0);this.setState({appearingMenuHeight:t?this.state.appearingMenuHeight:y,appearingMenuWidth:t?this.state.appearingMenuWidth:p.overrideWidth,appearingPosition:{x:v,y:S},isMeasureValid:!0},(()=>{this.props.doNotRestorePosition||this._restoreScrollPosition(),e&&e()}))},this._restoreScrollPosition=()=>{const e=document.activeElement,t=(0,r.ensureNotNull)(this._containerRef);if(null!==e&&t.contains(e))try{e.scrollIntoView()}catch(e){}else(0,r.ensureNotNull)(this._scrollWrapRef).scrollTop=this._scroll},this._resizeForced=()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0})},this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null})))},this._handleGlobalClose=e=>{this.props.onClose(e)},this._handleSlot=e=>{this._manager.setContainer(e)},this._handleScroll=()=>{this._scroll=(0,
r.ensureNotNull)(this._scrollWrapRef).scrollTop},this._handleScrollOutsideEnd=()=>{clearTimeout(this._scrollTimeout),this._scrollTimeout=setTimeout((()=>{this._handleMeasure({forceRecalcPosition:!0})}),80)},this._handleScrollOutside=e=>{e.target!==this._scrollWrapRef&&(this._handleScrollOutsideEnd(),null===this._scrollRaf&&(this._scrollRaf=requestAnimationFrame((()=>{this._handleMeasure({forceRecalcPosition:!0}),this._scrollRaf=null}))))},this.state={}}componentDidMount(){this._handleMeasure({callback:this.props.onOpen});const{customCloseDelegate:e=u.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.subscribe(this,this._handleGlobalClose),t?.subscribe(null,this._handleCustomRemeasureDelegate),window.addEventListener("resize",this._resize);const n=null!==this.context;this._hotkeys||n||(this._hotkeys=h.createGroup({desc:"Popup menu"}),this._hotkeys.add({desc:"Close",hotkey:27,handler:()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleGlobalClose()}})),this.props.repositionOnScroll&&window.addEventListener("scroll",this._handleScrollOutside,{capture:!0})}componentDidUpdate(){this._handleMeasure()}componentWillUnmount(){const{customCloseDelegate:e=u.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.unsubscribe(this,this._handleGlobalClose),t?.unsubscribe(null,this._handleCustomRemeasureDelegate),window.removeEventListener("resize",this._resize),window.removeEventListener("scroll",this._handleScrollOutside,{capture:!0}),this._hotkeys&&(this._hotkeys.destroy(),this._hotkeys=null),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),null!==this._scrollRaf&&(cancelAnimationFrame(this._scrollRaf),this._scrollRaf=null),this._scrollTimeout&&clearTimeout(this._scrollTimeout)}render(){const{id:e,role:t,"aria-label":n,"aria-labelledby":o,"aria-activedescendant":r,"aria-hidden":l,"aria-describedby":u,"aria-invalid":c,children:h,minWidth:g,theme:v=f,className:b,maxHeight:S,onMouseOver:y,onMouseOut:w,onKeyDown:x,onFocus:C,onBlur:E}=this.props,{appearingMenuHeight:k,appearingMenuWidth:T,appearingPosition:I,isMeasureValid:M}=this.state,N={"--ui-kit-menu-max-width":`${I&&I.x}px`,maxWidth:"calc(100vw - var(--ui-kit-menu-max-width) - 6px)"};return s.createElement(p.MenuContext.Provider,{value:this},s.createElement(m.SubmenuHandler,null,s.createElement(d.SlotContext.Provider,{value:this._manager},s.createElement("div",{id:e,role:t,"aria-label":n,"aria-labelledby":o,"aria-activedescendant":r,"aria-hidden":l,"aria-describedby":u,"aria-invalid":c,className:i()(b,v.menuWrap,!M&&v.isMeasuring),style:{height:k,left:I&&I.x,minWidth:g,position:"fixed",top:I&&I.y,width:T,...this.props.limitMaxWidth&&N},"data-name":this.props["data-name"],"data-tooltip-show-on-focus":this.props["data-tooltip-show-on-focus"],ref:this._handleContainerRef,onScrollCapture:this.props.onScroll,onContextMenu:a.preventDefaultForContextMenu,tabIndex:this.props.tabIndex,onMouseOver:y,onMouseOut:w,onKeyDown:x,onFocus:C,onBlur:E},s.createElement("div",{
className:i()(v.scrollWrap,!this.props.noMomentumBasedScroll&&v.momentumBased),style:{overflowY:void 0!==k?"scroll":"auto",maxHeight:S},onScrollCapture:this._handleScroll,ref:this._handleScrollWrapRef},s.createElement(_,{className:v.menuBox},h)))),s.createElement(d.Slot,{reference:this._handleSlot})))}update(e){e?this._resizeForced():this._resize()}focus(e){this._containerRef?.focus(e)}blur(){this._containerRef?.blur()}}function _(e){const t=(0,r.ensureNotNull)((0,s.useContext)(m.SubmenuContext)),n=s.useRef(null);return s.createElement("div",{ref:n,className:e.className,onMouseOver:function(e){if(!(null!==t.current&&e.target instanceof Node&&(s=e.target,n.current?.contains(s))))return;var s;t.isSubmenuNode(e.target)||t.setCurrent(null)},"data-name":"menu-inner"},e.children)}b.contextType=m.SubmenuContext},192063:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_POPUP_MENU_ITEM_THEME:()=>u,PopupMenuItem:()=>h});var s=n(50959),o=n(497754),i=n(32133),r=n(370981),l=n(361701),a=n(111706),d=n(509059);const u=d;function c(e){e.stopPropagation()}function h(e){const{id:t,role:n,className:u,title:h,labelRowClassName:m,labelClassName:p,toolboxClassName:f,shortcut:g,forceShowShortcuts:v,icon:b,iconClassname:_,isActive:S,isDisabled:y,isHovered:w,appearAsDisabled:x,label:C,link:E,showToolboxOnHover:k,showToolboxOnFocus:T,target:I,rel:M,toolbox:N,toolboxRole:L,reference:A,onMouseOut:D,onMouseOver:P,onKeyDown:R,suppressToolboxClick:W=!0,theme:O=d,tabIndex:B,tagName:F,renderComponent:V,roundedIcon:H,iconAriaProps:U,circleLogo:z,dontClosePopup:j,onClick:G,onClickArg:Q,trackEventObject:q,trackMouseWheelClick:J,trackRightClick:$,...K}=e,X=(0,s.useRef)(null),Y=(0,s.useMemo)((()=>function(e){function t(t){const{reference:n,...o}=t,i=e??(o.href?"a":"div"),r="a"===i?o:function(e){const{download:t,href:n,hrefLang:s,media:o,ping:i,rel:r,target:l,type:a,referrerPolicy:d,...u}=e;return u}(o);return s.createElement(i,{...r,ref:n})}return t.displayName=`DefaultComponent(${e})`,t}(F)),[F]),Z=V??Y;return s.createElement(Z,{...K,id:t,role:n,className:o(u,O.item,b&&O.withIcon,{[O.isActive]:S,[O.isDisabled]:y||x,[O.hovered]:w}),title:h,href:E,target:I,rel:M,reference:function(e){X.current=e,"function"==typeof A&&A(e);"object"==typeof A&&(A.current=e)},onClick:function(e){if(y)return;q&&(0,i.trackEvent)(q.category,q.event,q.label);G&&G(Q,e);j||(e.currentTarget.dispatchEvent(new CustomEvent("popup-menu-close-event",{bubbles:!0,detail:{clickType:(0,a.isKeyboardClick)(e)?"keyboard":"mouse"}})),(0,r.globalCloseMenu)())},onContextMenu:function(e){q&&$&&(0,i.trackEvent)(q.category,q.event,`${q.label}_rightClick`)},onMouseUp:function(e){if(1===e.button&&E&&q){let e=q.label;J&&(e+="_mouseWheelClick"),(0,i.trackEvent)(q.category,q.event,e)}},onMouseOver:P,onMouseOut:D,onKeyDown:R,tabIndex:B},z&&s.createElement(l.CircleLogo,{...U,className:d["disclosure-item-circle-logo"],size:"xxxsmall",logoUrl:z.logoUrl,placeholderLetter:"placeholderLetter"in z?z.placeholderLetter:void 0}),b&&s.createElement("span",{"aria-label":U&&U["aria-label"],
"aria-hidden":U&&Boolean(U["aria-hidden"]),className:o(O.icon,H&&d["round-icon"],_),dangerouslySetInnerHTML:{__html:b}}),s.createElement("span",{className:o(O.labelRow,m)},s.createElement("span",{className:o(O.label,p)},C)),(void 0!==g||v)&&s.createElement("span",{className:O.shortcut},(ee=g)&&ee.split("+").join(" + ")),void 0!==N&&s.createElement("span",{role:L,onClick:W?c:void 0,className:o(f,O.toolbox,{[O.showOnHover]:k,[O.showOnFocus]:T})},N));var ee}},917850:(e,t,n)=>{"use strict";n.d(t,{PopupMenuSeparator:()=>a});var s,o=n(50959),i=n(497754),r=n.n(i),l=n(700238);function a(e){const{size:t="normal",className:n,ariaHidden:s=!1}=e;return o.createElement("div",{className:r()(l.separator,"small"===t&&l.small,"normal"===t&&l.normal,"large"===t&&l.large,n),role:"separator","aria-hidden":s})}!function(e){e.Small="small",e.Large="large",e.Normal="normal"}(s||(s={}))},28466:(e,t,n)=>{"use strict";n.d(t,{CloseDelegateContext:()=>i});var s=n(50959),o=n(370981);const i=s.createContext(o.globalCloseDelegate)},624216:(e,t,n)=>{"use strict";n.d(t,{PopupMenu:()=>h});var s=n(50959),o=n(632227),i=n(688987),r=n(8361),l=n(510618),a=n(28466);const d=s.createContext(void 0);var u=n(908783);const c=s.createContext({setMenuMaxWidth:!1});function h(e){const{controller:t,children:n,isOpened:h,closeOnClickOutside:m=!0,doNotCloseOn:p,onClickOutside:f,onClose:g,onKeyboardClose:v,"data-name":b="popup-menu-container",..._}=e,S=(0,s.useContext)(a.CloseDelegateContext),y=s.useContext(c),w=(0,s.useContext)(d),x=(0,u.useOutsideEvent)({handler:function(e){f&&f(e);if(!m)return;const t=(0,i.default)(p)?p():null==p?[]:[p];if(t.length>0&&e.target instanceof Node)for(const n of t){const t=o.findDOMNode(n);if(t instanceof Node&&t.contains(e.target))return}g()},mouseDown:!0,touchStart:!0});return h?s.createElement(r.Portal,{top:"0",left:"0",right:"0",bottom:"0",pointerEvents:"none"},s.createElement("span",{ref:x,style:{pointerEvents:"auto"}},s.createElement(l.Menu,{..._,onClose:g,onKeyboardClose:v,onScroll:function(t){const{onScroll:n}=e;n&&n(t)},customCloseDelegate:S,customRemeasureDelegate:w,ref:t,"data-name":b,limitMaxWidth:y.setMenuMaxWidth,"data-tooltip-show-on-focus":"true"},n))):null}},8361:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>s.Portal,PortalContext:()=>s.PortalContext});var s=n(730654)},72621:(e,t,n)=>{"use strict";n.d(t,{RemoveButton:()=>c});var s=n(609838),o=n(50959),i=n(497754),r=n.n(i),l=n(878112),a=n(227570),d=n(333765),u=n(35990);function c(e){const{className:t,isActive:i,onClick:c,onMouseDown:h,title:m,hidden:p,"data-name":f="remove-button",icon:g,...v}=e,[b,_]=(0,a.useActiveDescendant)(null);return o.createElement(l.Icon,{...v,"data-name":f,className:r()(u.button,"apply-common-tooltip",i&&u.active,p&&u.hidden,_&&u.focused,t),icon:g||d,onClick:c,onMouseDown:h,title:m??s.t(null,void 0,n(767410)),ariaLabel:m??s.t(null,void 0,n(767410)),ref:b})}},117601:(e,t,n)=>{"use strict";n.d(t,{Renamable:()=>d});var s=n(50959),o=n(497754),i=n(409174),r=n(180185),l=n(822312),a=n(491989);function d(e){
const{name:t,isRenaming:n,onSubmit:d,onClose:u,children:c,inputClassName:h,size:m,maxLength:p,emojiPicker:f}=e,[g,v]=(0,s.useState)(t),b=(0,s.useRef)(null),_=(0,s.useRef)(null);return(0,s.useEffect)((()=>{v(t)}),[t]),(0,s.useEffect)((()=>{n&&null!==b.current&&(b.current.focus(),b.current.setSelectionRange(0,g.length))}),[n]),n?s.createElement(l.InputWithEmojiSelect,{value:g,onChange:function(e){v(e)},onClick:i.preventDefault,className:o(a.renameInput),inputClassName:h,onKeyDown:function(e){27===(0,r.hashFromEvent)(e)?(e.preventDefault(),S()):13===(0,r.hashFromEvent)(e)&&(e.preventDefault(),y())},reference:b,containerReference:_,onBlur:function(e){if(_.current?.contains(e.relatedTarget))return;y()},onDragStart:function(e){e.preventDefault(),e.stopPropagation()},size:m,maxLength:p,draggable:!0,stretch:!0,emojiPicker:f}):c;function S(){v(t),u()}function y(){""!==g?d(g):S()}}},753327:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>s.Slot,SlotContext:()=>s.SlotContext});var s=n(682925)},132455:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>s.Spinner});var s=n(672511)},515783:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetCaret:()=>a});var s=n(50959),o=n(497754),i=n(878112),r=n(149128),l=n(100578);function a(e){const{dropped:t,className:n}=e;return s.createElement(i.Icon,{className:o(n,r.icon,{[r.dropped]:t}),icon:l})}},438576:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},568484:e=>{e.exports={spinnerWrap:"spinnerWrap-cZT0OZe0"}},155352:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>l,ToolWidgetButton:()=>a});var s=n(50959),o=n(497754),i=n(878112),r=n(438576);const l=r,a=s.forwardRef(((e,t)=>{const{tag:n="div",icon:l,endIcon:a,isActive:d,isOpened:u,isDisabled:c,isGrouped:h,isHovered:m,isClicked:p,onClick:f,text:g,textBeforeIcon:v,title:b,theme:_=r,className:S,forceInteractive:y,inactive:w,"data-name":x,"data-tooltip":C,...E}=e,k=o(S,_.button,(b||C)&&"apply-common-tooltip",{[_.isActive]:d,[_.isOpened]:u,[_.isInteractive]:(y||Boolean(f))&&!c&&!w,[_.isDisabled]:Boolean(c||w),[_.isGrouped]:h,[_.hover]:m,[_.clicked]:p}),T=l&&("string"==typeof l?s.createElement(i.Icon,{className:_.icon,icon:l}):s.cloneElement(l,{className:o(_.icon,l.props.className)}));return"button"===n?s.createElement("button",{...E,ref:t,type:"button",className:o(k,_.accessible),disabled:c&&!w,onClick:f,title:b,"data-name":x,"data-tooltip":C},v&&g&&s.createElement("div",{className:o("js-button-text",_.text)},g),T,!v&&g&&s.createElement("div",{className:o("js-button-text",_.text)},g)):s.createElement("div",{...E,ref:t,"data-role":"button",className:k,onClick:c?void 0:f,title:b,"data-name":x,"data-tooltip":C},v&&g&&s.createElement("div",{className:o("js-button-text",_.text)},g),T,!v&&g&&s.createElement("div",{className:o("js-button-text",_.text)
},g),a&&s.createElement(i.Icon,{icon:a,className:r.endIcon}))}))},531600:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetMenuSpinner:()=>a});var s=n(50959),o=n(497754),i=n.n(o),r=n(234404),l=n(568484);function a(e){const{className:t}=e;return s.createElement("div",{className:i()(l.spinnerWrap,t)},s.createElement(r.Loader,null))}},642659:(e,t,n)=>{"use strict";n.d(t,{BrokerService:()=>i});var s,o=n(650151);!function(e){e.PositionsWatcher="PositionsWatcher",e.TradingNotificationsHandler="TradingNotificationsHandler",e.TradingStat="TradingStat",e.ExecutionsService="ExecutionsService",e.OrdersService="OrdersService",e.PositionsService="PositionsService"}(s||(s={}));class i{constructor(e,t){this._activeBroker=null,this._trading=e,this._serviceName=t,this._trading.onConnectionStatusChange.subscribe(this,this._onStatusChange),this._onStatusChange(this._trading.connectStatus())}destroy(){this.stopService(),this._trading.onConnectionStatusChange.unsubscribeAll(this)}activeBroker(){return this._activeBroker}trading(){return this._trading}_stopService(){this.stopService();const e=(0,o.ensureNotNull)(this._activeBroker,"activeBroker");switch(e.currentAccountUpdate.unsubscribeAll(this),this._serviceName){case"PositionsService":case"PositionsWatcher":e.positionsFullUpdate.unsubscribe(this,this._restartService),e.individualPositionsFullUpdate.unsubscribe(this,this._restartService);break;case"OrdersService":e.ordersFullUpdate.unsubscribe(this,this._restartService)}}_startService(){this.startService();const e=(0,o.ensureNotNull)(this._activeBroker,"activeBroker");switch(e.currentAccountUpdate.subscribe(this,this._onCurrentAccountUpdate),this._serviceName){case"PositionsService":case"PositionsWatcher":e.positionsFullUpdate.subscribe(this,this._restartService),e.individualPositionsFullUpdate.subscribe(this,this._restartService);break;case"OrdersService":e.ordersFullUpdate.subscribe(this,this._restartService)}}_onStatusChange(e){const t=this._trading.activeBroker();this._activeBroker===t&&1===e||(null!==this._activeBroker&&(this._stopService(),this._activeBroker=null),null!==t&&1===e&&(this._activeBroker=t,this._startService(),this._lastAccountId=t.currentAccount()))}_restartService(){this.stopService(),this.startService()}_onCurrentAccountUpdate(){const e=(0,o.ensureNotNull)(this._activeBroker);this._lastAccountId!==e.currentAccount()&&(this._restartService(),this._lastAccountId=e.currentAccount())}}},57174:(e,t,n)=>{"use strict";n.d(t,{LastChangeFormatter:()=>i});var s=n(172912),o=n(459895);class i{constructor(e){const{priceScale:t,minMove:n,fractional:i,minMove2:r,variableMinTick:l,precision:a,format:d,ignoreLocaleNumberFormat:u}=e;this._formatter="volume"===d?new o.VolumeFormatter({precision:a??2,ignoreLocaleNumberFormat:u}):new s.PriceFormatter({priceScale:t,minMove:n,fractional:i,minMove2:r,variableMinTick:l||void 0,ignoreLocaleNumberFormat:u})}format(e,t={}){const{signPositive:n,tailSize:s,signNegative:o=!0,useRtlFormat:i=!0,ignoreLocaleNumberFormat:r}=t;return"price"===this._formatter.type?this._formatter.format(e,{signPositive:n,
tailSize:s,signNegative:o,useRtlFormat:i,ignoreLocaleNumberFormat:r}):this._formatter.format(e,{signPositive:n,ignoreLocaleNumberFormat:r})}formatChange(e,t,n={}){const{signPositive:s,ignoreLocaleNumberFormat:o}=n;return"price"===this._formatter.type?this._formatter.formatChange(e,t,{signPositive:s,ignoreLocaleNumberFormat:o}):this._formatter.format(e-t,{signPositive:s,ignoreLocaleNumberFormat:o})}hasForexAdditionalPrecision(){return"hasForexAdditionalPrecision"in this._formatter&&this._formatter.hasForexAdditionalPrecision()}}},602948:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.92 7.83 9 12.29l5.08-4.46-1-1.13L9 10.29l-4.09-3.6-.99 1.14Z"/></svg>'},913042:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M8.5 3.84l.34.28 4 3.5-.66.76L9.01 5.6V14H8V5.6L4.84 8.38l-.66-.76 4-3.5.33-.28z"/></svg>'},347531:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="m4.67 7.38.66-.76L9 9.84l3.67-3.22.66.76L9 11.16 4.67 7.38Z"/></svg>'},615309:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9.84 9 6.62 5.33l.76-.66L11.16 9l-3.78 4.33-.76-.66L9.84 9Z"/></svg>'},394032:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M8 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM11 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2ZM6.2 10A3 3 0 0 0 9 12a3 3 0 0 0 2.8-2l.95.34A4 4 0 0 1 9 13a4 4 0 0 1-3.75-2.66L6.2 10Z"/><path fill="currentColor" fill-rule="evenodd" d="M1 9a8 8 0 1 1 16 0A8 8 0 0 1 1 9Zm1 0a7 7 0 1 1 14 0A7 7 0 0 1 2 9Z"/></svg>'},672197:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M10 6.13V8L6 5.5 10 3v2.1A5 5 0 1 1 4 10a.5.5 0 0 1 1 0 4 4 0 1 0 5-3.87Z"/></svg>'},611443:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="currentColor"><path fill-rule="evenodd" d="M6 6a3 3 0 0 1 6 0v1H6V6ZM5 7V6a4 4 0 1 1 8 0v1a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9c0-1.1.9-2 2-2Zm3 4a1 1 0 1 1 2 0v1a1 1 0 1 1-2 0v-1Z"/></svg>'},388140:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M4 10h5V9H4v1zM11 10h6V9h-6v1zM24 10h-5V9h5v1zM6.5 14c-.83 0-1.5.67-1.5 1.5v3c0 .83.67 1.5 1.5 1.5h15c.83 0 1.5-.67 1.5-1.5v-3c0-.83-.67-1.5-1.5-1.5h-15zM6 15.5c0-.28.22-.5.5-.5h15c.28 0 .5.22.5.5v3a.5.5 0 0 1-.5.5h-15a.5.5 0 0 1-.5-.5v-3z"/></svg>'},465890:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11 9" width="11" height="9" fill="none"><path stroke="currentColor" stroke-width="2" d="M0.999878 4L3.99988 7L9.99988 1"/></svg>'},100578:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},333765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},72952:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 4.5l4.39 3.61a.5.5 0 0 1-.32.89h-1.18v4.5H6.11V9H4.93a.5.5 0 0 1-.32-.89L9 4.5z"/></svg>'},833366:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" d="M7 13h7V6h1v7h7v1h-7v7h-1v-7H7v-1z"/></svg>'},119850:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 7 9" width="7" height="9" fill="none"><path stroke="currentColor" d="M6 5L3.5 7.5L1 5M3.5 7.5V0"/></svg>'},975361:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 7 9" width="7" height="9" fill="none"><path stroke="currentColor" d="M6 4L3.5 1.5L1 4M3.5 9V2"/></svg>'},133736:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5c1 3 7 7 11 7 2-4 2-8.5 2-8.5s-1-3.5-2-4-4.5.5-4.5.5-3 3.5-6.5 5z"/><path stroke="currentColor" d="M15.5 11l3-6s.5-1 1.5-.5.5 1.5.5 1.5l-3 6M12 11.5l6.5 3.5M7.5 19c2-.5 4-2.5 4-2.5m0 5.5c2-1 3-3.5 3-3.5"/></svg>'},808757:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 16v4.5a1 1 0 001 1h14a1 1 0 001-1V16M14.5 18V5.5m-4 4l4-4l4 4"/></svg>'},323595:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 16v4.5a1 1 0 001 1h14a1 1 0 001-1V16M14.5 5V17m-4-3.5l4 4l4-4"/></svg>'},569533:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'},918989:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="currentColor" d="M24 74a4 4 0 1 1-8 0 4 4 0 0 1 8 0ZM31 48a4 4 0 1 0 0-8 4 4 0 0 0 0 8ZM71 46H41v-4h30v4ZM88 87V77h4v10h10v4H92v10h-4V91H78v-4h10Z"/><path fill="currentColor" fill-rule="evenodd" d="M28 33V17h64v48.08A24 24 0 1 1 69.82 102H28V85H8V63h20v-8h-9V33h9Zm4-12h56v44.08A24 24 0 0 0 67.74 98H32V55h51V33H32V21Zm-4 46H12v14h16V67Zm-5-30h56v14H23V37Zm67 72a20 20 0 1 0 0-40 20 20 0 0 0 0 40Z"/></svg>'},715336:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="11" height="20" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M5.5 14a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3zm0-5a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3zM7 5.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0z"/></svg>'},563140:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none"><circle stroke="currentColor" cx="6" cy="6" r="5.5"/><path fill="currentColor" d="M4.5 8.523V2.8h.928v4.826H8.5v.897h-4z"/></svg>'},531340:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none"><circle stroke="#F44336" cx="6" cy="6" r="5.5"/><path fill="#F44336" d="M4.267 8.636l.31-.73c.159.11.355.2.59.274.**************.638.107.33 0 .597-.084.798-.253a.806.806 0 0 0 .302-.646.945.945 0 0 0-.17-.542c-.11-.17-.391-.353-.841-.551l-.501-.218c-.425-.185-.722-.404-.892-.657-.167-.254-.251-.559-.251-.915 0-.433.164-.792.493-1.077C5.07 3.143 5.493 3 6.008 3c.689 0 1.167.104 1.436.313l-.25.689a2.019 2.019 0 0 0-.519-.222 2.21 2.21 0 0 0-.645-.107c-.29 0-.517.077-.684.23a.77.77 0 0 0-.246.59c0 .148.03.283.089.404.06.121.141.223.246.305.108.082.326.197.654.345l.51.225c.425.188.722.412.892.674.173.258.259.588.259.99 0 .435-.188.805-.565 1.109C6.811 8.848 6.31 9 5.681 9c-.552 0-1.023-.121-1.414-.364z"/></svg>'},818438:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});const s=function(){var e={base:"https://twemoji.maxcdn.com/v/13.0.1/",ext:".png",size:"72x72",className:"emoji",convert:{fromCodePoint:function(e){var t="string"==typeof e?parseInt(e,16):e;if(t<65536)return l(t);return l(55296+((t-=65536)>>10),56320+(1023&t))},toCodePoint:b},onerror:function(){this.parentNode&&this.parentNode.replaceChild(a(this.alt,!1),this)},parse:function(t,n){n&&"function"!=typeof n||(n={callback:n});return("string"==typeof t?p:m)(t,{callback:n.callback||u,attributes:"function"==typeof n.attributes?n.attributes:g,base:"string"==typeof n.base?n.base:e.base,ext:n.ext||e.ext,size:n.folder||(s=n.size||e.size,"number"==typeof s?s+"x"+s:s),className:n.className||e.className,onerror:n.onerror||e.onerror});var s},replace:v,test:function(e){n.lastIndex=0;var t=n.test(e);return n.lastIndex=0,t}},t={"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"
},n=/(?:\ud83d\udc68\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc68\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc68\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffe]|\ud83e\uddd1\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\u200d\ud83e\udd1d\u200d\ud83e\uddd1|\ud83d\udc6b\ud83c[\udffb-\udfff]|\ud83d\udc6c\ud83c[\udffb-\udfff]|\ud83d\udc6d\ud83c[\udffb-\udfff]|\ud83d[\udc6b-\udc6d])|(?:\ud83d[\udc68\udc69]|\ud83e\uddd1)(?:\ud83c[\udffb-\udfff])?\u200d(?:\u2695\ufe0f|\u2696\ufe0f|\u2708\ufe0f|\ud83c[\udf3e\udf73\udf7c\udf84\udf93\udfa4\udfa8\udfeb\udfed]|\ud83d[\udcbb\udcbc\udd27\udd2c\ude80\ude92]|\ud83e[\uddaf-\uddb3\uddbc\uddbd])|(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75]|\u26f9)((?:\ud83c[\udffb-\udfff]|\ufe0f)\u200d[\u2640\u2642]\ufe0f)|(?:\ud83c[\udfc3\udfc4\udfca]|\ud83d[\udc6e\udc70\udc71\udc73\udc77\udc81\udc82\udc86\udc87\ude45-\ude47\ude4b\ude4d\ude4e\udea3\udeb4-\udeb6]|\ud83e[\udd26\udd35\udd37-\udd39\udd3d\udd3e\uddb8\uddb9\uddcd-\uddcf\uddd6-\udddd])(?:\ud83c[\udffb-\udfff])?\u200d[\u2640\u2642]\ufe0f|(?:\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f|\ud83c\udff3\ufe0f\u200d\ud83c\udf08|\ud83c\udff4\u200d\u2620\ufe0f|\ud83d\udc15\u200d\ud83e\uddba|\ud83d\udc3b\u200d\u2744\ufe0f|\ud83d\udc41\u200d\ud83d\udde8|\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc6f\u200d\u2640\ufe0f|\ud83d\udc6f\u200d\u2642\ufe0f|\ud83e\udd3c\u200d\u2640\ufe0f|\ud83e\udd3c\u200d\u2642\ufe0f|\ud83e\uddde\u200d\u2640\ufe0f|\ud83e\uddde\u200d\u2642\ufe0f|\ud83e\udddf\u200d\u2640\ufe0f|\ud83e\udddf\u200d\u2642\ufe0f|\ud83d\udc08\u200d\u2b1b)|[#*0-9]\ufe0f?\u20e3|(?:[©®\u2122\u265f]\ufe0f)|(?:\ud83c[\udc04\udd70\udd71\udd7e\udd7f\ude02\ude1a\ude2f\ude37\udf21\udf24-\udf2c\udf36\udf7d\udf96\udf97\udf99-\udf9b\udf9e\udf9f\udfcd\udfce\udfd4-\udfdf\udff3\udff5\udff7]|\ud83d[\udc3f\udc41\udcfd\udd49\udd4a\udd6f\udd70\udd73\udd76-\udd79\udd87\udd8a-\udd8d\udda5\udda8\uddb1\uddb2\uddbc\uddc2-\uddc4\uddd1-\uddd3\udddc-\uddde\udde1\udde3\udde8\uddef\uddf3\uddfa\udecb\udecd-\udecf\udee0-\udee5\udee9\udef0\udef3]|[\u203c\u2049\u2139\u2194-\u2199\u21a9\u21aa\u231a\u231b\u2328\u23cf\u23ed-\u23ef\u23f1\u23f2\u23f8-\u23fa\u24c2\u25aa\u25ab\u25b6\u25c0\u25fb-\u25fe\u2600-\u2604\u260e\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262a\u262e\u262f\u2638-\u263a\u2640\u2642\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267b\u267f\u2692-\u2697\u2699\u269b\u269c\u26a0\u26a1\u26a7\u26aa\u26ab\u26b0\u26b1\u26bd\u26be\u26c4\u26c5\u26c8\u26cf\u26d1\u26d3\u26d4\u26e9\u26ea\u26f0-\u26f5\u26f8\u26fa\u26fd\u2702\u2708\u2709\u270f\u2712\u2714\u2716\u271d\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u2764\u27a1\u2934\u2935\u2b05-\u2b07\u2b1b\u2b1c\u2b50\u2b55\u3030\u303d\u3297\u3299])(?:\ufe0f|(?!\ufe0e))|(?:(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75\udd90]|[\u261d\u26f7\u26f9\u270c\u270d])(?:\ufe0f|(?!\ufe0e))|(?:\ud83c[\udf85\udfc2-\udfc4\udfc7\udfca]|\ud83d[\udc42\udc43\udc46-\udc50\udc66-\udc69\udc6e\udc70-\udc78\udc7c\udc81-\udc83\udc85-\udc87\udcaa\udd7a\udd95\udd96\ude45-\ude47\ude4b-\ude4f\udea3\udeb4-\udeb6\udec0\udecc]|\ud83e[\udd0c\udd0f\udd18-\udd1c\udd1e\udd1f\udd26\udd30-\udd39\udd3d\udd3e\udd77\uddb5\uddb6\uddb8\uddb9\uddbb\uddcd-\uddcf\uddd1-\udddd]|[\u270a\u270b]))(?:\ud83c[\udffb-\udfff])?|(?:\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc73\udb40\udc63\udb40\udc74\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc77\udb40\udc6c\udb40\udc73\udb40\udc7f|\ud83c\udde6\ud83c[\udde8-\uddec\uddee\uddf1\uddf2\uddf4\uddf6-\uddfa\uddfc\uddfd\uddff]|\ud83c\udde7\ud83c[\udde6\udde7\udde9-\uddef\uddf1-\uddf4\uddf6-\uddf9\uddfb\uddfc\uddfe\uddff]|\ud83c\udde8\ud83c[\udde6\udde8\udde9\uddeb-\uddee\uddf0-\uddf5\uddf7\uddfa-\uddff]|\ud83c\udde9\ud83c[\uddea\uddec\uddef\uddf0\uddf2\uddf4\uddff]|\ud83c\uddea\ud83c[\udde6\udde8\uddea\uddec\udded\uddf7-\uddfa]|\ud83c\uddeb\ud83c[\uddee-\uddf0\uddf2\uddf4\uddf7]|\ud83c\uddec\ud83c[\udde6\udde7\udde9-\uddee\uddf1-\uddf3\uddf5-\uddfa\uddfc\uddfe]|\ud83c\udded\ud83c[\uddf0\uddf2\uddf3\uddf7\uddf9\uddfa]|\ud83c\uddee\ud83c[\udde8-\uddea\uddf1-\uddf4\uddf6-\uddf9]|\ud83c\uddef\ud83c[\uddea\uddf2\uddf4\uddf5]|\ud83c\uddf0\ud83c[\uddea\uddec-\uddee\uddf2\uddf3\uddf5\uddf7\uddfc\uddfe\uddff]|\ud83c\uddf1\ud83c[\udde6-\udde8\uddee\uddf0\uddf7-\uddfb\uddfe]|\ud83c\uddf2\ud83c[\udde6\udde8-\udded\uddf0-\uddff]|\ud83c\uddf3\ud83c[\udde6\udde8\uddea-\uddec\uddee\uddf1\uddf4\uddf5\uddf7\uddfa\uddff]|\ud83c\uddf4\ud83c\uddf2|\ud83c\uddf5\ud83c[\udde6\uddea-\udded\uddf0-\uddf3\uddf7-\uddf9\uddfc\uddfe]|\ud83c\uddf6\ud83c\udde6|\ud83c\uddf7\ud83c[\uddea\uddf4\uddf8\uddfa\uddfc]|\ud83c\uddf8\ud83c[\udde6-\uddea\uddec-\uddf4\uddf7-\uddf9\uddfb\uddfd-\uddff]|\ud83c\uddf9\ud83c[\udde6\udde8\udde9\uddeb-\udded\uddef-\uddf4\uddf7\uddf9\uddfb\uddfc\uddff]|\ud83c\uddfa\ud83c[\udde6\uddec\uddf2\uddf3\uddf8\uddfe\uddff]|\ud83c\uddfb\ud83c[\udde6\udde8\uddea\uddec\uddee\uddf3\uddfa]|\ud83c\uddfc\ud83c[\uddeb\uddf8]|\ud83c\uddfd\ud83c\uddf0|\ud83c\uddfe\ud83c[\uddea\uddf9]|\ud83c\uddff\ud83c[\udde6\uddf2\uddfc]|\ud83c[\udccf\udd8e\udd91-\udd9a\udde6-\uddff\ude01\ude32-\ude36\ude38-\ude3a\ude50\ude51\udf00-\udf20\udf2d-\udf35\udf37-\udf7c\udf7e-\udf84\udf86-\udf93\udfa0-\udfc1\udfc5\udfc6\udfc8\udfc9\udfcf-\udfd3\udfe0-\udff0\udff4\udff8-\udfff]|\ud83d[\udc00-\udc3e\udc40\udc44\udc45\udc51-\udc65\udc6a\udc6f\udc79-\udc7b\udc7d-\udc80\udc84\udc88-\udca9\udcab-\udcfc\udcff-\udd3d\udd4b-\udd4e\udd50-\udd67\udda4\uddfb-\ude44\ude48-\ude4a\ude80-\udea2\udea4-\udeb3\udeb7-\udebf\udec1-\udec5\uded0-\uded2\uded5-\uded7\udeeb\udeec\udef4-\udefc\udfe0-\udfeb]|\ud83e[\udd0d\udd0e\udd10-\udd17\udd1d\udd20-\udd25\udd27-\udd2f\udd3a\udd3c\udd3f-\udd45\udd47-\udd76\udd78\udd7a-\uddb4\uddb7\uddba\uddbc-\uddcb\uddd0\uddde-\uddff\ude70-\ude74\ude78-\ude7a\ude80-\ude86\ude90-\udea8\udeb0-\udeb6\udec0-\udec2\uded0-\uded6]|[\u23e9-\u23ec\u23f0\u23f3\u267e\u26ce\u2705\u2728\u274c\u274e\u2753-\u2755\u2795-\u2797\u27b0\u27bf\ue50a])|\ufe0f/g,s=/\uFE0F/g,o=String.fromCharCode(8205),i=/[&<>'"]/g,r=/^(?:iframe|noframes|noscript|script|select|style|textarea)$/,l=String.fromCharCode
;return e;function a(e,t){return document.createTextNode(t?e.replace(s,""):e)}function d(e){return e.replace(i,f)}function u(e,t){return"".concat(t.base,t.size,"/",e,t.ext)}function c(e,t){for(var n,s,o=e.childNodes,i=o.length;i--;)3===(s=(n=o[i]).nodeType)?t.push(n):1!==s||"ownerSVGElement"in n||r.test(n.nodeName.toLowerCase())||c(n,t);return t}function h(e){return b(e.indexOf(o)<0?e.replace(s,""):e)}function m(e,t){for(var s,o,i,r,l,d,u,m,p,f,g,v,b,_=c(e,[]),S=_.length;S--;){for(i=!1,r=document.createDocumentFragment(),d=(l=_[S]).nodeValue,m=0;u=n.exec(d);){if((p=u.index)!==m&&r.appendChild(a(d.slice(m,p),!0)),v=h(g=u[0]),m=p+g.length,b=t.callback(v,t),v&&b){for(o in(f=new Image).onerror=t.onerror,f.setAttribute("draggable","false"),s=t.attributes(g,v))s.hasOwnProperty(o)&&0!==o.indexOf("on")&&!f.hasAttribute(o)&&f.setAttribute(o,s[o]);f.className=t.className,f.alt=g,f.src=b,i=!0,r.appendChild(f)}f||r.appendChild(a(g,!1)),f=null}i&&(m<d.length&&r.appendChild(a(d.slice(m),!0)),l.parentNode.replaceChild(r,l))}return e}function p(e,t){return v(e,(function(e){var n,s,o=e,i=h(e),r=t.callback(i,t);if(i&&r){for(s in o="<img ".concat('class="',t.className,'" ','draggable="false" ','alt="',e,'"',' src="',r,'"'),n=t.attributes(e,i))n.hasOwnProperty(s)&&0!==s.indexOf("on")&&-1===o.indexOf(" "+s+"=")&&(o=o.concat(" ",s,'="',d(n[s]),'"'));o=o.concat("/>")}return o}))}function f(e){return t[e]}function g(){return null}function v(e,t){return String(e).replace(n,t)}function b(e,t){for(var n=[],s=0,o=0,i=0;i<e.length;)s=e.charCodeAt(i++),o?(n.push((65536+(o-55296<<10)+(s-56320)).toString(16)),o=0):55296<=s&&s<=56319?o=s:n.push(s.toString(16));return n.join(t||"-")}}()},755883:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});const s=function(){}},207809:(e,t,n)=>{"use strict";n.d(t,{TouchBackend:()=>p});var s,o=n(6346);!function(e){e.mouse="mouse",e.touch="touch",e.keyboard="keyboard"}(s||(s={}));class i{get delay(){var e;return null!==(e=this.args.delay)&&void 0!==e?e:0}get scrollAngleRanges(){return this.args.scrollAngleRanges}get getDropTargetElementsAtPoint(){return this.args.getDropTargetElementsAtPoint}get ignoreContextMenu(){var e;return null!==(e=this.args.ignoreContextMenu)&&void 0!==e&&e}get enableHoverOutsideTarget(){var e;return null!==(e=this.args.enableHoverOutsideTarget)&&void 0!==e&&e}get enableKeyboardEvents(){var e;return null!==(e=this.args.enableKeyboardEvents)&&void 0!==e&&e}get enableMouseEvents(){var e;return null!==(e=this.args.enableMouseEvents)&&void 0!==e&&e}get enableTouchEvents(){var e;return null===(e=this.args.enableTouchEvents)||void 0===e||e}get touchSlop(){return this.args.touchSlop||0}get delayTouchStart(){var e,t,n,s;return null!==(s=null!==(n=null===(e=this.args)||void 0===e?void 0:e.delayTouchStart)&&void 0!==n?n:null===(t=this.args)||void 0===t?void 0:t.delay)&&void 0!==s?s:0}get delayMouseStart(){var e,t,n,s;return null!==(s=null!==(n=null===(e=this.args)||void 0===e?void 0:e.delayMouseStart)&&void 0!==n?n:null===(t=this.args)||void 0===t?void 0:t.delay)&&void 0!==s?s:0}get window(){
return this.context&&this.context.window?this.context.window:"undefined"!=typeof window?window:void 0}get document(){var e;return(null===(e=this.context)||void 0===e?void 0:e.document)?this.context.document:this.window?this.window.document:void 0}get rootElement(){var e;return(null===(e=this.args)||void 0===e?void 0:e.rootElement)||this.document}constructor(e,t){this.args=e,this.context=t}}const r=1,l=0;function a(e){return void 0===e.button||e.button===l}function d(e){return!!e.targetTouches}function u(e,t){return d(e)?function(e,t){return 1===e.targetTouches.length?u(e.targetTouches[0]):t&&1===e.touches.length&&e.touches[0].target===t.target?u(e.touches[0]):void 0}(e,t):{x:e.clientX,y:e.clientY}}const c=(()=>{let e=!1;try{addEventListener("test",(()=>{}),Object.defineProperty({},"passive",{get:()=>(e=!0,!0)}))}catch(e){}return e})(),h={[s.mouse]:{start:"mousedown",move:"mousemove",end:"mouseup",contextmenu:"contextmenu"},[s.touch]:{start:"touchstart",move:"touchmove",end:"touchend"},[s.keyboard]:{keydown:"keydown"}};class m{profile(){var e;return{sourceNodes:this.sourceNodes.size,sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,targetNodes:this.targetNodes.size,dragOverTargetIds:(null===(e=this.dragOverTargetIds)||void 0===e?void 0:e.length)||0}}get document(){return this.options.document}setup(){const e=this.options.rootElement;e&&((0,o.invariant)(!m.isSetUp,"Cannot have two Touch backends at the same time."),m.isSetUp=!0,this.addEventListener(e,"start",this.getTopMoveStartHandler()),this.addEventListener(e,"start",this.handleTopMoveStartCapture,!0),this.addEventListener(e,"move",this.handleTopMove),this.addEventListener(e,"move",this.handleTopMoveCapture,!0),this.addEventListener(e,"end",this.handleTopMoveEndCapture,!0),this.options.enableMouseEvents&&!this.options.ignoreContextMenu&&this.addEventListener(e,"contextmenu",this.handleTopMoveEndCapture),this.options.enableKeyboardEvents&&this.addEventListener(e,"keydown",this.handleCancelOnEscape,!0))}teardown(){const e=this.options.rootElement;e&&(m.isSetUp=!1,this._mouseClientOffset={},this.removeEventListener(e,"start",this.handleTopMoveStartCapture,!0),this.removeEventListener(e,"start",this.handleTopMoveStart),this.removeEventListener(e,"move",this.handleTopMoveCapture,!0),this.removeEventListener(e,"move",this.handleTopMove),this.removeEventListener(e,"end",this.handleTopMoveEndCapture,!0),this.options.enableMouseEvents&&!this.options.ignoreContextMenu&&this.removeEventListener(e,"contextmenu",this.handleTopMoveEndCapture),this.options.enableKeyboardEvents&&this.removeEventListener(e,"keydown",this.handleCancelOnEscape,!0),this.uninstallSourceNodeRemovalObserver())}addEventListener(e,t,n,s=!1){const o=c?{capture:s,passive:!1}:s;this.listenerTypes.forEach((function(s){const i=h[s][t];i&&e.addEventListener(i,n,o)}))}removeEventListener(e,t,n,s=!1){const o=c?{capture:s,passive:!1}:s;this.listenerTypes.forEach((function(s){const i=h[s][t];i&&e.removeEventListener(i,n,o)}))}connectDragSource(e,t){
const n=this.handleMoveStart.bind(this,e);return this.sourceNodes.set(e,t),this.addEventListener(t,"start",n),()=>{this.sourceNodes.delete(e),this.removeEventListener(t,"start",n)}}connectDragPreview(e,t,n){return this.sourcePreviewNodeOptions.set(e,n),this.sourcePreviewNodes.set(e,t),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDropTarget(e,t){const n=this.options.rootElement;if(!this.document||!n)return()=>{};const s=s=>{if(!this.document||!n||!this.monitor.isDragging())return;let o;switch(s.type){case h.mouse.move:o={x:s.clientX,y:s.clientY};break;case h.touch.move:var i,r;o={x:(null===(i=s.touches[0])||void 0===i?void 0:i.clientX)||0,y:(null===(r=s.touches[0])||void 0===r?void 0:r.clientY)||0}}const l=null!=o?this.document.elementFromPoint(o.x,o.y):void 0,a=l&&t.contains(l);return l===t||a?this.handleMove(s,e):void 0};return this.addEventListener(this.document.body,"move",s),this.targetNodes.set(e,t),()=>{this.document&&(this.targetNodes.delete(e),this.removeEventListener(this.document.body,"move",s))}}getTopMoveStartHandler(){return this.options.delayTouchStart||this.options.delayMouseStart?this.handleTopMoveStartDelay:this.handleTopMoveStart}installSourceNodeRemovalObserver(e){this.uninstallSourceNodeRemovalObserver(),this.draggedSourceNode=e,this.draggedSourceNodeRemovalObserver=new MutationObserver((()=>{e&&!e.parentElement&&(this.resurrectSourceNode(),this.uninstallSourceNodeRemovalObserver())})),e&&e.parentElement&&this.draggedSourceNodeRemovalObserver.observe(e.parentElement,{childList:!0})}resurrectSourceNode(){this.document&&this.draggedSourceNode&&(this.draggedSourceNode.style.display="none",this.draggedSourceNode.removeAttribute("data-reactid"),this.document.body.appendChild(this.draggedSourceNode))}uninstallSourceNodeRemovalObserver(){this.draggedSourceNodeRemovalObserver&&this.draggedSourceNodeRemovalObserver.disconnect(),this.draggedSourceNodeRemovalObserver=void 0,this.draggedSourceNode=void 0}constructor(e,t,n){this.getSourceClientOffset=e=>{const t=this.sourceNodes.get(e);return t&&function(e){const t=1===e.nodeType?e:e.parentElement;if(!t)return;const{top:n,left:s}=t.getBoundingClientRect();return{x:s,y:n}}(t)},this.handleTopMoveStartCapture=e=>{a(e)&&(this.moveStartSourceIds=[])},this.handleMoveStart=e=>{Array.isArray(this.moveStartSourceIds)&&this.moveStartSourceIds.unshift(e)},this.handleTopMoveStart=e=>{if(!a(e))return;const t=u(e);t&&(d(e)&&(this.lastTargetTouchFallback=e.targetTouches[0]),this._mouseClientOffset=t),this.waitingForDelay=!1},this.handleTopMoveStartDelay=e=>{if(!a(e))return;const t=e.type===h.touch.start?this.options.delayTouchStart:this.options.delayMouseStart;this.timeout=setTimeout(this.handleTopMoveStart.bind(this,e),t),this.waitingForDelay=!0},this.handleTopMoveCapture=()=>{this.dragOverTargetIds=[]},this.handleMove=(e,t)=>{this.dragOverTargetIds&&this.dragOverTargetIds.unshift(t)},this.handleTopMove=e=>{if(this.timeout&&clearTimeout(this.timeout),!this.document||this.waitingForDelay)return
;const{moveStartSourceIds:t,dragOverTargetIds:n}=this,s=this.options.enableHoverOutsideTarget,o=u(e,this.lastTargetTouchFallback);if(!o)return;if(this._isScrolling||!this.monitor.isDragging()&&function(e,t,n,s,o){if(!o)return!1;const i=180*Math.atan2(s-t,n-e)/Math.PI+180;for(let e=0;e<o.length;++e){const t=o[e];if(t&&(null==t.start||i>=t.start)&&(null==t.end||i<=t.end))return!0}return!1}(this._mouseClientOffset.x||0,this._mouseClientOffset.y||0,o.x,o.y,this.options.scrollAngleRanges))return void(this._isScrolling=!0);var i,r,l,a;if(!this.monitor.isDragging()&&this._mouseClientOffset.hasOwnProperty("x")&&t&&(i=this._mouseClientOffset.x||0,r=this._mouseClientOffset.y||0,l=o.x,a=o.y,Math.sqrt(Math.pow(Math.abs(l-i),2)+Math.pow(Math.abs(a-r),2))>(this.options.touchSlop?this.options.touchSlop:0))&&(this.moveStartSourceIds=void 0,this.actions.beginDrag(t,{clientOffset:this._mouseClientOffset,getSourceClientOffset:this.getSourceClientOffset,publishSource:!1})),!this.monitor.isDragging())return;const d=this.sourceNodes.get(this.monitor.getSourceId());this.installSourceNodeRemovalObserver(d),this.actions.publishDragSource(),e.cancelable&&e.preventDefault();const c=(n||[]).map((e=>this.targetNodes.get(e))).filter((e=>!!e)),h=this.options.getDropTargetElementsAtPoint?this.options.getDropTargetElementsAtPoint(o.x,o.y,c):this.document.elementsFromPoint(o.x,o.y),m=[];for(const e in h){if(!h.hasOwnProperty(e))continue;let t=h[e];for(null!=t&&m.push(t);t;)t=t.parentElement,t&&-1===m.indexOf(t)&&m.push(t)}const p=m.filter((e=>c.indexOf(e)>-1)).map((e=>this._getDropTargetId(e))).filter((e=>!!e)).filter(((e,t,n)=>n.indexOf(e)===t));if(s)for(const e in this.targetNodes){const t=this.targetNodes.get(e);if(d&&t&&t.contains(d)&&-1===p.indexOf(e)){p.unshift(e);break}}p.reverse(),this.actions.hover(p,{clientOffset:o})},this._getDropTargetId=e=>{const t=this.targetNodes.keys();let n=t.next();for(;!1===n.done;){const s=n.value;if(e===this.targetNodes.get(s))return s;n=t.next()}},this.handleTopMoveEndCapture=e=>{this._isScrolling=!1,this.lastTargetTouchFallback=void 0,function(e){return void 0===e.buttons||!(e.buttons&r)}(e)&&(this.monitor.isDragging()&&!this.monitor.didDrop()?(e.cancelable&&e.preventDefault(),this._mouseClientOffset={},this.uninstallSourceNodeRemovalObserver(),this.actions.drop(),this.actions.endDrag()):this.moveStartSourceIds=void 0)},this.handleCancelOnEscape=e=>{"Escape"===e.key&&this.monitor.isDragging()&&(this._mouseClientOffset={},this.uninstallSourceNodeRemovalObserver(),this.actions.endDrag())},this.options=new i(n,t),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.sourceNodes=new Map,this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.targetNodes=new Map,this.listenerTypes=[],this._mouseClientOffset={},this._isScrolling=!1,this.options.enableMouseEvents&&this.listenerTypes.push(s.mouse),this.options.enableTouchEvents&&this.listenerTypes.push(s.touch),this.options.enableKeyboardEvents&&this.listenerTypes.push(s.keyboard)}}const p=function(e,t={},n={}){return new m(e,t,n)}},
365982:(e,t,n)=>{"use strict";n.d(t,{useDragLayer:()=>r});var s=n(50959),o=n(971690),i=n(868788);function r(e){const t=(0,i.useDragDropManager)().getMonitor(),[n,r]=(0,o.useCollector)(t,e);return(0,s.useEffect)((()=>t.subscribeToOffsetChange(r))),(0,s.useEffect)((()=>t.subscribeToStateChange(r))),n}}}]);