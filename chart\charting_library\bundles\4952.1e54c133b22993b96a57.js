"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4952],{584952:(t,e,r)=>{r.d(e,{FixedSizeList:()=>O,VariableSizeList:()=>z});var n=r(315882);function o(t,e){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},o(t,e)}function i(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e)}function a(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}var s=Number.isNaN||function(t){return"number"==typeof t&&t!=t};function l(t,e){if(t.length!==e.length)return!1;for(var r=0;r<t.length;r++)if(n=t[r],o=e[r],!(n===o||s(n)&&s(o)))return!1;var n,o;return!0}const c=function(t,e){var r;void 0===e&&(e=l);var n,o=[],i=!1;return function(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return i&&r===this&&e(a,o)||(n=t.apply(this,a),i=!0,r=this,o=a),n}};var u=r(50959),f="object"==typeof performance&&"function"==typeof performance.now?function(){return performance.now()}:function(){return Date.now()};function d(t){cancelAnimationFrame(t.id)}function h(t,e){var r=f();var n={id:requestAnimationFrame((function o(){f()-r>=e?t.call(null):n.id=requestAnimationFrame(o)}))};return n}var p=null;function m(t){if(void 0===t&&(t=!1),null===p||t){var e=document.createElement("div"),r=e.style;r.width="50px",r.height="50px",r.overflow="scroll",r.direction="rtl";var n=document.createElement("div"),o=n.style;return o.width="100px",o.height="100px",e.appendChild(n),document.body.appendChild(e),e.scrollLeft>0?p="positive-descending":(e.scrollLeft=1,p=0===e.scrollLeft?"negative":"positive-ascending"),document.body.removeChild(e),p}return p}var v=function(t,e){return t};function g(t){var e,r,o=t.getItemOffset,s=t.getEstimatedTotalSize,l=t.getItemSize,f=t.getOffsetForIndexAndAlignment,p=t.getStartIndexForOffset,g=t.getStopIndexForStartIndex,I=t.initInstanceProps,_=t.shouldResetStyleCacheOnItemSizeChange,y=t.validateProps;return r=e=function(t){function e(e){var r;return(r=t.call(this,e)||this)._instanceProps=I(r.props,a(a(r))),r._outerRef=void 0,r._resetIsScrollingTimeoutId=null,r.state={instance:a(a(r)),isScrolling:!1,scrollDirection:"forward",scrollOffset:"number"==typeof r.props.initialScrollOffset?r.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},r._callOnItemsRendered=void 0,r._callOnItemsRendered=c((function(t,e,n,o){return r.props.onItemsRendered({overscanStartIndex:t,overscanStopIndex:e,visibleStartIndex:n,visibleStopIndex:o})})),r._callOnScroll=void 0,r._callOnScroll=c((function(t,e,n){return r.props.onScroll({scrollDirection:t,scrollOffset:e,scrollUpdateWasRequested:n})})),r._getItemStyle=void 0,r._getItemStyle=function(t){var e,n=r.props,i=n.direction,a=n.itemSize,s=n.layout,c=r._getItemStyleCache(_&&a,_&&s,_&&i);if(c.hasOwnProperty(t))e=c[t];else{var u=o(r.props,t,r._instanceProps),f=l(r.props,t,r._instanceProps),d="horizontal"===i||"horizontal"===s,h="rtl"===i,p=d?u:0;c[t]=e={position:"absolute",left:h?void 0:p,right:h?p:void 0,top:d?0:u,height:d?"100%":f,
width:d?f:"100%"}}return e},r._getItemStyleCache=void 0,r._getItemStyleCache=c((function(t,e,r){return{}})),r._onScrollHorizontal=function(t){var e=t.currentTarget,n=e.clientWidth,o=e.scrollLeft,i=e.scrollWidth;r.setState((function(t){if(t.scrollOffset===o)return null;var e=r.props.direction,a=o;if("rtl"===e)switch(m()){case"negative":a=-o;break;case"positive-descending":a=i-n-o}return a=Math.max(0,Math.min(a,i-n)),{isScrolling:!0,scrollDirection:t.scrollOffset<o?"forward":"backward",scrollOffset:a,scrollUpdateWasRequested:!1}}),r._resetIsScrollingDebounced)},r._onScrollVertical=function(t){var e=t.currentTarget,n=e.clientHeight,o=e.scrollHeight,i=e.scrollTop;r.setState((function(t){if(t.scrollOffset===i)return null;var e=Math.max(0,Math.min(i,o-n));return{isScrolling:!0,scrollDirection:t.scrollOffset<e?"forward":"backward",scrollOffset:e,scrollUpdateWasRequested:!1}}),r._resetIsScrollingDebounced)},r._outerRefSetter=function(t){var e=r.props.outerRef;r._outerRef=t,"function"==typeof e?e(t):null!=e&&"object"==typeof e&&e.hasOwnProperty("current")&&(e.current=t)},r._resetIsScrollingDebounced=function(){null!==r._resetIsScrollingTimeoutId&&d(r._resetIsScrollingTimeoutId),r._resetIsScrollingTimeoutId=h(r._resetIsScrolling,150)},r._resetIsScrolling=function(){r._resetIsScrollingTimeoutId=null,r.setState({isScrolling:!1},(function(){r._getItemStyleCache(-1,null)}))},r}i(e,t),e.getDerivedStateFromProps=function(t,e){return S(t,e),y(t),null};var r=e.prototype;return r.scrollTo=function(t){t=Math.max(0,t),this.setState((function(e){return e.scrollOffset===t?null:{scrollDirection:e.scrollOffset<t?"forward":"backward",scrollOffset:t,scrollUpdateWasRequested:!0}}),this._resetIsScrollingDebounced)},r.scrollToItem=function(t,e){void 0===e&&(e="auto");var r=this.props.itemCount,n=this.state.scrollOffset;t=Math.max(0,Math.min(t,r-1)),this.scrollTo(f(this.props,t,e,n,this._instanceProps))},r.componentDidMount=function(){var t=this.props,e=t.direction,r=t.initialScrollOffset,n=t.layout;if("number"==typeof r&&null!=this._outerRef){var o=this._outerRef;"horizontal"===e||"horizontal"===n?o.scrollLeft=r:o.scrollTop=r}this._callPropsCallbacks()},r.componentDidUpdate=function(){var t=this.props,e=t.direction,r=t.layout,n=this.state,o=n.scrollOffset;if(n.scrollUpdateWasRequested&&null!=this._outerRef){var i=this._outerRef;if("horizontal"===e||"horizontal"===r)if("rtl"===e)switch(m()){case"negative":i.scrollLeft=-o;break;case"positive-ascending":i.scrollLeft=o;break;default:var a=i.clientWidth,s=i.scrollWidth;i.scrollLeft=s-a-o}else i.scrollLeft=o;else i.scrollTop=o}this._callPropsCallbacks()},r.componentWillUnmount=function(){null!==this._resetIsScrollingTimeoutId&&d(this._resetIsScrollingTimeoutId)},r.render=function(){
var t=this.props,e=t.children,r=t.className,o=t.direction,i=t.height,a=t.innerRef,l=t.innerElementType,c=t.innerTagName,f=t.itemCount,d=t.itemData,h=t.itemKey,p=void 0===h?v:h,m=t.layout,g=t.outerElementType,S=t.outerTagName,I=t.style,_=t.useIsScrolling,y=t.width,M=this.state.isScrolling,z="horizontal"===o||"horizontal"===m,O=z?this._onScrollHorizontal:this._onScrollVertical,x=this._getRangeToRender(),w=x[0],b=x[1],C=[];if(f>0)for(var R=w;R<=b;R++)C.push((0,u.createElement)(e,{data:d,key:p(R,d),index:R,isScrolling:_?M:void 0,style:this._getItemStyle(R)}));var T=s(this.props,this._instanceProps);return(0,u.createElement)(g||S||"div",{className:r,onScroll:O,ref:this._outerRefSetter,style:(0,n.default)({position:"relative",height:i,width:y,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:o},I)},(0,u.createElement)(l||c||"div",{children:C,ref:a,style:{height:z?"100%":T,pointerEvents:M?"none":void 0,width:z?T:"100%"}}))},r._callPropsCallbacks=function(){if("function"==typeof this.props.onItemsRendered&&this.props.itemCount>0){var t=this._getRangeToRender(),e=t[0],r=t[1],n=t[2],o=t[3];this._callOnItemsRendered(e,r,n,o)}if("function"==typeof this.props.onScroll){var i=this.state,a=i.scrollDirection,s=i.scrollOffset,l=i.scrollUpdateWasRequested;this._callOnScroll(a,s,l)}},r._getRangeToRender=function(){var t=this.props,e=t.itemCount,r=t.overscanCount,n=this.state,o=n.isScrolling,i=n.scrollDirection,a=n.scrollOffset;if(0===e)return[0,0,0,0];var s=p(this.props,a,this._instanceProps),l=g(this.props,s,a,this._instanceProps),c=o&&"backward"!==i?1:Math.max(1,r),u=o&&"forward"!==i?1:Math.max(1,r);return[Math.max(0,s-c),Math.max(0,Math.min(e-1,l+u)),s,l]},e}(u.PureComponent),e.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},r}var S=function(t,e){t.children,t.direction,t.height,t.layout,t.innerTagName,t.outerTagName,t.width,e.instance},I=function(t,e,r){var n=t.itemSize,o=r.itemMetadataMap,i=r.lastMeasuredIndex;if(e>i){var a=0;if(i>=0){var s=o[i];a=s.offset+s.size}for(var l=i+1;l<=e;l++){var c=n(l);o[l]={offset:a,size:c},a+=c}r.lastMeasuredIndex=e}return o[e]},_=function(t,e,r,n,o){for(;n<=r;){var i=n+Math.floor((r-n)/2),a=I(t,i,e).offset;if(a===o)return i;a<o?n=i+1:a>o&&(r=i-1)}return n>0?n-1:0},y=function(t,e,r,n){for(var o=t.itemCount,i=1;r<o&&I(t,r,e).offset<n;)r+=i,i*=2;return _(t,e,Math.min(r,o-1),Math.floor(r/2),n)},M=function(t,e){var r=t.itemCount,n=e.itemMetadataMap,o=e.estimatedItemSize,i=e.lastMeasuredIndex,a=0;if(i>=r&&(i=r-1),i>=0){var s=n[i];a=s.offset+s.size}return a+(r-i-1)*o},z=g({getItemOffset:function(t,e,r){return I(t,e,r).offset},getItemSize:function(t,e,r){return r.itemMetadataMap[e].size},getEstimatedTotalSize:M,getOffsetForIndexAndAlignment:function(t,e,r,n,o){var i=t.direction,a=t.height,s=t.layout,l=t.width,c="horizontal"===i||"horizontal"===s?l:a,u=I(t,e,o),f=M(t,o),d=Math.max(0,Math.min(f-c,u.offset)),h=Math.max(0,u.offset-c+u.size);switch("smart"===r&&(r=n>=h-c&&n<=d+c?"auto":"center"),r){case"start":return d;case"end":
return h;case"center":return Math.round(h+(d-h)/2);default:return n>=h&&n<=d?n:n<h?h:d}},getStartIndexForOffset:function(t,e,r){return function(t,e,r){var n=e.itemMetadataMap,o=e.lastMeasuredIndex;return(o>0?n[o].offset:0)>=r?_(t,e,o,0,r):y(t,e,Math.max(0,o),r)}(t,r,e)},getStopIndexForStartIndex:function(t,e,r,n){for(var o=t.direction,i=t.height,a=t.itemCount,s=t.layout,l=t.width,c="horizontal"===o||"horizontal"===s?l:i,u=I(t,e,n),f=r+c,d=u.offset+u.size,h=e;h<a-1&&d<f;)h++,d+=I(t,h,n).size;return h},initInstanceProps:function(t,e){var r={itemMetadataMap:{},estimatedItemSize:t.estimatedItemSize||50,lastMeasuredIndex:-1};return e.resetAfterIndex=function(t,n){void 0===n&&(n=!0),r.lastMeasuredIndex=Math.min(r.lastMeasuredIndex,t-1),e._getItemStyleCache(-1),n&&e.forceUpdate()},r},shouldResetStyleCacheOnItemSizeChange:!1,validateProps:function(t){t.itemSize}}),O=g({getItemOffset:function(t,e){return e*t.itemSize},getItemSize:function(t,e){return t.itemSize},getEstimatedTotalSize:function(t){var e=t.itemCount;return t.itemSize*e},getOffsetForIndexAndAlignment:function(t,e,r,n){var o=t.direction,i=t.height,a=t.itemCount,s=t.itemSize,l=t.layout,c=t.width,u="horizontal"===o||"horizontal"===l?c:i,f=Math.max(0,a*s-u),d=Math.min(f,e*s),h=Math.max(0,e*s-u+s);switch("smart"===r&&(r=n>=h-u&&n<=d+u?"auto":"center"),r){case"start":return d;case"end":return h;case"center":var p=Math.round(h+(d-h)/2);return p<Math.ceil(u/2)?0:p>f+Math.floor(u/2)?f:p;default:return n>=h&&n<=d?n:n<h?h:d}},getStartIndexForOffset:function(t,e){var r=t.itemCount,n=t.itemSize;return Math.max(0,Math.min(r-1,Math.floor(e/n)))},getStopIndexForStartIndex:function(t,e,r){var n=t.direction,o=t.height,i=t.itemCount,a=t.itemSize,s=t.layout,l=t.width,c=e*a,u="horizontal"===n||"horizontal"===s?l:o,f=Math.ceil((u+r-c)/a);return Math.max(0,Math.min(i-1,e+f-1))},initInstanceProps:function(t){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(t){t.itemSize}})},315882:(t,e,r)=>{function n(){return n=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},n.apply(this,arguments)}r.d(e,{default:()=>n})}}]);