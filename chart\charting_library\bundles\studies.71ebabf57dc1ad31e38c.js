(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[57539,43382],{963894:(e,t)=>{"use strict";t.CircularBuffer=void 0;var s=function(){function e(e){this._start=0,this._size=0,this._buffer=new Array(e)}return e.prototype.size=function(){return this._size},e.prototype.capacity=function(){return this._buffer.length},e.prototype.enqueue=function(e){this._size<this._buffer.length?this._size++:this._start=(this._start+1)%this._buffer.length;var t=(this._start+this._size-1)%this._buffer.length;this._buffer[t]=e},e.prototype.dequeue=function(){if(0===this._size)throw new Error("Buffer is empty");var e=this._buffer[this._start];return this._buffer[this._start]=void 0,this._start=(this._start+1)%this._buffer.length,this._size--,e},e.prototype.get=function(e){if(e>=this._size)throw new Error("Index is out of range");var t=(this._start+e)%this._buffer.length;return this._buffer[t]},e.prototype.forEach=function(e,t){for(var s=0;s<this._size;s++){var i=(this._start+s)%this._buffer.length;e.call(t,this._buffer[i],i,this)}},e.prototype.clear=function(){for(var e=this._buffer.length,t=0;t<e;t++)this._buffer[t]=void 0;this._start=0,this._size=0},e}();t.CircularBuffer=s},254526:(e,t,s)=>{"use strict";s.d(t,{SYMBOL_TYPE_BLACK_LIST:()=>i});const i=new Set(["spread","economic"])},163482:(e,t,s)=>{"use strict";s.r(t),s.d(t,{TECHNICALS_QS_FIELDS:()=>i,TECHNICALS_SCREENER_COLUMNS:()=>l,requiredMAColumns:()=>a,requiredOscColumns:()=>o,requiredPivotsColumns:()=>n,requiredRecommendColumns:()=>r});const i=["type","listed_exchange","pricescale","typespecs","pro_name","short_name"],r=["Recommend.Other","Recommend.All","Recommend.MA"],o=["RSI","Stoch.K","CCI20","ADX","AO","Mom","MACD.macd","Stoch.RSI.K","W.R","BBPower","UO"],a=["EMA10","SMA10","EMA20","SMA20","EMA30","SMA30","EMA50","SMA50","EMA100","SMA100","EMA200","SMA200","Ichimoku.BLine","VWMA","HullMA9"],n=["Pivot.M.Classic.R3","Pivot.M.Classic.R2","Pivot.M.Classic.R1","Pivot.M.Classic.Middle","Pivot.M.Classic.S1","Pivot.M.Classic.S2","Pivot.M.Classic.S3","Pivot.M.Fibonacci.R3","Pivot.M.Fibonacci.R2","Pivot.M.Fibonacci.R1","Pivot.M.Fibonacci.Middle","Pivot.M.Fibonacci.S1","Pivot.M.Fibonacci.S2","Pivot.M.Fibonacci.S3","Pivot.M.Camarilla.R3","Pivot.M.Camarilla.R2","Pivot.M.Camarilla.R1","Pivot.M.Camarilla.Middle","Pivot.M.Camarilla.S1","Pivot.M.Camarilla.S2","Pivot.M.Camarilla.S3","Pivot.M.Woodie.R3","Pivot.M.Woodie.R2","Pivot.M.Woodie.R1","Pivot.M.Woodie.Middle","Pivot.M.Woodie.S1","Pivot.M.Woodie.S2","Pivot.M.Woodie.S3","Pivot.M.Demark.R1","Pivot.M.Demark.Middle","Pivot.M.Demark.S1"],l=[...r,...o,...a,...n]},719885:(e,t,s)=>{"use strict";s.d(t,{TechnicalsDialogController:()=>h});var i=s(202373),r=s(584317),o=s(729030),a=s(254526),n=s(163482);let l;class h extends r.DialogRenderer{constructor(){super(...arguments),this._dialog=null,this._subscribe=e=>{this._setVisibility(e)}}async show(e){const t=e?.symbol?e.symbol:i.linking.proSymbol.value();if(e?.snapshot){const{snapshot:s}=e,i=await this._loadModule();this._getDialog(i,{...e,symbol:t,...s,exchange:s.listed_exchange
}).show()}else{const i=async()=>{const{snapshoter:e}=await s.e(51030).then(s.bind(s,795394));return e().getSnapshot(t,new Set(n.TECHNICALS_QS_FIELDS))},[r,o]=await Promise.all([this._loadModule(),i()]);if("error"===o.status)return void this._getDialog(r,{...e,symbol:t}).show();this._getDialog(r,{...e,symbol:t,...o.values,exchange:o.values.listed_exchange}).show()}}async canBeShown(e){const t=e||i.linking.proSymbol.value();if(!t)return!1;const r=new Set([...n.TECHNICALS_QS_FIELDS,"description"]),l=await(async()=>{const{snapshoter:e}=await s.e(51030).then(s.bind(s,795394));return e().getSnapshot(t,r)})();if("error"===l.status)return!1;const{values:{type:h,listed_exchange:u,description:d}}=l;if(!a.SYMBOL_TYPE_BLACK_LIST.has(h))try{const e=await(0,o.getSymbolData)(null,t,["Recommend.All"]);return Boolean(e&&0!==Object.keys(e).length&&d&&u&&h)}catch(e){return!1}return!1}hide(){this._dialog?.hide()}static getInstance(){return l||(l=new h),l}_getDialog(e,t){return this._dialog?.hide(),this._dialog?.visible().unsubscribe(this._subscribe),this._dialog=new e.TechnicalsDialogRenderer(t),this._dialog.visible().subscribe(this._subscribe),this._dialog}_loadModule(){return Promise.all([s.e(7056),s.e(98185),s.e(98933),s.e(39759),s.e(18883),s.e(32937),s.e(15371),s.e(46195),s.e(61681),s.e(63039),s.e(6612),s.e(50874),s.e(20563),s.e(38752),s.e(98158),s.e(34002),s.e(71450),s.e(97540),s.e(25563),s.e(72232),s.e(19365),s.e(59546),s.e(53147),s.e(47275),s.e(51513),s.e(34943),s.e(11058),s.e(23399),s.e(76848),s.e(45512),s.e(36463),s.e(21639),s.e(28828),s.e(90783),s.e(53987),s.e(32227),s.e(67912),s.e(97829),s.e(73306),s.e(71138)]).then(s.bind(s,948549))}}},202373:(e,t,s)=>{"use strict";s.d(t,{linking:()=>g});var i=s(290484),r=s(650151),o=s(820028),a=(s(213377),s(477786)),n=s(388741),l=s(68777),h=s(900210);var u=s(735566),d=s(62802),c=s(588948),_=s(516958),p=s(296538),y=s(912445);const m=(0,u.getLogger)("Linking");class S{constructor(e,t,s){this.watchedSymbols=new o.WatchedValue([]),this.watchedSymbol=new o.WatchedValue,this.seriesShortSymbol=new o.WatchedValue,this.proSymbol=new o.WatchedValue,this.ensuredProSymbol=new o.WatchedValue,this.watchedInterval=new o.WatchedValue,this.watchedIntraday=new o.WatchedValue,this.watchedSeconds=new o.WatchedValue,this.watchedTicks=new o.WatchedValue,this.watchedDataFrequencyResolution=new o.WatchedValue,this.watchedRange=new o.WatchedValue,this.watchedSupportedResolutions=new o.WatchedValue,this.watchedSupportedChartStyles=new o.WatchedValue,this.symbolNamesList=new o.WatchedValue,this._chartWidgetCollection=null,this._chartWidgetBindingState=0,this._activeChartWidget=null,this._linkingGroupCharts=null,this._boundChartWidget=null,this._watchedSymbolListenerBound=this._watchedSymbolListener.bind(this),this._watchedIntervalListenerBound=this._watchedIntervalListener.bind(this),this._setGroupSymbolCancellationToken={cancelled:!1},this._muted=!1,this.updateBoundChartWidget=()=>{const e=this._chartToBind();e!==this._boundChartWidget&&(null===e?this.unbindFromChartWidget():this.bindToChartWidget(e))},
this._updateAllGroupChartWidgets=()=>{this._destroySymbolIntervalPropertySubscriptions?.();const e=(0,r.ensureNotNull)(this._linkingGroupCharts).value(),t=[],s=[];for(const i of e){const e=i.symbolWV().spawn(),r=i.resolutionWV().spawn();e.subscribe(this._updateSymbolByProperty.bind(this,i)),r.subscribe(this._updateIntervalByProperty.bind(this,i)),t.push(e),s.push(r)}this._destroySymbolIntervalPropertySubscriptions=()=>{t.forEach((e=>e.destroy())),s.forEach((e=>e.destroy())),this._destroySymbolIntervalPropertySubscriptions=void 0};const i=this.watchedSymbol.value();e.length>1&&this._needApplySymbol(i)&&!this._muted&&this._setGroupSymbol(i);const o=this.watchedInterval.value();e.length>1&&this._needApplyInterval(o)&&!this._muted&&this._setGroupInterval(o),this.updateBoundChartWidget()},this._groupIndex=e;const i=(e,t)=>{e.subscribe((e=>{s()===this&&t.setValue(e)}),{callWithLast:!0})};i(this.watchedSymbols,t.watchedSymbols),i(this.watchedSymbol,t.watchedSymbol),i(this.seriesShortSymbol,t.seriesShortSymbol),i(this.proSymbol,t.proSymbol),i(this.ensuredProSymbol,t.ensuredProSymbol),i(this.watchedInterval,t.watchedInterval),i(this.watchedIntraday,t.watchedIntraday),i(this.watchedSeconds,t.watchedSeconds),i(this.watchedTicks,t.watchedTicks),i(this.watchedDataFrequencyResolution,t.watchedDataFrequencyResolution),i(this.watchedRange,t.watchedRange),i(this.watchedSupportedResolutions,t.watchedSupportedResolutions),i(this.watchedSupportedChartStyles,t.watchedSupportedChartStyles),i(this.symbolNamesList,t.symbolNamesList),this.watchedSymbol.subscribe((e=>{this._boundChartWidget&&this._boundChartWidget.hasModel()&&this.mainSeries().symbolSameAsCurrent(e)||this.proSymbol.setValue(e)}),{callWithLast:!0})}mute(e){this._muted=e}bindToChartWidgetCollection(e){this.unbindFromChartWidgetCollection(),this._chartWidgetCollection=e,this._activeChartWidget=e.activeChartWidget.spawn(),this._activeChartWidget.subscribe(this.updateBoundChartWidget),this._linkingGroupCharts=e.linkingGroupsCharts(this._groupIndex).spawn(),this._linkingGroupCharts.subscribe(this._updateAllGroupChartWidgets),this.updateBoundChartWidget(),this._updateAllGroupChartWidgets();const t=function(e){const t=(0,l.combine)((()=>e.getAll().filter((e=>e.isVisible()&&e.hasModel())).map((e=>(0,h.createWVFromGetterAndSubscription)((()=>e.model().mainSeries().symbol()),e.model().mainSeries().symbolResolved()).ownership()))),e.chartModels().weakReference(),e.layout.weakReference());return(0,l.accumulate)((e=>e),t.ownership())}(e);t.subscribe((e=>{this.watchedSymbols.setValue(e)})),this.watchedSymbols.setValue(t.value())}unbindFromChartWidgetCollection(){this.unbindFromChartWidget(),this._chartWidgetCollection=null,this._activeChartWidget?.destroy(),this._activeChartWidget=null,this._linkingGroupCharts?.destroy(),this._linkingGroupCharts=null,this._destroySymbolIntervalPropertySubscriptions?.()}bindToChartWidget(e){if(this.unbindFromChartWidget(),this._boundChartWidget=e,e.hasModel())return void this._onChartModelCreated(e.model())
;e.modelCreated().subscribe(this,this._onChartModelCreated,!0),this._chartWidgetBindingState=1;const t=e.mainSeriesProperties().childs();this.watchedSymbol.setValue(t.symbol.value()),this.watchedInterval.setValue(t.interval.value()),this._boundChartWidget.linkingGroupIndex().subscribe(this.updateBoundChartWidget)}unbindFromChartWidget(){const e=this._boundChartWidget;if(null!==e){switch(this._chartWidgetBindingState){case 1:e.modelCreated().unsubscribeAll(this);break;case 2:this.watchedSymbol.unsubscribe(this._watchedSymbolListenerBound);const t=this.mainSeries().dataEvents();t.symbolResolved().unsubscribeAll(this),t.symbolError().unsubscribeAll(this),this.mainSeries().onIntervalChanged().unsubscribeAll(this),this.watchedInterval.unsubscribe(this._watchedIntervalListenerBound),delete this.watchedSymbol.writeLock}e.linkingGroupIndex().unsubscribe(this.updateBoundChartWidget),this._boundChartWidget=null,this._chartWidgetBindingState=0}}boundChartWidget(){return this._boundChartWidget}mainSeries(){if(!this._boundChartWidget)throw new Error("ChartWidget is undefined");return this._boundChartWidget.model().mainSeries()}_watchedSymbolListener(e){this._needApplySymbol(e)&&!this._muted&&this._setGroupSymbol(e)}_updateSymbolByProperty(e,t){const s=this._symbolLock();s&&this._needApplySymbol(t)&&!this._muted&&this._setGroupSymbol(t),(s||e===this._boundChartWidget)&&this.watchedSymbol.setValue(t)}_watchedIntervalListener(e){const t=a.Interval.normalize(e);t&&this._needApplyInterval(t)&&!this._muted&&this._setGroupInterval(t)}_updateIntervalByProperty(e,t){const s=this._intervalLock(),i=a.Interval.normalize(t);s&&i&&this._needApplyInterval(i)&&!this._muted&&this._setGroupInterval(i),(s||e===this._boundChartWidget)&&this.watchedInterval.setValue(i??t)}_updateSeriesSymbolInfo(){this.seriesShortSymbol.setValue((0,r.ensureNotNull)(this._boundChartWidget).getSymbol(!0));const e=this.mainSeries().symbolInfo();if(e){const t=e.pro_name||!1;this.proSymbol.setValue(t||this.watchedSymbol.value()),this.ensuredProSymbol.setValue(this.proSymbol.value()),e.aliases&&this.symbolNamesList.setValue(e.aliases);let s=(0,p.allChartStyles)();((0,n.isCloseBasedSymbol)(e)||this.mainSeries().intervalObj().value().is1Tick())&&(s=s.filter((e=>(0,n.isSingleValueBasedStyle)(e)))),"hlc"===e.visible_plots_set&&(s=s.filter((e=>(0,n.isHLCBasedStyle)(e)||(0,n.isSingleValueBasedStyle)(e)))),this.watchedSupportedChartStyles.setValue(s),this.watchedIntraday.setValue(!!e.has_intraday),this.watchedSeconds.setValue(!!e.has_seconds),this.watchedTicks.setValue(!(0,n.isCloseBasedSymbol)(e)&&!!e["is-tickbars-available"]),this.watchedRange.setValue(!(0,n.isCloseBasedSymbol)(e));const i=e.data_frequency?e.data_frequency:void 0;this.watchedDataFrequencyResolution.setValue(i)}else this.watchedIntraday.setValue(!1),this.watchedSeconds.setValue(!1),this.watchedTicks.setValue(!1),this.watchedRange.setValue(!1),this.mainSeries().isFailed()&&this.ensuredProSymbol.setValue(this.proSymbol.value())}_onChartModelCreated(e){
if(!this._boundChartWidget)throw new Error("ChartWidget is undefined");this._chartWidgetBindingState=2,this.watchedSymbol.setValue(this._boundChartWidget.symbolWV().value()),this.watchedSymbol.subscribe(this._watchedSymbolListenerBound);const t=e.mainSeries().dataEvents();t.symbolResolved().subscribe(this,this._updateSeriesSymbolInfo),t.symbolError().subscribe(this,this._updateSeriesSymbolInfo),e.mainSeries().onIntervalChanged().subscribe(this,this._updateSeriesSymbolInfo),this._updateSeriesSymbolInfo(),this.watchedInterval.setValue(this._boundChartWidget.resolutionWV().value()),this.watchedInterval.subscribe(this._watchedIntervalListenerBound),this._boundChartWidget.readOnly()&&(this.watchedSymbol.writeLock=!0)}_chartToBind(){const e=this._chartWidgetCollection;return null===e?null:e.activeChartWidget.value().linkingGroupIndex().value()===this._groupIndex?e.activeChartWidget.value():null!==this._boundChartWidget&&this._boundChartWidget.linkingGroupIndex().value()===this._groupIndex?this._boundChartWidget:e.getAll().find((e=>e.linkingGroupIndex().value()===this._groupIndex))??null}_symbolLock(){return!!this._chartWidgetCollection?.lock.symbol.value()}_intervalLock(){return!!this._chartWidgetCollection?.lock.interval.value()}_chartsForLock(e){return(0===e?this._symbolLock():this._intervalLock())?(0,r.ensureNotNull)(this._linkingGroupCharts).value():this._boundChartWidget?[this._boundChartWidget]:[]}_setGroupSymbol(e){this.mute(!0),this._setGroupSymbolCancellationToken.cancelled=!0;const t=this._setGroupSymbolCancellationToken={cancelled:!1};(0,r.ensureNotNull)(this._chartWidgetCollection).setSymbol(e,this._groupIndex,(0,r.ensureNotNull)(this._boundChartWidget)).finally((()=>{if(this._boundChartWidget&&!t.cancelled){const t=this._boundChartWidget.model().mainSeries();t.symbolSameAsCurrent(e)||(this.watchedSymbol.setValue(t.symbol()),this.proSymbol.setValue(t.proSymbol()))}})),this.mute(!1)}_needApplySymbol(e){const t=this._chartsForLock(0),s=t.find((t=>t.hasModel()&&t.model().mainSeries().symbolInfo()&&t.model().mainSeries().symbolSameAsResolved(e)));if(s){const e=s.model().mainSeries();if(t.every((t=>e.symbolSameAsResolved(t.symbolWV().value()))))return!1}return t.some((t=>t.symbolWV().value()!==e))}_setGroupInterval(e){this.mute(!0),(0,r.ensureNotNull)(this._chartWidgetCollection).setResolution(e,this._groupIndex).finally((()=>{if(this._boundChartWidget){const t=this._boundChartWidget.resolutionWV().value();a.Interval.isEqual(t,e)||this.watchedInterval.setValue(t)}})),this.mute(!1)}_needApplyInterval(e){return this._chartsForLock(1).some((t=>!a.Interval.isEqual(t.resolutionWV().value(),e)))}}const g=new class{constructor(){this._watchedSymbols=new o.WatchedValue([]),this._watchedSymbol=new o.WatchedValue,this._seriesShortSymbol=new o.WatchedValue,this._proSymbol=new o.WatchedValue,this._ensuredProSymbol=new o.WatchedValue,this._watchedInterval=new o.WatchedValue,this._watchedIntraday=new o.WatchedValue,this._watchedSeconds=new o.WatchedValue,this._watchedTicks=new o.WatchedValue,
this._watchedDataFrequencyResolution=new o.WatchedValue,this._watchedRange=new o.WatchedValue,this._watchedSupportedResolutions=new o.WatchedValue,this._watchedSupportedChartStyles=new o.WatchedValue([]),this._symbolNamesList=new o.WatchedValue,this._chartWidgetCollection=null,this._onSymbolLinkBound=this._onSymbolLink.bind(this),this._searchCharts=null,this._searchChartsLoadDebounced=null,this._selfEmit=!1,this._preventFeedBySymbol=!1,this._feedBySymbolDebounceCounter=0,this._linkingGroups=new Map,this._activeLinkingGroup=new o.WatchedValue,this._activeLinkingGroupIndex=null,this._updateLinkingGroups=()=>{(0,r.ensureNotNull)(this._chartWidgetCollection).allLinkingGroups().value().forEach((e=>this._linkingGroup(e))),this._linkingGroups.forEach((e=>e.updateBoundChartWidget()))},this._activeLinkingGroup.setValue(this._linkingGroup(null));const e=(e,t)=>{e.subscribe((e=>t().setValue(e)),{callWithLast:!0})};e(this._watchedSymbols,(()=>this._activeLinkingGroup.value().watchedSymbols)),e(this._watchedSymbol,(()=>this._activeLinkingGroup.value().watchedSymbol)),e(this._seriesShortSymbol,(()=>this._activeLinkingGroup.value().seriesShortSymbol)),e(this._proSymbol,(()=>this._activeLinkingGroup.value().proSymbol)),e(this._ensuredProSymbol,(()=>this._activeLinkingGroup.value().ensuredProSymbol)),e(this._watchedInterval,(()=>this._activeLinkingGroup.value().watchedInterval)),e(this._watchedIntraday,(()=>this._activeLinkingGroup.value().watchedIntraday)),e(this._watchedSeconds,(()=>this._activeLinkingGroup.value().watchedSeconds)),e(this._watchedTicks,(()=>this._activeLinkingGroup.value().watchedTicks)),e(this._watchedDataFrequencyResolution,(()=>this._activeLinkingGroup.value().watchedDataFrequencyResolution)),e(this._watchedRange,(()=>this._activeLinkingGroup.value().watchedRange)),e(this._watchedSupportedResolutions,(()=>this._activeLinkingGroup.value().watchedSupportedResolutions)),e(this._watchedSupportedChartStyles,(()=>this._activeLinkingGroup.value().watchedSupportedChartStyles)),e(this._symbolNamesList,(()=>this._activeLinkingGroup.value().symbolNamesList));const t=e=>{this._watchedSymbol.setValue(e.watchedSymbol.value()),this._seriesShortSymbol.setValue(e.seriesShortSymbol.value()),this._proSymbol.setValue(e.proSymbol.value()),this._ensuredProSymbol.setValue(e.ensuredProSymbol.value()),this._watchedInterval.setValue(e.watchedInterval.value()),this._watchedIntraday.setValue(e.watchedIntraday.value()),this._watchedSeconds.setValue(e.watchedSeconds.value()),this._watchedTicks.setValue(e.watchedTicks.value()),this._watchedDataFrequencyResolution.setValue(e.watchedDataFrequencyResolution.value()),this._watchedRange.setValue(e.watchedRange.value()),this._watchedSupportedResolutions.setValue(e.watchedSupportedResolutions.value()),this._watchedSupportedChartStyles.setValue(e.watchedSupportedChartStyles.value()),this._symbolNamesList.setValue(e.symbolNamesList.value())};this._activeLinkingGroup.subscribe(t),t(this._activeLinkingGroup.value()),(0,y.setMuteLinkingGroup)(((e,t)=>{this._linkingGroup(e).mute(t)}))}get symbols(){
return this._watchedSymbols}get symbol(){return this._watchedSymbol}get proSymbol(){return this._proSymbol.readonly()}get ensuredProSymbol(){return this._ensuredProSymbol.readonly()}get symbolNamesList(){return this._symbolNamesList.readonly()}get seriesShortSymbol(){return this._seriesShortSymbol.readonly()}get interval(){return this._watchedInterval}get intraday(){return this._watchedIntraday.readonly()}get seconds(){return this._watchedSeconds.readonly()}get ticks(){return this._watchedTicks.readonly()}get range(){return this._watchedRange.readonly()}get supportedResolutions(){return this._watchedSupportedResolutions.readonly()}get supportedChartStyles(){return this._watchedSupportedChartStyles.readonly()}get preventFeedBySymbol(){return this._preventFeedBySymbol}get dataFrequencyResolution(){return this._watchedDataFrequencyResolution.readonly()}activeLinkingGroup(){return this._activeLinkingGroup.readonly()}getChartWidget(){return this.activeLinkingGroup().value().boundChartWidget()}bindToChartWidgetCollection(e){this._chartWidgetCollection?.onAboutToBeDestroyed.unsubscribeAll(this),this._unbindFromChartWidgetCollection(),this._chartWidgetCollection=e,this._chartWidgetCollection.onAboutToBeDestroyed.subscribe(this,this._unbindFromChartWidgetCollection),this._chartWidgetCollection.allLinkingGroups().subscribe(this._updateLinkingGroups),this._updateLinkingGroups(),this._activeLinkingGroupIndex=e.activeLinkingGroup().spawn(),this._activeLinkingGroupIndex.subscribe((e=>{this._activeLinkingGroup.setValue(this._linkingGroup(e))}),{callWithLast:!0}),this._linkingGroups.forEach((t=>t.bindToChartWidgetCollection(e)))}bindToSearchCharts(e){this.unbindFromSearchCharts(),this._searchCharts=e,e.onSearchBySymbol.subscribe(this,this._onSearchBySymbol),e.loadingSymbol.subscribe((e=>{!1===e&&(this._feedBySymbolDebounceCounter=0)})),this._watchedSymbol.subscribe(this._onSymbolLinkBound)}unbindFromSearchCharts(){this._searchCharts&&(this._searchCharts.onSearchBySymbol.unsubscribe(this,this._onSearchBySymbol),this._watchedSymbol.unsubscribe(this._onSymbolLinkBound),this._searchCharts=null)}setPreventFeedBySymbol(e){this._preventFeedBySymbol=e}setSymbolAndLogInitiator(e,t,s,i=!1){(this.symbol.value()!==e||s)&&(m.logInfo(`Change linking symbol to ${e}, initiator: ${t}`),this.symbol.setValue(e,s),i||null===this._chartWidgetCollection||(0,_.trackChangeSymbol)(this.activeLinkingGroup().value().mainSeries(),this._chartWidgetCollection.layout.value(),this._chartWidgetCollection.metaInfo.uid.value()||"",t))}_onSearchBySymbol(e){if(!e.resolved_symbol)throw new Error("no resolved_symbol");this._selfEmit=!0,this._watchedSymbol.setValue(e.resolved_symbol),this._selfEmit=!1}_onSymbolLink(e){if(!this._selfEmit){if(!this._searchCharts){const e="No search charts defined";throw m.logError(e),new Error(e)}this._preventFeedBySymbol||this._loadSearchCharts(e)}}_loadSearchCharts(e){if(!this._searchCharts){const e="No search charts defined";throw m.logError(e),new Error(e)}
if(this._searchChartsLoadDebounced)return void(this._feedBySymbolDebounceCounter<100&&(this._feedBySymbolDebounceCounter++,this._searchChartsLoadDebounced(e)));const t=e=>this._searchCharts?(!0===this._searchCharts.loadingSymbol.value()?this._feedBySymbolDebounceCounter<100&&(this._feedBySymbolDebounceCounter++,this._searchChartsLoadDebounced=(0,i.default)(t,2e3),this._searchChartsLoadDebounced(e)):this._searchChartsLoadDebounced=null,this._searchCharts.feedBySymbol.call(this._searchCharts,e)):()=>{};!0===this._searchCharts.loadingSymbol.value()?this._feedBySymbolDebounceCounter<100&&(this._feedBySymbolDebounceCounter++,this._searchChartsLoadDebounced=(0,i.default)(t,2e3),this._searchChartsLoadDebounced(e)):this._searchCharts.feedBySymbol(e)}_linkingGroup(e){let t=this._linkingGroups.get(e);if(void 0===t){const s={watchedSymbols:this._watchedSymbols,watchedSymbol:this._watchedSymbol,seriesShortSymbol:this._seriesShortSymbol,proSymbol:this._proSymbol,ensuredProSymbol:this._ensuredProSymbol,watchedInterval:this._watchedInterval,watchedIntraday:this._watchedIntraday,watchedSeconds:this._watchedSeconds,watchedTicks:this._watchedTicks,watchedDataFrequencyResolution:this._watchedDataFrequencyResolution,watchedRange:this._watchedRange,watchedSupportedResolutions:this._watchedSupportedResolutions,watchedSupportedChartStyles:this._watchedSupportedChartStyles,symbolNamesList:this._symbolNamesList};t=new S(e,s,(()=>this._activeLinkingGroup.value())),this._linkingGroups.set(e,t),this._chartWidgetCollection&&t.bindToChartWidgetCollection(this._chartWidgetCollection)}return t}_unbindFromChartWidgetCollection(){null!==this._chartWidgetCollection&&(this._activeLinkingGroupIndex?.destroy(),this._activeLinkingGroupIndex=null,this._linkingGroups.forEach((e=>e.unbindFromChartWidgetCollection())),this._chartWidgetCollection.allLinkingGroups().unsubscribe(this._updateLinkingGroups),this._chartWidgetCollection=null)}};{const e=(0,c.getFreshInitData)().symbolInfo,t=e?e.pro_symbol:d.getValue("editchart.model.symbol",window.DEFAULT_SYMBOL);g.setSymbolAndLogInitiator(t,"initial data",!1,!0)}window.TradingViewApi||(window.TradingViewApi={linking:g})},912445:(e,t,s)=>{"use strict";let i;function r(e,t){i?.(e,t)}function o(e){i=e}s.d(t,{muteLinkingGroup:()=>r,setMuteLinkingGroup:()=>o})},516958:(e,t,s)=>{"use strict";s.d(t,{trackChangeSymbol:()=>a});var i=s(17957),r=s(388130),o=s(776734);function a(e,t,s,a){window.user.do_not_track||e.symbolResolvingActive().subscribe((n=>{if(n)return;const l=e.symbolInfo();l&&(0,o.getTracker)().then((e=>{null!==e&&e.trackChartSymbols({symbolName:l.pro_name,symbolType:l.type,layoutType:t,chartId:s,openSource:a,platform:(0,i.getPlatform)(),isExpert:window.user.declared_status===r.UserStatus.Pro})}))}),{once:!0})}},988534:(e,t,s)=>{"use strict";s.d(t,{FinancialsDialogController:()=>o});var i=s(584317);let r;class o extends i.DialogRenderer{constructor(){super(...arguments),this._dialog=null,this._subscribe=e=>{this._setVisibility(e)}}show(e){this._load(e).then((e=>e.show()))}hide(){this._dialog?.hide()}
static getInstance(){return r||(r=new o),r}_load(e){return Promise.all([s.e(56662),s.e(92537),s.e(98185),s.e(98933),s.e(39759),s.e(18883),s.e(52186),s.e(85902),s.e(90579),s.e(7291),s.e(32937),s.e(15371),s.e(46195),s.e(61681),s.e(63039),s.e(6612),s.e(11069),s.e(50874),s.e(67780),s.e(20563),s.e(65743),s.e(61171),s.e(19190),s.e(87833),s.e(67691),s.e(14391),s.e(38452),s.e(84587),s.e(38752),s.e(98158),s.e(40887),s.e(92990),s.e(89126),s.e(82870),s.e(34002),s.e(10299),s.e(98681),s.e(71450),s.e(97540),s.e(25563),s.e(37242),s.e(72232),s.e(53271),s.e(71803),s.e(82279),s.e(19365),s.e(66113),s.e(14100),s.e(53147),s.e(47275),s.e(84030),s.e(93015),s.e(13485),s.e(69965),s.e(51513),s.e(80735),s.e(34943),s.e(44535),s.e(42486),s.e(59878),s.e(99796),s.e(58875),s.e(96908),s.e(37463),s.e(15169),s.e(79487),s.e(51979),s.e(65786),s.e(15377),s.e(93031),s.e(16570),s.e(11058),s.e(28158),s.e(37519),s.e(41652),s.e(36463),s.e(21639),s.e(36434),s.e(6507),s.e(11998),s.e(27624),s.e(98193),s.e(36053),s.e(71702),s.e(79700),s.e(99735),s.e(32227),s.e(2920),s.e(70169),s.e(21501),s.e(76285),s.e(33191),s.e(16803),s.e(67912),s.e(51225),s.e(89175),s.e(83351),s.e(47140),s.e(54587),s.e(24878)]).then(s.bind(s,571592)).then((t=>(this._dialog?.hide(),this._dialog?.visible().unsubscribe(this._subscribe),this._dialog=new t.FinancialsDialogRenderer(e),this._dialog.visible().subscribe(this._subscribe),this._dialog)))}}},316478:(e,t,s)=>{"use strict";s.d(t,{ForecastDialogController:()=>a});var i=s(584317),r=s(202373);let o;class a extends i.DialogRenderer{constructor(){super(...arguments),this._dialog=null,this._subscribe=e=>{this._setVisibility(e)}}show(e){this._load(e).then((e=>e.show()))}hide(){this._dialog?.hide()}async canBeShown(e){const t=e||r.linking.proSymbol.value(),i=new Set(["recommendation_mark","pro_name"]),o=await(async()=>{const{snapshoter:e}=await s.e(62495).then(s.bind(s,960669));return e().getSnapshot(t,i)})();return!("error"===o.status||!o.values.recommendation_mark)}static getInstance(){return o||(o=new a),o}_load(e){return Promise.all([s.e(84239),s.e(3788),s.e(98185),s.e(98933),s.e(39759),s.e(18883),s.e(32937),s.e(15371),s.e(46195),s.e(61681),s.e(63039),s.e(6612),s.e(50874),s.e(20563),s.e(61171),s.e(19190),s.e(67691),s.e(38752),s.e(98158),s.e(40887),s.e(89126),s.e(34002),s.e(10299),s.e(98681),s.e(71450),s.e(97540),s.e(25563),s.e(37242),s.e(72232),s.e(19365),s.e(59546),s.e(53147),s.e(47275),s.e(93015),s.e(3579),s.e(51513),s.e(80735),s.e(34943),s.e(58875),s.e(65786),s.e(15377),s.e(11058),s.e(23399),s.e(76848),s.e(28158),s.e(37519),s.e(80259),s.e(41652),s.e(18182),s.e(36463),s.e(21639),s.e(1169),s.e(50290),s.e(4122),s.e(5700),s.e(32227),s.e(2920),s.e(21501),s.e(76285),s.e(33191),s.e(16803),s.e(67912),s.e(25484),s.e(74127),s.e(85080)]).then(s.bind(s,231078)).then((t=>(this._dialog?.hide(),this._dialog?.visible().unsubscribe(this._subscribe),this._dialog=new t.ForecastDialogRenderer(e),this._dialog.visible().subscribe(this._subscribe),this._dialog)))}}},224743:(e,t,s)=>{"use strict";s.d(t,{globalCloseDelegate:()=>i,globalCloseMenu:()=>r})
;const i=new(s(547465).Delegate);function r(){i.fire()}},386288:e=>{e.exports={subtitle:"subtitle-cEWFvaCX",text:"text-cEWFvaCX",group:"group-cEWFvaCX",groupIcon:"groupIcon-cEWFvaCX",beforeMarketOpen:"beforeMarketOpen-cEWFvaCX",afterMarketClose:"afterMarketClose-cEWFvaCX",groupTitle:"groupTitle-cEWFvaCX",groupRow:"groupRow-cEWFvaCX",groupCell:"groupCell-cEWFvaCX"}},207815:(e,t,s)=>{"use strict";s.r(t),s.d(t,{QUOTE_FIELDS:()=>o,QUOTE_FIELDS_CACHE:()=>a,QuoteCache:()=>r});var i=s(650151);class r{constructor(e){this._cache=new Map,this._fields=[...e.fields]}update(e,t,s){const r=(0,i.ensureDefined)(e.symbolname);if(this._cache.has(r)||this._cache.set(r,{symbolname:r,status:e.status,values:{}}),"error"===e.status)return;const o=(0,i.ensureDefined)(this._cache.get(r));o.status=e.status;for(const i of this._fields)t.has(i)&&(s||void 0!==e.values[i])&&(o.values[i]=e.values[i])}get(e){return this._cache.get(e)??null}fields(){return this._fields}}
const o=new Set(["pro_name","base_name","logoid","currency-logoid","base-currency-logoid","source-logoid","short_name","web_site_url","pro_perm","timezone","current_session","last_price","lp_time","prev_close_price","open_price","high_price","low_price","price_52_week_high","price_52_week_low","ask","ask_size","bid","bid_size","rch","rchp","rtc","rtc_time","data_frequency","reference-last-period-start","business_description","web_site_url","figi","number_of_employees","float_shares_outstanding","earnings_release_next_calendar_date","root","description","exchange","listed_exchange","type","country_code","provider_id","sector","typespecs","visible-plots-set","industry","currency_id","last_price","fractional","minmov","minmove2","pricescale","variable_tick_size","change","change_percent","volume","average_volume","market_cap_basic","market_cap_calc","total_revenue","earnings_per_share_basic_ttm","price_earnings_ttm","beta_1_year","dps_common_stock_prim_issue_fy","dividends_yield","earnings_release_next_date","earnings_per_share_forecast_next_fq","earnings_publication_type_next_fq","earnings_release_date","earnings_per_share_fq","earnings_per_share_forecast_fq","forecast_raw","last_release_date","next_release_date","reference_last_period","fundamental_currency_code","number_of_employees","web_site_url","business_description","founded","ceo","float_shares_outstanding","total_shares_outstanding","dividend_payout_ratio_ttm","dividends_yield_current","dividend_ex_date_upcoming","dividend_amount_upcoming","dividend_amount_recent","dividend_ex_date_recent","dividend_amount_h","dividend_payment_date_upcoming","dividend_payment_date_recent","total_revenue_fq_h","total_revenue_fy_h","net_income_fy_h","net_income_fq_h","total_assets_fy_h","total_assets_fq_h","total_liabilities_fy_h","total_liabilities_fq_h","cash_f_operating_activities_fy_h","cash_f_operating_activities_fq_h","cash_f_investing_activities_fy_h","cash_f_investing_activities_fq_h","cash_f_financing_activities_fy_h","cash_f_financing_activities_fq_h","fiscal_period_fy_h","fiscal_period_fq_h","fiscal_period_fh_h","earnings_release_date_fq_h","earnings_release_next_date_fq","earnings_per_share_forecast_next_fq","earnings_per_share_forecast_fq_h","earnings_per_share_fq_h","earnings_fiscal_period_fq_h","next_earnings_fiscal_period_fq","is_next_earnings_release_date_estimated","symbol-primaryname","currency_code","rates_mc","rates_fy","rates_ttm","measure","value_unit_id","value-unit-id","update_mode","language","local_description","short_description","source","source2","format","recommendation_mark","last_report_frequency","price_target_estimates_num","price_target_average","update_mode_seconds","recommendation_total","recommendation_buy","recommendation_over","recommendation_hold","recommendation_under","recommendation_sell","recommendation_total","price_target_high","price_target_low","rates_pt","rates_pt","total_revenue_fy_h","total_revenue_fq_h","total_revenue_fh_h","net_income_fy_h","net_income_fq_h","net_income_fh_h","total_assets_fy_h","total_assets_fq_h","total_assets_fh_h","total_liabilities_fy_h","total_liabilities_fq_h","total_liabilities_fh_h","cash_f_operating_activities_fy_h","cash_f_operating_activities_fq_h","cash_f_operating_activities_fh_h","cash_f_investing_activities_fy_h","cash_f_investing_activities_fq_h","cash_f_investing_activities_fh_h","cash_f_financing_activities_fy_h","cash_f_financing_activities_fq_h","cash_f_financing_activities_fh_h","fiscal_period_fy","fiscal_period_fq","fiscal_period_fh","earnings_release_date_fq_h","earnings_release_date_fy_h","earnings_release_date_fh_h","earnings_release_next_date_fq","earnings_release_next_date_fy","earnings_release_next_date_fh","earnings_release_next_time","earnings_release_time","is_next_earnings_release_date_estimated","earnings_per_share_forecast_next_fq","earnings_per_share_forecast_next_fy","earnings_per_share_forecast_next_fh","earnings_per_share_forecast_fq_h","earnings_per_share_forecast_fy_h","earnings_per_share_forecast_fh_h","earnings_per_share_fq_h","earnings_per_share_fy_h","earnings_per_share_fh_h","earnings_fiscal_period_fq_h","earnings_fiscal_period_fy_h","earnings_fiscal_period_fh_h","next_earnings_fiscal_period_fq","next_earnings_fiscal_period_fy","next_earnings_fiscal_period_fh","revenue_fq_h","revenue_fy_h","revenue_fh_h","revenue_forecast_fq_h","revenue_forecast_fy_h","revenue_forecast_fh_h","revenue_forecast_next_fq","revenue_forecast_next_fy","revenue_forecast_next_fh","revenue_seg_by_business_h","revenue_seg_by_region_h","total_revenue_fy","total_revenue_fq","total_revenue_fh","gross_profit_fy","gross_profit_fq","gross_profit_fh","ebitda_fy","ebit_fy","net_income_fy","net_income_fq","net_income_fh","total_debt_fy_h","total_debt_fq_h","total_debt_fh_h","free_cash_flow_fy_h","free_cash_flow_fq_h","free_cash_flow_fh_h","cash_n_equivalents_fy_h","cash_n_equivalents_fq_h","cash_n_equivalents_fh_h","total_current_assets_fy","total_current_assets_fq","total_current_assets_fh","total_current_liabilities_fy","total_current_liabilities_fq","total_current_liabilities_fh","total_non_current_assets_fy","total_non_current_assets_fq","total_non_current_assets_fh","total_non_current_liabilities_fy","total_non_current_liabilities_fq","total_non_current_liabilities_fh","loans_net_fy","loans_net_fy_h","loans_net_fq_h","loans_net_fh_h","total_deposits_fy","total_deposits_fy_h","total_deposits_fq_h","total_deposits_fh_h","loan_loss_allowances_fy","loan_loss_allowances_fy_h","loan_loss_allowances_fq_h","loan_loss_allowances_fh_h","reserve_to_total_capital_fy_h","reserve_to_total_capital_fq_h","reserve_to_total_capital_fh_h","unearned_premium_to_total_capital_fy_h","unearned_premium_to_total_capital_fq_h","unearned_premium_to_total_capital_fh_h","insurance_reserves_fy_h","insurance_reserves_fq_h","insurance_reserves_fh_h","policy_claims_fy_h","policy_claims_fq_h","policy_claims_fh_h","premiums_earned_fy_h","premiums_earned_fq_h","premiums_earned_fh_h","price_earnings_fq_h","price_earnings_fy_h","price_earnings_fh_h","price_sales_fq_h","price_sales_fy_h","price_sales_fh_h","diluted_net_income_ttm","total_revenue_ttm","price_earnings_current","price_sales_current","isin-displayed","interest_income_fy_h","interest_income_fq_h","interest_income_fh_h","non_interest_income_fy_h","non_interest_income_fq_h","non_interest_income_fh_h","website","doc","explorer","sources","contracts","crypto_common_categories","crypto_asset","community","dividends_availability","earnings_availability","financials_availability","etf_asset_type_exposure","etf_region_exposure","top_holdings","unit-id","options-info","interest_income_fy","interest_income_fq","interest_income_fh","non_interest_income_fy","non_interest_income_fq","non_interest_income_fh","interest_expense_fy","interest_expense_fq","interest_expense_fh","loan_loss_provision_fy","loan_loss_provision_fq","loan_loss_provision_fh","non_interest_expense_fy","non_interest_expense_fq","non_interest_expense_fh","non_oper_income_fy","non_oper_income_fq","non_oper_income_fh","unusual_expense_inc_fy","unusual_expense_inc_fq","unusual_expense_inc_fh","pretax_income_fy","pretax_income_fq","pretax_income_fh","income_tax_fy","income_tax_fq","income_tax_fh","after_tax_other_income_fy","after_tax_other_income_fq","after_tax_other_income_fh","total_non_oper_income_fy","total_non_oper_income_fq","total_non_oper_income_fh","oper_income_fy","oper_income_fq","oper_income_fh","operating_expenses_fy","operating_expenses_fq","operating_expenses_fh","cost_of_goods_fy","cost_of_goods_fq","cost_of_goods_fh","equity_in_earnings_fy","equity_in_earnings_fq","equity_in_earnings_fh","minority_interest_exp_fy","minority_interest_exp_fq","minority_interest_exp_fh","discontinued_operations_fy","discontinued_operations_fq","discontinued_operations_fh","front_contract","pointvalue","unit_id","expiration","aum","asset_class","focus","expense_ratio","launch_date","issuer","brand","homepage","index_tracked","actively_managed","fund_view_mode","common_equity_tier1_ratio_fy_h","common_equity_tier1_ratio_fq_h","common_equity_tier1_ratio_fh_h","tier1_capital_ratio_fy_h","tier1_capital_ratio_fq_h","tier1_capital_ratio_fh_h","total_capital_ratio_fy_h","total_capital_ratio_fq_h","total_capital_ratio_fh_h","preferred_stock_carrying_value_fh","preferred_stock_carrying_value_fq","total_debt_fq","minority_interest_fh","minority_interest_fq","cash_n_short_term_invest_fq","cash_n_due_f_banks_fh","cash_n_due_f_banks_fq","enterprise_value_current","etf_holdings_count","contract-description","reference-last-period","all_time_high","all_time_high_day","all_time_low","all_time_low_day","outstanding_amount","nominal_value","denom_min","current_coupon","coupon_type_general","coupon_frequency","yield_to_maturity","maturity-date","days_to_maturity","bond_issuer","issue_date","bond_issuer_stock_symbol","total_issued_amount","paid_amount","bond_snp_rating_lt_h","placement_type","duration_type","maturity_type","offer_type","redemption_type","conversion_option","sinking_fund","ownership_form","daily-summary-ast","issue_status","coupon_h","sinking_fund_next_date","sinking_fund_min_amount_next","call_next_date","redemptions_h","call_notice_days","put_next_date","put_notice_days_min","seniority_level","inflation_protection","pledge_status","bond_issuer_country_of_risk","bond_issuer_cr_parent","credit_enhancement_type","credit_enhancement_status","use_of_proceeds","bond_issuer_snp_rating_lt_h","bond_issuer_snp_rating_st_h","bond_agents_tr","first_bar_time_1d","financial-indicator-id","exchange-info","underlying-symbol","strike","lotsize","option-style","has_bonds","open_time","has_bonds","dex_buys_15m","dex_sells_15m","dex_buy_volume_15m","dex_sell_volume_15m","dex_buyers_15m","dex_sellers_15m","dex_buys_1h","dex_sells_1h","dex_buy_volume_1h","dex_sell_volume_1h","dex_buyers_1h","dex_sellers_1h","dex_buys_4h","dex_sells_4h","dex_buy_volume_4h","dex_sell_volume_4h","dex_buyers_4h","dex_sellers_4h","dex_buys_12h","dex_sells_12h","dex_buy_volume_12h","dex_sell_volume_12h","dex_buyers_12h","dex_sellers_12h","dex_buys_24h","dex_sells_24h","dex_buy_volume_24h","dex_sell_volume_24h","dex_buyers_24h","dex_sellers_24h","dex_trading_volume_24h","dex_created_time","dex_currency","dex_currency_logoid","blockchain_addresses_urls","blockchain_addresses","base_currency"]),a=new r({
fields:o})},235267:(e,t,s)=>{"use strict";s.d(t,{QuoteSession:()=>r});var i=s(623213);s(638456);class r{constructor(e,t=(0,i.randomHash)()){this._sessionstarted=!1,this._globalHandler=null,this._chartApi=e,this._sessionid="qs_"+t}destroy(){this._sessionstarted&&(this._chartApi.quoteDeleteSession(this._sessionid),this._sessionstarted=!1)}connected(){return this._chartApi.connected()}connect(e){this._globalHandler=e,this._chartApi.createSession(this._sessionid,this),this._chartApi.connect()}disconnect(){this._chartApi.disconnect()}quoteAddSymbols(e){this._chartApi.quoteAddSymbols(this._sessionid,e)}quoteRemoveSymbols(e){this._chartApi.quoteRemoveSymbols(this._sessionid,e)}quoteFastSymbols(e){this._chartApi.quoteFastSymbols(this._sessionid,e)}quoteSetFields(e){this._chartApi.quoteSetFields(this._sessionid,e)}onMessage(e){switch(e.method){case"connected":this._sessionstarted||(this._chartApi.quoteCreateSession(this._sessionid),this._sessionstarted=!0);break;case"disconnected":this._sessionstarted=!1}this._globalHandler?.(e)}quoteHibernateAll(){this._chartApi.quoteHibernateAll(this._sessionid)}}window.TradingView=window.TradingView||{},window.TradingView.QuoteSession=r},778003:(e,t,s)=>{"use strict";function i(e){if(void 0===e)return null;const t=e.match(/(delayed_streaming)_(\d+)/);return null===t?null:{mode:t[1],interval:parseInt(t[2])}}function r(e){const t=i(e.update_mode);return null===t||(e.update_mode=t.mode,e.update_mode_seconds=t.interval),e}s.r(t),s.d(t,{normalizeUpdateMode:()=>r,parseUpdateMode:()=>i})},311943:(e,t,s)=>{"use strict";s.d(t,{LollipopGroupIcons:()=>i,LollipopTooltipMainCommonRow:()=>h});var i,r=s(50959),o=s(497754),a=s.n(o),n=s(79205),l=s(386288);function h(e){const{name:t,value:s,style:i,valueStyle:o,onValueClick:h,valueRightIcon:u,className:d}=e;return r.createElement("div",{className:a()(l.groupRow,d),style:i},t&&r.createElement("div",{className:l.groupCell},r.createElement("span",{className:l.text},t)),r.createElement("div",{className:l.groupCell},r.createElement("span",{className:l.text,style:o,onClick:h},s),u&&r.createElement(n.Icon,{icon:u.iconContent,className:a()(l.groupIcon,u.iconClass,"apply-common-tooltip"),title:u.tooltipText})))}!function(e){e[e.BeforeMarketOpen=l.beforeMarketOpen]="BeforeMarketOpen",e[e.AfterMarketClose=l.afterMarketClose]="AfterMarketClose"}(i||(i={}))},296538:(e,t,s)=>{"use strict";s.d(t,{allChartStyles:()=>o});var i=s(213377);const r=(0,s(638456).onWidget)();function o(){return function(){const e=[0,1,9,13,2,14,15,3,16,10];return e.push(12),r||e.push(17),r||e.push(18),r||e.push(20),r||e.push(19),e}().concat(function(){const e=[8];return i.enabled("japanese_chart_styles")&&(e.push(4,7,5,6),e.push(11)),e}())}},412338:(e,t,s)=>{"use strict";s.d(t,{getVolumeUnit:()=>r});var i=s(444372);function r(e){switch(e.volume_type){case"base":return e.base_currency||void 0;case"quote":return e.currency||void 0;case"tick":return i.t(null,void 0,s(824821))}}},194275:(e,t,s)=>{"use strict";s.d(t,{roundToThirdDigit:()=>r});var i=s(808708);function r(e,t){
const s=new i.NumericFormatter({precision:3}),r=new i.NumericFormatter({precision:2}),o=Math.abs(Math.round(1e3*e)/1e3),a=o.toFixed(3).split(".")[1];return`${e<0?"−":""}${(t||"0"!==a[a.length-1]?s:r).format(o)}`}},744380:(e,t,s)=>{"use strict";s.d(t,{IntervalsVisibilitiesProperty:()=>o});var i=s(905520),r=s(816541);class o extends i.Property{state(e,t){return(0,r.nonDefaultIntervalsVisibilities)(super.state(e,t))??void 0}storeStateIfUndefined(){return!1}}},320994:(e,t,s)=>{"use strict";s.d(t,{BitmapCoordinatesPaneRenderer:()=>r});var i=s(327714);class r{draw(e,t){new i.CanvasRenderingTarget2D(e,t.mediaSize,t.bitmapSize).useBitmapCoordinateSpace((e=>this._drawImpl(e)))}drawBackground(e,t){new i.CanvasRenderingTarget2D(e,t.mediaSize,t.bitmapSize).useBitmapCoordinateSpace((e=>this._drawBackgroundImpl(e)))}_drawBackgroundImpl(e){}}},708162:(e,t,s)=>{"use strict";s.d(t,{CompositeRenderer:()=>i});class i{constructor(){this._renderers=[],this._globalAlpha=1}setGlobalAlpha(e){this._globalAlpha=e}append(e){e&&this._renderers.push(e)}insert(e,t){this._renderers.splice(t,0,e)}clear(){this._renderers.length=0}isEmpty(){return 0===this._renderers.length}draw(e,t){for(let s=0;s<this._renderers.length;s++)e.save(),e.globalAlpha=this._globalAlpha,this._renderers[s].draw(e,t),e.restore()}drawBackground(e,t){e.save(),e.globalAlpha=this._globalAlpha;for(let s=0;s<this._renderers.length;s++){const i=this._renderers[s];i.drawBackground&&i.drawBackground(e,t)}e.restore()}hitTest(e,t){let s=null;for(let i=this._renderers.length-1;i>=0;i--){const r=this._renderers[i].hitTest(e,t);null!==r&&(null===s||r.target()>s.target())&&(s=r)}return s}doesIntersectWithBox(e,t){return this._renderers.some((s=>!!s.doesIntersectWithBox&&s.doesIntersectWithBox(e,t)))}}},820807:(e,t,s)=>{"use strict";s.d(t,{MediaCoordinatesPaneRenderer:()=>r});var i=s(327714);class r{draw(e,t){new i.CanvasRenderingTarget2D(e,t.mediaSize,t.bitmapSize).useMediaCoordinateSpace((e=>this._drawImpl(e)))}drawBackground(e,t){new i.CanvasRenderingTarget2D(e,t.mediaSize,t.bitmapSize).useMediaCoordinateSpace((e=>this._drawBackgroundImpl(e)))}_drawBackgroundImpl(e){}}},839800:(e,t,s)=>{"use strict";s.d(t,{sortSources:()=>r,sortSourcesPreOrdered:()=>i});const i={KeyFactsToday:10000001,LatestUpdates:10000002,BarMarks:10000003,TimeScaleMarks:10000004,ChartEventsSource:10000005,Dividends:10000006,Splits:10000007,Earnings:10000008,RollDates:10000009,FutureContractExpiration:10000010,LineToolOrder:10000011,LineToolPosition:10000012,LineToolExecution:10000013,AlertLabelInactive:10000014,AlertLabel:10000015};function r(e,t){if(0===e.length)return[];if(!t)return[...e].sort(((e,t)=>e.zorder()-t.zorder()));if(!(t.model().mainPane()===t))return[...e].sort(((e,t)=>e.zorder()-t.zorder()));const s=t.model().panes(),i=s.indexOf(t),r=e=>s.findIndex((t=>t.hasDataSource(e)));return e.map((e=>{const s=!t.hasDataSource(e);return{source:e,isMultipane:s,aboveSeries:e.zorder()>t.model().mainSeries().zorder(),paneIndex:s?r(e):i}
})).sort(((e,t)=>e.isMultipane||t.isMultipane?e.isMultipane&&!t.isMultipane?t.aboveSeries?-1:1:!e.isMultipane&&t.isMultipane?e.aboveSeries?1:-1:e.paneIndex-t.paneIndex:e.source.zorder()-t.source.zorder())).map((e=>e.source))}},436966:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Sessions:()=>_});var i=s(650151),r=s(807958),o=s(901466),a=s(196302),n=s(220129),l=s(840514),h=s(213377),u=s(437642),d=s(87347);const c=new n.PriceFormatter;class _ extends l.CustomSourceBase{constructor(e,t,s,i){super(e,t),this._studySource=null,this._paneViews=[],this._metaInfo=null,this._destroyed=!1,this._isStarted=!1,this._loadedGraphics=null,this._doubleClickHandler=i,this._properties=s,this._properties.subscribe(this,this._onPropertiesChanged),this._requestAndProcessMetaInfo(),t.timeScale().onReset().subscribe(this,this._clearData),t.timeScale().logicalRangeChanged().subscribe(this,this.updateAllViews.bind(this,(0,u.viewportChangeEvent)())),t.mainSeries().properties().childs().interval.subscribe(this,this._processHibernate)}start(){this._isStarted=!0,this._processHibernate()}restart(){this._clearData(),h.enabled("stop_study_on_restart")&&this.stop(),this.start()}isStarted(){return this._isStarted}stop(){this._isStarted=!1,null!==this._studySource&&this._studySource.stop()}isHoveredEnabled(){return!1}paneViews(e){return this._paneViews}updateAllViews(e){this._paneViews.forEach((t=>t.update(e)))}updateViewsForPane(e,t){this.updateAllViews(t)}destroy(){this._destroyed=!0,null!==this._studySource&&(this._studySource.dataCleared().unsubscribeAll(this),this._studySource.dataUpdated().unsubscribeAll(this),this._studySource.destroy(),this._studySource=null),this._model.timeScale().logicalRangeChanged().unsubscribeAll(this),this._model.timeScale().onReset().unsubscribeAll(this),this._model.mainSeries().properties().childs().interval.unsubscribeAll(this),this._properties.unsubscribeAll(this)}series(){return this._model.mainSeries()}priceScale(){return this.series().priceScale()}graphics(){return this._loadedGraphics||(0,i.ensureNotNull)(this._studySource).graphics()}valueAt(e,t){return null}properties(){return this._properties}graphicsInfo(){return(0,i.ensureNotNull)(this._metaInfo).graphics}firstValue(e){return this._model.mainSeries().firstValue()}formatter(){return c}stateData(){return null!==this._metaInfo?{graphics:(0,a.saveStudyGraphics)(this.graphics(),this._model.timeScale().visibleBarsStrictRange()),metaInfo:this._metaInfo.state()}:null}restoreStateData(e){void 0!==e&&(this._loadStudyGraphics(e.graphics),this._setMetaInfo(new r.StudyMetaInfo(e.metaInfo)),this._createPaneViews())}metaInfo(){return(0,i.ensureNotNull)(this._metaInfo)}async _requestAndProcessMetaInfo(){if(this._model.isSnapshot())return;const e=await(0,d.studyMetaInfoRepository)().findById({type:"java",studyId:"Sessions@tv-basicstudies"});this._destroyed||null===this._loadedGraphics&&(this._setMetaInfo(e),null!==this._metaInfo&&(this._studySource=new o.StudyDataSource(this._model.chartApi(),this._model.mainSeries().seriesSource(),"sessions_",this._metaInfo),this._createPaneViews(),
this._studySource.dataCleared().subscribe(this,this.updateAllViews.bind(this,(0,u.sourceChangeEvent)(this.id()))),this._studySource.dataUpdated().subscribe(this,this.updateAllViews.bind(this,(0,u.sourceChangeEvent)(this.id()))),this._studySource.setInputs({}),this._processHibernate()))}_loadStudyGraphics(e){const t=e.backgrounds;if(void 0!==t){const e=t.findIndex((e=>"inSession"===e.styleId));-1!==e&&t.splice(e,1)}this._loadedGraphics=(0,a.loadStudyGraphics)(e)}_setMetaInfo(e){const t=e.graphics.backgrounds;void 0!==t&&void 0!==t.inSession&&delete t.inSession,this._metaInfo=e}_clearData(){null!==this._studySource&&this._studySource.clearData()}_createPaneViews(){const e={doubleClickHandler:this._doubleClickHandler};(0,a.createGraphicsPaneViews)(this,this._model,e).then((e=>{this._paneViews=e.regularPaneViews,this._model.lightUpdate()}))}_onPropertiesChanged(){this._processHibernate(),this.updateAllViews((0,u.sourceChangeEvent)(this.id()))}_processHibernate(){if(null!==this._studySource){const e=this._canBeHibernated(),t=this._isHibernated(),s=this._studySource.isStarted();!t&&e&&s?this._studySource.stop():!t||e||s||this._studySource.start()}}_canBeHibernated(){if(this._model.mainSeries().isDWM())return!0;const e=this._properties.childs().sessionHighlight.childs(),{preMarket:t,postMarket:s,electronic:i,outOfSession:r}=e.backgrounds.childs(),{sessBreaks:o}=e.vertlines.childs(),a=t.childs().available.value()&&t.childs().visible.value()||s.childs().available.value()&&s.childs().visible.value()||i.childs().available.value()&&i.childs().visible.value()||r.childs().available.value()&&r.childs().visible.value(),n=o.childs().available.value()&&o.childs().visible.value();return!a&&!n}_isHibernated(){return this._isStarted&&(null===this._studySource||!this._studySource.isStarted())}}},37241:(e,t,s)=>{"use strict";s.d(t,{prepareStudyProperties:()=>w,prepareStudyPropertiesForLoadChart:()=>m});var i=s(998034),r=s(130551),o=s(735566),a=s(536794),n=s(807958),l=s(257145),h=s(85858),u=s(192809),d=s(266954),c=s(312692),_=s(744380),p=s(974145);const y=(0,o.getLogger)("Chart.Study");function m(e,t,s,r,o,l){return function(e,t,s,r,o,l,u){const d=function(e,t,s,r,o){e.version&&s.version&&e.version!==s.version&&y.logWarn("Serialized metaInfo version "+e.version+" is not equal to the saved state version "+s.version);const l=t||e,u=(0,a.clone)(l.defaults)??{},d=n.StudyMetaInfo.getStudyPropertyRootName(l),c=n.StudyMetaInfo.getStudyPropertyRootName(e);let _=g();(0,i.default)(_,S(e)),(0,i.default)(_,(0,a.clone)(e.defaults)),(0,i.default)(_,u),(0,i.default)(_,(0,h.factoryDefaults)(d)),(0,i.default)(_,(0,h.factoryDefaults)(c)),(0,i.default)(_,v(l,r,d)),(0,i.default)(_,v(e,r,c)),(0,i.default)(_,s),_=r.updateStudyState(_,e,t),void 0!==o&&t&&(_=o(s,_,e,t));n.StudyMetaInfo.versionOf(l)>=1&&(0,i.default)(_,f(u,_));return _}(e,t,s,r,l);return x(t||e,o,d,u,!0)}(e,t,s,r,n.StudyMetaInfo.getStudyPropertyRootName(e),o,l)}function S(e){const t={};if(e.plots)for(let s=0;s<e.plots.length;s++){const i=e.plots[s],r=i.id;if((0,p.isColorerPlot)(i))continue
;const o={display:15,color:"#0496FF",linestyle:l.LINESTYLE_SOLID,linewidth:2,plottype:p.LineStudyPlotStyle.Line,trackPrice:!1};(0,p.isBarColorerPlot)(i)&&(o.transparency=0),o.plottype=i.type,o.title=r,t[r]=o}return{styles:t}}function g(){const e=(0,a.clone)((0,h.defaults)("study"));return e.intervalsVisibilities=(0,a.clone)(d.intervalsVisibilitiesDefaults),e}function v(e,t,s){let i=(0,a.clone)((0,h.defaults)(s,t));return"Overlay"!==e.shortId&&"Compare"!==e.shortId||(i.currencyId=null,i.unitId=null),e.isTVScript&&e.TVScriptSourceCode!==i.TVScriptSourceCode&&(i=(0,a.clone)((0,h.factoryDefaults)(s))),i}function f(e,t){const s={};return u.StudyVersioning.mergeInputsObjPart(s,e.inputs??{}),u.StudyVersioning.mergeInputsObjPart(s,t.inputs),{inputs:s}}function b(e,t,s,o){if(n.StudyMetaInfo.versionOf(e)<1)throw new Error("This function cannot work with metainfo of the old format version. Required format version >= 1");const l=n.StudyMetaInfo.getStudyPropertyRootName(e),u=(0,a.clone)(e.defaults),d=(0,h.factoryDefaults)(l),c=g();if((0,i.default)(c,S(e)),(0,i.default)(c,u),(0,i.default)(c,d),(0,i.default)(c,v(e,o,l)),(0,i.default)(c,t),(0,i.default)(c,f(u,c)),null!==s){const t=s.model().studiesColorRotatorFactory().getColorRotator(e);null!==t&&("Overlay@tv-basicstudies"===e.id?c.lineStyle.color=t.getColor(c.lineStyle.color,d.lineStyle.color===c.lineStyle.color):(0,i.default)(c,function(e,t){for(const s of Object.keys(e.styles)){const i=e.styles[s];if((0,r.isObject)(i)&&"color"in i){const e=i.color;i.color=t.getColor(e)}}return e}(c,t)))}return o.updateStudyInputsIfNeeded(c,c.version??e.version,e),c}function w(e,t,s,i,r){return function(e,t,s,i,r,o){const l=b(e,t,s,i),h=n.StudyMetaInfo.getSourceInputIds(e);return h.forEach(((e,t)=>{const s=l.inputs[e];t<o.length?l.inputs[e]=`${o[t].id()}$0`:(0,a.isString)(s)&&s.includes("$")&&(l.inputs[e]="close")})),x(e,r,l)}(e,t,s,i,n.StudyMetaInfo.getStudyPropertyRootName(e),r)}const C=["id","description","description_localized","shortDescription","_metainfoVersion","is_price_study","is_hidden_study","priceScale","fullId","shortId","scriptIdPart","packageId","productId","isTVScriptStub","defaults","symbolSource","historyCalculationMayChange","format","linkedToSeries","isTVLibrary","docs","exports","exportTypes","extra","usesPrivateLib","financialPeriod","groupingKey","pine","isRGB","isTVScript","TVScriptMetaInfoExprs","usePlotsZOrder","isTVScriptStrategy","TVScriptSourceCode","lookaheadFutureData","hasAlertFunction","defaultStrategyAlertMessage","tags","canBeChild","canNotBeChild","_serverMetaInfoVersion","warnings"];function x(e,t,s,i,r){for(const e of C)delete s[e];const o=["visible","precision","minTick","intervalsVisibilities","inputs.first_visible_bar_time","inputs.last_visible_bar_time","inputs.subscribeRealtime","patchMetaInfoDefaults"];for(let t=0;t<e.inputs.length;++t){const s=e.inputs[t];s.isHidden&&(o.push(`inputs.${t}`),o.push(`inputs.${s.id}`))}const a=new c.DefaultProperty({defaultName:t,state:s,excludedDefaultsKeys:o,excludedStateKeys:["version"],theme:i})
;a.removeProperty("intervalsVisibilities"),a.addChild("intervalsVisibilities",new _.IntervalsVisibilitiesProperty(s&&s.intervalsVisibilities)),"PivotPointsStandard@tv-basicstudies"!==e.id&&"PivotPointsHighLow@tv-basicstudies"!==e.id||!a.hasChild("font")||a.removeProperty("font");const l=n.StudyMetaInfo.versionOf(e);return a.hasChild("version")?a.childs().version?.setValue(l):a.addProperty("version",l),e.TVScriptMetaInfoExprs&&!r&&a.addProperty("patchMetaInfoDefaults",!0),a}},859849:(e,t,s)=>{"use strict";s.r(t),s.d(t,{study_ESD$TV_DIVIDENDS:()=>ge,study_ESD$TV_EARNINGS:()=>fe,study_ESD$TV_SPLITS:()=>ve});var i=s(262609),r=s.n(i),o=s(650151),a=s(790188),n=s(444372),l=s(969680),h=s(586563),u=s(536794),d=s(534328),c=s(837462),_=s(951827),p=s(910282),y=s(311943),m=s(731974),S=s(257145),g=s(398453),v=s(338242);const f={fillPath:new Path2D("M8.961.92a3 3 0 0 1 3.078 0l7.5 4.48A3 3 0 0 1 21 7.975V20a3 3 0 0 1-3 3H3a3 3 0 0 1-3-3V7.975A3 3 0 0 1 1.461 5.4l7.5-4.48z"),strokePath:new Path2D("M9.867 2.742c.39-.23.875-.23 1.266 0l7.5 4.406c.382.225.617.635.617 1.078V20c0 .69-.56 1.25-1.25 1.25H3c-.69 0-1.25-.56-1.25-1.25V8.226c0-.443.235-.853.617-1.078l7.5-4.406z")},b={fillPath:new Path2D("M8.961 23.08a3 3 0 0 0 3.078 0l7.5-4.48A3 3 0 0 0 21 16.025V4a3 3 0 0 0-3-3H3a3 3 0 0 0-3 3v12.025A3 3 0 0 0 1.461 18.6l7.5 4.48z"),strokePath:new Path2D("M9.866 21.257c.391.23.877.23 1.268 0l7.5-4.414a1.25 1.25 0 0 0 .616-1.078V4c0-.69-.56-1.25-1.25-1.25H3c-.69 0-1.25.56-1.25 1.25v11.765c0 .443.234.853.616 1.078l7.5 4.414z")},w={fillPath:new Path2D("M3 0h15c1.662 0 3 1.338 3 3v15c0 1.662-1.338 3-3 3H3c-1.662 0-3-1.338-3-3V3c0-1.662 1.338-3 3-3z"),strokePath:new Path2D("M3 1.75h15c.693 0 1.25.557 1.25 1.25v15c0 .693-.557 1.25-1.25 1.25H3c-.692 0-1.25-.558-1.25-1.25V3c0-.692.558-1.25 1.25-1.25z")};function C(e,t,s,i,r){const{horizontalPixelRatio:o,verticalPixelRatio:a}=s;e.save(),e.translate(t.x-i.lollipop.width*o/2,t.y-i.lollipop.height*a/2),e.scale(o,a),i.lollipop.fillCircle&&i.lollipop.backgroundColor&&(e.fillStyle=i.lollipop.backgroundColor,e.fill(r.fillPath)),e.strokeStyle=i.lollipop.strokeStyle,e.lineWidth=Math.round(i.lollipop.lineWidth*o)/o,(0,v.setLineStyle)(e,S.LINESTYLE_SOLID),i.lollipop.fillCircle&&i.lollipop.fillStyle&&(e.fillStyle=i.lollipop.fillStyle,e.fill(r.strokePath)),e.stroke(r.strokePath),e.restore()}class x extends g.LollipopRenderer{_drawLollipop(e,t,s){const i=this._data.style;switch(i.type){case"Positive":!function(e,t,s,i){C(e,t,s,i,f)}(e,t,s,i);break;case"Negative":!function(e,t,s,i){C(e,t,s,i,b)}(e,t,s,i);break;default:!function(e,t,s,i){C(e,t,s,i,w)}(e,t,s,i)}}}var P=s(194275),I=s(328786),D=s(472134),V=s(183800),T=s(226235),k=s(682345),L=s(149204),R=s(85290),A=s(315507)
;const M=n.t(null,void 0,s(363872)),B=n.t(null,void 0,s(844543)),E=n.t(null,void 0,s(964747)),N=n.t(null,void 0,s(680110)),W=n.t(null,void 0,s(980767)),F=n.t(null,void 0,s(571858)),O=n.t(null,void 0,s(588488)),H=n.t(null,void 0,s(137260)),q=n.t(null,void 0,s(316183)),G=n.t(null,void 0,s(679505)),z=n.t(null,void 0,s(722677)),U=n.t(null,void 0,s(613551)),Y=n.t(null,void 0,s(609254)),$=n.t(null,void 0,s(772453)),j=n.t(null,void 0,s(278031)),Q=n.t(null,void 0,s(552274)),K=n.t(null,void 0,s(14834)),X={dark:(0,a.getHexColorByName)("color-cold-gray-900"),light:(0,a.getHexColorByName)("color-white")},Z={positive:(0,a.getHexColorByName)("color-minty-green-600"),negative:(0,a.getHexColorByName)("color-ripe-red-600"),future:(0,a.getHexColorByName)("color-grapes-purple-a400"),unknown:(0,a.getHexColorByName)("color-cold-gray-350")},J={positive:(0,a.getHexColorByName)("color-minty-green-500"),negative:(0,a.getHexColorByName)("color-ripe-red-500"),future:(0,a.getHexColorByName)("color-grapes-purple-a200"),unknown:(0,a.getHexColorByName)("color-cold-gray-700")};function ee(e){return e?Z:J}const te=(0,_.getPercentageFormatter)(),se=(0,_.getVolumeFormatter)();class ie extends I.LollipopStudyPaneViewBase{}class re extends ie{constructor(e,t){super(e,t)}styles(e){return e?{positive:(0,a.getHexColorByName)("color-minty-green-600"),negative:(0,a.getHexColorByName)("color-ripe-red-600"),future:(0,a.getHexColorByName)("color-grapes-purple-a400"),unknown:(0,a.getHexColorByName)("color-cold-gray-350")}:{positive:(0,a.getHexColorByName)("color-minty-green-500"),negative:(0,a.getHexColorByName)("color-ripe-red-500"),future:(0,a.getHexColorByName)("color-grapes-purple-a200"),unknown:(0,a.getHexColorByName)("color-cold-gray-700")}}extraData(e){const t=e[8],s=14===t,i=e[1],r=e[2],o=e[3],a=e[4],n=s?null:e[5],l=null!=n&&null!=r?n-r:null,h=e[6],u=s?null:e[7];let d;return null!=t?t<20?d="Future":null!==l&&(d=l<0?"Negative":"Positive"):null!==l?d=l<0?"Negative":"Positive":a>Date.now()&&(d="Future"),{standardized:i,reported:n,estimate:r,revenueValue:u,revenueEstimate:h,period:o,date:a,type:d,timeType:t??void 0}}afterUpdate(e,t,s,i){const r=this._source.getEstimate();if(r){const o=r.value.slice();o[4]*=1e3,this.addLollipop(e,t,s,i,r.index,o)}}getStyle(e){const t=this._getLollipopStatus(e);if(void 0===e.type)return super.getStyle(e,t);const s=t;let i=s;e.stack&&(i+="_stack:"+e.stack);let r=this._getStyleObject(e.type,i);if(null!==r)return r;if(this._stylesCache||(this._stylesCache={}),!this._stylesCache[i]){r=this._getStyleObject(e.type,s);const t=(0,u.clone)(r);t&&(e.stack&&(t.lollipop.incHeight=25*e.stack),this._stylesCache[i]=t)}return this._stylesCache[i]}_createTooltipContent(e){const t=e.standardized,s=null!=t,r=e.reported,a=null!=r,n=e.estimate,h=null!=n,u=a&&h?r-n:null;let d=e.revenueEstimate,c=!1;null!=d&&(c=!0,d*=1e6);let _=e.revenueValue,p=!1;null!=_&&(p=!0,_*=1e6);const m=p&&c?_-d:null,S=ee(this._model.dark().value());let g,v,f,b,w;switch(e.type){case"Future":v=S.future,g=T;break;case"Negative":v=S.negative,g=V;break;case"Positive":
v=S.positive,g=D;break;default:v=S.unknown,g=T}switch(e.timeType){case 12:case 22:f=A,b=y.LollipopGroupIcons.AfterMarketClose,w=K;break;case 11:case 21:f=R,b=y.LollipopGroupIcons.BeforeMarketOpen,w=Q}const C=10===e.timeType||14===e.timeType,x={name:z,value:this._formatDate(e.date,C)};f&&(x.valueRightIcon={iconContent:f,iconClass:b,tooltipText:w});const I=[x];e.period&&I.push({name:Y,value:i.unix(e.period).format("MMM 'YY")});const k={title:p||c?M:B,icon:g,style:{color:v},anchor:this._getMoreFinancialsLink(),main:{type:"common",title:I,content:[]}},L={title:B,content:[]};if(s&&L.content.push({name:E,value:(0,P.roundToThirdDigit)(t)}),a&&L.content.push({name:N,value:(0,P.roundToThirdDigit)(r)}),h&&L.content.push({name:W,value:(0,P.roundToThirdDigit)(n)}),null!==u){const e=100*Math.abs(u/n);let t=(0,P.roundToThirdDigit)(u);isFinite(e)&&(t+=" ("+te.format(e)+")"),L.content.push({name:F,value:(0,l.forceLTRStr)(t),style:{fontWeight:"bold",color:v}})}if((0,o.ensureDefined)(k.main.content).push(L),p||c){const e={title:j,content:[]};if(p&&e.content.push({name:N,value:se.format(_)}),c&&e.content.push({name:W,value:se.format(d)}),null!==m){const t=100*Math.abs(m/d);let s=se.format(m);isFinite(t)&&(s+=" ("+te.format(t)+")"),e.content.push({name:F,value:(0,l.forceLTRStr)(s),style:{fontWeight:"bold",color:m>=0?S.positive:S.negative}})}(0,o.ensureDefined)(k.main.content).push(e)}return[k]}_showBarLine(e){return super._showBarLine(e)||this._model.mainSeries().properties().childs().esdShowBreaks.value()}_createRendererForLollipop(e,t){return new x(e,new m.HitTestResult(m.HitTarget.Custom,t),this._textWidthCache)}_recreateStyles(e){const t=e.dark().value(),s=ee(t),i=s.unknown,r={barLine:{lineStyle:S.LINESTYLE_DASHED,lineWidth:1,strokeStyle:i},lollipop:{width:21,height:21,bottom:2,lineWidth:1.5,importance:"earnings",strokeStyle:i,backgroundColor:t?X.dark:X.light,fillCircle:!0,text:{label:"E",strokeStyle:i,font:(0,c.makeFont)(12,p.CHART_FONT_FAMILY,"bold")}}},o={lollipop:{fillStyle:(0,d.generateColor)(i,85)}},n={lollipop:{fillStyle:i,text:{strokeStyle:(0,a.getHexColorByName)("color-cold-gray-50")}}};this._defaultStyle=(0,h.deepCopy)(r),this._hoveredStyle=(0,u.merge)((0,h.deepCopy)(r),o),this._activeStyle=(0,u.merge)((0,h.deepCopy)(r),n);const l=(e,t)=>{const s=(0,h.deepCopy)(r);return s.barLine.strokeStyle=e,s.lollipop.strokeStyle=e,s.lollipop.text.strokeStyle=e,s.type=t,o.lollipop.fillStyle=(0,d.generateColor)(e,85),n.lollipop.fillStyle=e,{default:s,hovered:(0,u.merge)((0,h.deepCopy)(s),o),active:(0,u.merge)((0,h.deepCopy)(s),n)}};n.lollipop.text.strokeStyle=(0,a.getHexColorByName)("color-grapes-purple-50");const _=l(s.future,"Future");r.lollipop.height=23,r.lollipop.text.deltaY=1,n.lollipop.text.strokeStyle=(0,a.getHexColorByName)("color-minty-green-50");const y=l(s.positive,"Positive");r.lollipop.bottom=.5,r.lollipop.text.deltaY=-.5,n.lollipop.text.strokeStyle=(0,a.getHexColorByName)("color-ripe-red-50");const m=l(s.negative,"Negative");this._earningTypeStyles={Future:_,Positive:y,Negative:m},this._stylesCache=null}
_getStyleObject(e,t){const s=this._earningTypeStyles[e];switch(t){case"default":return s.default;case"hovered":return s.hovered;case"active":return s.active}return null}}const oe=(0,a.getHexColorByName)("color-tan-orange-600"),ae=(0,a.getHexColorByName)("color-tan-orange-500");function ne(e){return e?oe:ae}class le extends ie{extraData(e){const t=e[1];return{numerator:e[2],denominator:t,date:e[3]}}_createTooltipContent(e){let t=O;return(0,u.isNumber)(e.numerator)&&(0,u.isNumber)(e.denominator)&&(t+=": "+e.numerator+"/"+e.denominator),[{title:t,icon:k,style:{color:ne(this._model.dark().value())},anchor:this._getMoreFinancialsLink(),main:{type:"common",title:this._formatDate(e.date)}}]}_recreateStyles(e){const t=e.dark().value(),s=ne(t),i={barLine:{lineStyle:S.LINESTYLE_DASHED,lineWidth:1,strokeStyle:s},lollipop:{width:23,height:23,bottom:1,lineWidth:1.5,importance:"splits",strokeStyle:s,backgroundColor:t?X.dark:X.light,fillCircle:!0,text:{label:"S",strokeStyle:s,font:(0,c.makeFont)(12,p.CHART_FONT_FAMILY,"bold")}}},r={lollipop:{fillStyle:(0,d.generateColor)(s,85)}},o={lollipop:{fillStyle:s,text:{strokeStyle:(0,a.getHexColorByName)("color-tan-orange-50")}}};this._defaultStyle=i,this._hoveredStyle=(0,u.merge)((0,h.deepCopy)(i),r),this._activeStyle=(0,u.merge)((0,h.deepCopy)(i),o),this._stylesCache=null}}const he=(0,a.getHexColorByName)("color-tv-blue-600"),ue=(0,a.getHexColorByName)("color-tv-blue-500");function de(e){return e?he:ue}class ce extends ie{constructor(){super(...arguments),this._getMoreFinancialsLinkInitialTabId="dividends"}extraData(e){return{amountDividends:e[1],date:e[2],adjustedAmountDividends:e[3],paymentDate:e[4]}}_createTooltipContent(e){const{date:t,amountDividends:s,paymentDate:i,adjustedAmountDividends:r}=e,o=[{name:U,value:this._formatDate(t)}];return(0,u.isNumber)(s)&&o.push({name:q,value:(0,P.roundToThirdDigit)(s)}),(0,u.isNumber)(r)&&o.push({name:G,value:(0,P.roundToThirdDigit)(r)}),(0,u.isNumber)(i)&&o.push({name:$,value:this._formatDate(i)}),[{title:H,icon:L,style:{color:de(this._model.dark().value())},anchor:this._getMoreFinancialsLink(),main:{type:"common",title:o}}]}_recreateStyles(e){const t=e.dark().value(),s=de(t),i={barLine:{lineStyle:S.LINESTYLE_DASHED,lineWidth:1,strokeStyle:s},lollipop:{width:23,height:23,bottom:1,lineWidth:1.5,importance:"dividends",strokeStyle:s,backgroundColor:t?X.dark:X.light,fillCircle:!0,text:{label:"D",strokeStyle:s,font:(0,c.makeFont)(12,p.CHART_FONT_FAMILY,"bold")}}},r={lollipop:{fillStyle:(0,d.generateColor)(s,85)}},o={lollipop:{fillStyle:s,text:{strokeStyle:(0,a.getHexColorByName)("color-tv-blue-50")}}};this._defaultStyle=i,this._hoveredStyle=(0,u.merge)((0,h.deepCopy)(i),r),this._activeStyle=(0,u.merge)((0,h.deepCopy)(i),o),this._stylesCache=null}_getMoreFinancialsLinkText(){return n.t(null,{replace:{symbol:this._model.mainSeries().symbolInfo()?.name??""}},s(218248))}}var _e=s(839800),pe=s(758868);const ye=_e.sortSourcesPreOrdered.Dividends,me=_e.sortSourcesPreOrdered.Splits,Se=_e.sortSourcesPreOrdered.Earnings;class ge extends pe.LollipopStudyBase{
id(){return"ESD$TV_DIVIDENDS"}zorder(){return ye}isValid(e){const t=e[2];return(0,u.isNumber)(t)}state(e,t){const s=super.state(e,t);return s.data.version=2,s}restoreData(e){(e.version??1)<2&&e.data.forEach((e=>{e.value[3]=null})),super.restoreData(e)}_createViews(){this._paneView=new ce(this._model,this),super._createViews()}_externalVisibilityProperty(){return this._model.mainSeries().properties().childs().esdShowDividends}}class ve extends pe.LollipopStudyBase{id(){return"ESD$TV_SPLITS"}zorder(){return me}isValid(e){const t=e[3];return(0,u.isNumber)(t)}_createViews(){this._paneView=new le(this._model,this),super._createViews()}_externalVisibilityProperty(){return this._model.mainSeries().properties().childs().esdShowSplits}}class fe extends pe.LollipopStudyBase{constructor(){super(...arguments),this._estimatePlotRow=null}id(){return"ESD$TV_EARNINGS"}zorder(){return Se}isValid(e){const t=e[3],s=e[4];return(0,u.isNumber)(t)&&(0,u.isNumber)(s)}restoreData(e){super.restoreData(e);let t=null;if(e.estimate)t=this._restorePointset(e.estimate);else if(e.pointSets&&null==e.pointSetsVersion){const s=this._getSymbol();if(null!==s){const i=s.split(":")[1].toUpperCase();e.pointSets[s]?t=this._restorePointset(e.pointSets[s]):e.pointSets[i]&&(t=this._restorePointset(e.pointSets[i]))}}else if(e.pointSets&&"1"===e.pointSetsVersion){const s=this._getSymbol();if(null!==s){const i=s.split(":")[1].toUpperCase();e.pointSets[s]?t=e.pointSets[s].estimate:e.pointSets[i]&&(t=e.pointSets[i].estimate)}}else null!=e.estimatePlotRow&&"2"===e.pointSetsVersion&&(t=e.estimatePlotRow);this._estimatePlotRow=t}getEstimate(){return this._estimatePlotRow}_createViews(){this._paneView=new re(this._model,this),super._createViews()}_externalVisibilityProperty(){return this._model.mainSeries().properties().childs().esdShowEarnings}_getSymbol(){return this._series.symbolInfo()?.base_name[0].toLowerCase()??null}_restorePointset(e){return{index:e.index,value:[e.tickmark,void 0,e.estimate,(0,u.isNumber)(e.period)?e.period:r()(e.period).unix(),(0,u.isNumber)(e.date)?e.date:r()(e.date).unix()]}}}},590178:(e,t,s)=>{"use strict";s.d(t,{ExtendedStudyDataSource:()=>o});var i=s(901466),r=s(454184);class o extends i.StudyDataSource{constructor(e,t,s,i){super(e,t.seriesSource(),s,i),this._series=t}_createStudyError(e){return(0,r.createStudyError)(this._getStudyErrorDescription(e),this._series.symbolInfo()?.exchange)}}},190484:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Fundamental:()=>f});var i=s(444372),r=s(280775),o=s(203723),a=s(492380);class n extends a.StudyPriceAxisView{constructor(e,t){super(e,t),this._fundamental=e}_updatePaneRendererData(e){e.text="",this._showPaneLabel()&&(e.text=this._fundamental.labelTitle(),e.visible=!0)}}var l=s(514294);class h extends l.StudyPriceLineAxisView{constructor(e,t){super(e,t),this._model=e.model()}_isVisible(){return this._model.properties().childs().scalesProperties.childs().showFundamentalLastValue.value()&&this._study.properties().childs().styles.childs()[this._plotname].childs().trackPrice.value()}}var u=s(681240),d=s(454184)
;const c=i.t(null,void 0,s(576751));class _ extends u.StudyStatusProvider{sourceStatusText(){const e=this._source.status();return e.type===d.StudyStatusType.Error&&(e.errorDescription.error.startsWith("resolve_error ")||e.errorDescription.error.startsWith("Symbol resolve error:"))?c:super.sourceStatusText()}}var p=s(644036),y=s(974145),m=s(132565),S=s(584785),g=s(397535),v=s(816433);class f extends r.Study{constructor(e,t,s,i,r,o){super(e,t,s,i,r,o),this._linePlot=null;const a=i.plots.findIndex((e=>"line"===e.type));if(-1!==a){const e=i.plots[a],s=t.childs().styles.childs()[e.id];s.hasChild("plottype")&&(this._linePlot={plotIndex:a,plotType:s.childs().plottype})}}labelTitle(){return this.title(v.TitleDisplayTarget.StatusLine,!0)}hasStateForAlert(){return!1}titleInParts(e,t,s,i,r){return[this._title(e,!!t)]}statusProvider(e){return new _(this)}firstValue(){const e=super.firstValue();if(null!==e||null===this._linePlot||this._linePlot.plotType.value()!==y.LineStudyPlotStyle.StepLine&&this._linePlot.plotType.value()!==y.LineStudyPlotStyle.StepLineWithDiamonds)return e;const t=this._model.timeScale().visibleBarsStrictRange();if(null===t)return null;if(!this.properties().childs().visible.value()||!this.isActualInterval())return this._ownFirstValue;const s=this.data().search(t.firstBar()-1,S.PlotRowSearchMode.NearestLeft,this._linePlot.plotIndex+1);return null===s?null:s.value[this._linePlot.plotIndex+1]??null}priceRange(e,t,s){if(s.forceOverlayOnly)return null;let i=super.priceRange(e,t,s);if(null===i){const e=this.firstValue();null!==e&&(i=this._postProcessPriceRange(new g.PriceRange(e,e),s))}return i}_createPriceAxisView(e){return new n(this,{plotIndex:e})}_createPriceLineAxisView(e){return new h(this,e)}_defaultErrorTitle(){return"Metric error"}_title(e,t){const r=this.metaInfo();let a;if(a=t?r.shortDescription:r.description,void 0!==r.financialPeriod){const e=t?(0,o.financialPeriodToShortString)(r.financialPeriod):(0,o.financialPeriodToString)(r.financialPeriod);a=`${i.t(a,{context:"study"},s(783477))} · ${e}`}const n=this._titleInputs((0,p.toInputDisplayFlags)(e),!0),l=Object.keys(n);return l.length>0&&(a=`${i.t(n[l[0]],{context:"study"},s(783477))} · ${a}`),a}_titleInputsOptions(e,t,s){return{...super._titleInputsOptions(e,t,s),skipOptionalEmptySymbolInputs:!0,allowedInputTypes:["symbol"]}}_fillPrecalculatedAutoscaleInfo(e,t,s){const i=super._fillPrecalculatedAutoscaleInfo(e,t,s);if(null!==this._linePlot&&(this._linePlot.plotType.value()===y.LineStudyPlotStyle.StepLine||this._linePlot.plotType.value()===y.LineStudyPlotStyle.StepLineWithDiamonds)){const t=this.data().search(e,S.PlotRowSearchMode.NearestLeft);if(null!==t){const e=t.value[this._linePlot.plotIndex+1];i.baseValueMinMax=(0,m.mergeMinMax)(i.baseValueMinMax,{min:e,max:e})}}return i}}},780631:(e,t,s)=>{"use strict";s.r(t),s.d(t,{LinearRegressionStudy:()=>a});var i=s(650151),r=s(707771),o=s(384597);class a extends o.NonSeriesStudy{startIndex(){const e=this.customData();if(null==e||null==this._indexes)return null;const t=this._indexes[e.startIndex]
;return t!==r.INVALID_TIME_POINT_INDEX?t:null}endIndex(){const e=this.customData();if(null==e||null==this._indexes)return null;const t=this._indexes[e.endIndex];return t!==r.INVALID_TIME_POINT_INDEX?t:null}baseLine(){const e=this.customData();return null!=e?e.baseLine:null}downLine(){const e=this.customData();return null!=e?e.downLine:null}upLine(){const e=this.customData();return null!=e?e.upLine:null}pearsons(){const e=this.customData();return null!=e&&void 0!==e.pearsons?e.pearsons:null}isVisible(){if(!this.properties().childs().visible.value()||!this.isActualInterval())return!1;const e=(0,i.ensureDefined)(this.properties().childs().styles.childs());return 0!==e.upLine.childs().display.value()||0!==e.downLine.childs().display.value()||0!==e.baseLine.childs().display.value()}_createViews(){super._createViews(),Promise.all([s.e(63317),s.e(27860),s.e(9517),s.e(20507)]).then(s.bind(s,950129)).then((({LinearRegressionPaneView:e})=>{this._setPaneViews([new e(this.model(),this)])}))}}},758868:(e,t,s)=>{"use strict";s.d(t,{LollipopStudyBase:()=>u});var i=s(280775),r=s(707771),o=s(880865),a=s(197860),n=s(448381),l=s(437642);const h=Date.UTC(2024,1,1);class u extends i.Study{constructor(e,t,s,i,r,o){super(e,t,s,i,r,o),this._restartRequired=!1,this.setOwnerSource(e.mainSeries());const a=this._externalVisibilityProperty();null!==a&&(this._destroyPropertyBinder=(0,n.bindProperties)(a,this._properties.childs().visible))}destroy(){this._paneView.destroy(),this._destroyPropertyBinder?.(),super.destroy()}updateAllViewsAndRepaint(){this.updateAllViews((0,l.sourceChangeEvent)(this.id())),this._model.updateSource(this)}autoScaleInfo(){return{range:null}}lollipopsAtIndex(e){return!this.hideLollipops()&&this.data().contains(e)?1:0}hideLollipops(){{const e=this._model.mainSeries().intervalObj().value().inMilliseconds(h);if(e<432e6)return!1;return e/this._model.timeScale().barSpacing()>432e6}}async start(){const e=super.start();return this._model.mainSeries().dataEvents().symbolResolved().subscribe(this,this._initPlotStash),this._initPlotStash(),e}stop(){super.stop(),this._model.mainSeries().dataEvents().symbolResolved().unsubscribe(this,this._initPlotStash)}restoreData(e){e.data=e.data.filter((e=>e.index!==r.INVALID_TIME_POINT_INDEX&&this.isValid(e.value))),this._transformData(e.data),e.end=e.data.length,super.restoreData(e)}onClickOutside(e,t){this._paneView.processClickOutside(e,t)}preferNoScale(){return!0}showInObjectTree(){return!1}isSavedInStudyTemplates(){return!1}isRemovedByStudyTemplates(){return!1}removeByRemoveAllStudies(){return!1}isDraggable(){return!1}copiable(){return!1}statusView(){return null}hasStateForAlert(){return!1}isSavedInChart(e){return Boolean(e)}isSpeciallyZOrderedSource(){return!0}isUserDeletable(){return!1}canHaveChildren(){return!1}isIncludedInAutoScale(){return!1}isCurrencySource(){return!1}isUnitSource(){return!1}_externalVisibilityProperty(){return null}_transformData(e){for(let t=e.length-1;t>=0;--t){const s=e[t],i=s.index!==r.INVALID_TIME_POINT_INDEX&&this.isValid(s.value);this._updatePlotStash(s,i)}}
_createViews(){this._paneViews=[this._paneView],this._priceAxisViews=[],this._dataWindowView||(this._dataWindowView=new o.DataWindowView),this._legendView=null,this._statusView||(this._statusView=new a.StudyStatusView(this))}_seriesDataRangeToSave(e){return this._model.timeScale().visibleBarsStrictRange()}_initPlotStash(){this._plotStash={}}_updatePlotStash(e,t){if(this._restartRequired||!this._plotStash)return;const s=e.value.slice(),i=s.shift(),r=String(i);t?this._plotStash[r]?String(this._plotStash[r])!==String(s)&&(this._restartRequired=!0):this._plotStash[r]=s:this._plotStash[r]&&(this._restartRequired=!0)}}},328786:(e,t,s)=>{"use strict";s.d(t,{LollipopStudyPaneViewBase:()=>_});var i=s(86441),r=s(650151),o=s(276536),a=s(444372),n=s(224743),l=s(886338),h=s(700134),u=s(888194),d=s(437199),c=s(213377);class _ extends d.LollipopPaneView{constructor(){super(...arguments),this._getMoreFinancialsLinkInitialTabId="overview",this._symbolName=null}addLollipop(e,t,s,r,o,a,n){if(null===this._symbolName&&(this._symbolName=this._model.mainSeries().symbolInfo()?.name??null,null===this._symbolName))return;const l=`${this._source.metaInfo().id}_${this._lollipopId(a)}_${this._symbolName}`,h=this._lollipops[l],u=void 0!==h?h.itemIndex:this._lollipopsCounter++,d=this._model.lastHittestData(),c=this._model.hoveredSource()===this._source&&null!==d&&d.activeItem&&d.activeItem.itemIndex===u;this._lollipops[l]={id:l,itemIndex:u,visible:!0,basePoint:new i.Point(s.indexToCoordinate(o),e),hovered:c,stack:n,...this.extraData(a)}}renderer(e){return this._invalidated&&(this._updateImpl(e),this._invalidated=!1),this._source.hideLollipops()?null:this._renderer}_lollipopId(e){return e[0].toString()}_updateImpl(e){this._renderer.clear();const t=this._source.data(),s=this._model.timeScale(),i=s.visibleBarsStrictRange(),o=this._model.mainSeries().bars(),a=this._model.mainSeries(),n=a.priceScale(),l=a.firstBar();if(n.isEmpty()||s.isEmpty()||!l||!t||null===i)return;const h=this._model.mainPane();if(!h)return;if(this._source.hideLollipops())return void this._activeLollipopInfo.setValue(null);for(const e in this._lollipops)this._lollipops.hasOwnProperty(e)&&((0,r.ensureDefined)(this._lollipops[e]).visible=!1);const u=this._getY(),d=i.firstBar(),c=i.lastBar(),_=this._source.zorder(),p=h.lollipopDataSources().filter((e=>e!==this._source&&e.zorder()>_&&e.isVisible()));t.range(d,c).each(((e,t)=>{let i=0;for(const t of p)i+=t.lollipopsAtIndex(e);return this.addLollipop(u,o,s,n,e,t,i),!1})),this.afterUpdate(u,o,s,n),this._createRenderers(e)}_formatDate(e,t){let s,i=!0;if(i=l.Version.parse(this._source.metaInfo().version).isLessOrEqual(new l.Version(137,0)),i){let t="Etc/UTC";this._model.mainSeries().isDWM()||(t=this._model.timezoneExceptExchange().value()??"");const i=(0,o.get_timezone)(t);s=(0,o.utc_to_cal)(i,e)}else s=new Date(e);return(t?"≈ ":"")+this._model.dateFormatter().format(s)}_getMoreFinancialsLink(){if(!/\betf\b/i.test(this._model.mainSeries().symbolInfo()?.description||""))return{text:this._getMoreFinancialsLinkText(),onClick:()=>{
n.globalCloseDelegate.fire(),c.enabled("mobile_app_action_open_financials_webview")?(0,u.openFinancialsWebview)(new URLSearchParams):(0,h.showFinancialsDialog)({initialTabId:this._getMoreFinancialsLinkInitialTabId})}}}_getMoreFinancialsLinkText(){return a.t(null,{replace:{symbol:this._model.mainSeries().symbolInfo()?.name??""}},s(148358))}_onSymbolOrIntervalChanged(){super._onSymbolOrIntervalChanged(),this._symbolName=null}}},55840:(e,t,s)=>{"use strict";s.d(t,{OverlayDataWindowView:()=>d});var i=s(638456),r=s(529751),o=s(87913),a=s(213377),n=s(816433),l=s(986593),h=s(844859);const u=i.CheckMobile.any();class d extends h.StudyDataWindowView{_updateImpl(){this._header=this._study.title(n.TitleDisplayTarget.DataWindow);let e;this._showLastPriceAndChangeOnly()?e=this._study.data().lastIndex():(e=this._model.crosshairSource().appliedIndex(),(null===e||isNaN(e))&&(e=this._study.data().lastIndex(),a.enabled("use_last_visible_bar_value_in_legend")&&(e=this._model.timeScale().visibleBarsStrictRange()?.lastBar()??NaN)));const t=this._valueProvider.getValues(e);for(let e=0;e<t.length;++e){const s=t[e],i=this._items[e];i.setValue(s.value),i.setVisible(s.visible),i.setColor(s.color)}}_showLastPriceAndChangeOnly(){return u&&(null===this._model.crosshairSource().pane||(0,r.isLineToolName)(o.tool.value())||null!==this._model.lineBeingEdited())}_createValuesProvider(e,t){return new l.OverlayValuesProvider(e,t)}}},986593:(e,t,s)=>{"use strict";s.d(t,{OverlayValuesProvider:()=>w});var i=s(150335),r=s(444372),o=s(638456),a=s(534328),n=s(969680),l=s(951827),h=s(176749),u=s(584785),d=s(830101),c=s(87913),_=s(889858),p=s(909954),y=s(529751),m=s(634969);const S=d.lastDayChangeAvailable||d.alwaysShowLastPriceAndLastDayChange,g=o.CheckMobile.any(),v=(0,l.getPercentageFormatter)(),f=m.notAvailable,b=`${f} (${f}%)`;class w{constructor(e,t){this._study=e,this._model=t,this._emptyValues=[{title:r.t(null,void 0,s(16610)),visible:!1,value:"",index:0,id:"open"},{title:r.t(null,void 0,s(778254)),visible:!1,value:"",index:1,id:"high"},{title:r.t(null,void 0,s(165318)),visible:!1,value:"",index:2,id:"low"},{title:r.t(null,{context:"input"},s(51408)),visible:!1,value:"",index:3,id:"close"},{title:"",visible:!1,value:"",index:4,id:"source"},{title:"",visible:!1,value:"",index:5,id:"lastPrice"},{title:r.t(null,void 0,s(537276)),visible:!1,value:"",index:6,id:"change"},{title:r.t(null,void 0,s(463815)),visible:!1,value:"",index:7,id:"lastDayChange"}]}getItems(){return this._emptyValues}getValues(e){const t=this._emptyValues.map((e=>({...e})));if(this._model.timeScale().isEmpty())return t;const s=this._study.data(),r=s.lastIndex();if(0===s.size()||null===r)return t;const o=s.search(r,u.PlotRowSearchMode.NearestLeft,1);if(null===o)return t;const l=this._showLastPriceAndChangeOnly();if((0,i.isNumber)(e)||(l?e=r:(e=this._model.crosshairSource().appliedIndex(),(0,i.isNumber)(e)||(e=r))),null===e||!(0,i.isNumber)(e))return t;const d=s.search(e,u.PlotRowSearchMode.NearestLeft,1),c=this._model.backgroundTopColor().value();if(null===d)return t
;const _=d.index,y=d.value,m=y[1],g=y[2],w=y[3],C=y[4];t[0].value=f,t[1].value=f,t[2].value=f,t[3].value=f,t[7].value=b,t[6].value=b;for(const e of t)e.visible=!l;const x=t[4];x.visible=!1;const{barChange:P,lastDayChange:I}=(0,h.changesData)(s,this._study.quotes(),d.value,d.index,o.value),D=(0,p.getPriceValueFormatterForSource)(this._study);if((0,p.shouldBeFormattedAsPercent)(this._study)||(0,p.shouldBeFormattedAsIndexedTo100)(this._study))t[7].value="",t[6].value="";else{const e=this._study.formatter(),s={signPositive:!0};if(void 0!==P){const{currentPrice:i,prevPrice:r,change:o}=P,a=e.formatChange?.(i,r,s)??e.format(o,s);t[6].value=(0,n.forceLTRStr)(`${a} (${v.format(P.percentChange,s)})`)}if(void 0!==I){const{currentPrice:i,prevPrice:r,change:o,percentChange:a}=I,l=e.formatChange?.(i,r,s)??e.format(o,s);t[7].value=(0,n.forceLTRStr)(`${l} (${v.format(a,s)})`)}}let V=null;if(l)t[5].value=null==C?f:D(C),t[5].visible=!0,V=this._getChangeColor(I?.change),t[6].visible=void 0!==P,t[7].visible=void 0!==I||S;else{t[0].value=null==m?f:D(m),t[1].value=null==g?f:D(g),t[2].value=null==w?f:D(w),t[3].value=null==C?f:D(C),x.value=D(this._study.barFunction()(y)),t[5].visible=!1;const e=this._model.mainSeries().intervalObj().value().is1Tick(),s=21!==this._study.properties().childs().style.value();t[0].visible=!e&&s,t[1].visible=!e,t[2].visible=!e,t[7].visible=void 0!==I||S,t[6].visible=void 0!==P;const i=this._study.barColorer().barStyle(_,!1),r=i.barBorderColor??i.barColor;V=(0,h.calculateColor)(c,r)}V=(0,a.resetTransparency)((0,h.calculateColor)(c,V));for(const e of t)e.color||(e.color=V);return t[7].visible&&(t[7].color=(0,a.resetTransparency)((0,h.calculateColor)(c,this._getChangeColor(I?.change)))),t}_mobileNonTrackingMode(){return g&&(null===this._model.crosshairSource().pane||(0,y.isLineToolName)(c.tool.value())||null!==this._model.lineBeingEdited())}_showLastPriceAndChangeOnly(){return d.alwaysShowLastPriceAndLastDayChange||this._mobileNonTrackingMode()}_getChangeColor(e){const t=void 0===e||e>=0?_.SeriesBarColorer.upColor(this._study.properties()):_.SeriesBarColorer.downColor(this._study.properties());return t.barBorderColor??t.barColor}}},698703:(e,t,s)=>{"use strict";s.r(t),s.d(t,{study_PivotPointsStandard:()=>L});var i=s(650151),r=s(384597),o=s(327714),a=s(688857);class n{constructor(e,t,s,i){this._priceAxisFontSize=11,this._prices=[],this._labelWidth=0,this._pixelRatioParams=e,this._recreateCanvasAndContext((0,o.size)({width:0,height:0})),this.reset({font:t,fontSize:s,backColors:i})}destroy(){delete this._canvas,delete this._cache}canvas(){return this._canvas}reset(e){this._renderParams=e,this._prices=[],this._cache.font=e.fontSize+"px "+e.font,this._labelWidth=["P","S1","R1","S2","R2","S3","R3","S4","R4","S5","R5","/"].reduce(((e,t)=>{const s=this._cache.measureText(t).width;return Math.max(s,e)}),0)}rowHeight(){return this._priceAxisFontSize+4}labelRectByIndex(e){return{left:0,top:Math.round(this._topByIndex(e)),width:Math.round(this._labelWidth+4),height:Math.round(this._renderParams.fontSize+8)}}setPrices(e){
let t=!1;const s=(e,t)=>{const s=void 0===e,r=void 0===t;return(!s&&!r||s===r)&&(0,i.ensureDefined)(e).formatted===(0,i.ensureDefined)(t).formatted};if(e.length!==this._prices.length)t=!0;else for(let i=0;i<this._prices.length;i++)if(!s(this._prices[i],e[i])){t=!0;break}if(t){const t=this._labelWidth+6,s=this._renderParams.fontSize,i=Math.max(e.length,22)*(s+8);this._recreateCanvasAndContext((0,o.size)({width:t,height:i})),this._prices=e,this._cache.save(),(0,a.drawScaled)(this._cache,this._pixelRatioParams.horizontalPixelRatio,this._pixelRatioParams.verticalPixelRatio,(()=>{this._cache.translate(.5,.5),this._cache.font=this._renderParams.fontSize+"px "+this._renderParams.font,this._cache.textBaseline="middle";for(let e=0;e<this._prices.length;e++){if(!this._prices[e])continue;const t=["P","S1","R1","S2","R2","S3","R3","S4","R4","S5","R5"][e];this._cache.fillStyle=this._renderParams.backColors[e],this._cache.fillText(t,0,this._centerByIndex(e)),this._cache.fillText("/",0,this._centerByIndex(e+11))}})),this._cache.restore(),this._prices=e}}_recreateCanvasAndContext(e){this._canvas=document.createElement("canvas"),this._canvas.width=e.width*this._pixelRatioParams.horizontalPixelRatio,this._canvas.height=e.height*this._pixelRatioParams.verticalPixelRatio,this._cache=(0,i.ensureNotNull)(this._canvas.getContext("2d"))}_centerByIndex(e){return Math.round((e+.5)*(this._renderParams.fontSize+8))}_topByIndex(e){return Math.round(e*(this._renderParams.fontSize+8))}}var l=s(910282),h=s(597331),u=s(534328);const d=["P","S1","R1","S2","R2","S3","R3","S4","R4","S5","R5"];class c extends h.PriceAxisView{constructor(e,t){super(),this._source=e,this._data=t;const s=t.name;this._completeName="P"===s.toUpperCase()?"P":`S${s[1]}/R${s[1]}`}_updateRendererData(e,t,s){e.visible=!1,t.visible=!1;const i=this._source.properties().childs();if(!i.visible.value())return;const r=this._completeName,o=i.levelsStyle.childs().visibility.childs();if(!o[r]||!o[r].value())return;const a=this._source.model().timeScale(),n=this._source.priceScale();if(a.isEmpty()||null===a.visibleBarsStrictRange()||null!==n&&n.isEmpty())return;const l=this._source.customData();if(!l||!l.pivots)return;const h=this._source.pricesView().prices()[d.indexOf(this._data.name.toUpperCase())];if(!h)return;s.background=(0,u.resetTransparency)(h.color),s.textColor=this.generateTextColor(s.background),s.coordinate=h.coordinate,s.floatCoordinate=h.coordinate;const c=this._source.model().properties().childs().scalesProperties.childs();c.showStudyLastValue.value()&&(e.text=h.formatted,e.visible=!0),c.showStudyPlotLabels.value()&&(t.text=this._source.priceLabelText(this._data.name),t.visible=!0)}}var _=s(542301),p=s(844859),y=s(197860),m=s(584785);const S=["p","s1","r1","s2","r2","s3","r3","s4","r4","s5","r5"],g={P:"P",S1:"S1/R1",S2:"S2/R2",S3:"S3/R3",S4:"S4/R4",S5:"S5/R5",R1:"S1/R1",R2:"S2/R2",R3:"S3/R3",R4:"S4/R4",R5:"S5/R5"};class v{constructor(e){this._visiblePivots=new Set,this._invidated=!0,this._prices=[],this._source=e}visiblePivots(){return this._visiblePivots}update(){
this._invidated=!0}prices(){return this._invidated&&(this._updateImpl(),this._invidated=!1),this._prices}_updateImpl(){this._visiblePivots.clear();const e=this._source.model(),t=this._source.priceScale();if(null===t)return;if(e.timeScale().isEmpty()||t.isEmpty())return;const s=e.timeScale().visibleBarsStrictRange();if(null===s)return;if(!this._source.customData()||!this._source.customData().pivots)return;const i=e.mainSeries().bars().search(s.lastBar(),m.PlotRowSearchMode.NearestLeft);if(null===i)return;const r=this._source.indexes();if(!r)return;const o=i.index,a=this._source.customData().pivots,n=this._source.properties().childs(),l=this._source.firstValue();for(let e=0;e<a.length;e++){if(!a[e])continue;const s=r[a[e].startIndex],i=r[a[e].endIndex],h=n.inputs.childs().showHistoricalPivots.value();if(s<=o&&(i>=o||h)){this._visiblePivots.add(a[e]),this._prices=[];for(let s=0;s<S.length;s++){const i=S[s],r=a[e][i];if(void 0===r||null===l)continue;const o=t.priceToCoordinate(r,l),h=i.toUpperCase(),u=g[h],d=n.levelsStyle.childs().colors.childs()[u].value();this._prices.push({formatted:t.formatPrice(r,l),price:r,coordinate:o,color:d})}}}}}var f=s(86441),b=s(257145),w=s(708162),C=s(654397),x=s(731974),P=s(320994);class I extends P.BitmapCoordinatesPaneRenderer{constructor(e,t,s){super(),this._drawRects=[],this._cacheProvider=e,this._point=t,this._label=s}hitTest(e){for(const t of this._drawRects)if(e.x>=t.left&&e.x<=t.left+t.width&&e.y>=t.top&&e.y<=t.top+t.height)return new x.HitTestResult(x.HitTarget.Regular);return null}_drawImpl(e){const t=this._cacheProvider(e),{horizontalPixelRatio:s,verticalPixelRatio:i,context:r}=e;this._drawRects=[];const o=e=>{const o=t.labelRectByIndex(e),a={left:Math.round(this._point.x-o.width+n),top:Math.round(this._point.y-o.height/2),width:o.width,height:o.height};return r.drawImage(t.canvas(),Math.round(o.left*s),Math.round(o.top*i),o.width*s,o.height*i,Math.round(a.left*s),Math.round(a.top*i),a.width*s,a.height*i),this._drawRects.push(a),o.width},a=this._label.split("/");let n=0;for(let e=0;e<a.length;e++){const t=["P","S1","R1","S2","R2","S3","R3","S4","R4","S5","R5"].indexOf(a[e]);e>0&&(n+=o(t+11)/2),n+=o(t)/2}}}function D(e){return"P"===e?e:"S"+e[1]+"/R"+e[1]}function V(e,t,s){const i=t;void 0===e[i]?e[i]={text:s,ids:[D(s)]}:(e[i].text+="/"+s,e[i].ids.push(D(s)))}class T{constructor(e,t){this._pivots=[],this._invalidated=!0,this._renderer=new w.CompositeRenderer,this._model=e,this._source=t,this._cacheProvider=this._source.getCache.bind(this._source)}update(e){this._invalidated=!0}renderer(){return this._invalidated&&(this._updateImpl(),this._invalidated=!1),this._renderer}_updateImpl(){this._renderer.clear();const e=this._source.ownerSource();if(null===e)return;this._source.pricesView().prices(),this._pivots=[];const t=this._source.customData();if(!t||!t.pivots)return;const s=this._source.properties().childs();if(!s.visible.value())return;const r=t.pivots,o=this._source.indexes(),a=this._model.timeScale(),n=this._source.priceScale(),l=e.firstValue()
;if(!n||n.isEmpty()||a.isEmpty()||!r||!o||null===l)return;const h=(0,i.ensureNotNull)(a.visibleBarsStrictRange()),u=h.firstBar(),d=h.lastBar();for(let e=0;e<r.length;e++){if(!r[e])continue;const t=o[r[e].startIndex],i=o[r[e].endIndex];if(i<u||t>d)continue;const h={},c=s.levelsStyle.childs().visibility.childs();c.P.value()&&V(h,r[e].p,"P"),c["S1/R1"].value()&&(V(h,r[e].s1,"S1"),V(h,r[e].r1,"R1")),c["S2/R2"].value()&&(V(h,r[e].s2,"S2"),V(h,r[e].r2,"R2")),c["S3/R3"].value()&&(V(h,r[e].s3,"S3"),V(h,r[e].r3,"R3")),c["S4/R4"].value()&&(V(h,r[e].s4,"S4"),V(h,r[e].r4,"R4")),c["S5/R5"].value()&&(V(h,r[e].s5,"S5"),V(h,r[e].r5,"R5"));const _=a.indexToCoordinate(t),p=a.indexToCoordinate(i);for(const t of Object.keys(h)){const s=parseFloat(t),i=n.priceToCoordinate(s,l);this._pivots.push({x1:_,x2:p,y:i,label:h[t].text,labelIds:h[t].ids,src:r[e]})}}const c=s.levelsStyle.childs().colors,_=s.levelsStyle.childs().widths,p=this._source.visiblePivots();for(let e=0;e<this._pivots.length;e++){const t=this._pivots[e];if(!p.has(t.src))continue;const i={color:c.childs()[t.labelIds[0]].value(),linewidth:_.childs()[t.labelIds[0]].value(),linestyle:b.LINESTYLE_SOLID,y:t.y,left:t.x1,right:t.x2},r=new C.HorizontalLineRenderer;r.setData(i),r.setHitTest(new x.HitTestResult(x.HitTarget.Regular)),this._renderer.append(r),s.levelsStyle.childs().showLabels.value()&&this._renderer.append(new I(this._cacheProvider,new f.Point(t.x1,t.y),t.label))}}}var k=s(397535);class L extends r.NonSeriesStudy{constructor(e,t,s,i,r,o){super(e,t,s,i,r,o),this._cache=null,this._cachedPixelRatioParams=null;const a=["P","S1/R1","S2/R2","S3/R3","S4/R4","S5/R5"],n=this.properties().childs().levelsStyle.childs().visibility;for(let e=0;e<a.length;e++)n.childs()[a[e]].subscribe(this,(()=>this.processHibernate()))}pricesView(){return this._pricesView}indexes(){return this._indexes}properties(){return this._properties}getCache(e){if(null===this._cache||null==this._cachedPixelRatioParams||(t=e,s=this._cachedPixelRatioParams,t.horizontalPixelRatio!==s.horizontalPixelRatio||t.verticalPixelRatio!==s.verticalPixelRatio)){this._cache&&this._cache.destroy();const t=this._getActualCacheParams(),s={horizontalPixelRatio:e.horizontalPixelRatio,verticalPixelRatio:e.verticalPixelRatio};this._cache=new n(s,t.font,t.fontSize,t.backColors),this._cache.setPrices((0,i.ensureNotNull)(this._pricesView).prices()),this._cachedPixelRatioParams=s,this._cache.reset(t)}var t,s;return this._cache}priceLabelText(e){return this._metaInfo.value().shortDescription+":"+e.toUpperCase()}updateAllViews(e){super.updateAllViews(e),this._pricesView.update()}visiblePivots(){return this._pricesView.visiblePivots()}isVisible(){if(!this.properties().childs().visible.value()||!this.isActualInterval())return!1;const e=["P","S1/R1","S2/R2","S3/R3","S4/R4","S5/R5"],t=this.properties().childs().levelsStyle.childs().visibility;for(let s=0;s<e.length;s++)if(t.childs()[e[s]].value())return!0;return!1}stop(){super.stop(),this._cache&&(this._cache.destroy(),this._cache=null)}priceRange(e,t,s){
if(s.targetPriceScale!==this.priceScale())return null;const i=this.customData()?.pivots;if(!i||!this._indexes)return null;if(!this.priceScale())return null;const r=i,o=this._indexes;let a=null;for(let s=0;s<r.length;s++){if(!r[s])continue;const i=o[r[s].startIndex];if(o[r[s].endIndex]<e||i>t)continue;const n=[],l=this.properties().childs().levelsStyle.childs().visibility.childs();l.P.value()&&n.push(r[s].p),l["S1/R1"].value()&&n.push(r[s].s1,r[s].r1),l["S2/R2"].value()&&n.push(r[s].s2,r[s].r2),l["S3/R3"].value()&&n.push(r[s].s3,r[s].r3),l["S4/R4"].value()&&n.push(r[s].s4,r[s].r4),l["S5/R5"].value()&&n.push(r[s].s5,r[s].r5);for(let e=0;e<n.length;e++)n[e]&&(null===a?a=new k.PriceRange(n[e],n[e]):a.apply(n[e],n[e]))}const n=this.priceScale();return n&&n.isLog()&&a?new k.PriceRange(n.priceToLogical(a.minValue()),n.priceToLogical(a.maxValue())):a}_createViews(){this._cache&&(this._cache.destroy(),this._cache=null),this._priceAxisViews=[];const e=["P","S1","R1","S2","R2","S3","R3","S4","R4","S5","R5"];this._paneViews.length=0,this._labelPaneViews=[];const t=new T(this._model,this);this._paneViews.push(t);for(let t=0;t<e.length;t++){const s=new c(this,{name:e[t]});this._priceAxisViews.push(s),this._labelPaneViews.push(new _.PanePriceAxisView(s,this,this._model))}this._dataWindowView||(this._dataWindowView=new p.StudyDataWindowView(this,this._model)),this._statusView||(this._statusView=new y.StudyStatusView(this)),this._legendView=null,this._pricesView=new v(this)}_postProcessGraphics(){}_getActualCacheParams(){const e=this.properties().childs(),t=e.levelsStyle.childs().colors.childs();return{font:l.CHART_FONT_FAMILY,fontSize:e.fontsize.value(),backColors:[t.P.value(),t["S1/R1"].value(),t["S1/R1"].value(),t["S2/R2"].value(),t["S2/R2"].value(),t["S3/R3"].value(),t["S3/R3"].value(),t["S4/R4"].value(),t["S4/R4"].value(),t["S5/R5"].value(),t["S5/R5"].value()]}}}},31857:(e,t,s)=>{"use strict";s.r(t),s.d(t,{study_ESD$TV_ROLLDATES:()=>M});var i=s(758868),r=s(154834),o=s(790188),a=s(444372),n=s(214258),l=s(825957),h=s(328786),u=s(257145),d=s(837462),c=s(910282),_=s(534328),p=s(554255),y=s(528474);const m=[a.t(null,void 0,s(100200)),a.t(null,void 0,s(581069)),a.t(null,void 0,s(193878)),a.t(null,void 0,s(528896)),a.t(null,void 0,s(125734)),a.t(null,void 0,s(661487)),a.t(null,void 0,s(206608)),a.t(null,void 0,s(811081)),a.t(null,void 0,s(632179)),a.t(null,void 0,s(137997)),a.t(null,void 0,s(604607)),a.t(null,void 0,s(890082))];var S=s(731974),g=s(338242),v=s(398453);const f=new Path2D("m13 6.02.5-.52-.5-.52-3.33-3.5-1.09 1.04 2.13 2.23H6.23L2.13.95l-1.01 1.1 4.3 4 .22.2H10.71L8.58 8.48l1.09 1.04 3.34-3.5Zm-9.4.45-3 3 1.06 1.06 3-3L3.6 6.47Z");class b extends v.LollipopRenderer{_drawLollipop(e,t,s){const i=this._data.style;e.save(),e.translate(t.x,t.y),e.scale(s.horizontalPixelRatio,s.verticalPixelRatio),e.beginPath(),e.strokeStyle=i.outerBorderColor,e.lineWidth=1,e.arc(0,0,11,0,2*Math.PI),e.stroke(),e.fillStyle=i.backgroundColor,e.beginPath(),e.arc(0,0,10.5,0,2*Math.PI),e.fill(),e.translate(-6.5,-5.5),e.fillStyle=i.iconColor,
e.fill(f),e.translate(6.5,5.5),i.borderColor&&(e.strokeStyle=i.borderColor,e.lineWidth=1.5,(0,g.setLineStyle)(e,u.LINESTYLE_SOLID),e.beginPath(),e.arc(0,0,9.75,0,2*Math.PI),e.stroke()),e.restore()}}var w=s(85752);const C=a.t(null,void 0,s(716110)),x=a.t(null,void 0,s(198605)),P=a.t(null,void 0,s(560249)),I=a.t(null,void 0,s(722677)),D={dark:{background:(0,o.getHexColorByName)("color-cold-gray-900"),foreground:(0,o.getHexColorByName)("color-deep-blue-a200"),linkColor:(0,o.getHexColorByName)("color-tv-blue-500")},light:{background:(0,o.getHexColorByName)("color-white"),foreground:(0,o.getHexColorByName)("color-deep-blue-a400"),linkColor:(0,o.getHexColorByName)("color-tv-blue-500")}},V={barLine:{lineStyle:u.LINESTYLE_DASHED,lineWidth:1,strokeStyle:""},lollipop:{width:21,height:21,bottom:2,lineWidth:1.5,strokeStyle:"",backgroundColor:"",fillCircle:!0,fillStyle:"",text:{label:"",strokeStyle:"",font:(0,d.makeFont)(12,c.CHART_FONT_FAMILY,"bold")}},iconColor:"",backgroundColor:"",outerBorderColor:""},T=["F","G","H","J","K","M","N","Q","U","V","X","Z"];function k(e,t){const s=Math.floor(t/100),i=t-100*s-1;return{name:`${e}${T[i]}${s}`,expirationDate:`${m[i]} ${s}`}}class L extends h.LollipopStudyPaneViewBase{extraData(e){const t=[];for(let s=0;s<4;++s){const i=3*s+1;if(!Boolean(e[i]))break;t.push({currentContractCode:e[i],nextContractCode:e[i+1],switchDate:e[i+2]})}return{items:t}}_createRendererForLollipop(e,t){return new b(e,new S.HitTestResult(S.HitTarget.Custom,t),this._textWidthCache)}_createTooltipContent(e){return e.items.map((e=>{const t=[{name:I,value:(0,p.formatYYYYMMDD)(this._model.dateFormatter(),e.switchDate)}],i=this._model.dark().value()?D.dark:D.light,r=this._model.mainSeries(),o=this._model.mainSeries().symbolInfo();if(null!==o&&void 0!==o.root){const s=o.listed_exchange,a={color:i.linkColor,cursor:"pointer"},n=k(o.root,e.currentContractCode);t.push({name:x,value:n.name,valueStyle:a,onValueClick:()=>this._model.undoModel().setSymbol(r,`${s}:${n.name}`)}),t.push({name:" ",value:n.expirationDate});const l=k(o.root,e.nextContractCode);t.push({name:P,value:l.name,valueStyle:a,onValueClick:()=>this._model.undoModel().setSymbol(r,`${s}:${l.name}`)}),t.push({name:" ",value:l.expirationDate})}return{title:C,informationButtonProps:{ariaLabel:a.t(null,void 0,s(750619)),icon:"question",onClick:()=>(0,l.showSupportDialog)({solutionId:n.solutionIds.SWITCHING_CONTINUOUS_FUTURES_CONTRACTS})},icon:w,style:{color:i.foreground},main:{type:"common",title:t}}}))}_lollipopId(e){return e[3].toString()}_showBarLine(e){return super._showBarLine(e)||this._model.mainSeries().properties().childs().showContinuousContractSwitchesBreaks.value()}_recreateStyles(e){const t=this._model.dark().value()?D.dark:D.light,s=t.foreground,i=t.background,o=(0,r.default)(V);o.iconColor=s,o.borderColor=s,o.backgroundColor=i,o.outerBorderColor=i,o.barLine.strokeStyle=s,this._defaultStyle=o;const a=(0,r.default)(o);a.backgroundColor=(0,y.blendColors)(i,(0,_.applyAlpha)(s,.15)),this._hoveredStyle=a;const n=(0,r.default)(o);n.backgroundColor=s,
n.iconColor=i,n.borderColor=void 0,this._activeStyle=n,this._stylesCache=null}}var R=s(839800),A=s(536794);class M extends i.LollipopStudyBase{id(){return"ESD$TV_ROLLDATES"}zorder(){return R.sortSourcesPreOrdered.RollDates}isValid(e){const t=e[4];return(0,A.isNumber)(t)}_createViews(){this._paneView=new L(this._model,this),super._createViews()}_externalVisibilityProperty(){return this._model.mainSeries().properties().childs().showContinuousContractSwitches}}},445296:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ReplayStudyStrategy:()=>a});var i=s(252010),r=s(454184),o=s(925049);class a extends i.StudyStrategy{constructor(e,t,s,i,r,o,a){t.merge({strategy:{orders:{showLabels:!0,showQty:!0,visible:!0}}}),super(e,t,s,i,r,o,a,!0),this._notifyStudyTurnaroundCounter=0,this._notificationsInProgress=new Map,this._replayStudyProperties=null,this._assignRecoveryState=()=>{const e=this.properties().childs().inputs,t=this._recoveryState();"recoveryState"in e.childs()&&e.childs().recoveryState.setValue(JSON.stringify(t))};const n=this.properties().childs().inputs;e.mainSeries().onIntervalChanged().subscribe(this,this._assignRecoveryState),e.replayStudyStrategyProperties().then((e=>{this._isDestroyed||(this._replayStudyProperties=e,this._replayStudyProperties.subscribe(this,(()=>{this.properties().childs().inputs.mergeAndFire(e.state())})),this.properties().childs().inputs.mergeAndFire(e.state()))})),n.subscribe(this,((e,t)=>{"recoveryState"!==t&&this._assignRecoveryState()}))}destroy(){this._notificationsInProgress.forEach((e=>{e.reject("The study has been destroyed")})),this._model.mainSeries().onIntervalChanged().unsubscribeAll(this),this._replayStudyProperties?.unsubscribeAll(this),super.destroy()}clearRecoveryStateAndReport(){const e=this.properties().childs().inputs;"recoveryState"in e.childs()&&e.childs().recoveryState.setValue("{}"),this._reportData=null}clearData(){this._assignRecoveryState(),super.clearData()}showInObjectTree(){return!1}statusView(){return null}async notifyStudy(e){await this._waitForComplete();const t="notify_"+ ++this._notifyStudyTurnaroundCounter,s=(0,o.createDeferredPromise)();return this._notificationsInProgress.set(t,s),this._sendNotifyCommand(t,e),s.promise}removeByRemoveAllStudies(){return!1}isRemovedByStudyTemplates(){return!1}isSavedInChart(e){return!1}isSavedInStudyTemplates(){return!1}_onData(e){if("notify_study_ok"===e.method){const t=e.params[1];this._notificationsInProgress.get(t)?.resolve(),this._notificationsInProgress.delete(t)}if("notify_study_error"===e.method){const t=e.params[1],s=e.params[2];this._notificationsInProgress.get(t)?.reject(s),this._notificationsInProgress.delete(t)}super._onData(e)}async _waitForComplete(){if(this.status().type!==r.StudyStatusType.Completed)return new Promise(((e,t)=>{const s=i=>{i.type===r.StudyStatusType.Completed?(this.onStatusChanged().unsubscribe(null,s),e()):i.type===r.StudyStatusType.Error&&(this.onStatusChanged().unsubscribe(null,s),t(i.errorDescription))};this.onStatusChanged().subscribe(null,s)}))}_recoveryState(){const e=this.reportData();if(!e){
const e=this.properties().childs().inputs.childs().recoveryState.value();return JSON.parse(e)}const{activeOrders:t,filledOrders:s}=e;return{activeOrders:t,filledOrders:s}}}},901466:(e,t,s)=>{"use strict";s.r(t),s.d(t,{StudyDataSource:()=>p});var i=s(650151),r=s(547465),o=s(132565),a=s(359035),n=s(807958),l=s(259710),h=s(587482),u=s(284881),d=s(808825);const c=(0,s(735566).getLogger)("Chart.StudyDataSource");var _;!function(e){e[e.Idle=0]="Idle",e[e.AwaitingConnection=1]="AwaitingConnection",e[e.AwaitingParent=2]="AwaitingParent",e[e.AwaitingFirstDataUpdate=3]="AwaitingFirstDataUpdate",e[e.Active=4]="Active"}(_||(_={}));class p{constructor(e,t,s,i,a=!1){this._inputs=null,this._status=_.Idle,this._studyId=null,this._turnaroundCounter=1,this._studyStatus={type:d.StudyStatusType.Undefined},this._studyStatusChanged=new r.Delegate,this._dataCleared=new r.Delegate,this._dataUpdated=new r.Delegate,this._boundOnGatewayIsConnectedChanged=this._onGatewayIsConnectedChanged.bind(this),this._ongoingDataUpdate=Promise.resolve(),this._gateway=e,this._metaInfo=i,this._forceUseExclamationMark=a,this._seriesSource=t,this._turnaroundPrefix=s,this._plots=new o.PlotList((0,h.studyPlotFunctionMap)(i),h.studyEmptyPlotValuePredicate),this._gateway.isConnected().subscribe(this._boundOnGatewayIsConnectedChanged),this._graphics=new u.LiveStudyGraphics(i.graphics)}destroy(){this.stop(),this._gateway.isConnected().unsubscribe(this._boundOnGatewayIsConnectedChanged),this._seriesSource.dataEvents().created().unsubscribeAll(this)}metaInfo(){return this._metaInfo}inputs(){return this._inputs}setInputs(e){this._inputs=e,null!==this._studyId&&(this._turnaroundCounter++,this._onStudyStatusChangedTo({type:d.StudyStatusType.Undefined}),this._gateway.modifyStudy(this._studyId,this._turnaround(),e,this._onMessage.bind(this)),this._status===_.Active&&this._changeStatusTo(_.AwaitingFirstDataUpdate))}isStarted(){return this._status!==_.Idle}isActive(){return this._status===_.Active}start(){this.isStarted()?c.logNormal("start: data source is already started, nothing to do"):((0,i.assert)(null!==this._inputs,"Inputs should be defined when starting a study data source"),this._gateway.isConnected().value()?this._createStudy():this._changeStatusTo(_.AwaitingConnection))}stop(){this.isStarted()?(null!==this._studyId&&(this._gateway.isConnected().value()&&this._gateway.removeStudy(this._studyId),this._studyId=null,this._onStudyStatusChangedTo({type:d.StudyStatusType.Undefined})),this._changeStatusTo(_.Idle)):c.logNormal("stop: data source is already stopped, nothing to do")}studyId(){return this._studyId}studyStatus(){return this._studyStatus}studyStatusChanged(){return this._studyStatusChanged}plots(){return this._plots}graphics(){return this._graphics}clearData(){this._plots.clear(),this._graphics.clear(),this._dataCleared.fire()}stopAndStealData(){(0,i.assert)(this._status===_.Active,"Couldn't steal data from non-active data source"),this.stop();const e=this._plots,t=this._graphics.extract();return this._plots=new o.PlotList((0,
h.studyPlotFunctionMap)(this._metaInfo),h.studyEmptyPlotValuePredicate),{plots:e,graphics:t}}dataCleared(){return this._dataCleared}dataUpdated(){return this._dataUpdated}moveData(e){this._ongoingDataUpdate=this._ongoingDataUpdate.then((()=>{this._plots.move(e)}))}pendingUpdatesReady(){return this._ongoingDataUpdate}_createStudyError(e){return{type:d.StudyStatusType.Error,errorDescription:this._getStudyErrorDescription(e)}}_getStudyErrorDescription(e){return"string"==typeof e?{error:e.split(":",2)[0]}:e}_changeStatusTo(e){(0,i.assert)(this._status!==e,"Source and destination status should be distinct"),c.logNormal(`Status changed from ${_[this._status]} to ${_[e]}`),this._status=e}_createStudy(){const e=this._seriesSource.instanceId();null!==e?this._createStudyUsingParentId(e):(this._changeStatusTo(_.AwaitingParent),this._seriesSource.dataEvents().created().subscribe(this,this._onSeriesCreated,!0))}_createStudyUsingParentId(e){(0,i.assert)(this._status!==_.Active,'Status should not be "Active" when creating a study'),(0,i.assert)(this._studyStatus.type===d.StudyStatusType.Undefined,'Study status should be "Undefined" when creating a study'),(0,i.assert)(null===this._studyId,"Study id should be empty when creating a study"),this._studyId=(0,l.makeNextStudyId)(),this._gateway.createStudy(this._studyId,this._turnaround(),e,n.StudyMetaInfo.getStudyIdWithLatestVersion(this.metaInfo(),this._forceUseExclamationMark),(0,i.ensureNotNull)(this._inputs),this._onMessage.bind(this),{id:this._metaInfo.id}),this._changeStatusTo(_.AwaitingFirstDataUpdate)}_onGatewayIsConnectedChanged(e){e?this._onGatewayConnected():this._onGatewayDisconnected()}_onGatewayConnected(){this._status===_.AwaitingConnection&&this._createStudy()}_onGatewayDisconnected(){this._status!==_.Idle&&this._status!==_.AwaitingConnection&&(this._studyId=null,this._changeStatusTo(_.AwaitingConnection),this._studyStatus.type!==d.StudyStatusType.Undefined&&this._onStudyStatusChangedTo({type:d.StudyStatusType.Undefined})),this._turnaroundCounter=1}_onSeriesCreated(){this._status===_.AwaitingParent&&this._createStudyUsingParentId((0,i.ensure)(this._seriesSource.instanceId()))}_onStudyStatusChangedTo(e){const t=this._studyStatus;this._studyStatus=e,c.logNormal(`Study status type changed from ${d.StudyStatusType[t.type]} to ${d.StudyStatusType[e.type]}`),this._studyStatusChanged.fire(t,e)}_onMessage(e){if("data_update"===e.method){const{customId:t,turnaround:s,plots:r,nonseries:o}=e.params;t===this._studyId&&this._checkTurnaround(s)&&this._onDataUpdate(r,(0,i.ensureDefined)(o))}else if("study_loading"===e.method){const[t,s]=e.params;t===this._studyId&&this._checkTurnaround(s)&&this._onStudyLoading(e.time)}else if("study_completed"===e.method){const[t,s]=e.params;t===this._studyId&&this._checkTurnaround(s)&&this._onStudyCompleted(e.time)}else if("study_error"===e.method){const[t,s,i,r]=e.params;t===this._studyId&&this._checkTurnaround(s)&&this._onStudyError(i,r,e.time)}else"clear_data"===e.method&&this._checkTurnaround(e.params.turnaround)&&this.clearData()}
_onDataUpdate(e,t){const s=(0,a.unpackNonSeriesData)(t.d);return this._ongoingDataUpdate=this._ongoingDataUpdate.then((()=>s),(()=>s)).then(this._onDataUnpacked.bind(this,e,t.indexes)),this._ongoingDataUpdate}_onDataUnpacked(e,t,s){this._status!==_.Idle&&(this._status===_.AwaitingFirstDataUpdate&&(this._changeStatusTo(_.Active),this.clearData()),this._mergePlots(e),null!==s&&(s.indexes_replace?((0,i.assert)("nochange"!==t),this._graphics.replaceIndexesTo(t)):("nochange"!==t&&this._graphics.replaceIndexesTo(t),void 0!==s.graphicsCmds&&this._graphics.processCommands(s.graphicsCmds))),this._dataUpdated.fire(e,s,t))}_onStudyLoading(e){this._onStudyStatusChangedTo({type:d.StudyStatusType.Loading,startTime:Date.now()})}_onStudyError(e,t,s){this.clearData(),this._onStudyStatusChangedTo(this._createStudyError(e))}_onStudyCompleted(e){this._onStudyStatusChangedTo({type:d.StudyStatusType.Completed})}_mergePlots(e){this._plots.merge(e)}_turnaround(){return`${this._turnaroundPrefix}${this._turnaroundCounter}`}_checkTurnaround(e){const t=this._turnaround();return e===t||e===this._seriesSource.turnaround()||e===`${this._seriesSource.turnaround()}_${t}`}}},231406:(e,t,s)=>{"use strict";s.r(t),s.d(t,{study_ScriptWithDataOffset:()=>l});var i=s(650151),r=s(810715),o=s(132565),a=s(280775),n=s(587482);class l extends a.Study{constructor(e,t,s,i,r,a){super(e,t,s,i,r,a),this._underlyingData=new o.PlotList((0,n.studyPlotFunctionMap)(i),n.studyEmptyPlotValuePredicate)}clearData(){super.clearData(),this._underlyingData.clear()}_mergeData(e){this._invalidateLastNonEmptyPlotRowCache();const t=this._underlyingData.firstIndex();this._underlyingData.merge(e),t!==this._underlyingData.firstIndex()&&(this._data=new o.PlotList((0,n.studyPlotFunctionMap)(this._metaInfo.value()),n.studyEmptyPlotValuePredicate));let s=null;const i=this._data.lastIndex()??this._underlyingData.firstIndex(),a=this._underlyingData.lastIndex();if(null!==i&&null!==a)for(const e of this._underlyingData.rangeIterator(i,a))null===s&&(s=e),this._data.add(e.index,(0,r.clone)(e.value));for(const e of this._plotsForStrategyProcessing()){new h(e.strategyIndex,e.targetIndex,i).rebuildData(this._data)}return s}_plotsForStrategyProcessing(){const e=this._metaInfo.value(),t=[];return e.plots.forEach(((s,r)=>{if("dataoffset"!==s.type)return;const o=e.plots.findIndex((e=>e.id===s.target));(0,i.assert)(o>=0,`target plot not found for strategy plot ${s.id}`),t.push({strategyIndex:r,targetIndex:o})})),t}}class h{constructor(e,t,s){this._strategyPlotIndex=e,this._targetPlotIndex=t,this._startIndex=s}rebuildData(e){const t=this._targetPlotIndex+1,s=this._strategyPlotIndex+1;let i=null,r=null;const o=this._startIndex??e.firstIndex(),a=e.lastIndex();if(null!==o&&null!==a)for(const{index:n,value:l}of e.rangeIterator(o,a)){const o=l[t],a=l[s]?Math.round(l[s]):null;if(l[t]=null,l[s]=null,!a||a>0)continue;const h=n+a,u={pointIndex:h,value:o};if(r){if(r.pointIndex!==u.pointIndex){if(h>=0){const s=e.valueAt(h);s&&(s[t]=o)}let s=!1;if(i&&r&&(s=i.value<=r.value&&r.value<=o||i.value>=r.value&&r.value>=o),
s){if(r.pointIndex>=0){const s=e.valueAt(r.pointIndex);s&&(s[t]=null)}}else i=r;r=u}}else r=u}}}},141653:(e,t,s)=>{"use strict";s.r(t),s.d(t,{TpoPeriodicStudy:()=>u,createTpoPeriodicStudy:()=>d});var i=s(650151),r=s(870855),o=s(388741),a=s(37241),n=s(87347),l=s(340993),h=s(407232);class u extends h.TpoStudyBase{splitMergeSupported(){return!0}_splitsAndMergesActionsValue(){return this._splitsAndMerges[this._splitsAndMergesActionsKey()]??this._splitsAndMerges[this._splitsAndMergesActionsKey(1)]??null}_updateSplitsAndMergesActionsValue(e){this._splitsAndMerges[this._splitsAndMergesActionsKey()]=e,this._removeSplitsAndMergesActionsValue(1)}_removeSplitsAndMergesActionsValue(e=2){delete this._splitsAndMerges[this._splitsAndMergesActionsKey(e)],2===e&&this._removeSplitsAndMergesActionsValue(1)}_splitsAndMergesActionsKey(e=2){const t=this._model.mainSeries(),s=t.proSymbol(),i=this.properties().childs().inputs.childs(),r=[s,this._chartStyleMode||(0,o.isTimeBasedStyle)(t.style())?"common":t.style(),i.blockSize.value(),i.periodsNum.value(),i.period.value()];return e>1&&r.push(t.sessionId()),r.join(";")}_splitsAndMergesInputsDeps(){return["blockSize","periodsNum","period"]}}async function d(e,t){const s=(0,i.ensureDefined)(r.SYMBOL_STRING_DATA[18]),o=await(0,n.studyMetaInfoRepository)().findById({type:"java",studyId:s.type}),h=(0,a.prepareStudyProperties)(o,{},null,(0,n.studyMetaInfoRepository)().studyVersioning(),[]);return(0,l.addStudyInfoToMap)("study_TPOPeriodic",{studyConstructor:u,colorRotationMode:"shift"}),new u(e,h,[],o,o,!1,!0,t)}},861715:(e,t,s)=>{"use strict";s.r(t),s.d(t,{TpoSessionsStudy:()=>r});var i=s(407232);class r extends i.TpoStudyBase{splitMergeSupported(){return!1}}},928047:(e,t,s)=>{"use strict";s.r(t),s.d(t,{VolumeFootprintStudy:()=>h,createVolumeFootprintStudy:()=>u});var i=s(650151),r=s(280775),o=s(37241),a=s(87347),n=s(870855),l=s(340993);class h extends r.Study{constructor(){super(...arguments),this._rowSize=.1}rowSize(){return this._rowSize}hasStateForAlert(){return!1}paneViews(){return this._paneViews}priceScale(){return this._model.mainSeries().priceScale()}firstValue(){return this._model.mainSeries().firstValue()}stateCustomFields(){return{rowSize:this._rowSize}}restoreStateCustomFields(e){this._rowSize=e.rowSize??this._rowSize}_onDataUpdated(e,t,s,i){super._onDataUpdated(e,t,s,i),t?.indexes_replace||(this._rowSize=(t?.data).rowSize??this._rowSize)}}async function u(e){const t=(0,i.ensureDefined)(n.SYMBOL_STRING_DATA[17]),s=await(0,a.studyMetaInfoRepository)().findById({type:"java",studyId:t.type}),r=(0,o.prepareStudyProperties)(s,{},null,(0,a.studyMetaInfoRepository)().studyVersioning(),[]);return(0,l.addStudyInfoToMap)("study_VolumeFootprint",{studyConstructor:h,colorRotationMode:"shift"}),new h(e,r,[],s,s,!1)}},926801:(e,t,s)=>{"use strict";s.r(t),s.d(t,{SessionVolume2Study:()=>T});var i=s(650151),r=s(963894),o=s(444372),a=s(132565),n=s(657619),l=s(479483),h=s(833214),u=s(454184),d=s(590178),c=s(587482),_=s(196302);class p{constructor(e,t){this._rough=e,this._detailed=t}forEach(e,t){
this._mostDetailedHorizLines().forEach((s=>{e.call(t,s,s,this)}))}has(e){let t=!1;return this._mostDetailedHorizLines().forEach((s=>{t=t||s===e})),t}get size(){return this._rough.size}[Symbol.iterator](){const e=this._mostDetailedHorizLines().entries();return{[Symbol.iterator](){return this},next(){const t=e.next();return!0===t.done?t:{value:t.value[1]}}}}entries(){throw new Error("Not implemented")}keys(){throw new Error("Not implemented")}values(){throw new Error("Not implemented")}_mostDetailedHorizLines(){const e=new Map;this._rough.forEach((t=>e.set(t.startIndex,t)));for(let t=this._detailed.length-1;t>=0;t--){this._detailed[t].forEach((t=>{e.has(t.startIndex)&&e.set(t.startIndex,t)}))}return e}}class y{constructor(e,t){this._mergedHorizLinesSet=e,this._roughIterator=t}[Symbol.iterator](){return this}next(){const e=this._roughIterator.next();if(!0===e.done)return e;const t=e.value[0];return{value:[t,(0,i.ensureDefined)(this._mergedHorizLinesSet.get(t))]}}}class m{constructor(e,t){this._roughHorizLineSets=e,this._detailedHorizLineSets=t}forEach(e,t){this._roughHorizLineSets.forEach(((s,r)=>{e.call(t,(0,i.ensureDefined)(this.get(r)),r,this)}))}get(e){const t=this._roughHorizLineSets.get(e);if(void 0!==t)return new p(t,this._detailedHorizLineSets.map((t=>t.get(e))).filter((e=>void 0!==e)))}has(e){return this._roughHorizLineSets.has(e)}get size(){return this._roughHorizLineSets.size}[Symbol.iterator](){return new y(this,this._roughHorizLineSets.entries())}entries(){throw new Error("Not implemented")}keys(){throw new Error("Not implemented")}values(){throw new Error("Not implemented")}}class S{constructor(e,t){this._rough=e,this._detailed=t}forEach(e,t){this._rough.forEach(((s,i)=>{for(const s of this._detailed){const r=s.get(i);if(void 0!==r)return void e.call(t,r,i,this)}e.call(t,s,i,this)}))}get(e){for(const t of this._detailed){const s=t.get(e);if(void 0!==s)return s}return this._rough.get(e)}has(e){for(const t of this._detailed){if(t.has(e))return!0}return this._rough.has(e)}get size(){return this._rough.size}[Symbol.iterator](){throw new Error("Not implemented")}entries(){throw new Error("Not implemented")}keys(){throw new Error("Not implemented")}values(){throw new Error("Not implemented")}}class g{constructor(e,t){this._graphics=e,this._styleId=t}forEach(e,t){this._graphics.hhistsByTimePointIndex().forEach((s=>{s.forEach((s=>{if(s.styleId===this._styleId){const i={firstBarTime:s.firstBarTime,lastBarTime:s.lastBarTime,rate:s.rate,priceHigh:s.priceHigh,priceLow:s.priceLow};e.call(t,i,i,this)}}))}))}has(e){let t=!1;return this._graphics.hhistsByTimePointIndex().forEach((s=>{s.forEach((s=>{s.styleId===this._styleId&&s===e&&(t=!0)}))})),t}get size(){let e=0;return this._graphics.hhistsByTimePointIndex().forEach((t=>{t.forEach((t=>{t.styleId===this._styleId&&e++}))})),e}[Symbol.iterator](){const e=this._graphics.hhistsByTimePointIndex()[Symbol.iterator]();let t,s;return{[Symbol.iterator](){return this},next(){for(;;){if(void 0===t&&(s=void 0,t=e.next(),!t.done&&!t.value))return{done:!0,value:void 0};const i=t.value
;s||(s=i[Symbol.iterator]());const r=s.next();if(!r.done)return{value:r.value};{const e=t;if(t=void 0,r.value)return{value:r.value,done:e.done}}}}}}entries(){throw new Error("Not implemented")}keys(){throw new Error("Not implemented")}values(){throw new Error("Not implemented")}}class v{constructor(e,t){this._graphicsInfo=e,this._graphics=t}forEach(e,t){if(void 0===this._graphicsInfo.hhists)return;const s=Object.keys(this._graphicsInfo.hhists);for(const r of s)e.call(t,(0,i.ensureDefined)(this.get(r)),r,this)}get(e){if(void 0!==this._graphicsInfo.hhists)return new g(this._graphics,e)}has(e){return void 0!==this._graphicsInfo.hhists&&e in this._graphicsInfo.hhists}get size(){return void 0===this._graphicsInfo.hhists?0:Object.keys(this._graphicsInfo.hhists).length}[Symbol.iterator](){const e=Object.keys(this._graphicsInfo?.hhists??{})[Symbol.iterator]();return{[Symbol.iterator](){return this},next:()=>{const t=e.next();return t.done?{done:!0,value:t.value?[t.value,(0,i.ensureDefined)(this.get(t.value))]:void 0}:{value:[t.value,(0,i.ensureDefined)(this.get(t.value))],done:t.done}}}}entries(){throw new Error("Not implemented")}keys(){throw new Error("Not implemented")}values(){throw new Error("Not implemented")}}class f{constructor(e,t,s){this._graphicsInfo=e,this._roughStudyGraphics=t,this._detailedStudyGraphics=s}horizlines(){return new m(this._roughStudyGraphics.horizlines(),this._detailedStudyGraphics.map((e=>e.horizlines())))}vertlines(){return this._roughStudyGraphics.vertlines()}lines(){return this._roughStudyGraphics.lines()}hlines(){return this._roughStudyGraphics.hlines()}textmarks(){return this._roughStudyGraphics.textmarks()}shapemarks(){return this._roughStudyGraphics.shapemarks()}backgrounds(){return this._roughStudyGraphics.backgrounds()}polygons(){return this._roughStudyGraphics.polygons()}trendchannels(){return this._roughStudyGraphics.trendchannels()}hhists(){return new v(this._graphicsInfo,this)}hhistsByTimePointIndex(){return new S(this._roughStudyGraphics.hhistsByTimePointIndex(),this._detailedStudyGraphics.map((e=>e.hhistsByTimePointIndex())))}dwglabels(){return this._roughStudyGraphics.dwglabels()}dwglines(){return this._roughStudyGraphics.dwglines()}dwgpolylines(){return this._roughStudyGraphics.dwgpolylines()}dwgboxes(){return this._roughStudyGraphics.dwgboxes()}dwgtables(){return this._roughStudyGraphics.dwgtables()}dwgtablecells(){return this._roughStudyGraphics.dwgtablecells()}dwglinefills(){return this._roughStudyGraphics.dwglinefills()}tpos(){return this._roughStudyGraphics.tpos()}tpoBlockSets(){return this._roughStudyGraphics.tpoBlockSets()}tpoLevels(){return this._roughStudyGraphics.tpoLevels()}tpoVolumeRows(){return this._roughStudyGraphics.tpoVolumeRows()}tpoSummaryInfo(){return this._roughStudyGraphics.tpoSummaryInfo()}logs(){return this._roughStudyGraphics.logs()}performance(){return this._roughStudyGraphics.performance()}footprints(){return this._roughStudyGraphics.footprints()}footprintLevels(){return this._roughStudyGraphics.footprintLevels()}}var b=s(437642),w=s(87347)
;const C="tv-volumebyprice",x=(0,n.studyIdString)("VbPSessionsRough",C),P=(0,n.studyIdString)("VbPSessionsDetailed",C),I=(0,n.studyIdString)("VbPSessionsDetailedAlerts",C),D=Number.MAX_SAFE_INTEGER,V=o.t(null,void 0,s(419431));class T extends h.VolumeProfileWithPriceRangeStudy{constructor(e,t,s,i,o,a){super(e,t,s,i,o,a),this._isPackageAvailable=k(),this._alertsStudyMetaInfo=null,this._roughDataSource=null,this._detailedDataSource=null,this._detailedDataBuffer=new r.CircularBuffer(7),this._loadedGraphics=null,this._loadedPlots=null,this._mergedGraphics=(0,_.emptyStudyGraphics)(),this._mergedPlots=this._emptyPlotList(),this._isPackageAvailable||this._setStatus({type:u.StudyStatusType.Error,errorDescription:{error:V}}),this._setAlertsStudyMetaInfo()}destroy(){null!==this._detailedDataSource&&(this._detailedDataSource.dataUpdated().unsubscribe(this,this._onRoughOrDetailedDataUpdated),this._detailedDataSource.dataCleared().unsubscribe(this,this._onRoughOrDetailedDataCleared),this._detailedDataSource.studyStatusChanged().unsubscribe(this,this._onStudyStatusChanged),this._detailedDataSource.destroy(),this._detailedDataSource=null),null!==this._roughDataSource&&(this._roughDataSource.dataUpdated().unsubscribe(this,this._onRoughOrDetailedDataUpdated),this._roughDataSource.dataCleared().unsubscribe(this,this._onRoughOrDetailedDataCleared),this._roughDataSource.studyStatusChanged().unsubscribe(this,this._onStudyStatusChanged),this._roughDataSource.destroy(),this._roughDataSource=null),super.destroy()}data(){return this._loadedPlots||this._mergedPlots}graphics(){return this._loadedGraphics||this._mergedGraphics}clearData(){null!==this._roughDataSource&&this._roughDataSource.clearData(),null!==this._detailedDataSource&&this._detailedDataSource.clearData()}restoreData(e){this._invalidateLastNonEmptyPlotRowCache(),this._loadedPlots=this._emptyPlotList(),this._loadedPlots.restoreState(e),void 0!==e.graphics&&(this._loadedGraphics=(0,_.loadStudyGraphics)(e.graphics)),this._postProcessGraphics(),this._setStatus({type:u.StudyStatusType.Completed},!0)}isFailed(){return!this._isPackageAvailable||null!==this._roughDataSource&&this._roughDataSource.studyStatus().type===u.StudyStatusType.Error||null!==this._detailedDataSource&&this._detailedDataSource.studyStatus().type===u.StudyStatusType.Error}isLoading(){return null!==this._roughDataSource&&this._roughDataSource.studyStatus().type===u.StudyStatusType.Loading||null!==this._detailedDataSource&&this._detailedDataSource.studyStatus().type===u.StudyStatusType.Loading}isStarted(){return null!==this._roughDataSource&&this._roughDataSource.isStarted()||null!==this._detailedDataSource&&this._detailedDataSource.isStarted()}sourceId(){throw new Error("Invalid operation")}idForAlert(){return super._collectDepsForAlert().idForAlert}canHaveChildren(){return!1}isChildStudy(){return!1}hasChildren(){return!1}getAllChildren(){return[]}isHibernated(){
return!(this.isVisible()||null!==this._roughDataSource&&this._roughDataSource.isStarted()||null!==this._detailedDataSource&&this._detailedDataSource.isStarted())}_alertMetaInfo(){return this._alertsStudyMetaInfo}_stopStudyOnServer(){this._chartApi&&this._chartApi.isConnected().value()&&(this._roughDataSource?.isStarted()&&this._roughDataSource.stop(),this._detailedDataSource?.isStarted()&&this._detailedDataSource.stop())}async _startAfterSymbolsResolved(e,t){if(!this.isStarted()||this.isRestarting())if(this._restarting=!1,this._isPackageAvailable=k(),this._isPackageAvailable){if(this._allInputsAreValid()){if(this._inputs=this._apiInputs(),null===this._roughDataSource){const e=await(0,w.studyMetaInfoRepository)().findById({type:"java",studyId:x});this._roughDataSource=new d.ExtendedStudyDataSource(this.model().chartApi(),this.model().mainSeries(),"vbpsessionsrough_",e),this._roughDataSource.studyStatusChanged().subscribe(this,this._onStudyStatusChanged),this._roughDataSource.dataCleared().subscribe(this,this._onRoughOrDetailedDataCleared),this._roughDataSource.dataUpdated().subscribe(this,this._onRoughOrDetailedDataUpdated)}else this._roughDataSource.stop();if(this._roughDataSource.setInputs(L(this._roughDataSource.metaInfo().inputs,this._inputs)),null===this._detailedDataSource){const e=await(0,w.studyMetaInfoRepository)().findById({type:"java",studyId:P});this._detailedDataSource=new d.ExtendedStudyDataSource(this.model().chartApi(),this.model().mainSeries(),"vbpsessionsdetailed_",e),this._detailedDataSource.studyStatusChanged().subscribe(this,this._onStudyStatusChanged),this._detailedDataSource.dataCleared().subscribe(this,this._onRoughOrDetailedDataCleared),this._detailedDataSource.dataUpdated().subscribe(this,this._onRoughOrDetailedDataUpdated)}else this._detailedDataSource.stop();this._detailedDataSource.setInputs(R(this._inputs)),this._roughDataSource.start(),this._detailedDataSource.start(),this._createStudyGraphics(),this._subscribeToSessionId(),this._setStatus(this._statusValueFromDataSources()),this._onStart.fire(),null===this._alertsStudyMetaInfo&&await this._setAlertsStudyMetaInfo()}}else this._setStatus({type:u.StudyStatusType.Error,errorDescription:{error:V}})}async _changeInputsImpl(e,t){if(null!==this._roughDataSource){if((0,l.areStudyInputsEqual)(this._roughDataSource.metaInfo().inputs,t,e)){if(null!==this._detailedDataSource){const t=R(e),s=(0,l.rangeDependentStudyInputsToTimeRange)(t);if(this._moreDetailedDataRequiredForTimeRange(s)){if(this._detailedDataSource.isActive()){const e=this._detailedDataSource.stopAndStealData();if(e.plots.size()>0||!(0,_.isStudyGraphicsEmpty)(e.graphics)){const t=(0,i.ensureNotNull)(this._detailedDataSource.inputs());e.plots=this._extractEffectiveDetailedPlotValues(e.plots,t),this._detailedDataBuffer.enqueue({lod:A(t),value:e})}}this._detailedDataSource.setInputs(t),this._detailedDataSource.start()}}}else this._inputs=e,this._roughDataSource.setInputs(L(this._roughDataSource.metaInfo().inputs,e)),this._detailedDataBuffer.clear(),
null!==this._detailedDataSource&&this._detailedDataSource.setInputs(R(e));null!==this._detailedDataSource&&this._createStudyGraphics()}}_collectDepsForAlert(){const e=super._collectDepsForAlert();if(null===this._alertsStudyMetaInfo)throw new Error("Alerts study metainfo is not ready yet. Have you checked the alert availability using hasStateForAlert()?");return{...e,studyDependencies:e.studyDependencies.map((e=>{const t=e.study===M(this.metaInfo());return{id:e.id,inputs:t?(s=e.inputs,{...s,subscribeRealtime:!0}):e.inputs,study:t&&null!==this._alertsStudyMetaInfo?M(this._alertsStudyMetaInfo):e.study};var s}))}}_getAlertCreationAvailable(){return!1}_statusValueFromDataSources(){const e={type:u.StudyStatusType.Undefined};if(null===this._roughDataSource||null===this._detailedDataSource)return e;const t=this._roughDataSource.studyStatus(),s=this._detailedDataSource.studyStatus();if(t.type===u.StudyStatusType.Error)return t;if(s.type===u.StudyStatusType.Error)return s;if(t.type===u.StudyStatusType.Loading||s.type===u.StudyStatusType.Loading){const e=t.type===u.StudyStatusType.Loading?t.startTime:1/0,i=s.type===u.StudyStatusType.Loading?s.startTime:1/0;return{type:u.StudyStatusType.Loading,startTime:Math.min(e,i)}}return t.type===u.StudyStatusType.Completed?{type:u.StudyStatusType.Completed}:e}_emptyPlotList(){return new a.PlotList((0,c.studyPlotFunctionMap)(this.metaInfo()),c.studyEmptyPlotValuePredicate)}_createStudyPlots(){const e=(0,i.ensureNotNull)(this._roughDataSource),t=(0,i.ensureNotNull)(this._detailedDataSource);this._mergedPlots.clear();const s=[];s.push({lod:D,value:e.plots()}),this._detailedDataBuffer.forEach((e=>{s.push({lod:e.lod,value:e.value.plots})}));const r=(0,i.ensureNotNull)(t.inputs());s.push({lod:A(r),value:this._extractEffectiveDetailedPlotValues(t.plots(),r)}),s.sort(((e,t)=>t.lod-e.lod));for(const e of s)e.value.each(((e,t)=>{for(const e of t)if(void 0===e)return!1;return this._mergedPlots.add(e,t),!1}));this._invalidateLastNonEmptyPlotRowCache()}_createStudyGraphics(){const e=(0,i.ensureNotNull)(this._roughDataSource),t=(0,i.ensureNotNull)(this._detailedDataSource),s=[];this._detailedDataBuffer.forEach((e=>{s.push({lod:e.lod,value:e.value.graphics})})),void 0!==this._inputs&&s.push({lod:A(this._inputs),value:t.graphics()}),s.sort(((e,t)=>e.lod-t.lod));const r=s.map((e=>e.value));this._mergedGraphics=new f(this.metaInfo().graphics,e.graphics(),r),this._postProcessGraphics()}_extractEffectiveDetailedPlotValues(e,t){const s=(0,l.rangeDependentStudyInputsToTimeRange)(t),r=this.model().timeScale().timePointToIndex(s.from),o=this.model().timeScale().timePointToIndex(s.to);return e.size()>0?e.range(r||(0,i.ensureNotNull)(e.firstIndex()),o||(0,i.ensureNotNull)(e.lastIndex())):this._emptyPlotList()}_moreDetailedDataRequiredForTimeRange(e){const{from:t,to:s}=e;let r=!1;return(0,i.ensureNotNull)(this._roughDataSource).graphics().hhistsByTimePointIndex().forEach(((e,i)=>{if(r)return;if(null===i)return;const o=this.model().timeScale().indexToTimePoint(i);if(null===o||o<t||s<o)return;const a=s-t;let n=!1
;this._detailedDataBuffer.forEach((e=>{if(e.lod<=a){e.value.graphics.hhistsByTimePointIndex().has(i)&&(n=!0)}})),n||(r=!0)})),r}_onStudyStatusChanged(){const e=this.status();this._setStatus(this._statusValueFromDataSources()),e.type!==this.status().type&&this._updateAlertCreationAvailable(),(0,i.ensureDefined)(this._statusView).update((0,b.sourceChangeEvent)(this.id()))}_onRoughOrDetailedDataCleared(){null!==this._roughDataSource&&null!==this._detailedDataSource&&(this._invalidateLastNonEmptyPlotRowCache(),this._detailedDataBuffer.clear(),this._createStudyPlots(),this._createStudyGraphics(),this.updateAllViews((0,b.sourceChangeEvent)({sourceId:this.id(),clearData:!0})),this.model().updateSource(this))}_onRoughOrDetailedDataUpdated(e){null!==this._roughDataSource&&null!==this._detailedDataSource&&(e.length>0&&this._createStudyPlots(),this.updateAllViews((0,b.sourceChangeEvent)(this.id())),this.model().updateSource(this))}async _setAlertsStudyMetaInfo(){this._alertsStudyMetaInfo=await(0,w.studyMetaInfoRepository)().findById({type:"java",studyId:I}),this._updateAlertCreationAvailable()}}function k(){return void 0!==window.pro&&window.pro.hasPackage(C)}function L(e,t){const s={};for(const i of e)s[i.id]=t[i.id];return s}function R(e){const t=e.last_visible_bar_time-e.first_visible_bar_time;return{...e,first_visible_bar_time:e.first_visible_bar_time-t,last_visible_bar_time:e.last_visible_bar_time+t}}function A(e){const{from:t,to:s}=(0,l.rangeDependentStudyInputsToTimeRange)(e);return s-t}function M(e){return`${e.id}-${e.version}`}},238308:(e,t,s)=>{"use strict";s.r(t),s.d(t,{SessionVolumeStudy:()=>h,createSessionVolumeStudy:()=>u});var i=s(650151),r=s(870855),o=s(37241),a=s(87347),n=s(340993),l=s(833214);class h extends l.VolumeProfileWithPriceRangeStudy{constructor(e,t,s,i,r,o,a=!1){super(e,t,s,i,r,o),this._chartStyleMode=a}paneViews(e){return this._chartStyleMode?this._paneViews:super.paneViews(e)}priceScale(){return this._chartStyleMode?this._model.mainSeries().priceScale():super.priceScale()}hasStateForAlert(){return!this._chartStyleMode&&super.hasStateForAlert()}}async function u(e){const t=(0,i.ensureDefined)(r.SYMBOL_STRING_DATA[20]),s=await(0,a.studyMetaInfoRepository)().findById({type:"java",studyId:t.type}),l=(0,o.prepareStudyProperties)(s,{},null,(0,a.studyMetaInfoRepository)().studyVersioning(),[]);return(0,n.addStudyInfoToMap)("study_VbPSessions",{studyConstructor:h,colorRotationMode:"shift"}),new h(e,l,[],s,s,!1,!0)}},833214:(e,t,s)=>{"use strict";s.r(t),s.d(t,{VbPVisibleWrapper:()=>y,VolumeProfileBaseStudy:()=>c,VolumeProfileStudyWithThemedColors:()=>_,VolumeProfileWithPriceRangeStudy:()=>p});var i=s(650151),r=s(820028),o=s(397535);var a=s(597331),n=s(534328);class l extends a.PriceAxisView{constructor(e,t){super(),this._source=e,this._styleId=t}_updateRendererData(e,t,s){e.visible=!1;const i=this._source.priceScale(),r=this._source.properties().childs();if(!i||i.isEmpty()||!r.visible.value())return;const o=this._source.properties().childs().graphics.childs().horizlines?.childs()[this._styleId].childs()
;if(!(o&&o.visible&&o.visible.value()&&this._isLabelVisibleAccordinglyToProperties()))return;const a=this._source.model().timeScale().logicalRange(),l=this._source.firstValue();if(null===l||null===a)return;const h={price:NaN,time:-1/0},u=this._source.graphics().horizlines().get(this._styleId);if(void 0===u)return;for(const e of u){if(void 0===e.level)continue;const t=a.contains(e.startIndex,!0);t===a.contains(e.endIndex,!0)&&0!==t||h.time<e.endIndex&&(h.time=e.endIndex,h.price=e.level)}if(isNaN(h.price))return;const d=(0,n.resetTransparency)(o.color.value());s.background=d,s.textColor=this.generateTextColor(d),s.coordinate=i.priceToCoordinate(h.price,l),e.text=i.formatPrice(h.price,l,{signPositive:i.isPercentage()}),e.visible=!0}_isLabelVisibleAccordinglyToProperties(){const e=this._source.model().properties().childs().scalesProperties.childs();return(e.showStudyLastValue.value()||e.showFundamentalLastValue.value())&&this._source.properties().childs().showLabelsOnPriceScale.value()}}var h=s(280775),u=s(12384),d=s(78358);class c extends h.Study{preferredZOrder(){return 0}async _createGraphicsPaneViews(){const e=this.metaInfo().graphics,t=this.model(),i={regularPaneViews:[],forceOverlayPaneViews:[]},r=this._needExtendToBarsEnding();if(e.hhists){const{HHistPaneView:e}=await Promise.all([s.e(63317),s.e(27860),s.e(9517),s.e(20507)]).then(s.bind(s,910529)),o=this.properties().childs().graphics.childs().polygons?.childs();i.regularPaneViews.push(new e(this,t,void 0,o?.histBoxBg,r))}if(e.horizlines){const{HorizLinePaneView:e}=await Promise.all([s.e(63317),s.e(27860),s.e(9517),s.e(20507)]).then(s.bind(s,614478));i.regularPaneViews.push(new e(this,t,void 0,r))}return i}_createGraphicsPriceAxisViews(){return Object.keys(this.metaInfo().graphics.horizlines??{}).map((e=>new l(this,e)))}_createStudyPlotPaneView(e){return new u.StudyPlotPaneView(this,this._series,this._model,e,this._needExtendToBarsEnding())}_apiInputs(){return{...super._apiInputs(),mapRightBoundaryToBarStartTime:!!this._needExtendToBarsEnding()||void 0}}_needExtendToBarsEnding(){return void 0!==this.metaInfo().defaults.inputs?.mapRightBoundaryToBarStartTime}}class _ extends c{constructor(e,t,s,i,r,o){t.setThemedColors((0,d.volumeProfileThemedColors)(t.childs().graphics.childs().polygons?.hasChild("histBoxBg"))),super(e,t,s,i,r,o)}}class p extends _{priceRange(e,t,s){if(s.targetPriceScale!==this.priceScale())return null;let r=!1;this.graphics().hhists().forEach(((e,t)=>{r=r||(0,i.ensureDefined)(this.properties().childs().graphics.childs().hhists?.childs()[t]).value()}));const a=function(e,t,s,i){let r=null;return e.forEach((e=>{e.forEach((e=>{const i=e.firstBarTime;null!==i&&i<=s&&e.lastBarTime>=t&&(null===r?r={low:{l:e.priceLow,h:e.priceHigh},high:{h:e.priceHigh}}:(e.priceLow<r.low.l&&(r.low.l=e.priceLow,r.low.h=e.priceHigh),r.high.h=Math.max(r.high.h,e.priceHigh)))}))})),null===r?null:i?new o.PriceRange(r.low.l-.8*(r.low.h-r.low.l),r.high.h):new o.PriceRange(r.low.l,r.high.h)}(this.graphics().hhists(),e,t,r);if(null===a)return null;const n=(0,
i.ensureNotNull)(this.priceScale());return n.isLog()?new o.PriceRange(n.priceToLogical(a.minValue()),n.priceToLogical(a.maxValue())):a}}class y extends p{alertCreationAvailable(){return new r.WatchedValue(!1).readonly()}_needExtendToBarsEnding(){return!1}}},78358:(e,t,s)=>{"use strict";s.d(t,{volumeProfileThemedColors:()=>_});var i=s(553407);const{colorColdGray200:r,colorColdGray900:o,colorBerryPink400Alpha50:a,colorBerryPink400Alpha75:n,colorSkyBlue400Alpha50:l,colorSkyBlue400Alpha5:h,colorSkyBlue400Alpha75:u,colorSkyBlue500:d}=i.colors,c={val:[o,r],poc:[o,r],vah:[o,r],developingPoc:[o,r],developingVA:[d,d],valuesColor:[o,r],volumeColorUp:[l,l],volumeColorDown:[a,a],valueAreaColorUp:[u,u],valueAreaColorDown:[n,n],histogramBoxColor:[h,h]};function _(e){const t="graphics.horizlines",s="graphics.hhists",i=[{path:`${t}.pocLines.color`,colors:c.poc},{path:`${t}.vahLines.color`,colors:c.vah},{path:`${t}.valLines.color`,colors:c.val},{path:`${s}.histBars2.colors.0`,colors:c.volumeColorUp},{path:`${s}.histBars2.colors.1`,colors:c.volumeColorDown},{path:`${s}.histBars2.valuesColor`,colors:c.valuesColor},{path:`${s}.histBarsVA.colors.0`,colors:c.valueAreaColorUp},{path:`${s}.histBarsVA.colors.1`,colors:c.valueAreaColorDown},{path:`${s}.histBarsVA.valuesColor`,colors:c.valuesColor},{path:"styles.developingPoc.color",colors:c.developingPoc},{path:"styles.developingVAHigh.color",colors:c.developingVA},{path:"styles.developingVALow.color",colors:c.developingVA}];return e&&i.push({path:"graphics.polygons.histBoxBg.color",colors:c.histogramBoxColor}),i}},42212:(e,t,s)=>{"use strict";s.r(t),s.d(t,{VolumeStudy:()=>o});var i=s(280775),r=s(412338);class o extends i.Study{base(){return 1}destroy(){super.destroy()}showOnTopOnHovering(){return!1}chartFloatingTooltipView(){return this._model.paneForSource(this)===this._model.mainPane()?null:super.chartFloatingTooltipView()}_titleInParts(e,t,s,i,r,o){const a=super._titleInParts(e,t,s,i,r),n=this._getVolumeUnit();return n&&!o&&(a[0]+=` · ${n}`),a}_skippedTitleInputs(){const e=super._skippedTitleInputs();0===this._getPlotDisplayValue("vol_ma")&&!e.includes("length")&&e.push("length");return e}_getVolumeUnit(){const e=this.symbolSource().symbolInfo();return e?(0,r.getVolumeUnit)({...e,currency:e.original_currency_code||e.currency_code}):void 0}}},845464:(e,t,s)=>{"use strict";s.r(t),s.d(t,{study_Overlay:()=>Se});var i=s(650151),r=s(517241),o=s(638456),a=s(735566),n=s(280775),l=s(766409),h=s(756163),u=s(326488),d=s(388741),c=s(905520),_=s(955722),p=s(213377),y=s(14767),m=s(616427),S=s(641208),g=s(132565),v=s(584785),f=s(536794),b=s(397535),w=s(820028),C=s(191914),x=s(197860),P=s(973592),I=s(597331),D=s(980057),V=s(316250),T=s(534328);const k=p.enabled("force_exchange_as_title");class L extends I.PriceAxisView{constructor(e){super(),this._source=e}_updateRendererData(e,t,s){e.visible=!1,t.visible=!1;const r=this._source.lastValueData("close",!1);if(r.noData)return;const o=this._source.model(),a=this._source.priceScale();if(null===a)return;if(!o.isPriceScaleVisible(a))return
;const n=o.timeScale().visibleBarsStrictRange(),l=o.mainSeries().bars().lastIndex();if(null===n||null===l)return;if(l<=n.lastBar())s.background=(0,T.resetTransparency)(r.color),s.textColor=this.generateTextColor(r.color),e.borderVisible=!1,t.borderVisible=!1;else{const a=o.backgroundColorAtYPercentFromTop(r.coordinate/(0,i.ensureNotNull)(o.paneForSource(this._source)).height());s.background=a,s.textColor=(0,T.resetTransparency)(r.color),s.borderColor=s.textColor,e.borderVisible=!0,t.borderVisible=!0}s.coordinate=r.coordinate,s.floatCoordinate=r.floatCoordinate;const h=this._source.model().properties().childs().scalesProperties.childs();h.showSeriesLastValue.value()&&(e.text=(0,V.getCurrentModePriceText)(a,r),h.seriesLastValueMode.value()!==D.PriceAxisLastValueMode.LastPriceAndPercentageValue?e.secondLine="":e.secondLine=(0,V.getOppositeModePriceText)(a,r),e.visible=!0),t.text="",h.showSymbolLabels.value()&&(t.text=this._paneText(),t.visible=t.text.length>0)}_paneText(){let e="";const t=this._source.symbolInfo();return k?e=(0,d.displayedSymbolExchange)(t):this._source.model().properties().childs().scalesProperties.childs().showSymbolLabels.value()&&(e=(0,d.displayedSymbolName)(t)),e}}var R=s(514294);class A extends R.PriceLineAxisView{constructor(e){super(),this._study=e}_value(){return this._study.lastValueData("",!0)}_priceLineColor(e){return e}_lineWidth(){return 1}_isVisible(){const e=this._study.model().properties().childs().scalesProperties.childs().showSeriesLastValue.value();return this._study.properties().childs().showPriceLine.value()&&e}}var M=s(257145),B=s(654397);class E{constructor(e){this._lineRenderer=new B.HorizontalLineRenderer,this._visible=!1,this._source=e}update(){if(this._visible=!1,!this._source.properties().childs().showPriceLine.value())return;const e=this._source.lastValueData("",!0);e.noData||(this._visible=!0,this._lineRenderer.setData({y:e.coordinate,color:e.color,linewidth:1,linestyle:M.LINESTYLE_DOTTED,visible:this._visible}))}renderer(){return this._visible?this._lineRenderer:null}}var N=s(55840),W=s(444372),F=s(437642),O=s(986593);class H extends O.OverlayValuesProvider{constructor(e,t){super(e,t);const s=t.properties().childs().paneProperties.childs().legendProperties.childs();this._showBarChangeProp=s.showBarChange,this._showLastDayChangeProp=s.showLastDayChange,this._showSeriesOHLCProp=s.showSeriesOHLC}getValues(e){const t=super.getValues(e),s=this._study.properties().childs(),i=s.style.value(),r=12!==i,o=this._showSeriesOHLC(),a=r&&this._showBarChangeProp.value(),n=r&&this._showLastDayChangeProp.value();if(this._showLastPriceAndChangeOnly())return t[7].visible&&=a,t[6].visible=!1,t;const l=(0,d.isPriceSourceStyle)(i),h=12!==i&&16!==i&&21!==i,u=12!==i,c=this._model.mainSeries().intervalObj().value().is1Tick(),_=o&&!l,p=o&&l;if(t[0].visible=_&&h&&!c,t[1].visible=_&&!c,t[2].visible=_&&!c,t[3].visible=_&&u,t[7].visible&&=n,t[6].visible&&=a,t[4].visible=p,16===i){const e=s.hlcAreaStyle.childs();t[1].color=e.highLineColor.value(),t[2].color=e.lowLineColor.value(),
t[3].color=e.closeLineColor.value()}return t}_showSeriesOHLC(){return this._showSeriesOHLCProp.value()}}var q=s(185419),G=s(326301);class z extends N.OverlayDataWindowView{constructor(e,t){super(e,t),this._additional=null,this._studyOverlay=e,this._backgroundColorSpawn=t.backgroundTopColor().spawn(),this._backgroundColorSpawn.subscribe(this.update.bind(this,(0,F.sourceChangeEvent)(e.id())));const s=t.properties().childs().paneProperties.childs().legendProperties.childs();this._visibilityProperty=(0,q.combineProperty)(((e,t)=>e||t),s.showBarChange.weakReference(),s.showSeriesOHLC.weakReference()),this._visibilityProperty.subscribe(this,this.update.bind(this,(0,F.sourceChangeEvent)(e.id())))}areValuesVisible(){return this._visibilityProperty.value()}additional(){return this._invalidated&&(this._updateImpl(),this._invalidated=!1),this._additional}destroy(){this._backgroundColorSpawn.destroy(),this._visibilityProperty.destroy()}_updateImpl(){super._updateImpl();{const e=(0,G.getDataVendorString)(this._studyOverlay.symbolInfo());this._additional=e?W.t(null,void 0,s(957131))+" "+e:null}}_createValuesProvider(e,t){return new H(e,t)}}var U=s(542301),Y=s(5253),$=s(616332),j=s(316661),Q=s(271747),K=s(517742),X=s(340104),Z=s(498969),J=s(351018),ee=s(735922),te=s(889858),se=s(296538),ie=s(681240),re=s(816433);class oe extends ie.StudyStatusProvider{getSplitTitle(){return this._source.titleInParts(re.TitleDisplayTarget.StatusLine,!0,void 0,!1,!1)}text(){return this._source.isActualInterval()?this._source.isFailed()?`${this._source.title(re.TitleDisplayTarget.StatusLine,!0,void 0,!1,!1)}: ${this.sourceStatusText()}`:`${this._source.title(re.TitleDisplayTarget.StatusLine,!0,void 0,!1,!1)} ${this.sourceStatusText()}`:this._source.title(re.TitleDisplayTarget.StatusLine,!0,void 0,!1,!1)}}var ae=s(20019),ne=s(490550);class le extends H{_showSeriesOHLC(){return!0}}var he=s(669110);class ue extends O.OverlayValuesProvider{getItems(){const e=this._emptyValues;e[4].visible=!1,e[5].visible=!1,e[7].visible=!1;const t=this._model.mainSeries().intervalObj().value().is1Tick(),s=21!==this._study.properties().childs().style.value();return e[0].visible=!t&&s,e[1].visible=!t,e[2].visible=!t,e[3].visible=!0,e[6].visible=!0,e}}p.enabled("legend_last_day_change");const de=(o.CheckMobile.any(),!0),ce=p.enabled("study_overlay_compare_legend_option"),_e=(p.enabled("secondary_series_extend_time_scale"),p.enabled("hide_unresolved_symbols_in_legend")),pe=!p.enabled("hide_study_overlay_legend_item"),ye=(0,a.getLogger)("Chart.StudyOverlay");function me(e,t){return null==e[t]}class Se extends n.Study{constructor(e,t,s,i,r,o){super(e,function(e){e.hasChild("currencyId")||e.addChild("currencyId",new c.Property(null)),e.hasChild("unitId")||e.addChild("unitId",new c.Property(null)),(0,se.allChartStyles)().includes(e.childs().style.value())||e.childs().style.setValueSilently(2);const t=e.childs();if(t.lineStyle.hasChild("styleType")){const e=t.lineStyle.childs(),s=e.styleType.value();let i,r;0===s&&(r=14,i=t.lineWithMarkersStyle.childs()),1===s&&(r=15,
i=t.steplineStyle.childs()),i&&(i.color.setValueSilently(e.color.value()),i.linestyle.setValueSilently(e.linestyle.value()),i.linewidth.setValueSilently(e.linewidth.value()),i.priceSource.setValueSilently(e.priceSource.value())),void 0!==r&&2===t.style.value()&&t.style.setValueSilently(r),t.lineStyle.removeProperty("styleType")}return e.addExcludedKey("currencyId",1),e.addExcludedKey("unitId",1),e}(t),s,i,r,o),this._symbolResolvingActive=new w.WatchedValue(!1),this._symbolHibernated=new w.WatchedValue(!1),this._styleToRecover=null,this._isActingAsSymbolSource=new w.WatchedValue(!0),this._realignToolsLastParams=null,this._precomputedBarStyles=new WeakMap,this._lastResolvedSymbolSource="",this._data=new g.PlotList((0,u.seriesPlotFunctionMap)(),me),this._quotesProvider=new y.QuotesProvider(void 0,e.collapsed().spawnOwnership()),de&&this._quotesProvider.quotesUpdate().subscribe(this,this._onQuotesUpdate);const a=this.properties().childs();a.currencyId.subscribe(this,this._onCurrencyChanged),a.unitId.subscribe(this,this._onUnitChanged),a.allowExtendTimeScale.subscribe(this,this._onAllowExtendTimeScaleChanged),this._onAllowExtendTimeScaleChanged(),this._model.mainSeries().properties().childs().dividendsAdjustment.subscribe(this,(()=>{this._tryChangeInputs()})),a.style.subscribe(this,this._onChartStyleChanged),a.lineStyle.childs().priceSource.subscribe(this,this._updateBarFunction),a.lineWithMarkersStyle.childs().priceSource.subscribe(this,this._updateBarFunction),a.steplineStyle.childs().priceSource.subscribe(this,this._updateBarFunction),a.areaStyle.childs().priceSource.subscribe(this,this._updateBarFunction),a.baselineStyle.childs().priceSource.subscribe(this,this._updateBarFunction),a.columnStyle.childs().priceSource.subscribe(this,this._updateBarFunction),this._updateBarFunction(),a.minTick.subscribe(this,this._onMinTickChanged),this._formatter=null,this._defaultFormatter=null,e.mainSeries().onIntervalChanged().subscribe(this,(()=>e.realignLineTools(this))),e.mainSeries().onIntervalChanged().subscribe(this,(()=>this._checkStyle())),this._conflatedChunksBuilder=new ae.ConflatedChunksBuilder(this._data,(e=>u.barFunctions[e])),this._overlayValuesProvider=new O.OverlayValuesProvider(this,e),this._overlayLegendValuesProvider=new H(this,e),this._overlayTableViewValuesProvider=new ue(this,e),this._overlayChartFloatingTooltipValuesProvider=new le(this,e)}destroy(){this._model.mainSeries().properties().childs().dividendsAdjustment.unsubscribeAll(this),this._model.mainSeries().onIntervalChanged().unsubscribeAll(this),this._quotesProvider.destroy(),super.destroy()}isActingAsSymbolSource(){return this._isActingAsSymbolSource.readonly()}precomputedBarStyle(e){return this._precomputedBarStyles.get(e)}setPrecomputedBarStyle(e,t){this._precomputedBarStyles.set(e,t)}properties(){return this._properties}barColorer(){return new te.SeriesBarColorer({data:()=>this.data(),bars:()=>this.data(),nsBars:()=>new g.PlotList,priceScale:()=>(0,i.ensureNotNull)(this.priceScale()),properties:()=>this.properties(),firstValue:()=>this.firstValue(),
barFunction:()=>this.barFunction(),lineColorAtYPercentFromTop:e=>this.lineColorAtYPercentFromTop(e)})}symbolParams(){return(0,m.symbolParams)(this)}compareSymbolParams(e){return(0,m.compareSymbolParams)(this,e,this._model.unitConversionEnabled())}async setSymbolParams(e){this._setSymbolParamsInternal(e)}symbol(){return this.properties().childs().inputs.childs().symbol.value()}symbolChanged(){return this.properties().childs().inputs.childs().symbol}onSymbolIntervalChanged(){return this.symbolChanged()}setSymbol(e){this.setSymbolParams({symbol:e})}symbolInfo(){if(!this._resolvedSymbols)return null;const e=this._properties.childs().inputs.childs().symbol.value();if(!e)return null;let t;return t=this._model.isSnapshot()?this._resolvedSymbols.___snapshot||null:this._resolvedSymbols[this._getSymbolForResolve(e)]||null,this._model.isSnapshot()&&null===t&&(t=(0,r.getObsoleteIdeaSymbolInfo)(this._resolvedSymbols,e,this.currency()),null!==t&&(this._resolvedSymbols.___snapshot=t)),t}supportsConflatedChunks(){return!0}conflatedChunks(e,t){return this._conflatedChunksBuilder.conflatedChunks(e,t)}symbolResolved(){return this.symbolsResolved()}symbolResolvingActive(){return this._symbolResolvingActive}symbolHibernated(){return this._symbolHibernated}isVisible(){const e=super.isVisible();return this._symbolHibernated.setValue(!e),e}symbolSameAsCurrent(e){return(0,m.symbolSameAsCurrent)(e,this.symbolInfo())}symbolSameAsResolved(e){return(0,S.symbolSameAsResolved)(e,this._lastResolvedSymbolSource,this.symbol())}priceSource(){const e=this.properties().childs();switch(e.style.value()){case 2:return e.lineStyle.childs().priceSource.value();case 14:return e.lineWithMarkersStyle.childs().priceSource.value();case 15:return e.steplineStyle.childs().priceSource.value();case 3:return e.areaStyle.childs().priceSource.value();case 10:return e.baselineStyle.childs().priceSource.value();case 13:return e.columnStyle.childs().priceSource.value()}return null}barFunction(){return this._barFunction}quotesProvider(){return this._quotesProvider}quotes(){return this._quotesProvider.quotes()}currency(){return this.properties().childs().currencyId.value()||null}setCurrency(e){this.setSymbolParams({currency:e})}isConvertedToOtherCurrency(){return(0,d.isConvertedToOtherCurrency)(this.symbolInfo())}unit(){return this.properties().childs().unitId.value()||null}setUnit(e){this.setSymbolParams({unit:e})}isConvertedToOtherUnit(){return(0,d.isConvertedToOtherUnit)(this.symbolInfo(),this._model.unitConversionEnabled())}style(){return this.properties().childs().style.value()}setStyle(e){this.setSymbolParams({style:e})}interval(){return this._model.mainSeries().interval()}setInterval(e){}valueAt(e,t){return this.data().search(e)?.value[t]??null}symbolSource(){return this}barsProvider(){return this}state(e,t){const s=super.state(e,t);this._model.unitConversionEnabled()||delete s.state.unitId;const r=this.symbol();return s.state.inputs.symbol=(0,d.symbolToSaveInState)(this.symbolInfo()??this._model.chartApi().lastSymbolResolveInfo(r),r),e&&((0,
i.ensureDefined)(s.data).symbols={___snapshot:this.symbolInfo()||void 0}),s}symbolTitle(e,t,s){return this.title(e,!0,{},!1,t)}title(e,t,s,i,r){const o=this._titleInParts(e,t,s,i,r);return[o[0],...o[1]??[]].join(` ${d.symbolTitleSeparator} `)}titleInParts(e,t,s,i,r){return[this.title(e,t,s,i,r)]}firstValue(){const e=this._model.timeScale().visibleBarsStrictRange();if(null===e)return null;const t=e.firstBar();if(0===this.data().size())return this._ownFirstValue;const s=this.data().search(t,v.PlotRowSearchMode.NearestRight,1);return this._ownFirstValue=null!==s?this._barFunction(s.value,0):null,this._ownFirstValue}lineColorAtYPercentFromTop(e){switch(this.style()){case 2:return this.properties().childs().lineStyle.childs().color.value();case 14:return this.properties().childs().lineWithMarkersStyle.childs().color.value();case 15:return this.properties().childs().steplineStyle.childs().color.value()}return null}lastValueData(e,t,s){const i={noData:!0},r=this.priceScale();if(this._model.timeScale().isEmpty()||null===r||r.isEmpty()||this.data().isEmpty())return i;const o=this._model.timeScale().visibleBarsStrictRange();if(null===o)return i;const a=this.properties().childs();if(!a.visible.value())return i;const n=this.nearestIndex(o.lastBar(),v.PlotRowSearchMode.NearestLeft,1);if(void 0===n)return i;const l=this.firstValue();if(null===l)return i;const h=this._lastNonEmptyPlotRow(4),u=null!==h&&o.contains(h.index),d=null!==h?h.value:null,c=t||u?d:this.data().valueAt(n);if(null===c)return i;const _=this._barFunction(c,2);if(!(0,f.isNumber)(_))return i;const p=r.priceToCoordinate(_,l);let y;switch(a.style.value()){case 0:y=c[1]<=c[4]?a.barStyle.childs().upColor.value():a.barStyle.childs().downColor.value();break;case 1:y=c[1]<=c[4]?a.candleStyle.childs().upColor.value():a.candleStyle.childs().downColor.value();break;case 9:y=c[1]<=c[4]?a.hollowCandleStyle.childs().upColor.value():a.hollowCandleStyle.childs().downColor.value();break;case 13:y=c[1]<=c[4]?a.columnStyle.childs().upColor.value():a.columnStyle.childs().downColor.value();break;case 2:y=a.lineStyle.childs().color.value();break;case 14:y=a.lineWithMarkersStyle.childs().color.value();break;case 15:y=a.steplineStyle.childs().color.value();break;case 3:y=a.areaStyle.childs().linecolor.value();break;case 16:y=a.hlcAreaStyle.childs().closeLineColor.value();break;case 10:{const e=a.baselineStyle.childs();y=p<Math.round(r.height()*(Math.abs(100-e.baseLevelPercentage.value())/100))?e.topLineColor.value():e.bottomLineColor.value();break}case 12:y=a.hiloStyle.childs().color.value();break;case 21:y=a.hlcBarsStyle.childs().color.value();break;default:throw new Error("Not supported overlay style")}const m={...r.getFormattedValues(_,l,!0),noData:!1,floatCoordinate:p,coordinate:p,color:y};return s&&(m.price=_),m}priceRange(e,t,s){if(s.forceOverlayOnly)return null;if(!(0,f.isInteger)(e))return ye.logDebug("priceRange: incorrect startBar"),null;if(!(0,f.isInteger)(t))return ye.logDebug("priceRange: incorrect endBar"),null;if(0===this.data().size())return null
;const i=this.priceSource();let r;r=null!==i?this.data().minMaxOnRangeCached(e,t,[{name:i,offset:0}]):this.data().minMaxOnRangeCached(e,t,[{name:"low",offset:0},{name:"high",offset:0}]);const o=null!==r?new b.PriceRange(r.min,r.max):null;return this._postProcessPriceRange(o,s)}bars(){return this.data()}open(e){return(0,i.ensureNotNull)(this.bars().valueAt(e))[1]}high(e){return(0,i.ensureNotNull)(this.bars().valueAt(e))[2]}low(e){return(0,i.ensureNotNull)(this.bars().valueAt(e))[3]}close(e){return(0,i.ensureNotNull)(this.bars().valueAt(e))[4]}hl2(e){return(this.high(e)+this.low(e))/2}hlc3(e){return(this.high(e)+this.low(e)+this.close(e))/3}ohlc4(e){return(this.open(e)+this.high(e)+this.low(e)+this.close(e))/4}canBeHiddenByGlobalFlag(){return!1}async start(e,t){this._formatter=null,this._defaultFormatter=null;const s=super.start(e,t);return this.priceScale()?.updateFormatter(),de&&this._setQuotesSymbol((0,_.encodeExtendedSymbolOrGetSimpleSymbolString)(this._getSymbolObject(this.symbol()))),s}stop(e){super.stop(e),this._quotesProvider.setQuotesSessionSymbol(null)}formatter(){return this._formatter||this._recreateFormatter(),(0,i.ensureNotNull)(this._formatter)}statusView(){return pe?super.statusView():null}moveItem(e,t,s){if(10===this.style()&&0===t){const t=(0,i.ensureNotNull)(this.priceScale()),s=this.properties().childs().baselineStyle,r=t.height(),o=100-e.y/r*100,a=o<0?0:Math.round(10*o)/10;s.childs().baseLevelPercentage.setValue(Math.max(Math.min(a,100),0))}}measureUnitId(){return(0,d.measureUnitId)(this.symbolInfo())}dataUpdated(){return this._dataUpdated}alertCreationAvailable(){return new w.WatchedValue(!this.priceScale()?.isPercentage()&&super.alertCreationAvailable().value()).readonly()}legendValuesProvider(){return this._overlayLegendValuesProvider}tableViewValuesProvider(){return this._overlayTableViewValuesProvider}valuesProvider(){return this._overlayValuesProvider}chartFloatingTooltipValuesProvider(){return this._overlayChartFloatingTooltipValuesProvider}defaultPlotIdForAlert(){return""}tags(){const e=[],t=this.symbolInfo();if(t)e.push(t.name);else{const t=this.symbol();t&&e.push(t)}return e}statusProvider(e){return new oe(this)}inputsInParts(e,t,s,i,r){return null}_onPropertiesChanged(){super._onPropertiesChanged(),this._precomputedBarStyles=new WeakMap}async _tryChangeInputs(){await super._tryChangeInputs(),this._formatter=null,this._defaultFormatter=null,this.priceScale()?.updateFormatter()}_tryCreateFormatter(){C.customFormatters?.priceFormatterFactory?.(this.symbolInfo(),this.properties().childs().minTick.value());return(0,d.createSeriesFormatter)(this.symbolInfo(),this.properties().childs().minTick.value())}_tryCreateDefaultFormatter(){return(0,d.createSeriesFormatter)(this.symbolInfo(),"default")}_onUnitChanged(){"alwaysOff"!==(0,h.currencyUnitVisibilityProperty)().value()&&this._model.fullUpdate(),this._model.unitConversionEnabled()&&this.isStarted()&&this._tryChangeInputs(),this._unitChanged.fire()}_getSymbolObject(e){const t=super._getSymbolObject(e),s=this.currency()
;null!==s&&(t["currency-id"]=s);const i=this.unit();return this._model.unitConversionEnabled()&&null!==i&&(t["unit-id"]=i),t.adjustment=this._model.mainSeries().properties().childs().dividendsAdjustment.value()?"dividends":"splits",t}_onSymbolResolvingStart(e,t){this._lastResolvedSymbolSource=t,super._onSymbolResolvingStart(e,t),this._symbolResolvingActive.setValue(!0)}_onSymbolError(){super._onSymbolError(),this._symbolResolvingActive.setValue(!1)}_onSymbolResolved(e,t,s){super._onSymbolResolved(e,t,s),this._recreatePriceFormattingDependencies();const i=t===this.symbol()?(0,d.extractSymbolNameFromSymbolInfo)(s,this.symbol()):null,r=(0,d.symbolCurrency)(s),o=(0,d.symbolUnit)(s,this._model.unitConversionEnabled());this._setSymbolParamsInternal({symbol:i??void 0,currency:r,unit:o},s),de&&this._setQuotesSymbol((0,_.encodeExtendedSymbolOrGetSimpleSymbolString)(this._getSymbolObject(this.symbol()))),this._checkStyle(),this._symbolResolvingActive.setValue(!1)}async _changeInputsImpl(e,t){await super._changeInputsImpl(e,t),this._realignLineToolsIfParamsChanged()}_getPropertyDefinitionsViewModelClass(){return Promise.all([s.e(32856),s.e(93637),s.e(12677),s.e(9263),s.e(62040),s.e(60607)]).then(s.bind(s,779648)).then((e=>e.StudyOverlayDefinitionsViewModel))}_setQuotesSymbol(e){const t=this.symbolInfo(),s=(0,d.extractSymbolNameFromSymbolInfo)(t,e),i=s&&this.getSymbolString(s);this._quotesProvider.setQuotesSessionSymbol(i)}_onQuotesUpdate(e,t){!this._legendView||void 0===t.values.change&&void 0===t.values.change_percent||(this._legendView.update((0,F.sourceChangeEvent)(this.id())),this._model.updateSource(this))}_createViews(){this._priceAxisViews=[];const e=new L(this);this._priceAxisViewsBase=[e];const t=new A(this);this._priceLinesAxisViews=[t],this._paneViews=[],this._labelPaneViews=[];let s=null,r=null;switch(this.properties().childs().style.value()){case 0:r=new Y.SeriesBarsPaneView(this,this._model);break;case 1:r=new $.SeriesCandlesPaneView(this,this._model);break;case 9:r=new Z.SeriesHollowCandlesPaneView(this,this._model);break;case 13:r=new J.SeriesColumnsPaneView(this,this._model);break;case 2:case 14:case 15:r=new j.SeriesLinePaneView(this,this._model);break;case 3:r=new Q.SeriesAreaPaneView(this,this._model);break;case 16:r=new K.SeriesHLCAreaPaneView(this,this._model);break;case 10:{r=new X.SeriesBaselinePaneView(this,this._model);const e=this.properties().childs().baselineStyle.childs();s=new P.SeriesWaterlinePaneView({paneHeight:()=>(0,i.ensureNotNull)(this.priceScale()).height(),color:()=>e.baselineColor.value(),baseLevelPercentage:()=>e.baseLevelPercentage.value()});break}case 12:r=new ee.SeriesHiLoPaneView(this,this._model);break;case 21:r=new he.SeriesHLCBarsPaneView(this,this._model)}r&&this._paneViews.push(r),null!==s&&this._paneViews.push(s),this._paneViews.push(new E(this)),this._dataWindowView||(this._dataWindowView=new N.OverlayDataWindowView(this,this._model)),this._legendView||(this._legendView=new z(this,this._model)),this._statusView||(this._statusView=new x.StudyStatusView(this)),
this._floatingTooltipView||(this._floatingTooltipView=new ne.StudyChartFloatingTooltipView(this,this._model)),this._priceAxisViews=[...this._priceAxisViewsBase],this._labelPaneViews.push(new U.PanePriceAxisView(e,this,this._model))}_createStudyOnServer(){const e=super._createStudyOnServer();return this._realignLineToolsIfParamsChanged(),e}_modifyStudyOnServer(e){super._modifyStudyOnServer(e,0)}_titleInParts(e,t,s,i,r){const o=this.symbolInfo();return[this._getSymbolTitlePart(o),[this._getExchangeTitlePart(o,r),this._getPriceSourceTitlePart(o)].filter((e=>null!==e))]}_mergeData(e){return this._invalidateLastNonEmptyPlotRowCache(),this._conflatedChunksBuilder.mergeData(e)}_clearData(){this._conflatedChunksBuilder.clearData()}_moveData(e){this._conflatedChunksBuilder.moveData(e)}_getSymbolTitlePart(e){if(null===e)return _e?"":this.properties().childs().inputs.childs().symbol.value();if(ce)switch(this._model.mainSeries().symbolTextSourceProxyProperty().value()){case"description":return e.description;case"ticker-and-description":return`${e.name}, ${e.description}`;case"long-description":return e.long_description??e.description}return e.name}_getExchangeTitlePart(e,t){return null===e||t?null:(0,d.getSymbolExchange)(e)}_getPriceSourceTitlePart(e){return null}_onAllowExtendTimeScaleChanged(){}_setSymbolParamsInternal(e,t){const{symbol:s,currency:i,unit:r,style:o}=e,a=this.properties().childs(),n=a.inputs.childs().symbol.value(),l=a.currencyId.value(),h=a.unitId.value(),u=a.style.value();if(void 0!==s&&a.inputs.childs().symbol.setValueSilently(s),void 0!==i&&a.currencyId.setValueSilently(i),void 0!==r&&a.unitId.setValueSilently(r),void 0!==o&&a.style.setValueSilently(o),t)this._resolvedSymbolsByInput[this.symbol()]=t,this._resolvedSymbols[this._getSymbolForResolve(this.symbol())]=t,this._realignToolsLastParams=null;else{const e=this.symbolInfo();null!==e&&(a.currencyId.setValueSilently((0,d.symbolCurrency)(e)),a.unitId.setValueSilently((0,d.symbolUnit)(e,this._model.unitConversionEnabled())))}a.inputs.childs().symbol.value()!==n&&a.inputs.childs().symbol.fireChanged(),a.currencyId.value()!==l&&a.currencyId.fireChanged(),a.unitId.value()!==h&&a.unitId.fireChanged(),a.style.value()!==u&&a.style.fireChanged(),this._checkStyle(),this._realignLineToolsIfParamsChanged()}_updateBarFunction(){this._barFunction=(0,l.barFunctionByStyle)(this.style(),this.priceSource())}_onMinTickChanged(){this._recreatePriceFormattingDependencies(),this.updateAllViews({type:"global-change"}),this._model.fullUpdate()}_onChartStyleChanged(){this._updateBarFunction(),this._styleToRecover?.originalStyle!==this.style()&&(this._styleToRecover=null)}_checkStyle(){const e=this.style();(0,d.isCloseBasedSymbol)(this.symbolInfo())||this.model().mainSeries().intervalObj().value().is1Tick()?(0,d.isSingleValueBasedStyle)(e)||(this.setStyle(2),this._styleToRecover={correctedStyle:this.style(),originalStyle:e}):null!==this._styleToRecover&&(this.setStyle(this._styleToRecover.originalStyle),this._styleToRecover=null)}_realignLineToolsIfParamsChanged(){
let e=null===this._realignToolsLastParams;if(null!==this._realignToolsLastParams){const t=this.compareSymbolParams(this._realignToolsLastParams);e=t.symbolChanged||t.intervalChanged||t.currencyChanged||t.unitChanged}e&&(this._model.realignLineTools(this),this._realignToolsLastParams=this.symbolParams())}}},252010:(e,t,s)=>{"use strict";s.r(t),s.d(t,{StudyStrategy:()=>B});var i=s(650279),r=s(650151),o=s(820028),a=s(280775),n=s(790188),l=s(86441),h=s(724377),u=s(731974),d=s(278688),c=s(688857),_=s(462140);function p(e,t,s){return s?Math.sign(e)*(Math.round(Math.abs(e))+t):Math.round(e)+t}class y{constructor(e,t){this._data=e,this._textWidths=t}draw(e,t){const{horizontalPixelRatio:s,verticalPixelRatio:i}=t;void 0!==this._textWidths.renderingInfo&&(0,d.equalRenderingInfos)(this._textWidths.renderingInfo,t)||(this._textWidths.renderingInfo=t,this._textWidths.width.clear());const r=this._data;e.lineJoin="miter",e.miterLimit=Math.round(2*s),e.strokeStyle=r.bgColor,e.lineWidth=Math.max(2,(0,_.ceiledEven)(2*s));for(const s of r.items){e.beginPath();const o="lower"===s.pos;e.save();const a=Math.round(r.orderArrow.vMargin*i);e.translate(0,o?a:-a),this._drawOrderArrow(e,s.orderPoint,r.orderArrow,r.exitLineWidth,o,t),e.restore(),e.save(),this._drawPriceArrow(e,s.pricePoint,r.priceArrow,!o,t),e.restore(),e.closePath(),e.stroke(),e.fillStyle=r.exitLineWidth?r.exitColor:"buy"===s.type?r.buyColor:r.sellColor,e.fill()}const o=r.text;o.visible&&(0,c.drawScaled)(e,s,i,(()=>{e.font=o.fontSize+"px "+o.font,e.textAlign="center",e.textBaseline="top",e.fillStyle=r.labelColor,e.strokeStyle=r.bgColor,e.lineWidth=Math.min(2,Math.floor(2*s));for(const t of r.items){const s="lower"===t.pos;e.save();const i=s?1:-1,a=r.orderArrow;e.translate(0,(a.height+a.vMargin+r.exitLineWidth)*i);const n=s?o.vMargin:-o.fontSize-o.vMargin,l=i*(o.fontSize+o.lineSpacing);if(t.label&&(e.translate(0,n),e.strokeText(t.label,Math.ceil(t.orderPoint.x),t.orderPoint.y),e.fillText(t.label,Math.ceil(t.orderPoint.x),t.orderPoint.y),this._textWidths.width.has(t.label)||this._textWidths.width.set(t.label,e.measureText(t.label).width)),t.qty){const s=Boolean(t.label)?l:n;e.translate(0,s),e.strokeText(t.qty,Math.ceil(t.orderPoint.x),t.orderPoint.y),e.fillText(t.qty,Math.ceil(t.orderPoint.x),t.orderPoint.y),this._textWidths.width.has(t.qty)||this._textWidths.width.set(t.qty,e.measureText(t.qty).width)}e.restore()}}))}hitTest(e){const t=this._testOrderArrows(e),s=!t&&this._data.text&&this._testText(e);return t||s?new u.HitTestResult(u.HitTarget.Regular):null}_drawOrderArrow(e,t,s,i,r,o){let a,n;r?(e.rotate(Math.PI),a=-t.x,n=-t.y):(a=t.x,n=t.y),n-=s.height+i;const{horizontalPixelRatio:l,verticalPixelRatio:h}=o,u=Math.max(1,Math.floor(l)),d=u%2?.5:0,c=r?"floor":"ceil";a=p(a*l,d,r),n=Math.round((Math[c](n)+.5)*h),e.translate(a,n);const y=(0,_.ceiledEven)(s.width*l)/2+d,m=(0,_.ceiledEven)(s.tailWidth*l)/2+d,S=Math.round(s.tailHeight*h);if(e.moveTo(-m,0),e.lineTo(m,0),e.lineTo(m,S),e.lineTo(y,S),e.lineTo(0,Math.round(s.height*h)),i){const t=Math.floor(u/2)
;e.lineTo(y+t,Math.round(s.height*h)),e.lineTo(y+t,Math.round((s.height+i)*h)),e.lineTo(-y-t,Math.round((s.height+i)*h)),e.lineTo(-y-t,Math.round(s.height*h)),e.lineTo(0,Math.round(s.height*h))}e.lineTo(-y,S),e.lineTo(-m,S),e.lineTo(-m,0),e.lineTo(m,0)}_drawPriceArrow(e,t,s,i,r){let o,a;i?(e.rotate(Math.PI),o=-t.x,a=-t.y):(o=t.x,a=t.y);const{horizontalPixelRatio:n,verticalPixelRatio:l}=r,h=Math.max(1,Math.floor(n)),u=h%2?.5:0;o=p(o*n,u,i)-h,a=p(a*l,u,i);const d=(0,_.ceiledEven)(2*s.width*l)/2,c=-(Math.round(s.width*n)+u);e.translate(o,a),e.moveTo(0,0),e.lineTo(c,-d),e.lineTo(c,d),e.lineTo(0,0),e.lineTo(c,-d)}_testOrderArrows(e){const t=this._data,s=t.orderArrow.width/2;for(const i of t.items){const r=i.orderPoint;if(e.x<r.x-s||e.x>r.x+s)continue;const o="lower"===i.pos;let a=t.orderArrow.height+t.exitLineWidth;o||(a=-a);const n=r.y+(o?t.orderArrow.vMargin:-t.orderArrow.vMargin),l=Math.min(n,n+a),h=Math.max(n,n+a);if(!(e.y<l||e.y>h))return!0}return!1}_testText(e){const t=this._data,s=t.text;if(!s.visible)return!1;for(const i of t.items){const r=i.orderPoint,o="lower"===i.pos;let a=t.orderArrow.vMargin+t.orderArrow.height+t.exitLineWidth;o||(a=-a);const n=r.y+a,l=(i.label?1:0)+(i.qty?1:0);let h=s.vMargin+s.fontSize*l+s.lineSpacing*Math.max(0,l-1);o||(h=-h);const u=Math.min(n,n+h),d=Math.max(n,n+h);if(e.y<u||e.y>d)continue;const c=i.label&&i.qty?o&&e.y<n+s.fontSize||!o&&e.y>n-s.fontSize?i.label:i.qty:i.label||i.qty,_=this._textWidths.width.get(c);if(!(void 0===_||e.x<r.x-_/2||e.x>r.x+_/2))return!0}return!1}}var m=s(910282),S=s(584785),g=s(708162);const v={BuyColor:(0,n.getHexColorByName)("color-tv-blue-500"),SellColor:"#FF1744",ExitColor:"#D500F9",LabelDarkColor:(0,n.getHexColorByName)("color-cold-gray-300"),LabelLightColor:(0,n.getHexColorByName)("color-cold-gray-900")};function f(e){return!1===e.e?"exit":e.b?"entryBuy":"entrySell"}function b(e){return e.b?"buy":"sell"}function w(e,t){return t?!e.b:e.b}function C(e,t){return w(e,t)?"lower":"upper"}function x(e,t,s){let i=e[t].get(s);return void 0===i&&(i=0,e[t].set(s,i)),i}function P(e,t,s,i){const o=(0,r.ensureDefined)(e[t].get(s));e[t].set(s,o+i)}function I(e,t){return t&&(e.c||e.id||"Untitled")||""}function D(e,t){return t&&(e.b?"+":"-")+e.q||""}function V(e,t,s){return(0,r.ensureNotNull)(e.b?s.valueAt(t,3):s.valueAt(t,2))}function T(e,t){return("exit"===f(e)?2:0)+t.vMargin+t.height}class k{constructor(e,t,s){this._invalidated=!0,this._textWidths={width:new Map},this._study=e,this._series=t,this._model=s,this._orderArrow={vMargin:11,width:10,height:15,tailWidth:2,tailHeight:10},this._priceArrow={width:5},this._text={vMargin:7,font:m.CHART_FONT_FAMILY,fontSize:12,lineSpacing:2,visible:!0},this._items={entryBuy:[],entrySell:[],exit:[]},this._study.properties().childs().strategy.childs().orders.subscribe(this,this.update)}update(){this._invalidated=!0}clearItems(){this._items.entryBuy=[],this._items.entrySell=[],this._items.exit=[]}renderer(){const e=this._study.properties().childs()
;if(!e.visible.value()||!e.strategy.childs().orders.childs().visible.value())return null;if(this._invalidated&&(this._updateImpl(),this._invalidated=!1),this._items.entryBuy.length||this._items.entrySell.length||this._items.exit.length){const e=new g.CompositeRenderer;return this._items.entryBuy.length&&e.append(this._strategyOrdersRenderer(this._items.entryBuy,!1)),this._items.entrySell.length&&e.append(this._strategyOrdersRenderer(this._items.entrySell,!1)),this._items.exit.length&&e.append(this._strategyOrdersRenderer(this._items.exit,!0)),e}return null}_strategyOrdersRenderer(e,t){const s=this._model.backgroundColor().value(),i="black"===(0,h.rgbToBlackWhiteString)((0,h.parseRgb)(s),150);return new y({items:e,buyColor:v.BuyColor,sellColor:v.SellColor,exitColor:v.ExitColor,labelColor:i?v.LabelDarkColor:v.LabelLightColor,bgColor:s,orderArrow:this._orderArrow,exitLineWidth:t?2:0,priceArrow:this._priceArrow,text:this._text},this._textWidths)}_updateImpl(){this.clearItems();const e=this._study.ordersData();if(!e||!e.length)return;const t=this._model.timeScale(),s=this._series.priceScale(),i=t.visibleBarsStrictRange();if(t.isEmpty()||s.isEmpty()||null===i)return;const o=this._study.barsIndexes(),a=this._series.nearestIndex(i.firstBar(),S.PlotRowSearchMode.NearestRight),n=this._series.nearestIndex(i.lastBar(),S.PlotRowSearchMode.NearestLeft);if(void 0===a||void 0===n)return;const[h,u]=function(e,t,s,i){const r=[],o=[];for(let a=0;a<e.length;++a){const n=i[a];n<t||n>s||(r.push(e[a]),o.push(n))}return[r,o]}(e,a,n,o),[d,c]=this._labelAndQtyVisibility(h.length),_=d||c,p=this._textHeight(d,c),y=(0,r.ensureNotNull)(this._series.firstValue()),m=s.isInverted(),g={upper:new Map,lower:new Map};for(let e=0;e<h.length;e++){const i=h[e],r=w(i,m),o=u[e],a=t.indexToCoordinate(o),n=V(i,o,this._series);let _=s.priceToCoordinate(n,y);const S=C(i,m),v=x(g,S,o);_+=r?v:-v;P(g,S,o,T(i,this._orderArrow)+p);const k=s.priceToCoordinate(i.p,y),L=f(i);this._items[L].push({orderPoint:new l.Point(a,_),pricePoint:new l.Point(a,k),label:I(i,d),qty:D(i,c),pos:S,type:b(i)})}this._text.visible=_}_labelAndQtyVisibility(e){const t=this._model.timeScale();if(t.barSpacing()<10&&this._orderArrow.width*e*2>t.width())return[!1,!1];const s=this._study.properties().childs().strategy.childs().orders.childs();return[s.showLabels.value(),s.showQty.value()]}_textHeight(e,t){return e||t?e&&t?2*this._text.fontSize+this._text.vMargin+this._text.lineSpacing:this._text.fontSize+this._text.vMargin:0}}var L=s(923995),R=s(547465),A=s(44049);const M=["strategy_props"];class B extends a.Study{constructor(e,t,s,i,r,a,n,l){super(e,t,s,i,r,a,n),this.watchedData=new o.WatchedValue,this._reportData=null,this._reportChanged=new R.Delegate,this._barsIndexes=[],this._srcBarsIndexes=[],this._reportDataBuffer=null,this._activeState=!1,l||(t.childs().visible.subscribe(this,(()=>{this._processChangeVisibility()})),this.onIsActualIntervalChange().subscribe(this,(()=>this._processChangeVisibility())),this._processChangeVisibility(a))}destroy(){
this._properties.childs().visible.unsubscribeAll(this),this.model().removeStrategySource(this,0),super.destroy()}properties(){return this._properties}hasStateForAlert(){return L.alertsAvailable&&this._series.alertCreationAvailable().value()}canHasAlertOnLineTools(){return!1}inputsForAlertState(){const e=this.inputs();return this._metaInfo.value().inputs.forEach((t=>{const s=e[t.id];s&&t.internalID&&(s.internalID=t.internalID)})),e}reportData(){return this._reportData}ordersData(){return this._reportData?.filledOrders??null}barsIndexes(){return this._barsIndexes}clearData(){super.clearData(),this._reportDataBuffer=null,this._reportData=null,this._strategyOrdersPaneView&&this._strategyOrdersPaneView.clearItems(),this._reportChanged.fire({activeOrdersChanged:!0,filledOrdersChanged:!0,positionChanged:!0})}reportChanged(){return this._reportChanged}strategyOrdersPaneView(){return this.isSourceHidden()?null:this._strategyOrdersPaneView}restoreData(e){super.restoreData(e);const t=e;this.model().addStrategySource(this,0),t.reportData&&t.barsIndexes&&(this._reportData=t.reportData,this._barsIndexes=t.barsIndexes,this.watchedData.setValue({method:"data_update",nonSeriesData:{indexes_replace:!1,data:{report:this._reportData}}})),t.activeState&&(this._activeState=!0)}activeState(){return this._activeState}updateAllViews(e){super.updateAllViews(e),this._strategyOrdersPaneView&&this._strategyOrdersPaneView.update()}state(e,t){const s=super.state(e,t);return e&&this._reportData&&this._barsIndexes&&(s.data||(s.data={}),s.data.reportData=this._reportData,s.data.barsIndexes=this._barsIndexes,s.data.activeState=this.model().activeStrategySource().value()===this),s}isHibernationAllowed(){return!1}pineSourceCodeModel(){return this.metaInfo().pine?(this._pineSourceCodeModel||(this._pineSourceCodeModel=new A.AsyncResourceWrapper(Promise.all([s.e(35556),s.e(50687),s.e(72449),s.e(76536),s.e(8092),s.e(7351),s.e(73618),s.e(26843),s.e(35168),s.e(10270),s.e(89199)]).then(s.bind(s,465675)).then((e=>this._isDestroyed?null:new e.PineStrategySourceCodeModel(this))),(e=>{e?.destroy()}))),this._pineSourceCodeModel.promise()):Promise.resolve(null)}_onData(e){e.method&&"data_update"!==e.method&&this.watchedData.setValue({method:e.method}),super._onData(e)}_onDataUpdated(e,t,s,o){let a=t&&!t.indexes_replace&&t.data&&t.data.report;if(a){const e=!(0,i.default)(a.activeOrders,this._reportData?.activeOrders),s=!(0,i.default)(a.filledOrders,this._reportData?.filledOrders),o=!(0,i.default)(a.position,this._reportData?.position);t&&!t.indexes_replace&&t.isUpdate&&this._reportDataBuffer&&(a={...this._reportDataBuffer,...a}),this._reportData={...this._reportDataBuffer,...a},this._reportDataBuffer=this._reportData,this.watchedData.setValue({method:"data_update",nonSeriesData:(0,r.ensureNotNull)(t)}),this._reportChanged.fire({activeOrdersChanged:e,filledOrdersChanged:s,positionChanged:o})}else this.watchedData.setValue({method:"data_update",nonSeriesData:{indexes_replace:!1,data:{report:this._reportDataBuffer}}});const n=!(!t||!t.indexes_replace)
;("nochange"!==s&&s.length>0||(this._reportData?.filledOrders?.length??0)!==this._barsIndexes.length)&&(a||n)&&this._collateBarsIndexes(Array.isArray(s)?s:this._srcBarsIndexes),super._onDataUpdated(e,t,s,o)}_createViews(){super._createViews(),this._strategyOrdersPaneView=new k(this,this._series,this._model)}_titleInputs(e,t,s){return this.inputs({symbolsForDisplay:!0,skipHiddenInputs:!0,skipFakeInputs:!1,fakeInputsForDisplay:!0,asObject:!0,skippedGroups:M,noExchanges:t,displayMask:e})}_getTelemetryObjectName(){return"strategy"}_collateBarsIndexes(e){this._srcBarsIndexes=e;const t=this._reportData?.filledOrders;if(t&&t.length){this._barsIndexes.length=0;for(let s=0;s<t.length;s++)this._barsIndexes[s]=e[t[s].tm]}}_processChangeVisibility(e){const t=!0===e?2:0;this.isSourceHidden()?this.model().removeStrategySource(this,t):this.model().addStrategySource(this,t)}}},616427:(e,t,s)=>{"use strict";s.d(t,{areEqualSymbols:()=>a,compareSymbolParams:()=>u,symbolParams:()=>h,symbolSameAsCurrent:()=>l});s(213377);var i=s(388741),r=s(477786);const o=!0;function a(e,t){return void 0===e?void 0===t:void 0!==t&&(o?e.toUpperCase()===t.toUpperCase():e===t)}function n(e,t){return e.some((e=>a(t,e)))}function l(e,t){if(null===t)return!1;if(t){if(a(t.full_name,e)||a(t.pro_name,e))return!0;if(a(t.ticker,e))return!0;if(t.aliases&&n(t.aliases,e))return!0;if(t.alternatives&&n(t.alternatives,e))return!0;if(0===e.indexOf("FRA:")&&a(t.pro_name,e.replace("FRA:","FWB:")))return!0}return!1}function h(e){return{symbol:e.symbol(),currency:e.currency(),unit:e.unit(),interval:e.interval(),style:e.style()}}function u(e,t,s){const{symbol:o,currency:a,unit:n,style:l,interval:h}=t,u=void 0!==o&&!e.symbolSameAsResolved(o);let d,c;const _=e.symbolInfo();null!==_?(d=void 0!==a&&!function(e,t){return null===e&&!(0,i.isConvertedToOtherCurrency)(t)||e===(0,i.symbolCurrency)(t)}(a,_),c=void 0!==n&&!function(e,t,s){return null===e&&!(0,i.isConvertedToOtherUnit)(t,s)||e===(0,i.symbolUnit)(t,s)}(n,_,s)):(d=void 0!==a&&a!==e.currency(),c=void 0!==n&&n!==e.unit());return{symbolChanged:u,intervalChanged:void 0!==h&&!r.Interval.isEqual(e.interval(),h),currencyChanged:d,unitChanged:c,styleChanged:void 0!==l&&l!==e.style(),styleChangeRequiresRestart:void 0!==l&&(0,i.styleChangeRequiresRestart)(l,e.style())}}},422063:(e,t,s)=>{"use strict";s.d(t,{UndoCommand:()=>r});var i=s(418450);class r{constructor(e,t=!0,s=!0){this._text=e||new i.TranslatedString("",""),this._executeOnPush=t,this._affectsState=s}text(){return this._text}executeOnPush(){return this._executeOnPush}affectsState(){return this._affectsState}canMerge(e){return!1}merge(e){throw new Error("Should be re-implemented in child classes")}}},825957:(e,t,s)=>{"use strict";s.d(t,{showSupportDialog:()=>r});s(659863);var i=s(383913);function r(e){
return Promise.all([s.e(67102),s.e(92537),s.e(98185),s.e(98933),s.e(84712),s.e(85902),s.e(90579),s.e(7291),s.e(15371),s.e(61681),s.e(63039),s.e(6612),s.e(11069),s.e(50874),s.e(67780),s.e(65743),s.e(61171),s.e(87833),s.e(65161),s.e(67691),s.e(38452),s.e(84587),s.e(98158),s.e(43329),s.e(40887),s.e(92990),s.e(89126),s.e(82870),s.e(98681),s.e(37242),s.e(27702),s.e(59546),s.e(38490),s.e(87265),s.e(63585),s.e(41392),s.e(16990),s.e(66534),s.e(11194),s.e(71761),s.e(26070),s.e(34709),s.e(52165),s.e(91961),s.e(95542),s.e(64175),s.e(75134),s.e(70882),s.e(5516),s.e(32227),s.e(47793),s.e(90786),s.e(25977),s.e(24951),s.e(82687),s.e(39278),s.e(86923),s.e(58468),s.e(19556),s.e(52773),s.e(90462),s.e(8138),s.e(27455),s.e(57274)]).then(s.bind(s,96182)).then((t=>i.availableOffers.runOrUpdate((()=>new t.SupportDialog(e).open()))))}},388130:(e,t,s)=>{"use strict";s.d(t,{DAYS_UNTIL_BLOCK:()=>o,UserStatus:()=>i,getDaysUntilBlock:()=>a,isUserLockedOut:()=>n});var i,r=s(336669);!function(e){e.NonPro="non_pro",e.Pro="pro"}(i||(i={}));const o=7,a=(0,r.memoize)((()=>{const e=window.user.declared_status_timestamp;if(!e)return null;const t=((new Date).getTime()-new Date(1e3*e).getTime())/1e3/24/60/60;return o-Math.floor(t)})),n=(0,r.memoize)((e=>{const t=e??a();return Boolean(null!==t&&t<=0)}))},262609:(e,t,s)=>{"use strict";s(142492);var i=s(324951),r=s(414879),o=r.WeekDays,a=r.Months,n=s(936410),l=n.monthsFullNames,h=n.monthsShortNames,u=n.weekDaysFullNames,d=n.weekDaysShortNames,c=n.weekDaysMiniNames;window.language&&(i.locale(window.language,{months:[l[a.JANUARY],l[a.FEBRUARY],l[a.MARCH],l[a.APRIL],l[a.MAY],l[a.JUNE],l[a.JULY],l[a.AUGUST],l[a.SEPTEMBER],l[a.OCTOBER],l[a.NOVEMBER],l[a.DECEMBER]],monthsShort:[h[a.JANUARY],h[a.FEBRUARY],h[a.MARCH],h[a.APRIL],h[a.MAY],h[a.JUNE],h[a.JULY],h[a.AUGUST],h[a.SEPTEMBER],h[a.OCTOBER],h[a.NOVEMBER],h[a.DECEMBER]],weekdays:[u[o.SUNDAY],u[o.MONDAY],u[o.TUESDAY],u[o.WEDNESDAY],u[o.THURSDAY],u[o.FRIDAY],u[o.SATURDAY]],weekdaysShort:[d[o.SUNDAY],d[o.MONDAY],d[o.TUESDAY],d[o.WEDNESDAY],d[o.THURSDAY],d[o.FRIDAY],d[o.SATURDAY]],weekdaysMin:[c[o.SUNDAY],c[o.MONDAY],c[o.TUESDAY],c[o.WEDNESDAY],c[o.THURSDAY],c[o.FRIDAY],c[o.SATURDAY]],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MMM D",l:"M/D/YYYY",LL:"MMM D, YYYY",ll:"MMM D LT",LLL:"LT - LL",lll:"MMM D, YYYY LT",LLLL:"ddd D MMMM YYYY LT",llll:"ddd D MMM YYYY LT"},calendar:{sameDay:s.i18next(null,void 0,s(689399)).format({specialSymbolOpen:"[",specialSymbolClose:"]",dayTime:"LT"}),nextDay:s.i18next(null,void 0,s(534437)).format({specialSymbolOpen:"[",specialSymbolClose:"]",dayTime:"LT"}),nextWeek:s.i18next(null,void 0,s(71460)).format({specialSymbolOpen:"[",specialSymbolClose:"]",dayTime:"LT",dayName:"dddd"}),lastDay:s.i18next(null,void 0,s(285799)).format({specialSymbolOpen:"[",specialSymbolClose:"]",dayTime:"LT"}),lastWeek:s.i18next(null,void 0,s(632811)).format({specialSymbolOpen:"[",specialSymbolClose:"]",dayTime:"LT",dayName:"dddd"}),sameElse:"L"},relativeTime:{future:function(e){return e===s.i18next(null,void 0,s(464017))?e:s.i18next(null,{context:"time_range"
},s(326715)).replace("%s",e)},past:function(e){return e===s.i18next(null,void 0,s(464017))?e:s.i18next(null,{context:"time_range"},s(820864)).replace("%s",e)},s:s.i18next(null,void 0,s(464017)),m:function(e){return s.i18next(null,{plural:"%d minutes",count:e},s(584890)).replace("%d",e)},mm:function(e){return s.i18next(null,{plural:"%d minutes",count:e},s(584890)).replace("%d",e)},h:s.i18next(null,void 0,s(105756)),hh:function(e){return s.i18next(null,{plural:"%d hours",count:e},s(817856)).replace("%d",e)},d:s.i18next(null,void 0,s(426940)),dd:function(e){return s.i18next(null,{plural:"%d days",count:e},s(851068)).replace("%d",e)},M:s.i18next(null,void 0,s(2046)),MM:function(e){return s.i18next(null,{plural:"%d months",count:e},s(515185)).replace("%d",e)},y:s.i18next(null,void 0,s(403823)),yy:function(e){return s.i18next(null,{plural:"%d years",count:e},s(909806)).replace("%d",e)}},week:{dow:1,doy:4}}),i.locale(window.language)),e.exports=i},17957:(e,t,s)=>{"use strict";s.d(t,{getPlatform:()=>o});var i=s(314802),r=s(638456);function o(){return(0,r.isDesktopApp)()?"desktop":(0,i.isOnMobileAppPage)("old")?"ios":(0,i.isOnMobileAppPage)("new")?"android":"web"}},999102:(e,t,s)=>{"use strict";function i(e){return e.reduce((function(e,t,s){return~e.indexOf(t)||e.push(t),e}),[])}s.r(t),s.d(t,{uniq:()=>i})},149204:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25" width="25" height="25" fill="none"><circle stroke="currentColor" stroke-width="1.5" cx="12.5" cy="12.5" r="11.75"/><path fill="currentColor" d="M8.25 18V7.43h3.96c1.05 0 1.95.2 2.68.62.75.4 1.31 1 1.7 1.78.4.78.6 1.73.6 2.83v.02a6.5 6.5 0 0 1-.59 2.88c-.39.8-.96 1.4-1.7 1.82a5.4 5.4 0 0 1-2.69.62H8.25Zm1.9-1.59h1.82c.7 0 1.29-.14 1.78-.43.5-.29.87-.71 1.12-1.26.26-.55.4-1.22.4-2.02v-.01c0-.77-.14-1.43-.4-1.98a2.78 2.78 0 0 0-1.13-1.26 3.45 3.45 0 0 0-1.77-.43h-1.83v7.4Z"/></svg>'},226235:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25" width="25" height="25" fill="none"><rect width="21.5" height="21.5" stroke="currentColor" stroke-width="1.5" rx="1.25" x=".75" y="2.75"/><path fill="currentColor" d="M8.21 19V8.43h6.84v1.6H10.1v2.85h4.68v1.5H10.1v3.03h4.95V19H8.21Z"/></svg>'},183800:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25" width="25" height="25" fill="none"><path stroke="currentColor" stroke-width="1.5" d="M10.85 23.72c.4.25.9.25 1.3 0l9.5-5.82c.37-.22.6-.63.6-1.06V2c0-.69-.56-1.25-1.25-1.25H2C1.31.75.75 1.31.75 2v14.84c0 .43.23.84.6 1.06l9.5 5.82Z"/><path fill="currentColor" d="M8.21 17V6.43h6.84v1.6H10.1v2.85h4.68v1.5H10.1v3.03h4.95V17H8.21Z"/></svg>'},472134:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25" width="25" height="25" fill="none"><path stroke="currentColor" stroke-width="1.5" d="M10.87 1.23c.39-.22.87-.22 1.26 0l9.5 5.52c.38.23.62.64.62 1.08V23c0 .69-.56 1.25-1.25 1.25H2c-.69 0-1.25-.56-1.25-1.25V7.83c0-.44.24-.85.62-1.08l9.5-5.52Z"/><path fill="currentColor" d="M8.21 19V8.43h6.84v1.6H10.1v2.85h4.68v1.5H10.1v3.03h4.95V19H8.21Z"/></svg>'},
85752:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25" width="25" height="25" fill="none"><circle stroke="currentColor" stroke-width="1.5" cx="12.5" cy="12.5" r="11.75"/><path fill="currentColor" d="m19.27 13.25.53-.53-.53-.53-3.5-3.5-1.06 1.06 2.22 2.22h-5.38L7.27 7.7 6.2 8.75l4.5 4.5.22.22h6L14.7 15.7l1.06 1.06 3.5-3.5Zm-10.56.44-3 3 1.06 1.06 3-3L8.7 13.7Z"/></svg>'},682345:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25" width="25" height="25" fill="none"><circle stroke="currentColor" stroke-width="1.5" cx="12.5" cy="12.5" r="11.75"/><path fill="currentColor" d="M12.51 18.26a5.5 5.5 0 0 1-2.13-.38c-.6-.25-1.08-.6-1.43-1.06a2.9 2.9 0 0 1-.58-1.59v-.1h1.82l.01.07c.03.3.15.56.35.78.21.22.49.4.84.52a3.56 3.56 0 0 0 2.31-.02c.33-.13.58-.3.77-.54.19-.23.28-.5.28-.8v-.01c0-.38-.15-.7-.45-.93-.3-.25-.79-.44-1.47-.59l-1.14-.23a5.8 5.8 0 0 1-1.76-.64c-.46-.28-.8-.62-1.01-1.02-.22-.4-.32-.86-.32-1.38 0-.64.16-1.19.5-1.66.33-.48.8-.85 1.38-1.11.59-.27 1.27-.4 2.03-.4.78 0 1.45.13 2.03.4.57.25 1.02.6 1.34 1.05.33.45.51.96.55 1.53l.01.1h-1.8l-.02-.1a1.38 1.38 0 0 0-.34-.72 1.85 1.85 0 0 0-.73-.5c-.3-.12-.65-.18-1.06-.18-.38 0-.73.06-1.03.18-.3.12-.54.29-.72.5a1.2 1.2 0 0 0-.25.78c0 .25.06.47.19.66.12.2.32.36.6.5.27.13.63.24 1.07.34l1.13.24c.74.16 1.35.36 1.81.62.46.26.8.58 1.02.97.22.38.33.84.33 1.38 0 .69-.17 1.28-.5 1.78-.34.5-.82.88-1.44 1.15-.61.28-1.34.41-2.19.41Z"/></svg>'},920057:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var i=s(290484),r=s(182433);const o=function(e,t,s){var o=!0,a=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return(0,r.default)(s)&&(o="leading"in s?!!s.leading:o,a="trailing"in s?!!s.trailing:a),(0,i.default)(e,t,{leading:o,maxWait:t,trailing:a})}}}]);