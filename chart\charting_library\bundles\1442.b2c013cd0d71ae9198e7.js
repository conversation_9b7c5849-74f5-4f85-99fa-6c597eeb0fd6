"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1442],{891442:(t,r,e)=>{function n(t){for(var r=arguments.length,e=Array(r>1?r-1:0),n=1;n<r;n++)e[n-1]=arguments[n];throw Error("[Immer] minified error nr: "+t+(e.length?" "+e.map((function(t){return"'"+t+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function o(t){return!!t&&!!t[J]}function i(t){return!!t&&(function(t){if(!t||"object"!=typeof t)return!1;var r=Object.getPrototypeOf(t);if(null===r)return!0;var e=Object.hasOwnProperty.call(r,"constructor")&&r.constructor;return e===Object||"function"==typeof e&&Function.toString.call(e)===B}(t)||Array.isArray(t)||!!t[q]||!!t.constructor[q]||s(t)||d(t))}function u(t,r,e){void 0===e&&(e=!1),0===c(t)?(e?Object.keys:G)(t).forEach((function(n){e&&"symbol"==typeof n||r(n,t[n],t)})):t.forEach((function(e,n){return r(n,e,t)}))}function c(t){var r=t[J];return r?r.i>3?r.i-4:r.i:Array.isArray(t)?1:s(t)?2:d(t)?3:0}function f(t,r){return 2===c(t)?t.has(r):Object.prototype.hasOwnProperty.call(t,r)}function a(t,r){return 2===c(t)?t.get(r):t[r]}function l(t,r,e){var n=c(t);2===n?t.set(r,e):3===n?(t.delete(r),t.add(e)):t[r]=e}function p(t,r){return t===r?0!==t||1/t==1/r:t!=t&&r!=r}function s(t){return L&&t instanceof Map}function d(t){return V&&t instanceof Set}function v(t){return t.o||t.t}function y(t){if(Array.isArray(t))return Array.prototype.slice.call(t);var r=H(t);delete r[J];for(var e=G(r),n=0;n<e.length;n++){var o=e[n],i=r[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(r[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:t[o]})}return Object.create(Object.getPrototypeOf(t),r)}function h(t,r){return void 0===r&&(r=!1),g(t)||o(t)||!i(t)||(c(t)>1&&(t.set=t.add=t.clear=t.delete=b),Object.freeze(t),r&&u(t,(function(t,r){return h(r,!0)}),!0)),t}function b(){n(2)}function g(t){return null==t||"object"!=typeof t||Object.isFrozen(t)}function O(t){var r=Q[t];return r||n(18,t),r}function m(t,r){Q[t]||(Q[t]=r)}function w(){return K}function P(t,r){r&&(O("Patches"),t.u=[],t.s=[],t.v=r)}function j(t){A(t),t.p.forEach(_),t.p=null}function A(t){t===K&&(K=t.l)}function S(t){return K={p:[],l:K,h:t,m:!0,_:0}}function _(t){var r=t[J];0===r.i||1===r.i?r.j():r.O=!0}function E(t,r){r._=r.p.length;var e=r.p[0],o=void 0!==t&&t!==e;return r.h.g||O("ES5").S(r,t,o),o?(e[J].P&&(j(r),n(4)),i(t)&&(t=D(r,t),r.l||x(r,t)),r.u&&O("Patches").M(e[J],t,r.u,r.s)):t=D(r,e,[]),j(r),r.u&&r.v(r.u,r.s),t!==$?t:void 0}function D(t,r,e){if(g(r))return r;var n=r[J];if(!n)return u(r,(function(o,i){return k(t,n,r,o,i,e)}),!0),r;if(n.A!==t)return r;if(!n.P)return x(t,n.t,!0),n.t;if(!n.I){n.I=!0,n.A._--;var o=4===n.i||5===n.i?n.o=y(n.k):n.o;u(3===n.i?new Set(o):o,(function(r,i){return k(t,n,o,r,i,e)})),x(t,o,!1),e&&t.u&&O("Patches").R(n,e,t.u,t.s)}return n.o}function k(t,r,e,n,u,c){if(o(u)){var a=D(t,u,c&&r&&3!==r.i&&!f(r.D,n)?c.concat(n):void 0);if(l(e,n,a),!o(a))return;t.m=!1}if(i(u)&&!g(u)){if(!t.h.F&&t._<1)return;D(t,u),r&&r.A.l||x(t,u)}}function x(t,r,e){void 0===e&&(e=!1),
t.h.F&&t.m&&h(r,e)}function R(t,r){var e=t[J];return(e?v(e):t)[r]}function F(t,r){if(r in t)for(var e=Object.getPrototypeOf(t);e;){var n=Object.getOwnPropertyDescriptor(e,r);if(n)return n;e=Object.getPrototypeOf(e)}}function C(t){t.P||(t.P=!0,t.l&&C(t.l))}function I(t){t.o||(t.o=y(t.t))}function M(t,r,e){var n=s(r)?O("MapSet").N(r,e):d(r)?O("MapSet").T(r,e):t.g?function(t,r){var e=Array.isArray(t),n={i:e?1:0,A:r?r.A:w(),P:!1,I:!1,D:{},l:r,t,k:null,o:null,j:null,C:!1},o=n,i=Y;e&&(o=[n],i=Z);var u=Proxy.revocable(o,i),c=u.revoke,f=u.proxy;return n.k=f,n.j=c,f}(r,e):O("ES5").J(r,e);return(e?e.A:w()).p.push(n),n}function N(t){return o(t)||n(22,t),function t(r){if(!i(r))return r;var e,n=r[J],o=c(r);if(n){if(!n.P&&(n.i<4||!O("ES5").K(n)))return n.t;n.I=!0,e=T(r,o),n.I=!1}else e=T(r,o);return u(e,(function(r,o){n&&a(n.t,r)===o||l(e,r,t(o))})),3===o?new Set(e):e}(t)}function T(t,r){switch(r){case 2:return new Map(t);case 3:return Array.from(t)}return y(t)}function X(){function t(t,r){var e=i[t];return e?e.enumerable=r:i[t]=e={configurable:!0,enumerable:r,get:function(){var r=this[J];return Y.get(r,t)},set:function(r){var e=this[J];Y.set(e,t,r)}},e}function r(t){for(var r=t.length-1;r>=0;r--){var o=t[r][J];if(!o.P)switch(o.i){case 5:n(o)&&C(o);break;case 4:e(o)&&C(o)}}}function e(t){for(var r=t.t,e=t.k,n=G(e),o=n.length-1;o>=0;o--){var i=n[o];if(i!==J){var u=r[i];if(void 0===u&&!f(r,i))return!0;var c=e[i],a=c&&c[J];if(a?a.t!==u:!p(c,u))return!0}}var l=!!r[J];return n.length!==G(r).length+(l?0:1)}function n(t){var r=t.k;if(r.length!==t.t.length)return!0;var e=Object.getOwnPropertyDescriptor(r,r.length-1);return!(!e||e.get)}var i={};m("ES5",{J:function(r,e){var n=Array.isArray(r),o=function(r,e){if(r){for(var n=Array(e.length),o=0;o<e.length;o++)Object.defineProperty(n,""+o,t(o,!0));return n}var i=H(e);delete i[J];for(var u=G(i),c=0;c<u.length;c++){var f=u[c];i[f]=t(f,r||!!i[f].enumerable)}return Object.create(Object.getPrototypeOf(e),i)}(n,r),i={i:n?5:4,A:e?e.A:w(),P:!1,I:!1,D:{},l:e,t:r,k:o,o:null,O:!1,C:!1};return Object.defineProperty(o,J,{value:i,writable:!0}),o},S:function(t,e,i){i?o(e)&&e[J].A===t&&r(t.p):(t.u&&function t(r){if(r&&"object"==typeof r){var e=r[J];if(e){var o=e.t,i=e.k,c=e.D,a=e.i;if(4===a)u(i,(function(r){r!==J&&(void 0!==o[r]||f(o,r)?c[r]||t(i[r]):(c[r]=!0,C(e)))})),u(o,(function(t){void 0!==i[t]||f(i,t)||(c[t]=!1,C(e))}));else if(5===a){if(n(e)&&(C(e),c.length=!0),i.length<o.length)for(var l=i.length;l<o.length;l++)c[l]=!1;else for(var p=o.length;p<i.length;p++)c[p]=!0;for(var s=Math.min(i.length,o.length),d=0;d<s;d++)void 0===c[d]&&t(i[d])}}}}(t.p[0]),r(t.p))},K:function(t){return 4===t.i?e(t):n(t)}})}e.d(r,{configureStore:()=>Pt,createSlice:()=>St});var z,K,U="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),L="undefined"!=typeof Map,V="undefined"!=typeof Set,W="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,$=U?Symbol.for("immer-nothing"):((z={})["immer-nothing"]=!0,
z),q=U?Symbol.for("immer-draftable"):"__$immer_draftable",J=U?Symbol.for("immer-state"):"__$immer_state",B=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),G="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:Object.getOwnPropertyNames,H=Object.getOwnPropertyDescriptors||function(t){var r={};return G(t).forEach((function(e){r[e]=Object.getOwnPropertyDescriptor(t,e)})),r},Q={},Y={get:function(t,r){if(r===J)return t;var e=v(t);if(!f(e,r))return function(t,r,e){var n,o=F(r,e);return o?"value"in o?o.value:null===(n=o.get)||void 0===n?void 0:n.call(t.k):void 0}(t,e,r);var n=e[r];return t.I||!i(n)?n:n===R(t.t,r)?(I(t),t.o[r]=M(t.A.h,n,t)):n},has:function(t,r){return r in v(t)},ownKeys:function(t){return Reflect.ownKeys(v(t))},set:function(t,r,e){var n=F(v(t),r);if(null==n?void 0:n.set)return n.set.call(t.k,e),!0;if(!t.P){var o=R(v(t),r),i=null==o?void 0:o[J];if(i&&i.t===e)return t.o[r]=e,t.D[r]=!1,!0;if(p(e,o)&&(void 0!==e||f(t.t,r)))return!0;I(t),C(t)}return t.o[r]===e&&"number"!=typeof e&&(void 0!==e||r in t.o)||(t.o[r]=e,t.D[r]=!0,!0)},deleteProperty:function(t,r){return void 0!==R(t.t,r)||r in t.t?(t.D[r]=!1,I(t),C(t)):delete t.D[r],t.o&&delete t.o[r],!0},getOwnPropertyDescriptor:function(t,r){var e=v(t),n=Reflect.getOwnPropertyDescriptor(e,r);return n?{writable:!0,configurable:1!==t.i||"length"!==r,enumerable:n.enumerable,value:e[r]}:n},defineProperty:function(){n(11)},getPrototypeOf:function(t){return Object.getPrototypeOf(t.t)},setPrototypeOf:function(){n(12)}},Z={};u(Y,(function(t,r){Z[t]=function(){return arguments[0]=arguments[0][0],r.apply(this,arguments)}})),Z.deleteProperty=function(t,r){return Y.deleteProperty.call(this,t[0],r)},Z.set=function(t,r,e){return Y.set.call(this,t[0],r,e,t[0])};var tt=function(){function t(t){var r=this;this.g=W,this.F=!0,this.produce=function(t,e,o){if("function"==typeof t&&"function"!=typeof e){var u=e;e=t;var c=r;return function(t){var r=this;void 0===t&&(t=u);for(var n=arguments.length,o=Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return c.produce(t,(function(t){var n;return(n=e).call.apply(n,[r,t].concat(o))}))}}var f;if("function"!=typeof e&&n(6),void 0!==o&&"function"!=typeof o&&n(7),i(t)){var a=S(r),l=M(r,t,void 0),p=!0;try{f=e(l),p=!1}finally{p?j(a):A(a)}return"undefined"!=typeof Promise&&f instanceof Promise?f.then((function(t){return P(a,o),E(t,a)}),(function(t){throw j(a),t})):(P(a,o),E(f,a))}if(!t||"object"!=typeof t){if((f=e(t))===$)return;return void 0===f&&(f=t),r.F&&h(f,!0),f}n(21,t)},this.produceWithPatches=function(t,e){return"function"==typeof t?function(e){for(var n=arguments.length,o=Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return r.produceWithPatches(e,(function(r){return t.apply(void 0,[r].concat(o))}))}:[r.produce(t,e,(function(t,r){n=t,o=r})),n,o];var n,o},"boolean"==typeof(null==t?void 0:t.useProxies)&&this.setUseProxies(t.useProxies),
"boolean"==typeof(null==t?void 0:t.autoFreeze)&&this.setAutoFreeze(t.autoFreeze)}var r=t.prototype;return r.createDraft=function(t){i(t)||n(8),o(t)&&(t=N(t));var r=S(this),e=M(this,t,void 0);return e[J].C=!0,A(r),e},r.finishDraft=function(t,r){var e=(t&&t[J]).A;return P(e,r),E(void 0,e)},r.setAutoFreeze=function(t){this.F=t},r.setUseProxies=function(t){t&&!W&&n(20),this.g=t},r.applyPatches=function(t,r){var e;for(e=r.length-1;e>=0;e--){var n=r[e];if(0===n.path.length&&"replace"===n.op){t=n.value;break}}e>-1&&(r=r.slice(e+1));var i=O("Patches").$;return o(t)?i(t,r):this.produce(t,(function(t){return i(t,r)}))},t}(),rt=new tt,et=rt.produce;rt.produceWithPatches.bind(rt),rt.setAutoFreeze.bind(rt),rt.setUseProxies.bind(rt),rt.applyPatches.bind(rt),rt.createDraft.bind(rt),rt.finishDraft.bind(rt);const nt=et;e(377145);var ot=e(406047);function it(t){return function(r){var e=r.dispatch,n=r.getState;return function(r){return function(o){return"function"==typeof o?o(e,n,t):r(o)}}}}var ut=it();ut.withExtraArgument=it;const ct=ut;var ft,at=(ft=function(t,r){return ft=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])},ft(t,r)},function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function e(){this.constructor=t}ft(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)}),lt=function(t,r){for(var e=0,n=r.length,o=t.length;e<n;e++,o++)t[o]=r[e];return t},pt=Object.defineProperty,st=(Object.defineProperties,Object.getOwnPropertyDescriptors,Object.getOwnPropertySymbols),dt=Object.prototype.hasOwnProperty,vt=Object.prototype.propertyIsEnumerable,yt=function(t,r,e){return r in t?pt(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e},ht=function(t,r){for(var e in r||(r={}))dt.call(r,e)&&yt(t,e,r[e]);if(st)for(var n=0,o=st(r);n<o.length;n++){e=o[n];vt.call(r,e)&&yt(t,e,r[e])}return t},bt="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?ot.compose:ot.compose.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function gt(t){if("object"!=typeof t||null===t)return!1;for(var r=t;null!==Object.getPrototypeOf(r);)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(t)===r}var Ot=function(t){function r(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var o=t.apply(this,e)||this;return Object.setPrototypeOf(o,r.prototype),o}return at(r,t),Object.defineProperty(r,Symbol.species,{get:function(){return r},enumerable:!1,configurable:!0}),r.prototype.concat=function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];return t.prototype.concat.apply(this,r)},r.prototype.prepend=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]
;return 1===t.length&&Array.isArray(t[0])?new(r.bind.apply(r,lt([void 0],t[0].concat(this)))):new(r.bind.apply(r,lt([void 0],t.concat(this))))},r}(Array);function mt(){return function(t){return function(t){void 0===t&&(t={});var r=t.thunk,e=void 0===r||r,n=(t.immutableCheck,t.serializableCheck,new Ot);e&&(!function(t){return"boolean"==typeof t}(e)?n.push(ct.withExtraArgument(e.extraArgument)):n.push(ct));0;return n}(t)}}var wt=!0;function Pt(t){var r,e=mt(),n=t||{},o=n.reducer,i=void 0===o?void 0:o,u=n.middleware,c=void 0===u?e():u,f=n.devTools,a=void 0===f||f,l=n.preloadedState,p=void 0===l?void 0:l,s=n.enhancers,d=void 0===s?void 0:s;if("function"==typeof i)r=i;else{if(!gt(i))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');r=(0,ot.combineReducers)(i)}var v=c;if("function"==typeof v&&(v=v(e),!wt&&!Array.isArray(v)))throw new Error("when using a middleware builder function, an array of middleware must be returned");if(!wt&&v.some((function(t){return"function"!=typeof t})))throw new Error("each middleware provided to configureStore must be a function");var y=ot.applyMiddleware.apply(void 0,v),h=ot.compose;a&&(h=bt(ht({trace:!wt},"object"==typeof a&&a)));var b=[y];Array.isArray(d)?b=lt([y],d):"function"==typeof d&&(b=d(b));var g=h.apply(void 0,b);return(0,ot.createStore)(r,p,g)}function jt(t,r){function e(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(r){var o=r.apply(void 0,e);if(!o)throw new Error("prepareAction did not return an object");return ht(ht({type:t,payload:o.payload},"meta"in o&&{meta:o.meta}),"error"in o&&{error:o.error})}return{type:t,payload:e[0]}}return e.toString=function(){return""+t},e.type=t,e.match=function(r){return r.type===t},e}function At(t){var r,e={},n=[],o={addCase:function(t,r){var n="string"==typeof t?t:t.type;if(n in e)throw new Error("addCase cannot be called with two reducers for the same action type");return e[n]=r,o},addMatcher:function(t,r){return n.push({matcher:t,reducer:r}),o},addDefaultCase:function(t){return r=t,o}};return t(o),[e,n,r]}function St(t){var r=t.name,e=t.initialState;if(!r)throw new Error("`name` is a required option for createSlice");var n=t.reducers||{},u="function"==typeof t.extraReducers?At(t.extraReducers):[t.extraReducers],c=u[0],f=void 0===c?{}:c,a=u[1],l=void 0===a?[]:a,p=u[2],s=void 0===p?void 0:p,d=Object.keys(n),v={},y={},h={};d.forEach((function(t){var e,o,i=n[t],u=r+"/"+t;"reducer"in i?(e=i.reducer,o=i.prepare):e=i,v[t]=e,y[u]=e,h[t]=o?jt(u,o):jt(u)}));var b=function(t,r,e,n){void 0===e&&(e=[]);var u="function"==typeof r?At(r):[r,e,n],c=u[0],f=u[1],a=u[2],l=nt(t,(function(){}));return function(t,r){void 0===t&&(t=l);var e=lt([c[r.type]],f.filter((function(t){return(0,t.matcher)(r)})).map((function(t){return t.reducer})));return 0===e.filter((function(t){return!!t})).length&&(e=[a]),e.reduce((function(t,e){if(e){var n;if(o(t))return void 0===(n=e(t,r))?t:n;if(i(t))return nt(t,(function(t){return e(t,r)}));if(void 0===(n=e(t,r))){
if(null===t)return t;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return t}),t)}}(e,ht(ht({},f),y),l,s);return{name:r,reducer:b,actions:h,caseReducers:v}}X()},377145:(t,r,e)=>{function n(t,r){return t===r}e.d(r,{createSelector:()=>o});var o=function(t){for(var r=arguments.length,e=Array(r>1?r-1:0),n=1;n<r;n++)e[n-1]=arguments[n];return function(){for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=0,u=n.pop(),c=function(t){var r=Array.isArray(t[0])?t[0]:t;if(!r.every((function(t){return"function"==typeof t}))){var e=r.map((function(t){return typeof t})).join(", ");throw new Error("Selector creators expect all input-selectors to be functions, instead received the following types: ["+e+"]")}return r}(n),f=t.apply(void 0,[function(){return i++,u.apply(null,arguments)}].concat(e)),a=t((function(){for(var t=[],r=c.length,e=0;e<r;e++)t.push(c[e].apply(null,arguments));return f.apply(null,t)}));return a.resultFunc=u,a.dependencies=c,a.recomputations=function(){return i},a.resetRecomputations=function(){return i=0},a}}((function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n,e=null,o=null;return function(){return function(t,r,e){if(null===r||null===e||r.length!==e.length)return!1;for(var n=r.length,o=0;o<n;o++)if(!t(r[o],e[o]))return!1;return!0}(r,e,arguments)||(o=t.apply(null,arguments)),e=arguments,o}}))}}]);