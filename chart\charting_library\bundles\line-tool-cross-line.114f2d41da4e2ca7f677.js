"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7203],{848422:(e,i,r)=>{r.r(i),r.d(i,{LineToolCrossLine:()=>l});var t=r(792535),s=r(76410),n=r(180882),o=r(889868);class l extends o.LineDataSource{constructor(e,i,t,o){super(e,i??l.createProperties(e.backgroundTheme().spawnOwnership()),t,o),this._priceAxisView=new s.LineToolHorzLinePriceAxisView(this),this._timeAxisView=new n.LineToolVertLineTimeAxisView(this),Promise.all([r.e(6290),r.e(6881),r.e(5579),r.e(1583)]).then(r.bind(r,193192)).then((({CrossLinePaneView:e})=>{this._setPaneViews([new e(this,this._model)])}))}pointsCount(){return 1}name(){return"Cross Line"}priceAxisViews(e,i){return this.isSourceHidden()?null:i===this.priceScale()&&this.properties().childs().showPrice.value()&&this._model.paneForSource(this)===e?[this._priceAxisView]:null}timeAxisViews(){return this.isSourceHidden()?null:this.properties().childs().showTime.value()?[this._timeAxisView]:null}updateAllViews(e){super.updateAllViews(e),this._priceAxisView.update(e),this._timeAxisView.update()}canHasAlert(){return!1}lineColor(){return this.properties().childs().linecolor.value()}lineWidth(){return this.properties().childs().linewidth.value()}lineStyle(){return this.properties().childs().linestyle.value()}static createProperties(e,i){const r=new t.DefaultProperty({defaultName:"linetoolcrossline",state:i,theme:e});return this._configureProperties(r),r}_normalizePoint(e,i){return super._normalizePointWithoutOffset(e)??super._normalizePoint(e,i)}_getPropertyDefinitionsViewModelClass(){return Promise.all([r.e(6406),r.e(8511),r.e(5234),r.e(4590),r.e(8537)]).then(r.bind(r,943948)).then((e=>e.CrossLineDefinitionsViewModel))}}},76410:(e,i,r)=>{r.d(i,{LineToolHorzLinePriceAxisView:()=>n});var t=r(887946),s=r(589637);class n extends t.PriceAxisView{constructor(e){super(),this._source=e}_updateRendererData(e,i,r){e.visible=!1;const t=this._source.points(),n=this._source.priceScale();if(0===t.length||null===n||n.isEmpty())return;const o=t[0];if(!isFinite(o.price))return;const l=this._source.ownerSource(),u=null!==l?l.firstValue():null;if(null===u)return;const c=(0,s.resetTransparency)(this._source.properties().linecolor.value());r.background=c,r.textColor=this.generateTextColor(c),r.coordinate=n.priceToCoordinate(o.price,u),e.text=n.formatPrice(o.price,u),e.visible=!0}}},180882:(e,i,r)=>{r.d(i,{LineToolVertLineTimeAxisView:()=>s});var t=r(169476);class s extends t.LineDataSourceTimeAxisView{constructor(e){super(e,0)}_getBgColor(){return this._source.properties().linecolor.value()}_getAlwaysInViewPort(){return!1}_getIndex(){const e=this._source.points();return 0===e.length?null:e[0].index}}}}]);