"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1155],{423562:(e,t,r)=>{r.r(t),r.d(t,{LineTool5PointsPattern:()=>a});var n=r(650151),i=r(792535),o=r(889868),s=r(981856);class a extends o.LineDataSource{constructor(e,t,r,n){super(e,t??a.createProperties(e.backgroundTheme().spawnOwnership()),r,n),this._loadPaneViews(e)}pointsCount(){return 5}name(){return"XABCD Pattern"}static createProperties(e,t){const r=new i.DefaultProperty({defaultName:"linetool5pointspattern",state:t,theme:e});return this._configureProperties(r),r}_getPropertyDefinitionsViewModelClass(){return Promise.all([r.e(6406),r.e(8511),r.e(5234),r.e(4590),r.e(8537)]).then(r.bind(r,809863)).then((e=>e.PatternWithBackgroundDefinitionViewModel))}_loadPaneViews(e){Promise.all([r.e(6290),r.e(6881),r.e(5579),r.e(1583)]).then(r.bind(r,67534)).then((t=>{this._setPaneViews([new t.Pattern5pointsPaneView(this,e)])}))}static _configureProperties(e){super._configureProperties(e),e.addChild("linesColors",new s.LineToolColorsProperty([(0,n.ensureDefined)(e.child("color"))])),e.addChild("textsColors",new s.LineToolColorsProperty([(0,n.ensureDefined)(e.child("textcolor"))]))}}}}]);