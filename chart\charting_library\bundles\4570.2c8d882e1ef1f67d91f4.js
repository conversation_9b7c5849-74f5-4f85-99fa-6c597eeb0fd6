(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4570],{497754:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)&&n.length){var a=o.apply(null,n);a&&e.push(a)}else if("object"===i)for(var s in n)r.call(n,s)&&n[s]&&e.push(s)}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},262453:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},845946:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC",iconOnly:"iconOnly-D4RPB3ZC",link:"link-D4RPB3ZC",brand:"brand-D4RPB3ZC",primary:"primary-D4RPB3ZC",secondary:"secondary-D4RPB3ZC",gray:"gray-D4RPB3ZC",green:"green-D4RPB3ZC",red:"red-D4RPB3ZC",black:"black-D4RPB3ZC","black-friday":"black-friday-D4RPB3ZC","cyber-monday":"cyber-monday-D4RPB3ZC",slot:"slot-D4RPB3ZC",xsmall:"xsmall-D4RPB3ZC",withStartSlot:"withStartSlot-D4RPB3ZC",withEndSlot:"withEndSlot-D4RPB3ZC",startSlotWrap:"startSlotWrap-D4RPB3ZC",endSlotWrap:"endSlotWrap-D4RPB3ZC",small:"small-D4RPB3ZC",medium:"medium-D4RPB3ZC",large:"large-D4RPB3ZC",xlarge:"xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC",adjustPosition:"adjustPosition-D4RPB3ZC",firstRow:"firstRow-D4RPB3ZC",firstCol:"firstCol-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC",textWrap:"textWrap-D4RPB3ZC",multilineContent:"multilineContent-D4RPB3ZC",secondaryText:"secondaryText-D4RPB3ZC",primaryText:"primaryText-D4RPB3ZC"}},577508:e=>{e.exports={dialog:"dialog-b8SxMnzX",wrapper:"wrapper-b8SxMnzX",separator:"separator-b8SxMnzX",bounded:"bounded-b8SxMnzX"}},710326:e=>{e.exports={"small-height-breakpoint":"(max-height: 360px)",container:"container-BZKENkhT",unsetAlign:"unsetAlign-BZKENkhT",title:"title-BZKENkhT",subtitle:"subtitle-BZKENkhT",textWrap:"textWrap-BZKENkhT",ellipsis:"ellipsis-BZKENkhT",close:"close-BZKENkhT",icon:"icon-BZKENkhT"}},692335:e=>{e.exports={container:"container-qm7Rg5MB",mobile:"mobile-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},294386:e=>{e.exports={container:"container-QcG0kDOU",image:"image-QcG0kDOU",title:"title-QcG0kDOU",description:"description-QcG0kDOU",button:"button-QcG0kDOU"}},295059:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},536204:e=>{
e.exports={separator:"separator-Pf4rIzEt"}},805184:(e,t,n)=>{"use strict";var r,o,i;function a(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function s(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function l(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}n.d(t,{Button:()=>p}),function(e){e.Primary="primary",e.Success="success",e.Default="default",e.Danger="danger"}(r||(r={})),function(e){e.Small="s",e.Medium="m",e.Large="l"}(o||(o={})),function(e){e.Default="default",e.Stroke="stroke"}(i||(i={}));var c=n(50959),d=n(228837);function u(e){const{intent:t,size:n,appearance:r,useFullWidth:o,icon:i,...c}=e;return{...c,color:s(t),size:l(n),variant:a(r),stretch:o}}function p(e){return c.createElement(d.SquareButton,{...u(e)})}},389986:(e,t,n)=>{"use strict";n.d(t,{CloseButton:()=>u});var r=n(50959),o=n(270762),i=n(117105),a=n(315130),s=n(238822),l=n(663346),c=n(534983);function d(e="large"){switch(e){case"large":return i;case"medium":default:return a;case"small":return s;case"xsmall":return l;case"xxsmall":return c}}const u=r.forwardRef(((e,t)=>r.createElement(o.NavButton,{...e,ref:t,icon:d(e.size)})))},270762:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var r=n(50959),o=n(497754),i=n(878112),a=(n(15378),n(262453));function s(e){const{size:t="large",preservePaddings:n,isLink:r,flipIconOnRtl:i,className:s}=e;return o(a["nav-button"],a[`size-${t}`],n&&a["preserve-paddings"],i&&a["flip-icon"],r&&a.link,s)}function l(e){const{children:t,icon:n}=e;return r.createElement(r.Fragment,null,r.createElement("span",{className:a.background}),r.createElement(i.Icon,{icon:n,className:a.icon,"aria-hidden":!0}),t&&r.createElement("span",{className:a["visually-hidden"]},t))}const c=(0,r.forwardRef)(((e,t)=>{const{icon:n,type:o="button",preservePaddings:i,flipIconOnRtl:a,size:c,"aria-label":d,...u}=e;return r.createElement("button",{...u,className:s({...e,children:d}),ref:t,type:o},r.createElement(l,{icon:n},d))}));c.displayName="NavButton";var d=n(591365),u=n(273388);(0,r.forwardRef)(((e,t)=>{const{icon:n,renderComponent:o,"aria-label":i,...a}=e,c=o??d.CustomComponentDefaultLink;return r.createElement(c,{...a,className:s({...e,children:i,isLink:!0}),reference:(0,u.isomorphicRef)(t)},r.createElement(l,{icon:n},i))})).displayName="NavAnchorButton"},373989:(e,t,n)=>{"use strict";function r(e){return"brand"===e?"black":"blue"===e?"brand":e}n.d(t,{renameColors:()=>r})},228837:(e,t,n)=>{"use strict";n.d(t,{SquareButton:()=>C});var r=n(50959),o=n(497754),i=n(331774),a=n(373989),s=n(845946),l=n.n(s);const c="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text apply-common-tooltip";function d(e){const{size:t="medium",variant:n="primary",stretch:r=!1,startSlot:s,endSlot:d,iconOnly:u=!1,className:p,isGrouped:m,cellState:h,disablePositionAdjustment:f=!1,primaryText:g,secondaryText:C,isAnchor:v=!1}=e,x=(0,
a.renameColors)(e.color??"brand"),y=function(e){let t="";return 0!==e&&(1&e&&(t=o(t,l()["no-corner-top-left"])),2&e&&(t=o(t,l()["no-corner-top-right"])),4&e&&(t=o(t,l()["no-corner-bottom-right"])),8&e&&(t=o(t,l()["no-corner-bottom-left"]))),t}((0,i.getGroupCellRemoveRoundBorders)(h)),w=u&&(s||d);return o(p,l().button,l()[t],l()[x],l()[n],r&&l().stretch,s&&l().withStartIcon,d&&l().withEndIcon,w&&l().iconOnly,y,m&&l().grouped,m&&!f&&l().adjustPosition,m&&h.isTop&&l().firstRow,m&&h.isLeft&&l().firstCol,g&&C&&l().multilineContent,v&&l().link,c)}function u(e){const{startSlot:t,iconOnly:n,children:i,endSlot:a,primaryText:s,secondaryText:d}=e;if(t&&a&&n)return r.createElement("span",{className:o(l().slot,l().startSlotWrap)},t);const u=n&&(t??a),p=!t&&!a&&!n&&!i&&s&&d;return r.createElement(r.Fragment,null,t&&r.createElement("span",{className:o(l().slot,l().startSlotWrap)},t),i&&!u&&r.createElement("span",{className:l().content},i),a&&r.createElement("span",{className:o(l().slot,l().endSlotWrap)},a),p&&!u&&function(e){return e.primaryText&&e.secondaryText&&r.createElement("div",{className:o(l().textWrap,c)},r.createElement("span",{className:l().primaryText}," ",e.primaryText," "),"string"==typeof e.secondaryText?r.createElement("span",{className:l().secondaryText}," ",e.secondaryText," "):r.createElement("span",{className:l().secondaryText},r.createElement("span",null,e.secondaryText.firstLine),r.createElement("span",null,e.secondaryText.secondLine)))}(e))}var p=n(601198),m=n(380327),h=n(800417);function f(e,t){return n=>{if(t)return n.preventDefault(),void n.stopPropagation();e?.(n)}}function g(e){const{className:t,color:n,variant:r,size:o,stretch:i,animated:a,iconOnly:s,startSlot:l,endSlot:c,primaryText:d,secondaryText:u,...p}=e;return{...p,...(0,h.filterDataProps)(e),...(0,h.filterAriaProps)(e)}}function C(e){const{reference:t,tooltipText:n,disabled:o,onClick:i,onMouseOver:a,onMouseOut:s,onMouseDown:l,...c}=e,{isGrouped:h,cellState:C,disablePositionAdjustment:v}=(0,r.useContext)(m.ControlGroupContext),x=d({...c,isGrouped:h,cellState:C,disablePositionAdjustment:v}),y=n??(e.primaryText?[e.primaryText,e.secondaryText].join(" "):(0,p.getTextForTooltip)(e.children));return r.createElement("button",{...g(c),"aria-disabled":o,tabIndex:e.tabIndex??(o?-1:0),className:x,ref:t,onClick:f(i,o),onMouseDown:f(l,o),"data-overflow-tooltip-text":y},r.createElement(u,{...c}))}n(15378)},15378:(e,t,n)=>{"use strict";var r,o,i,a;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(r||(r={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(o||(o={})),function(e){e.Brand="brand",e.Blue="blue",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(i||(i={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",
e.Regular16px="regular16px",e.Regular14px="regular14px"}(a||(a={}))},380327:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>r});const r=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},331774:(e,t,n)=>{"use strict";function r(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>r})},409245:(e,t,n)=>{"use strict";function r(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>r})},591365:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>i});var r=n(50959),o=n(409245);function i(e){return r.createElement("a",{...(0,o.renameRef)(e)})}r.PureComponent},601198:(e,t,n)=>{"use strict";n.d(t,{getTextForTooltip:()=>a});var r=n(50959);const o=e=>(0,r.isValidElement)(e)&&Boolean(e.props.children),i=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",a=e=>Array.isArray(e)||(0,r.isValidElement)(e)?r.Children.toArray(e).reduce(((e,t)=>{let n="";return n=(0,r.isValidElement)(t)&&o(t)?a(t.props.children):(0,r.isValidElement)(t)&&!o(t)?"":i(t),e.concat(n)}),"").trim():i(e)},273388:(e,t,n)=>{"use strict";function r(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function o(e){return r([e])}n.d(t,{isomorphicRef:()=>o,mergeRefs:()=>r})},651674:(e,t,n)=>{"use strict";n.d(t,{createReactRoot:()=>u});var r=n(50959),o=n(632227),i=n(904237);const a=(0,r.createContext)({isOnMobileAppPage:()=>!1,isRtl:!1,locale:"en"});var s=n(69111),l=n(431520);const c={iOs:"old",android:"new",old:"old",new:"new",any:"any"};function d(e){const[t]=(0,r.useState)({isOnMobileAppPage:e=>(0,s.isOnMobileAppPage)(c[e]),isRtl:(0,l.isRtl)(),locale:window.locale});return r.createElement(a.Provider,{value:t},e.children)}function u(e,t,n="legacy"){const a=r.createElement(d,null,e);if("modern"===n){const e=(0,i.createRoot)(t);return e.render(a),{render(t){e.render(r.createElement(d,null,t))},unmount(){e.unmount()}}}return o.render(a,t),{render(e){o.render(r.createElement(d,null,e),t)},unmount(){o.unmountComponentAtNode(t)}}}},533408:(e,t,n)=>{"use strict";n.d(t,{AdaptivePopupDialog:()=>k});var r=n(50959),o=n(650151),i=n(497754),a=n.n(i),s=n(180185),l=n(431520),c=n(698043),d=n(59216),u=n(494707),p=n(996038),m=n(930052),h=n(910549);var f=n(206594),g=n(559410),C=n(609838),v=n(389986),x=n(190410),y=n(710326);function w(e){const{titleId:t,title:o,titleTextWrap:i=!1,subtitle:s,showCloseIcon:l=!0,onClose:c,onCloseButtonKeyDown:d,renderBefore:u,renderAfter:p,draggable:m,className:h,unsetAlign:f,closeAriaLabel:g=C.t(null,void 0,n(47742)),closeButtonReference:w}=e,[b,B]=(0,r.useState)(!1);return r.createElement(x.DialogHeaderContext.Provider,{value:{setHideClose:B}},r.createElement("div",{className:a()(y.container,h,(s||f)&&y.unsetAlign)},u,r.createElement("div",{id:t,className:y.title,"data-dragg-area":m},r.createElement("div",{className:a()(i?y.textWrap:y.ellipsis)},o),s&&r.createElement("div",{
className:a()(y.ellipsis,y.subtitle)},s)),p,l&&!b&&r.createElement(v.CloseButton,{className:y.close,"data-name":"close","aria-label":g,onClick:c,onKeyDown:d,ref:w,size:"medium",preservePaddings:!0})))}var b=n(273388),B=n(800417),R=n(924910),E=n(440891),S=n(577508);const P={vertical:20},D={vertical:0};class k extends r.PureComponent{constructor(){super(...arguments),this._controller=null,this._reference=null,this._orientationMediaQuery=null,this._embedResizerOverridesEnabled=E.enabled("embed_resizer_overrides"),this._titleId=`title_${(0,R.randomHash)()}`,this._renderChildren=(e,t)=>(this._controller=e,this.props.render({requestResize:this._requestResize,centerAndFit:this._centerAndFit,isSmallWidth:t})),this._handleReference=e=>this._reference=e,this._handleCloseBtnClick=()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleClose()},this._handleClose=()=>{this.props.onClose()},this._handleOpen=()=>{void 0!==this.props.onOpen&&this.props.isOpened&&this.props.onOpen(this.props.fullScreen||window.matchMedia(p.DialogBreakpoints.TabletSmall).matches)},this._handleKeyDown=e=>{if(!e.defaultPrevented){if(this.props.onKeyDown&&this.props.onKeyDown(e),27===(0,s.hashFromEvent)(e)){if(e.defaultPrevented)return;if(this.props.forceCloseOnEsc&&this.props.forceCloseOnEsc())return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose();const{activeElement:n}=document;if(null!==n){if(e.preventDefault(),"true"===(t=n).getAttribute("data-haspopup")&&"true"!==t.getAttribute("data-expanded"))return void this._handleClose();const r=this._reference;if(null!==r&&(0,c.isTextEditingField)(n))return void r.focus();if(r?.contains(n))return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose()}}var t,n;(function(e){if("function"==typeof e)return e();return Boolean(e)})(this.props.disableTabNavigationContainment)||(n=e,[9,s.Modifiers.Shift+9].includes((0,s.hashFromEvent)(n))&&n.stopPropagation())}},this._requestResize=()=>{null!==this._controller&&this._controller.recalculateBounds()},this._centerAndFit=()=>{null!==this._controller&&this._controller.centerAndFit()},this._calculatePositionWithOffsets=(e,t)=>{const n=(0,o.ensureDefined)(this.props.fullScreenViewOffsets).value();return{top:n.top,left:(0,l.isRtl)()?-n.right:n.left,width:t.clientWidth-n.left-n.right,height:t.clientHeight-n.top-n.bottom}}}componentDidMount(){this.props.ignoreClosePopupsAndDialog||g.subscribe(f.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),this._handleOpen(),void 0!==this.props.onOpen&&(this._orientationMediaQuery=window.matchMedia("(orientation: portrait)"),this._orientationMediaQuery.addEventListener("change",this._handleOpen)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.subscribe(this._requestResize);const{backdrop:e,draggable:t=!e,centerOnResize:n=!t}=this.props;n&&window.addEventListener("resize",this._centerAndFit)}componentWillUnmount(){
this.props.ignoreClosePopupsAndDialog||g.unsubscribe(f.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),null!==this._orientationMediaQuery&&this._orientationMediaQuery.removeEventListener("change",this._handleOpen),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.unsubscribe(this._requestResize),window.removeEventListener("resize",this._centerAndFit)}focus(){(0,o.ensureNotNull)(this._reference).focus()}getElement(){return this._reference}contains(e){return this._reference?.contains(e)??!1}render(){const{className:e,wrapperClassName:t,headerClassName:n,isOpened:o,title:i,titleTextWrap:s,dataName:l,onClickOutside:c,additionalElementPos:f,additionalHeaderElement:g,backdrop:C,shouldForceFocus:v=!0,shouldReturnFocus:x,onForceFocus:y,showSeparator:R,subtitle:E,draggable:k=!C,fullScreen:N=!1,showCloseIcon:_=!0,rounded:z=!0,isAnimationEnabled:O,growPoint:T,dialogTooltip:M,unsetHeaderAlign:A,onDragStart:Z,dataDialogName:L,closeAriaLabel:I,containerAriaLabel:F,reference:K,containerTabIndex:W,closeButtonReference:G,onCloseButtonKeyDown:H,shadowed:q,fullScreenViewOffsets:Q,fixedBody:V,onClick:j}=this.props,U="after"!==f?g:void 0,X="after"===f?g:void 0,$="string"==typeof i?i:L||"",J=(0,B.filterDataProps)(this.props),Y=(0,b.mergeRefs)([this._handleReference,K]);return r.createElement(m.MatchMedia,{rule:p.DialogBreakpoints.SmallHeight},(f=>r.createElement(m.MatchMedia,{rule:p.DialogBreakpoints.TabletSmall},(p=>r.createElement(d.PopupDialog,{rounded:!(p||N)&&z,className:a()(S.dialog,N&&Q&&S.bounded,e),isOpened:o,reference:Y,onKeyDown:this._handleKeyDown,onClickOutside:c,onClickBackdrop:c,fullscreen:p||N,guard:f?D:P,boundByScreen:p||N,shouldForceFocus:v,onForceFocus:y,shouldReturnFocus:x,backdrop:C,draggable:k,isAnimationEnabled:O,growPoint:T,name:this.props.dataName,dialogTooltip:M,onDragStart:Z,containerAriaLabel:F,containerTabIndex:W,calculateDialogPosition:N&&Q?this._calculatePositionWithOffsets:void 0,shadowed:q,fixedBody:V,onClick:j,...J},r.createElement("div",{role:"dialog","aria-labelledby":void 0!==i?this._titleId:void 0,className:a()(S.wrapper,t),"data-name":l,"data-dialog-name":$},void 0!==i&&r.createElement(w,{draggable:k&&!(p||N),onClose:this._handleCloseBtnClick,renderAfter:X,renderBefore:U,subtitle:E,title:i,titleId:this._titleId,titleTextWrap:s,showCloseIcon:_,className:n,unsetAlign:A,closeAriaLabel:I,closeButtonReference:G,onCloseButtonKeyDown:H}),R&&r.createElement(u.Separator,{className:S.separator}),r.createElement(h.PopupContext.Consumer,null,(e=>this._renderChildren(e,p||N)))))))))}}},190410:(e,t,n)=>{"use strict";n.d(t,{DialogHeaderContext:()=>r});const r=n(50959).createContext({setHideClose:()=>{}})},231862:(e,t,n)=>{"use strict";n.d(t,{DialogSearch:()=>u});var r=n(50959),o=n(497754),i=n.n(o),a=n(609838),s=n(878112),l=n(606347),c=n(654313),d=n(692335);function u(e){const{children:t,isMobile:o,renderInput:u,onCancel:m,containerClassName:h,inputContainerClassName:f,iconClassName:g,cancelTitle:C=a.t(null,void 0,n(904543)),...v}=e;return r.createElement("div",{
className:i()(d.container,o&&d.mobile,h)},r.createElement("div",{className:i()(d.inputContainer,o&&d.mobile,f,m&&d.withCancel)},u||r.createElement(p,{isMobile:o,...v})),t,r.createElement(s.Icon,{className:i()(d.icon,o&&d.mobile,g),icon:o?c:l}),m&&(!o||""!==v.value)&&r.createElement("div",{className:i()(d.cancel,o&&d.mobile),onClick:m},C))}function p(e){const{className:t,reference:n,isMobile:o,value:a,onChange:s,onFocus:l,onBlur:c,onKeyDown:u,onSelect:p,placeholder:m,activeDescendant:h,...f}=e;return r.createElement("input",{...f,ref:n,type:"text",className:i()(t,d.input,o&&d.mobile),autoComplete:"off",role:"searchbox","data-role":"search",placeholder:m,value:a,onChange:s,onFocus:l,onBlur:c,onSelect:p,onKeyDown:u,"aria-activedescendant":h})}},993517:(e,t,n)=>{"use strict";n.d(t,{ContentIsNotFound:()=>c});var r=n(50959),o=n(497754),i=n.n(o),a=n(878112),s=n(805184),l=n(294386);function c(e){const{className:t,icon:n,title:o,description:c,buttonText:d,buttonAction:u}=e;return r.createElement("div",{className:i()(l.container,t)},n&&r.createElement(a.Icon,{icon:n,className:l.image}),o&&r.createElement("h3",{className:l.title},o),c&&r.createElement("p",{className:l.description},c),d&&u&&r.createElement(s.Button,{onClick:u,className:l.button},d))}},170876:(e,t,n)=>{"use strict";n.d(t,{createRegExpList:()=>s,getHighlightedChars:()=>l,rankedSearch:()=>a});var r=n(41899);function o(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}var i;function a(e){const{data:t,rules:n,queryString:o,isPreventedFromFiltering:i,primaryKey:a,secondaryKey:s=a,optionalPrimaryKey:l,tertiaryKey:c}=e;return t.map((e=>{const t=l&&e[l]?e[l]:e[a],i=e[s],d=c&&e[c];let u,p=0;return n.forEach((e=>{const{re:n,fullMatch:a}=e;if(n.lastIndex=0,(0,r.isString)(t)&&t&&t.toLowerCase()===o.toLowerCase())return p=4,void(u=t.match(a)?.index);if((0,r.isString)(t)&&a.test(t))return p=3,void(u=t.match(a)?.index);if((0,r.isString)(i)&&a.test(i))return p=2,void(u=i.match(a)?.index);if((0,r.isString)(i)&&n.test(i))return p=2,void(u=i.match(n)?.index);if(Array.isArray(d))for(const e of d)if(a.test(e))return p=1,void(u=e.match(a)?.index)})),{matchPriority:p,matchIndex:u,item:e}})).filter((e=>i||e.matchPriority)).sort(((e,t)=>{if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}function s(e,t){const n=[],r=e.toLowerCase(),i=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${o(e)}`:o(e)})`)).join("(.*?)")+"(.*)";return n.push({fullMatch:new RegExp(`(${o(e)})`,"i"),re:new RegExp(`^${i}`,"i"),reserveRe:new RegExp(i,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(r)&&n.push({fullMatch:t[r],re:t[r],fuzzyHighlight:!1}),n}function l(e,t,n){const r=[];return e&&n?(n.forEach((e=>{const{fullMatch:n,re:o,reserveRe:i}=e;n.lastIndex=0,o.lastIndex=0;const a=n.exec(t),s=a||o.exec(t)||i&&i.exec(t);if(e.fuzzyHighlight=!a,s)if(e.fuzzyHighlight){let e=s.index
;for(let t=1;t<s.length;t++){const n=s[t],o=s[t].length;if(t%2){const t=n.startsWith(" ")||n.startsWith("/")||n.startsWith("-");r[t?e+1:e]=!0}e+=o}}else for(let e=0;e<s[0].length;e++)r[s.index+e]=!0})),r):r}!function(e){e[e.Low=0]="Low",e[e.MediumLow=1]="MediumLow",e[e.Medium=2]="Medium",e[e.High=3]="High",e[e.Highest=4]="Highest"}(i||(i={}))},260598:(e,t,n)=>{"use strict";n.d(t,{HighlightedText:()=>s});var r=n(50959),o=n(497754),i=n(170876),a=n(295059);function s(e){const{queryString:t,rules:n,text:s,className:l}=e,c=(0,r.useMemo)((()=>(0,i.getHighlightedChars)(t,s,n)),[t,n,s]);return r.createElement(r.Fragment,null,c.length?s.split("").map(((e,t)=>r.createElement(r.Fragment,{key:t},c[t]?r.createElement("span",{className:o(a.highlighted,l)},e):r.createElement("span",null,e)))):s)}},494707:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>a});var r=n(50959),o=n(497754),i=n(536204);function a(e){return r.createElement("div",{className:o(i.separator,e.className)})}},904237:(e,t,n)=>{"use strict";var r=n(632227);t.createRoot=r.createRoot,r.hydrateRoot},117105:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 21 21m0-21-21 21"/></svg>'},315130:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 15 15m0-15-15 15"/></svg>'},238822:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 11 11m0-11-11 11"/></svg>'},663346:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 9 9m0-9-9 9"/></svg>'},534983:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 7 7m0-7-7 7"/></svg>'},654313:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M18.5 12.5a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm-1.25 5.8a7.5 7.5 0 1 1 1.06-1.06l4.22 4.23.53.53L22 23.06l-.53-.53-4.22-4.22Z"/></svg>'},606347:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M17.4 17.5a7 7 0 1 0-4.9 2c1.9 0 3.64-.76 4.9-2zm0 0l5.1 5"/></svg>'},925931:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>r});let r=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);