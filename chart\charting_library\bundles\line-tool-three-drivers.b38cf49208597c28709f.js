"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4602],{923:(e,r,t)=>{t.r(r),t.d(r,{LineToolThreeDrivers:()=>n});var o=t(792535),i=t(889868),s=t(981856);class n extends i.LineDataSource{constructor(e,r,o,i){super(e,r??n.createProperties(e.backgroundTheme().spawnOwnership()),o,i),Promise.all([t.e(6290),t.e(6881),t.e(5579),t.e(1583)]).then(t.bind(t,203414)).then((r=>{this._setPaneViews([new r.LineToolThreeDrivesPaneView(this,e)])}))}pointsCount(){return 7}name(){return"Three Drives Pattern"}static createProperties(e,r){const t=new o.DefaultProperty({defaultName:"linetoolthreedrivers",state:r,theme:e});return this._configureProperties(t),t}_getPropertyDefinitionsViewModelClass(){return Promise.all([t.e(6406),t.e(8511),t.e(5234),t.e(4590),t.e(8537)]).then(t.bind(t,897384)).then((e=>e.PatternWithoutBackgroundDefinitionsViewModel))}static _configureProperties(e){super._configureProperties(e),e.addChild("linesColors",new s.LineToolColorsProperty([e.childs().color])),e.addChild("textsColors",new s.LineToolColorsProperty([e.childs().textcolor])),e.addChild("linesWidths",new s.LineToolWidthsProperty([e.childs().linewidth])),e.hasChild("backgroundsColors")&&e.removeProperty("backgroundsColors")}}}}]);