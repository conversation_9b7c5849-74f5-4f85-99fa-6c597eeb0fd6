(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[33786,15483,90209],{429341:e=>{e.exports={}},105332:e=>{e.exports={}},591800:(e,o,t)=>{"use strict";t.d(o,{Modifiers:()=>l,hashFromEvent:()=>a,humanReadableHash:()=>d,isMacKeyboard:()=>s,modifiersFromEvent:()=>i});var r=t(23482);const s=r.isMac||r.isIOS;var l;function i(e){let o=0;return e.shiftKey&&(o+=1024),e.altKey&&(o+=512),e.ctrlKey&&(o+=256),e.metaKey&&(o+=2048),o}function a(e){return i(e)|e.keyCode}!function(e){e[e.None=0]="None",e[e.Alt=512]="Alt",e[e.Shift=1024]="Shift",e[e.Mod=s?2048:256]="Mod",e[e.Control=256]="Control",e[e.Meta=2048]="Meta"}(l||(l={}));const n={9:"⇥",13:"↵",27:"Esc",8:s?"⌫":"Backspace",32:"Space",35:"End",36:"Home",37:"←",38:"↑",39:"→",40:"↓",45:"Ins",46:"Del",188:",",191:"/"},c={9:"Tab",13:"Enter",27:"Esc",8:"Backspace",32:"Space",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Ins",46:"Del",188:",",191:"/"};for(let e=1;e<=16;e++)n[e+111]=`F${e}`,c[e+111]=`F${e}`;function d(e){let o=function(e,o=!s){let t="";return 256&e&&(t+=h(s?"^":"Ctrl",o)),512&e&&(t+=h(s?"⌥":"Alt",o)),1024&e&&(t+=h(s?"⇧":"Shift",o)),2048&e&&(t+=h(s?"⌘":"Win",o)),t}(e);const t=255&e;return o+=t in n?n[t]:String.fromCharCode(t),o}function h(e,o){return`${e}${o?" + ":s?" ":""}`}},927301:(e,o,t)=>{"use strict";t.d(o,{decodeHTMLEntities:()=>n,getFirstSegmentOrCodePointString:()=>d,htmlEscape:()=>c});const r=/[<"'&>]/g,s=e=>`&#${e.charCodeAt(0)};`,l={"&lt;":"<","&gt;":">","&quot;":'"',"&apos;":"'","&amp;":"&","&#60;":"<","&#62;":">","&#34;":'"',"&#39;":"'","&#039;":"'","&#38;":"&"},i=Object.assign({},...Object.entries(l).map((([e,o])=>({[o]:e})))),a=new RegExp(Object.keys(l).join("|"),"g");new RegExp(Object.keys(i).join("|"),"g");function n(e){return e.replace(a,(e=>l[e]||e))}function c(e){return e.replace(r,s)}function d(e){const o=Intl.Segmenter;if(o){const t=new o(void 0,{granularity:"grapheme"}),[{segment:r}={segment:null}]=t.segment(e);return r?.toUpperCase()??null}{const o=e.codePointAt(0);return o?String.fromCodePoint(o).toUpperCase():null}}},604286:(e,o,t)=>{"use strict";t.d(o,{hotKeyDeserialize:()=>s});var r=t(927301);function s(e){return JSON.parse((0,r.decodeHTMLEntities)(e))}},572570:(e,o,t)=>{"use strict";function r(e){if(null!=e&&"boolean"!=typeof e&&!isNaN(Number(e)))return Number(e)}t.d(o,{toNumberOrUndefined:()=>r})},780792:(e,o,t)=>{"use strict";t.d(o,{AbstractIndicator:()=>a});var r=t(735566),s=t(623213),l=t(534197);t(673747);const i=(0,r.getLogger)("GUI.Blocks.AbstractIndicator");class a{constructor(e){this._classSuffix="",this._quoteSessionPrefix="abstract-indicator",this._shortMode=!1,this._showTooltip=!0,this._subscribed=!1,this._tooltipType="custom",this._lastTooltipText="",this._quoteSession=e.quoteSession}getValue(){return this._value}getTooltipText(){return this._labelMap[this._value]||""}getLabel(){return this._labelMap[this._value]||""}getElement(){return this._el}update(e,o){this._updateValue(e,o),this._render()}setTooltipEnabled(e=!1){this._showTooltip!==e&&(this._showTooltip=e,
this._renderTooltip())}enableShortMode(){!0!==this._shortMode&&(this._shortMode=!0,this._render())}disableShortMode(){!1!==this._shortMode&&(this._shortMode=!1,this._render())}isShortModeEnabled(){return this._shortMode}start(){!this._subscribed&&this._symbolName&&(this._quoteSession||(this._quoteSession=(0,l.getQuoteSessionInstance)("simple")),this._quoteSession.subscribe(this._getQuoteSessionId(),this._symbolName,this.update.bind(this)),this._subscribed=!0)}stop(){this._subscribed&&this._quoteSession&&this._symbolName&&(this._quoteSession.unsubscribe(this._getQuoteSessionId(),this._symbolName),this._subscribed=!1)}setSessionStatusIcon(e){this._sessionStatusIcon!==e&&(this._sessionStatusIcon=e,this._render())}_init(e){this._el=e.el?e.el:document.createElement("span"),this._el.innerHTML="",this._classMap=e.classMap,this._iconClassMap=e.iconClassMap,this._labelMap=e.labelMap,this._showTooltip=e.showTooltip,this._classSuffix=e.classSuffix,this._symbolName=e.symbol,this._sessionStatusIcon=e.sessionStatusIcon,this._onValueChange=e.onValueChange,e.tooltipType&&(this._tooltipType=e.tooltipType),this._quoteSessionGUID=(0,s.guid)(),!0===e.short&&this.enableShortMode(),e.data&&this._updateValue(e.data)}_clearClasses(){Object.values(this._classMap).map((e=>{this._el.classList.remove(`${e}`),this._el.classList.remove(`${e}${this._classSuffix}`)}))}_render(){this._renderClasses(),this._renderTooltip(),this._renderLabel()}_renderLabel(){this._el.textContent=this.getLabel()}_updateValue(e,o){const t=this._getValueFromData(e);(o||t!==this._value)&&(this._value=t,this._onValueChange?.(this._value))}_renderClasses(){const e=this._el.classList;e.add(this._componentClass,this._componentClass+this._classSuffix);const o=this._classMap[this._value];for(const t in this._classMap){const r=this._classMap[t];r&&(r===o?(e.add(r,r+this._classSuffix),this._sessionStatusIcon&&e.add(r+"__withIcon")):(e.remove(r,r+this._classSuffix),this._sessionStatusIcon&&e.remove(r+"__withIcon")))}!o&&this._value&&i.logWarn(`no className for status ${this._value}`)}_renderTooltip(){const e=this._showTooltip?this.getTooltipText():"";e!==this._lastTooltipText&&(this._lastTooltipText=e,this._el.setAttribute("title",e),"custom"===this._tooltipType&&this._el.classList.toggle("apply-common-tooltip",this._showTooltip))}_getQuoteSessionId(){return`${this._quoteSessionPrefix}.${this._quoteSessionGUID}`}}},719240:(e,o,t)=>{"use strict";t.d(o,{DataModeIndicator:()=>n});var r=t(444372),s=(t(142492),t(429341),t(780792));const l={connecting:"tv-data-mode--connecting",delayed:"tv-data-mode--delayed",delayed_streaming:"tv-data-mode--delayed",endofday:"tv-data-mode--endofday",forbidden:"tv-data-mode--forbidden",realtime:"tv-data-mode--realtime",snapshot:"tv-data-mode--snapshot",loading:"tv-data-mode--loading",replay:"tv-data-mode--replay"};function i(){return{connecting:r.t(null,{context:"data_mode_connecting_letter"},t(167040)),delayed:r.t(null,{context:"data_mode_delayed_letter"},t(800919)),delayed_streaming:r.t(null,{context:"data_mode_delayed_streaming_letter"},t(933088)),
endofday:r.t(null,{context:"data_mode_end_of_day_letter"},t(918400)),forbidden:r.t(null,{context:"data_mode_forbidden_letter"},t(514149)),realtime:r.t(null,{context:"data_mode_realtime_letter"},t(650940)),snapshot:r.t(null,{context:"data_mode_snapshot_letter"},t(756757)),loading:"",replay:r.t(null,{context:"data_mode_replay_letter"},t(745540))}}const a={streaming:"realtime"};class n extends s.AbstractIndicator{constructor(e){super(e),this._quoteSessionPrefix="data-mode-indicator",this._componentClass="tv-data-mode",this._init(e)}getLabel(){return!0===this._shortMode?this._shortLabelMap[this._value]||"":super.getLabel()}setMode(e,o){this.update({values:{update_mode:e,update_mode_seconds:o}})}hide(){this._el.classList.add("i-hidden")}show(){this._el.classList.remove("i-hidden")}getTooltipText(){let e="";const o=this.getValue();if(""===o)return e;switch(o){case"delayed":e=r.t(null,void 0,t(369539));break;case"delayed_streaming":e=r.t(null,void 0,t(167476));break;default:e=this._labelMap[o]||e}return["delayed","delayed_streaming"].includes(o)&&(e=e.format({number:String(Math.round(this._modeInterval/60))})),e}_init(e={}){const o=Object.assign({},{classMap:l,classSuffix:"",data:{values:{update_mode:"connecting"}},labelMap:{connecting:r.t(null,void 0,t(366891)),delayed:r.t(null,void 0,t(739688)),delayed_streaming:r.t(null,void 0,t(739688)),endofday:r.t(null,void 0,t(328304)),forbidden:r.t(null,void 0,t(909161)),realtime:r.t(null,void 0,t(303058)),snapshot:r.t(null,void 0,t(988408)),loading:"",replay:r.t(null,void 0,t(38822))},modeInterval:600,short:!1,shortLabelMap:i(),showTooltip:!0,tooltipType:"custom"},e);this._modeInterval=o.modeInterval||600,this._shortLabelMap=o.shortLabelMap||i(),super._init(o),this._render()}_getValueFromData(e){let o;return o=void 0!==e.values&&void 0!==e.values.update_mode?e.values.update_mode:this.getValue(),a[o]||o}_updateValue(e,o){void 0!==e.values&&void 0!==e.values.update_mode_seconds&&(this._modeInterval=e.values.update_mode_seconds),super._updateValue(e,o)}}},600303:(e,o,t)=>{"use strict";t.d(o,{MarketStatusIndicator:()=>a});var r=t(444372),s=t(780792);t(105332);const l={invalid:"tv-market-status--invalid",market:"tv-market-status--market",out_of_session:"tv-market-status--out-of-session",post_market:"tv-market-status--post-market",pre_market:"tv-market-status--pre-market",loading:"tv-market-status--loading",holiday:"tv-market-status--holiday",replay:"tv-market-status--replay",delisted:"tv-market-status--delisted"},i={invalid:"tv-market-status__icon--invalid",market:"tv-market-status__icon--market",out_of_session:"tv-market-status__icon--out-of-session",post_market:"tv-market-status__icon--post-market",pre_market:"tv-market-status__icon--pre-market",loading:"tv-market-status__icon--loading",holiday:"tv-market-status__icon--holiday",replay:"tv-market-status__icon--replay",delisted:"tv-market-status__icon--delisted"};class a extends s.AbstractIndicator{constructor(e){super(e),this._quoteSessionPrefix="market-status-indicator",this._componentClass="tv-market-status",this._extraTitle="",
this._init(e)}setStatus(e,o){const t={values:{current_session:"delisted"===this.getValue()?"delisted":e}};this.update(t,o)}getTooltipText(){let e=super.getTooltipText();return""===e||""!==this._extraTitle&&(e=`${e}, ${this._extraTitle}`),e}setExtraTitle(e){this._extraTitle=e}reset(){this._clearClasses(),this._labelEl.textContent="",this._extraTitle="",this._el.setAttribute("title",""),this._value=""}enableShortMode(e=!0){void 0!==this._labelEl&&this._labelEl.classList.add("i-hidden"),super.enableShortMode()}disableShortMode(){void 0!==this._labelEl&&this._labelEl.classList.remove("i-hidden"),super.disableShortMode()}_renderLabel(){this._labelEl.textContent=this.getLabel()}_getValueFromData(e){return e.values?.typespecs?.includes("discontinued")?"delisted":void 0!==e.values?.current_session?e.values.current_session:this.getValue()}_render(){this._renderLabelElement(),this._sessionStatusIcon?this._renderIconElement():this._renderDotElement(),super._render()}_init(e){const o=Object.assign({},function(){const e={invalid:r.t(null,void 0,t(652969)),market:r.t(null,void 0,t(241410)),out_of_session:r.t(null,void 0,t(762464)),post_market:r.t(null,void 0,t(673897)),pre_market:r.t(null,void 0,t(236018)),loading:r.t(null,void 0,t(786726)),holiday:r.t(null,void 0,t(987845)),delisted:r.t(null,void 0,t(254602)),replay:""};return{classMap:l,iconClassMap:i,classSuffix:"",data:{},extraTitle:"",labelMap:e,short:!1,showTooltip:!0,tooltipType:"custom"}}(),e);super._init(o),this.setExtraTitle(o.extraTitle),this._render()}_renderLabelElement(){void 0===this._labelEl&&(this._labelEl=document.createElement("span"),this._labelEl.classList.add(`${this._componentClass}__label`),this._labelEl.classList.add(`${this._componentClass}__label${this._classSuffix}`),this._el.appendChild(this._labelEl))}_renderDotElement(){this._el.contains(this._iconEl)&&this._el.removeChild(this._iconEl),void 0===this._dotEl&&(this._dotEl=document.createElement("span"),this._dotEl.classList.add(`${this._componentClass}__dot`),this._dotEl.classList.add(`${this._componentClass}__dot${this._classSuffix}`),this._el.appendChild(this._dotEl))}_renderIconElement(){this._el.contains(this._dotEl)&&this._el.removeChild(this._dotEl),void 0===this._iconEl&&this._value&&(this._iconEl=document.createElement("span"),this._iconEl.classList.add(`${this._componentClass}__icon`),this._iconEl.classList.add(`${this._componentClass}__icon${this._classSuffix}`),this._el.appendChild(this._iconEl))}}},40763:(e,o,t)=>{"use strict";function r(e){return e.endsWith("_dly")}function s(e){return!!e&&(!(void 0===e.symbolname||!r(e.symbolname))||Boolean("permission_denied"===e.status&&e.values&&void 0!==e.values.alternative&&r(e.values.alternative)))}t.d(o,{isSfQuoteData:()=>s})},416234:(e,o,t)=>{"use strict";t.d(o,{SymbolGroup:()=>r});var r;t(142492),t(444372);!function(e){e.NseFlag="nse_free",e.McxFlag="mcx_free"}(r||(r={}))},345848:(e,o,t)=>{"use strict";t.d(o,{trackEvent:()=>i});t(251954);var r=t(931924);const s=(0,t(735566).getLogger)("Common.TrackEvent");let l=!1;const i=(e,o,t)=>{if(l)return
;let i=(e?e+":":"")+o;t&&(i+=" "+t),s.logNormal(i),r.enabled("widget")||!window._UNIT_TESTS&&window.gtag&&window.gtag("event",o,{event_category:e,event_label:t})};"undefined"!=typeof window&&(window.TradingView=window.TradingView||{},window.TradingView.trackEvent=i)},808708:(e,o,t)=>{"use strict";t.d(o,{NumericFormatter:()=>l});var r=t(781441),s=t(976531);class l{constructor(e={}){this._options=e}format(e,o){if(!Number.isFinite(e))return String(e);const{ignoreLocaleNumberFormat:t,precision:i,minPrecision:a}=this._options,n=(0,s.getNumberFormat)(t||o?.ignoreLocaleNumberFormat);return void 0===i?l._formatNoEImpl(e,n,i,a):(0,r.formatNumber)(e,n,i,void 0,a)}parse(e,o){const{ignoreLocaleNumberFormat:t,precision:l}=this._options,i=(0,s.getNumberFormat)(t||o?.ignoreLocaleNumberFormat);let a=(0,r.parseNumber)(e,i);return Number.isFinite(a)?(l&&(a=+a.toFixed(l)),{res:!0,value:a}):{res:!1}}static formatNoE(e,o){return this._formatNoEImpl(e,o)}static _formatNoEImpl(e,o,t,s){return Number.isFinite(e)?(o=o??{groupingSeparator:"",decimalSign:"."},(0,r.formatNumber)(e,o,t,!0,s)):String(e)}}},722652:(e,o,t)=>{"use strict";t.d(o,{PercentageFormatter:()=>l});var r=t(89831),s=t(969680);class l extends r.PriceFormatter{constructor(e={}){void 0!==e.decimalPlaces&&(e.priceScale=Math.pow(10,e.decimalPlaces)),super(e),this.type="percentage"}state(){return{...super.state(),percent:!0}}parse(e,o){return e=e.replace("%",""),super.parse(e,o)}format(e,o={}){const{useRtlFormat:t=!0}=o,r=super.format(e,{...o,useRtlFormat:!1})+"%";return t?(0,s.forceLTRStr)(r):r}static serialize(e){return e.state()}static deserialize(e){return new l(e)}}},280355:(e,o,t)=>{"use strict";t.d(o,{PriceColorer:()=>r});var r,s=t(335048),l=t(790188);!function(e){function o(e,o){return 0===e?o&&void 0!==o.neutral?o.neutral:s.color.black70:e>0?o&&void 0!==o.up?o.up:l.colorsPalette["color-success"]:o&&void 0!==o.down?o.down:l.colorsPalette["color-danger"]}e.formatSign=o,e.formatDiff=function(e,t){return o(t-e)},e.domDifference=function(e,o,t=0,r="",s=""){function l(e){const o=document.createElement("span");return o.innerHTML=e,o}const i=document.createElement("div");if(!t)return i.appendChild(l(e)),i;const a=e+"",n=o+"";let c=null;if(a.length===n.length){for(let e=0;e<a.length;e++)if(a.charAt(e)!==n.charAt(e)){c=e;break}}else c=Number("0");if(null===c)return i.appendChild(l(a)),i;const d=l(a.substring(0,c)),h=document.createElement("span");return h.className=t<0?r:s,h.appendChild(l(a.substring(c))),i.appendChild(d),i.appendChild(h),i}}(r||(r={}))},51196:(e,o,t)=>{"use strict";t.d(o,{PLACE_HOLDER:()=>i,VolumeFormatter:()=>c});var r=t(444372),s=t(808708),l=t(536794);const i="---",a=[{value:3,letter:"K"},{value:6,letter:"M"},{value:9,letter:"B"},{value:12,letter:"T"}];function n(e,o){const t=e-o;return a.find((e=>e.value>=t))??a[a.length-1]}class c{constructor(e={}){this.type="volume";const{precision:o=0,minPrecision:t=0,dimensionPrecision:r=2,dimensionMinPrecision:l=0,significantDigits:i=3,ignoreLocaleNumberFormat:a,removeSpaceBeforeDimension:n}=e;this._precision=o,
this._formatter=new s.NumericFormatter({ignoreLocaleNumberFormat:a,precision:this._precision,minPrecision:t}),this._dimensionPrecision=r,this._dimensionFormatter=new s.NumericFormatter({ignoreLocaleNumberFormat:a,precision:this._dimensionPrecision,minPrecision:l}),this._significantDigits=i,this._fractionalValues=void 0!==o&&o>0,this._spaceBeforeDimension=n?"":" ",this._options=e}state(){const{ignoreLocaleNumberFormat:e,...o}=this._options;return o}format(e,o){if(!(0,l.isNumber)(e))return i;if(Math.abs(e)>=1e100)return r.t(null,void 0,t(696935));let s="";Math.abs(e)<1&&(e=+e.toFixed(this._precision)),e<0?s="−":e>0&&o?.signPositive&&(s="+"),e=Math.abs(e);const a=!!(o?.ignoreLocaleNumberFormat??this._options.ignoreLocaleNumberFormat);let c,d,h=Math.floor(Math.log10(e))+1;if(h<=this._significantDigits&&(e=+e.toFixed(this._precision),h=Math.floor(Math.log10(e))+1),h<=this._significantDigits)c=this._formatNumber(e,a,this._formatter);else{let o=n(h,this._significantDigits);const t=Math.pow(10,o.value);e=+(e/t).toFixed(this._dimensionPrecision)*t,o=n(Math.floor(Math.log10(e))+1,this._significantDigits),c=this._formatNumber(e/Math.pow(10,o.value),a,this._dimensionFormatter),d=o.letter}return d?`${s}${c}${this._spaceBeforeDimension}${d}`:`${s}${c}`}parse(e,o){if("---"===e)return{error:"not a number",res:!1,value:NaN};const t={K:1e3,M:1e6,B:1e9,T:1e12},r=(e=e.replace("−","-")).slice(-1);if(t.hasOwnProperty(r)){const s=this._formatter.parse(e.slice(0,-1).trim(),o),i=s.res?s.value:NaN;return(0,l.isNumber)(i)?{res:!0,value:i*t[r]}:{error:"not a number",res:!1,value:NaN}}{const t=this._formatter.parse(e.trim(),o),r=t.res?t.value:NaN;return(0,l.isNumber)(r)?{res:!0,value:r}:{error:"not a number",res:!1,value:NaN}}}static serialize(e){return e.state()}static deserialize(e){return new c(e)}_formatNumber(e,o,t){if(this._fractionalValues&&0!==e){const o=14-Math.ceil(Math.log10(e)),t=Math.pow(10,o);e=Math.round(e*t)/t}return t.format(e,{ignoreLocaleNumberFormat:o})}}},335048:(e,o,t)=>{"use strict";t.d(o,{color:()=>r.color});var r=t(745269)},23203:(e,o,t)=>{"use strict";t.d(o,{makeSymbolPageUrl:()=>g,makeTemplateSymbolUrl:()=>m});var r=t(199583),s=t(912465),l=t(974629),i=t(889267),a=t(519073);function n(e){const o={...e};if("spread"===o.type||"expression"===o.type){const e=o.shortName&&c(o.shortName),t=o.proName&&c(o.proName);o.type=void 0,o.shortName=e,o.proName=t}return o}function c(e){return(0,i.tokenize)(e).find((e=>"symbol"===e.type))?.value}function d(e){const o={shortName:e.shortName,exchange:e.exchange,proName:e.proName,type:e.type,typespecs:e.typespecs,root:e.root};return o.proName&&o.proName.includes(":")&&([o.exchange,o.shortName]=o.proName.split(":")),o}function h(e,o){const t=encodeURIComponent(o.shortName||""),r=encodeURIComponent(o.exchange||""),s=encodeURIComponent(o.proName||""),l=encodeURIComponent(o.root||"");return e.replace("{tvexchange}",r).replace("{tvsymbol}",t).replace("{tvprosymbol}",s).replace("{tvroot}",l)}function u(e,o=""){const t=d(e),{type:r,typespecs:s,shortName:l,proName:i,exchange:n,root:c}=t
;return void 0===l&&void 0===i?(console.warn("Params missed"),"/"):r||s?"commodity"===r&&s&&s.includes("cfd")?"/symbols/{tvsymbol}/?exchange={tvexchange}":!c||!s||"futures"!==r||s.includes("continuous")&&l?.endsWith("1!")||s.includes("exchange-continuous")?n&&("forex"===r||s&&(0,a.hasCryptoTypespec)(s))?"/symbols/{tvsymbol}/?exchange={tvexchange}":n?`/symbols/{tvexchange}-{tvsymbol}/${o}`:`/symbols/{tvsymbol}/${o}`:`/symbols/{tvexchange}-{tvroot}1!/${o}?contract={tvsymbol}`:n?`/symbols/{tvexchange}-{tvsymbol}/${o}`:`/symbols/{tvsymbol}/${o}`}function m(e,o){let t=e;if(!/{tvsymbol}|{tvexchange}|{tvprosymbol}/.test(e)){let r="tvprosymbol";void 0===o.proName&&(r="tvsymbol"),t=`${e}?tvwidgetsymbol={${r}}`}const r=h(t,d(n(o)));if(!(0,l.isSafeUrl)(r))throw new Error(`The symbol URL ${r} is not allowed.`);return r}function p(e,o,t,l){const i=(window.locale_domains?(0,s.determineBaseUrl)(window.locale_domains,l):window.location.origin)+h(e,d(n(o)));return t?(0,r.addUtmToUrl)(i,t):i}function g(e,o,t,r){const s=n(e);return p(u(s,r),s,o,t)}},190209:(e,o,t)=>{"use strict";t.d(o,{QuoteTicker:()=>k});var r=t(144731),s=t(735566),l=t(536794),i=t(722652),a=t(280355),n=t(89831),c=t(51196),d=t(600303),h=t(719240),u=t(904207),m=t(927435),p=t(534197);var g=t(572570);function _(e,o,t){const r=(0,g.toNumberOrUndefined)(e);if("economic"===o?.type&&0===r||void 0===r)return;const s=t?.customPriceFormatter||"volume"===o.format&&new c.VolumeFormatter({precision:2})||new n.PriceFormatter({priceScale:o.pricescale||100,minMove:o.minmov||1,fractional:o.fractional,minMove2:o.minmove2}),l=void 0!==t?.signPositiveChange?t.signPositiveChange:t?.signPositive;return((e,o=!0)=>o?`(${e})`:e)(s.format(r,{signPositive:l,signNegative:t?.signNegative}),t?.changeInBrackets)}var b=t(444372);function y(e){switch(e){case"PCTDY":return b.t(null,{context:"_max_len_9"},t(291500));case"PCTPAR":return b.t(null,{context:"_max_len_9"},t(46374));default:return e}}var v=t(416234);function f(e){return e===v.SymbolGroup.McxFlag}var x=t(40763);const F=(0,s.getLogger)("QuoteTicker"),C={addDescriptionTitle:!0,changeDirectionDownClass:"down",changeDirectionUpClass:"up",changeDirectionNeutralClass:"neutral",changeInBrackets:!1,changePercentInBrackets:!1,lastPriceTimeInBrackets:!0,lastReleaseDateTimeInBrackets:!0,rtcTimeInBrackets:!0,clientName:"quote-ticker",dontDyePrice:!1,fallingBg:null,growingBg:null,lastFallingClass:"falling",lastGrowingClass:"growing",quoteSession:null,signNegative:!0,signPositive:!1,customPriceFormatter:null,customTimeFormatter:null,sessionStatusClassSuffix:"--for-ticker",dataModeClassSuffix:"--for-ticker",showInvalidSymbolStatus:!1,indicatorsTooltipType:"custom",lastPriceLastCharSup:!1,lastPriceHighlightDiffOnly:!1,initedHook:void 0,setStateHook:void 0,permissionDeniedHook:void 0,noSuchSymbolHook:void 0},N=/[KMBT]/;function k(e,o,t={}){this.enabled=!0,this._symbol=e,this._symbolOriginal=null,this._options=(0,r.deepExtend)({},C,t),!1!==this._options.signNegative&&!0!==this._options.signNegative&&delete this._options.signNegative,
t.customPriceFormatter&&(this._customPriceFormatter=t.customPriceFormatter),t.customTimeFormatter&&(void 0!==t.customTimeFormatter.lastPrice||void 0!==t.customTimeFormatter.rtc?this._timeFormatter=t.customTimeFormatter:this._timeFormatter={lastPrice:t.customPriceFormatter,rtc:t.customPriceFormatter}),this._percentFormatter=new i.PercentageFormatter,this._defaultPriceFormatter=new n.PriceFormatter({priceScale:100}),this._priceFormatter=this._customPriceFormatter||this._defaultPriceFormatter,this._volumeFormatter=new c.VolumeFormatter({precision:2}),this._cache={},this._lastPrice=null,this._lastPriceFormatted="",this._lastMinMove=0,this._elements={},this._textNodes={},this._changeVolumeLetter=this._getChangeVolumeLetterCallback(),this._setElements(o),this._highlighters={},this._initHighlighters(),this._options.setStateHook&&this.setStateHook(this._options.setStateHook),this.quoteSession=this._options.quoteSession||(0,p.getQuoteSessionInstance)("simple"),this._quoteSessionEventHandler=this.onData.bind(this),this._connectTimeoutId=setTimeout(this.connect.bind(this),0)}function w(e){const o=e.search(N),t=o>=0?e.slice(o):"";return[o>0?e.substring(o,0):0===o?"":e,t]}function E(e,o,t,r,s){var l,i=null;function a(){if(l&&(clearTimeout(l),l=void 0),t||o)for(var i=0;i<e.length;i++)e[i].style.background="";if(!t||!o)for(i=0;i<e.length;i++)r&&e[i].classList.remove(r),s&&e[i].classList.remove(s)}return{show:function(n){var c=0;if(n!==i){if(null!==i&&null!==n&&(c=n-i),a(),l=setTimeout(a,750),0<c){if(o)for(var d=0;d<e.length;d++)e[d].style.background=o;else if(r)for(d=0;d<e.length;d++)e[d].classList.add(r)}else if(c<0)if(t)for(d=0;d<e.length;d++)e[d].style.background=t;else if(s)for(d=0;d<e.length;d++)e[d].classList.add(s);null!==n&&(i=n)}},hide:a}}k.prototype._setElements=function(e){this._container=e,this._elements.change=this._findElements(e,["js-symbol-change","symbol-change"]),this._elements.changeDirection=this._findElements(e,["js-symbol-change-direction","symbol-change-direction"]),this._elements.extHrsChangeDirection=this._findElements(e,["js-symbol-ext-hrs-change-direction"]),this._elements.changePercent=this._findElements(e,["js-symbol-change-pt","symbol-change-pt"]),this._elements.description=this._findElements(e,["js-symbol-description-name","symbol-description-name"]),this._elements.extHrsChange=this._findElements(e,"js-symbol-ext-hrs-change"),this._elements.extHrsChangePercent=this._findElements(e,"js-symbol-ext-hrs-change-pt"),this._elements.extHrsClose=this._findElements(e,"js-symbol-ext-hrs-close"),this._elements.industry=this._findElements(e,["js-symbol-industry","symbol-industry"]),this._elements.last=this._findElements(e,["js-symbol-last","symbol-last"]),this._elements.sector=this._findElements(e,["js-symbol-sector","symbol-sector"]),this._elements.sessionStatus=this._findElements(e,"js-symbol-session-status"),this._elements.shortName=this._findElements(e,["js-symbol-short-name","symbol-short-name"]),this._elements.updateMode=this._findElements(e,"js-data-mode"),
this._elements.lastPeriod=this._findElements(e,"js-symbol-last-period"),this._elements.updateMode.forEach((e=>e.classList.add("i-hidden"))),this._textNodes.change=this._getOrCreateTextNodes(this._elements.change),this._textNodes.changePercent=this._getOrCreateTextNodes(this._elements.changePercent),this._textNodes.extHrsChange=this._getOrCreateTextNodes(this._elements.extHrsChange),this._textNodes.extHrsChangePercent=this._getOrCreateTextNodes(this._elements.extHrsChangePercent),this._textNodes.extHrsClose=this._getOrCreateTextNodes(this._elements.extHrsClose),this._textNodes.last=this._getOrCreateTextNodes(this._elements.last),this._textNodes.open=this._findTextNodes(e,"js-symbol-open"),this._textNodes.eps=this._findTextNodes(e,"js-symbol-eps"),this._textNodes.marketCap=this._findTextNodes(e,"js-symbol-market-cap"),this._textNodes.prevClose=this._findTextNodes(e,"js-symbol-prev-close"),this._textNodes.dividends=this._findTextNodes(e,"js-symbol-dividends"),this._textNodes.priceEarnings=this._findTextNodes(e,"js-symbol-pe"),this._textNodes.volume=this._findTextNodes(e,"js-symbol-volume"),this._textNodes.high=this._findTextNodes(e,"js-symbol-high"),this._textNodes.low=this._findTextNodes(e,"js-symbol-low"),this._textNodes.currency=this._findTextNodes(e,"js-symbol-currency"),this._textNodes.lastPriceTime=this._findTextNodes(e,"js-symbol-lp-time"),this._textNodes.lastReleaseDate=this._findTextNodes(e,"js-symbol-last-release-date"),this._textNodes.rtcTime=this._findTextNodes(e,"js-symbol-rtc-time"),this._elements.lastHighlight=this._options.lastPriceHighlightDiffOnly?this._elements.last.map((e=>this._appendAndGetNewElement(e,"span"))):this._elements.last,this._textNodes.lastHighlight=this._getOrCreateTextNodes(this._elements.lastHighlight),this._elements.lastSup=this._options.lastPriceLastCharSup?this._elements.lastHighlight.map((e=>this._appendAndGetNewElement(e,"sup"))):[],this._textNodes.lastSup=this._getOrCreateTextNodes(this._elements.lastSup)},k.prototype._findElements=function(e,o){var t=(0,l.isArray)(o)?o:[o];return Array.prototype.concat.apply([],t.map((o=>Array.prototype.slice.call(e.getElementsByClassName(o)))))},k.prototype._findTextNodes=function(e,o){return this._getOrCreateTextNodes(this._findElements(e,o))},k.prototype._getOrCreateTextNodes=function(e){return e.map((e=>{var o=this._getFirstTextNode(e);return o||(o=e.ownerDocument.createTextNode(""),e.appendChild(o)),o}))},k.prototype._appendAndGetNewElement=function(e,o){const t=document.createElement(o);return e.appendChild(t),t},k.prototype._getFirstTextNode=function(e){for(var o=e.childNodes,t=o.length-1;t>=0;t--)if(3===o.item(t).nodeType)return o.item(t);return null},k.prototype.connect=function(e){this._subscribed||(this._subscribedSymbol=e||this._symbol,this.quoteSession.subscribe(this._options.clientName,this._subscribedSymbol,this._quoteSessionEventHandler),this._subscribed=!0)},k.prototype.disconnect=function(){clearTimeout(this._connectTimeoutId),
this._subscribed&&(this.quoteSession.unsubscribe(this._options.clientName,this._subscribedSymbol,this._quoteSessionEventHandler),this._subscribed=!1)},k.prototype.onData=function(e,o){this.enabled&&("ok"===e.status?this.successData(e,o):"permission_denied"===e.status?this.onPermissionDenied(e):"error"===e.status&&this.errorData(e))},k.prototype.successData=function(e,o){e.values&&this.setState(e.values,e,o)},k.prototype.onPermissionDenied=function(e){(0,x.isSfQuoteData)(e)||function(e){return!!e&&(f(e.symbolname)||"permission_denied"===e.status&&f(e.values?.alternative))}(e)?this._options.permissionDeniedHook?this._options.permissionDeniedHook(e,this._symbolOriginal||this._symbol):this.errorData(e):this.downgradeData(e)},k.prototype.errorData=function(e){this._options.showInvalidSymbolStatus&&(this.setShortName(this._symbol),this._elements.sessionStatus.map((e=>new d.MarketStatusIndicator({classSuffix:this._options.sessionStatusClassSuffix,el:e,data:{values:{current_session:"invalid"}},tooltipType:this._options.indicatorsTooltipType})))),this._options.noSuchSymbolHook?(this._symbolOriginal&&this.setShortName(this._symbolOriginal),this._options.noSuchSymbolHook.call(this,e,this._symbolOriginal||this._symbol)):F.logWarn("No data for: "+this._symbol)},k.prototype.downgradeData=function(e){{const o=e&&e.values&&e.values.alternative,t=this._symbol===o;if(!(o&&-1!==o.indexOf(":"))||t)return void this.errorData(e);this._symbolOriginal=this._symbol,this._symbol=o,this._subscribed=!1,this.connect()}},k.prototype.setState=function(e,o,r){const s=r.values;this._isVolumeFormat="volume"===e.format,null==s.pricescale&&null==s.minmov&&null==s.fractional&&null==s.minmove2||(this._priceFormatter=this._customPriceFormatter||this._isVolumeFormat&&this._volumeFormatter||new n.PriceFormatter({priceScale:e.pricescale||100,minMove:e.minmov||1,fractional:e.fractional,minMove2:e.minmove2}));const l=this._options,i=this._percentFormatter,a=this._priceFormatter,p=this._defaultPriceFormatter,g=this._volumeFormatter,b=(e,o=!0)=>o?`(${e})`:e,v=void 0!==l.signPositiveChange?l.signPositiveChange:l.signPositive,f=e=>_(e,s,l),x=e=>{const o=i.format(e,{signPositive:v,signNegative:l.signNegative});return b(o,l.changePercentInBrackets)},C=a.format.bind(a),N=p.format.bind(p),k=g.format.bind(g),w=e=>null==e?c.PLACE_HOLDER:N(e);l.disableChange||(this._setNodesValue(this._textNodes.change,s.change,f),this._setNodesValue(this._textNodes.changePercent,s.change_percent,x),null!=s.change&&(l.dontDyePrice||this._setChangeFontColor([].concat(this._elements.change,this._elements.changePercent),s.change,l.changeUpFontColor,l.changeDownFontColor,l.changeNeutralFontColor),this._setChangeDirection(this._elements.changeDirection,s.change)),this._setNodesValue(this._textNodes.extHrsChange,s.rch,f),this._setNodesValue(this._textNodes.extHrsChangePercent,s.rchp,x),null!=s.rch&&(l.dontDyePrice||this._setChangeFontColor([].concat(this._elements.extHrsChange,this._elements.extHrsChangePercent),s.rch,l.changeUpFontColor,l.changeDownFontColor,l.changeNeutralFontColor),
this._setChangeDirection(this._elements.extHrsChangeDirection,s.rch))),this._setNodesValue(this._textNodes.prevClose,s.prev_close_price,C),this._setNodesValue(this._textNodes.dividends,e.dividends_yield,(e=>null==e?c.PLACE_HOLDER:i.format(e,{signPositive:l.signPositive,signNegative:l.signNegative})),!0);let E=(0,u.getTranslatedSymbolDescription)(s);if(E&&(E=this._prepareSymbolDescription(E),this._setTextsContent(this._elements.description,E),this._options.addDescriptionTitle))for(var S=0;S<this._elements.description.length;S++)this._elements.description[S].setAttribute("title",E);if((null!=s.short_name||null!=s.exchange)&&e.short_name){var T=e.short_name;"QUANDL"===e.exchange&&void 0!==e.short_name.split("/")[1]&&(T=e.short_name.split("/")[1]+", "+e.short_name.split("/")[0]),this.setShortName(T)}e["reference-last-period"]&&this._elements.lastPeriod.length&&Promise.all([t.e(43878),t.e(3844)]).then(t.bind(t,800620)).then((o=>{this._setTextsContent(this._elements.lastPeriod,o.periodFormatter(e["reference-last-period"]))})),this._elements.lastPeriod.length&&void 0===e["reference-last-period"]&&this._setTextsContent(this._elements.lastPeriod,"—"),this._setLastValue(s.last_price,s.minmove2,C),this._setNodesValue(this._textNodes.extHrsClose,s.rtc,C),s.rtc&&this._highlighters.extHrsClose.show(s.rtc),s.industry&&this._setTextsContent(this._elements.industry,e.industry,(e=>e)),s.sector&&this._setTextsContent(this._elements.sector,e.sector),this._elements.sessionStatus&&s.current_session&&(this._sessionStatusInstances?this._sessionStatusInstances.forEach((e=>e.setStatus(s.current_session))):this._sessionStatusInstances=this._elements.sessionStatus.map((o=>new d.MarketStatusIndicator({classSuffix:this._options.sessionStatusClassSuffix,el:o,short:!0,data:{values:s},quoteSession:this.quoteSession,symbol:e.original_name,manualUpdate:!0,tooltipType:this._options.indicatorsTooltipType,sessionStatusIcon:this._options.sessionStatusIcon})))),this._setNodesValue(this._textNodes.open,s.open_price,C),this._setNodesValue(this._textNodes.high,s.high_price,C),this._setNodesValue(this._textNodes.low,s.low_price,C),this._setNodesValue(this._textNodes.eps,e.earnings_per_share_basic_ttm,w,!0),this._setNodesValue(this._textNodes.priceEarnings,e.price_earnings_ttm,w,!0),this._setNodesValue(this._textNodes.marketCap,e.market_cap_basic,k,!0),this._setNodesValue(this._textNodes.volume,e.volume,k,!0);const D=(0,m.prepareCurrencyValue)(s);if(D&&this._setNodesValue(this._textNodes.currency,D,y,!0),(s.lp_time||e.lp_time)&&this._textNodes.lastPriceTime.length&&(this._timeFormatter&&this._timeFormatter.lastPrice?this._setNodesValue(this._textNodes.lastPriceTime,b(this._timeFormatter.lastPrice(e.lp_time,e.current_session),this._options.lastPriceTimeInBrackets)):F.logError("last price time field requested with no formatter provided")),
(s.last_release_date||e.last_release_date)&&this._textNodes.lastReleaseDate.length&&(this._timeFormatter&&this._timeFormatter.lastReleaseDate?this._setNodesValue(this._textNodes.lastReleaseDate,b(this._timeFormatter.lastReleaseDate(e.last_release_date,e.current_session),this._options.lastReleaseDateTimeInBrackets)):F.logError("lastReleaseDate time field requested with no formatter provided")),(s.rtc_time||e.rtc_time)&&this._textNodes.rtcTime.length&&(this._timeFormatter&&this._timeFormatter.rtc?this._setNodesValue(this._textNodes.rtcTime,b(this._timeFormatter.rtc(e.rtc_time,e.current_session),this._options.rtcTimeInBrackets)):F.logError("rtc time field requested with no formatter provided")),null!=s.last_price)for(S=0;S<this._elements.updateMode.length;S++)this._elements.updateMode[S].classList.remove("i-hidden");this._elements.updateMode&&(s.update_mode||s.update_mode_seconds)&&(this._updateModeInstances?this._updateModeInstances.forEach((o=>o.update({values:e}))):this._updateModeInstances=this._elements.updateMode.map((o=>new h.DataModeIndicator({classSuffix:this._options.dataModeClassSuffix,el:o,data:{values:e},modeInterval:e.update_mode_seconds,short:!0,tooltipType:this._options.indicatorsTooltipType})))),this._setStateHook&&this._setStateHook(o.values,r.values,o.complete,this.getOptions()),this._lastPrice?this._highlighters.last.show(e.last_price):null===this._lastPrice&&(this._container.classList.add("quote-ticker-inited"),"function"==typeof this._options.initedHook&&this._options.initedHook("last_price"in e)),this._lastPrice=e.last_price},k.prototype._setNodesValue=function(e,o,t,r){if(null!=o||r)for(var s="function"==typeof t?t(o):o,l=0;l<e.length;l++)e[l].nodeValue=s},k.prototype._setTextsContent=function(e,o,t){if(e&&e.length&&o)for(var r="function"==typeof t?t(o):o,s=0;s<e.length;s++)e[s].textContent=r},k.prototype._setLastValue=function(e,o,t){if(null==e)return;const r="function"==typeof t?t(e):String(e);if(r===this._lastPriceFormatted)return;const[s,l,i]=this._options.lastPriceHighlightDiffOnly?this._getLastValueStringDiff(this._lastPriceFormatted,r):["",r,""],a=s,n=this._calculatePipetteDigits(o),[c,d]=this._options.lastPriceLastCharSup&&n?[l.slice(0,-n),l.slice(-n)]:[l,""];this._options.lastPriceHighlightDiffOnly&&(this._isVolumeFormat&&this._changeVolumeLetter.call(this,i),this._setNodesValue(this._textNodes.last,a)),this._setNodesValue(this._textNodes.lastHighlight,c),this._setNodesValue(this._textNodes.lastSup,d),this._lastPriceFormatted=r},k.prototype._getChangeVolumeLetterCallback=function(){let e;return function(o){o!==e&&(e=o,this._elements.last.forEach((o=>{3!==o.childNodes.length?o.appendChild(document.createTextNode(e)):o.childNodes[2].nodeValue=e})))}},k.prototype._getLastValueStringDiff=function(e,o){if(e===o){const[e,t]=w(o);return[e,"",t]}let t=0;for(;e[t]===o[t];)t++;const[r,s]=w(o.slice(t));return[o.slice(0,t),r,s]},k.prototype._calculatePipetteDigits=function(e){return e&&e!==this._lastMinMove&&(this._lastMinMove=e),this._lastMinMove?(""+this._lastMinMove).length-1:0},
k.prototype._setChangeFontColor=function(e,o,t,r,s){for(var l=a.PriceColorer.formatSign(o,{up:t,down:r,neutral:s}),i=0;i<e.length;i++)e[i].style.color=l},k.prototype._setChangeDirection=function(e,o){for(var t=0;t<e.length;t++){var r=e[t].classList;r.toggle(this._options.changeDirectionUpClass,o>0),r.toggle(this._options.changeDirectionDownClass,o<0),r.toggle(this._options.changeDirectionNeutralClass,0===o)}},k.prototype.setShortName=function(e=""){this._setTextsContent(this._elements.shortName,e)},k.prototype._prepareSymbolDescription=function(e){var o=this._getCache("symbol-description:"+e);return o||(o=e,this._setCache("symbol-description:"+e,o),o)},k.prototype._initHighlighters=function(){this._highlighters.last=new E(this._elements.lastHighlight,this._options.growingBg,this._options.fallingBg,this._options.lastGrowingClass,this._options.lastFallingClass),this._highlighters.extHrsClose=new E(this._elements.extHrsClose,this._options.growingBg,this._options.fallingBg,this._options.lastGrowingClass,this._options.lastFallingClass)},k.prototype.disable=function(){this.enabled=!1,this.disconnect()},k.prototype.enable=function(){this.enabled=!0,this.connect()},k.prototype.setStateHook=function(e){null===e&&this._setStateHook?delete this._setStateHook:"function"==typeof e&&(this._setStateHook=e)},k.prototype._setCache=function(e,o){null==o?delete this._cache[e]:this._cache[e]=o},k.prototype._getCache=function(e){return this._cache&&this._cache[e]},k.prototype.getOptions=function(){return this._options}},904207:(e,o,t)=>{"use strict";t.d(o,{getTranslatedSymbolDescription:()=>l});var r=t(444372);function s(e){const o=`#${e}-symbol-description`,s=r.t(o,void 0,t(956316));return s===o?null:s}function l(e){if(void 0!==e.pro_name){const o=s(e.pro_name);if(null!==o)return o;if(void 0!==e.short_name){const o=s(e.short_name);if(null!==o)return o}}return"en"!==e.language&&void 0!==e.local_description&&e.language===window.language||"en"===e.language&&void 0!==e.local_description&&e.language===window.language?e.local_description:e.description||""}},927435:(e,o,t)=>{"use strict";t.d(o,{prepareCurrencyValue:()=>s});var r=t(444372);function s(e){let o=[];const s=function(e){switch(e){case"PCT":return"%";case"PCTPAR":return r.t(null,{context:"_max_len_9"},t(46374));case"PCTGDP":return r.t(null,{context:"_max_len_9"},t(421685));default:return e}}(e.currency_code||e.value_unit_id||"");switch(e.measure){case"price":case"unit":o=[s,e.unit_id];break;default:o=[s]}return o.filter(Boolean).join(" / ")}},982528:(e,o,t)=>{"use strict";function r(e){e.preventDefault()}t.d(o,{preventDefault:()=>r,preventDefaultForContextMenu:()=>l});const s=["input:not([type])",'input[type="text"]','input[type="email"]','input[type="password"]','input[type="search"]','input[type="number"]','input[type="url"]',"textarea","a[href]",'*[contenteditable="true"]',"[data-allow-context-menu]"];function l(e){const o=e.target;o&&!o.closest(s.join(", "))&&e.preventDefault()}},130714:e=>{"use strict"
;e.exports=JSON.parse('{"color-white":"#ffffff","color-black":"#000000","color-transparent":"#00000000","color-cold-gray-50":"#F9F9F9","color-cold-gray-100":"#F2F2F2","color-cold-gray-150":"#EBEBEB","color-cold-gray-200":"#DBDBDB","color-cold-gray-250":"#C9C9C9","color-cold-gray-300":"#B8B8B8","color-cold-gray-350":"#A8A8A8","color-cold-gray-400":"#9C9C9C","color-cold-gray-450":"#8C8C8C","color-cold-gray-500":"#808080","color-cold-gray-550":"#707070","color-cold-gray-600":"#636363","color-cold-gray-650":"#575757","color-cold-gray-700":"#4A4A4A","color-cold-gray-750":"#3D3D3D","color-cold-gray-800":"#2E2E2E","color-cold-gray-850":"#1F1F1F","color-cold-gray-900":"#0F0F0F","color-cold-gray-950":"#000000","color-ripe-red-50":"#FFEBEC","color-ripe-red-100":"#FCCBCD","color-ripe-red-200":"#FAA1A4","color-ripe-red-300":"#F77C80","color-ripe-red-400":"#F7525F","color-ripe-red-500":"#F23645","color-ripe-red-600":"#CC2F3C","color-ripe-red-700":"#B22833","color-ripe-red-800":"#991F29","color-ripe-red-900":"#801922","color-ripe-red-a100":"#FF8080","color-ripe-red-a200":"#FF5252","color-ripe-red-a400":"#FF3333","color-ripe-red-a600":"#CC2929","color-ripe-red-a700":"#802028","color-ripe-red-a800":"#4D191D","color-ripe-red-a900":"#331F20","color-tan-orange-50":"#FFF3E0","color-tan-orange-100":"#FFE0B2","color-tan-orange-200":"#FFCC80","color-tan-orange-300":"#ffb74d","color-tan-orange-400":"#FFA726","color-tan-orange-500":"#FF9800","color-tan-orange-600":"#FB8C00","color-tan-orange-700":"#F57C00","color-tan-orange-800":"#EF6C00","color-tan-orange-900":"#e65100","color-tan-orange-a100":"#FFD180","color-tan-orange-a200":"#ffab40","color-tan-orange-a400":"#FF9100","color-tan-orange-a500":"#E57E17","color-tan-orange-a600":"#CC7014","color-tan-orange-a700":"#8C541C","color-tan-orange-a800":"#593A1B","color-tan-orange-a900":"#33261A","color-iguana-green-50":"#E8F5E9","color-iguana-green-100":"#C8E6C9","color-iguana-green-200":"#A5D6A7","color-iguana-green-300":"#81c784","color-iguana-green-400":"#66BB6A","color-iguana-green-500":"#4caf50","color-iguana-green-600":"#43a047","color-iguana-green-700":"#388e3c","color-iguana-green-800":"#2E7D32","color-iguana-green-900":"#1B5E20","color-iguana-green-a100":"#B9F6CA","color-iguana-green-a200":"#69F0AE","color-iguana-green-a400":"#00E676","color-iguana-green-a700":"#00c853","color-banana-yellow-50":"#FFFDE7","color-banana-yellow-100":"#FFF9C4","color-banana-yellow-200":"#FFF59D","color-banana-yellow-300":"#FFF176","color-banana-yellow-400":"#ffee58","color-banana-yellow-500":"#ffeb3b","color-banana-yellow-600":"#fdd835","color-banana-yellow-700":"#fbc02d","color-banana-yellow-800":"#f9a825","color-banana-yellow-900":"#F57F17","color-banana-yellow-a100":"#FFFF8D","color-banana-yellow-a200":"#FFFF00","color-banana-yellow-a400":"#ffea00","color-banana-yellow-a700":"#EEDA01","color-tv-blue-50":"#E3EFFD","color-tv-blue-100":"#BBD9FB","color-tv-blue-200":"#90BFF9","color-tv-blue-300":"#5B9CF6","color-tv-blue-400":"#3179F5","color-tv-blue-500":"#2962FF","color-tv-blue-600":"#1E53E5","color-tv-blue-700":"#1848CC","color-tv-blue-800":"#143EB2","color-tv-blue-900":"#0C3299","color-tv-blue-a100":"#82b1ff","color-tv-blue-a200":"#448aff","color-tv-blue-a400":"#2979ff","color-tv-blue-a600":"#2962FF","color-tv-blue-a700":"#143A87","color-tv-blue-a800":"#142E61","color-tv-blue-a900":"#132042","color-deep-blue-50":"#EDE7F6","color-deep-blue-100":"#D1C4E9","color-deep-blue-200":"#B39DDB","color-deep-blue-300":"#9575cd","color-deep-blue-400":"#7e57c2","color-deep-blue-500":"#673ab7","color-deep-blue-600":"#5E35B1","color-deep-blue-700":"#512da8","color-deep-blue-800":"#4527A0","color-deep-blue-900":"#311B92","color-deep-blue-a100":"#b388ff","color-deep-blue-a200":"#7C4DFF","color-deep-blue-a400":"#651FFF","color-deep-blue-a700":"#6200EA","color-minty-green-50":"#DAF2EE","color-minty-green-100":"#ACE5DC","color-minty-green-200":"#70CCBD","color-minty-green-300":"#42BDA8","color-minty-green-400":"#22AB94","color-minty-green-500":"#089981","color-minty-green-600":"#06806B","color-minty-green-700":"#056656","color-minty-green-800":"#004D40","color-minty-green-900":"#00332A","color-minty-green-a100":"#A7FFF0","color-minty-green-a200":"#45E5CB","color-minty-green-a400":"#2BD9BC","color-minty-green-a600":"#24B29B","color-minty-green-a700":"#1B7667","color-minty-green-a800":"#10443B","color-minty-green-a900":"#082621","color-grapes-purple-50":"#F3E5F5","color-grapes-purple-100":"#E1BEE7","color-grapes-purple-200":"#CE93D8","color-grapes-purple-300":"#ba68c8","color-grapes-purple-400":"#ab47bc","color-grapes-purple-500":"#9c27b0","color-grapes-purple-600":"#8e24aa","color-grapes-purple-700":"#7b1fa2","color-grapes-purple-800":"#6A1B9A","color-grapes-purple-900":"#4A148C","color-grapes-purple-a100":"#EA80FC","color-grapes-purple-a200":"#E040FB","color-grapes-purple-a400":"#D500F9","color-grapes-purple-a700":"#aa00ff","color-berry-pink-50":"#FCE4EC","color-berry-pink-100":"#F8BBD0","color-berry-pink-200":"#f48fb1","color-berry-pink-300":"#f06292","color-berry-pink-400":"#ec407a","color-berry-pink-500":"#e91e63","color-berry-pink-600":"#D81B60","color-berry-pink-700":"#C2185B","color-berry-pink-800":"#AD1457","color-berry-pink-900":"#880E4F","color-berry-pink-a100":"#ff80ab","color-berry-pink-a200":"#ff4081","color-berry-pink-a400":"#f50057","color-berry-pink-a700":"#c51162","color-sky-blue-50":"#E0F7FA","color-sky-blue-100":"#B2EBF2","color-sky-blue-200":"#80DEEA","color-sky-blue-300":"#4dd0e1","color-sky-blue-400":"#26c6da","color-sky-blue-500":"#00bcd4","color-sky-blue-600":"#00acc1","color-sky-blue-700":"#0097A7","color-sky-blue-800":"#00838F","color-sky-blue-900":"#006064","color-sky-blue-a100":"#84FFFF","color-sky-blue-a200":"#18FFFF","color-sky-blue-a400":"#00e5ff","color-sky-blue-a700":"#00B8D4","color-forest-green-50":"#DAF2E6","color-forest-green-100":"#ACE5C9","color-forest-green-200":"#70CC9E","color-forest-green-300":"#42BD7F","color-forest-green-400":"#22AB67","color-forest-green-500":"#089950","color-forest-green-600":"#068043","color-forest-green-700":"#056636","color-forest-green-800":"#004D27","color-forest-green-900":"#1A3326","color-facebook":"#1877F2","color-deep-facebook":"#1564CA","color-twitter":"#1DA1F2","color-deep-twitter":"#188CD3","color-youtube":"#FF0000","color-linkedin":"#007BB5","color-seeking-alpha-brand":"#ff7200"}')
},410515:e=>{"use strict"
;e.exports=JSON.parse('{"color-header-bg":"color-white","color-body-bg":"color-white","color-body-secondary-bg":"color-cold-gray-100","color-bg-primary":"color-white","color-bg-primary-hover":"color-cold-gray-100","color-bg-secondary":"color-white","color-bg-highlight":"color-cold-gray-50","color-bg-scroll-buttons":"color-cold-gray-100","color-legacy-bg-scroll-buttons":"color-cold-gray-850","color-legacy-bg-widget":"color-white","color-text-primary":"color-cold-gray-900","color-text-secondary":"color-cold-gray-550","color-text-tertiary":"color-cold-gray-400","color-text-disabled":"color-cold-gray-300","color-accent-content":"color-cold-gray-900","color-box-shadow":"color-cold-gray-300","color-divider":"color-cold-gray-150","color-divider-hover":"color-cold-gray-100","color-divider-secondary":"color-cold-gray-100","color-active-hover-text":"color-cold-gray-900","color-alert-text":"color-cold-gray-900","color-border-table":"color-cold-gray-100","color-brand":"color-tv-blue-500","color-brand-active":"color-tv-blue-700","color-brand-hover":"color-tv-blue-600","color-chart-page-bg":"color-cold-gray-150","color-common-tooltip-bg":"color-cold-gray-800","color-danger":"color-ripe-red-400","color-danger-hover":"color-ripe-red-500","color-danger-active":"color-ripe-red-600","color-depthrenderer-stroke-style":"color-cold-gray-100","color-halal":"color-iguana-green-400","color-continuous":"color-cold-gray-500","color-tv-calculated-pair":"color-grapes-purple-400","color-highlight-new":"color-tan-orange-50","color-input-bg":"color-white","color-input-publish-bg":"color-white","color-link":"color-tv-blue-500","color-link-hover":"color-tv-blue-600","color-link-active":"color-tv-blue-700","color-list-nth-child-bg":"color-cold-gray-50","color-pane-bg":"color-white","color-pane-secondary-bg":"color-cold-gray-100","color-popup-menu-item-hover-bg":"color-cold-gray-100","color-popup-menu-separator":"color-cold-gray-150","color-primary-symbol":"color-sky-blue-500","color-screener-description":"color-cold-gray-650","color-success":"color-minty-green-500","color-success-hover":"color-minty-green-600","color-success-active":"color-minty-green-700","color-toolbar-button-text":"color-cold-gray-900","color-toolbar-button-text-hover":"color-cold-gray-900","color-toolbar-button-text-active":"color-tv-blue-500","color-toolbar-button-text-active-hover":"color-tv-blue-600","color-toolbar-button-background-hover":"color-cold-gray-100","color-toolbar-button-background-secondary-hover":"color-cold-gray-150","color-toolbar-button-background-active":"color-tv-blue-50","color-toolbar-button-background-active-hover":"color-tv-blue-100","color-toolbar-toggle-button-background-active":"color-tv-blue-500","color-toolbar-toggle-button-background-active-hover":"color-tv-blue-600","color-toolbar-toggle-button-icon":"color-cold-gray-200","color-toolbar-interactive-element-text-normal":"color-cold-gray-900","color-toolbar-opened-element-bg":"color-cold-gray-100","color-toolbar-divider-background":"color-cold-gray-150","color-popup-background":"color-white","color-popup-element-text":"color-cold-gray-900","color-popup-element-text-hover":"color-cold-gray-900","color-popup-element-background-hover":"color-cold-gray-100","color-popup-element-secondary-text":"color-cold-gray-500","color-popup-element-hint-text":"color-cold-gray-400","color-popup-element-text-active":"color-white","color-popup-element-background-active":"color-tv-blue-500","color-popup-element-toolbox-text":"color-cold-gray-500","color-popup-element-toolbox-text-hover":"color-cold-gray-900","color-popup-element-toolbox-text-active-hover":"color-tv-blue-200","color-popup-element-toolbox-background-hover":"color-cold-gray-150","color-popup-element-toolbox-background-active-hover":"color-tv-blue-700","color-tooltip-bg":"color-cold-gray-800","color-tv-dialog-caption":"color-cold-gray-650","color-tv-dropdown-item-hover-bg":"color-cold-gray-100","color-underlined-text":"color-cold-gray-550","color-widget-pages-bg":"color-white","color-warning":"color-tan-orange-500","color-growing":"color-minty-green-500","color-falling":"color-ripe-red-500","color-forex-icon":"color-cold-gray-750","color-list-item-active-bg":"color-tv-blue-400","color-list-item-hover-bg":"color-tv-blue-50","color-list-item-text":"color-cold-gray-800","color-price-axis-label-back":"color-cold-gray-150","color-price-axis-label-text":"color-cold-gray-650","color-price-axis-gear":"color-cold-gray-900","color-price-axis-gear-hover":"color-black","color-price-axis-highlight":"color-cold-gray-150","color-bid":"color-tv-blue-500","color-border":"color-cold-gray-150","color-border-chat-fields":"color-cold-gray-250","color-border-hover":"color-cold-gray-250","color-button-hover-bg":"color-cold-gray-150","color-depthrenderer-fill-style":"color-cold-gray-650","color-disabled-border-and-color":"color-cold-gray-150","color-disabled-input":"color-cold-gray-150","color-empty-container-message":"color-cold-gray-550","color-icons":"color-cold-gray-550","color-input-textarea-readonly":"color-cold-gray-650","color-input-placeholder-text":"color-cold-gray-350","color-item-active-blue":"color-tv-blue-50","color-item-hover-active-bg":"color-tv-blue-100","color-item-hover-bg":"color-tv-blue-100","color-item-hover-blue":"color-tv-blue-100","color-item-selected-blue":"color-tv-blue-50","color-item-active-text":"color-white","color-item-active-bg":"color-tv-blue-500","color-list-item":"color-cold-gray-550","color-news-highlight":"color-tv-blue-100","color-placeholder":"color-cold-gray-350","color-row-hover-active-bg":"color-cold-gray-100","color-sb-scrollbar-body-bg":"color-cold-gray-200","color-section-separator-border":"color-cold-gray-300","color-separator-table-chat":"color-cold-gray-150","color-tag-active-bg":"color-cold-gray-200","color-tag-hover-bg":"color-cold-gray-150","color-text-regular":"color-cold-gray-700","color-tv-button-checked":"color-cold-gray-550","color-scroll-bg":"color-cold-gray-400","color-scroll-border":"color-cold-gray-100","color-widget-border":"color-cold-gray-100","color-scroll-buttons-arrow":"color-white","color-control-intent-default":"color-cold-gray-200","color-control-intent-success":"color-minty-green-500","color-control-intent-primary":"color-tv-blue-500","color-control-intent-warning":"color-tan-orange-500","color-control-intent-danger":"color-ripe-red-500","color-goto-label-background":"color-cold-gray-800","color-pre-market":"color-tan-orange-600","color-pre-market-bg":"color-tan-orange-400","color-post-market":"color-tv-blue-500","color-post-market-bg":"color-tv-blue-400","color-market-open":"color-minty-green-500","color-market-open-bg":"color-minty-green-400","color-market-closed":"color-cold-gray-400","color-market-holiday":"color-cold-gray-400","color-market-expired":"color-ripe-red-500","color-invalid-symbol":"color-ripe-red-400","color-invalid-symbol-hover":"color-ripe-red-700","color-delisted-symbol":"color-ripe-red-600","color-delisted-symbol-hover":"color-ripe-red-800","color-replay-mode":"color-tv-blue-500","color-replay-mode-point-select":"color-cold-gray-350","color-replay-mode-icon":"color-white","color-replay-mode-hover":"color-tv-blue-600","color-notaccurate-mode":"color-berry-pink-600","color-delay-mode":"color-tan-orange-700","color-delay-mode-bg":"color-tan-orange-400","color-eod-mode":"color-grapes-purple-700","color-eod-mode-bg":"color-grapes-purple-400","color-data-problem":"color-ripe-red-600","color-data-problem-bg":"color-ripe-red-400","color-data-problem-hover":"color-ripe-red-700","color-list-item-bg-highlighted":"color-tv-blue-50","color-list-item-bg-selected":"color-tv-blue-100","color-list-item-bg-highlighted-hover":"color-tv-blue-100","color-list-item-bg-selected-hover":"color-tv-blue-200","color-screener-header-bg":"color-white","color-screener-header-bg-hover":"color-cold-gray-100","color-marker-flagged":"color-ripe-red-400","color-marker-flagged-hovered":"color-ripe-red-600","color-ask":"color-ripe-red-400","color-sell":"color-ripe-red-400","color-buy":"color-tv-blue-500","color-neutral":"color-cold-gray-550","color-pro":"color-minty-green-400","color-pro-hover":"color-minty-green-600","color-pro-plus":"color-tv-blue-500","color-pro-plus-hover":"color-tv-blue-600","color-pro-premium":"color-tan-orange-500","color-pro-premium-hover":"color-tan-orange-700","color-trial":"color-cold-gray-550","color-trial-hover":"color-cold-gray-550","color-mod":"color-ripe-red-400","color-mod-hover":"color-ripe-red-600","color-ad":"color-tan-orange-500","color-broker-featured":"color-minty-green-400","color-broker-featured-hover":"color-minty-green-600","color-alert-status-active":"color-minty-green-400","color-alert-status-stopped":"color-ripe-red-500","color-alert-status-triggered":"color-tan-orange-500","color-overlay":"color-cold-gray-400","color-search-button-hover":"color-cold-gray-150","color-boost-button-content-selected":"color-tv-blue-600","color-boost-button-content-hover":"color-cold-gray-900","color-boost-button-bg-hover":"color-cold-gray-150","color-boost-button-border-hover":"color-cold-gray-150","color-boost-button-border-default":"color-cold-gray-150","color-common-tooltip-text":"color-cold-gray-100","color-replay-data-mode":"color-ripe-red-400","color-legacy-success":"color-minty-green-300","color-collapse-tabs-border":"color-cold-gray-100","color-site-widget-hover":"color-cold-gray-50","color-attention":"color-banana-yellow-700","color-x-twitter-content":"color-cold-gray-900","color-card-border":"color-cold-gray-150","color-card-border-hover":"color-cold-gray-300","color-background-special-primary":"color-white","color-stroke-special-primary":"color-cold-gray-150","color-selection-bg":"color-tv-blue-100","color-default-gray":"color-cold-gray-550","color-featured-broker-badge-bg":"color-cold-gray-900","color-featured-broker-badge-bg-hover":"color-cold-gray-800","color-featured-broker-badge-text":"color-white"}')
}}]);