(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1754],{997375:e=>{e.exports={dialog:"dialog-LfNchNNG",tabletDialog:"tabletDialog-LfNchNNG",desktopDialog:"desktopDialog-LfNchNNG"}},8510:e=>{e.exports={button:"button-w6lVe_oI",hovered:"hovered-w6lVe_oI",disabled:"disabled-w6lVe_oI"}},912015:(e,t,o)=>{"use strict";o.d(t,{isPlatformMobile:()=>n});var i=o(69111);o(440891),o(601227);function n(){return!(0,i.isOnMobileAppPage)("any")&&(window.matchMedia("(min-width: 602px) and (min-height: 445px)").matches,!1)}},723698:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Components:()=>O,showDefaultSearchDialog:()=>E,showSymbolSearchItemsDialog:()=>s.showSymbolSearchItemsDialog});var i=o(688401),n=o(972535),a=o(992835),l=o(284741),s=o(558323),r=o(240534),c=o(50959),d=o(650151),u=o(56871),h=o(113060),m=o(944080),g=o(497754),S=o.n(g),p=o(609838),f=o(800296),v=o(993544),b=o(398120),I=o(177042),y=o(292069);function C(e){const{isSelected:t,existInWatchlist:i,findInWatchlist:n,addToWatchlist:a,removeFromWatchlist:l}=e,{selectedAction:s}=(0,d.ensureNotNull)((0,c.useContext)(h.SymbolSearchWatchlistContext));return c.createElement(c.Fragment,null,i?c.createElement(c.Fragment,null,c.createElement(f.ListItemButton,{className:S()(y.action,y.removeAction,t&&2===s&&y.selected,"apply-common-tooltip"),onClick:l,icon:v,title:p.t(null,void 0,o(595888))}),c.createElement(f.ListItemButton,{className:S()(y.action,y.targetAction,t&&1===s&&y.selected,"apply-common-tooltip"),onClick:n,icon:I,title:p.t(null,void 0,o(23653))})):c.createElement(f.ListItemButton,{className:S()(y.action,y.addAction,t&&0===s&&y.selected,"apply-common-tooltip"),onClick:a,icon:b,title:p.t(null,void 0,o(77957))}))}var w=o(180185),D=o(32133),W=o(979359),A=o(190266);var T=o(533408),N=o(442092),x=o(997375);(0,o(912015).isPlatformMobile)();function E(e){new r.WatchedValue({});const t=(0,l.getSymbolSearchCompleteOverrideFunction)(),{defaultValue:o,showSpreadActions:n,source:c,onSearchComplete:d,trackResultsOptions:u,...h}=e,m={...h,showSpreadActions:n??(0,a.canShowSpreadActions)(),onSymbolFiltersParamsChange:void 0,onSearchComplete:(e,o)=>{t(e[0].symbol,e[0].result).then((e=>{i.linking.setSymbolAndLogInitiator(e.symbol,"symbol search"),d?.(e.symbol)}))},onEmptyResults:void 0};(0,s.showSymbolSearchItemsDialog)({...m,defaultValue:o})}const O={SymbolSearchWatchlistDialogContentItem:function(e){const{addToWatchlist:t,removeFromWatchlist:o,findInWatchlist:i,existInWatchlist:a,isSelected:l,fullSymbolName:s,...r}=e,{onClose:g,searchRef:S,searchSpreads:p}=(0,d.ensureNotNull)((0,c.useContext)(m.SymbolSearchItemsDialogContext)),{setSelectedAction:f,isSpreadOrMultipleMode:v,addAfter:b,clearTargetSymbol:I,highlighted:y,highlight:T}=(0,d.ensureNotNull)((0,c.useContext)(h.SymbolSearchWatchlistContext)),N=v(p,S);return(0,c.useLayoutEffect)((()=>{l&&f(void 0!==a?a?2:0:null)}),[l,a]),c.createElement(u.SymbolSearchDialogContentItem,{...r,fullSymbolName:s,onClick:N?e.onClick:function(i){if(void 0===s)return;if(void 0===a)return void(0,d.ensureDefined)(e.onClick)(i)
;a?(o(W.WATCHLIST_WIDGET_ID,[s]),E("watchlist remove click",i),b===s&&I()):((0,A.runOrSignInWithPromo)("watchList",{source:"add symbol to watchlist"},(()=>{t(W.WATCHLIST_WIDGET_ID,[s],b),e.id&&T(e.id)})),E("watchlist add click",i));x(i)},isHighlighted:y===e.id,isSelected:l,actions:void 0===a||N?void 0:c.createElement(C,{isSelected:l,existInWatchlist:a,addToWatchlist:function(o){if(o.stopPropagation(),void 0===s)return;(0,A.runOrSignInWithPromo)("watchList",{source:"add symbol to watchlist"},(()=>{t(W.WATCHLIST_WIDGET_ID,[s],b),e.id&&T(e.id)})),x(o),E("watchlist add button",o)},removeFromWatchlist:function(e){if(e.stopPropagation(),void 0===s)return;o(W.WATCHLIST_WIDGET_ID,[s]),x(e),E("watchlist remove button",e),b===s&&I()},findInWatchlist:function(e){if(e.stopPropagation(),void 0===s)return;i(W.WATCHLIST_WIDGET_ID,s),g(),E("watchlist goto button")}})});function x(e){e&&(0,w.modifiersFromEvent)(e)===w.Modifiers.Shift?g():n.mobiletouch||S.current?.select()}function E(e,t){let o=e;t&&(0,w.modifiersFromEvent)(t)===w.Modifiers.Shift&&(o+=" shift"),(0,D.trackEvent)("GUI","SS",o)}},SymbolSearchWatchlistDialog:function(e){const{addToWatchlist:t,removeFromWatchlist:o,findInWatchlist:i,...n}=e,{feedItems:a,searchRef:l,searchSpreads:s,selectedIndex:r,onSubmit:u,setSelectedIndex:g,onClose:p,isMobile:f,isTablet:v,mode:b,setMode:I,symbolSearchState:y}=(0,d.ensureNotNull)((0,c.useContext)(m.SymbolSearchItemsDialogContext)),{selectedAction:C,setSelectedAction:D,isSpreadOrMultipleMode:E,addAfter:O,clearTargetSymbol:k,highlight:M}=(0,d.ensureNotNull)((0,c.useContext)(h.SymbolSearchWatchlistContext)),_=a[r],L="exchange"===b;return c.createElement(T.AdaptivePopupDialog,{...n,className:S()(x.dialog,!f&&(v?x.tabletDialog:x.desktopDialog)),dataName:"watchlist-symbol-search-dialog",onKeyDown:function(e){if(e.target&&e.target!==l.current)return;const t=(0,w.hashFromEvent)(e);switch(t){case 13:return E(s,l)?void u(!0):(_?F(!1):u(!1),void l.current?.select());case 13+w.Modifiers.Shift:return E(s,l)?void u(!0):void(_?F(!0):u(!0));case 27:return e.preventDefault(),L?void I("symbolSearch"):void p()}switch((0,N.mapKeyCodeToDirection)(t)){case"blockPrev":if(e.preventDefault(),0===r||"good"!==y)return;if(-1===r)return void g(0);g(r-1);break;case"blockNext":if(e.preventDefault(),r===a.length-1||"good"!==y)return;g(r+1);break;case"inlinePrev":if(!_)return;1===C&&(e.preventDefault(),D(2));break;case"inlineNext":if(!_)return;2===C&&(e.preventDefault(),D(1))}},backdrop:!0,draggable:!1});function F(e){if(!_||void 0===_.fullSymbolName)return;const{fullSymbolName:n}=_;switch(C){case 0:(0,A.runOrSignInWithPromo)("watchList",{source:"add symbol to watchlist"},(()=>{t(W.WATCHLIST_WIDGET_ID,[n],O),_.id&&M(_.id)}));break;case 1:return i(W.WATCHLIST_WIDGET_ID,n),void p();case 2:o(W.WATCHLIST_WIDGET_ID,[n]),O===n&&k()}e&&p()}}}},558323:(e,t,o)=>{"use strict";o.d(t,{showSymbolSearchItemsDialog:()=>r});var i=o(50959),n=o(753327),a=o(63192),l=o(798154),s=o(651674);function r(e){
const{symbolTypeFilter:t,initialMode:o="symbolSearch",autofocus:r=!0,defaultValue:c,showSpreadActions:d,selectSearchOnInit:u,onSearchComplete:h,dialogTitle:m,placeholder:g,fullscreen:S,initialScreen:p,wrapper:f,dialog:v,contentItem:b,onClose:I,onOpen:y,footer:C,symbolTypes:w,searchInput:D,emptyState:W,hideMarkedListFlag:A,dialogWidth:T="auto",manager:N,shouldReturnFocus:x,onSymbolFiltersParamsChange:E,onEmptyResults:O,customSearchSymbols:k,enableOptionsChain:M,searchInitiationPoint:_}=e;if(a.dialogsOpenerManager.isOpened("SymbolSearch")||a.dialogsOpenerManager.isOpened("ChangeIntervalDialog"))return;const L=document.createElement("div"),F=i.createElement(n.SlotContext.Provider,{value:N??null},i.createElement(l.SymbolSearchItemsDialog,{symbolTypeFilter:t,onClose:R,initialMode:o,defaultValue:c,showSpreadActions:d,hideMarkedListFlag:A,selectSearchOnInit:u,onSearchComplete:h,dialogTitle:m,placeholder:g,fullscreen:S,initialScreen:p,wrapper:f,dialog:v,contentItem:b,footer:C,symbolTypes:w,searchInput:D,emptyState:W,autofocus:r,dialogWidth:T,shouldReturnFocus:x,onSymbolFiltersParamsChange:E,onEmptyResults:O,customSearchSymbols:k,enableOptionsChain:M,searchInitiationPoint:_})),P=(0,s.createReactRoot)(F,L);function R(){P.unmount(),a.dialogsOpenerManager.setAsClosed("SymbolSearch"),I&&I()}return a.dialogsOpenerManager.setAsOpened("SymbolSearch"),y&&y(),{close:R}}},113060:(e,t,o)=>{"use strict";o.d(t,{SymbolSearchWatchlistContext:()=>a});var i,n=o(50959);!function(e){e[e.Add=0]="Add",e[e.Find=1]="Find",e[e.Remove=2]="Remove"}(i||(i={}));const a=n.createContext(null)},63192:(e,t,o)=>{"use strict";o.d(t,{DialogsOpenerManager:()=>i,dialogsOpenerManager:()=>n});class i{constructor(){this._storage=new Map}setAsOpened(e,t){this._storage.set(e,t)}setAsClosed(e){this._storage.delete(e)}isOpened(e){return this._storage.has(e)}getDialogPayload(e){return this._storage.get(e)}}const n=new i},190266:(e,t,o)=>{"use strict";o.d(t,{runOrSignIn:()=>i,runOrSignInWithPromo:()=>n});function i(e,t){e()}function n(e,t,o){o()}},840976:(e,t,o)=>{"use strict";o.d(t,{useEnsuredContext:()=>a});var i=o(50959),n=o(650151);function a(e){return(0,n.ensureNotNull)((0,i.useContext)(e))}},102478:(e,t,o)=>{"use strict";o.d(t,{useResizeObserver:()=>i.useResizeObserver});var i=o(664332)},800296:(e,t,o)=>{"use strict";o.d(t,{ListItemButton:()=>r});var i=o(50959),n=o(497754),a=o.n(n),l=o(878112),s=o(8510);function r(e){const{className:t,disabled:o,...n}=e;return i.createElement(l.Icon,{className:a()(s.button,o&&s.disabled,t),...n})}},398120:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M13.9 14.1V22h1.2v-7.9H23v-1.2h-7.9V5h-1.2v7.9H6v1.2h7.9z"/></svg>'},177042:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14 9.5a.5.5 0 0 0 1 0V7.02A6.5 6.5 0 0 1 20.98 13H18.5a.5.5 0 0 0 0 1h2.48A6.5 6.5 0 0 1 15 19.98V17.5a.5.5 0 0 0-1 0v2.48A6.5 6.5 0 0 1 8.02 14h2.48a.5.5 0 0 0 0-1H8.02A6.5 6.5 0 0 1 14 7.02V9.5zm1-3.48V4.5a.5.5 0 0 0-1 0v1.52A7.5 7.5 0 0 0 7.02 13H5.5a.5.5 0 0 0 0 1h1.52A7.5 7.5 0 0 0 14 20.98v1.52a.5.5 0 0 0 1 0v-1.52A7.5 7.5 0 0 0 21.98 14h1.52a.5.5 0 0 0 0-1h-1.52A7.5 7.5 0 0 0 15 6.02z"/></svg>'},925931:(e,t,o)=>{"use strict";o.d(t,{nanoid:()=>i});let i=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);