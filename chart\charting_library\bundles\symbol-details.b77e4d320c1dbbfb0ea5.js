(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6991],{78217:e=>{e.exports={pair:"pair-ocURKVwI",xxceptionallysmalldonotusebrv1023:"xxceptionallysmalldonotusebrv1023-ocURKVwI",xxxxsmall:"xxxxsmall-ocURKVwI",xxxsmall:"xxxsmall-ocURKVwI",xxsmall:"xxsmall-ocURKVwI",xsmall:"xsmall-ocURKVwI",small:"small-ocURKVwI",medium:"medium-ocURKVwI",large:"large-ocURKVwI",xlarge:"xlarge-ocURKVwI",xxlarge:"xxlarge-ocURKVwI",xxxlarge:"xxxlarge-ocURKVwI",logo:"logo-ocURKVwI",skeleton:"skeleton-ocURKVwI",empty:"empty-ocURKVwI"}},456057:e=>{e.exports={logo:"logo-PsAlMQQF",hidden:"hidden-PsAlMQQF",xxceptionallysmalldonotusebrv1023:"xxceptionallysmalldonotusebrv1023-PsAlMQQF",xxxsmall:"xxxsmall-PsAlMQQF",xxsmall:"xxsmall-PsAlMQQF",xsmall:"xsmall-PsAlMQQF",small:"small-PsAlMQQF",medium:"medium-PsAlMQQF",large:"large-PsAlMQQF",xlarge:"xlarge-PsAlMQQF",xxlarge:"xxlarge-PsAlMQQF",xxxlarge:"xxxlarge-PsAlMQQF",skeleton:"skeleton-PsAlMQQF",letter:"letter-PsAlMQQF"}},55679:e=>{e.exports={wrapper:"wrapper-TJ9ObuLF",animated:"animated-TJ9ObuLF",pulsation:"pulsation-TJ9ObuLF"}},489928:e=>{e.exports={}},979566:e=>{e.exports={container:"container-M1mz4quA",pairContainer:"pairContainer-M1mz4quA",logo:"logo-M1mz4quA",hidden:"hidden-M1mz4quA"}},772041:e=>{e.exports={label:"label-ou2KkVr5",text:"text-ou2KkVr5",icon:"icon-ou2KkVr5"}},518561:e=>{e.exports={scrollWrap:"scrollWrap-FaOvTD2r"}},131987:e=>{e.exports={container:"container-Tv7LSjUz",overlayScrollWrap:"overlayScrollWrap-Tv7LSjUz",wrapper:"wrapper-Tv7LSjUz"}},151810:e=>{e.exports={wrap:"wrap-vSb6C0Bj","wrap--horizontal":"wrap--horizontal-vSb6C0Bj",bar:"bar-vSb6C0Bj",barInner:"barInner-vSb6C0Bj","barInner--horizontal":"barInner--horizontal-vSb6C0Bj","bar--horizontal":"bar--horizontal-vSb6C0Bj"}},319585:e=>{e.exports={change:"change-SNvPvlJ3",wrap:"wrap-SNvPvlJ3"}},807889:e=>{e.exports={highlight:"highlight-maJ2WnzA",growing:"growing-maJ2WnzA",falling:"falling-maJ2WnzA"}},366215:e=>{e.exports={container:"container-qWcO4bp9",price:"price-qWcO4bp9",info:"info-qWcO4bp9",currency:"currency-qWcO4bp9",priceWrapper:"priceWrapper-qWcO4bp9",changeWrap:"changeWrap-qWcO4bp9"}},394869:e=>{e.exports={logo:"logo-d0vVmGvT"}},62269:e=>{e.exports={additional:"additional-lu2ARROZ",invalid:"invalid-lu2ARROZ",dotWrap:"dotWrap-lu2ARROZ",smallMargin:"smallMargin-lu2ARROZ",dot:"dot-lu2ARROZ",main:"main-lu2ARROZ",link:"link-lu2ARROZ"}},612217:e=>{e.exports={container:"container-bxBC50sK",bid:"bid-bxBC50sK",ask:"ask-bxBC50sK"}},922845:e=>{e.exports={container:"container-ZJX9Rmzv",title:"title-ZJX9Rmzv",groupWrap:"groupWrap-ZJX9Rmzv",hasDisabledSet:"hasDisabledSet-ZJX9Rmzv"}},601474:e=>{e.exports={statusWrapper:"statusWrapper-d1N3lNBX",lastTime:"lastTime-d1N3lNBX",lastPrice:"lastPrice-d1N3lNBX",plus:"plus-d1N3lNBX",change:"change-d1N3lNBX",changePercent:"changePercent-d1N3lNBX",minus:"minus-d1N3lNBX",price:"price-d1N3lNBX",changeWrap:"changeWrap-d1N3lNBX",extra:"extra-d1N3lNBX",icon:"icon-d1N3lNBX",neutral:"neutral-d1N3lNBX",blue:"blue-d1N3lNBX",yellow:"yellow-d1N3lNBX",green:"green-d1N3lNBX",
currency:"currency-d1N3lNBX",status:"status-d1N3lNBX"}},65812:e=>{e.exports={container:"container-cff_aFgr",header:"header-cff_aFgr",title:"title-cff_aFgr",price:"price-cff_aFgr",range:"range-cff_aFgr",bar:"bar-cff_aFgr",low:"low-cff_aFgr",roundedLeft:"roundedLeft-cff_aFgr",roundedRight:"roundedRight-cff_aFgr",arrowContainer:"arrowContainer-cff_aFgr",arrow:"arrow-cff_aFgr",extraMargin:"extraMargin-cff_aFgr"}},251387:e=>{e.exports={container:"container-BSF4XTsE",opacity:"opacity-BSF4XTsE",widgetWrapper:"widgetWrapper-BSF4XTsE",large:"large-BSF4XTsE",offsetDisabled:"offsetDisabled-BSF4XTsE",separator:"separator-BSF4XTsE",hintPlaceholder:"hintPlaceholder-BSF4XTsE",hint:"hint-BSF4XTsE",previewButtonsWrap:"previewButtonsWrap-BSF4XTsE",previewButton:"previewButton-BSF4XTsE",userSelectText:"userSelectText-BSF4XTsE",moreAboutFundButton:"moreAboutFundButton-BSF4XTsE",highlight:"highlight-BSF4XTsE",growing:"growing-BSF4XTsE",falling:"falling-BSF4XTsE"}},108937:(e,t,l)=>{"use strict";l.d(t,{getBlockStyleClasses:()=>r,getLogoStyleClasses:()=>s});var n=l(497754),i=l(92318),o=l(78217),a=l.n(o);function r(e,t){return n(a().pair,a()[e],t)}function s(e,t=2,l=!0){return n(a().logo,a()[e],a().skeleton,i.skeletonTheme.wrapper,!l&&a().empty,1===t&&n(i.skeletonTheme.animated))}},185934:(e,t,l)=>{"use strict";l.d(t,{getStyleClasses:()=>r,isCircleLogoWithUrlProps:()=>s});var n=l(497754),i=l(92318),o=l(456057),a=l.n(o);function r(e,t=2,l){return n(a().logo,a()[e],l,0===t||1===t?n(i.skeletonTheme.wrapper,a().skeleton):a().letter,1===t&&i.skeletonTheme.animated)}function s(e){return"logoUrl"in e&&null!==e.logoUrl&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},185727:(e,t,l)=>{"use strict";l.d(t,{usePrevious:()=>i});var n=l(50959);function i(e){const t=(0,n.useRef)(null);return(0,n.useEffect)((()=>{t.current=e}),[e]),t.current}},92318:(e,t,l)=>{"use strict";l.d(t,{skeletonTheme:()=>i});var n=l(55679);const i=n},979377:(e,t,l)=>{"use strict";l.d(t,{formatTime:()=>v,isValidTimeOptionsDateStyle:()=>c,isValidTimeOptionsRange:()=>d});const n={calendar:"gregory",numberingSystem:"latn",hour12:!1},i={year:"numeric",month:"short",day:"numeric"},o={year:"numeric",month:"2-digit",day:"2-digit"},a={hour:"2-digit",minute:"2-digit",second:"2-digit"},r={timeZoneName:"shortOffset",weekday:"short"},s={year:0,month:1,day:2,hour:3,minute:4,second:5};const u=["year","month","day","hour","minute","second"];function d(e){return u.includes(e)}function c(e){return"numeric"===e||"short"===e}function v(e,t,l="year",u="day",d){const c=function(e="year",t="day",l={}){[e,t]=s[t]>s[e]?[e,t]:[t,e];const u={..."numeric"===l.dateStyle?o:i,...a},d=l.fractionalSecondDigits,c={...n,fractionalSecondDigits:void 0===d?void 0:Math.floor(Math.min(Math.max(1,d),3)),timeZone:l.timeZone,weekday:l.weekday?r.weekday:void 0,timeZoneName:l.timeZoneName?r.timeZoneName:void 0};return Object.keys(u).forEach((l=>{s[l]>=s[e]&&s[l]<=s[t]&&(c[l]=u[l])})),c}(l,u,d),v=new Intl.DateTimeFormat(t,c),m=new Date(e);return v.format(m)}},609970:(e,t,l)=>{"use strict";l.d(t,{AbstractIndicator:()=>r})
;var n=l(671945),i=l(924910),o=l(334529);l(744471);const a=(0,n.getLogger)("GUI.Blocks.AbstractIndicator");class r{constructor(e){this._classSuffix="",this._quoteSessionPrefix="abstract-indicator",this._shortMode=!1,this._showTooltip=!0,this._subscribed=!1,this._tooltipType="custom",this._lastTooltipText="",this._quoteSession=e.quoteSession}getValue(){return this._value}getTooltipText(){return this._labelMap[this._value]||""}getLabel(){return this._labelMap[this._value]||""}getElement(){return this._el}update(e,t){this._updateValue(e,t),this._render()}setTooltipEnabled(e=!1){this._showTooltip!==e&&(this._showTooltip=e,this._renderTooltip())}enableShortMode(){!0!==this._shortMode&&(this._shortMode=!0,this._render())}disableShortMode(){!1!==this._shortMode&&(this._shortMode=!1,this._render())}isShortModeEnabled(){return this._shortMode}start(){!this._subscribed&&this._symbolName&&(this._quoteSession||(this._quoteSession=(0,o.getQuoteSessionInstance)("simple")),this._quoteSession.subscribe(this._getQuoteSessionId(),this._symbolName,this.update.bind(this)),this._subscribed=!0)}stop(){this._subscribed&&this._quoteSession&&this._symbolName&&(this._quoteSession.unsubscribe(this._getQuoteSessionId(),this._symbolName),this._subscribed=!1)}setSessionStatusIcon(e){this._sessionStatusIcon!==e&&(this._sessionStatusIcon=e,this._render())}_init(e){this._el=e.el?e.el:document.createElement("span"),this._el.innerHTML="",this._classMap=e.classMap,this._iconClassMap=e.iconClassMap,this._labelMap=e.labelMap,this._showTooltip=e.showTooltip,this._classSuffix=e.classSuffix,this._symbolName=e.symbol,this._sessionStatusIcon=e.sessionStatusIcon,this._onValueChange=e.onValueChange,e.tooltipType&&(this._tooltipType=e.tooltipType),this._quoteSessionGUID=(0,i.guid)(),!0===e.short&&this.enableShortMode(),e.data&&this._updateValue(e.data)}_clearClasses(){Object.values(this._classMap).map((e=>{this._el.classList.remove(`${e}`),this._el.classList.remove(`${e}${this._classSuffix}`)}))}_render(){this._renderClasses(),this._renderTooltip(),this._renderLabel()}_renderLabel(){this._el.textContent=this.getLabel()}_updateValue(e,t){const l=this._getValueFromData(e);(t||l!==this._value)&&(this._value=l,this._onValueChange?.(this._value))}_renderClasses(){const e=this._el.classList;e.add(this._componentClass,this._componentClass+this._classSuffix);const t=this._classMap[this._value];for(const l in this._classMap){const n=this._classMap[l];n&&(n===t?(e.add(n,n+this._classSuffix),this._sessionStatusIcon&&e.add(n+"__withIcon")):(e.remove(n,n+this._classSuffix),this._sessionStatusIcon&&e.remove(n+"__withIcon")))}!t&&this._value&&a.logWarn(`no className for status ${this._value}`)}_renderTooltip(){const e=this._showTooltip?this.getTooltipText():"";e!==this._lastTooltipText&&(this._lastTooltipText=e,this._el.setAttribute("title",e),"custom"===this._tooltipType&&this._el.classList.toggle("apply-common-tooltip",this._showTooltip))}_getQuoteSessionId(){return`${this._quoteSessionPrefix}.${this._quoteSessionGUID}`}}},678100:(e,t,l)=>{"use strict";l.d(t,{
DataModeIndicator:()=>u});var n,i=l(609838),o=(l(68212),l(489928),l(609970));!function(e){e.Delayed="delayed",e.DelayedStreaming="delayed_streaming",e.EndOfDay="endofday",e.Snapshot="snapshot",e.Realtime="realtime",e.Connecting="connecting",e.Loading="loading",e.Invalid="invalid",e.Forbidden="forbidden",e.Streaming="streaming"}(n||(n={}));const a={connecting:"tv-data-mode--connecting",delayed:"tv-data-mode--delayed",delayed_streaming:"tv-data-mode--delayed",endofday:"tv-data-mode--endofday",forbidden:"tv-data-mode--forbidden",realtime:"tv-data-mode--realtime",snapshot:"tv-data-mode--snapshot",loading:"tv-data-mode--loading",replay:"tv-data-mode--replay"};function r(){return{connecting:i.t(null,{context:"data_mode_connecting_letter"},l(167040)),delayed:i.t(null,{context:"data_mode_delayed_letter"},l(800919)),delayed_streaming:i.t(null,{context:"data_mode_delayed_streaming_letter"},l(933088)),endofday:i.t(null,{context:"data_mode_end_of_day_letter"},l(918400)),forbidden:i.t(null,{context:"data_mode_forbidden_letter"},l(514149)),realtime:i.t(null,{context:"data_mode_realtime_letter"},l(650940)),snapshot:i.t(null,{context:"data_mode_snapshot_letter"},l(756757)),loading:"",replay:i.t(null,{context:"data_mode_replay_letter"},l(745540))}}const s={streaming:"realtime"};class u extends o.AbstractIndicator{constructor(e){super(e),this._quoteSessionPrefix="data-mode-indicator",this._componentClass="tv-data-mode",this._init(e)}getLabel(){return!0===this._shortMode?this._shortLabelMap[this._value]||"":super.getLabel()}setMode(e,t){this.update({values:{update_mode:e,update_mode_seconds:t}})}hide(){this._el.classList.add("i-hidden")}show(){this._el.classList.remove("i-hidden")}getTooltipText(){let e="";const t=this.getValue();if(""===t)return e;switch(t){case"delayed":e=i.t(null,void 0,l(369539));break;case"delayed_streaming":e=i.t(null,void 0,l(167476));break;default:e=this._labelMap[t]||e}return["delayed","delayed_streaming"].includes(t)&&(e=e.format({number:String(Math.round(this._modeInterval/60))})),e}_init(e={}){const t=Object.assign({},{classMap:a,classSuffix:"",data:{values:{update_mode:"connecting"}},labelMap:{connecting:i.t(null,void 0,l(366891)),delayed:i.t(null,void 0,l(739688)),delayed_streaming:i.t(null,void 0,l(739688)),endofday:i.t(null,void 0,l(328304)),forbidden:i.t(null,void 0,l(909161)),realtime:i.t(null,void 0,l(303058)),snapshot:i.t(null,void 0,l(988408)),loading:"",replay:i.t(null,void 0,l(38822))},modeInterval:600,short:!1,shortLabelMap:r(),showTooltip:!0,tooltipType:"custom"},e);this._modeInterval=t.modeInterval||600,this._shortLabelMap=t.shortLabelMap||r(),super._init(t),this._render()}_getValueFromData(e){let t;return t=void 0!==e.values&&void 0!==e.values.update_mode?e.values.update_mode:this.getValue(),s[t]||t}_updateValue(e,t){void 0!==e.values&&void 0!==e.values.update_mode_seconds&&(this._modeInterval=e.values.update_mode_seconds),super._updateValue(e,t)}}},868333:(e,t,l)=>{"use strict";var n;l.d(t,{LogoSize:()=>n,getLogoUrlResolver:()=>a}),function(e){e[e.Medium=0]="Medium",e[e.Large=1]="Large"
}(n||(n={}));class i{getSymbolLogoUrl(e){return e}getCountryFlagUrl(){return""}getCryptoLogoUrl(e){return e}getProviderLogoUrl(e){return e}getSourceLogoUrl(e){return e}getBlockchainContractLogoUrl(e){return e}}let o;function a(){return o||(o=new i),o}},887859:(e,t,l)=>{"use strict";l.d(t,{getLocaleIso:()=>a});var n=l(650151)
;const i=JSON.parse('{"ar_AE":{"language":"ar","language_name":"العربية","flag":"sa","geoip_code":"sa","countries_with_this_language":["ae","bh","dj","dz","eg","er","iq","jo","km","kw","lb","ly","ma","mr","om","qa","sa","sd","so","sy","td","tn","ye"],"priority":500,"dir":"rtl","iso":"ar","iso_639_3":"arb","show_on_widgets":true,"global_name":"Arabic"},"br":{"language":"pt","language_name":"Português","flag":"br","geoip_code":"br","priority":650,"iso":"pt","iso_639_3":"por","show_on_widgets":true,"global_name":"Portuguese"},"ca_ES":{"language":"ca_ES","language_name":"Català","flag":"es","geoip_code":"es","priority":745,"iso":"ca","iso_639_3":"cat","disabled":true,"show_on_widgets":true,"global_name":"Catalan"},"cs":{"language":"cs","language_name":"Czech","flag":"cz","geoip_code":"cz","priority":718,"iso":"cs","iso_639_3":"ces","show_on_widgets":true,"global_name":"Czech","is_in_european_union":true,"isBattle":true},"de_DE":{"language":"de","language_name":"Deutsch","flag":"de","geoip_code":"de","countries_with_this_language":["at","ch"],"priority":756,"iso":"de","iso_639_3":"deu","show_on_widgets":true,"global_name":"German","is_in_european_union":true},"en":{"language":"en","language_name":"English","flag":"us","geoip_code":"us","priority":1000,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"English","is_only_recommended_tw_autorepost":true},"es":{"language":"es","language_name":"Español","flag":"es","geoip_code":"es","countries_with_this_language":["mx","ar","ve","cl","co","pe","uy","py","cr","gt","c","bo","pa","pr"],"priority":744,"iso":"es","iso_639_3":"spa","show_on_widgets":true,"global_name":"Spanish","is_in_european_union":true},"fr":{"language":"fr","language_name":"Français","flag":"fr","geoip_code":"fr","priority":750,"iso":"fr","iso_639_3":"fra","show_on_widgets":true,"global_name":"French","is_in_european_union":true},"he_IL":{"language":"he_IL","language_name":"עברית","flag":"il","geoip_code":"il","priority":490,"dir":"rtl","iso":"he","iso_639_3":"heb","show_on_widgets":true,"global_name":"Israeli"},"hu_HU":{"language":"hu_HU","language_name":"Magyar","flag":"hu","geoip_code":"hu","priority":724,"iso":"hu","iso_639_3":"hun","show_on_widgets":true,"global_name":"Hungarian","is_in_european_union":true,"disabled":true},"id":{"language":"id_ID","language_name":"Bahasa Indonesia","flag":"id","geoip_code":"id","priority":648,"iso":"id","iso_639_3":"ind","show_on_widgets":true,"global_name":"Indonesian"},"in":{"language":"en","language_name":"English ‎(India)‎","flag":"in","geoip_code":"in","priority":800,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"Indian"},"it":{"language":"it","language_name":"Italiano","flag":"it","geoip_code":"it","priority":737,"iso":"it","iso_639_3":"ita","show_on_widgets":true,"global_name":"Italian","is_in_european_union":true},"ja":{"language":"ja","language_name":"日本語","flag":"jp","geoip_code":"jp","priority":600,"iso":"ja","iso_639_3":"jpn","show_on_widgets":true,"global_name":"Japanese"},"kr":{"language":"ko","language_name":"한국어","flag":"kr","geoip_code":"kr","priority":550,"iso":"ko","iso_639_3":"kor","show_on_widgets":true,"global_name":"Korean"},"ms_MY":{"language":"ms_MY","language_name":"Bahasa Melayu","flag":"my","geoip_code":"my","priority":647,"iso":"ms","iso_639_3":"zlm","show_on_widgets":true,"global_name":"Malaysian"},"pl":{"language":"pl","language_name":"Polski","flag":"pl","geoip_code":"pl","priority":725,"iso":"pl","iso_639_3":"pol","show_on_widgets":true,"global_name":"Polish","is_in_european_union":true},"ru":{"language":"ru","language_name":"Русский","flag":"ru","geoip_code":"ru","countries_with_this_language":["am","by","kg","kz","md","tj","tm","uz"],"priority":700,"iso":"ru","iso_639_3":"rus","show_on_widgets":true,"global_name":"Russian","is_only_recommended_tw_autorepost":true},"sv_SE":{"language":"sv","language_name":"Svenska","flag":"se","geoip_code":"se","priority":723,"iso":"sv","iso_639_3":"swe","show_on_widgets":true,"global_name":"Swedish","is_in_european_union":true,"disabled":true},"th_TH":{"language":"th","language_name":"ภาษาไทย","flag":"th","geoip_code":"th","priority":646,"iso":"th","iso_639_3":"tha","show_on_widgets":true,"global_name":"Thai"},"tr":{"language":"tr","language_name":"Türkçe","flag":"tr","geoip_code":"tr","priority":713,"iso":"tr","iso_639_3":"tur","show_on_widgets":true,"global_name":"Turkish","is_only_recommended_tw_autorepost":true},"vi_VN":{"language":"vi","language_name":"Tiếng Việt","flag":"vn","geoip_code":"vn","priority":645,"iso":"vi","iso_639_3":"vie","show_on_widgets":true,"global_name":"Vietnamese"},"zh_CN":{"language":"zh","language_name":"简体中文","flag":"cn","geoip_code":"cn","countries_with_this_language":["zh"],"priority":537,"iso":"zh-Hans","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Chinese"},"zh_TW":{"language":"zh_TW","language_name":"繁體中文","flag":"tw","geoip_code":"tw","countries_with_this_language":["hk"],"priority":536,"iso":"zh-Hant","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Taiwanese"},"el":{"language":"el","language_name":"Greek","flag":"gr","geoip_code":"gr","priority":625,"iso":"el","iso_639_3":"ell","global_name":"Greek","is_in_european_union":true,"isBattle":true},"nl_NL":{"language":"nl_NL","language_name":"Dutch","flag":"nl","geoip_code":"nl","priority":731,"iso":"nl","iso_639_3":"nld","global_name":"Dutch","is_in_european_union":true,"isBattle":true},"ro":{"language":"ro","language_name":"Romanian","flag":"ro","geoip_code":"ro","priority":707,"iso":"ro","iso_639_3":"ron","global_name":"Romanian","is_in_european_union":true,"isBattle":true}}'),o=function(){
const e=document.querySelectorAll("link[rel~=link-locale][data-locale]");if(0===e.length)return i;const t={};return e.forEach((e=>{const l=(0,n.ensureNotNull)(e.getAttribute("data-locale"));t[l]={...i[l],href:e.href}})),t}();function a(e){return e=e||window.locale,o[e]?.iso}},789749:(e,t,l)=>{"use strict";l.d(t,{CachedLogo:()=>v});var n=l(50959),i=l(497754),o=l.n(i),a=l(361701),r=l(439067),s=l(41192),u=l(35089),d=l(855393),c=l(979566);function v(e){const{className:t,placeholderLetter:l,url1:i,url2:v,size:p="xxxsmall"}=e,h=(0,n.useRef)(null),f=(0,n.useRef)(null),S=(0,n.useRef)(null),y=(0,n.useRef)(null),_=(0,n.useRef)(null),P=(0,n.useRef)(null);return(0,d.useIsomorphicLayoutEffect)((()=>{const e=void 0===i?[]:void 0===v?[i]:[i,v],t=P.current=(l=e,Promise.all(l.map((e=>(0,u.getImage)(`symbol_logo_${e}`,e,g).then((e=>e.cloneNode()))))));var l;t.catch((()=>[])).then((e=>{if(t===P.current)switch(e.length){case 0:S.current?.classList.add(c.hidden),f.current?.classList.add(a.hiddenCircleLogoClass),h.current?.classList.remove(a.hiddenCircleLogoClass);break;case 1:m(f.current,e[0]),S.current?.classList.add(c.hidden),f.current?.classList.remove(a.hiddenCircleLogoClass),h.current?.classList.add(a.hiddenCircleLogoClass);break;case 2:m(y.current,e[0]),m(_.current,e[1]),S.current?.classList.remove(c.hidden),f.current?.classList.add(a.hiddenCircleLogoClass),h.current?.classList.add(a.hiddenCircleLogoClass)}}))}),[i,v]),n.createElement("span",{className:o()(t,c.container)},n.createElement("span",{ref:S,className:o()(c.pairContainer,c.hidden)},n.createElement("span",{className:(0,s.getBlockStyleClasses)(p)},n.createElement("span",{ref:_,className:o()(c.logo,(0,s.getLogoStyleClasses)(p))}),n.createElement("span",{ref:y,className:o()(c.logo,(0,s.getLogoStyleClasses)(p))}))),n.createElement("span",{ref:f,className:o()(c.logo,a.hiddenCircleLogoClass,(0,r.getStyleClasses)(p))}),n.createElement("span",{ref:h,className:o()(c.logo,(0,r.getStyleClasses)(p))},n.createElement(a.CircleLogo,{size:p,placeholderLetter:l})))}function m(e,t){e&&(e.innerHTML="",e.appendChild(t))}function g(e){e.crossOrigin="",e.decoding="async"}},975317:(e,t,l)=>{"use strict";l.d(t,{DelistedLabel:()=>d});var n=l(50959),i=l(609838),o=l(497754),a=l.n(o),r=l(878112),s=l(492315),u=l(772041);function d(e){const{className:t}=e;return n.createElement("span",{className:a()(t,u.label)},n.createElement(r.Icon,{className:u.icon,icon:s}),n.createElement("span",{className:u.text},i.t(null,void 0,l(254602))))}},73288:(e,t,l)=>{"use strict";l.d(t,{OverlayScrollContainer:()=>f});var n=l(50959),i=l(497754),o=l.n(i),a=l(431520),r=l(650151),s=l(822960);const u=l(151810);var d;!function(e){e[e.Vertical=0]="Vertical",e[e.Horizontal=1]="Horizontal",e[e.HorizontalRtl=2]="HorizontalRtl"}(d||(d={}));const c={0:{isHorizontal:!1,isNegative:!1,sizePropName:"height",minSizePropName:"minHeight",startPointPropName:"top",currentMousePointPropName:"clientY",progressBarTransform:"translateY"},1:{isHorizontal:!0,isNegative:!1,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"left",
currentMousePointPropName:"clientX",progressBarTransform:"translateX"},2:{isHorizontal:!0,isNegative:!0,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"right",currentMousePointPropName:"clientX",progressBarTransform:"translateX"}},v=40;function m(e){const{size:t,scrollSize:l,clientSize:i,scrollProgress:a,onScrollProgressChange:d,scrollMode:m,theme:g=u,onDragStart:p,onDragEnd:h,minBarSize:f=v}=e,S=(0,n.useRef)(null),y=(0,n.useRef)(null),[_,P]=(0,n.useState)(!1),b=(0,n.useRef)(0),{isHorizontal:w,isNegative:C,sizePropName:M,minSizePropName:E,startPointPropName:N,currentMousePointPropName:T,progressBarTransform:F}=c[m];(0,n.useEffect)((()=>{const e=(0,r.ensureNotNull)(S.current).ownerDocument;return _?(p&&p(),e&&(e.addEventListener("mousemove",W),e.addEventListener("mouseup",H))):h&&h(),()=>{e&&(e.removeEventListener("mousemove",W),e.removeEventListener("mouseup",H))}}),[_]);const I=t/l||0,R=i*I||0,k=Math.max(R,f),x=(t-k)/(t-R),A=l-t,B=C?-A:0,L=C?0:A,D=z((0,s.clamp)(a,B,L))||0;return n.createElement("div",{ref:S,className:o()(g.wrap,w&&g["wrap--horizontal"]),style:{[M]:t},onMouseDown:function(e){if(e.isDefaultPrevented())return;e.preventDefault();const t=O(e.nativeEvent,(0,r.ensureNotNull)(S.current)),l=Math.sign(t),n=(0,r.ensureNotNull)(y.current).getBoundingClientRect();b.current=l*n[M]/2;let i=Math.abs(t)-Math.abs(b.current);const o=z(A);i<0?(i=0,b.current=t):i>o&&(i=o,b.current=t-l*o);d(V(l*i)),P(!0)}},n.createElement("div",{ref:y,className:o()(g.bar,w&&g["bar--horizontal"]),style:{[E]:f,[M]:k,transform:`${F}(${D}px)`},onMouseDown:function(e){e.preventDefault(),b.current=O(e.nativeEvent,(0,r.ensureNotNull)(y.current)),P(!0)}},n.createElement("div",{className:o()(g.barInner,w&&g["barInner--horizontal"])})));function W(e){const t=O(e,(0,r.ensureNotNull)(S.current))-b.current;d(V(t))}function H(){P(!1)}function O(e,t){const l=t.getBoundingClientRect()[N];return e[T]-l}function z(e){return e*I*x}function V(e){return e/I/x}}var g=l(522224),p=l(518561);const h=8;function f(e){const{reference:t,className:l,containerHeight:o=0,containerWidth:r=0,contentHeight:s=0,contentWidth:u=0,scrollPosTop:d=0,scrollPosLeft:c=0,onVerticalChange:v,onHorizontalChange:f,visible:S}=e,[y,_]=(0,g.useHover)(),[P,b]=(0,n.useState)(!1),w=o<s,C=r<u,M=w&&C?h:0;return n.createElement("div",{..._,ref:t,className:i(l,p.scrollWrap),style:{visibility:S||y||P?"visible":"hidden"}},w&&n.createElement(m,{size:o-M,scrollSize:s-M,clientSize:o-M,scrollProgress:d,onScrollProgressChange:function(e){v&&v(e)},onDragStart:E,onDragEnd:N,scrollMode:0}),C&&n.createElement(m,{size:r-M,scrollSize:u-M,clientSize:r-M,scrollProgress:c,onScrollProgressChange:function(e){f&&f(e)},onDragStart:E,onDragEnd:N,scrollMode:(0,a.isRtl)()?2:1}));function E(){b(!0)}function N(){b(!1)}}},109955:(e,t,l)=>{"use strict";l.d(t,{OverlayScrollWrapper:()=>u});var n=l(50959),i=l(497754),o=l.n(i),a=l(73288),r=l(139043),s=l(131987);function u(e){
const{children:t,className:l,wrapperClassName:i,reference:u,hasCustomTouchScrollAnimation:d,scrollContainerRef:c,isForceVisible:v,tabIndex:m,...g}=e,[p,h,f,S]=(0,r.useOverlayScroll)(c,d);return(0,n.useImperativeHandle)(u,(()=>({updateScrollState:S}))),n.createElement("div",{...g,...h,className:o()(s.container,l)},n.createElement(a.OverlayScrollContainer,{...p,visible:v??p.visible,className:s.overlayScrollWrap}),n.createElement("div",{className:o()(s.wrapper,i),tabIndex:m,ref:f,onScroll:S},t))}},6354:(e,t,l)=>{"use strict";l.d(t,{SymbolLogo:()=>d});var n=l(50959),i=l(497754),o=l.n(i),a=l(306858),r=l(868333),s=l(789749),u=l(394869);function d(e){const{logoId:t,baseCurrencyLogoId:l,currencyLogoId:i,placeholder:d,className:c,size:v="xsmall"}=e,m=(0,n.useMemo)((()=>{const e={logoid:t,"currency-logoid":i,"base-currency-logoid":l};return(0,a.removeUsdFromCryptoPairLogos)((0,a.resolveLogoUrls)(e,r.LogoSize.Medium))}),[t,i,l]);return n.createElement(s.CachedLogo,{key:v,className:o()(u.logo,c),url1:m[0],url2:m[1],placeholderLetter:d,size:v})}},281956:(e,t,l)=>{"use strict";l.d(t,{DESCRIPTION_WITH_INDUSTRY_MARKET_LIST:()=>n});const n=new Set(["stock","fund","dr","right","warrant","structured"])},533615:(e,t,l)=>{"use strict";l.r(t),l.d(t,{DetailsWrapper:()=>$e});var n=l(50959),i=l(632227),o=l(497754),a=l.n(o),r=l(626800),s=l(185727);const u=500;function d(e,t,l){const i=(0,n.useRef)(null),[o,a]=(0,n.useState)(!1),r=(0,s.usePrevious)(e);return(0,n.useEffect)((()=>{e&&void 0===l&&t&&null!==i.current&&(clearTimeout(i.current),i.current=null,a(!0))}),[t]),(0,n.useEffect)((()=>{if(e&&void 0===l)return e!==r&&(a(!1),i.current=setTimeout((()=>a(!0)),u)),()=>{null!==i.current&&(clearTimeout(i.current),i.current=null)}}),[e]),!!e&&(void 0!==l?l:e===r&&(Boolean(t)||o))}var c=l(440891),v=l(6354),m=l(840976),g=l(650151);const p=(0,n.createContext)(null);var h=l(922845);function f(e){const{complete:t,logoId:l,symbolName:o,baseCurrencyLogoId:a,currencyLogoId:s,disabledSet:u,onDisabledSetChange:f,typespecs:S,pricescale:y,listedExchange:_,symbolType:P,hasFinancials:b,hasTechnicals:w,shortName:C,optionsInfo:M}=e,{header:E}=(0,m.useEnsuredContext)(p),N=d(o,Boolean(t)),T=c.enabled("show_symbol_logos"),F=C&&c.enabled("prefer_quote_short_name");return i.createPortal(n.createElement("div",{className:h.container},T&&!c.enabled("widget")&&n.createElement(v.SymbolLogo,{logoId:l,currencyLogoId:s,baseCurrencyLogoId:a,placeholder:N?(0,r.safeShortName)(o)[0]:void 0}),n.createElement("span",{className:h.title},F?C:(0,r.safeShortName)(o)),!1),(0,g.ensureNotNull)(E))}var S=l(609838),y=l(409174),_=l(150335),P=l(563223);const b=l(807889);function w(e){const{value:t,formatter:l,className:i,theme:o=b}=e,r=(0,n.useRef)(null),u=(0,s.usePrevious)(t),d=function(e){const t=(0,s.usePrevious)(e),l=(0,n.useRef)(null);return(0,n.useEffect)((()=>{t!==e&&(l.current=t)}),[e]),l.current}(t),c=(0,n.useCallback)((()=>{null!==r.current&&(clearTimeout(r.current),r.current=null)}),[]),[v,m]=(0,n.useState)(i),[g,p]=(0,n.useMemo)((()=>{if(void 0===t||!l)return["",""]
;if(null==d)return[l.format(t),""];const e=l.format(d),n=l.format(t),i=Math.min(e.length,n.length);let o=0;for(;o<i&&e.charAt(o)===n.charAt(o);)o++;return[n.slice(0,o),n.slice(o)]}),[t,d,l]);(0,n.useEffect)((()=>c),[]),(0,n.useEffect)((()=>{if(void 0===t||null==u)return void m(i);u!==t&&(c(),r.current=setTimeout((()=>m(i)),500),m(a()(i,t>u&&o.growing,t<u&&o.falling)))}),[t,u,i]);const h=d||u,f=Boolean(l&&l.hasForexAdditionalPrecision());return n.createElement("span",{className:a()(o.highlight,v),translate:"no"},!p.length&&f?n.createElement(C,{value:g}):g,!!p.length&&n.createElement("span",{className:a()(o.highlight,t&&h&&t>h&&o.growing,t&&h&&t<h&&o.falling)},f?n.createElement(C,{value:p}):p))}function C(e){const{value:t}=e;return n.createElement(n.Fragment,null,t.slice(0,t.length-1),n.createElement("sup",null,t.slice(t.length-1)))}function M(e,t=b){return e?a()(t.highlight,e>0?t.growing:t.falling):""}var E=l(678100);const N=l(319585);function T(e){const{change:t,formattedChange:l,formattedChangePercent:i,changePercent:o,lastPriceTheme:r,showBrackets:s,theme:u=N}=e;return n.createElement("span",{translate:"no",className:u.wrap},(0,_.isNumber)(t)&&n.createElement("span",{className:a()(u.change,M(t,r))},l),(0,_.isNumber)(o)&&n.createElement("span",{className:a()(u.change,M(o,r))},s?`(${i})`:i))}var F=l(276735),I=l(493173);const R=l(366215),k=new P.NumericFormatter;function x(e){const{className:t,symbol:l,updateMode:i,updateModeSeconds:o,currency:r,lastPrice:s,change:u,changeSignPositive:d,changePercent:c,priceFormatter:v,lastPriceTheme:m,theme:g=R,classSuffix:p,type:h,fractional:f,changeTheme:S}=e,y=(0,n.useRef)(null),P=(0,n.useRef)(null),b=(0,n.useMemo)((()=>{if((0,_.isNumber)(s)&&(0,_.isNumber)(u))return"spread"!==h||f?v?.formatChange(s,s-u,{signPositive:d}):k.format(u)}),[h,v,f,s,u,d]),C=(0,n.useMemo)((()=>new F.PercentageFormatter),[]),M=(0,n.useMemo)((()=>{if((0,_.isNumber)(c))return C.format(c,{signPositive:d})}),[C,d,c]);return(0,n.useEffect)((()=>(null!==y.current&&(P.current=new E.DataModeIndicator({classSuffix:p,el:y.current,short:!0,symbol:l,manualUpdate:!0})),()=>{P.current=null})),[]),(0,n.useEffect)((()=>{const e=P.current;null!==e&&(void 0===i||"economic"===h&&"endofday"===i?e.hide():(e.show(),e.update({values:{update_mode:i,update_mode_seconds:o}})))}),[i,o]),n.createElement("div",{className:a()(g.container,t)},n.createElement("span",{className:g.priceWrapper},n.createElement(w,{key:l,className:g.price,value:s,formatter:v,theme:m}),n.createElement("span",{className:g.info},n.createElement("span",{className:g.mode,ref:y}),n.createElement("span",{className:g.currency},r))),n.createElement(T,{change:u,formattedChange:b,formattedChangePercent:M,changePercent:c,lastPriceTheme:m,theme:(0,I.mergeThemes)(S??N,{wrap:g.changeWrap})}))}var A=l(878112),B=l(975317),L=l(44258),D=l(979377);const W=864e5;function H(e,t,l){const n=O(e),i=O(new Date);let o="hour";return(l||n.day!==i.day||i.time-n.time>W)&&(o="month"),(l||n.year!==i.year)&&(o="year"),(0,D.formatTime)(n.time,t,o,"minute",{timeZoneName:!0})}function O(e){
const t=new Date(e);return{day:t.getDate(),month:t.getMonth()+1,year:t.getFullYear(),hours:t.getHours(),minutes:t.getMinutes(),time:t.getTime(),timezone:-1*t.getTimezoneOffset()/60}}var z=l(471187);var V=l(887859),G=l(553218),q=l(732140),U=l(725230),K=l(315507),X=l(85290),j=l(601474);const Q=new F.PercentageFormatter;function J(e){const{className:t,currentSession:i,lastPriceTime:o,extraHoursPrice:r,extraHoursChange:s,extraHoursChangePercent:u,priceFormatter:d,typespecs:c,currency:v,rtcTime:m,isEconomic:g,lastReleaseDate:p}=e,h="market"===i,f="pre_market"===i||"post_market"===i;if((0,L.isSymbolDelisted)(c))return n.createElement("div",{className:a()(t,j.statusWrapper)},n.createElement(B.DelistedLabel,{className:a()(o&&j.withRightMargin)}),o&&n.createElement("span",{className:j.lastTime},ee(o,!0)));if(g&&p)return n.createElement("div",{className:t},n.createElement("span",{className:a()(j.lastTime)},function(e){const t=window.language?(0,z.getIsoLanguageCodeFromLanguage)(window.language):void 0;return S.t(null,{replace:{date:(0,D.formatTime)(1e3*e,t,"year","day")}},l(800733))}(p)));if(!i)return null;const y=o&&!h;return n.createElement("div",{className:t},n.createElement("div",{className:a()(j.statusWrapper,Z(i))},!f&&n.createElement("span",{className:j.status},n.createElement(A.Icon,{className:j.icon,icon:$(i)}),n.createElement("span",null,Y(i))),y&&n.createElement("span",{className:j.lastTime},ee(o))),f&&n.createElement(n.Fragment,null,null!=s&&n.createElement("div",{className:a()(j.lastPrice,s<0&&j.minus,s>0&&j.plus)},n.createElement("span",{className:j.price},r&&d?.format(r),v&&n.createElement("span",{className:j.currency},v)),n.createElement("span",{className:j.changeWrap},n.createElement("span",{className:j.change},r&&d?.formatChange(r,r-s,{signPositive:!0})),null!=u&&n.createElement("span",{className:j.changePercent},Q.format(u,{signPositive:!0})))),n.createElement("div",{className:a()(j.statusWrapper,j.extra,Z(i))},n.createElement("span",{className:j.status},n.createElement(A.Icon,{className:j.icon,icon:$(i)}),n.createElement("span",null,Y(i))),null==r&&n.createElement(n.Fragment,null,n.createElement("span",null,S.t(null,void 0,l(534288)))),m&&n.createElement("span",{className:j.lastTime},ee(m)))))}function Z(e){switch(e){case"pre_market":return j.yellow;case"holiday":case"out_of_session":return j.neutral;case"post_market":return j.blue;case"market":return j.green;default:return}}function $(e){switch(e){case"pre_market":return X;case"holiday":return U;case"out_of_session":return G;case"post_market":return K;case"market":return q;default:return}}function Y(e){switch(e){case"market":return S.t(null,void 0,l(241410));case"out_of_session":return S.t(null,void 0,l(762464));case"pre_market":return S.t(null,void 0,l(236018));case"post_market":return S.t(null,void 0,l(673897));case"holiday":return S.t(null,void 0,l(987845));default:return}}function ee(e,t){return S.t(null,{replace:{date:H(1e3*e,(0,V.getLocaleIso)(),t)}},l(403213))}l(32133);var te=l(62269);function le(e){return n.createElement("div",{
className:a()(te.dotWrap,e.className)},n.createElement("div",{className:te.dot}))}function ne(e){const t=new URL(e,location.href);return t.host=window.locale_domains&&window.locale_domains[window.locale]||"www.tradingview.com",t.toString()}function ie(e){const{description:t,exchange:l,additionalMain:i,additionalSecondary:o,invalid:r,symbolPagePath:s}=e;if(r)return n.createElement("div",null,n.createElement("span",{className:te.invalid},t));const u=i?.link?"a":"span",d=o?.link?"a":"span";return n.createElement(n.Fragment,null,n.createElement("div",null,n.createElement("span",{className:te.main},t,l&&n.createElement(le,{className:te.smallMargin}),n.createElement("span",{"data-name":"details-exchange"},l))),(i?.value||o?.value)&&n.createElement("div",null,n.createElement("span",{className:te.additional},i?.value&&n.createElement(u,{href:ne(i.link||""),target:"_blank",className:a()(te.additionalText,i.link&&te.link),"data-name":"details-additional-main"},i.value),o?.value&&n.createElement(n.Fragment,null,n.createElement(le,null),n.createElement(d,{href:ne(o.link||""),target:"_blank",className:a()(te.additionalText,o.link&&te.link),"data-name":"details-additional-secondary"},o.value)))))}var oe=l(281956);function ae(e){const{price:t,priceFormatter:l,size:i,className:o}=e;if(l&&t){const e=l.hasForexAdditionalPrecision(),a=l.format(t);if(e){const e=a.toString();return n.createElement("span",{translate:"no",className:o},e.slice(0,e.length-1),n.createElement("sup",null,e.slice(e.length-1)),!!i&&n.createElement("span",null,`×${i}`))}return n.createElement("span",{translate:"no",className:o},a,!!i&&n.createElement("span",null,`×${i}`))}return n.createElement("span",{translate:"no",className:o},t)}var re=l(612217);function se(e){const{className:t,priceFormatter:l,bid:i,ask:o,bidSize:r,askSize:s}=e;return n.createElement("div",{className:a()(re.container,t)},n.createElement(ae,{price:i,priceFormatter:l,size:r,className:re.bid}),n.createElement(ae,{price:o,priceFormatter:l,size:s,className:re.ask}))}var ue=l(431520),de=l(518283),ce=l(65812);function ve(e){const{lowPrice:t,highPrice:l,priceFormatter:i,title:o,open:r,close:s,lastPrice:u,dataName:d}=e,c=l-t,v=(Math.min(s,r)-t)/c*100,m=(Math.max(s,r)-t)/c*100,g=s<r,p=v<=1,h=m>=99,f=100*(u-t)/c,S={width:m-v+"%"},y={};(0,ue.isRtl)()?(S.right=v+"%",y.right=f+"%"):(S.left=v+"%",y.left=f+"%");const _=a()(ce.bar,g&&ce.low,p&&ce.roundedLeft,h&&ce.roundedRight),P=a()(ce.arrow,p&&ce.extraMargin);return n.createElement("div",{className:ce.container,"data-name":d},n.createElement("div",{className:ce.header},t&&n.createElement(ae,{price:t,priceFormatter:i,className:ce.price}),n.createElement("span",{className:ce.title},o),l&&n.createElement(ae,{price:l,priceFormatter:i,className:ce.price})),n.createElement("div",{className:ce.range},n.createElement("div",{className:_,style:S})),n.createElement("div",{className:ce.arrowContainer},n.createElement("div",{className:P,style:y,dangerouslySetInnerHTML:{__html:de}})))}function me(e){
const{openPrice:t,lastPrice:i,lowPrice:o,highPrice:a,price52WeekHigh:r,price52WeekLow:s,priceFormatter:u}=e,d=!(!t||!i)&&t>i,c=d?a:o,v=d?o:a,m=a&&r?Math.max(a,r):r,g=o&&s?Math.min(o,s):s;return n.createElement(ve,{dataName:"details-52wk-range",lowPrice:g,highPrice:m,priceFormatter:u,title:S.t(null,void 0,l(826233)),open:c,close:v,lastPrice:i})}function ge(e){const{className:t,priceFormatter:i}=e;return n.createElement("div",{className:t},he(e)&&n.createElement(ve,{dataName:"details-days-range",title:S.t(null,void 0,l(971406)),lowPrice:e.lowPrice,highPrice:e.highPrice,priceFormatter:i,open:e.openPrice,close:e.lastPrice,lastPrice:e.lastPrice}),pe(e)&&n.createElement(me,{openPrice:e.openPrice,lastPrice:e.lastPrice,lowPrice:e.lowPrice,highPrice:e.highPrice,price52WeekHigh:e.price52WeekHigh,price52WeekLow:e.price52WeekLow,priceFormatter:i}))}function pe(e){const{openPrice:t,lastPrice:l,lowPrice:n,highPrice:i,price52WeekHigh:o,price52WeekLow:a}=e;return void 0!==t&&void 0!==l&&void 0!==n&&void 0!==i&&void 0!==a&&void 0!==o}function he(e){const{openPrice:t,lastPrice:l,lowPrice:n,highPrice:i}=e;return void 0!==t&&void 0!==l&&void 0!==n&&void 0!==i}var fe=l(57174),Se=l(370981),ye=l(109955),_e=l(671945);const Pe=new Map([["Basic Materials",()=>S.t(null,void 0,l(232730))],["Capital Goods",()=>S.t(null,void 0,l(754515))],["Commercial Services",()=>S.t(null,void 0,l(162660))],["Communications",()=>S.t(null,void 0,l(210593))],["Conglomerates",()=>S.t(null,void 0,l(383431))],["Consumer Cyclical",()=>S.t(null,void 0,l(48711))],["Consumer Cyclicals",()=>S.t(null,void 0,l(146053))],["Consumer Durables",()=>S.t(null,void 0,l(76577))],["Consumer Non-Cyclicals",()=>S.t(null,void 0,l(267739))],["Consumer Non-Durables",()=>S.t(null,void 0,l(545853))],["Consumer Services",()=>S.t(null,void 0,l(752020))],["Consumer/Non-Cyclical",()=>S.t(null,void 0,l(876055))],["Distribution Services",()=>S.t(null,void 0,l(631973))],["Electronic Technology",()=>S.t(null,void 0,l(868486))],["Energy",()=>S.t(null,void 0,l(538580))],["Energy Minerals",()=>S.t(null,void 0,l(510492))],["Finance",()=>S.t(null,void 0,l(354140))],["Financial",()=>S.t(null,void 0,l(281841))],["Financials",()=>S.t(null,void 0,l(856135))],["Government",()=>S.t(null,void 0,l(219289))],["Health Services",()=>S.t(null,void 0,l(199493))],["Health Technology",()=>S.t(null,void 0,l(15483))],["Healthcare",()=>S.t(null,void 0,l(362368))],["Industrial Services",()=>S.t(null,void 0,l(231542))],["Industrials",()=>S.t(null,void 0,l(478038))],["Miscellaneous",()=>S.t(null,void 0,l(76201))],["Non-Energy Minerals",()=>S.t(null,void 0,l(831670))],["Process Industries",()=>S.t(null,void 0,l(662723))],["Producer Manufacturing",()=>S.t(null,void 0,l(796367))],["Retail Trade",()=>S.t(null,void 0,l(46761))],["Services",()=>S.t(null,void 0,l(993602))],["Technology",()=>S.t(null,void 0,l(83347))],["Technology Services",()=>S.t(null,void 0,l(12259))],["Telecommunications Services",()=>S.t(null,void 0,l(271566))],["Transportation",()=>S.t(null,void 0,l(134867))],["Utilities",()=>S.t(null,void 0,l(565775))]])
;const be=new Map([["Exotic",()=>S.t(null,void 0,l(257626))],["Major",()=>S.t(null,void 0,l(67973))],["Minor",()=>S.t(null,void 0,l(844650))],["Accessories",()=>S.t(null,void 0,l(670543))],["Accounting & Tax Preparation",()=>S.t(null,void 0,l(573691))],["Adhesive",()=>S.t(null,void 0,l(329690))],["Adult Entertainment Production & Broadcasting",()=>S.t(null,void 0,l(311679))],["Adult Products Retailers",()=>S.t(null,void 0,l(137904))],["Advanced Electronic Equipment",()=>S.t(null,void 0,l(511710))],["Advanced Medical Equipment & Technology",()=>S.t(null,void 0,l(185743))],["Advanced Medical Equipment Wholesale",()=>S.t(null,void 0,l(596772))],["Advanced Polymer",()=>S.t(null,void 0,l(205875))],["Adventure Sports Facilities & Ski Resorts",()=>S.t(null,void 0,l(206))],["Advertising & Marketing",()=>S.t(null,void 0,l(531738))],["Advertising/Marketing Services",()=>S.t(null,void 0,l(86771))],["Advertising Agency",()=>S.t(null,void 0,l(610490))],["Aerospace & Defense",()=>S.t(null,void 0,l(561248))],["Agricultural Biotechnology",()=>S.t(null,void 0,l(526362))],["Agricultural Chemicals Wholesale",()=>S.t(null,void 0,l(170240))],["Agricultural Chemicals",()=>S.t(null,void 0,l(938789))],["Agricultural Commodities/Milling",()=>S.t(null,void 0,l(26604))],["Agricultural Machinery",()=>S.t(null,void 0,l(376946))],["Agriculture Support Services",()=>S.t(null,void 0,l(431716))],["Air & Gas Compressors",()=>S.t(null,void 0,l(73560))],["Air Freight & Courier Services",()=>S.t(null,void 0,l(724707))],["Air Freight & Logistics",()=>S.t(null,void 0,l(359940))],["Air Freight",()=>S.t(null,void 0,l(907130))],["Air Freight/Couriers",()=>S.t(null,void 0,l(596863))],["Air Freight/Delivery Services",()=>S.t(null,void 0,l(150861))],["Aircraft Equipment Wholesale",()=>S.t(null,void 0,l(332315))],["Aircraft Parts Manufacturing",()=>S.t(null,void 0,l(475375))],["Airline Catering Services",()=>S.t(null,void 0,l(624055))],["Airlines",()=>S.t(null,void 0,l(625507))],["Airport Operators",()=>S.t(null,void 0,l(604784))],["Airport Services",()=>S.t(null,void 0,l(331704))],["All Other Food Manufacturing",()=>S.t(null,void 0,l(255796))],["Alternative Medicine Facilities",()=>S.t(null,void 0,l(255862))],["Alternative Medicine",()=>S.t(null,void 0,l(915310))],["Alternative Power Generation",()=>S.t(null,void 0,l(382607))],["Aluminum Rolling",()=>S.t(null,void 0,l(47990))],["Aluminum",()=>S.t(null,void 0,l(449048))],["Ambulance & Emergency Services",()=>S.t(null,void 0,l(169550))],["Amusement Parks and Zoos",()=>S.t(null,void 0,l(285682))],["Animal Breeding",()=>S.t(null,void 0,l(948998))],["Animal Feed",()=>S.t(null,void 0,l(471399))],["Animal Slaughtering & Processing",()=>S.t(null,void 0,l(329704))],["Antique Dealers",()=>S.t(null,void 0,l(21020))],["Apparel & Accessories Retailers",()=>S.t(null,void 0,l(597520))],["Apparel & Accessories",()=>S.t(null,void 0,l(868135))],["Apparel Wholesale",()=>S.t(null,void 0,l(372219))],["Apparel/Footwear",()=>S.t(null,void 0,l(503880))],["Apparel/Footwear Retail",()=>S.t(null,void 0,l(703865))],["Appliance & Houseware Wholesale",()=>S.t(null,void 0,l(734225))],["Appliances, Tools & Housewares",()=>S.t(null,void 0,l(807100))],["Application Software",()=>S.t(null,void 0,l(830626))],["Aquaculture",()=>S.t(null,void 0,l(230251))],["Arms & Ammunitions Manufacturing",()=>S.t(null,void 0,l(728772))],["Auto & Truck Manufacturers",()=>S.t(null,void 0,l(522809))],["Auto & Truck Parts Wholesale",()=>S.t(null,void 0,l(435756))],["Auto & Truck Wholesale",()=>S.t(null,void 0,l(805770))],["Auto Cleaning Products",()=>S.t(null,void 0,l(636832))],["Auto Parts: OEM",()=>S.t(null,void 0,l(300180))],["Auto Vehicles, Parts & Service Retailers",()=>S.t(null,void 0,l(919900))],["Auto, Truck & Motorcycle Parts",()=>S.t(null,void 0,l(13987))],["Automatic Vending Machines",()=>S.t(null,void 0,l(466676))],["Automotive Aftermarket",()=>S.t(null,void 0,l(811471))],["Automobiles & Multi Utility Vehicles",()=>S.t(null,void 0,l(405037))],["Automotive Accessories",()=>S.t(null,void 0,l(472722))],["Automotive Batteries",()=>S.t(null,void 0,l(85512))],["Automotive Body Parts",()=>S.t(null,void 0,l(769888))],["Automotive Parts & Accessories Retailers",()=>S.t(null,void 0,l(977101))],["Automotive Systems",()=>S.t(null,void 0,l(206771))],["Baby Food",()=>S.t(null,void 0,l(902107))],["Ball & Roller Bearings",()=>S.t(null,void 0,l(585436))],["Banks",()=>S.t(null,void 0,l(500002))],["Banquet Halls & Catering",()=>S.t(null,void 0,l(615718))],["Bathroom Fixtures",()=>S.t(null,void 0,l(622534))],["Batteries & Uninterruptible Power supplies",()=>S.t(null,void 0,l(228408))],["Bauxite Mining",()=>S.t(null,void 0,l(597136))],["Beauty Supply Shop",()=>S.t(null,void 0,l(633753))],["Beef & Veal Farming",()=>S.t(null,void 0,l(176625))],["Beer, Wine & Liquor Stores",()=>S.t(null,void 0,l(173555))],["Beverages: Alcoholic",()=>S.t(null,void 0,l(713493))],["Beverages: Non-Alcoholic",()=>S.t(null,void 0,l(952556))],["Beverages (Production/Distribution)",()=>S.t(null,void 0,l(407696))],["Bicycle Manufacturing",()=>S.t(null,void 0,l(650933))],["Bio Diagnostics & Testing",()=>S.t(null,void 0,l(138241))],["Bio Medical Devices",()=>S.t(null,void 0,l(751479))],["Bio Therapeutic Drugs",()=>S.t(null,void 0,l(864813))],["Biodiesel",()=>S.t(null,void 0,l(790377))],["Biomass & Biogas Fuels",()=>S.t(null,void 0,l(32056))],["Biometric Products",()=>S.t(null,void 0,l(230609))],["Biopharmaceuticals",()=>S.t(null,void 0,l(522692))],["Biotechnology",()=>S.t(null,void 0,l(519840))],["Biotechnology & Medical Research",()=>S.t(null,void 0,l(912788))],["Birth Control Products",()=>S.t(null,void 0,l(813393))],["Book & Magazine Retailers",()=>S.t(null,void 0,l(227101))],["Book Printing Services",()=>S.t(null,void 0,l(955793))],["Book Publishing",()=>S.t(null,void 0,l(102866))],["Bottled Water & Ice",()=>S.t(null,void 0,l(371772))],["Branding & Naming",()=>S.t(null,void 0,l(185661))],["Bread & Bakery Product Manufacturing",()=>S.t(null,void 0,l(623822))],["Breakfast Cereal Manufacturing",()=>S.t(null,void 0,l(537786))],["Brewers",()=>S.t(null,void 0,l(28450))],["Broadcasting Equipment",()=>S.t(null,void 0,l(946425))],["Broadcasting",()=>S.t(null,void 0,l(793903))],["Brokerage Services",()=>S.t(null,void 0,l(638532))],["Brooms, Brushes & Dustpans",()=>S.t(null,void 0,l(127331))],["Builder Merchants",()=>S.t(null,void 0,l(652591))],["Building Contractors",()=>S.t(null,void 0,l(886299))],["Building Products",()=>S.t(null,void 0,l(517870))],["Business Services",()=>S.t(null,void 0,l(910808))],["Business Support Services",()=>S.t(null,void 0,l(83558))],["Business Support Supplies",()=>S.t(null,void 0,l(185377))],["Cable Service Providers",()=>S.t(null,void 0,l(642530))],["Cable/Satellite TV",()=>S.t(null,void 0,l(463964))],["Cafes",()=>S.t(null,void 0,l(738908))],["Call Center Services",()=>S.t(null,void 0,l(743166))],["Carbon Capture & Storage",()=>S.t(null,void 0,l(763418))],["Carbonated Soft Drinks",()=>S.t(null,void 0,l(418348))],["Carpets & Curtains",()=>S.t(null,void 0,l(555683))],["Casinos & Gaming",()=>S.t(null,void 0,l(822916))],["Casinos",()=>S.t(null,void 0,l(651323))],["Casinos/Gaming",()=>S.t(null,void 0,l(875753))],["Casualty Insurance",()=>S.t(null,void 0,l(626663))],["Catalog/Specialty Distribution",()=>S.t(null,void 0,l(880458))],["Cellular Fiber",()=>S.t(null,void 0,l(705306))],["Cement & Concrete Manufacturing",()=>S.t(null,void 0,l(627030))],["Charter & Private Air Services",()=>S.t(null,void 0,l(268461))],["Charter Bus Services",()=>S.t(null,void 0,l(156616))],["Chemicals: Agricultural",()=>S.t(null,void 0,l(621021))],["Chemicals: Major Diversified",()=>S.t(null,void 0,l(196805))],["Chemicals: Specialty",()=>S.t(null,void 0,l(713025))],["Child Care & Family Services",()=>S.t(null,void 0,l(753702))],["Children & Infants Clothing Retailers",()=>S.t(null,void 0,l(80956))],["Children & Infants Clothing",()=>S.t(null,void 0,l(4214))],["Chocolate & Confectionery",()=>S.t(null,void 0,l(910841))],["Cigars & Cigarette Manufacturing",()=>S.t(null,void 0,l(313706))],["Civil Engineers & Architects",()=>S.t(null,void 0,l(333067))],["Cleaning Services",()=>S.t(null,void 0,l(481730))],["Clearing, Settlement & Custodial Service",()=>S.t(null,void 0,l(79040))],["Closed End Funds",()=>S.t(null,void 0,l(831900))],["Cloud Computing Services",()=>S.t(null,void 0,l(642494))],["Coal Mining Support",()=>S.t(null,void 0,l(692886))],["Coal Wholesale",()=>S.t(null,void 0,l(21577))],["Coal",()=>S.t(null,void 0,l(433819))],["Coffee & Tea",()=>S.t(null,void 0,l(616158))],["Coffee, Tea & Cocoa Farming",()=>S.t(null,void 0,l(402914))],["Coke Coal Mining",()=>S.t(null,void 0,l(237538))],["Collective Investment Fund Operators",()=>S.t(null,void 0,l(481759))],["Coloring Agent",()=>S.t(null,void 0,l(544863))],["Commercial Aircraft Manufacturing",()=>S.t(null,void 0,l(511992))],["Commercial Banks",()=>S.t(null,void 0,l(250028))],["Commercial Buildings",()=>S.t(null,void 0,l(642175))],["Commercial Document Management",()=>S.t(null,void 0,l(863925))],["Commercial Equipment Rental",()=>S.t(null,void 0,l(933014))],["Commercial Fishing",()=>S.t(null,void 0,l(15302))],["Commercial Food Services",()=>S.t(null,void 0,l(589814))],["Commercial Leasing",()=>S.t(null,void 0,l(178095))],["Commercial Loans",()=>S.t(null,void 0,l(595863))],["Commercial Nurseries",()=>S.t(null,void 0,l(964288))],["Commercial Printing Services",()=>S.t(null,void 0,l(593401))],["Commercial Printing/Forms",()=>S.t(null,void 0,l(67809))],["Commercial REITs",()=>S.t(null,void 0,l(210491))],["Commodity Chemicals Wholesale",()=>S.t(null,void 0,l(168713))],["Commodity Chemicals",()=>S.t(null,void 0,l(989308))],["Communication & Satellite Equipment",()=>S.t(null,void 0,l(557556))],["Communications & Networking",()=>S.t(null,void 0,l(28530))],["Commuter Ferry",()=>S.t(null,void 0,l(285655))],["Commuting Services",()=>S.t(null,void 0,l(94601))],["Computer Communications",()=>S.t(null,void 0,l(257317))],["Computer & Electronics Retailers",()=>S.t(null,void 0,l(503166))],["Computer Hardware & Software Retailers",()=>S.t(null,void 0,l(449538))],["Computer Hardware Component Assembly",()=>S.t(null,void 0,l(331647))],["Computer Hardware",()=>S.t(null,void 0,l(868182))],["Computer Peripherals",()=>S.t(null,void 0,l(312919))],["Computer Processing Hardware",()=>S.t(null,void 0,l(111814))],["Computer Programming",()=>S.t(null,void 0,l(597649))],["Consumer Sundries",()=>S.t(null,void 0,l(640002))],["Computer Training",()=>S.t(null,void 0,l(561743))],["Conferencing Tools & Systems",()=>S.t(null,void 0,l(409535))],["Containers/Packaging",()=>S.t(null,void 0,l(872348))],["Contract Drilling",()=>S.t(null,void 0,l(203769))],["Construction & Engineering",()=>S.t(null,void 0,l(668373))],["Construction Machinery",()=>S.t(null,void 0,l(365084))],["Construction Material Processing",()=>S.t(null,void 0,l(527870))],["Construction Material Wholesale",()=>S.t(null,void 0,l(235142))],["Construction Materials",()=>S.t(null,void 0,l(141906))],["Construction Supplies & Fixtures Wholesale",()=>S.t(null,void 0,l(901072))],["Construction Supplies & Fixtures",()=>S.t(null,void 0,l(265813))],["Construction Supplies",()=>S.t(null,void 0,l(403437))],["Consumer Credit Cards Services",()=>S.t(null,void 0,l(817190))],["Consumer Document Management",()=>S.t(null,void 0,l(794168))],["Consumer Electronic Wholesale",()=>S.t(null,void 0,l(306177))],["Consumer Electronics Retailers",()=>S.t(null,void 0,l(109169))],["Consumer Electronics/Appliances",()=>S.t(null,void 0,l(682503))],["Consumer Goods Rental",()=>S.t(null,void 0,l(692220))],["Consumer Leasing",()=>S.t(null,void 0,l(466661))],["Consumer Lending",()=>S.t(null,void 0,l(746363))],["Consumer Publishing",()=>S.t(null,void 0,l(33212))],["Consumer Repair Services",()=>S.t(null,void 0,l(661301))],["Container & Packaging Material Wholesale",()=>S.t(null,void 0,l(460769))],["Content & Site Management Services",()=>S.t(null,void 0,l(482289))],["Cookie, Cracker & Pasta Manufacturing",()=>S.t(null,void 0,l(919381))],["Copper Ore  Mining",()=>S.t(null,void 0,l(74599))],["Copyright Management",()=>S.t(null,void 0,l(267290))],["Corporate Accounting Services",()=>S.t(null,void 0,l(467219))],["Corporate Financial Services",()=>S.t(null,void 0,l(275894))],["Cosmetics & Perfumes",()=>S.t(null,void 0,l(364539))],["Courier Services",()=>S.t(null,void 0,l(781292))],["Craft & Micro Brewers",()=>S.t(null,void 0,l(854401))],["Credit Unions",()=>S.t(null,void 0,l(127313))],["Cruise Lines",()=>S.t(null,void 0,l(486186))],["Cutlery & Flatware",()=>S.t(null,void 0,l(764809))],["Dairy Products",()=>S.t(null,void 0,l(42174))],["Data Processing Services",()=>S.t(null,void 0,l(250252))],["Deep Sea Freight",()=>S.t(null,void 0,l(820295))],["Department Stores",()=>S.t(null,void 0,l(879104))],["Department/Specialty Retail Stores",()=>S.t(null,void 0,l(46792))],["Design Services",()=>S.t(null,void 0,l(154213))],["Diagnostic & Testing Substances Manufacturers",()=>S.t(null,void 0,l(165787))],["Diagnostic & Testing Substances",()=>S.t(null,void 0,l(700869))],["Diamond Mining",()=>S.t(null,void 0,l(227175))],["Digital Media Agencies",()=>S.t(null,void 0,l(510500))],["Digital Publishing",()=>S.t(null,void 0,l(564537))],["Direct Marketing",()=>S.t(null,void 0,l(528090))],["Directory Publishing",()=>S.t(null,void 0,l(635908))],["Discount Stores with groceries",()=>S.t(null,void 0,l(154865))],["Discount Stores without groceries",()=>S.t(null,void 0,l(291040))],["Discount Stores",()=>S.t(null,void 0,l(257822))],["Display Screens",()=>S.t(null,void 0,l(735029))],["Distilleries",()=>S.t(null,void 0,l(501621))],["Distillers & Wineries",()=>S.t(null,void 0,l(822609))],["Diversified Chemicals",()=>S.t(null,void 0,l(37183))],["Diversified Investment Services",()=>S.t(null,void 0,l(973021))],["Diversified REITs",()=>S.t(null,void 0,l(807194))],["Diversified Trading & Distributing",()=>S.t(null,void 0,l(680380))],["Doctor's Office",()=>S.t(null,void 0,l(564659))],["Dolls & Stuffed Toys",()=>S.t(null,void 0,l(340933))],["Doors & Window Frames",()=>S.t(null,void 0,l(225061))],["Drug Delivery Systems",()=>S.t(null,void 0,l(841028))],["Drug Retailers",()=>S.t(null,void 0,l(658334))],["Drugstore Chains",()=>S.t(null,void 0,l(622590))],["E-commerce & Auction Services",()=>S.t(null,void 0,l(621826))],["Education & Training Information Providers",()=>S.t(null,void 0,l(130078))],["Educational Services",()=>S.t(null,void 0,l(768516))],["Electric Construction",()=>S.t(null,void 0,l(179369))],["Electric Equipment Wholesale",()=>S.t(null,void 0,l(740776))],["Electric Utilities",()=>S.t(null,void 0,l(125491))],["Electric Utilities: Central",()=>S.t(null,void 0,l(835607))],["Electrical (Alternative) Vehicles",()=>S.t(null,void 0,l(409487))],["Electrical Components & Equipment",()=>S.t(null,void 0,l(108066))],["Electrical Measuring & Testing Instruments",()=>S.t(null,void 0,l(956563))],["Electrical Products",()=>S.t(null,void 0,l(286301))],["Electron Tubes & Insulators",()=>S.t(null,void 0,l(145094))],["Electronic Component",()=>S.t(null,void 0,l(827393))],["Electronic Components",()=>S.t(null,void 0,l(220205))],["Electronic Equipment & Parts",()=>S.t(null,void 0,l(573282))],["Electronic Equipments & Parts",()=>S.t(null,void 0,l(745635))],["Electronic Equipment/Instruments",()=>S.t(null,void 0,l(965564))],["Electronic Production Equipment",()=>S.t(null,void 0,l(383656))],["Electronic Repair Services",()=>S.t(null,void 0,l(132563))],["Electronics Distributors",()=>S.t(null,void 0,l(393975))],["Electronics/Appliance Stores",()=>S.t(null,void 0,l(280544))],["Electronics/Appliances",()=>S.t(null,void 0,l(722279))],["Elevator & Conveying Equipment",()=>S.t(null,void 0,l(514913))],["Employment Services",()=>S.t(null,void 0,l(688491))],["Energy Drinks",()=>S.t(null,void 0,l(273398))],["Engine & Powertrain Systems",()=>S.t(null,void 0,l(225923))],["Engineering & Construction",()=>S.t(null,void 0,l(257335))],["Enterprise Software",()=>S.t(null,void 0,l(870249))],["Entertainment Production Equipment & Services",()=>S.t(null,void 0,l(345352))],["Entertainment Production",()=>S.t(null,void 0,l(233100))],["Environmental Biotechnology",()=>S.t(null,void 0,l(788178))],["Environmental Consultancy Services",()=>S.t(null,void 0,l(663850))],["Environmental Services",()=>S.t(null,void 0,l(928349))],["Environmental Services & Equipment",()=>S.t(null,void 0,l(53721))],["Epoxy",()=>S.t(null,void 0,l(147084))],["Ethanol Fuels",()=>S.t(null,void 0,l(473094))],["Exchange Traded Fund",()=>S.t(null,void 0,l(262671))],["Exchange Traded Funds",()=>S.t(null,void 0,l(670868))],["Executive Search Services",()=>S.t(null,void 0,l(341123))],["Exhibition & Conference Services",()=>S.t(null,void 0,l(376874))],["Explosives",()=>S.t(null,void 0,l(222922))],["Fabric Dyeing & Finishing",()=>S.t(null,void 0,l(193309))],["Factoring",()=>S.t(null,void 0,l(39200))],["Fashion Eyewear",()=>S.t(null,void 0,l(422616))],["Fertilizer",()=>S.t(null,void 0,l(269679))],["Fiber Optic Cable Manufacturing",()=>S.t(null,void 0,l(287001))],["Finance: Consumer Services",()=>S.t(null,void 0,l(274678))],["Finance/Rental/Leasing",()=>S.t(null,void 0,l(35078))],["Financial Conglomerates",()=>S.t(null,void 0,l(928200))],["Financial & Commodity Market Operators",()=>S.t(null,void 0,l(892931))],["Financial Information Providers",()=>S.t(null,void 0,l(359217))],["Financial Publishing/Services",()=>S.t(null,void 0,l(372428))],["Financial Technology & Infrastructure",()=>S.t(null,void 0,l(140062))],["Fishing & Farming Wholesale",()=>S.t(null,void 0,l(715348))],["Fishing & Farming",()=>S.t(null,void 0,l(265562))],["Floor Covering Retailers",()=>S.t(null,void 0,l(873806))],["Flooring & Interior Tile Manufacturers",()=>S.t(null,void 0,l(160937))],["Florists",()=>S.t(null,void 0,l(318322))],["Flour Milling",()=>S.t(null,void 0,l(945669))],["Fluid Power Cylinder & Actuators",()=>S.t(null,void 0,l(986387))],["Food Distributors",()=>S.t(null,void 0,l(115823))],["Food Ingredients",()=>S.t(null,void 0,l(657940))],["Food Markets",()=>S.t(null,void 0,l(444450))],["Food Processing",()=>S.t(null,void 0,l(585662))],["Food Retail",()=>S.t(null,void 0,l(37954))],["Food Retail & Distribution",()=>S.t(null,void 0,l(717506))],["Food Wholesale",()=>S.t(null,void 0,l(927618))],["Food: Major Diversified",()=>S.t(null,void 0,l(460357))],["Food: Meat/Fish/Dairy",()=>S.t(null,void 0,l(776574))],["Food: Specialty/Candy",()=>S.t(null,void 0,l(938688))],["Footwear Retailers",()=>S.t(null,void 0,l(649501))],["Footwear Wholesale",()=>S.t(null,void 0,l(312711))],["Footwear",()=>S.t(null,void 0,l(72137))],["Forest & Wood Products",()=>S.t(null,void 0,l(756033))],["Forest Nurseries & Gathering of Forest Products",()=>S.t(null,void 0,l(185675))],["Forest Products",()=>S.t(null,void 0,l(43939))],["Forest Support & Services",()=>S.t(null,void 0,l(324797))],["Fossil Fuel Electric Utilities",()=>S.t(null,void 0,l(682680))],["Fossil Fuel IPPs",()=>S.t(null,void 0,l(687441))],["Freight Logistics",()=>S.t(null,void 0,l(867352))],["Freight Trucking",()=>S.t(null,void 0,l(270695))],["Frozen Food Manufacturing",()=>S.t(null,void 0,l(697450))],["Fruit & Vegetable Processing",()=>S.t(null,void 0,l(555071))],["Fruit Drinks",()=>S.t(null,void 0,l(603096))],["Funeral Services",()=>S.t(null,void 0,l(275470))],["Furniture Retailers",()=>S.t(null,void 0,l(348614))],["Furniture",()=>S.t(null,void 0,l(167872))],["Gambling & Gaming Machine Manufacturers",()=>S.t(null,void 0,l(661741))],["Games, Toys & Children Vehicles",()=>S.t(null,void 0,l(318493))],["Gaming Machine Operators",()=>S.t(null,void 0,l(125817))],["Gas Distributors",()=>S.t(null,void 0,l(487953))],["Gas Drilling - Onshore",()=>S.t(null,void 0,l(995085))],["Gasoline stations",()=>S.t(null,void 0,l(207180))],["General Education Services",()=>S.t(null,void 0,l(527681))],["General Government",()=>S.t(null,void 0,l(977612))],["Generic Pharmaceuticals",()=>S.t(null,void 0,l(53384))],["Geophysical Surveying & Mapping Services",()=>S.t(null,void 0,l(225997))],["Gift, Novelty & Souvenir Stores",()=>S.t(null,void 0,l(651879))],["Glass Containers & Packaging",()=>S.t(null,void 0,l(104892))],["Glasses, Spectacles & Contact lenses",()=>S.t(null,void 0,l(434701))],["Gold Mining",()=>S.t(null,void 0,l(517162))],["Gold",()=>S.t(null,void 0,l(669536))],["Golf Courses",()=>S.t(null,void 0,l(248629))],["Grain (Crop) Production",()=>S.t(null,void 0,l(989611))],["Ground Freight & Logistics",()=>S.t(null,void 0,l(525296))],["Guided Tour Operators",()=>S.t(null,void 0,l(925429))],["Gyms, Fitness and Spa Centers",()=>S.t(null,void 0,l(929153))],["Hair Accessories",()=>S.t(null,void 0,l(59686))],["Halal Animal Slaughtering & Processing",()=>S.t(null,void 0,l(508967))],["Handbags & Luggage Retailers",()=>S.t(null,void 0,l(939917))],["Handbags & Luggage",()=>S.t(null,void 0,l(123075))],["Health Food Stores",()=>S.t(null,void 0,l(894757))],["Health Insurance",()=>S.t(null,void 0,l(978814))],["Healthcare Facilities & Services",()=>S.t(null,void 0,l(141951))],["Healthcare REITs",()=>S.t(null,void 0,l(265452))],["Heating, Ventilation & Air Conditioning Systems",()=>S.t(null,void 0,l(184525))],["Heavy Buses & Coaches",()=>S.t(null,void 0,l(704529))],["Heavy Electrical Equipment",()=>S.t(null,void 0,l(731674))],["Heavy Machinery & Vehicles Wholesale",()=>S.t(null,void 0,l(567395))],["Heavy Machinery & Vehicles",()=>S.t(null,void 0,l(541586))],["Heavy Motors & Generators",()=>S.t(null,void 0,l(34587))],["Heavy Trucks",()=>S.t(null,void 0,l(928660))],["Hedge Funds",()=>S.t(null,void 0,l(388163))],["Highway & Bridge Construction",()=>S.t(null,void 0,l(308790))],["Highway Operators",()=>S.t(null,void 0,l(579896))],["Highways & Rail Tracks",()=>S.t(null,void 0,l(741246))],["Holding Companies",()=>S.t(null,void 0,l(410776))],["Home Audio",()=>S.t(null,void 0,l(357905))],["Home Decor Retailers",()=>S.t(null,void 0,l(671632))],["Home Furnishings Retailers",()=>S.t(null,void 0,l(273584))],["Home Furnishings Wholesale",()=>S.t(null,void 0,l(67034))],["Home Furnishings",()=>S.t(null,void 0,l(904306))],["Home Healthcare Services",()=>S.t(null,void 0,l(326980))],["Home Improvement Chains",()=>S.t(null,void 0,l(889916))],["Home Improvement Products & Services Retailers",()=>S.t(null,void 0,l(735113))],["Homebuilding",()=>S.t(null,void 0,l(451496))],["Horse & Dog Race Tracks",()=>S.t(null,void 0,l(358393))],["Hosiery & Sock",()=>S.t(null,void 0,l(332768))],["Hospital/Nursing Management",()=>S.t(null,void 0,l(223909))],["Hospitality REITs",()=>S.t(null,void 0,l(341506))],["Hospitals, Clinics & Primary Care Services",()=>S.t(null,void 0,l(7239))],["Hotels & Motels",()=>S.t(null,void 0,l(211767))],["Hotels, Motels & Cruise Lines",()=>S.t(null,void 0,l(526581))],["Hotels/Resorts/Cruise lines",()=>S.t(null,void 0,l(127729))],["Household Appliances",()=>S.t(null,void 0,l(880437))],["Household Electronics",()=>S.t(null,void 0,l(776204))],["Household Products",()=>S.t(null,void 0,l(555568))],["Household/Personal Care",()=>S.t(null,void 0,l(223754))],["Human Resources Consulting Services",()=>S.t(null,void 0,l(347126))],["Hydrogen Fuel",()=>S.t(null,void 0,l(779425))],["Hydropower Equipment",()=>S.t(null,void 0,l(452303))],["IT Services & Consulting",()=>S.t(null,void 0,l(487308))],["Independent Power Producers",()=>S.t(null,void 0,l(468730))],["Industrial  Real Estate Development",()=>S.t(null,void 0,l(67476))],["Industrial Biotechnology Chemicals",()=>S.t(null,void 0,l(270111))],["Industrial Clothing & Uniforms",()=>S.t(null,void 0,l(535707))],["Industrial Conglomerates",()=>S.t(null,void 0,l(820900))],["Industrial Electrical Switchgear",()=>S.t(null,void 0,l(124128))],["Industrial Gas",()=>S.t(null,void 0,l(414217))],["Industrial Machinery & Equipment Wholesale",()=>S.t(null,void 0,l(921102))],["Industrial Machinery & Equipment",()=>S.t(null,void 0,l(118945))],["Industrial Machinery",()=>S.t(null,void 0,l(206052))],["Industrial Moulds",()=>S.t(null,void 0,l(397486))],["Industrial Parts & Components",()=>S.t(null,void 0,l(797286))],["Industrial Plant",()=>S.t(null,void 0,l(672551))],["Industrial Process Furnace & Ovens",()=>S.t(null,void 0,l(199085))],["Industrial REITs",()=>S.t(null,void 0,l(62953))],["Industrial Rubber Products",()=>S.t(null,void 0,l(873309))],["Industrial Specialties",()=>S.t(null,void 0,l(869220))],["Industrial Valve Manufacturing",()=>S.t(null,void 0,l(27054))],["Information Technology Services",()=>S.t(null,void 0,l(925276))],["Inland Water Freight",()=>S.t(null,void 0,l(422820))],["Input Devices",()=>S.t(null,void 0,l(572220))],["Insurance - Automobile",()=>S.t(null,void 0,l(199364))],["Insurance Brokers",()=>S.t(null,void 0,l(853223))],["Insurance Brokers/Services",()=>S.t(null,void 0,l(641970))],["Integrated  Mining",()=>S.t(null,void 0,l(324898))],["Integrated Circuits",()=>S.t(null,void 0,l(224708))],["Integrated Logistics Operators",()=>S.t(null,void 0,l(294349))],["Integrated Oil",()=>S.t(null,void 0,l(940364))],["Integrated Oil & Gas",()=>S.t(null,void 0,l(319737))],["Integrated Telecommunications Services",()=>S.t(null,void 0,l(220389))],["Inter-dealer Broker",()=>S.t(null,void 0,l(878620))],["Interior Design Services",()=>S.t(null,void 0,l(555751))],["International Trade Financing",()=>S.t(null,void 0,l(577388))],["Internet & Mail Order Department Stores",()=>S.t(null,void 0,l(234794))],["Internet & Mail Order Discount Stores",()=>S.t(null,void 0,l(240666))],["Internet Gaming",()=>S.t(null,void 0,l(231401))],["Internet Retail",()=>S.t(null,void 0,l(784381))],["Internet Security & Transactions Services",()=>S.t(null,void 0,l(646163))],["Internet Service Providers",()=>S.t(null,void 0,l(968836))],["Internet Services",()=>S.t(null,void 0,l(756806))],["Internet Software/Services",()=>S.t(null,void 0,l(52882))],["Investment Banks/Brokers",()=>S.t(null,void 0,l(735369))],["Investment Bankers/Brokers/Service",()=>S.t(null,void 0,l(700613))],["Investment Banking & Brokerage Services",()=>S.t(null,void 0,l(902609))],["Investment Banking",()=>S.t(null,void 0,l(331631))],["Investment Management & Fund Operators",()=>S.t(null,void 0,l(896340))],["Investment Management",()=>S.t(null,void 0,l(520903))],["Investment Managers",()=>S.t(null,void 0,l(158646))],["Investment Support Tools",()=>S.t(null,void 0,l(939962))],["Investment Trusts",()=>S.t(null,void 0,l(844277))],["Investment Trusts/Mutual Funds",()=>S.t(null,void 0,l(494092))],["Iron Ore Mining",()=>S.t(null,void 0,l(7325))],["Iron, Steel Mills & Foundries",()=>S.t(null,void 0,l(293096))],["Jeans",()=>S.t(null,void 0,l(435523))],["Jewelry & Watch Retailers",()=>S.t(null,void 0,l(711170))],["Jewelry",()=>S.t(null,void 0,l(166107))],["Juvenile Furniture",()=>S.t(null,void 0,l(361570))],["Kitchen Appliances",()=>S.t(null,void 0,l(861096))],["Kitchen Cabinets",()=>S.t(null,void 0,l(162311))],["Knitwear",()=>S.t(null,void 0,l(95364))],["LNG Transportation & Storage",()=>S.t(null,void 0,l(515602))],["Land Division & Subdivision",()=>S.t(null,void 0,l(943747))],["Laptop & Desktop Computers",()=>S.t(null,void 0,l(536169))],["Laser Equipment",()=>S.t(null,void 0,l(682933))],["Laundry Supplies",()=>S.t(null,void 0,l(365570))],["Lead Ore Mining",()=>S.t(null,void 0,l(721645))],["Leather Goods",()=>S.t(null,void 0,l(53249))],["Legal Services",()=>S.t(null,void 0,l(983359))],["Leisure & Recreation",()=>S.t(null,void 0,l(160791))],["Leisure Products Wholesale",()=>S.t(null,void 0,l(605688))],["Life & Health Insurance",()=>S.t(null,void 0,l(999066))],["Life & Health Reinsurance",()=>S.t(null,void 0,l(154701))],["Life/Health Insurance",()=>S.t(null,void 0,l(962992))],["Life Insurance",()=>S.t(null,void 0,l(709387))],["Light Trucks",()=>S.t(null,void 0,l(656467))],["Lighting Equipment",()=>S.t(null,void 0,l(997242))],["Lighting Fixtures",()=>S.t(null,void 0,l(451881))],["Lingerie",()=>S.t(null,void 0,l(26053))],["Local Agencies",()=>S.t(null,void 0,l(332421))],["Locomotive Engines & Rolling Stock",()=>S.t(null,void 0,l(523640))],["Logging & Sawmills",()=>S.t(null,void 0,l(665805))],["Lottery Operators",()=>S.t(null,void 0,l(315963))],["Luxury Accessories",()=>S.t(null,void 0,l(983083))],["Luxury Car Dealers",()=>S.t(null,void 0,l(216770))],["Machine Tools",()=>S.t(null,void 0,l(481550))],["Magazine Publishing",()=>S.t(null,void 0,l(472769))],["Maintenance & Repair Services",()=>S.t(null,void 0,l(649429))],["Major Banks",()=>S.t(null,void 0,l(52315))],["Major Pharmaceuticals",()=>S.t(null,void 0,l(231840))],["Major Telecommunications",()=>S.t(null,void 0,l(695157))],["Malt producers",()=>S.t(null,void 0,l(576086))],["Managed Health care",()=>S.t(null,void 0,l(356323))],["Managed Health Care",()=>S.t(null,void 0,l(940017))],["Management Consulting Services",()=>S.t(null,void 0,l(61221))],["Marinas",()=>S.t(null,void 0,l(668728))],["Marine Cargo Handling Services",()=>S.t(null,void 0,l(346491))],["Marine Freight & Logistics",()=>S.t(null,void 0,l(344789))],["Marine Logistics",()=>S.t(null,void 0,l(135424))],["Marine Passenger Transportation",()=>S.t(null,void 0,l(826861))],["Marine Port Services",()=>S.t(null,void 0,l(684542))],["Marine Shipping",()=>S.t(null,void 0,l(759014))],["Market Research",()=>S.t(null,void 0,l(528685))],["Marketing Consulting Services",()=>S.t(null,void 0,l(437898))],["Media Buying Agency",()=>S.t(null,void 0,l(581239))],["Media Conglomerates",()=>S.t(null,void 0,l(561349))],["Medical & Diagnostic Laboratories",()=>S.t(null,void 0,l(302677))],["Medical Devices & Implants",()=>S.t(null,void 0,l(455121))],["Medical Diagnostic & Testing Equipment",()=>S.t(null,void 0,l(761040))],["Medical Distributors",()=>S.t(null,void 0,l(752415))],["Medical Equipment Wholesale",()=>S.t(null,void 0,l(149943))],["Medical Equipment",()=>S.t(null,void 0,l(115240))],["Medical Equipment, Supplies & Distribution",()=>S.t(null,void 0,l(435643))],["Medical Imaging Systems",()=>S.t(null,void 0,l(160428))],["Medical Monitoring Systems",()=>S.t(null,void 0,l(208844))],["Medical Prosthetics",()=>S.t(null,void 0,l(672378))],["Medical Software & Technology Services",()=>S.t(null,void 0,l(64611))],["Medical Specialties",()=>S.t(null,void 0,l(671521))],["Medical Supplies",()=>S.t(null,void 0,l(112347))],["Medical/Nursing Services",()=>S.t(null,void 0,l(286978))],["Memory Chips (RAM)",()=>S.t(null,void 0,l(605863))],["Men's Apparel Retailers",()=>S.t(null,void 0,l(89049))],["Men's Clothing",()=>S.t(null,void 0,l(378898))],["Men's Footwear",()=>S.t(null,void 0,l(695281))],["Merchant Banks",()=>S.t(null,void 0,l(298268))],["Metal Containers & Packaging",()=>S.t(null,void 0,l(542903))],["Metal Fabrication",()=>S.t(null,void 0,l(327273))],["Metal Merchant Wholesalers",()=>S.t(null,void 0,l(976996))],["Metal Service Centers",()=>S.t(null,void 0,l(779097))],["Metallic Rolling & Drawing Products",()=>S.t(null,void 0,l(137580))],["Microfinancing",()=>S.t(null,void 0,l(233329))],["Mining Machinery & Equipment Manufacturing",()=>S.t(null,void 0,l(691113))],["Mining Support Activities",()=>S.t(null,void 0,l(171681))],["Mining Support Services & Equipment",()=>S.t(null,void 0,l(83666))],["Miscellaneous",()=>S.t(null,void 0,l(76201))],["Miscellaneous Commercial Services",()=>S.t(null,void 0,l(981897))],["Miscellaneous Manufacturing",()=>S.t(null,void 0,l(886897))],["Mobile Application Software",()=>S.t(null,void 0,l(707957))],["Mobile Phone Retailers",()=>S.t(null,void 0,l(225193))],["Mobile System Software",()=>S.t(null,void 0,l(848981))],["Mortgage REITs",()=>S.t(null,void 0,l(653282))],["Motor Vehicles",()=>S.t(null,void 0,l(159132))],["Motorcycle Dealers",()=>S.t(null,void 0,l(688345))],["Motorcycle Parts & Accessories",()=>S.t(null,void 0,l(828351))],["Motorcycles & Scooters",()=>S.t(null,void 0,l(8552))],["Movie Theaters & Movie Products",()=>S.t(null,void 0,l(19834))],["Movie, TV Production & Distribution",()=>S.t(null,void 0,l(850763))],["Movies/Entertainment",()=>S.t(null,void 0,l(606877))],["Multiline Insurance & Brokers",()=>S.t(null,void 0,l(77418))],["Multiline Utilities",()=>S.t(null,void 0,l(495405))],["Multi-Line Insurance",()=>S.t(null,void 0,l(124012))],["Municipality",()=>S.t(null,void 0,l(250510))],["Museums & Historic Places",()=>S.t(null,void 0,l(85577))],["Music, Music Video Production & Distribution",()=>S.t(null,void 0,l(195077))],["Musical Instrument Retailers",()=>S.t(null,void 0,l(543730))],["Musical Instruments",()=>S.t(null,void 0,l(139466))],["Mutual Funds",()=>S.t(null,void 0,l(537064))],["National Agency",()=>S.t(null,void 0,l(66802))],["National Agencies",()=>S.t(null,void 0,l(632159))],["Natural Fabrics",()=>S.t(null,void 0,l(280655))],["Natural Gas Distribution",()=>S.t(null,void 0,l(812774))],["Natural Gas Exploration & Production - Offshore",()=>S.t(null,void 0,l(46947))],["Natural Gas Exploration & Production - Onshore",()=>S.t(null,void 0,l(234287))],["Natural Gas Pipeline",()=>S.t(null,void 0,l(111171))],["Natural Gas Utilities",()=>S.t(null,void 0,l(829724))],["Network Equipment",()=>S.t(null,void 0,l(94972))],["New Car Dealers",()=>S.t(null,void 0,l(450298))],["News Agencies",()=>S.t(null,void 0,l(566644))],["Newspaper & Magazine Printing Services",()=>S.t(null,void 0,l(113989))],["Newspaper Publishing",()=>S.t(null,void 0,l(439605))],["Newsprint Mills",()=>S.t(null,void 0,l(735544))],["Nickel Ore Mining",()=>S.t(null,void 0,l(834615))],["Non-Alcoholic Beverages",()=>S.t(null,void 0,l(851363))],["Non-Paper Containers & Packaging",()=>S.t(null,void 0,l(723550))],["Non-ferrous Metal Mining",()=>S.t(null,void 0,l(735888))],["Non-ferrous Metal Processing",()=>S.t(null,void 0,l(471630))],["Nuclear IPPs",()=>S.t(null,void 0,l(355785))],["Nuclear Utilities",()=>S.t(null,void 0,l(401273))],["Nursery & Garden Centers",()=>S.t(null,void 0,l(907418))],["Office Equipment & Supplies Rental",()=>S.t(null,void 0,l(567934))],["Office Equipment Wholesale",()=>S.t(null,void 0,l(990042))],["Office Equipment",()=>S.t(null,void 0,l(858466))],["Office Equipment/Supplies",()=>S.t(null,void 0,l(842943))],["Office Furniture",()=>S.t(null,void 0,l(512989))],["Office REITs",()=>S.t(null,void 0,l(718948))],["Office Real Estate Development",()=>S.t(null,void 0,l(97252))],["Office Real Estate Services",()=>S.t(null,void 0,l(250838))],["Office Supplies & Stationery Stores",()=>S.t(null,void 0,l(925078))],["Office Supplies Wholesale",()=>S.t(null,void 0,l(703832))],["Office Supplies",()=>S.t(null,void 0,l(411281))],["Office Technology Equipment",()=>S.t(null,void 0,l(63187))],["Oil & Gas Drilling",()=>S.t(null,void 0,l(288936))],["Oil & Gas Exploration and Production",()=>S.t(null,void 0,l(602501))],["Oil & Gas Pipelines",()=>S.t(null,void 0,l(203944))],["Oil & Gas Production",()=>S.t(null,void 0,l(988763))],["Oil & Gas Refining and Marketing",()=>S.t(null,void 0,l(323349))],["Oil & Gas Storage",()=>S.t(null,void 0,l(623983))],["Oil & Gas Transportation Services",()=>S.t(null,void 0,l(813512))],["Oil Drilling - Offshore",()=>S.t(null,void 0,l(336264))],["Oil Drilling - Onshore",()=>S.t(null,void 0,l(116247))],["Oil Exploration & Production - Offshore",()=>S.t(null,void 0,l(835202))],["Oil Exploration & Production - Onshore",()=>S.t(null,void 0,l(803589))],["Oil Pipeline",()=>S.t(null,void 0,l(771551))],["Oil Refining/Marketing",()=>S.t(null,void 0,l(314932))],["Oil Related - Surveying & Mapping Services",()=>S.t(null,void 0,l(194988))],["Oil Related Equipment",()=>S.t(null,void 0,l(487685))],["Oil Related Services and Equipment",()=>S.t(null,void 0,l(276729))],["Oil Related Services",()=>S.t(null,void 0,l(132664))],["Oilfield Services/Equipment",()=>S.t(null,void 0,l(966417))],["Online Job portals",()=>S.t(null,void 0,l(940800))],["Optical Goods Stores",()=>S.t(null,void 0,l(67338))],["Organic & Ecologically Produced Fabric",()=>S.t(null,void 0,l(998073))],["Organic Fertilizer",()=>S.t(null,void 0,l(729027))],["Other Consumer Services",()=>S.t(null,void 0,l(421626))],["Other Consumer Specialties",()=>S.t(null,void 0,l(346452))],["Other Metals/Minerals",()=>S.t(null,void 0,l(338016))],["Other Specialty Retailers",()=>S.t(null,void 0,l(66480))],["Other Transportation",()=>S.t(null,void 0,l(342675))],["Outdoor Advertising",()=>S.t(null,void 0,l(773040))],["Outsourcing & Staffing Services",()=>S.t(null,void 0,l(538600))],["Packaged Software",()=>S.t(null,void 0,l(712489))],["Paint & Coating",()=>S.t(null,void 0,l(613704))],["Paper Mills & Products",()=>S.t(null,void 0,l(792216))],["Paper Packaging Wholesale",()=>S.t(null,void 0,l(941185))],["Paper Packaging",()=>S.t(null,void 0,l(565297))],["Paper Product Wholesale",()=>S.t(null,void 0,l(43045))],["Paper Products",()=>S.t(null,void 0,l(459003))],["Parking Lot Operators",()=>S.t(null,void 0,l(516834))],["Passenger Car rental",()=>S.t(null,void 0,l(744814))],["Passenger Transportation, Ground & Sea",()=>S.t(null,void 0,l(638193))],["Pension Funds",()=>S.t(null,void 0,l(640248))],["Personal & Car Loans",()=>S.t(null,void 0,l(637884))],["Personal Care Services",()=>S.t(null,void 0,l(105047))],["Personal Legal Services",()=>S.t(null,void 0,l(669173))],["Personal Music Players",()=>S.t(null,void 0,l(410366))],["Personal Products",()=>S.t(null,void 0,l(170621))],["Personal Services",()=>S.t(null,void 0,l(271699))],["Personnel Services",()=>S.t(null,void 0,l(208743))],["Pest Control Services",()=>S.t(null,void 0,l(247683))],["Pesticide",()=>S.t(null,void 0,l(131964))],["Pet & Pet Supplies Retailers",()=>S.t(null,void 0,l(174084))],["Pet Food Manufacturing",()=>S.t(null,void 0,l(65543))],["Petroleum Product Wholesale",()=>S.t(null,void 0,l(188593))],["Petroleum Refining",()=>S.t(null,void 0,l(21303))],["Pharmaceuticals",()=>S.t(null,void 0,l(510877))],["Pharmaceuticals: Generic",()=>S.t(null,void 0,l(513128))],["Pharmaceuticals: Major",()=>S.t(null,void 0,l(879520))],["Pharmaceuticals: Other",()=>S.t(null,void 0,l(121395))],["Phones & Handheld Devices",()=>S.t(null,void 0,l(529699))],["Phones & Smart Phones",()=>S.t(null,void 0,l(386848))],["Photographic Equipment",()=>S.t(null,void 0,l(319047))],["Photovoltaic Solar Systems & Equipment",()=>S.t(null,void 0,l(11390))],["Plastic Container & Packaging",()=>S.t(null,void 0,l(720086))],["Plastics",()=>S.t(null,void 0,l(83982))],["Platinum Mining",()=>S.t(null,void 0,l(988357))],["Plays & Concert Production",()=>S.t(null,void 0,l(56267))],["Plumbing Fixtures & Fittings",()=>S.t(null,void 0,l(789874))],["Point of Sale Systems",()=>S.t(null,void 0,l(916747))],["Port Operators",()=>S.t(null,void 0,l(913945))],["Port Warehousing Services",()=>S.t(null,void 0,l(677932))],["Portable Motors & Generators",()=>S.t(null,void 0,l(555835))],["Portable Satellite Navigation",()=>S.t(null,void 0,l(12366))],["Poultry Farming",()=>S.t(null,void 0,l(312451))],["Precious Metals",()=>S.t(null,void 0,l(586270))],["Precious Metals & Minerals",()=>S.t(null,void 0,l(698191))],["Prefabricated Homes",()=>S.t(null,void 0,l(183747))],["Primary Aluminum Production",()=>S.t(null,void 0,l(372767))],["Private Banks",()=>S.t(null,void 0,l(546564))],["Private Equity",()=>S.t(null,void 0,l(809775))],["Processors",()=>S.t(null,void 0,l(114814))],["Professional Information Services",()=>S.t(null,void 0,l(886075))],["Professional Sports Venues",()=>S.t(null,void 0,l(132398))],["Programming Software & Testing Tools",()=>S.t(null,void 0,l(168010))],["Property & Casualty Insurance",()=>S.t(null,void 0,l(66292))],["Property & Casualty Reinsurance",()=>S.t(null,void 0,l(9002))],["Property Insurance",()=>S.t(null,void 0,l(570594))],["Property-Casualty Insurers",()=>S.t(null,void 0,l(389460))],["Property/Casualty Insurance",()=>S.t(null,void 0,l(335615))],["Province/State",()=>S.t(null,void 0,l(168378))],["Public Relations",()=>S.t(null,void 0,l(650127))],["Public Sport Facilities",()=>S.t(null,void 0,l(319488))],["Publishing: Books/Magazines",()=>S.t(null,void 0,l(295447))],["Publishing: Newspapers",()=>S.t(null,void 0,l(58825))],["Pubs, Bars & Night Clubs",()=>S.t(null,void 0,l(57025))],["Pulp Mills",()=>S.t(null,void 0,l(77054))],["Pulp & Paper",()=>S.t(null,void 0,l(938610))],["Pump & Pumping Equipment",()=>S.t(null,void 0,l(825507))],["Purification & Treatment Equipment",()=>S.t(null,void 0,l(186424))],["Quick Service Restaurants",()=>S.t(null,void 0,l(68443))],["RFID Systems",()=>S.t(null,void 0,l(535003))],["Radio Broadcasting",()=>S.t(null,void 0,l(143542))],["Rail Services",()=>S.t(null,void 0,l(70519))],["Railroads",()=>S.t(null,void 0,l(579371))],["Railway Construction",()=>S.t(null,void 0,l(856346))],["Railway Freight Operators",()=>S.t(null,void 0,l(822650))],["Rare Earth Minerals",()=>S.t(null,void 0,l(107603))],["Rating Agencies",()=>S.t(null,void 0,l(268380))],["Ready-Made Meals",()=>S.t(null,void 0,l(870561))],["Real Estate Development",()=>S.t(null,void 0,l(809918))],["Real Estate Development & Operations",()=>S.t(null,void 0,l(425162))],["Real Estate Investment Trusts",()=>S.t(null,void 0,l(741536))],["Real Estate Services",()=>S.t(null,void 0,l(404450))],["Recreational Products",()=>S.t(null,void 0,l(394838))],["Regional Airlines",()=>S.t(null,void 0,l(18867))],["Regional Banks",()=>S.t(null,void 0,l(999299))],["Reinsurance",()=>S.t(null,void 0,l(306357))],["Renewable Energy Equipment & Services",()=>S.t(null,void 0,l(317387))],["Renewable Energy Services",()=>S.t(null,void 0,l(666280))],["Renewable Fuels",()=>S.t(null,void 0,l(758352))],["Renewable IPPs",()=>S.t(null,void 0,l(115785))],["Renewable Utilities",()=>S.t(null,void 0,l(515299))],["Residential  Real Estate Development",()=>S.t(null,void 0,l(781779))],["Residential & Long Term Care",()=>S.t(null,void 0,l(90578))],["Residential Architectural & Interior Design Services",()=>S.t(null,void 0,l(269195))],["Residential Builders - Multifamily Homes",()=>S.t(null,void 0,l(452633))],["Residential Builders - Single Homes",()=>S.t(null,void 0,l(960430))],["Residential REITs",()=>S.t(null,void 0,l(93567))],["Residential Real Estate Services",()=>S.t(null,void 0,l(125001))],["Resort Operators",()=>S.t(null,void 0,l(805025))],["Restaurants",()=>S.t(null,void 0,l(339188))],["Restaurants & Bars",()=>S.t(null,void 0,l(898403))],["Retail  Real Estate Development",()=>S.t(null,void 0,l(296146))],["Retail & Mortgage Banks",()=>S.t(null,void 0,l(292279))],["Retail - Department Stores",()=>S.t(null,void 0,l(805409))],["Retail - Drugs with Grocery",()=>S.t(null,void 0,l(477333))],["Retail - Drugs without Grocery",()=>S.t(null,void 0,l(958285))],["Retail REITs",()=>S.t(null,void 0,l(42332))],["Retail Real Estate Services",()=>S.t(null,void 0,l(476807))],["Retirement Home Builders",()=>S.t(null,void 0,l(743391))],["Rock Mining",()=>S.t(null,void 0,l(545687))],["Roofing Supplies",()=>S.t(null,void 0,l(84573))],["Rubber Plantation",()=>S.t(null,void 0,l(152894))],["Sailing Yachts & Motorboats",()=>S.t(null,void 0,l(565082))],["Sales Promotions & Events Management",()=>S.t(null,void 0,l(247130))],["Sanitary Products",()=>S.t(null,void 0,l(45712))],["Satellite  Systems & Accessories",()=>S.t(null,void 0,l(635631))],["Satellite Service Operators",()=>S.t(null,void 0,l(996719))],["Savings Banks",()=>S.t(null,void 0,l(486753))],["Scientific & Precision Equipment",()=>S.t(null,void 0,l(183210))],["Scientific & Super Computers",()=>S.t(null,void 0,l(793502))],["Sea-Borne Tankers",()=>S.t(null,void 0,l(963653))],["Seafood Product Preparation & Packaging",()=>S.t(null,void 0,l(202209))],["Search Engines",()=>S.t(null,void 0,l(590814))],["Secondary Smelting & Alloying of Aluminum",()=>S.t(null,void 0,l(60769))],["Securities & Commodity Exchanges",()=>S.t(null,void 0,l(298390))],["Security & Surveillance",()=>S.t(null,void 0,l(15286))],["Security Services",()=>S.t(null,void 0,l(723180))],["Self-Storage REITs",()=>S.t(null,void 0,l(295255))],["Semiconductor Equipment & Testing",()=>S.t(null,void 0,l(226914))],["Semiconductor Equipment Wholesalers",()=>S.t(null,void 0,l(726997))],["Semiconductor Machinery Manufacturing",()=>S.t(null,void 0,l(472247))],["Semiconductor Testing Equipment & Service",()=>S.t(null,void 0,l(381059))],["Semiconductor Wholesale",()=>S.t(null,void 0,l(649159))],["Semiconductors",()=>S.t(null,void 0,l(377038))],["Semiprecious Gem Stones",()=>S.t(null,void 0,l(743535))],["Server & Database Software",()=>S.t(null,void 0,l(785484))],["Servers & Systems",()=>S.t(null,void 0,l(680170))],["Services to the Health Industry",()=>S.t(null,void 0,l(861229))],["Sewage Treatment Facilities",()=>S.t(null,void 0,l(343684))],["Shell Companies",()=>S.t(null,void 0,l(612406))],["Ship Part Manufacturer",()=>S.t(null,void 0,l(628911))],["Ship Repairing & Maintanance",()=>S.t(null,void 0,l(621690))],["Shipbuilding",()=>S.t(null,void 0,l(133840))],["Signs & Advertising Specialty Producers",()=>S.t(null,void 0,l(531424))],["Silver Mining",()=>S.t(null,void 0,l(11836))],["Smart Grid & Electrical Transmission",()=>S.t(null,void 0,l(480414))],["Smart Grid & Power Distribution Construction",()=>S.t(null,void 0,l(445800))],["Snack Food & Non-chocolate Confectionary",()=>S.t(null,void 0,l(36147))],["Social Media & Networking",()=>S.t(null,void 0,l(938381))],["Soft Furnishing Retailers",()=>S.t(null,void 0,l(208073))],["Software",()=>S.t(null,void 0,l(522180))],["Sovereign",()=>S.t(null,void 0,l(694418))],["Spacecraft Manufacturing",()=>S.t(null,void 0,l(36697))],["Special Foods & Welbeing Products",()=>S.t(null,void 0,l(766971))],["Special Foods & Wellbeing Products",()=>S.t(null,void 0,l(42008))],["Specialized Aviation Services",()=>S.t(null,void 0,l(299808))],["Specialized Printing Services",()=>S.t(null,void 0,l(947403))],["Specialized REITs",()=>S.t(null,void 0,l(865161))],["Specialty & Advanced Pharmaceuticals",()=>S.t(null,void 0,l(34512))],["Specialty Chemicals Wholesale",()=>S.t(null,void 0,l(625160))],["Specialty Chemicals",()=>S.t(null,void 0,l(766191))],["Specialty Insurance",()=>S.t(null,void 0,l(531853))],["Specialty Insurers",()=>S.t(null,void 0,l(992208))],["Specialty Mining & Metals Wholesale",()=>S.t(null,void 0,l(160840))],["Specialty Mining & Metals",()=>S.t(null,void 0,l(277832))],["Specialty Stores",()=>S.t(null,void 0,l(780797))],["Specialty Telecommunications",()=>S.t(null,void 0,l(840180))],["Sporting & Outdoor Goods",()=>S.t(null,void 0,l(891707))],["Sporting Goods Stores",()=>S.t(null,void 0,l(847772))],["Sports & Outdoor Footwear",()=>S.t(null,void 0,l(264918))],["Sports & Outdoors Retailers",()=>S.t(null,void 0,l(2390))],["Sportswear & Outdoors Clothing",()=>S.t(null,void 0,l(600973))],["Starch, Vegetable Fat & Oil Manufacturing",()=>S.t(null,void 0,l(759872))],["State Agency",()=>S.t(null,void 0,l(192488))],["Stationary Fuel Cells",()=>S.t(null,void 0,l(117336))],["Steam & Air-Conditioning Supply",()=>S.t(null,void 0,l(846725))],["Steel",()=>S.t(null,void 0,l(726086))],["Storage Devices",()=>S.t(null,void 0,l(692966))],["Sugar & Artificial Sweeteners",()=>S.t(null,void 0,l(430788))],["Sugarcane Farming",()=>S.t(null,void 0,l(55256))],["Supermarkets & Convenience Stores",()=>S.t(null,void 0,l(995172))],["Supranational",()=>S.t(null,void 0,l(73122))],["Switchgear",()=>S.t(null,void 0,l(812285))],["Synthetic Fabrics",()=>S.t(null,void 0,l(580157))],["System Software",()=>S.t(null,void 0,l(614777))],["TV & Video",()=>S.t(null,void 0,l(210099))],["Tablet & Netbook Computers",()=>S.t(null,void 0,l(347383))],["Tanning & Softening agents",()=>S.t(null,void 0,l(896524))],["Taxi & Limousine",()=>S.t(null,void 0,l(216379))],["Technology Consulting & Outsourcing Services",()=>S.t(null,void 0,l(787426))],["Telecommunication Construction",()=>S.t(null,void 0,l(75083))],["Telecommunications Equipment",()=>S.t(null,void 0,l(282374))],["Telecommunications Network Infrastructure",()=>S.t(null,void 0,l(134178))],["Telecommunications Resellers",()=>S.t(null,void 0,l(242131))],["Telemedicine Services",()=>S.t(null,void 0,l(844618))],["Television Broadcasting",()=>S.t(null,void 0,l(826179))],["Testing & Measuring Equipment",()=>S.t(null,void 0,l(698290))],["Testing Laboratories",()=>S.t(null,void 0,l(262141))],["Testing Services",()=>S.t(null,void 0,l(822261))],["Textiles & Leather Goods Wholesale",()=>S.t(null,void 0,l(46585))],["Textiles & Leather Goods",()=>S.t(null,void 0,l(202473))],["Textiles",()=>S.t(null,void 0,l(56700))],["Theatres & Performing Arts",()=>S.t(null,void 0,l(193536))],["Thermal Solar Systems & Equipment",()=>S.t(null,void 0,l(296391))],["Tile & Paving Material Manufacturing",()=>S.t(null,void 0,l(669537))],["Timber REITs",()=>S.t(null,void 0,l(31726))],["Timber Tract Operations",()=>S.t(null,void 0,l(468120))],["Tire & Tube Manufacturers",()=>S.t(null,void 0,l(282430))],["Tire Dealers",()=>S.t(null,void 0,l(870039))],["Tire Retreading",()=>S.t(null,void 0,l(234902))],["Tires & Rubber Products Wholesale",()=>S.t(null,void 0,l(488524))],["Tires & Rubber Products",()=>S.t(null,void 0,l(874936))],["Tobacco",()=>S.t(null,void 0,l(96599))],["Tools & Hardware",()=>S.t(null,void 0,l(786542))],["Tools & Housewares",()=>S.t(null,void 0,l(449798))],["Toys & Games Retailers",()=>S.t(null,void 0,l(699699))],["Toys & Juvenile Products Wholesale",()=>S.t(null,void 0,l(973787))],["Toys & Juvenile Products",()=>S.t(null,void 0,l(458476))],["Trade & Business Publishing",()=>S.t(null,void 0,l(892159))],["Transaction & Payment Services",()=>S.t(null,void 0,l(311225))],["Translation & Interpretation Services",()=>S.t(null,void 0,l(924054))],["Travel Agents",()=>S.t(null,void 0,l(817795))],["Trucking",()=>S.t(null,void 0,l(678706))],["Trucks/Construction/Farm Machinery",()=>S.t(null,void 0,l(112238))],["Turbine Manufacturing",()=>S.t(null,void 0,l(140795))],["Unconventional Oil & Gas Drilling",()=>S.t(null,void 0,l(505905))],["Unconventional Oil & Gas Production",()=>S.t(null,void 0,l(184628))],["Uranium Mining",()=>S.t(null,void 0,l(499123))],["Uranium",()=>S.t(null,void 0,l(676204))],["Used Car Dealers",()=>S.t(null,void 0,l(907369))],["Used Merchandise Stores",()=>S.t(null,void 0,l(161223))],["VOIP Equipment & Systems",()=>S.t(null,void 0,l(783767))],["VOIP Services",()=>S.t(null,void 0,l(173126))],["Vegetable, Fruit & Nut Farming",()=>S.t(null,void 0,l(144117))],["Vending Machine Providers",()=>S.t(null,void 0,l(185733))],["Venture Capital",()=>S.t(null,void 0,l(300223))],["Veterinary Drugs",()=>S.t(null,void 0,l(75634))],["Veterinary Medical Equipment & Supplies",()=>S.t(null,void 0,l(102359))],["Veterinary Services",()=>S.t(null,void 0,l(789996))],["Wallpaper",()=>S.t(null,void 0,l(622354))],["Warehousing",()=>S.t(null,void 0,l(402407))],["Waste Management, Disposal & Recycling Services",()=>S.t(null,void 0,l(383203))],["Waste to Energy Systems & Equipment",()=>S.t(null,void 0,l(778396))],["Watches",()=>S.t(null,void 0,l(877669))],["Water & Sewage Construction",()=>S.t(null,void 0,l(619233))],["Water Supply & Irrigation Systems",()=>S.t(null,void 0,l(358963))],["Water Utilities",()=>S.t(null,void 0,l(678587))],["Wave Power Energy Equipment",()=>S.t(null,void 0,l(164549))],["Wealth Management",()=>S.t(null,void 0,l(46160))],["Welding & Soldering Equipment",()=>S.t(null,void 0,l(472007))],["Wholesale Distributors",()=>S.t(null,void 0,l(259742))],["Wi-Fi & Wi-Max Providers",()=>S.t(null,void 0,l(436058))],["Wind Systems & Equipment",()=>S.t(null,void 0,l(266315))],["Wineries",()=>S.t(null,void 0,l(303068))],["Wired Telecommunications Carriers",()=>S.t(null,void 0,l(593201))],["Wireless Telecom",()=>S.t(null,void 0,l(256865))],["Wireless Telecommunications",()=>S.t(null,void 0,l(918887))],["Wireless Telecommunications Services",()=>S.t(null,void 0,l(192979))],["Wires & Cables",()=>S.t(null,void 0,l(426425))],["Women's Apparel Retailers",()=>S.t(null,void 0,l(658168))],["Women's Clothing",()=>S.t(null,void 0,l(322938))],["Women's Footwear",()=>S.t(null,void 0,l(498630))],["Wood Container & Packaging",()=>S.t(null,void 0,l(501290))],["Wood Product Wholesale",()=>S.t(null,void 0,l(174743))],["Wood Products",()=>S.t(null,void 0,l(507294))],["Yarn Goods",()=>S.t(null,void 0,l(64065))],["Zinc Ore Mining",()=>S.t(null,void 0,l(736684))]])
;function we(e){const t=be.get(e);return void 0!==t?t():function(e){const t=Pe.get(e);return void 0!==t?t():e}(e)}
const Ce=new Map([["Africa",()=>S.t(null,void 0,l(578163))],["Americas",()=>S.t(null,void 0,l(615493))],["Asia",()=>S.t(null,void 0,l(360389))],["Europe",()=>S.t(null,void 0,l(546890))],["Pacific",()=>S.t(null,void 0,l(847081))],["Middle East",()=>S.t(null,void 0,l(64614))],["Afghanistan",()=>S.t(null,void 0,l(641527))],["Åland Islands",()=>S.t(null,void 0,l(831928))],["Aland Islands",()=>S.t(null,void 0,l(831928))],["Albania",()=>S.t(null,void 0,l(959086))],["Algeria",()=>S.t(null,void 0,l(856683))],["American Samoa",()=>S.t(null,void 0,l(61015))],["Andorra",()=>S.t(null,void 0,l(673791))],["Angola",()=>S.t(null,void 0,l(512563))],["Anguilla",()=>S.t(null,void 0,l(372813))],["Antarctica",()=>S.t(null,void 0,l(166557))],["Antigua and Barbuda",()=>S.t(null,void 0,l(817607))],["Argentina",()=>S.t(null,void 0,l(106226))],["Armenia",()=>S.t(null,void 0,l(533146))],["Aruba",()=>S.t(null,void 0,l(100499))],["Australia",()=>S.t(null,void 0,l(722157))],["Austria",()=>S.t(null,void 0,l(696600))],["Azerbaijan",()=>S.t(null,void 0,l(692604))],["Bahamas",()=>S.t(null,void 0,l(1096))],["Bahrain",()=>S.t(null,void 0,l(290594))],["Bangladesh",()=>S.t(null,void 0,l(225040))],["Barbados",()=>S.t(null,void 0,l(907853))],["Belarus",()=>S.t(null,void 0,l(58753))],["Belgium",()=>S.t(null,void 0,l(795521))],["Belize",()=>S.t(null,void 0,l(73958))],["Benin",()=>S.t(null,void 0,l(59986))],["Bermuda",()=>S.t(null,void 0,l(155340))],["Bhutan",()=>S.t(null,void 0,l(264708))],["Bolivia",()=>S.t(null,void 0,l(568977))],["Bonaire, Sint Eustatius and Saba",()=>S.t(null,void 0,l(717326))],["Bosnia and Herzegovina",()=>S.t(null,void 0,l(749972))],["Botswana",()=>S.t(null,void 0,l(682635))],["Bouvet Island",()=>S.t(null,void 0,l(987629))],["Brazil",()=>S.t(null,void 0,l(497100))],["British Indian Ocean Territory",()=>S.t(null,void 0,l(695239))],["British Virgin Islands",()=>S.t(null,void 0,l(179027))],["Brunei",()=>S.t(null,void 0,l(44542))],["Bulgaria",()=>S.t(null,void 0,l(734548))],["Burkina Faso",()=>S.t(null,void 0,l(691063))],["Burundi",()=>S.t(null,void 0,l(395606))],["Cabo Verde",()=>S.t(null,void 0,l(835641))],["Cambodia",()=>S.t(null,void 0,l(68997))],["Cameroon",()=>S.t(null,void 0,l(213250))],["Canada",()=>S.t(null,void 0,l(588590))],["Cayman Islands",()=>S.t(null,void 0,l(847135))],["Central African Republic",()=>S.t(null,void 0,l(907024))],["Chad",()=>S.t(null,void 0,l(556572))],["Chile",()=>S.t(null,void 0,l(887583))],["China",()=>S.t(null,void 0,l(133470))],["Christmas Island",()=>S.t(null,void 0,l(192739))],["Cocos (Keeling) Islands",()=>S.t(null,void 0,l(777344))],["Colombia",()=>S.t(null,void 0,l(882104))],["Comoros",()=>S.t(null,void 0,l(84472))],["Congo",()=>S.t(null,void 0,l(796825))],["Congo (the Democratic Republic of the)",()=>S.t(null,void 0,l(179502))],["Cook Islands",()=>S.t(null,void 0,l(366326))],["Curacao",()=>S.t(null,void 0,l(738609))],["Costa Rica",()=>S.t(null,void 0,l(153161))],["Côte d'Ivoire",()=>S.t(null,void 0,l(430952))],["Croatia",()=>S.t(null,void 0,l(96747))],["Cuba",()=>S.t(null,void 0,l(909463))],["Curaçao",()=>S.t(null,void 0,l(429420))],["Cyprus",()=>S.t(null,void 0,l(935415))],["Czechia",()=>S.t(null,void 0,l(659645))],["Czech Republic",()=>S.t(null,void 0,l(728962))],["Denmark",()=>S.t(null,void 0,l(877527))],["Djibouti",()=>S.t(null,void 0,l(44760))],["Dominica",()=>S.t(null,void 0,l(224496))],["Dominican Republic",()=>S.t(null,void 0,l(213047))],["Ecuador",()=>S.t(null,void 0,l(236580))],["Egypt",()=>S.t(null,void 0,l(324432))],["El Salvador",()=>S.t(null,void 0,l(561191))],["Equatorial Guinea",()=>S.t(null,void 0,l(161980))],["Eritrea",()=>S.t(null,void 0,l(429824))],["Estonia",()=>S.t(null,void 0,l(714153))],["Falkland Islands",()=>S.t(null,void 0,l(455450))],["Eswatini",()=>S.t(null,void 0,l(544127))],["Ethiopia",()=>S.t(null,void 0,l(634249))],["European Union",()=>S.t(null,void 0,l(256596))],["Falkland Islands (Malvinas)",()=>S.t(null,void 0,l(357436))],["Faroe Islands",()=>S.t(null,void 0,l(330249))],["Fiji",()=>S.t(null,void 0,l(883259))],["Finland",()=>S.t(null,void 0,l(223442))],["France",()=>S.t(null,void 0,l(891160))],["French Guiana",()=>S.t(null,void 0,l(438361))],["French Polynesia",()=>S.t(null,void 0,l(886670))],["French Southern Territories",()=>S.t(null,void 0,l(366930))],["Gabon",()=>S.t(null,void 0,l(226211))],["Gambia",()=>S.t(null,void 0,l(952020))],["Georgia",()=>S.t(null,void 0,l(43846))],["Germany",()=>S.t(null,void 0,l(403447))],["Ghana",()=>S.t(null,void 0,l(573817))],["Gibraltar",()=>S.t(null,void 0,l(656047))],["Greece",()=>S.t(null,void 0,l(327434))],["Greenland",()=>S.t(null,void 0,l(1235))],["Grenada",()=>S.t(null,void 0,l(881641))],["Guadeloupe",()=>S.t(null,void 0,l(473562))],["Guam",()=>S.t(null,void 0,l(567540))],["Guatemala",()=>S.t(null,void 0,l(961914))],["Guernsey",()=>S.t(null,void 0,l(681937))],["Guinea",()=>S.t(null,void 0,l(317252))],["Guinea-Bissau",()=>S.t(null,void 0,l(967090))],["Guyana",()=>S.t(null,void 0,l(855033))],["Haiti",()=>S.t(null,void 0,l(887765))],["Heard Island and McDonald Islands",()=>S.t(null,void 0,l(80508))],["Holy See",()=>S.t(null,void 0,l(99838))],["Honduras",()=>S.t(null,void 0,l(282792))],["Hong Kong",()=>S.t(null,void 0,l(248861))],["Hungary",()=>S.t(null,void 0,l(893317))],["Iceland",()=>S.t(null,void 0,l(217499))],["India",()=>S.t(null,void 0,l(719912))],["Indonesia",()=>S.t(null,void 0,l(532355))],["Iraq",()=>S.t(null,void 0,l(128295))],["Ireland",()=>S.t(null,void 0,l(501759))],["Isle of Man",()=>S.t(null,void 0,l(521579))],["Israel",()=>S.t(null,void 0,l(368291))],["Italy",()=>S.t(null,void 0,l(35146))],["Jamaica",()=>S.t(null,void 0,l(503861))],["Japan",()=>S.t(null,void 0,l(200186))],["Jersey",()=>S.t(null,void 0,l(645271))],["Jordan",()=>S.t(null,void 0,l(75447))],["Kazakhstan",()=>S.t(null,void 0,l(892613))],["Kenya",()=>S.t(null,void 0,l(981233))],["Kiribati",()=>S.t(null,void 0,l(184523))],["Kosovo",()=>S.t(null,void 0,l(757671))],["Kuwait",()=>S.t(null,void 0,l(76614))],["Kyrgyzstan",()=>S.t(null,void 0,l(35025))],["Laos",()=>S.t(null,void 0,l(185682))],["Latvia",()=>S.t(null,void 0,l(262063))],["Lebanon",()=>S.t(null,void 0,l(516210))],["Lesotho",()=>S.t(null,void 0,l(365317))],["Liberia",()=>S.t(null,void 0,l(710525))],["Libya",()=>S.t(null,void 0,l(301115))],["Liechtenstein",()=>S.t(null,void 0,l(813250))],["Lithuania",()=>S.t(null,void 0,l(868380))],["Luxembourg",()=>S.t(null,void 0,l(681038))],["Macau",()=>S.t(null,void 0,l(846081))],["Macao",()=>S.t(null,void 0,l(978374))],["Macedonia",()=>S.t(null,void 0,l(739008))],["Madagascar",()=>S.t(null,void 0,l(133712))],["Malawi",()=>S.t(null,void 0,l(546923))],["Malaysia",()=>S.t(null,void 0,l(910613))],["Maldives",()=>S.t(null,void 0,l(39656))],["Mali",()=>S.t(null,void 0,l(365226))],["Malta",()=>S.t(null,void 0,l(638365))],["Marshall Islands",()=>S.t(null,void 0,l(218866))],["Martinique",()=>S.t(null,void 0,l(933381))],["Mauritania",()=>S.t(null,void 0,l(579706))],["Mauritius",()=>S.t(null,void 0,l(635743))],["Mayotte",()=>S.t(null,void 0,l(213187))],["Mexico",()=>S.t(null,void 0,l(774951))],["Micronesia (Federated States of)",()=>S.t(null,void 0,l(56829))],["Moldova",()=>S.t(null,void 0,l(150883))],["Monaco",()=>S.t(null,void 0,l(440019))],["Mongolia",()=>S.t(null,void 0,l(770187))],["Montenegro",()=>S.t(null,void 0,l(641357))],["Montserrat",()=>S.t(null,void 0,l(498609))],["Morocco",()=>S.t(null,void 0,l(224794))],["Mozambique",()=>S.t(null,void 0,l(579468))],["Myanmar",()=>S.t(null,void 0,l(9877))],["Namibia",()=>S.t(null,void 0,l(12872))],["Nauru",()=>S.t(null,void 0,l(342666))],["Nepal",()=>S.t(null,void 0,l(512503))],["Netherlands",()=>S.t(null,void 0,l(376970))],["New Caledonia",()=>S.t(null,void 0,l(364389))],["New Zealand",()=>S.t(null,void 0,l(866103))],["Nicaragua",()=>S.t(null,void 0,l(227276))],["Niger",()=>S.t(null,void 0,l(966217))],["Nigeria",()=>S.t(null,void 0,l(591520))],["Niue",()=>S.t(null,void 0,l(304944))],["Norfolk Island",()=>S.t(null,void 0,l(467891))],["North Macedonia",()=>S.t(null,void 0,l(163654))],["Northern Mariana Islands",()=>S.t(null,void 0,l(201209))],["Norway",()=>S.t(null,void 0,l(723309))],["Oman",()=>S.t(null,void 0,l(349693))],["Pakistan",()=>S.t(null,void 0,l(917892))],["Palau",()=>S.t(null,void 0,l(399553))],["Palestine, State of",()=>S.t(null,void 0,l(997622))],["Panama",()=>S.t(null,void 0,l(614586))],["Papua New Guinea",()=>S.t(null,void 0,l(639272))],["Paraguay",()=>S.t(null,void 0,l(449486))],["Peru",()=>S.t(null,void 0,l(184677))],["Philippines",()=>S.t(null,void 0,l(728919))],["Pitcairn",()=>S.t(null,void 0,l(292752))],["Poland",()=>S.t(null,void 0,l(15112))],["Portugal",()=>S.t(null,void 0,l(958902))],["Puerto Rico",()=>S.t(null,void 0,l(162916))],["Qatar",()=>S.t(null,void 0,l(328756))],["Réunion",()=>S.t(null,void 0,l(907816))],["La Reunion",()=>S.t(null,void 0,l(907816))],["Romania",()=>S.t(null,void 0,l(700102))],["Russia",()=>S.t(null,void 0,l(859546))],["Russian Federation",()=>S.t(null,void 0,l(115446))],["SINT MAARTEN (DUTCH PART)",()=>S.t(null,void 0,l(404421))],["Rwanda",()=>S.t(null,void 0,l(260044))],["Saint Barthélemy",()=>S.t(null,void 0,l(19774))],["Saint Helena, Ascension and Tristan da Cunha",()=>S.t(null,void 0,l(936924))],["Saint Kitts and Nevis",()=>S.t(null,void 0,l(714742))],["Saint Lucia",()=>S.t(null,void 0,l(556268))],["Saint Martin (French part)",()=>S.t(null,void 0,l(653833))],["Saint Pierre and Miquelon",()=>S.t(null,void 0,l(438490))],["Saint Vincent and the Grenadines",()=>S.t(null,void 0,l(283190))],["Samoa",()=>S.t(null,void 0,l(552348))],["San Marino",()=>S.t(null,void 0,l(303407))],["Sao Tome and Principe",()=>S.t(null,void 0,l(890812))],["Saudi Arabia",()=>S.t(null,void 0,l(605991))],["Senegal",()=>S.t(null,void 0,l(444125))],["Serbia",()=>S.t(null,void 0,l(428819))],["Seychelles",()=>S.t(null,void 0,l(650985))],["Sierra Leone",()=>S.t(null,void 0,l(28461))],["Singapore",()=>S.t(null,void 0,l(977377))],["Sint Maarten (Dutch part)",()=>S.t(null,void 0,l(145578))],["Slovakia",()=>S.t(null,void 0,l(742494))],["Slovenia",()=>S.t(null,void 0,l(721687))],["Solomon Islands",()=>S.t(null,void 0,l(761110))],["Somalia",()=>S.t(null,void 0,l(34194))],["South Africa",()=>S.t(null,void 0,l(663647))],["South Georgia and the South Sandwich Islands",()=>S.t(null,void 0,l(826082))],["South Korea",()=>S.t(null,void 0,l(617661))],["South Sudan",()=>S.t(null,void 0,l(998037))],["Spain",()=>S.t(null,void 0,l(174897))],["Sri Lanka",()=>S.t(null,void 0,l(308201))],["Sudan",()=>S.t(null,void 0,l(18118))],["Suriname",()=>S.t(null,void 0,l(729068))],["Svalbard and Jan Mayen",()=>S.t(null,void 0,l(562457))],["Sweden",()=>S.t(null,void 0,l(73412))],["Switzerland",()=>S.t(null,void 0,l(508058))],["Syria",()=>S.t(null,void 0,l(717325))],["Taiwan",()=>S.t(null,void 0,l(59215))],["Tajikistan",()=>S.t(null,void 0,l(434058))],["Tanzania",()=>S.t(null,void 0,l(635623))],["Thailand",()=>S.t(null,void 0,l(215786))],["Timor-Leste",()=>S.t(null,void 0,l(367129))],["Togo",()=>S.t(null,void 0,l(631945))],["Tokelau",()=>S.t(null,void 0,l(298549))],["Tonga",()=>S.t(null,void 0,l(435125))],["Trinidad and Tobago",()=>S.t(null,void 0,l(390103))],["Tunisia",()=>S.t(null,void 0,l(420349))],["Turkey",()=>S.t(null,void 0,l(929826))],["Turkmenistan",()=>S.t(null,void 0,l(650800))],["Turks and Caicos Islands",()=>S.t(null,void 0,l(606372))],["Tuvalu",()=>S.t(null,void 0,l(747680))],["U.S. Virgin Islands",()=>S.t(null,void 0,l(2120))],["UAE",()=>S.t(null,void 0,l(879479))],["USA",()=>S.t(null,void 0,l(232240))],["Uganda",()=>S.t(null,void 0,l(87849))],["Ukraine",()=>S.t(null,void 0,l(364971))],["United Arab Emirates",()=>S.t(null,void 0,l(770695))],["United Kingdom",()=>S.t(null,void 0,l(460411))],["United States",()=>S.t(null,void 0,l(415459))],["United States Minor Outlying Islands",()=>S.t(null,void 0,l(266635))],["Uruguay",()=>S.t(null,void 0,l(931315))],["USA",()=>S.t(null,void 0,l(232240))],["Uzbekistan",()=>S.t(null,void 0,l(552979))],["Vanuatu",()=>S.t(null,void 0,l(105598))],["Venezuela",()=>S.t(null,void 0,l(250614))],["Vietnam",()=>S.t(null,void 0,l(403944))],["Virgin Islands (British)",()=>S.t(null,void 0,l(960580))],["Virgin Islands (U.S.)",()=>S.t(null,void 0,l(914315))],["Wallis and Futuna",()=>S.t(null,void 0,l(57056))],["Western Sahara",()=>S.t(null,void 0,l(75648))],["Yemen",()=>S.t(null,void 0,l(510588))],["Zambia",()=>S.t(null,void 0,l(652361))],["Zimbabwe",()=>S.t(null,void 0,l(42386))]]),Me=(0,
_e.getLogger)("ErrorBoundary");class Ee extends n.Component{constructor(){super(...arguments),this.state={hasError:!1}}componentDidCatch(e){this.props.onError?this.props.onError(e):Me.logError(e.message)}render(){return this.state.hasError?n.createElement(n.Fragment,null,this.props.fallback):n.createElement(n.Fragment,null,this.props.children)}static getDerivedStateFromError(e){return{hasError:!0}}}const Ne=!1;var Te=l(251387);const Fe=(0,_e.getLogger)("Details"),Ie=["Description","Price","MarketStatus","BidAndAsk","Ranges","KeyFacts","News","Ideas","Minds","Notes","FuturesChart","BondsChart","ExpectedPnlChart","KeyStats","OptionsPricingModel","BondsPlug","BondsClassification","BondsRedemption","BondsIssueRating","Earnings","Financials","Dividends","MoreFinancials","Performance","Seasonals","FundsExposure","Technicals","AnalystRecommendations","Profile","EconomyIndicators","Options"],Re=new Set(["BidAndAsk","FuturesChart","BondsChart","FundsExposure","BondsClassification","BondsRedemption","BondsIssueRating","ExpectedPnlChart"]),ke=new Map;for(let e=0;e<Ie.length;e++)ke.set(Ie[e],e);const xe=(0,I.mergeThemes)(b,{highlight:Te.highlight,growing:Te.growing,falling:Te.falling});function Ae(e){const{visibility:t,showSocialPageLinks:i=!0,showBondsPlug:o=!0,financialsLink:r,technicalsLink:u,forecastLink:d,seasonalsLink:c,optionsLink:v,dividendsLink:m,earningsLink:p,corporateBondsLink:h,etfAnalysisLink:f,economyIndicatorsLink:_,useNotesRouting:P}=e,b=function(e,t){const l=(0,s.usePrevious)(e),i=(0,n.useRef)(null);return(t=t??((e,t,l)=>e!==t&&t!==l))(i.current,l,e)&&(i.current=l),i.current}(e,(function(e,t,l){if(null===e&&!Nl(l)&&Nl(t))return!0;return e?.symbol!==t?.symbol&&t?.symbol!==l.symbol&&Nl(t)})),w=El()?e:(0,
g.ensureNotNull)(b),{className:C,isDialog:M,complete:E,descriptionClassName:N,volume:T,averageVolume30dCalc:F,aum:I,navDiscountPremium:R,beta1Year:k,currentSession:A,updateMode:B,updateModeSeconds:L,lastPrice:D,futures:W,bonds:H,cryptoData:O,change:z,changePercent:V,currency:G,fundamentalCurrencyCode:q,lastPriceTime:U,extraHoursPrice:K,extraHoursChange:X,extraHoursChangePercent:j,proName:Q,shortName:Z,type:$,typespecs:Y,symbolDescription:ee,exchange:te,listedExchange:le,invalid:ne,marketCap:oe,basicEarningsTTM:ae,dividendsYield:re,priceToEarningsRatioTTM:ue,employees:de,floatShares:ce,performance:ve,navTotalReturn:me,widgetWidth:pe,symbol:he,pricescale:_e,minMovePrimary:Pe,minMoveSecondary:be,fractional:we,summaryCoefficient:Ce,openPrice:Me,lowPrice:Ne,highPrice:Ae,price52WeekHigh:Le,price52WeekLow:We,bid:Oe,ask:ze,bidSize:Ve,askSize:Ge,financialsData:qe,incomeData:Ue,websiteUrl:Ke,businessDescription:Xe,newsPreview:je,primaryName:Qe,symbolNotes:Je,priceTargetAverage:Ze,recommendationMark:$e,ratesPtToSymbol:Ye,dividendsData:et,source2:tt,format:lt,lastReportFrequency:nt,isinDisplayed:it,figi:ot,cryptoMetaData:at,cryptoAsset:rt,cryptoAssetData:st,dividendsAvailability:ut,optionsInfo:dt,assetClass:ct,focus:vt,expenseRatio:mt,launchDate:gt,issuer:pt,brand:ht,homepage:ft,indexTracked:St,etfAssetTypeExposure:yt,etfRegionExposure:_t,fundViewMode:Pt,variableTickSize:bt,etfHoldingsCount:wt,openInterest:Ct,pointvalue:Mt,frontContract:Et,contractDescription:Nt,expiration:Tt,allTimeLowDay:Ft,allTimeLow:It,allTimeHighDay:Rt,allTimeHigh:kt,referenceLastPeriod:xt,forecastRaw:At,nextReleaseDate:Bt,lastReleaseDate:Lt,outstandingAmount:Dt,nominalValue:Wt,denomMin:Ht,currentCoupon:Ot,couponTypeGeneral:zt,couponFrequency:Vt,yieldToMaturity:Gt,maturityDate:qt,daysToMaturity:Ut,bondIssuer:Kt,issueDate:Xt,bondIssuerStockSymbol:jt,placementType:Qt,durationType:Jt,maturityType:Zt,offerType:$t,redemptionType:Yt,conversionOption:el,sinkingFund:tl,ownershipForm:ll,rtcTime:nl,totalIssuedAmount:il,paidAmount:ol,bondsIssueRatingItems:al,firstBarTime1D:rl,underlyingSymbolData:sl,optionStyle:ul,lotsize:dl,iv:cl,strike:vl}=w,ml=(0,n.useRef)(new Map),gl=(0,n.useRef)(new Map),{additionalMain:pl,additionalSecondary:hl}=De(w,i),fl=te,Sl=($&&Y&&isEtf($,Y),(0,n.useRef)(null)),yl=(0,n.useMemo)((()=>new fe.LastChangeFormatter({format:lt,priceScale:_e,minMove:Pe,minMove2:be,fractional:we,variableMinTick:bt})),[_e,Pe,we,be,lt,bt]);(0,n.useMemo)((()=>H?.filter((e=>e.typespecs.includes("yield")))),[Y,H]);(0,n.useEffect)((()=>{t&&(ml.current.forEach(((e,t)=>{Fe.logDebug(`${t} render reason: ${e}`)})),gl.current.forEach(((e,t)=>{Fe.logDebug(`${t} were blocked by ${JSON.stringify(Array.from(e))}`)})))}),[t]),(0,n.useEffect)((()=>{ml.current=new Map,gl.current=new Map}),[e.symbol]);const{seasonalsAction:_l=null,forecastAction:Pl=null,corporateBondAction:bl=null,analysisAction:wl=null,optionsAction:Cl=null,economyIndicatorsAction:Ml=null}={};wl?.isDisabled(),Pl?.isDisabled(),_l?.isDisabled(),bl?.isDisabled(),Cl?.isDisabled(),Ml?.isDisabled()
;return n.createElement(ye.OverlayScrollWrapper,{className:a()(C,!El()&&Te.opacity),onContextMenu:y.preventDefaultForContextMenu,onScrollCapture:function(e){Sl.current?.contains(e.target)&&Se.globalCloseDelegate.fire()},scrollContainerRef:Sl},(!El()||(Be("Description",w)||t))&&n.createElement(Ee,{onError:e=>He("Description",e)},n.createElement("div",{className:a()(Te.widgetWrapper,N)},ne?n.createElement(ie,{description:S.t(null,void 0,l(652969)),invalid:ne}):n.createElement(ie,{description:ee,exchange:tt&&"economic"===$?tt.description:fl,symbolPagePath:void 0,additionalMain:pl,additionalSecondary:hl}))),Tl("Price")&&n.createElement(Ee,{onError:e=>He("Price",e)},n.createElement(x,{className:a()(Te.widgetWrapper,Te.userSelectText,(A||"economic"===$&&Lt)&&Te.offsetDisabled),updateMode:B,classSuffix:"--for-details",updateModeSeconds:L,symbol:he,priceFormatter:yl,lastPrice:D,change:z,changePercent:V,currency:G,type:$,fractional:we,changeSignPositive:!0,lastPriceTheme:xe})),Tl("MarketStatus")&&n.createElement(Ee,{onError:e=>He("MarketStatus",e)},n.createElement(J,{className:Te.widgetWrapper,currentSession:A,priceFormatter:yl,lastPriceTime:U,rtcTime:nl,extraHoursPrice:K,extraHoursChange:X,extraHoursChangePercent:j,currency:G,typespecs:Y,isEconomic:"economic"===$,lastReleaseDate:Lt})),Tl("BidAndAsk")&&n.createElement(Ee,{onError:e=>He("BidAndAsk",e)},n.createElement(se,{className:Te.widgetWrapper,bid:Oe,bidSize:Ve,ask:ze,askSize:Ge,priceFormatter:yl})),Tl("Ranges")&&n.createElement(Ee,{onError:e=>He("Ranges",e)},n.createElement(ge,{className:Te.widgetWrapper,openPrice:Me,lastPrice:D,lowPrice:Ne,highPrice:Ae,price52WeekHigh:Le,price52WeekLow:We,priceFormatter:yl})),!1,!1,!1,!1,!1);function El(){return Nl(e)||null===b}function Nl(e){return null!==e&&(Be("Description",e)||Boolean(t))}function Tl(e){if(t&&Be(e,w))return ml.current.has(e)||ml.current.set(e,E?"complete":"timeout"),!0;const l=Ie.slice(0,(0,g.ensureDefined)(ke.get(e))).filter((e=>!Re.has(e))),n=!El()||l.every((e=>Be(e,w))),i=n&&Be(e,w);if(!n&&Be(e,w)){const t=l.find((e=>!Be(e,w)));if(void 0!==t){const l=gl.current.get(e);l?l.add(t):gl.current.set(e,new Set([t]))}}return i&&!ml.current.has(e)&&ml.current.set(e,"data exist"),i}}function Be(e,t){switch(t.disabledSet,e){case"Description":return t.invalid||Boolean(De(t).additionalMain?.value);case"Price":return void 0!==t.lastPrice;case"KeyFacts":return Boolean(t.complete&&Le()&&t.showNews&&t.dailySummaryAst);case"News":return Boolean(t.complete&&!(t.dailySummaryAst&&Le())&&t.showNews);case"Ideas":return Boolean(t.showIdeas);case"Minds":return Boolean(t.showMinds);case"Notes":return Boolean(t.showNotes)&&void 0!==t.notesStatus&&(t.notesStatus===SymbolNotesStatus.Fetched||window&&!window.is_authenticated);case"MarketStatus":return Boolean(c.enabled("display_market_status")&&(t.currentSession||"economic"===t.type&&t.lastReleaseDate));case"BidAndAsk":return!Ne&&Boolean(t.bid)&&Boolean(t.ask);case"Ranges":return he(t)||pe(t);case"FuturesChart":return Boolean(t.futures&&t.futures.length>0);case"BondsChart":
return Boolean(t.bonds&&t.bonds.length>0&&isYield({typespecs:t.typespecs}));case"KeyStats":return hasKeyStats(t.type,t.typespecs);case"OptionsPricingModel":return Boolean(t.type&&isOption(t.type)&&(t.delta||t.theta||t.gamma||t.rho||t.vega||t.theoPrice));case"Earnings":return checkEarningsVisible(t.incomeData);case"Dividends":return void 0!==t.type&&["stock","dr","fund","structured"].includes(t.type)&&void 0!==t.dividendsData.hasDividends&&(l=t.type,n=t.typespecs,!(l&&n&&isFundEtf(l,n)));case"Financials":return Boolean(t.financialsData&&(t.financialsData[PeriodId.Year]||t.financialsData[PeriodId.Quarter]||t.financialsData[PeriodId.HalfYear]));case"MoreFinancials":case"AnalystRecommendations":default:return!1;case"Performance":return checkPerformanceVisible(t.performance);case"FundsExposure":return Boolean(t.type&&t.typespecs&&isFundEtf(t.type,t.typespecs));case"Technicals":return void 0!==t.summaryCoefficient;case"Profile":return function(e){return Boolean("economic"===e.type?e.businessDescription:e.websiteUrl||e.businessDescription||e.employees||e.isinDisplayed||e.bondIssuerStockSymbol||e.bondIssuer||e.type&&isOption(e.type)&&(e.underlyingSymbol||e.optionStyle)||("spread"!==e.type?e.figi:null)||e.cryptoAsset)}(t);case"EconomyIndicators":return Boolean(t.type&&isEconomicSymbol(t.type)&&t.financialIndicatorId);case"Options":return isFeatureEnabled("options_details_widget")&&Boolean(t.optionsInfo)&&checkExchangeOptionsVisible(t.listedExchange);case"BondsClassification":return Oe(t);case"BondsRedemption":return Oe(t)&&Boolean(t.outstandingAmount||t.paidAmount);case"BondsIssueRating":return Oe(t)&&Boolean(t.bondsIssueRatingItems&&t.bondsIssueRatingItems.length>0);case"Seasonals":case"BondsPlug":case"ExpectedPnlChart":return Boolean(!1)}var l,n}function Le(){return"en"===window.locale}function De(e,t=!0){const{type:l,proName:n,sector:i,industry:o,typespecs:a,market:r}=e,s=l&&oe.DESCRIPTION_WITH_INDUSTRY_MARKET_LIST.has(l),u=l&&a&&isCorporateBond(l,a);if(s||u){let e,t;return{additionalMain:{value:we(n&&i||""),link:e},additionalSecondary:{value:we(o||""),link:t}}}const d=new Set(["cfd","crypto"]);a&&a.filter((e=>d.has(e)));return{additionalMain:{value:We(e)}}}function We(e){const{proName:t,type:n,typespecs:i,country:o,sector:a,completeScreener:r}=e,s=t&&o||"",u=t&&a||"";let d;switch(n){case"equity":d=S.t(null,void 0,l(522653));break;case"futures":d=S.t(null,void 0,l(353539));break;case"index":d=S.t(null,void 0,l(223051));break;case"spread":d=S.t(null,void 0,l(245967));break;case"economic":d=S.t(null,void 0,l(576796));break;case"cfd":d="CFD";break;case"bond":d=S.t(null,void 0,l(642358));break;case"forex":let e;switch(u){case"Minor":e=S.t(null,{context:"currency"},l(91005));break;case"Major":e=S.t(null,{context:"currency"},l(4798));break;case"Exotic":e=S.t(null,void 0,l(257626));break;default:e=u}e&&s?d=S.t(null,void 0,l(246201)).replace("{sector}",e).replace("{country}",function(e){const t=Ce.get(e);return void 0!==t?t():e}(s)):r&&(d=S.t(null,void 0,l(442370)))}return d}function He(e,t){
Fe.logError(`An error occurred in block ${e}`);try{Fe.logError(`${t.message} ${t.stack}`)}catch{}}function Oe(e){return Boolean(!1)}const ze=!1;function Ve(e){const{detailsHeaderProps:t,detailsInfoProps:l,newsPreview:i,widgetWidth:o,showNews:a,visibility:r}=e,[s,u]=(0,n.useState)({showMinds:!1,showIdeas:!1,showNotes:!1}),[d,c]=[];return(0,n.useEffect)((()=>{!l?.proName&&l?.invalid||l?.proName?u({showIdeas:!0,showMinds:!0,showNotes:!0}):l?.proName||u({showIdeas:!1,showMinds:!1,showNotes:!1})}),[l?.proName,l?.invalid]),n.createElement(n.Fragment,null,t&&n.createElement(f,{disabledSet:d,onDisabledSetChange:c,...t}),l&&n.createElement(Ae,{...l,newsPreview:i,widgetWidth:o,symbolNotes:undefined,notesStatus:undefined,disabledSet:d,showIdeas:!ze&&s.showIdeas,showMinds:!ze&&s.showMinds,showNotes:s.showNotes,showNews:a,visibility:r}))}var Ge=l(297265),qe=l(924910),Ue=l(334529),Ke=l(574266);function Xe(e){return{symbolName:e}}function je(e){return{source:"RightToolbar",symbol:e,dividendsData:{hasDividends:void 0}}}const Qe=!1,Je=!1;function Ze(e){const{bridge:t}=(0,m.useEnsuredContext)(p),l=(0,Ge.useWatchedValueReadonly)({watchedValue:(0,g.ensureDefined)(t).symbol},!0),i=(0,Ge.useWatchedValueReadonly)({watchedValue:(0,g.ensureDefined)(t).width}),o=(0,Ge.useWatchedValueReadonly)({watchedValue:(0,g.ensureDefined)(t).visible}),a=function(e){const[t,l]=(0,n.useState)(e);return(0,n.useEffect)((()=>(window.loginStateChange.subscribe(null,i),()=>window.loginStateChange.unsubscribe(null,i))),[]),t;function i(){l(window.is_authenticated)}}(window.is_authenticated),[r,s]=function(e,t){const[l,i]=(0,n.useState)((()=>Xe(e))),[o,a]=(0,n.useState)((()=>je(e))),r=(0,n.useRef)({detailsHeaderProps:l,detailsInfoProps:o}),s=(0,n.useRef)(t);return s.current=t,(0,n.useEffect)((()=>{t&&(i(r.current.detailsHeaderProps),a(r.current.detailsInfoProps))}),[t]),(0,n.useEffect)((()=>{if(r.current.detailsInfoProps=je(e),r.current.detailsHeaderProps=Xe(e),s.current&&(i((()=>Xe(e))),a((()=>je(e)))),!e)return;const t=(0,qe.guid)(),l=(0,Ue.getQuoteSessionInstance)("full");return l.subscribe(t,e,(function(e){const{average_volume:t,last_price:l,open_price:n,low_price:o,high_price:u,ask:d,bid:c,pricescale:v,volume:m,short_name:g,minmov:p,minmove2:h,fractional:f,change:S,change_percent:y,current_session:_,exchange:P,pro_name:b,type:w,sector:C,industry:M,logoid:E,"currency-logoid":N,"base-currency-logoid":T,rtc:F,rtc_time:I,rch:R,rchp:k}=e.values,x={...r.current.detailsInfoProps,averageVolume:t,lastPrice:l,openPrice:n,lowPrice:o,highPrice:u,ask:d,bid:c,pricescale:v,volume:m,shortName:g,minMovePrimary:p,minMoveSecondary:h,fractional:f,change:S,changePercent:y,currentSession:_,exchange:P,proName:b,type:w,sector:C,industry:M,invalid:"ok"!==e.status,symbolDescription:(0,Ke.getTranslatedSymbolDescription)(e.values),rtcTime:I,extraHoursPrice:F,extraHoursChange:R,extraHoursChangePercent:k},A={...r.current.detailsHeaderProps,logoId:E,currencyLogoId:N,baseCurrencyLogoId:T,shortName:g};r.current.detailsInfoProps=x,r.current.detailsHeaderProps=A,s.current&&(i(A),a(x))})),()=>{
l.unsubscribe(t,e)}}),[e]),(0,n.useMemo)((()=>[l,o]),[l,o])}(l,o),u=Qe?Je&&a?useNewsPreviewStreamingProvider({symbol:s?.proName,type:s?.type}):useNewsPreviewProvider({symbol:s?.proName,type:s?.type}):null,c=d(s.symbol,Boolean(s.complete&&(u?.complete??!0)));return n.createElement(Ve,{detailsHeaderProps:r,detailsInfoProps:s,widgetWidth:i,newsPreview:u?.newsPreview||null,showNews:u?.showNews,visibility:c})}function $e(e){const{widgetHeaderElement:t,bridge:l,history:i,mindsFormState:o}=e,a=(0,n.useMemo)((()=>({header:t,bridge:l,mindsFormState:o})),[l,t,o]);return n.createElement(p.Provider,{value:a},n.createElement(Ze,null))}},306858:(e,t,l)=>{"use strict";l.d(t,{removeUsdFromCryptoPairLogos:()=>a,resolveLogoUrls:()=>o});var n=l(868333);const i=(0,n.getLogoUrlResolver)();function o(e,t=n.LogoSize.Medium){const l=e.logoid,o=e["base-currency-logoid"],a=e["currency-logoid"],r=l&&i.getSymbolLogoUrl(l,t);if(r)return[r];const s=o&&i.getSymbolLogoUrl(o,t),u=a&&i.getSymbolLogoUrl(a,t);return s&&u?[s,u]:s?[s]:u?[u]:[]}function a(e){return 2!==e.length?e:function(e){return e.some((e=>r(e)))}(e)&&!function(e){return e.some((e=>e.includes("country")&&!r(e)))}(e)?e.filter((e=>!r(e))):e}function r(e){return!1}},41192:(e,t,l)=>{"use strict";l.d(t,{getBlockStyleClasses:()=>n.getBlockStyleClasses,getLogoStyleClasses:()=>n.getLogoStyleClasses});var n=l(108937)},361701:(e,t,l)=>{"use strict";l.d(t,{CircleLogo:()=>r,hiddenCircleLogoClass:()=>a});var n=l(50959),i=l(185934),o=l(456057);const a=l.n(o)().hidden;function r(e){const t=(0,i.isCircleLogoWithUrlProps)(e),[l,o]=(0,n.useState)(0),a=(0,n.useRef)(null),r=(0,i.getStyleClasses)(e.size,l,e.className),s=e.alt??e.title??"",u=t?s[0]:e.placeholderLetter;return(0,n.useEffect)((()=>o(a.current?.complete??!t?2:1)),[t]),t&&3!==l?n.createElement("img",{ref:a,className:r,crossOrigin:"",src:e.logoUrl,alt:s,title:e.title,loading:e.loading,onLoad:()=>o(2),onError:()=>o(3),"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):n.createElement("span",{className:r,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},u)}},439067:(e,t,l)=>{"use strict";l.d(t,{getStyleClasses:()=>n.getStyleClasses});var n=l(185934)},840976:(e,t,l)=>{"use strict";l.d(t,{useEnsuredContext:()=>o});var n=l(50959),i=l(650151);function o(e){return(0,i.ensureNotNull)((0,n.useContext)(e))}},139043:(e,t,l)=>{"use strict";l.d(t,{useOverlayScroll:()=>s});var n=l(50959),i=l(650151),o=l(522224),a=l(601227);const r={onMouseOver:()=>{},onMouseOut:()=>{}};function s(e,t=a.CheckMobile.any()){const l=(0,n.useRef)(null),s=e||(0,n.useRef)(null),[u,d]=(0,o.useHover)(),[c,v]=(0,n.useState)({reference:l,containerHeight:0,containerWidth:0,contentHeight:0,contentWidth:0,scrollPosTop:0,scrollPosLeft:0,onVerticalChange:function(e){v((t=>({...t,scrollPosTop:e}))),(0,i.ensureNotNull)(s.current).scrollTop=e},onHorizontalChange:function(e){v((t=>({...t,scrollPosLeft:e}))),(0,i.ensureNotNull)(s.current).scrollLeft=e},visible:u}),m=(0,n.useCallback)((()=>{if(!s.current)return
;const{clientHeight:e,scrollHeight:t,scrollTop:n,clientWidth:i,scrollWidth:o,scrollLeft:a}=s.current,r=l.current?l.current.offsetTop:0;v((l=>({...l,containerHeight:e-r,contentHeight:t-r,scrollPosTop:n,containerWidth:i,contentWidth:o,scrollPosLeft:a})))}),[]);function g(){v((e=>({...e,scrollPosTop:(0,i.ensureNotNull)(s.current).scrollTop,scrollPosLeft:(0,i.ensureNotNull)(s.current).scrollLeft})))}return(0,n.useEffect)((()=>{u&&m(),v((e=>({...e,visible:u})))}),[u]),(0,n.useEffect)((()=>{const e=s.current;return e&&e.addEventListener("scroll",g),()=>{e&&e.removeEventListener("scroll",g)}}),[s]),[c,t?r:d,s,m]}},297265:(e,t,l)=>{"use strict";l.d(t,{useWatchedValueReadonly:()=>i});var n=l(50959);const i=(e,t=!1,l=[])=>{const i="watchedValue"in e?e.watchedValue:void 0,o="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[a,r]=(0,n.useState)(i?i.value():o);return(t?n.useLayoutEffect:n.useEffect)((()=>{if(i){r(i.value());const e=e=>r(e);return i.subscribe(e),()=>i.unsubscribe(e)}return()=>{}}),[i,...l]),a}},493173:(e,t,l)=>{"use strict";function n(e,t,l={}){return Object.assign({},e,function(e,t,l={}){const n=Object.assign({},t);for(const i of Object.keys(t)){const o=l[i]||i;o in e&&(n[i]=[e[o],t[i]].join(" "))}return n}(e,t,l))}l.d(t,{mergeThemes:()=>n})},57174:(e,t,l)=>{"use strict";l.d(t,{LastChangeFormatter:()=>o});var n=l(172912),i=l(459895);class o{constructor(e){const{priceScale:t,minMove:l,fractional:o,minMove2:a,variableMinTick:r,precision:s,format:u,ignoreLocaleNumberFormat:d}=e;this._formatter="volume"===u?new i.VolumeFormatter({precision:s??2,ignoreLocaleNumberFormat:d}):new n.PriceFormatter({priceScale:t,minMove:l,fractional:o,minMove2:a,variableMinTick:r||void 0,ignoreLocaleNumberFormat:d})}format(e,t={}){const{signPositive:l,tailSize:n,signNegative:i=!0,useRtlFormat:o=!0,ignoreLocaleNumberFormat:a}=t;return"price"===this._formatter.type?this._formatter.format(e,{signPositive:l,tailSize:n,signNegative:i,useRtlFormat:o,ignoreLocaleNumberFormat:a}):this._formatter.format(e,{signPositive:l,ignoreLocaleNumberFormat:a})}formatChange(e,t,l={}){const{signPositive:n,ignoreLocaleNumberFormat:i}=l;return"price"===this._formatter.type?this._formatter.formatChange(e,t,{signPositive:n,ignoreLocaleNumberFormat:i}):this._formatter.format(e-t,{signPositive:n,ignoreLocaleNumberFormat:i})}hasForexAdditionalPrecision(){return"hasForexAdditionalPrecision"in this._formatter&&this._formatter.hasForexAdditionalPrecision()}}},492315:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9 14A5 5 0 1 0 9 4a5 5 0 0 0 0 10Zm3-4H6V8h6v2Z"/></svg>'},553218:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><rect width="10" height="4" fill="currentColor" rx="2" x="4" y="7"/></svg>'},732140:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><circle fill="currentColor" cx="9" cy="9" r="4"/></svg>'},725230:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9.3 9l.9-4.53a1.23 1.23 0 1 0-2.4 0L8.7 9l-.9 4.53a1.23 1.23 0 1 0 2.4 0L9.3 9z"/><path fill="currentColor" d="M9.15 9.26l4.38-1.48a1.23 1.23 0 1 0-1.21-2.09L8.85 8.74l-4.38 1.48a1.23 1.23 0 1 0 1.21 2.09l3.47-3.05z"/><path fill="currentColor" d="M9.15 8.74L5.68 5.69a1.23 1.23 0 1 0-1.2 2.09l4.37 1.48 3.47 3.05a1.23 1.23 0 1 0 1.2-2.09L9.16 8.74z"/></svg>'},315507:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12.57 5.5h-.07a3.5 3.5 0 1 0 .07 7A4.98 4.98 0 0 1 4 9a5 5 0 0 1 8.57-3.5z"/></svg>'},85290:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12.22 11.78A3.47 3.47 0 0 0 9 6.98a3.48 3.48 0 0 0-3.22 4.8 6.97 6.97 0 0 1 6.44 0zM4.18 9.83L2.1 9.28l.33-1.24 2.07.55-.33 1.24zM6.38 6.36l-.9-1.94 1.16-.54.9 1.94-1.16.54zM10.46 5.82l.9-1.94 1.16.54-.9 1.94-1.16-.54zM13.49 8.6l2.07-.56.33 1.24-2.07.55-.33-1.24z"/></svg>'},518283:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 8" width="12" height="8" fill="none"><path fill="currentColor" d="M10 8H2a1 1 0 0 1-.8-1.6l4-5.33a1 1 0 0 1 1.6 0l4 5.33A1 1 0 0 1 10 8z"/></svg>'}}]);