"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[5283],{510179:(e,t,r)=>{r.r(t),r.d(t,{LineToolABCD:()=>a});var n=r(650151),i=r(792535),o=r(889868),s=r(981856);class a extends o.LineDataSource{constructor(e,t,n,i){super(e,t??a.createProperties(e.backgroundTheme().spawnOwnership()),n,i),Promise.all([r.e(6290),r.e(6881),r.e(5579),r.e(1583)]).then(r.bind(r,934666)).then((t=>{this._setPaneViews([new t.ABCDPaneView(this,e)])}))}pointsCount(){return 4}name(){return"ABCD Pattern"}static createProperties(e,t){const r=new i.DefaultProperty({defaultName:"linetoolabcd",state:t,theme:e});return this._configureProperties(r),r}_getPropertyDefinitionsViewModelClass(){return Promise.all([r.e(6406),r.e(8511),r.e(5234),r.e(4590),r.e(8537)]).then(r.bind(r,897384)).then((e=>e.PatternWithoutBackgroundDefinitionsViewModel))}static _configureProperties(e){super._configureProperties(e),e.addChild("linesColors",new s.LineToolColorsProperty([(0,n.ensureDefined)(e.child("color"))])),e.addChild("textsColors",new s.LineToolColorsProperty([(0,n.ensureDefined)(e.child("textcolor"))]))}}}}]);