(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[8179],{247465:(e,t,n)=>{"use strict";n.d(t,{isCancelled:()=>r,makeCancelable:()=>a});class i extends Error{constructor(){super("CancelToken")}}function a(e){let t=!1;return{promise:new Promise(((n,a)=>{e.then((e=>t?a(new i):n(e))),e.catch((e=>a(t?new i:e)))})),cancel(){t=!0}}}function r(e){return e instanceof i}},865266:(e,t,n)=>{"use strict";n.d(t,{useRovingTabindexElement:()=>r});var i=n(50959),a=n(718736);function r(e,t=[]){const[n,r]=(0,i.useState)(!1),o=(0,a.useFunctionalRefObject)(e);return(0,i.useLayoutEffect)((()=>{const e=o.current;if(null===e)return;const t=e=>{switch(e.type){case"roving-tabindex:main-element":r(!0);break;case"roving-tabindex:secondary-element":r(!1)}};return e.addEventListener("roving-tabindex:main-element",t),e.addEventListener("roving-tabindex:secondary-element",t),()=>{e.removeEventListener("roving-tabindex:main-element",t),e.removeEventListener("roving-tabindex:secondary-element",t)}}),t),[o,n?0:-1]}},294264:e=>{e.exports={container:"container-RoCcHn9S",active:"active-RoCcHn9S",title:"title-RoCcHn9S",inactive:"inactive-RoCcHn9S",titleText:"titleText-RoCcHn9S",indicator:"indicator-RoCcHn9S",disconnected:"disconnected-RoCcHn9S",failed:"failed-RoCcHn9S",connecting:"connecting-RoCcHn9S",connected:"connected-RoCcHn9S",loginTooltip:"loginTooltip-RoCcHn9S"}},497876:e=>{e.exports={tab:"tab-jJ_D7IlA",accessible:"accessible-jJ_D7IlA"}},469038:e=>{e.exports={wrapper:"wrapper-mXzF5cTO",button:"button-mXzF5cTO"}},259706:(e,t,n)=>{"use strict";n.r(t),n.d(t,{getTerminalConfigSet:()=>y});var i=n(609838);const a={paper_trading:{name:"paper_trading",title:i.t(null,void 0,n(666803)),buttonOpenTooltip:i.t(null,void 0,n(713935)),buttonCloseTooltip:i.t(null,void 0,n(124939)),ctor:null,_gaEvent:"Trading Panel"},scripteditor:{name:"scripteditor",title:i.t(null,void 0,n(76324)),buttonOpenTooltip:i.t(null,void 0,n(580067)),buttonCloseTooltip:i.t(null,void 0,n(3111)),ctor:null,_gaEvent:"Pine Editor"},backtesting:{name:"backtesting",title:i.t(null,void 0,n(435155)),buttonOpenTooltip:i.t(null,void 0,n(248686)),buttonCloseTooltip:i.t(null,void 0,n(70046)),ctor:null,_gaEvent:"Strategy Tester"},screener:{name:"screener",title:i.t(null,void 0,n(696609)),buttonOpenTooltip:i.t(null,void 0,n(54391)),buttonCloseTooltip:i.t(null,void 0,n(540474)),ctor:null,_gaEvent:"Screener"},replay_trading:{name:"replay_trading",title:i.t(null,void 0,n(169623)),buttonOpenTooltip:i.t(null,void 0,n(419912)),buttonCloseTooltip:i.t(null,void 0,n(161307)),ctor:null,_gaEvent:"Replay Trading Panel"}};var r=n(650151),o=n(344557),s=(n(599016),n(739390));class l{constructor(e){this._contentRenderer=Promise.resolve(),this._spinnerContainer=document.createElement("div"),this._offlineScreenContainer=document.createElement("div"),this._renderer=Promise.resolve(),this._bridge=e,(0,o.waitTradingService)().then((e=>{this._trading=e,this.onStatusChange(e.connectStatus()),this._trading.onConnectionStatusChange.subscribe(this,this.onStatusChange)})),
Promise.all([n.e(9325),n.e(7102)]).then(n.bind(n,974063)).then((e=>{e.render(this._spinnerContainer)})),(0,s.renderOfflineScreen)((0,r.ensureDefined)(this._offlineScreenContainer))}activate(){this._contentRenderer.then((()=>{this._content&&this._content.drawAttention&&this._content.drawAttention()}))}onStatusChange(e,t){this._connectStatus!==e&&(this._connectStatus=this._trading.connectStatus(),window.navigator.onLine?(this._contentRenderer=Promise.resolve(),this._content&&this._content.remove(),2===e&&this._renderSpinner(),3!==e&&4!==e?1===e&&this._renderAccountManager(this._trading.activeBroker()):this._renderSpinner()):this._renderOfflineScreen())}_renderSpinner(){this._bridge.container.innerText="",this._content=this._bridge.container.appendChild(this._spinnerContainer)}_renderOfflineScreen(){this._bridge.container.innerText="",this._content=this._bridge.container.appendChild(this._offlineScreenContainer)}async _createAccountManager(e){const{AccountManager:t}=await Promise.all([n.e(8255),n.e(3703),n.e(3953),n.e(4752),n.e(2520),n.e(5826),n.e(9481),n.e(4600),n.e(7384),n.e(3799),n.e(5323),n.e(8985),n.e(5621),n.e(7849),n.e(5083),n.e(9642),n.e(8473),n.e(2234),n.e(4797),n.e(3828),n.e(6243),n.e(7414),n.e(4938),n.e(1249),n.e(7280),n.e(1707),n.e(9704),n.e(5299),n.e(3251),n.e(2433),n.e(6050),n.e(5476),n.e(1191),n.e(427),n.e(8354)]).then(n.bind(n,399890)),{SummaryFieldsVisibilityManager:i}=await Promise.all([n.e(8255),n.e(3703),n.e(3953),n.e(4752),n.e(2520),n.e(5826),n.e(9481),n.e(4600),n.e(7384),n.e(3799),n.e(5323),n.e(8985),n.e(5621),n.e(7849),n.e(5083),n.e(9642),n.e(8473),n.e(2234),n.e(4797),n.e(3828),n.e(6243),n.e(7414),n.e(4938),n.e(1249),n.e(7280),n.e(1707),n.e(9704),n.e(5299),n.e(3251),n.e(2433),n.e(6050),n.e(5476),n.e(1191),n.e(427),n.e(8354)]).then(n.bind(n,332510));if(this._contentRenderer!==this._renderer)return;this._content&&this._content.remove();const a=new i((0,r.ensureNotNull)(e).accountManagerInfo().summary,this._trading.getBrokerTradingSettingsStorage);this._content=await t.create({broker:e,bridge:this._bridge,mode:1,summaryFieldsVisibilityManager:a})}_renderAccountManager(e){this._renderer=this._contentRenderer=this._createAccountManager(e)}_renderBrokerSelectScreen(){0}}var c=n(50959),d=n(497754),u=n.n(d),h=n(440891),g=n(247465),p=n(391431),v=n(14517),b=n(28033),_=n(898237),m=n(865266),C=n(469038);const T=i.t(null,void 0,n(366823));function S(e){const{onClick:t,onWidthChange:a,tradeButtonWidth:r,showHint:o,isActive:s}=e,[,l]=(0,m.useRovingTabindexElement)(null),d=(0,c.useRef)(null);return(0,c.useEffect)((()=>{a(d.current?.offsetWidth??0)}),[]),c.createElement("div",{className:C.wrapper,ref:d},c.createElement(_.LightButton,{size:"xsmall",color:"gray",onClick:t,variant:s?"primary":"secondary",title:i.t(null,void 0,n(319133)),className:u()("apply-common-tooltip",C.button),"area-label":i.t(null,void 0,n(319133)),tabIndex:l,"data-name":"trade-panel-button"},T,!1))}var f=n(294264);const k=i.t(null,void 0,n(332668)),E={4:"failed",2:"connecting",1:"connected",3:"disconnected"},w={4:i.t(null,void 0,n(935284)),
2:i.t(null,void 0,n(366891)),1:i.t(null,void 0,n(618305)),3:i.t(null,void 0,n(551772))};class A extends c.PureComponent{constructor(e){super(e),this._trading=null,this._tradingServiceCancelable=null,this._isTradingButtonsAvailable=(0,b.checkIsDOMAvailable)()||h.enabled("order_panel"),this._titleClick=()=>{const{onClick:e}=this.props;e&&e()},this._update=()=>{const e=this._trading,t=e&&e.activeBroker(),n=e?e.connectStatus():3,i=null!==e,a=!!i&&e.tradingPanel.isOpeningAvailable.value(),r=!!i&&e.tradingPanel.isOpened.value(),o=window.is_authenticated&&i&&null!==t&&1===n;this.setState({status:n,hasActiveBroker:o,title:o?t.accountManagerInfo().accountTitle:k,isOpeningTradingPanelAvailable:a,isTradingPanelOpened:r})},this._handleClickTradeButton=()=>{null!==this._trading&&this._trading.toggleTradingPanelVisibility()},this.state={status:3,hasActiveBroker:!1,title:k,isOpeningTradingPanelAvailable:!1,isTradingPanelOpened:!1,tradeButtonWidth:0}}componentDidMount(){const e=(0,o.tradingService)();null===e?(this._tradingServiceCancelable=(0,g.makeCancelable)((0,o.waitTradingService)()),this._tradingServiceCancelable.promise.then(this._onTradingService.bind(this))):this._onTradingService(e)}componentWillUnmount(){this._cancelWaitingTrading(),null!==this._trading&&(this._trading.onBrokerChange.unsubscribe(this,this._update),this._trading.onConnectionStatusChange.unsubscribe(this,this._update),this._trading.tradingPanel.isOpeningAvailable.unsubscribe(this._update),this._trading.tradingPanel.isOpened.unsubscribe(this._update))}render(){const{status:e,hasActiveBroker:t,title:n,tradeButtonWidth:i,isOpeningTradingPanelAvailable:a}=this.state,r=(1===e||t)&&this._isTradingButtonsAvailable&&a,o={"--trade-button-width":r?i:0};return c.createElement(c.Fragment,null,c.createElement(v.FooterToolbarTab,{style:o,className:d(p.bottomTradingTabClassName,this.props.tooltip&&"apply-common-tooltip",f.container,f[E[e]],{[f.active]:this.props.isActive,[f.inactive]:!t}),onClick:this._titleClick,"data-name":this.props.dataName,"data-active":this.props.isActive,"aria-pressed":this.props.isActive,tooltip:this.props.tooltip},c.createElement("span",{className:f.title},c.createElement("span",{className:f.titleText},n)),c.createElement("div",{className:d(f.indicator,"apply-common-tooltip"),title:w[e]})),r&&c.createElement(S,{onClick:this._handleClickTradeButton,tradeButtonWidth:i,onWidthChange:e=>this.setState({...this.state,tradeButtonWidth:e}),showHint:void 0!==this.props.dataName,isActive:this.state.isTradingPanelOpened}))}_onTradingService(e){this._cancelWaitingTrading(),this._trading=e,e.onBrokerChange.subscribe(this,this._update),e.onConnectionStatusChange.subscribe(this,this._update),e.tradingPanel.isOpeningAvailable.subscribe(this._update),e.tradingPanel.isOpened.subscribe(this._update),this._update()}_cancelWaitingTrading(){null!==this._tradingServiceCancelable&&(this._tradingServiceCancelable.cancel(),this._tradingServiceCancelable=null)}}var O=n(41899);function y(){const e={paper_trading:{ctor:l,customTitleComponent:A,
buttonOpenTooltip:i.t(null,void 0,n(951065)),buttonCloseTooltip:i.t(null,void 0,n(287902))}};return(0,O.merge)((0,O.clone)(a),e)}},14517:(e,t,n)=>{"use strict";n.d(t,{FooterToolbarTab:()=>l});var i=n(50959),a=n(497754),r=n.n(a),o=n(865266),s=n(497876);function l(e){const{tooltip:t,children:n,className:a,...l}=e,[c,d]=(0,o.useRovingTabindexElement)(null);return i.createElement("button",{"aria-label":t,...l,ref:c,tabIndex:d,type:"button","data-tooltip":t,className:r()(s.tab,s.accessible,a)},n)}}}]);