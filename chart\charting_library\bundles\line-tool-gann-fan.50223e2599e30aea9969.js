"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4981],{959107:(e,t,s)=>{s.d(t,{LevelsProperty:()=>v});var r=s(154834),n=s(916738),i=s(41899),a=s(792535),l=s(851068);const o={prefixes:[""],range:[0,0],names:["coeff","color","visible","linestyle","linewidth"],typecheck:{pack:()=>Object(),unpack:()=>[]}};function c(e,t,s,r){return r.push(s[t]),r}function u(e,t,s,r){return r[t]=s[e],r}function p(){return[]}function h(){return{}}function f(e,t,s){return s.prefixes.forEach((r=>{const n=r+"level";for(let r=s.range[0];r<=s.range[1];r++)if(e[n+r]&&(0,i.isSameType)(e[n+r],t.typecheck())){let i=t.tpl();s.names.forEach(((s,a)=>{i=t.fill(""+a,s,e[n+r],i)})),e[n+r]=i}})),e}function d(e,t,s){return s(e,{tpl:h,fill:u,typecheck:t.typecheck.unpack},t)}class v extends a.DefaultProperty{constructor(e){const{levelsIterator:t=f,map:s={},...r}=e,n={...o,...s};r.state&&(r.state=d(r.state,n,t)),super(r),this._map=n,this._levelsIterator=t}state(e,t,s){const r=super.state(e,t);return s?r:(n=r,i=this._map,(0,this._levelsIterator)(n,{tpl:p,fill:c,typecheck:i.typecheck.pack},i));var n,i}preferences(){return(0,a.extractState)(this.state(this._excludedDefaultsKeys,void 0,!0),this._allDefaultsKeys)}applyTemplate(e,t){this.mergeAndFire((0,a.extractState)((0,n.default)((0,r.default)(t),d(e,this._map,this._levelsIterator)),this._allStateKeys,this._excludedTemplateKeys))}saveDefaults(){this._useUserPreferences&&(0,l.saveDefaults)(this._defaultName,this.preferences())}clone(){return new v(this._options())}merge(e,t){return super.merge(this._map?d(e,this._map,this._levelsIterator):e,t)}_options(){return{...super._options(),map:{...this._map},levelsIterator:this._levelsIterator}}}},363676:(e,t,s)=>{s.r(t),s.d(t,{LineToolGannFan:()=>h});var r,n=s(650151),i=s(609838),a=s(959107),l=s(272047),o=s(979910),c=s(981856),u=s(889868);!function(e){e[e.LevelsCount=9]="LevelsCount"}(r||(r={}));const p=new l.TranslatedString("erase level line",i.t(null,void 0,s(77114)));class h extends u.LineDataSource{constructor(e,t,r,n){super(e,t??h.createProperties(e.backgroundTheme().spawnOwnership()),r,n),Promise.all([s.e(6290),s.e(6881),s.e(5579),s.e(1583)]).then(s.bind(s,708192)).then((({GannFanPaneView:e})=>{this._setPaneViews([new e(this,this._model)])}))}levelsCount(){return 9}pointsCount(){return 2}name(){return"Gann Fan"}processErase(e,t){const s="level"+t,r=(0,n.ensureDefined)(this.properties().child(s)).childs().visible;e.setProperty(r,!1,p,o.lineToolsDoNotAffectChartInvalidation)}static createProperties(e,t){const s=new a.LevelsProperty({defaultName:"linetoolgannfan",state:t,map:{range:[1,9]},theme:e});return this._configureProperties(s),s}async _getPropertyDefinitionsViewModelClass(){return(await Promise.all([s.e(6406),s.e(8511),s.e(5234),s.e(4590),s.e(8537)]).then(s.bind(s,690079))).GannFanDefinitionsViewModel}static _configureProperties(e){super._configureProperties(e);const t=[],s=[];for(let r=1;r<=9;r++){const i=(0,n.ensureDefined)(e.child("level"+r));t.push((0,n.ensureDefined)(i.child("linewidth"))),s.push((0,
n.ensureDefined)(i.child("color")))}e.addChild("linesColors",new c.LineToolColorsProperty(s)),e.addChild("linesWidths",new c.LineToolWidthsProperty(t))}}}}]);