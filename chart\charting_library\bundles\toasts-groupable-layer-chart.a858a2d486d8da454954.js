(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4604],{497754:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var l=typeof n;if("string"===l||"number"===l)e.push(n);else if(Array.isArray(n)&&n.length){var a=o.apply(null,n);a&&e.push(a)}else if("object"===l)for(var s in n)r.call(n,s)&&n[s]&&e.push(s)}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},262453:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},966784:e=>{e.exports={link:"link-b4qVahJC"}},821904:e=>{e.exports={roundButtonColor:"roundButtonColor-xCeOT7of",disableThemes:"disableThemes-xCeOT7of",activated:"activated-xCeOT7of",brand:"brand-xCeOT7of",primary:"primary-xCeOT7of","quiet-primary":"quiet-primary-xCeOT7of",secondary:"secondary-xCeOT7of",ghost:"ghost-xCeOT7of",gray:"gray-xCeOT7of",red:"red-xCeOT7of",black:"black-xCeOT7of",invertedblack:"invertedblack-xCeOT7of",animated:"animated-xCeOT7of"}},106935:e=>{e.exports={xsmall:"xsmall-FujgyDpN",small:"small-FujgyDpN",medium:"medium-FujgyDpN",large:"large-FujgyDpN",xlarge:"xlarge-FujgyDpN",xxlarge:"xxlarge-FujgyDpN",roundButton:"roundButton-FujgyDpN",iconOnly:"iconOnly-FujgyDpN",startSlot:"startSlot-FujgyDpN",endSlot:"endSlot-FujgyDpN",withStartSlot:"withStartSlot-FujgyDpN",withEndSlot:"withEndSlot-FujgyDpN",slot:"slot-FujgyDpN",animated:"animated-FujgyDpN",stretch:"stretch-FujgyDpN",content:"content-FujgyDpN"}},78217:e=>{e.exports={pair:"pair-ocURKVwI",xxceptionallysmalldonotusebrv1023:"xxceptionallysmalldonotusebrv1023-ocURKVwI",xxxxsmall:"xxxxsmall-ocURKVwI",xxxsmall:"xxxsmall-ocURKVwI",xxsmall:"xxsmall-ocURKVwI",xsmall:"xsmall-ocURKVwI",small:"small-ocURKVwI",medium:"medium-ocURKVwI",large:"large-ocURKVwI",xlarge:"xlarge-ocURKVwI",xxlarge:"xxlarge-ocURKVwI",xxxlarge:"xxxlarge-ocURKVwI",logo:"logo-ocURKVwI",skeleton:"skeleton-ocURKVwI",empty:"empty-ocURKVwI"}},291011:e=>{e.exports={counter:"counter-napy2vLF","size-xxxsmall":"size-xxxsmall-napy2vLF","size-xxsmall":"size-xxsmall-napy2vLF","size-xsmall":"size-xsmall-napy2vLF","size-small":"size-small-napy2vLF","size-medium":"size-medium-napy2vLF","size-large":"size-large-napy2vLF","size-xlarge":"size-xlarge-napy2vLF","color-danger":"color-danger-napy2vLF","color-accent":"color-accent-napy2vLF","color-accent-light":"color-accent-light-napy2vLF","color-neutral-bold":"color-neutral-bold-napy2vLF","color-neutral":"color-neutral-napy2vLF","borderColor-primary":"borderColor-primary-napy2vLF","borderColor-secondary":"borderColor-secondary-napy2vLF","borderColor-tertiary":"borderColor-tertiary-napy2vLF"}
},55679:e=>{e.exports={wrapper:"wrapper-TJ9ObuLF",animated:"animated-TJ9ObuLF",pulsation:"pulsation-TJ9ObuLF"}},519722:e=>{e.exports={"tag-icon":"tag-icon-Or4nqEjj",small:"small-Or4nqEjj",xsmall:"xsmall-Or4nqEjj",xxsmall:"xxsmall-Or4nqEjj",xxxsmall:"xxxsmall-Or4nqEjj",square:"square-Or4nqEjj",iconPair:"iconPair-Or4nqEjj"}},40415:e=>{e.exports={"tag-text":"tag-text-rVj4hiuX",small:"small-rVj4hiuX",xsmall:"xsmall-rVj4hiuX",xxsmall:"xxsmall-rVj4hiuX",xxxsmall:"xxxsmall-rVj4hiuX"}},324184:e=>{e.exports={tag:"tag-zVPYJd3B",interactive:"interactive-zVPYJd3B",selected:"selected-zVPYJd3B",error:"error-zVPYJd3B",small:"small-zVPYJd3B",square:"square-zVPYJd3B",xsmall:"xsmall-zVPYJd3B",xxsmall:"xxsmall-zVPYJd3B",xxxsmall:"xxxsmall-zVPYJd3B",stretch:"stretch-zVPYJd3B"}},16891:e=>{e.exports={toastCommon:"toastCommon-ZZzgDlel",swipable:"swipable-ZZzgDlel",iconContainer:"iconContainer-ZZzgDlel",icon:"icon-ZZzgDlel",contentContainerWrapper:"contentContainerWrapper-ZZzgDlel",contentContainerInner:"contentContainerInner-ZZzgDlel",contentContainer:"contentContainer-ZZzgDlel",topContent:"topContent-ZZzgDlel",header:"header-ZZzgDlel",closeButton:"closeButton-ZZzgDlel"}},338986:e=>{e.exports={toastGroup:"toastGroup-JUpQSPBo",controls:"controls-JUpQSPBo",controlsHidden:"controlsHidden-JUpQSPBo",list:"list-JUpQSPBo",listEmplty:"listEmplty-JUpQSPBo",item:"item-JUpQSPBo",itemAdded:"itemAdded-JUpQSPBo",itemRemoved:"itemRemoved-JUpQSPBo",itemStable:"itemStable-JUpQSPBo",itemInner:"itemInner-JUpQSPBo",itemInnerInner:"itemInnerInner-JUpQSPBo",listCollapsed:"listCollapsed-JUpQSPBo"}},680653:e=>{e.exports={toastList:"toastList-Hvz5Irky",toastListScroll:"toastListScroll-Hvz5Irky",notEmpty:"notEmpty-Hvz5Irky",toastListInner:"toastListInner-Hvz5Irky"}},638353:e=>{e.exports={iconColorBuy:"iconColorBuy-MMDBBz2U",iconColorSell:"iconColorSell-MMDBBz2U",iconColorSuccess:"iconColorSuccess-MMDBBz2U",iconColorWarning:"iconColorWarning-MMDBBz2U",iconColorCancel:"iconColorCancel-MMDBBz2U",tag:"tag-MMDBBz2U",orderInfo:"orderInfo-MMDBBz2U",buyOrderInfo:"buyOrderInfo-MMDBBz2U",sellOrderInfo:"sellOrderInfo-MMDBBz2U",content:"content-MMDBBz2U"}},739050:e=>{e.exports={iconColorSuccess:"iconColorSuccess-fFcr0x3b",iconColorWarning:"iconColorWarning-fFcr0x3b",iconColorInfo:"iconColorInfo-fFcr0x3b"}},565865:e=>{e.exports={toastLayerChart:"toastLayerChart-h0NSCjCQ"}},959189:(e,t,n)=>{"use strict";function r(e,t){return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}n.d(t,{isIconOnly:()=>r})},270762:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var r=n(50959),o=n(497754),l=n(878112),a=(n(15378),n(262453));function s(e){const{size:t="large",preservePaddings:n,isLink:r,flipIconOnRtl:l,className:s}=e;return o(a["nav-button"],a[`size-${t}`],n&&a["preserve-paddings"],l&&a["flip-icon"],r&&a.link,s)}function i(e){const{children:t,icon:n}=e;return r.createElement(r.Fragment,null,r.createElement("span",{className:a.background}),r.createElement(l.Icon,{icon:n,className:a.icon,"aria-hidden":!0}),t&&r.createElement("span",{className:a["visually-hidden"]},t))}
const c=(0,r.forwardRef)(((e,t)=>{const{icon:n,type:o="button",preservePaddings:l,flipIconOnRtl:a,size:c,"aria-label":u,...d}=e;return r.createElement("button",{...d,className:s({...e,children:u}),ref:t,type:o},r.createElement(i,{icon:n},u))}));c.displayName="NavButton";var u=n(591365),d=n(273388);(0,r.forwardRef)(((e,t)=>{const{icon:n,renderComponent:o,"aria-label":l,...a}=e,c=o??u.CustomComponentDefaultLink;return r.createElement(c,{...a,className:s({...e,children:l,isLink:!0}),reference:(0,d.isomorphicRef)(t)},r.createElement(i,{icon:n},l))})).displayName="NavAnchorButton"},373989:(e,t,n)=>{"use strict";function r(e){return"brand"===e?"black":"blue"===e?"brand":e}n.d(t,{renameColors:()=>r})},15378:(e,t,n)=>{"use strict";var r,o,l,a;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(r||(r={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(o||(o={})),function(e){e.Brand="brand",e.Blue="blue",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(l||(l={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(a||(a={}))},72903:(e,t,n)=>{"use strict";n.d(t,{CircleLogoPair:()=>l});var r=n(50959),o=n(108937);function l(e){const{primaryLogoUrl:t,secondaryLogoUrl:n,size:l,className:s}=e;return r.createElement("span",{className:(0,o.getBlockStyleClasses)(l,s),title:e.title},r.createElement(a,{logoUrl:n,size:l}),r.createElement(a,{logoUrl:t,size:l}))}function a(e){const{logoUrl:t,size:n}=e,l=void 0!==t,[a,s]=(0,r.useState)(0),i=(0,r.useRef)(null),c=(0,o.getLogoStyleClasses)(n,a);return(0,r.useEffect)((()=>s(i.current?.complete??!l?2:1)),[l]),l&&3!==a?r.createElement("img",{ref:i,className:c,crossOrigin:"",src:t,alt:"",onLoad:()=>s(2),onError:()=>s(3)}):r.createElement("span",{className:c})}},108937:(e,t,n)=>{"use strict";n.d(t,{getBlockStyleClasses:()=>s,getLogoStyleClasses:()=>i});var r=n(497754),o=n(92318),l=n(78217),a=n.n(l);function s(e,t){return r(a().pair,a()[e],t)}function i(e,t=2,n=!0){return r(a().logo,a()[e],a().skeleton,o.skeletonTheme.wrapper,!n&&a().empty,1===t&&r(o.skeletonTheme.animated))}},409245:(e,t,n)=>{"use strict";function r(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>r})},591365:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>l});var r=n(50959),o=n(409245);function l(e){return r.createElement("a",{...(0,o.renameRef)(e)})}r.PureComponent},732219:(e,t,n)=>{"use strict";n.d(t,{Counter:()=>d});var r,o,l,a=n(497754),s=n.n(a),i=n(50959),c=n(291011),u=n.n(c);function d(e){const{className:t,count:n,compact:r=!0,size:o="xlarge",color:l="danger",sign:a,borderColor:c="none","aria-label":d,"aria-hidden":m,...p}=e,g=p;let y;y=!("xxsmall"===o||"xxxsmall"===o)&&n?r&&n>=100?"99+":a&&n>0?`+${n}`:n:""
;const f=s()(t,u().counter,u()[`size-${o}`],u()[`color-${l}`],"none"!==c&&u()[`borderColor-${c}`]);return i.createElement("span",{...g,className:f,"aria-label":d,"aria-hidden":m},y)}!function(e){e.XXXSmall="xxxsmall",e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge"}(r||(r={})),function(e){e.Danger="danger",e.Accent="accent",e.AccentLight="accent-light",e.NeutralBold="neutral-bold",e.Neutral="neutral"}(o||(o={})),function(e){e.Primary="primary",e.Secondary="secondary",e.Tertiary="tertiary",e.None="none"}(l||(l={}))},718736:(e,t,n)=>{"use strict";n.d(t,{useFunctionalRefObject:()=>l});var r=n(50959),o=n(855393);function l(e){const t=(0,r.useMemo)((()=>function(e){const t=n=>{e(n),t.current=n};return t.current=null,t}((e=>{s.current(e)}))),[]),n=(0,r.useRef)(null),l=t=>{if(null===t)return a(n.current,t),void(n.current=null);n.current!==e&&(n.current=e,a(n.current,t))},s=(0,r.useRef)(l);return s.current=l,(0,o.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return s.current(t.current),()=>s.current(null)}),[e]),t}function a(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},855393:(e,t,n)=>{"use strict";n.d(t,{useIsomorphicLayoutEffect:()=>o});var r=n(50959);function o(e,t){("undefined"==typeof window?r.useEffect:r.useLayoutEffect)(e,t)}},139784:(e,t,n)=>{"use strict";n.d(t,{useTooltip:()=>s});var r=n(50959),o=n(799573);var l=n(718736);const a=200;function s(e,t=null){const{showTooltip:n,hideTooltip:s,onClick:i,doNotShowTooltipOnTouch:c=!1}=e,u=(0,l.useFunctionalRefObject)(t),d=function(){const[e,t]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{t(o.mobiletouch)}),[]),e}(),m=d&&c?void 0:e.tooltip;(0,r.useEffect)((()=>{const e=()=>s&&s();return document.addEventListener("scroll",e,!0),()=>document.removeEventListener("scroll",e,!0)}),[u,s]);return{onBlur:(0,r.useCallback)((e=>{s&&s()}),[s]),onFocus:(0,r.useCallback)((e=>{!e.target.matches(":hover")&&n&&e.target.matches(":focus-visible")&&n(e.currentTarget,{tooltipDelay:a})}),[n]),onClick:(0,r.useCallback)((e=>{d&&u?.current?.focus(),i&&i(e)}),[i,u,d]),tooltip:m,className:void 0!==m?"apply-common-tooltip":void 0,ref:u}}},183787:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>o});var r=n(50959);const o=r.forwardRef(((e,t)=>{const{icon:n="",title:o,ariaLabel:l,ariaLabelledby:a,ariaHidden:s,...i}=e,c=!!(o||l||a);return r.createElement("span",{role:"img",...i,ref:t,"aria-label":l,"aria-labelledby":a,"aria-hidden":s||!c,title:o,dangerouslySetInnerHTML:{__html:n}})}))},878112:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>r.Icon});var r=n(183787)},92318:(e,t,n)=>{"use strict";n.d(t,{skeletonTheme:()=>o});var r=n(55679);const o=r},800417:(e,t,n)=>{"use strict";function r(e){return l(e,a)}function o(e){return l(e,s)}function l(e,t){const n=Object.entries(e).filter(t),r={};for(const[e,t]of n)r[e]=t;return r}function a(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function s(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>o,filterDataProps:()=>r,filterProps:()=>l,isAriaAttribute:()=>s,
isDataAttribute:()=>a})},273388:(e,t,n)=>{"use strict";function r(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function o(e){return r([e])}n.d(t,{isomorphicRef:()=>o,mergeRefs:()=>r})},801808:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>a,getRootOverlapManager:()=>i});var r=n(650151),o=n(481564);class l{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class a{constructor(e=document){this._storage=new l,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e);if(void 0!==n)return n;this.registerWindow(e);const r=this._document.createElement("div");if(r.style.position=t.position,r.style.zIndex=this._index.toString(),r.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(r);else if(t.index<=0)this._container.insertBefore(r,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(r,e)}}else"reverse"===t.direction?this._container.insertBefore(r,this._container.firstChild):this._container.appendChild(r);return this._windows.set(e,r),++this._index,r}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveLastWindowToTop(){const e=this._storage.getItems(),t=e[e.length-1];t&&this.moveToTop(t)}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,n)=>{e.hasAttribute(o.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(o.FOCUS_TRAP_DATA_ATTRIBUTE,e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const s=new WeakMap;function i(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,r.ensureDefined)(s.get(t));{const t=new a(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return s.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}var c;!function(e){e[e.BaseZindex=150]="BaseZindex"}(c||(c={}))},586816:(e,t,n)=>{"use strict";n.d(t,{ORDERS_GROUP_ID:()=>r});const r="orders"},242843:(e,t,n)=>{"use strict";n.d(t,{getHeadingText:()=>c,getOrderInfoTexts:()=>u});var r=n(609838),o=n(46415),l=n(989546);const a={0:e=>r.t(null,{replace:{symbol:e}},n(877889)),5:e=>r.t(null,{replace:{symbol:e}},n(735055)),
3:e=>r.t(null,{replace:{symbol:e}},n(184536)),2:e=>r.t(null,{replace:{symbol:e}},n(425034)),1:e=>r.t(null,{replace:{symbol:e}},n(107364)),4:e=>r.t(null,{replace:{symbol:e}},n(978630))},s={[o.StopType.StopLoss]:{0:e=>r.t(null,{replace:{symbol:e}},n(533423)),5:e=>r.t(null,{replace:{symbol:e}},n(256969)),3:e=>r.t(null,{replace:{symbol:e}},n(465730)),2:e=>r.t(null,{replace:{symbol:e}},n(141089)),1:e=>r.t(null,{replace:{symbol:e}},n(25141)),4:e=>r.t(null,{replace:{symbol:e}},n(258578))},[o.StopType.TrailingStop]:{0:e=>r.t(null,{replace:{symbol:e}},n(581861)),5:e=>r.t(null,{replace:{symbol:e}},n(543655)),3:e=>r.t(null,{replace:{symbol:e}},n(490645)),2:e=>r.t(null,{replace:{symbol:e}},n(107497)),1:e=>r.t(null,{replace:{symbol:e}},n(538030)),4:e=>r.t(null,{replace:{symbol:e}},n(428941))},[o.StopType.GuaranteedStop]:{0:e=>r.t(null,{replace:{symbol:e}},n(131629)),5:e=>r.t(null,{replace:{symbol:e}},n(728481)),3:e=>r.t(null,{replace:{symbol:e}},n(779598)),2:e=>r.t(null,{replace:{symbol:e}},n(888894)),1:e=>r.t(null,{replace:{symbol:e}},n(666391)),4:e=>r.t(null,{replace:{symbol:e}},n(508106))}},i={1:{0:e=>r.t(null,{replace:{symbol:e}},n(900493)),5:e=>r.t(null,{replace:{symbol:e}},n(586246)),3:e=>r.t(null,{replace:{symbol:e}},n(133480)),2:e=>r.t(null,{replace:{symbol:e}},n(570694)),1:e=>r.t(null,{replace:{symbol:e}},n(743087)),4:e=>r.t(null,{replace:{symbol:e}},n(804352))},2:{0:e=>r.t(null,{replace:{symbol:e}},n(649642)),5:e=>r.t(null,{replace:{symbol:e}},n(924681)),3:e=>r.t(null,{replace:{symbol:e}},n(771010)),2:e=>r.t(null,{replace:{symbol:e}},n(847816)),1:e=>r.t(null,{replace:{symbol:e}},n(404107)),4:e=>r.t(null,{replace:{symbol:e}},n(623851))},3:{0:e=>r.t(null,{replace:{symbol:e}},n(447530)),5:e=>r.t(null,{replace:{symbol:e}},n(321105)),3:e=>r.t(null,{replace:{symbol:e}},n(920175)),2:e=>r.t(null,{replace:{symbol:e}},n(27267)),1:e=>r.t(null,{replace:{symbol:e}},n(543880)),4:e=>r.t(null,{replace:{symbol:e}},n(897978))},4:{0:e=>r.t(null,{replace:{symbol:e}},n(531955)),5:e=>r.t(null,{replace:{symbol:e}},n(347693)),3:e=>r.t(null,{replace:{symbol:e}},n(875482)),2:e=>r.t(null,{replace:{symbol:e}},n(407371)),1:e=>r.t(null,{replace:{symbol:e}},n(672173)),4:e=>r.t(null,{replace:{symbol:e}},n(976044))}};function c(e){const{displaySymbol:t,orderType:n,toastType:r,parentId:l,stopType:c}=e;return l?1===n?a[r](t):s[c??o.StopType.StopLoss][r](t):i[n][r](t)}function u(e){const{side:t,orderType:o,quantity:a,primaryPrice:s,secondaryPrice:i}=e,c=l.defaultQuantityFormatter.format(a);if(!s)return{primaryText:1===t?r.t(null,{replace:{quantity:c}},n(581591)):r.t(null,{replace:{quantity:c}},n(117112))};let u;4===o&&i&&(u=1===t?r.t(null,{replace:{quantity:c,limitPrice:s,stopPrice:i}},n(379498)):r.t(null,{replace:{quantity:c,limitPrice:s,stopPrice:i}},n(754789))),void 0===u&&(u=1===t?r.t(null,{replace:{quantity:c,price:s}},n(880512)):r.t(null,{replace:{quantity:c,price:s}},n(162734)));const[d,m]=u.split("{separatorTag}");return{primaryText:d,secondaryText:m}}},153894:(e,t,n)=>{"use strict";n.d(t,{TRADING_NOTIFICATIONS_GROUP_ID:()=>r})
;const r="trading-notifications"},869104:(e,t,n)=>{"use strict";n.r(t),n.d(t,{initToastLayerChartPageComponent:()=>Ge});var r=n(50959),o=n(904237),l=n(801808),a=n(787382),s=n(497754),i=n.n(s),c=n(925931),u=n(373989);var d=n(139784),m=n(800417),p=n(959189),g=n(106935),y=n.n(g);function f(e){const{startSlot:t,endSlot:n,iconOnly:o,children:l}=e;return r.createElement(r.Fragment,null,t&&r.createElement("span",{className:i()(y().slot,y().startSlot)},t),!(0,p.isIconOnly)(l,o)&&r.createElement("span",{className:y().content},l),n&&r.createElement("span",{className:i()(y().slot,y().endSlot)},n))}var h=n(821904),v=n.n(h);function x(e){const{className:t,color:n,variant:r,size:o,stretch:l,animated:a,disableThemes:s,isInvertedColorTheme:i,...c}=e;return{...c,...(0,m.filterDataProps)(e),...(0,m.filterAriaProps)(e)}}function b(e){const{reference:t,children:n,iconOnly:o,startSlot:l,endSlot:a,...i}=e,{tooltip:c,className:m}=(0,d.useTooltip)({tooltip:e.title,doNotShowTooltipOnTouch:!1},t),p=function(e,t,n){const{className:r,variant:o="primary",size:l,stretch:a,animated:i=!1,disableThemes:c=!1,iconOnly:d=!1,isAnchor:m=!1,isActivated:p=!1,isInvertedColorTheme:g=!1,endSlot:y,startSlot:f}=t,h=(0,u.renameColors)(t.color??"brand"),v=function(e,t){return!!t&&"black"===e}(h,g);return s(r,e.roundButton,e.roundButtonColor,v?e[`inverted${h}`]:e[h],e[o],void 0!==l&&e[l],i&&e.animated,a&&e.stretch,c&&e.disableThemes,d&&e.iconOnly,m&&e.link,p&&e.activated,f&&e.withStartSlot,y&&e.withEndSlot,n)}({...v(),...y()},e,m);return r.createElement("button",{...x(i),className:p,ref:t,title:c},r.createElement(f,{startSlot:l,endSlot:a,iconOnly:o,children:n}))}n(591365),n(966784);n(15378);var C=n(183787),S=n(732219);const w=(0,r.createContext)(void 0);var E=n(916898),T=n(552019),z=n.n(T),N=n(338986);const L=e=>`toast-group-close-button-${e}`,I={list:new Map,removed:new Set,added:new Set};const B=r.memo((function(e){const{expandButtonLabel:t,collapseButtonLabel:n,closeAllButtonAriaLabel:o,ariaLive:l="polite",toastComponent:a,closable:s,closeGroupEventDetails:c,onCloseAll:u,onClose:d,onAddAnimationStart:m,groupId:p,groupState:g=I}=e,[y,f]=(0,r.useState)(!1),[h,v]=(0,r.useState)(!1),[x,w]=(0,r.useState)(y),T=(0,r.useMemo)((()=>{const e=Array.from(g.list.values());return y||x?e:e.slice(Math.max(0,e.length-6))}),[g,y,x]);(0,r.useEffect)((()=>{if(y)return v(!0),void w(!0);v(!1);const e=setTimeout((()=>{w(!1)}),E.TOAST_ANIMATION_DURATION);return()=>clearTimeout(e)}),[y]),(0,r.useEffect)((()=>{g.added.forEach((e=>{g.list.get(e).manualAnimationStart||m(e)}))}),[g,m]);const[B,O]=(0,r.useState)(void 0),U=g.list.size-g.removed.size,D=(0,r.useMemo)((()=>{if(U<=0)return;let e;for(let t=T.length-1;t>=0;t--){const n=T[t];if(!g.removed.has(n.id)){e=n.id;break}}return e}),[U,T,g.removed]),P=g.list.size-g.added.size-g.removed.size,_=(0,r.useRef)(P);U>0&&(_.current=P);const A=y?n:t,F=T.map(((e,t,n)=>{const o=g.removed.has(e.id),l=g.added.has(e.id),s=e.id===D,i=n.length-1===t,c=o||!y&&!i;return r.createElement(R,{data:e,Component:a,key:e.id,removed:o,added:l,isLastNotRemoved:s,
inert:c,onClose:d,setLastChildHeight:O,ariaSetsize:n.length,ariaPosinset:t+1})})),k=(0,r.useId)(),M=P<=1,V=y?0:Math.min(2,U-1),j=P>0?B:0,q=void 0===B&&0===g.added.size?"unset":`${j}px`;return r.createElement("div",{className:N.toastGroup,style:{"--ui-lib-toast-group-last-height-padded":q,"--ui-lib-toast-group-list-count":V}},r.createElement("div",{className:i()(N.controls,M&&N.controlsHidden),inert:M?"":void 0},r.createElement("div",null,r.createElement(b,{variant:"quiet-primary",color:"gray",size:"xsmall","aria-controls":k,"aria-expanded":y,onClick:()=>{f((e=>!e))},endSlot:r.createElement(S.Counter,{size:"small",color:"neutral-bold",count:_.current,"aria-label":""}),"data-name":(Z=p,`toast-group-expand-button-${Z}`)},A)),s&&r.createElement("div",null,r.createElement(b,{variant:"quiet-primary",color:"gray",size:"xsmall","aria-controls":k,onClick:()=>{u(p,c)},iconOnly:!0,title:o,endSlot:r.createElement(C.Icon,{icon:z()}),"data-name":L(p)}))),r.createElement("div",{role:"log","aria-live":l},r.createElement("ul",{id:k,className:i()(N.list,!h&&N.listCollapsed,0===P&&N.listEmplty)},F)));var Z}));const R=r.memo((function(e){const{data:t,ariaPosinset:n,removed:o,isLastNotRemoved:l,added:a,inert:s,onClose:c,ariaSetsize:u,Component:d,setLastChildHeight:m}=e,p=!o&&!a,g=i()(N.item,o?N.itemRemoved:N.itemNotRemoved,p&&N.itemStable,a&&N.itemAdded),y={inert:s?"":void 0};return r.createElement("li",{"aria-atomic":!0,className:g,key:t.id,"aria-posinset":n,"aria-setsize":u,...y},r.createElement("div",{className:N.itemInner},r.createElement("div",{className:N.itemInnerInner},r.createElement(w.Provider,{value:l?m:void 0},r.createElement(d,{...t,onClose:e=>{c(t.id,e)}})))))}));var O=n(680653);function U(e){const{toastManager:t,setUserInteracting:n,groupsConfig:o,expandGroupButtonLabel:l,collapseGroupButtonLabel:a,closeGroupButtonAriaLabel:s,regionAriaLabel:u}=e,{startAddAnimation:d,removeToast:m,removeGroup:p}=t,[g,y]=(0,r.useState)(t.state.groups);(0,r.useEffect)((()=>t.subscribe((()=>{y(t.state.groups)}))),[t]);const f=function(e){const t=(0,r.useRef)(!1),n=(0,r.useRef)(!1),o=(0,r.useRef)(!1),l=(0,r.useRef)(!1),a=(0,r.useRef)((0,c.nanoid)());if((0,r.useEffect)((()=>()=>{e?.(!1,a.current)}),[e]),!e)return{};const s=()=>{setTimeout((()=>{const r=t.current||n.current||o.current;l.current!==r&&(l.current=r,e(l.current,a.current))}),0)},i=()=>{o.current=!1,s()};return{onPointerEnter:()=>{t.current=!0,s()},onPointerLeave:()=>{t.current=!1,s()},onFocus:e=>{e.target.matches(":focus-visible")&&(n.current=!0,s())},onBlur:()=>{n.current=!1,s()},onTouchStart:()=>{o.current=!0,s()},onTouchEnd:i,onTouchCancel:i}}(n),h=(0,r.useMemo)((()=>function(e){const t=new Map;return Array.from(e.values()).forEach(((e,n)=>{t.set(e.id,n)})),t}(g)),[g]),v=(0,r.useMemo)((()=>function(e,t){const n=Object.values(t);return n.sort(((t,n)=>{const r=t.priority-n.priority;if(0!==r)return r;const o=e.get(t.id),l=e.get(n.id);return(void 0===o?Number.POSITIVE_INFINITY:o)-(void 0===l?Number.POSITIVE_INFINITY:l)})),n}(h,o)),[h,o]),x=v.map((e=>{const t=g.get(e.id)
;return r.createElement(B,{expandButtonLabel:l,collapseButtonLabel:a,closeAllButtonAriaLabel:s,toastComponent:e.renderer,closable:e.closable,closeGroupEventDetails:e.closeGroupEventDetails,onClose:m,onCloseAll:p,onAddAnimationStart:d,groupId:e.id,key:e.id,groupState:t})})),b=v.every((e=>{const t=g.get(e.id);return!t||0===t.list.size}));return r.createElement("section",{"aria-label":u,className:O.toastList},r.createElement("div",{className:i()(O.toastListScroll,!b&&O.notEmpty)},r.createElement("div",{className:O.toastListInner,...f},x)))}var D,P=n(348194),_=n(878112);function A(e){return{x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY}}function F(e,t,n){const o=(0,r.useRef)({x:0,y:0});return{onTouchStart:(0,r.useCallback)((e=>{o.current=A(e)}),[]),onTouchEnd:(0,r.useCallback)((r=>{const l=function(e,t,n,r,o){const l=Math.abs(n.x-t.x),a=Math.abs(n.y-t.y),s=e.offsetWidth*r,i=e.offsetHeight*o;return l>a&&l>s?n.x>t.x?D.Right:D.Left:a>i?n.y>t.y?D.Down:D.Up:D.Unset}(r.currentTarget,o.current,A(r),e,t);n(l)}),[n,o,e,t])}}!function(e){e.Right="right",e.Left="left",e.Up="up",e.Down="down",e.Unset="unset"}(D||(D={}));var k=n(270762),M=n(663346),V=n.n(M);function j(e){const{label:t,onClose:n}=e;return t?r.createElement(k.NavButton,{onClick:n,"aria-label":t,icon:V(),size:"xsmall"}):null}var q=n(958180),Z=n.n(q),G=n(16891);const X={paddingBlock:"4px"},J={padding:"3px 0 3px 3px",borderWidth:"1px"};function W(e){const{onClose:t,header:n,closeButtonLabel:o,iconColorClassname:l,icon:a=Z(),children:s,onSwipe:c}=e;const{onTouchStart:u,onTouchEnd:d}=F(.2,.7,(function(e){c&&e===D.Left&&c()})),m=function(e){const t=(0,r.useContext)(w),n=(0,r.useRef)(null),o=(0,r.useRef)(null),l=(0,r.useCallback)((t=>{if(n.current?.disconnect(),n.current=null,!t||!o.current)return;const r=new ResizeObserver((n=>{const r=n[0].contentRect.height;r>0&&t(r+e)}));r.observe(o.current),n.current=r}),[]),a=(0,r.useMemo)((()=>t?e=>{o.current=e,l(t)}:void 0),[t,l]);return(0,r.useEffect)((()=>{l(t)}),[t,l]),a}(16);return r.createElement("div",{className:i()(G.toastCommon,c&&G.swipable),onTouchStart:c?u:void 0,onTouchEnd:c?d:void 0,style:J},r.createElement("div",{className:i()(G.iconContainer,l)},r.createElement(_.Icon,{icon:a,className:G.icon})),r.createElement("div",{className:G.contentContainerWrapper,ref:m,style:X},r.createElement("div",{className:G.contentContainerInner},r.createElement("div",{className:G.contentContainer},(n||o)&&r.createElement("div",{className:G.topContent},r.createElement("div",{className:G.header},n),r.createElement("div",{className:G.closeButton},r.createElement(j,{label:o,onClose:t}))),s))))}var Q,H;!function(e){e.XXXSmall="xxxsmall",e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small"}(Q||(Q={})),function(e){e.Round="round",e.Square="square"}(H||(H={}));const K=r.createContext({size:Q.Small,shape:H.Round,isSelected:!1});var Y=n(324184),$=n.n(Y);function ee(e,t){const{className:n,isSelected:r,shape:o,hasError:l,disableOverflowTooltip:a,size:i,stretch:c}=e
;return s(n,$().tag,r&&$().selected,c&&$().stretch,i&&$()[i],o&&$()[o],l&&$().error,t&&$().interactive,!a&&["apply-overflow-tooltip","apply-overflow-tooltip--allow-text","apply-overflow-tooltip--check-children"])}function te(e){const t={size:e.size,shape:e.shape,isSelected:e.isSelected};return r.createElement(K.Provider,{value:t},e.children)}function ne(e){const t=e=>["string","number"].includes(typeof e);if(t(e))return e;if(e instanceof Array)return e.map(ne).filter(Boolean)[0];if("object"==typeof e){const n=e?.props.children;return t(n)?ne(n):Array.isArray(n)?ne(n.join("").trim()):void 0}}function re(e,t){const{className:n,shape:o,children:l,size:a=Q.Small,hasError:s,isSelected:i,disableOverflowTooltip:c,stretch:u,...d}=e;return r.createElement("a",{...d,className:ee({className:n,isSelected:i,children:l,size:a,hasError:s,shape:o,disableOverflowTooltip:c,stretch:u},!0),ref:t},r.createElement(te,{size:a,shape:o,isSelected:i},l))}re.displayName="AnchorTag";r.forwardRef(re);function oe(e,t){const{className:n,shape:o,children:l,size:a=Q.Small,hasError:s,isSelected:i,disableOverflowTooltip:c,stretch:u,...d}=e;return r.createElement("button",{...d,className:ee({className:n,isSelected:i,children:l,size:a,hasError:s,shape:o,disableOverflowTooltip:c,stretch:u},!0),ref:t},r.createElement(te,{size:a,shape:o,isSelected:i},l))}oe.displayName="ButtonTag";r.forwardRef(oe);const le=r.forwardRef(((e,t)=>{const{className:n,shape:o,children:l,size:a=Q.Small,hasError:s,isSelected:i,disableOverflowTooltip:c,stretch:u,...d}=e;return r.createElement("span",{...d,className:ee({className:n,isSelected:i,children:l,size:a,hasError:s,shape:o,disableOverflowTooltip:c,stretch:u},!1),"data-overflow-tooltip-text":ne(l),ref:t},r.createElement(te,{size:a,shape:o,isSelected:i},l))}));var ae=n(40415);function se(e){const{children:t,className:n}=e,{size:o=Q.Small}=(0,r.useContext)(K);return r.createElement("span",{className:i()(ae["tag-text"],ae[o],n)},t)}var ie=n(72903),ce=n(519722);function ue(e){const{className:t,icon:n,src:o,"aria-label":l,primaryLogoUrl:a,secondaryLogoUrl:s,...c}=e,{size:u=Q.Small,shape:d=H.Round}=(0,r.useContext)(K),[m,p]=(0,r.useState)(!1),g=a&&s,y=i()(ce["tag-icon"],g&&ce.iconPair,ce[d],ce[u],t),f=()=>p(!0);return n?r.createElement(_.Icon,{...c,className:y,icon:n,ariaLabel:e["aria-label"]}):o?m?r.createElement(r.Fragment,null):r.createElement("img",{...c,className:y,src:o,"aria-hidden":!l,alt:l,onError:f}):r.createElement(ie.CircleLogoPair,{size:u===Q.XXXSmall?"xxxxsmall":"xxxsmall",className:y,primaryLogoUrl:a,secondaryLogoUrl:s})}var de=n(583882),me=n(242843),pe=n(638353),ge=n.n(pe),ye=n(793296),fe=n.n(ye),he=n(635052),ve=n.n(he),xe=n(813298),be=n.n(xe),Ce=n(757520),Se=n.n(Ce),we=n(79233),Ee=n.n(we);const Te="{symbolPlacement}";function ze(e){const{logoUrls:[t,n]}=e;return void 0===t?r.createElement(r.Fragment,null):void 0===n?r.createElement(ue,{src:t}):r.createElement(ue,{primaryLogoUrl:t,secondaryLogoUrl:n})}function Ne(e){const{symbol:t,displaySymbol:n,orderType:o,toastType:l,parentId:a,stopType:s}=e,i=(0,
de.useSymbolLogoAndDescription)(t),c=(0,me.getHeadingText)({displaySymbol:Te,orderType:o,toastType:l,parentId:a,stopType:s}).match(new RegExp(`(.*)${Te}(.*)`));if(!c)return r.createElement(r.Fragment,null);const[,u,d]=c;return r.createElement(r.Fragment,null,u&&r.createElement("span",null,u),r.createElement(le,{shape:H.Round,size:Q.XXSmall,className:ge().tag},i?.logoUrls&&r.createElement(ze,{logoUrls:i.logoUrls}),r.createElement(se,null,n)),d&&r.createElement("span",null,d))}function Le(e){const{side:t}=e,{primaryText:n,secondaryText:o}=(0,me.getOrderInfoTexts)(e);return r.createElement(r.Fragment,null,n&&r.createElement("span",{className:i()(ge().orderInfo,1===t?ge().buyOrderInfo:ge().sellOrderInfo)},n),o)}const Ie=r.memo((e=>{const{side:t,toastType:o,orderType:l,symbol:s,displaySymbol:i,quantity:c,primaryPrice:u,secondaryPrice:d,priceFormatter:m,parentId:p,stopType:g,content:y,onClose:f}=e,h=function(e,t){return 0===t?1===e?fe():ve():5===t||3===t||4===t?Se():2===t?Ee():1===t?be():null}(t,o),v=function(e,t){return 0===t?1===e?ge().iconColorBuy:ge().iconColorSell:5===t||3===t||4===t?ge().iconColorSuccess:2===t?ge().iconColorWarning:1===t?ge().iconColorCancel:null}(t,o);if(null===h||null===v)return r.createElement(r.Fragment,null);const x=u?m.format(u):void 0,b=d?m.format(d):void 0;return r.createElement(W,{icon:h,iconColorClassname:v,closeButtonLabel:a.t(null,void 0,n(862578)),onClose:f,header:r.createElement(Ne,{symbol:s,displaySymbol:i,orderType:l,toastType:o,parentId:p,stopType:g})},r.createElement("div",null,r.createElement(Le,{orderType:l,side:t,quantity:c,primaryPrice:x,secondaryPrice:b}),y&&r.createElement("div",{className:ge().content},y)))}),(()=>!0));var Be=n(586816),Re=n(739050),Oe=n.n(Re),Ue=n(500175),De=n.n(Ue);const Pe={0:{icon:Se(),className:Oe().iconColorSuccess},1:{icon:Ee(),className:Oe().iconColorWarning},2:{icon:De(),className:Oe().iconColorInfo}};var _e=n(153894);const Ae={id:Be.ORDERS_GROUP_ID,renderer:Ie,priority:4,closable:!0},Fe={id:_e.TRADING_NOTIFICATIONS_GROUP_ID,renderer:function(e){const{title:t,content:o,notificationType:l,onClose:s}=e,i=Pe[l];return r.createElement(W,{icon:i.icon,iconColorClassname:i.className,closeButtonLabel:a.t(null,void 0,n(862578)),onClose:s,header:t},r.createElement("div",null,o))},priority:5,closable:!0},ke={[Be.ORDERS_GROUP_ID]:Ae,[_e.TRADING_NOTIFICATIONS_GROUP_ID]:Fe},Me=(0,r.memo)((()=>r.createElement(U,{setUserInteracting:P.toastUserInteractionStatus.update,toastManager:P.toastManager,groupsConfig:ke,expandGroupButtonLabel:a.t(null,{context:"action"},n(822622)),collapseGroupButtonLabel:a.t(null,{context:"action"},n(23707)),closeGroupButtonAriaLabel:a.t(null,{context:"action"},n(646269))})));var Ve=n(297265),je=n(565865);function qe(e,t){const n=(0,Ve.useWatchedValueReadonly)({watchedValue:e?.[t],defaultValue:0});return 0===n?0:n+4}function Ze(e){const{topArea:t,leftArea:n}=e,o={"--toasts-top":`${qe(t,"height")}px`,"--toasts-left":`${qe(n,"width")}px`};return r.createElement("div",{className:je.toastLayerChart,style:o},r.createElement(Me,null))}
function Ge(e){const t=(0,l.getRootOverlapManager)().ensureWindow("chart-toasts-container"),n=(0,o.createRoot)(t),a=r.createElement(Ze,e);n.render(a)}},868333:(e,t,n)=>{"use strict";var r;n.d(t,{LogoSize:()=>r,getLogoUrlResolver:()=>a}),function(e){e[e.Medium=0]="Medium",e[e.Large=1]="Large"}(r||(r={}));class o{getSymbolLogoUrl(e){return e}getCountryFlagUrl(){return""}getCryptoLogoUrl(e){return e}getProviderLogoUrl(e){return e}getSourceLogoUrl(e){return e}getBlockchainContractLogoUrl(e){return e}}let l;function a(){return l||(l=new o),l}},297265:(e,t,n)=>{"use strict";n.d(t,{useWatchedValueReadonly:()=>o});var r=n(50959);const o=(e,t=!1,n=[])=>{const o="watchedValue"in e?e.watchedValue:void 0,l="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[a,s]=(0,r.useState)(o?o.value():l);return(t?r.useLayoutEffect:r.useEffect)((()=>{if(o){s(o.value());const e=e=>s(e);return o.subscribe(e),()=>o.unsubscribe(e)}return()=>{}}),[o,...n]),a}},904237:(e,t,n)=>{"use strict";var r=n(632227);t.createRoot=r.createRoot,r.hydrateRoot},583882:(e,t,n)=>{"use strict";n.d(t,{useSymbolLogoAndDescription:()=>l});n(868333);var r=n(50959),o=n(334529);function l(e){{let t=[];const n=function(e,t=!0){const[n,l]=(0,r.useState)(null===e?{completed:!0,invalid:!0,quotes:null,symbol:e}:{completed:!1,invalid:!1,quotes:null,symbol:e});return(0,r.useEffect)((()=>{if(null===e)return void l({completed:!0,invalid:!0,quotes:null,symbol:e});l({completed:!1,invalid:!1,quotes:null,symbol:e});const n=(0,o.getQuoteSessionInstance)("full"),r=()=>{n.unsubscribe("useSymbolQuotes",e,a)},a=n=>{const{values:o,complete:a,status:s}=n;l({invalid:"error"===s,quotes:{...o},completed:void 0!==a||"error"===s,symbol:e}),void 0===a&&"error"!==s||t&&r()};return n.subscribe("useSymbolQuotes",e,a),()=>{r()}}),[e]),n}(e,!1),l=n.quotes;return l&&(l.logoid?t=[l.logoid]:l["currency-logoid"]&&l["base-currency-logoid"]&&(t=[l["base-currency-logoid"],l["currency-logoid"]])),{logoUrls:t,description:l?.description}}}},46415:(e,t,n)=>{"use strict";var r,o,l,a,s;n.d(t,{ConnectWarningMessageDisplayType:()=>a,StopType:()=>r}),function(e){e[e.StopLoss=0]="StopLoss",e[e.TrailingStop=1]="TrailingStop",e[e.GuaranteedStop=2]="GuaranteedStop"}(r||(r={})),function(e){e.Stocks="stocks",e.Futures="futures",e.Forex="forex",e.Crypto="crypto",e.Others="others"}(o||(o={})),function(e){e.Symbol="symbol"}(l||(l={})),function(e){e[e.PopUp=0]="PopUp",e[e.Notification=1]="Notification"}(a||(a={})),function(e){e.Quantity="qty",e.OrderSide="side",e.Price="price",e.Duration="duration",e.Brackets="brackets",e.StopLossType="slType"}(s||(s={}))},552019:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M13.35 5.35a.5.5 0 0 0-.7-.7L9 8.29 5.35 4.65a.5.5 0 1 0-.7.7L8.29 9l-3.64 3.65a.5.5 0 0 0 .7.7L9 9.71l3.65 3.64a.5.5 0 0 0 .7-.7L9.71 9l3.64-3.65z"/></svg>'},793296:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentcolor" fill-rule="evenodd" clip-rule="evenodd" d="M14.468 9.379l-.47-.38-.471.38-6.64 5.353a2.397 2.397 0 003.01 3.732l4.1-3.306 4.101 3.306a2.397 2.397 0 103.01-3.732l-6.64-5.353z"/></svg>'},635052:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentcolor" fill-rule="evenodd" clip-rule="evenodd" d="M14.468 18.616l-.47.38-.471-.38-6.64-5.353a2.397 2.397 0 013.01-3.732l4.1 3.306 4.101-3.306a2.397 2.397 0 013.01 3.732l-6.64 5.353z"/></svg>'},813298:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14 25c6.075 0 11-4.925 11-11S20.075 3 14 3 3 7.925 3 14s4.925 11 11 11zM9.293 10.707l1.414-1.414L14 12.586l3.293-3.293 1.414 1.414L15.414 14l3.293 3.293-1.414 1.414L14 15.414l-3.293 3.293-1.414-1.414L12.586 14l-3.293-3.293z"/></svg>'},757520:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M25 14a11 11 0 1 1-22 0 11 11 0 0 1 22 0Zm-5-2.54L18.63 10l-5.66 5.3-3.66-3.17L8 13.63 13.02 18 20 11.46Z"/></svg>'},79233:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 25a11 11 0 1 0 0-22 11 11 0 0 0 0 22Zm-1-9V8h2v8h-2Zm2 3a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"/></svg>'},500175:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14 25c6.075 0 11-4.925 11-11S20.075 3 14 3 3 7.925 3 14s4.925 11 11 11zm1.5-16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zm-2 5H12V12h3v8h-1.5v-6.5z"/></svg>'},958180:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M12 8H2v5h5v7h5V8Zm8.92 12H15l5.08-12H26l-5.08 12Zm-5.42-7a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"/></svg>'},663346:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 9 9m0-9-9 9"/></svg>'},925931:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>r});let r=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);