(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[139],{497754:(t,e)=>{var r;!function(){"use strict";var o={}.hasOwnProperty;function i(){for(var t=[],e=0;e<arguments.length;e++){var r=arguments[e];if(r){var a=typeof r;if("string"===a||"number"===a)t.push(r);else if(Array.isArray(r)&&r.length){var n=i.apply(null,r);n&&t.push(n)}else if("object"===a)for(var l in r)o.call(r,l)&&r[l]&&t.push(l)}}return t.join(" ")}t.exports?(i.default=i,t.exports=i):void 0===(r=function(){return i}.apply(e,[]))||(t.exports=r)}()},188317:t=>{t.exports={pills:"pills-PVWoXu5j",primary:"primary-PVWoXu5j",gray:"gray-PVWoXu5j",selected:"selected-PVWoXu5j",grouped:"grouped-PVWoXu5j",active:"active-PVWoXu5j",disableActiveOnTouch:"disableActiveOnTouch-PVWoXu5j",disableActiveStateStyles:"disableActiveStateStyles-PVWoXu5j",withGrouped:"withGrouped-PVWoXu5j","quiet-primary":"quiet-primary-PVWoXu5j",green:"green-PVWoXu5j",red:"red-PVWoXu5j",blue:"blue-PVWoXu5j",secondary:"secondary-PVWoXu5j",ghost:"ghost-PVWoXu5j"}},601538:t=>{t.exports={lightButton:"lightButton-bYDQcOkp",link:"link-bYDQcOkp",ltr:"ltr-bYDQcOkp",rtl:"rtl-bYDQcOkp","typography-regular16px":"typography-regular16px-bYDQcOkp","typography-medium16px":"typography-medium16px-bYDQcOkp","typography-regular14px":"typography-regular14px-bYDQcOkp","typography-semibold14px":"typography-semibold14px-bYDQcOkp","typography-semibold16px":"typography-semibold16px-bYDQcOkp",content:"content-bYDQcOkp",visuallyHidden:"visuallyHidden-bYDQcOkp",nowrap:"nowrap-bYDQcOkp",ellipsisContainer:"ellipsisContainer-bYDQcOkp",textWrapContainer:"textWrapContainer-bYDQcOkp",textWrapWithEllipsis:"textWrapWithEllipsis-bYDQcOkp",slot:"slot-bYDQcOkp",caret:"caret-bYDQcOkp",activeCaret:"activeCaret-bYDQcOkp",xsmall:"xsmall-bYDQcOkp",withStartSlot:"withStartSlot-bYDQcOkp",withEndSlot:"withEndSlot-bYDQcOkp",noContent:"noContent-bYDQcOkp",wrap:"wrap-bYDQcOkp",small:"small-bYDQcOkp",medium:"medium-bYDQcOkp"}},344352:t=>{t.exports={errorCard:"errorCard-S9sXvhAu",errorCard__icon:"errorCard__icon-S9sXvhAu",errorCard_size_big:"errorCard_size_big-S9sXvhAu",errorCard__message:"errorCard__message-S9sXvhAu",errorCard_limitWidth:"errorCard_limitWidth-S9sXvhAu",errorCard__link:"errorCard__link-S9sXvhAu",errorCard__buttons:"errorCard__buttons-S9sXvhAu",errorCardRendererContainer:"errorCardRendererContainer-S9sXvhAu",errorCardButton:"errorCardButton-S9sXvhAu"}},959189:(t,e,r)=>{"use strict";function o(t,e){return e||null==t||("string"==typeof t||Array.isArray(t))&&0===t.length}r.d(e,{isIconOnly:()=>o})},418920:(t,e,r)=>{"use strict";r.d(e,{useLightButtonClasses:()=>c});var o=r(50959),i=r(497754),a=r(234539),n=r(959189),l=r(380327);const s=o.createContext({isInButtonGroup:!1,isGroupPrimary:!1}),c=(t,e,r)=>{const c=(0,
o.useContext)(a.CustomBehaviourContext),{className:d,isSelected:u,children:p,showCaret:h,forceDirection:m,iconOnly:v,color:g="gray",variant:C="primary",size:y="medium",enableActiveStateStyles:x=c.enableActiveStateStyles,typography:w,isLink:b=!1,textWrap:f,isPills:S,isActive:_,startSlot:A,endSlot:W}=e,k=t[`typography-${((t,e,r)=>{if(r){const t=r.replace(/^\D+/g,"");return e?`semibold${t}`:r}switch(t){case"xsmall":return e?"semibold14px":"regular14px";case"small":case"medium":return e?"semibold16px":"regular16px";default:return""}})(y,u||S,w||void 0)}`],E=(0,o.useContext)(l.ControlGroupContext),{isInButtonGroup:V,isGroupPrimary:H}=(0,o.useContext)(s);return i(d,t.lightButton,b&&t.link,_&&t.active,u&&t.selected,(0,n.isIconOnly)(p,v)&&t.noContent,!!A&&t.withStartSlot,(h||!!W)&&t.withEndSlot,r&&t.withGrouped,m&&t[m],t[H?"primary":C],t[H?"gray":g],t[y],k,!x&&t.disableActiveStateStyles,E.isGrouped&&t.grouped,f&&t.wrap,V&&t.disableActiveOnTouch,S&&t.pills)}},854797:(t,e,r)=>{"use strict";r.d(e,{LightButtonContent:()=>p});var o=r(50959),i=r(497754),a=r(601198),n=r(959189),l=r(878112),s=r(602948),c=r(601538),d=r.n(c);const u=t=>o.createElement(l.Icon,{className:i(d().caret,t&&d().activeCaret),icon:s});function p(t){const{showCaret:e,iconOnly:r,ellipsis:l=!0,textWrap:s,tooltipText:c,children:p,endSlot:h,startSlot:m,isActiveCaret:v}=t;[h,e].filter((t=>!!t));return o.createElement(o.Fragment,null,m&&o.createElement("span",{className:i(d().slot,d().startSlot)},m),!(0,n.isIconOnly)(p,r)&&o.createElement("span",{className:i(d().content,!s&&d().nowrap,"apply-overflow-tooltip","apply-overflow-tooltip--check-children-recursively","apply-overflow-tooltip--allow-text"),"data-overflow-tooltip-text":c??(0,a.getTextForTooltip)(p)},s||l?o.createElement(o.Fragment,null,o.createElement("span",{className:i(!s&&l&&d().ellipsisContainer,s&&d().textWrapContainer,s&&l&&d().textWrapWithEllipsis)},p),o.createElement("span",{className:d().visuallyHidden,"aria-hidden":!0},p)):o.createElement(o.Fragment,null,p,o.createElement("span",{className:d().visuallyHidden,"aria-hidden":!0},p))),h&&o.createElement("span",{className:i(d().slot,d().endSlot)},h),e&&u(v))}},943158:(t,e,r)=>{"use strict";r.d(e,{LightButton:()=>u});var o=r(50959),i=r(380327),a=r(418920),n=r(854797),l=r(601538),s=r.n(l),c=r(188317),d=r.n(c);function u(t){const{isGrouped:e}=o.useContext(i.ControlGroupContext),{reference:r,className:l,isSelected:c,children:u,iconOnly:p,ellipsis:h,showCaret:m,forceDirection:v,endSlot:g,startSlot:C,color:y,variant:x,size:w,enableActiveStateStyles:b,typography:f,textWrap:S=!1,maxLines:_,style:A={},isPills:W,isActive:k,tooltipText:E,role:V,...H}=t,O=S?_??2:1,L=O>0?{...A,"--ui-lib-light-button-content-max-lines":O}:A;return o.createElement("button",{...H,className:(0,a.useLightButtonClasses)({...d(),...s()},{className:l,isSelected:c,children:u,iconOnly:p,showCaret:m,forceDirection:v,endSlot:g,startSlot:C,color:y,variant:x,size:w,enableActiveStateStyles:b,typography:f,textWrap:S,isPills:W,isActive:k},e),ref:r,style:L,role:V
},o.createElement(n.LightButtonContent,{showCaret:m,isActiveCaret:m&&(W||k||c),iconOnly:p,ellipsis:h,textWrap:S,tooltipText:E,endSlot:g,startSlot:C},u))}},380327:(t,e,r)=>{"use strict";r.d(e,{ControlGroupContext:()=>o});const o=r(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},234539:(t,e,r)=>{"use strict";r.d(e,{CustomBehaviourContext:()=>o});const o=(0,r(50959).createContext)({enableActiveStateStyles:!0});o.displayName="CustomBehaviourContext"},183787:(t,e,r)=>{"use strict";r.d(e,{Icon:()=>i});var o=r(50959);const i=o.forwardRef(((t,e)=>{const{icon:r="",title:i,ariaLabel:a,ariaLabelledby:n,ariaHidden:l,...s}=t,c=!!(i||a||n);return o.createElement("span",{role:"img",...s,ref:e,"aria-label":a,"aria-labelledby":n,"aria-hidden":l||!c,title:i,dangerouslySetInnerHTML:{__html:r}})}))},878112:(t,e,r)=>{"use strict";r.d(e,{Icon:()=>o.Icon});var o=r(183787)},601198:(t,e,r)=>{"use strict";r.d(e,{getTextForTooltip:()=>n});var o=r(50959);const i=t=>(0,o.isValidElement)(t)&&Boolean(t.props.children),a=t=>null==t||"boolean"==typeof t||"{}"===JSON.stringify(t)?"":t.toString()+" ",n=t=>Array.isArray(t)||(0,o.isValidElement)(t)?o.Children.toArray(t).reduce(((t,e)=>{let r="";return r=(0,o.isValidElement)(e)&&i(e)?n(e.props.children):(0,o.isValidElement)(e)&&!i(e)?"":a(e),t.concat(r)}),"").trim():a(t)},42898:(t,e,r)=>{"use strict";r.r(e),r.d(e,{ErrorCard:()=>x,ErrorCardRenderer:()=>b,ResizableErrorCard:()=>w});var o,i,a=r(50959),n=r(632227),l=r(920057),s=r(497754),c=r.n(s),d=r(878112),u=r(943158),p=(r(609838),r(440891)),h=r(229540),m=r(911425),v=(r(178080),r(779035),r(244645)),g=r(177678),C=r(344352);!function(t){t[t.ShowErrorCardIconMinHeight=105]="ShowErrorCardIconMinHeight"}(o||(o={})),function(t){t[t.Empty=0]="Empty",t[t.Medium=1]="Medium",t[t.Big=2]="Big"}(i||(i={}));const y={ghost:{1:(0,p.enabled)("hide_image_invalid_symbol")?void 0:h,2:(0,p.enabled)("hide_image_invalid_symbol")?void 0:m},attention:{1:void 0,2:void 0},"stop-hand":{1:void 0,2:void 0},"unsupported-resolution":{1:v,2:g}};new Set(["en","gb","de_DE","fr","es","it","pl","ja"]);const x=a.forwardRef(((t,e)=>{const{icon:r,message:o,size:i=1,rawHtml:n=!1,doNotLimitWidth:l,solutionId:s,buttons:p}=t,h=a.useMemo((()=>r&&function(t,e){if(0!==e)return y[t][e]}(r,i)),[r,i]),m=2===i?"medium":"xsmall",v=p?p.map((t=>a.createElement(u.LightButton,{onClick:t.onClick,className:C.errorCardButton,size:m,key:t.name},t.name))):null;return a.createElement("div",{ref:e,className:c()(C.errorCard,2===i&&C.errorCard_size_big,!l&&C.errorCard_limitWidth),"data-name":"error-card"},h&&a.createElement(d.Icon,{icon:h,className:C.errorCard__icon}),n?a.createElement("div",{className:C.errorCard__message,dangerouslySetInnerHTML:{__html:o}}):a.createElement("div",{className:C.errorCard__message},o),null,v&&a.createElement("div",{className:C.errorCard__buttons}," ",v," "))}));function w(t){
const{icon:e,message:r,rawHtml:o,doNotLimitWidth:i,maxWidth:n=200,maxHeight:s=200,zeroHeight:c=105,offsetHeight:d=0,solutionId:u,buttons:p}=t,h=a.useRef(null),[m,v]=a.useState(1);return a.useEffect((()=>{const t=h.current;if(t){const e=new ResizeObserver((0,l.default)(g,150));return e.observe(t),()=>e.disconnect()}}),[n,s,d,c]),a.createElement(x,{ref:h,message:r,icon:e,size:m,rawHtml:o,doNotLimitWidth:i,solutionId:u,buttons:p});function g(){const t=h.current;if(!t)return;const e=t.clientHeight+d;e<c?v(0):t.clientWidth<n||e<s?v(1):v(2)}}class b{constructor(){this._state={message:null,rawHtml:!1,doNotLimitWidth:!1,icon:void 0,backgroundColor:null,textColor:null,maxWidth:200,maxHeight:200,zeroHeight:105,offsetHeight:0,solutionId:void 0,buttons:void 0};const t=document.createElement("div");t.classList.add(C.errorCardRendererContainer),this.container=t}destroy(){n.unmountComponentAtNode(this.container)}update(t){this._state=Object.assign({},this._state,t),this._updateContainer(),this._render()}_updateContainer(){const{backgroundColor:t,textColor:e}=this._state;this.container.style.setProperty("--backgroundColor",t),this.container.style.setProperty("--textColor",e)}_render(){const{message:t,icon:e,rawHtml:r,doNotLimitWidth:o,maxWidth:i,maxHeight:l,zeroHeight:s,offsetHeight:c,solutionId:d,buttons:u}=this._state;n.render(t?a.createElement(w,{message:t,icon:e,rawHtml:r,doNotLimitWidth:o,maxWidth:i,maxHeight:l,zeroHeight:s,offsetHeight:c,solutionId:d,buttons:u}):a.createElement(a.Fragment,null),this.container)}}},602948:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.92 7.83 9 12.29l5.08-4.46-1-1.13L9 10.29l-4.09-3.6-.99 1.14Z"/></svg>'},779035:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="currentColor" d="m60 14-50 87h100L60 14Zm0 8.02L103.55 97h-87.1L60 22.02ZM58 75V51h4v24h-4Zm5 9a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/></svg>'},178080:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72" width="72" height="72"><path fill="currentColor" d="M36 10 7 58h58L36 10Zm0 3.9L61.2 56H10.8L36 13.9ZM35 44V30h2v14h-2Zm2.5 4.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"/></svg>'},911425:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="currentColor" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},229540:t=>{
t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72" width="72" height="72"><path fill="currentColor" d="M15 24a21 21 0 1 1 42 0v7.41l8.97 5.01 1.08.6-.82.94-7.77 8.82 2.34 2.53-1.47 1.36L57 48.15V69H46v-7h-6v5h-9V56h-6v13H15V48.15l-2.33 2.52-1.47-1.36 2.35-2.53-7.78-8.82-.82-.93 1.08-.6L15 31.4V24Zm0 9.7-6.9 3.87L15 45.4V33.7Zm42 11.7 6.91-7.83-6.9-3.87v11.7ZM36 5a19 19 0 0 0-19 19v43h6V54h10v11h5v-5h10v7h7V24A19 19 0 0 0 36 5Zm-5 19.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM42.5 26a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"/></svg>'},177678:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="currentColor" fill-rule="evenodd" d="M19 53a41.13 41.13 0 0 1 39-40.95V24h4V12.05A41.13 41.13 0 0 1 101 53H86v4h14.92c-.4 9.6-2.33 14.5-5.23 19.07-1.07 1.69-2.89 2.86-5.24 4.27l-.35.22c-2.12 1.26-4.64 2.77-6.34 5.07a20.08 20.08 0 0 0-2.46 4.78c-.3.81-.6 1.64-.86 2.42l-.01.03-.79 2.2a11.1 11.1 0 0 1-1.62 3.13c-.5.58-.96.81-1.59.81-1.02 0-1.42-.3-1.68-.6a8.03 8.03 0 0 1-1.11-2.25 9.58 9.58 0 0 0-2.06-3.45c-1.22-1.2-2.87-1.79-5.04-1.7a5.18 5.18 0 0 0-3.37 1.38 7.62 7.62 0 0 0-1.82 2.8 30.91 30.91 0 0 0-1.44 5.91l-.11.67c-.41 2.28-.81 4.2-1.51 5.55a3.5 3.5 0 0 1-1.03 1.3c-.33.22-.76.39-1.42.39a1.3 1.3 0 0 1-1.04-.46 5.71 5.71 0 0 1-1.08-2.15c-.68-2.13-1-5.03-1.35-8.26l-.02-.18c-.34-3.16-.73-6.64-1.66-9.35-.91-2.69-2.67-5.42-6.17-5.6-2.58-.13-4.56.41-6.15 1.42-1.53.97-2.54 2.3-3.3 3.36l-.33.45c-.66.92-1.12 1.56-1.7 2.05-.55.45-1.23.78-2.44.72-.29-.01-.74-.15-1.36-.67a10.78 10.78 0 0 1-2.05-2.46c-1.45-2.26-2.86-5.51-4.1-9.37A91.79 91.79 0 0 1 19.12 57H34v-4H19Zm-4 0C15 28.24 35.24 8 60 8s45 20.24 45 45c0 12.9-2.15 19.26-5.93 25.21-1.66 2.61-4.33 4.23-6.56 5.56l-.08.05c-2.35 1.41-4.24 2.55-5.45 4.2a16.23 16.23 0 0 0-1.94 3.82c-.29.75-.55 1.51-.82 2.3l-.02.06-.82 2.28a14.83 14.83 0 0 1-2.31 4.3 5.9 5.9 0 0 1-4.64 2.22 5.9 5.9 0 0 1-4.7-1.98c-.86-1-1.36-2.26-1.72-3.16l-.05-.13-.04-.1a5.95 5.95 0 0 0-1.13-2.07c-.29-.28-.8-.61-2.08-.56-.38.01-.62.13-.83.33-.25.23-.54.65-.82 1.35a27.9 27.9 0 0 0-1.22 5.17l-.05.28-.05.33c-.4 2.18-.88 4.7-1.9 6.69a7.45 7.45 0 0 1-2.28 2.72 6.32 6.32 0 0 1-3.72 1.13 5.29 5.29 0 0 1-3.97-1.73 9.55 9.55 0 0 1-1.97-3.66c-.8-2.53-1.15-5.78-1.48-8.8l-.04-.43c-.36-3.28-.72-6.28-1.47-8.48-.76-2.23-1.63-2.85-2.59-2.9-1.88-.1-3.02.3-3.8.8a8.33 8.33 0 0 0-2.21 2.32l-.28.38c-.62.89-1.42 2-2.44 2.86A7.23 7.23 0 0 1 30.42 95a6.33 6.33 0 0 1-3.73-1.59c-1.05-.87-2-2.04-2.86-3.37-1.72-2.68-3.26-6.3-4.55-10.32A93.67 93.67 0 0 1 15 53Zm43-12V66.16l1.23.51 12.86 5.38 1.54-3.69L62 63.5V41h-4Z"/></svg>'},244645:t=>{
t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72" width="72" height="72"><path fill="currentColor" fill-rule="evenodd" d="M21 31h-9A24.1 24.1 0 0 1 35 7.02V14h2V7.02A24.1 24.1 0 0 1 60 31h-9v2h8.97c-.13 4.05-.7 6.68-1.59 8.8-.88 2.09-2.77 3.67-4.91 5.47-2.05 1.72-4.35 3.64-5.35 6.32l-.3.82c-.37.98-.67 1.83-1.07 2.5-.47.8-.9 1.09-1.46 1.09-.62 0-.9-.2-1.09-.44-.26-.32-.43-.77-.68-1.45v-.02a5.85 5.85 0 0 0-1.15-2.07A3.52 3.52 0 0 0 39.53 53c-1.6.07-2.44 1.25-2.9 2.42a19.56 19.56 0 0 0-.87 3.56l-.08.4c-.25 1.4-.5 2.6-.95 3.47-.2.42-.44.7-.67.88-.23.16-.51.27-.92.27a.9.9 0 0 1-.72-.31 3.33 3.33 0 0 1-.64-1.29 26.49 26.49 0 0 1-.7-4.78l-.02-.12a26.01 26.01 0 0 0-.87-5.36c-.51-1.54-1.48-3.04-3.42-3.14a6.15 6.15 0 0 0-3.4.67 6.1 6.1 0 0 0-1.84 1.69l-.08.1-.1.12c-.35.46-.6.79-.93 1.03-.32.24-.75.43-1.54.39-.48-.02-1.23-.48-2.12-1.82a22.59 22.59 0 0 1-2.39-5.31A51.01 51.01 0 0 1 12.04 33H21v-2Zm-11 .18A26.1 26.1 0 0 1 36 5a26.1 26.1 0 0 1 26 26.18c0 5.27-.62 8.65-1.78 11.39-1.06 2.54-3.29 4.4-5.28 6.07l-.19.16c-2.13 1.79-3.97 3.39-4.76 5.5l-.26.7c-.37 1-.76 2.07-1.25 2.92-.64 1.1-1.62 2.08-3.2 2.08-1.2 0-2.04-.45-2.63-1.17a6.57 6.57 0 0 1-.96-1.9l-.05-.14a4.03 4.03 0 0 0-.71-1.39c-.22-.22-.56-.43-1.32-.4-.4.02-.77.25-1.13 1.16a18.04 18.04 0 0 0-.83 3.58 15.3 15.3 0 0 1-1.13 4.02c-.3.6-.71 1.16-1.28 1.58-.59.43-1.29.66-2.1.66a2.9 2.9 0 0 1-2.18-.95 5.25 5.25 0 0 1-1.09-2.07c-.44-1.44-.61-3.3-.78-5.05l-.02-.24c-.18-1.89-.36-3.63-.78-4.92-.43-1.3-.96-1.74-1.62-1.77a4.18 4.18 0 0 0-2.33.41 4.16 4.16 0 0 0-1.38 1.37 7.1 7.1 0 0 1-1.35 1.44 4.2 4.2 0 0 1-2.82.78c-1.52-.07-2.75-1.3-3.7-2.72-.99-1.49-1.88-3.51-2.62-5.79A52.32 52.32 0 0 1 10 31.2ZM35 23v13.81l.61.25 7.58 3.17.77-1.84L37 35.48V23h-2Z"/></svg>'}}]);