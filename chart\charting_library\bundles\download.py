import requests
import os
from urllib.parse import urlparse
import time

def download_file(url, save_path):
    """Download a file from URL and save it to the specified path"""
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # Download the file
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, stream=True)
        response.raise_for_status()
        
        # Save the file
        with open(save_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        
        print(f"✓ Downloaded: {os.path.basename(save_path)}")
        return True
        
    except requests.RequestException as e:
        print(f"✗ Failed to download {os.path.basename(save_path)}: {str(e)}")
        return False
    except Exception as e:
        print(f"✗ Error saving {os.path.basename(save_path)}: {str(e)}")
        return False

def main():
    # Base URL template
    base_url = "https://chart/charting_library/bundles/"
    
    # Save directory
    save_directory = r"C:\xampp\htdocs\top\chart\charting_library\bundles"
    
    # List of all filenames from your images
    filenames = [
        # JS Files
        "218.5f3e0e6cd93fad5e3d2e.js",
        "427.8e4ad2884751c78b5ebd.js",
        "1191.7eaa73b7c4e196f5e964.js",
        "1298.ef9292896ac7193ea04a.js",
        "1442.b2c013cd0d71ae9198e7.js",
        "1456.b82a647336b03fd9960f.js",
        "1553.eaf0cb371a2c4fa90bbf.js",
        "2227.b3ce716bc6a1eebf1a92.js",
        "2433.06b575c44f467c97bacf.js",
        "2538.13f4bc31d9a146bc25c4.js",
        "2614.3c6e9a4d2c016c8e0d98.js",
        "3010.b6767e7ba0734f8ea43b.js",
        "3054.6323f377d9660accde3b.js",
        "3251.3c8acb079b74b035c388.js",
        "3450.6298d8acc05971e75477.js",
        "3805.42495629e38f02c4d148.js",
        "3847.4a2226b0147b5a279dee.js",
        "4333.5833c0eb2dffcbc3ab13.js",
        "4952.1e54c133b22993b96a57.js",
        "5476.778f33a85e34c613bb7e.js",
        "5789.f84eb4cc44a8ece5086b.js",
        "6161.10c4a7de17f463d1d76e.js",
        "6212.f2b0954f9876e5dbec45.js",
        "6898.f753c8c58a80d1e8b237.js",
        "7125.f506fee64d0fbd3aac4a.js",
        "7335.f7017c32a7d4a05f3652.js",
        "7403.9a5d289733609ece1fe7.js",
        "7898.93482c7ce4146884bda0.js",
        "8402.2756e58e899131679cc9.js",
        "8524.1f8a90c800e6e2b3b5dd.js",
        "8687.4bb18f37c96aa9314d64.js",
        "9088.e5b04ee7028204603d5e.js",
        "9216.dce4735e29d10fe9a604.js",
        "9937.e9709e88abb24d2bb653.js",
        "bottom-widgetbar.6cd99a863ac9d00955f1.js",
        "change-interval-dialog.a8ba0df4c4a77167cc2a.js",
        "chart-bottom-toolbar.fd38479e975a1ac4fdd8.js",
        "chart-widget-gui.373398f680e71823f0f1.js",
        "drawing-toolbar.9f682ebb059580da885e.js",
        "en.680.004b61cbed26bd77c2f5.js",
        "en.1067.39a6f6638bd5cbc0e3a9.js",
        "en.1445.f90a91ddb479cd36c697.js",
        "en.1904.b23c76f1dd4da22ffcf5.js",
        "en.2199.5dbcea5dfbfa18bc6170.js",
        "en.2603.f9d2a02c886ec7640135.js",
        "en.2970.192d64e32a7edae012d7.js",
        "en.4662.4bb596a7e9afdc8b047c.js",
        "en.4821.a10db9c3614cc8c6daa2.js",
        "en.4829.73fb317fcfa2da965c86.js",
        "en.4962.a3f22bee213fb4ec46dd.js",
        "en.5127.00183b016a522bc4e6d6.js",
        "en.6173.98c05959b9e5f8d38113.js",
        "en.6342.96ca6cec41634eba0dcf.js",
        "en.6822.ee9307d05514c90ba8af.js",
        "en.7690.6d79ca989ab1377db6ba.js",
        "en.8189.619732a30f4aaf1ca1a4.js",
        "en.8370.8f53ecb203cb7ef46f74.js",
        "en.8975.1103e4b8aa7c7a60ddc4.js",
        "en.9179.1bb17110d4007c8fb7ef.js",
        "en.9762.7c9a1342de661f25c89b.js",
        "floating-toolbars.435f06c307950384d090.js",
        "footer-widget.08780bf0181c58031ae2.js",
        "get-error-card.a031f39f01dfc4896f5c.js",
        "header-toolbar.33da2545627b9608b949.js",
        "library.529d5b8c7f545237e54b.js",
        "line-tools-icons.b492c92d44816b2cfc80.js",
        "order-widget.1d82e2e1e9cb3f946588.js",
        "position-widget.b36b02fe1cc4cfc34e5a.js",
        "price-scale-mode-buttons-renderer.f005deb14baec3edb33f.js",
        "restricted-toolset.6f81af351cfbad2af083.js",
        "runtime.ea0bf94eec0f51895e4d.js",
        "spinner-renderer.20676b1f5bb8e3a5388e.js",
        "studies.df9d03d5d71d89aed43c.js",
        "study-pane-views.c62fc52b9a2dc4005c4b.js",
        "symbol-list-service.ff72f86845c1d337fda3.js",
        "symbol-search-dialog.f3e926b328070c8036b4.js",
        "terminal-configset.abbe3b2ddf1adcad2530.js",
        "toasts-groupable-layer-chart.a858a2d486d8da454954.js",
        "trading-custom-sources.8d00ec8d822b402ca34b.js",
        "trading-groups.9659877d7d96e0bb027b.js",
        "trading.c5c349137695482c030.js",
        "user-defined-bars-marks-tooltip.5ed365d69bbe08c5ca0d.js",
        "watchlist-widget.c7bd684646cd360803e2.js",
        "widgetbar.d988faecd0403223478e.js",
        
        # CSS Files
        "303.e9b6e523eb66dacf92bd.css",
        "422.bc23975c348a4f41dea4.css",
        "579.50ef343d165189abd0ba.css",
        "601.b6bc31b7ce369908d97d.css",
        "779.b84315f4350430cdb348.css",
        "1013.7a29c300c9788022d8a3.css",
        "1072.67a2846c0506e2e592be.css",
        "1135.a33c76f5549cc7102e61.css",
        "1249.7f5f09a1d671819fafa9.css",
        "1252.788a58021829bdae27fc.css",
        "1356.e2248aecf1ad4e148d65.css",
        "1380.5ba7850c7061f2d26173.css",
        "1389.9ce134321e5f7ed1c4ad.css",
        "1390.626f0a194297d6d23002.css",
        "1551.0163412c2c2bbec3323e.css",
        "1575.60bc11558d66aa690217.css",
        "1707.fbb750fd312778403036.css",
        "1728.f5824899227309b419bc.css",
        "2082.acf5e91fffdb18a2cf1.css",
        "2092.4b97d1e084a7f81e17f2.css",
        "2106.3418d3509b4f6a41c2eb.css",
        "2115.d4ce023e54009adf69b3.css",
        "2197.3c275591170ccafa3bbe.css",
        "2234.be3e2002889c3507ea77.css",
        "2440.8620e9f557ec49b4b3d7.css",
        "2444.3360c6e677bffb470a53.css",
        "2520.884e337706189fe0160c.css",
        "2564.cac75529a7aa17227aa7.css",
        "2568.3586586cffe61c1d9c0f.css",
        "2590.4a004f1a3b71d5105cb2.css",
        "2666.d7dd4a59f33a2f52cf86.css",
        "2736.c88f0ff04a966a2fb2df.css",
        "2743.a9087bf79796576ba8f5.css",
        "2849.d213553651036775ff1.css",
        "3062.553fc65e7c6f62255d16.css",
        "3362.2476428ae07d34323af5.css",
        "3473.e93220a4080eb191e0fd.css",
        "3565.f3d891b275768e3e0a5f.css",
        "3703.fbb750fd312778403036.css",
        "3782.f5f264bb4298497c0f97.css",
        "3799.8fe69f925e501b5d9dea.css",
        "3828.b87cd06c9d4e7baa6eda.css",
        "3953.7146f14703227db023af.css",
        "4057.400f57042c58b6946a7c.css",
        "4066.eba4ddabfe5309662e8a.css",
        "4106.8577632fdab29ee53ddf.css",
        "4125.839d0d8af876d7e15703.css",
        "4256.1d39c3c8dc435b82e4c6.css",
        "4353.9eade1db5c4beb02071a.css",
        "4510.35411b67165343bc4949.css",
        "4524.d6f97e44b70e5e01f7d4.css",
        "4600.362fa6a7ab1f3e3b06c4.css",
        "4752.e86f4005171a6af35829.css",
        "4797.ecf541f2301446a3089c.css",
        "4920.d7c8574fa306aeadd297.css",
        "4938.a07e1da10e414c329264.css",
        "4986.8b484bfeff80564f9f879.css",
        "5083.a9187278de5ab079b6d2.css",
        "5323.05b152d9c203bbe1e861.css",
        "5375.132067dc73c5743d825f.css",
        "5387.6dc6a5ec31f9d97dc855.css",
        "5446.1f5883a75f4d7d613e20.css",
        "5480.62c8085685951c0bc278.css",
        "5621.1dd4b446247208c1269.css",
        "5758.7723043bc419795133c2.css",
        "5816.44efac501e6cf354a70b.css",
        "5826.c70dad3fc1bd0fd0f22d.css",
        "5881.429d5b055ce817909b34.css",
        "5891.24783caa1cacf6c87546.css",
        "5975.a3e49a7b95404261d4eb.css",
        "6164.3d69261024d7c24d7b64.css",
        "6190.ef0bd3010b1c05e755bc.css",
        "6243.1cbe83f8e15217dddb9b.css",
        "6262.cce9c2d0878310b9ee03.css",
        "6445.d2b2f28850f8c828a4d7.css",
        "6489.9070beae9e8cef21670a.css",
        "6750.de7185fa4470c6688792.css",
        "6752.a496d8cf9d524daacf28.css",  # Missing file added
        "6828.3bcf3e2b04d67b8ca804.css",
        "6842.f6f5a9b5ab991d9de920.css",
        "6983.cadd6c828bd2c0930e97.css",
        "7001.d167a8793031fa802176.css",
        "7280.7c6e45398eccc266b2a0.css",
        "7384.aefeb9db8e938fbfb1dc.css",
        "7414.8b6b93d5fc5e20706c25.css",
        "7431.ef930db5ef4b533a7904.css",
        "7762.c7ba43013335f9bc0a67.css",
        "7849.2e870d1d6307ad5e1004.css",
        "7854.77fde9897992fb67135b.css",
        "7935.5553a5da7a4e4bd90438.css",
        "8146.4982384e389f11dd6517.css",
        "8185.3f7aa75dce6ef9bcaeb.css",
        "8357.8fc764ff0ed9b29a01ab.css",
        "8473.bd22fdbcb9baef45b965.css",
        "8666.d1d8147337bae3fa6372.css",
        "8985.10034c1258391b9f008c.css",
        "9162.7dd9df5592fa3bfd0827.css",
        "9255.574882a5338ad0774967.css",
        "9258.127a4c4030a8c2724b98.css",
        "9296.df3cd88e89f770d4753.css",
        "9325.f2e1edd6097be38e73da.css",
        "9388.e55a351c0813be7d0c20.css",
        "9481.63a2692b383c39f8a3d1.css",
        "9531.9edf4ac64b0547e8a926.css",
        "9561.892974d637b862abee04.css",
        "9642.60f4b13d8eb012aa4791.css",
        "9704.004638546a58c07591ff.css",
        "9753.b7b1c5568e3084458241.css",
        "9766.631d966499ce5a6b82a9.css"
    ]
    
    print(f"Starting download of {len(filenames)} files...")
    print(f"Base URL: {base_url}")
    print(f"Save directory: {save_directory}")
    print("-" * 60)
    
    successful_downloads = 0
    failed_downloads = 0
    
    for i, filename in enumerate(filenames, 1):
        print(f"\n[{i}/{len(filenames)}] Processing: {filename}")
        
        # Construct full URL
        file_url = base_url + filename
        
        # Construct save path
        save_path = os.path.join(save_directory, filename)
        
        # Download file
        if download_file(file_url, save_path):
            successful_downloads += 1
        else:
            failed_downloads += 1
        
        # Small delay between downloads to be respectful
        time.sleep(0.5)
    
    print("\n" + "=" * 60)
    print(f"Download Summary:")
    print(f"✓ Successful: {successful_downloads}")
    print(f"✗ Failed: {failed_downloads}")
    print(f"Total: {len(filenames)}")
    print("=" * 60)

if __name__ == "__main__":
    main()