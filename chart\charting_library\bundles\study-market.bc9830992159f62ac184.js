(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6456,9790],{36136:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},268976:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC",iconOnly:"iconOnly-D4RPB3ZC",link:"link-D4RPB3ZC",brand:"brand-D4RPB3ZC",primary:"primary-D4RPB3ZC",secondary:"secondary-D4RPB3ZC",gray:"gray-D4RPB3ZC",green:"green-D4RPB3ZC",red:"red-D4RPB3ZC",black:"black-D4RPB3ZC","black-friday":"black-friday-D4RPB3ZC","cyber-monday":"cyber-monday-D4RPB3ZC",xsmall:"xsmall-D4RPB3ZC",startIconWrap:"startIconWrap-D4RPB3ZC",endIconWrap:"endIconWrap-D4RPB3ZC",withStartIcon:"withStartIcon-D4RPB3ZC",withEndIcon:"withEndIcon-D4RPB3ZC",small:"small-D4RPB3ZC",medium:"medium-D4RPB3ZC",large:"large-D4RPB3ZC",xlarge:"xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC",adjustPosition:"adjustPosition-D4RPB3ZC",firstRow:"firstRow-D4RPB3ZC",firstCol:"firstCol-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC",textWrap:"textWrap-D4RPB3ZC",multilineContent:"multilineContent-D4RPB3ZC",secondaryText:"secondaryText-D4RPB3ZC",primaryText:"primaryText-D4RPB3ZC"}},253330:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},808473:e=>{e.exports={dialog:"dialog-b8SxMnzX",wrapper:"wrapper-b8SxMnzX",separator:"separator-b8SxMnzX",bounded:"bounded-b8SxMnzX"}},780822:e=>{e.exports={"small-height-breakpoint":"screen and (max-height: 360px)",container:"container-BZKENkhT",unsetAlign:"unsetAlign-BZKENkhT",title:"title-BZKENkhT",subtitle:"subtitle-BZKENkhT",textWrap:"textWrap-BZKENkhT",ellipsis:"ellipsis-BZKENkhT",close:"close-BZKENkhT",icon:"icon-BZKENkhT"}},559086:e=>{e.exports={container:"container-qm7Rg5MB",mobile:"mobile-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},87886:e=>{e.exports={wrapper:"wrapper-nGEmjtaX",container:"container-nGEmjtaX",tab:"tab-nGEmjtaX",active:"active-nGEmjtaX",title:"title-nGEmjtaX",icon:"icon-nGEmjtaX",withoutIcon:"withoutIcon-nGEmjtaX",titleText:"titleText-nGEmjtaX",nested:"nested-nGEmjtaX",isTablet:"isTablet-nGEmjtaX",isMobile:"isMobile-nGEmjtaX",showLastDivider:"showLastDivider-nGEmjtaX",medium:"medium-nGEmjtaX",large:"large-nGEmjtaX",withoutArrow:"withoutArrow-nGEmjtaX",accessible:"accessible-nGEmjtaX"}},671921:e=>{
e.exports={title:"title-z9fs4j4t",small:"small-z9fs4j4t",normal:"normal-z9fs4j4t",large:"large-z9fs4j4t"}},872910:e=>{e.exports={container:"container-XOHpda28",mobile:"mobile-XOHpda28"}},974136:e=>{e.exports={title:"title-cIIj4HrJ",disabled:"disabled-cIIj4HrJ",icon:"icon-cIIj4HrJ",locked:"locked-cIIj4HrJ",open:"open-cIIj4HrJ",actionIcon:"actionIcon-cIIj4HrJ",selected:"selected-cIIj4HrJ",codeIcon:"codeIcon-cIIj4HrJ",solutionIcon:"solutionIcon-cIIj4HrJ"}},970307:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",container:"container-WeNdU0sq",mobile:"mobile-WeNdU0sq",selected:"selected-WeNdU0sq",disabled:"disabled-WeNdU0sq",favorite:"favorite-WeNdU0sq",highlighted:"highlighted-WeNdU0sq","highlight-animation":"highlight-animation-WeNdU0sq",badge:"badge-WeNdU0sq",main:"main-WeNdU0sq",paddingLeft:"paddingLeft-WeNdU0sq",author:"author-WeNdU0sq",likes:"likes-WeNdU0sq",actions:"actions-WeNdU0sq",isActive:"isActive-WeNdU0sq",mobileText:"mobileText-WeNdU0sq"}},815511:e=>{e.exports={container:"container-hrZZtP0J"}},177335:e=>{e.exports={container:"container-jiYDR9Eu",centerElement:"centerElement-jiYDR9Eu",contentWrap:"contentWrap-jiYDR9Eu",noticeShowed:"noticeShowed-jiYDR9Eu",icon:"icon-jiYDR9Eu",textWrap:"textWrap-jiYDR9Eu"}},528598:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",dialog:"dialog-I087YV6b",dialogLibrary:"dialogLibrary-I087YV6b",contentContainer:"contentContainer-I087YV6b",listContainer:"listContainer-I087YV6b",scroll:"scroll-I087YV6b",sidebarContainer:"sidebarContainer-I087YV6b",noContentBlock:"noContentBlock-I087YV6b",tabWithHint:"tabWithHint-I087YV6b",solution:"solution-I087YV6b",mobileSidebarItem:"mobileSidebarItem-I087YV6b"}},464856:e=>{e.exports={container:"container-QcG0kDOU",image:"image-QcG0kDOU",title:"title-QcG0kDOU",description:"description-QcG0kDOU",button:"button-QcG0kDOU"}},998992:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",active:"active-_FRQhM5Y",checked:"checked-_FRQhM5Y"}},532248:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},547625:e=>{e.exports={separator:"separator-Pf4rIzEt"}},805184:(e,t,n)=>{"use strict";var i,r,o;function a(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function s(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function l(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}n.d(t,{Button:()=>h}),function(e){e.Primary="primary",e.Success="success",e.Default="default",e.Danger="danger"}(i||(i={})),function(e){e.Small="s",e.Medium="m",e.Large="l"}(r||(r={})),function(e){e.Default="default",e.Stroke="stroke"}(o||(o={}));var c=n(50959),d=n(171529);function u(e){const{intent:t,size:n,appearance:i,useFullWidth:r,icon:o,...c}=e;return{...c,color:s(t),size:l(n),variant:a(i),stretch:r,startIcon:o}}function h(e){return c.createElement(d.SquareButton,{...u(e)})}},389986:(e,t,n)=>{"use strict";n.d(t,{CloseButton:()=>u})
;var i=n(50959),r=n(270762),o=n(117105),a=n(315130),s=n(238822),l=n(663346),c=n(534983);function d(e="large"){switch(e){case"large":return o;case"medium":default:return a;case"small":return s;case"xsmall":return l;case"xxsmall":return c}}const u=i.forwardRef(((e,t)=>i.createElement(r.NavButton,{...e,ref:t,icon:d(e.size)})))},270762:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var i=n(50959),r=n(497754),o=n(72571),a=(n(15378),n(36136));function s(e){const{size:t="large",preservePaddings:n,isLink:i,flipIconOnRtl:o,className:s}=e;return r(a["nav-button"],a[`size-${t}`],n&&a["preserve-paddings"],o&&a["flip-icon"],i&&a.link,s)}function l(e){const{children:t,icon:n}=e;return i.createElement(i.Fragment,null,i.createElement("span",{className:a.background}),i.createElement(o.Icon,{icon:n,className:a.icon,"aria-hidden":!0}),t&&i.createElement("span",{className:a["visually-hidden"]},t))}const c=(0,i.forwardRef)(((e,t)=>{const{icon:n,type:r="button",preservePaddings:o,flipIconOnRtl:a,size:c,"aria-label":d,...u}=e;return i.createElement("button",{...u,className:s({...e,children:d}),ref:t,type:r},i.createElement(l,{icon:n},d))}));c.displayName="NavButton";var d=n(591365),u=n(273388);(0,i.forwardRef)(((e,t)=>{const{icon:n,renderComponent:r,"aria-label":o,...a}=e,c=null!=r?r:d.CustomComponentDefaultLink;return i.createElement(c,{...a,className:s({...e,children:o,isLink:!0}),reference:(0,u.isomorphicRef)(t)},i.createElement(l,{icon:n},o))})).displayName="NavAnchorButton"},171529:(e,t,n)=>{"use strict";n.d(t,{SquareButton:()=>f});var i=n(50959),r=n(497754),o=n(331774),a=n(72571),s=n(268976),l=n.n(s);const c="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text apply-common-tooltip";function d(e){const{color:t="brand",size:n="medium",variant:i="primary",stretch:a=!1,icon:s,startIcon:d,endIcon:u,iconOnly:h=!1,className:p,isGrouped:m,cellState:g,disablePositionAdjustment:f=!1,primaryText:_,secondaryText:v,isAnchor:w=!1}=e,y=function(e){let t="";return 0!==e&&(1&e&&(t=r(t,l()["no-corner-top-left"])),2&e&&(t=r(t,l()["no-corner-top-right"])),4&e&&(t=r(t,l()["no-corner-bottom-right"])),8&e&&(t=r(t,l()["no-corner-bottom-left"]))),t}((0,o.getGroupCellRemoveRoundBorders)(g));return r(p,l().button,l()[n],l()[t],l()[i],a&&l().stretch,(s||d)&&l().withStartIcon,u&&l().withEndIcon,h&&l().iconOnly,y,m&&l().grouped,m&&!f&&l().adjustPosition,m&&g.isTop&&l().firstRow,m&&g.isLeft&&l().firstCol,_&&v&&l().multilineContent,w&&l().link,c)}function u(e){const{startIcon:t,icon:n,iconOnly:o,children:s,endIcon:d,primaryText:u,secondaryText:h}=e,p=null!=t?t:n,m=!(t||n||d||o)&&!s&&u&&h;return i.createElement(i.Fragment,null,p&&i.createElement(a.Icon,{icon:p,className:l().startIconWrap}),s&&i.createElement("span",{className:l().content},s),d&&!o&&i.createElement(a.Icon,{icon:d,className:l().endIconWrap}),m&&function(e){return e.primaryText&&e.secondaryText&&i.createElement("div",{className:r(l().textWrap,c)},i.createElement("span",{className:l().primaryText
}," ",e.primaryText," "),"string"==typeof e.secondaryText?i.createElement("span",{className:l().secondaryText}," ",e.secondaryText," "):i.createElement("span",{className:l().secondaryText},i.createElement("span",null,e.secondaryText.firstLine),i.createElement("span",null,e.secondaryText.secondLine)))}(e))}var h=n(601198),p=n(380327),m=n(800417);function g(e){const{className:t,color:n,variant:i,size:r,stretch:o,animated:a,icon:s,iconOnly:l,startIcon:c,endIcon:d,primaryText:u,secondaryText:h,...p}=e;return{...p,...(0,m.filterDataProps)(e),...(0,m.filterAriaProps)(e)}}function f(e){const{reference:t,tooltipText:n,...r}=e,{isGrouped:o,cellState:a,disablePositionAdjustment:s}=(0,i.useContext)(p.ControlGroupContext),l=d({...r,isGrouped:o,cellState:a,disablePositionAdjustment:s});return i.createElement("button",{...g(r),className:l,ref:t,"data-overflow-tooltip-text":null!=n?n:e.primaryText?[e.primaryText,e.secondaryText].join(" "):(0,h.getTextForTooltip)(e.children)},i.createElement(u,{...r}))}n(15378)},15378:(e,t,n)=>{"use strict";var i,r,o,a;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(i||(i={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(r||(r={})),function(e){e.Brand="brand",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(o||(o={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(a||(a={}))},380327:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>i});const i=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},331774:(e,t,n)=>{"use strict";function i(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>i})},409245:(e,t,n)=>{"use strict";function i(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>i})},591365:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>o});var i=n(50959),r=n(409245);function o(e){return i.createElement("a",{...(0,r.renameRef)(e)})}i.PureComponent},855393:(e,t,n)=>{"use strict";n.d(t,{useIsomorphicLayoutEffect:()=>r});var i=n(50959);function r(e,t){("undefined"==typeof window?i.useEffect:i.useLayoutEffect)(e,t)}},778199:(e,t,n)=>{"use strict";function i(e,t,n,i,r){function o(r){if(e>r.timeStamp)return;const o=r.target;void 0!==n&&null!==t&&null!==o&&o.ownerDocument===i&&(t.contains(o)||n(r))}return r.click&&i.addEventListener("click",o,!1),r.mouseDown&&i.addEventListener("mousedown",o,!1),r.touchEnd&&i.addEventListener("touchend",o,!1),r.touchStart&&i.addEventListener("touchstart",o,!1),()=>{i.removeEventListener("click",o,!1),i.removeEventListener("mousedown",o,!1),i.removeEventListener("touchend",o,!1),
i.removeEventListener("touchstart",o,!1)}}n.d(t,{addOutsideEventListener:()=>i})},908783:(e,t,n)=>{"use strict";n.d(t,{useOutsideEvent:()=>a});var i=n(50959),r=n(855393),o=n(778199);function a(e){const{click:t,mouseDown:n,touchEnd:a,touchStart:s,handler:l,reference:c}=e,d=(0,i.useRef)(null),u=(0,i.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,r.useIsomorphicLayoutEffect)((()=>{const e={click:t,mouseDown:n,touchEnd:a,touchStart:s},i=c?c.current:d.current;return(0,o.addOutsideEventListener)(u.current,i,l,document,e)}),[t,n,a,s,l]),c||d}},72571:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>r});var i=n(50959);const r=i.forwardRef(((e,t)=>{const{icon:n="",title:r,ariaLabel:o,ariaLabelledby:a,ariaHidden:s,...l}=e,c=!!(r||o||a);return i.createElement("span",{...l,ref:t,role:"img","aria-label":o,"aria-labelledby":a,"aria-hidden":s||!c,title:r,dangerouslySetInnerHTML:{__html:n}})}))},682925:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>r,SlotContext:()=>o});var i=n(50959);class r extends i.Component{shouldComponentUpdate(){return!1}render(){return i.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const o=i.createContext(null)},672511:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>a});var i=n(50959),r=n(497754),o=n(843442);n(907727);function a(e){const t=r(e.className,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${o.spinnerSizeMap[e.size||o.DEFAULT_SIZE]}`);return i.createElement("div",{className:t,style:e.style,role:"progressbar"})}},800417:(e,t,n)=>{"use strict";function i(e){return o(e,a)}function r(e){return o(e,s)}function o(e,t){const n=Object.entries(e).filter(t),i={};for(const[e,t]of n)i[e]=t;return i}function a(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function s(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>r,filterDataProps:()=>i,filterProps:()=>o,isAriaAttribute:()=>s,isDataAttribute:()=>a})},601198:(e,t,n)=>{"use strict";n.d(t,{getTextForTooltip:()=>a});var i=n(50959);const r=e=>(0,i.isValidElement)(e)&&Boolean(e.props.children),o=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",a=e=>Array.isArray(e)||(0,i.isValidElement)(e)?i.Children.toArray(e).reduce(((e,t)=>{let n="";return n=(0,i.isValidElement)(t)&&r(t)?a(t.props.children):(0,i.isValidElement)(t)&&!r(t)?"":o(t),e.concat(n)}),"").trim():o(e)},273388:(e,t,n)=>{"use strict";function i(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function r(e){return i([e])}n.d(t,{isomorphicRef:()=>r,mergeRefs:()=>i})},801808:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>o,getRootOverlapManager:()=>s});var i=n(650151);class r{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class o{constructor(e=document){this._storage=new r,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){
const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e);if(void 0!==n)return n;this.registerWindow(e);const i=this._document.createElement("div");if(i.style.position=t.position,i.style.zIndex=this._index.toString(),i.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(i);else if(t.index<=0)this._container.insertBefore(i,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(i,e)}}else"reverse"===t.direction?this._container.insertBefore(i,this._container.firstChild):this._container.appendChild(i);return this._windows.set(e,i),++this._index,i}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,n)=>{e.hasAttribute("data-focus-trap")&&e.setAttribute("data-focus-trap",e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const a=new WeakMap;function s(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,i.ensureDefined)(a.get(t));{const t=new o(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return a.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}var l;!function(e){e[e.BaseZindex=150]="BaseZindex"}(l||(l={}))},110963:(e,t,n)=>{"use strict";n.d(t,{useHintShowAnimation:()=>r});var i=n(50959);function r(e){const[t,n]=(0,i.useState)(!1);return(0,i.useLayoutEffect)((()=>{const t=setTimeout((()=>n(!0)),50),i=setTimeout((()=>n(!1)),null!=e?e:2500);return()=>{clearTimeout(t),clearTimeout(i)}}),[]),t}},285089:(e,t,n)=>{"use strict";n.d(t,{setFixedBodyState:()=>l});var i=n(735922);const r=()=>!window.matchMedia("screen and (min-width: 768px)").matches,o=()=>!window.matchMedia("screen and (min-width: 1280px)").matches;let a=0,s=!1;function l(e){const{body:t}=document,n=t.querySelector(".widgetbar-wrap");if(e&&1==++a){const e=(0,i.getCSSProperty)(t,"overflow"),r=(0,i.getCSSPropertyNumericValue)(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&((0,i.setStyle)(n,"right",`${(0,i.getScrollbarWidth)()}px`),t.style.paddingRight=`${r+(0,i.getScrollbarWidth)()}px`,s=!0),t.classList.add("i-no-scroll")}else if(!e&&a>0&&0==--a&&(t.classList.remove("i-no-scroll"),s)){(0,i.setStyle)(n,"right","0px");let e=0;e=n?(l=(0,i.getContentWidth)(n),r()?0:o()?45:Math.min(Math.max(l,45),450)):0,
t.scrollHeight<=t.clientHeight&&(e-=(0,i.getScrollbarWidth)()),t.style.paddingRight=(e<0?0:e)+"px",s=!1}var l}},651674:(e,t,n)=>{"use strict";n.d(t,{createReactRoot:()=>h});var i=n(50959),r=n(632227),o=n(904237);const a=(0,i.createContext)({isOnMobileAppPage:()=>!1,isRtl:!1,locale:{language:"en"}});var s=n(69111),l=n(431520),c=n(887859);const d={iOs:"old",android:"new",old:"old",new:"new",any:"any"};function u(e){const[t]=(0,i.useState)({isOnMobileAppPage:e=>(0,s.isOnMobileAppPage)(d[e]),isRtl:(0,l.isRtl)(),locale:(0,c.getLocale)()});return i.createElement(a.Provider,{value:t},e.children)}function h(e,t,n="legacy"){const a=i.createElement(u,null,e);if("modern"===n){const e=(0,o.createRoot)(t);return e.render(a),{render(t){e.render(i.createElement(u,null,t))},unmount(){e.unmount()}}}return r.render(a,t),{render(e){r.render(i.createElement(u,null,e),t)},unmount(){r.unmountComponentAtNode(t)}}}},887859:(e,t,n)=>{"use strict";n.d(t,{getLocale:()=>a,getLocaleIso:()=>s});var i=n(650151)
;const r=JSON.parse('{"ar_AE":{"language":"ar","language_name":"العربية","flag":"sa","geoip_code":"sa","countries_with_this_language":["ae","bh","dj","dz","eg","er","iq","jo","km","kw","lb","ly","ma","mr","om","qa","sa","sd","so","sy","td","tn","ye"],"priority":500,"dir":"rtl","iso":"ar","iso_639_3":"arb","show_on_widgets":true,"global_name":"Arabic"},"br":{"language":"pt","language_name":"Português","flag":"br","geoip_code":"br","priority":650,"iso":"pt","iso_639_3":"por","show_on_widgets":true,"global_name":"Portuguese"},"ca_ES":{"language":"ca_ES","language_name":"Català","flag":"es","geoip_code":"es","priority":745,"iso":"ca","iso_639_3":"cat","disabled":true,"show_on_widgets":true,"global_name":"Catalan"},"cs":{"language":"cs","language_name":"Czech","flag":"cz","geoip_code":"cz","priority":718,"iso":"cs","iso_639_3":"ces","show_on_widgets":true,"global_name":"Czech","is_in_european_union":true,"isBattle":true},"de_DE":{"language":"de","language_name":"Deutsch","flag":"de","geoip_code":"de","countries_with_this_language":["at","ch"],"priority":756,"iso":"de","iso_639_3":"deu","show_on_widgets":true,"global_name":"German","is_in_european_union":true},"en":{"language":"en","language_name":"English","flag":"us","geoip_code":"us","priority":1000,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"English","is_only_recommended_tw_autorepost":true},"es":{"language":"es","language_name":"Español","flag":"es","geoip_code":"es","countries_with_this_language":["mx","ar","ve","cl","co","pe","uy","py","cr","gt","c","bo","pa","pr"],"priority":744,"iso":"es","iso_639_3":"spa","show_on_widgets":true,"global_name":"Spanish","is_in_european_union":true},"fa_IR":{"language":"fa","language_name":"فارسى","flag":"ir","geoip_code":"ir","priority":480,"dir":"rtl","iso":"fa","iso_639_3":"fas","show_on_widgets":false,"global_name":"Iranian","disabled":true},"fr":{"language":"fr","language_name":"Français","flag":"fr","geoip_code":"fr","priority":750,"iso":"fr","iso_639_3":"fra","show_on_widgets":true,"global_name":"French","is_in_european_union":true},"he_IL":{"language":"he_IL","language_name":"עברית","flag":"il","geoip_code":"il","priority":490,"dir":"rtl","iso":"he","iso_639_3":"heb","show_on_widgets":true,"global_name":"Israeli"},"hu_HU":{"language":"hu_HU","language_name":"Magyar","flag":"hu","geoip_code":"hu","priority":724,"iso":"hu","iso_639_3":"hun","show_on_widgets":true,"global_name":"Hungarian","is_in_european_union":true,"disabled":true},"id":{"language":"id_ID","language_name":"Bahasa Indonesia","flag":"id","geoip_code":"id","priority":648,"iso":"id","iso_639_3":"ind","show_on_widgets":true,"global_name":"Indonesian"},"in":{"language":"en","language_name":"English ‎(India)‎","flag":"in","geoip_code":"in","priority":800,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"Indian"},"it":{"language":"it","language_name":"Italiano","flag":"it","geoip_code":"it","priority":737,"iso":"it","iso_639_3":"ita","show_on_widgets":true,"global_name":"Italian","is_in_european_union":true},"ja":{"language":"ja","language_name":"日本語","flag":"jp","geoip_code":"jp","priority":600,"iso":"ja","iso_639_3":"jpn","show_on_widgets":true,"global_name":"Japanese"},"kr":{"language":"ko","language_name":"한국어","flag":"kr","geoip_code":"kr","priority":550,"iso":"ko","iso_639_3":"kor","show_on_widgets":true,"global_name":"Korean"},"ms_MY":{"language":"ms_MY","language_name":"Bahasa Melayu","flag":"my","geoip_code":"my","priority":647,"iso":"ms","iso_639_3":"zlm","show_on_widgets":true,"global_name":"Malaysian"},"pl":{"language":"pl","language_name":"Polski","flag":"pl","geoip_code":"pl","priority":725,"iso":"pl","iso_639_3":"pol","show_on_widgets":true,"global_name":"Polish","is_in_european_union":true},"ru":{"language":"ru","language_name":"Русский","flag":"ru","geoip_code":"ru","countries_with_this_language":["am","by","kg","kz","md","tj","tm","uz"],"priority":700,"iso":"ru","iso_639_3":"rus","show_on_widgets":true,"global_name":"Russian","is_only_recommended_tw_autorepost":true},"sv_SE":{"language":"sv","language_name":"Svenska","flag":"se","geoip_code":"se","priority":723,"iso":"sv","iso_639_3":"swe","show_on_widgets":true,"global_name":"Swedish","is_in_european_union":true},"th_TH":{"language":"th","language_name":"ภาษาไทย","flag":"th","geoip_code":"th","priority":646,"iso":"th","iso_639_3":"tha","show_on_widgets":true,"global_name":"Thai"},"tr":{"language":"tr","language_name":"Türkçe","flag":"tr","geoip_code":"tr","priority":713,"iso":"tr","iso_639_3":"tur","show_on_widgets":true,"global_name":"Turkish","is_only_recommended_tw_autorepost":true},"vi_VN":{"language":"vi","language_name":"Tiếng Việt","flag":"vn","geoip_code":"vn","priority":645,"iso":"vi","iso_639_3":"vie","show_on_widgets":true,"global_name":"Vietnamese"},"zh_CN":{"language":"zh","language_name":"简体中文","flag":"cn","geoip_code":"cn","countries_with_this_language":["zh"],"priority":537,"iso":"zh-Hans","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Chinese"},"zh_TW":{"language":"zh_TW","language_name":"繁體中文","flag":"tw","geoip_code":"tw","countries_with_this_language":["hk"],"priority":536,"iso":"zh-Hant","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Taiwanese"},"el":{"language":"el","language_name":"Greek","flag":"gr","geoip_code":"gr","priority":625,"iso":"el","iso_639_3":"ell","global_name":"Greek","is_in_european_union":true,"isBattle":true},"nl_NL":{"language":"nl_NL","language_name":"Dutch","flag":"nl","geoip_code":"nl","priority":731,"iso":"nl","iso_639_3":"nld","global_name":"Dutch","is_in_european_union":true,"isBattle":true},"ro":{"language":"ro","language_name":"Romanian","flag":"ro","geoip_code":"ro","priority":707,"iso":"ro","iso_639_3":"ron","global_name":"Romanian","is_in_european_union":true,"isBattle":true}}'),o=function(){
const e=document.querySelectorAll("link[rel~=link-locale][data-locale]");if(0===e.length)return r;const t={};return e.forEach((e=>{const n=(0,i.ensureNotNull)(e.getAttribute("data-locale"));t[n]={...r[n],href:e.href}})),t}();function a(e){return e=e||window.locale,o[e]}function s(e){var t;return e=e||window.locale,null===(t=o[e])||void 0===t?void 0:t.iso}},996038:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>r});var i=n(253330);const r={SmallHeight:i["small-height-breakpoint"],TabletSmall:i["tablet-small-breakpoint"],TabletNormal:i["tablet-normal-breakpoint"]}},533408:(e,t,n)=>{"use strict";n.d(t,{AdaptivePopupDialog:()=>I});var i=n(50959),r=n(650151),o=n(497754),a=n.n(o),s=n(180185),l=n(431520),c=n(698043),d=n(59216),u=n(494707),h=n(996038),p=n(930052),m=n(910549);var g=n(206594),f=n(559410),_=n(609838),v=n(389986),w=n(190410),y=n(780822);function b(e){const{title:t,titleTextWrap:r=!1,subtitle:o,showCloseIcon:s=!0,onClose:l,onCloseButtonKeyDown:c,renderBefore:d,renderAfter:u,draggable:h,className:p,unsetAlign:m,closeAriaLabel:g=_.t(null,void 0,n(47742)),closeButtonReference:f}=e,[b,C]=(0,i.useState)(!1);return i.createElement(w.DialogHeaderContext.Provider,{value:{setHideClose:C}},i.createElement("div",{className:a()(y.container,p,(o||m)&&y.unsetAlign)},d,i.createElement("div",{"data-dragg-area":h,className:y.title},i.createElement("div",{className:a()(r?y.textWrap:y.ellipsis)},t),o&&i.createElement("div",{className:a()(y.ellipsis,y.subtitle)},o)),u,s&&!b&&i.createElement(v.CloseButton,{className:y.close,"data-name":"close","aria-label":g,onClick:l,onKeyDown:c,ref:f,size:"medium",preservePaddings:!0})))}var C=n(273388),x=n(800417),E=n(440891),S=n(808473);const R={vertical:20},N={vertical:0};class I extends i.PureComponent{constructor(){super(...arguments),this._controller=null,this._reference=null,this._orientationMediaQuery=null,this._embedResizerOverridesEnabled=E.enabled("embed_resizer_overrides"),this._renderChildren=(e,t)=>(this._controller=e,this.props.render({requestResize:this._requestResize,centerAndFit:this._centerAndFit,isSmallWidth:t})),this._handleReference=e=>this._reference=e,this._handleCloseBtnClick=()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleClose()},this._handleClose=()=>{this.props.onClose()},this._handleOpen=()=>{void 0!==this.props.onOpen&&this.props.isOpened&&this.props.onOpen(this.props.fullScreen||window.matchMedia(h.DialogBreakpoints.TabletSmall).matches)},this._handleKeyDown=e=>{if(!e.defaultPrevented){if(this.props.onKeyDown&&this.props.onKeyDown(e),27===(0,s.hashFromEvent)(e)){if(e.defaultPrevented)return;if(this.props.forceCloseOnEsc&&this.props.forceCloseOnEsc())return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose();const{activeElement:n}=document;if(null!==n){if(e.preventDefault(),"true"===(t=n).getAttribute("data-haspopup")&&"true"!==t.getAttribute("data-expanded"))return void this._handleClose();const i=this._reference;if(null!==i&&(0,c.isTextEditingField)(n))return void i.focus()
;if(null==i?void 0:i.contains(n))return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose()}}var t,n;(function(e){if("function"==typeof e)return e();return Boolean(e)})(this.props.disableTabNavigationContainment)||(n=e,[9,s.Modifiers.Shift+9].includes((0,s.hashFromEvent)(n))&&n.stopPropagation())}},this._requestResize=()=>{null!==this._controller&&this._controller.recalculateBounds()},this._centerAndFit=()=>{null!==this._controller&&this._controller.centerAndFit()},this._calculatePositionWithOffsets=(e,t)=>{const n=(0,r.ensureDefined)(this.props.fullScreenViewOffsets).value();return{top:n.top,left:(0,l.isRtl)()?-n.right:n.left,width:t.clientWidth-n.left-n.right,height:t.clientHeight-n.top-n.bottom}}}componentDidMount(){this.props.ignoreClosePopupsAndDialog||f.subscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),this._handleOpen(),void 0!==this.props.onOpen&&(this._orientationMediaQuery=window.matchMedia("(orientation: portrait)"),this._orientationMediaQuery.addEventListener("change",this._handleOpen)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.subscribe(this._requestResize)}componentWillUnmount(){this.props.ignoreClosePopupsAndDialog||f.unsubscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),null!==this._orientationMediaQuery&&this._orientationMediaQuery.removeEventListener("change",this._handleOpen),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.unsubscribe(this._requestResize)}focus(){(0,r.ensureNotNull)(this._reference).focus()}getElement(){return this._reference}contains(e){var t,n;return null!==(n=null===(t=this._reference)||void 0===t?void 0:t.contains(e))&&void 0!==n&&n}render(){const{className:e,wrapperClassName:t,headerClassName:n,isOpened:r,title:o,titleTextWrap:s,dataName:l,onClickOutside:c,additionalElementPos:g,additionalHeaderElement:f,backdrop:_,shouldForceFocus:v=!0,shouldReturnFocus:w,onForceFocus:y,showSeparator:E,subtitle:I,draggable:k=!0,fullScreen:D=!1,showCloseIcon:P=!0,rounded:B=!0,isAnimationEnabled:T,growPoint:M,dialogTooltip:L,unsetHeaderAlign:F,onDragStart:A,dataDialogName:O,closeAriaLabel:z,containerAriaLabel:W,reference:H,containerTabIndex:j,closeButtonReference:Z,onCloseButtonKeyDown:q,shadowed:G,fullScreenViewOffsets:U,fixedBody:K,onClick:V}=this.props,X="after"!==g?f:void 0,Y="after"===g?f:void 0,J="string"==typeof o?o:O||"",Q=(0,x.filterDataProps)(this.props),$=(0,C.mergeRefs)([this._handleReference,H]);return i.createElement(p.MatchMedia,{rule:h.DialogBreakpoints.SmallHeight},(g=>i.createElement(p.MatchMedia,{rule:h.DialogBreakpoints.TabletSmall},(h=>i.createElement(d.PopupDialog,{rounded:!(h||D)&&B,className:a()(S.dialog,D&&U&&S.bounded,e),isOpened:r,reference:$,onKeyDown:this._handleKeyDown,onClickOutside:c,onClickBackdrop:c,fullscreen:h||D,guard:g?N:R,boundByScreen:h||D,shouldForceFocus:v,onForceFocus:y,shouldReturnFocus:w,backdrop:_,draggable:k,isAnimationEnabled:T,growPoint:M,name:this.props.dataName,dialogTooltip:L,onDragStart:A,
containerAriaLabel:W,containerTabIndex:j,calculateDialogPosition:D&&U?this._calculatePositionWithOffsets:void 0,shadowed:G,fixedBody:K,onClick:V,...Q},i.createElement("div",{className:a()(S.wrapper,t),"data-name":l,"data-dialog-name":J},void 0!==o&&i.createElement(b,{draggable:k&&!(h||D),onClose:this._handleCloseBtnClick,renderAfter:Y,renderBefore:X,subtitle:I,title:o,titleTextWrap:s,showCloseIcon:P,className:n,unsetAlign:F,closeAriaLabel:z,closeButtonReference:Z,onCloseButtonKeyDown:q}),E&&i.createElement(u.Separator,{className:S.separator}),i.createElement(m.PopupContext.Consumer,null,(e=>this._renderChildren(e,h||D)))))))))}}},190410:(e,t,n)=>{"use strict";n.d(t,{DialogHeaderContext:()=>i});const i=n(50959).createContext({setHideClose:()=>{}})},231862:(e,t,n)=>{"use strict";n.d(t,{DialogSearch:()=>u});var i=n(50959),r=n(497754),o=n.n(r),a=n(609838),s=n(72571),l=n(969859),c=n(654313),d=n(559086);function u(e){const{children:t,isMobile:r,renderInput:u,onCancel:p,containerClassName:m,inputContainerClassName:g,iconClassName:f,cancelTitle:_=a.t(null,void 0,n(904543)),...v}=e;return i.createElement("div",{className:o()(d.container,r&&d.mobile,m)},i.createElement("div",{className:o()(d.inputContainer,r&&d.mobile,g,p&&d.withCancel)},u||i.createElement(h,{isMobile:r,...v})),t,i.createElement(s.Icon,{className:o()(d.icon,r&&d.mobile,f),icon:r?c:l}),p&&(!r||""!==v.value)&&i.createElement("div",{className:o()(d.cancel,r&&d.mobile),onClick:p},_))}function h(e){const{className:t,reference:n,isMobile:r,value:a,onChange:s,onFocus:l,onBlur:c,onKeyDown:u,onSelect:h,placeholder:p,activeDescendant:m,...g}=e;return i.createElement("input",{...g,ref:n,type:"text",className:o()(t,d.input,r&&d.mobile),autoComplete:"off","data-role":"search",placeholder:p,value:a,onChange:s,onFocus:l,onBlur:c,onSelect:h,onKeyDown:u,"aria-activedescendant":m})}},982850:(e,t,n)=>{"use strict";n.d(t,{DialogSidebarItem:()=>h,DialogSidebarWrapper:()=>u});var i,r=n(50959),o=n(497754),a=n.n(o),s=n(72571),l=n(393832),c=n(568648),d=n(87886);function u(e){return r.createElement("div",{className:d.wrapper,...e})}function h(e){const{mode:t,title:n,icon:i,isActive:o,onClick:u,tag:h="div",reference:p,className:m,mobileFontSize:g="medium",showLastDivider:f,useBoldIconsForMobile:_,hideArrow:v,...w}=e,{isMobile:y,isTablet:b}=(0,l.getSidebarMode)(t),C=function(){if(y&&_)return null==i?void 0:i.bold;return o?null==i?void 0:i.bold:null==i?void 0:i.default}();return r.createElement(h,{...w,ref:p,title:b?n:"",className:a()(d.tab,b&&d.isTablet,y&&d.isMobile,o&&d.active,v&&d.withoutArrow,m,b&&"apply-common-tooltip"),onClick:u},i&&r.createElement(s.Icon,{className:d.icon,icon:C}),!b&&r.createElement("span",{className:a()(d.title,!i&&d.withoutIcon,"medium"===g?d.medium:d.large,f&&d.showLastDivider)},r.createElement("span",{className:a()(d.titleText,"apply-overflow-tooltip")},n),y&&!v&&r.createElement(s.Icon,{className:d.nested,icon:c})))}!function(e){e.Medium="medium",e.Large="large"}(i||(i={}))},393832:(e,t,n)=>{"use strict";var i,r;function o(e){return{isMobile:"mobile"===e,
isTablet:"tablet"===e}}n.d(t,{getSidebarMode:()=>o}),function(e){e.Bold="bold",e.Default="default"}(i||(i={})),function(e){e.Tablet="tablet",e.Mobile="mobile"}(r||(r={}))},596783:(e,t,n)=>{"use strict";n.r(t),n.d(t,{IndicatorsLibraryContainer:()=>me});var i=n(50959),r=n(609838),o=n(651407);const a=r.t(null,void 0,n(669644));var s,l,c,d,u,h,p;!function(e){e.Title="Title",e.Item="Item",e.Loader="Loader"}(s||(s={})),function(e){e.User="Script$USER",e.Public="Script$PUB",e.InviteOnly="Script$INVITE",e.Favorite="Script$FAVORITE",e.BuiltIn="tv-basicstudies",e.CandlestickPatterns="candlestick-patterns",e.Standard="Script$STD",e.VolumeProfile="tv-volumebyprice",e.Strategies="strategies",e.EditorsPicks="editorsPicks",e.Trending="trending",e.AutoJava="auto-java",e.AutoStandard="auto-standard",e.Auto="auto",e.OldChartPatterns="tv-chartpatterns",e.ChartPatterns="tv-chart_patterns"}(l||(l={})),function(e){e.Favorites="favorites",e.BuiltIns="built-ins",e.PublicLibrary="public-library",e.UserScripts="my-scripts",e.InviteOnlyScripts="invite-only-scripts",e.Addons="addons",e.Financials="financials"}(c||(c={})),function(e){e.Indicators="indicators",e.Strategies="strategies",e.Patterns="patterns",e.Profiles="profiles"}(d||(d={})),function(e){e.Top="top",e.EditorsPicks="editorsPicks",e.Trending="trending"}(u||(u={})),function(e){e.Top="top",e.Trending="trending"}(h||(h={})),function(e){e.Favorites="favorites",e.IncomeStatement="income statements",e.BalanceSheet="balance sheet",e.CashFlow="cash flow",e.Ratios="ratios",e.Statistics="statistics"}(p||(p={}));var m=n(762293),g=n(440891),f=n(868674);function _(e,t){const n=e.title.toLowerCase(),i=t.title.toLowerCase();return n<i?-1:n>i?1:0}const v={earning:new RegExp("EPS"),earnings:new RegExp("EPS"),"trailing twelve months":new RegExp("TTM")};function w(e){var t;const{id:i,description:o,shortDescription:s,description_localized:l,is_hidden_study:c,version:d,extra:u,tags:h}=e,p=g.enabled("graying_disabled_tools_enabled")&&(null===(t=window.ChartApiInstance)||void 0===t?void 0:t.studiesAccessController.isToolGrayed(o));return{id:i,title:l||r.t(o,{context:"study"},n(783477)),shortDescription:s,shortTitle:s,isStrategy:m.StudyMetaInfo.isScriptStrategy(e),isHidden:c,isNew:null==u?void 0:u.isNew,isUpdated:null==u?void 0:u.isUpdated,isBeta:null==u?void 0:u.isBeta,isPro:null==u?void 0:u.isPro,proBadgeTitle:a,isFundamental:!1,isOverlay:e.is_price_study,studyData:{id:i,version:d,descriptor:{type:"java",studyId:e.id},packageName:y(i,u)},isGrayed:p,tags:h}}function y(e,t){return(null==t?void 0:t.isChartPattern)?"tv-chart_patterns":(null==t?void 0:t.isAuto)?"auto-java":m.StudyMetaInfo.getPackageName(e)}var b=n(497754),C=n.n(b),x=n(132455),E=n(533408),S=n(601227),R=n(231862),N=n(982850),I=n(815511);function k(e){const{reference:t,className:n,...r}=e;return i.createElement("div",{ref:t,className:C()(I.container,n),...r,"data-role":"dialog-content"})}var D=n(974136);function P(e){const{children:t,className:n,disabled:r}=e;return i.createElement("span",{className:C()(D.title,r&&D.disabled,n)},t)}
const B=i.createContext(null);var T=n(260598),M=n(577687),L=n(180185),F=n(970307);function A(e){const t=(0,i.useContext)(B),{style:o,isMobile:a,item:s,query:l,regExpRules:c,isBeta:d,isNew:u,isUpdated:h,isSelected:p,isHighlighted:m,reference:g,onClick:f,renderActions:_,isPro:v,proBadgeTitle:w,onItemActionsClick:y,favoriteClickHandler:b,hideEP:x}=e,{isFavorite:E,isLocked:S,public:R,editorsPick:N}=s,I=void 0!==E,k=O(f,s),D=(0,i.useCallback)((e=>{e.stopPropagation(),null==y||y()}),[y]),L=(0,i.useCallback)((e=>{if(b)return null==y||y(),void b(e);if(null==t?void 0:t.toggleFavorite){O((e=>{null==y||y(),t.toggleFavorite(e)}),s)(e)}}),[b,y,null==t?void 0:t.toggleFavorite]),A=C()(F.container,a&&F.mobile,s.isGrayed&&F.disabled,p&&F.selected,m&&F.highlighted);return i.createElement("div",{ref:g,className:A,onClick:k,style:o,"data-role":"list-item","data-disabled":s.isGrayed,"data-title":s.title,"data-id":s.id},i.createElement("div",{className:C()(F.main,!I&&F.paddingLeft)},I&&i.createElement(M.FavoriteButton,{className:C()(F.favorite,E&&F.isActive),isFilled:E,onClick:L}),i.createElement(P,{disabled:s.isGrayed,className:C()(a&&F.mobileText)},i.createElement(T.HighlightedText,{queryString:l,rules:c,text:s.title})),!1,d&&i.createElement(BadgeStatus,{type:"beta",className:F.badge}),u&&i.createElement(BadgeStatus,{type:"new",className:F.badge}),h&&i.createElement(BadgeStatus,{type:"updated",className:F.badge}),Boolean(N&&!x)&&i.createElement(BadgeStatus,{type:"ep",className:F.badge,tooltip:r.t(null,void 0,n(510640))}),!1),R&&i.createElement("a",{href:R.authorLink,className:F.author,target:"_blank",onClick:D},R.authorName),!a&&R&&i.createElement("span",{className:F.likes},R.likesCount),!1)}function O(e,t){return n=>{const i=0===(0,L.modifiersFromEvent)(n)&&0===n.button;!n.defaultPrevented&&e&&i&&(n.preventDefault(),e(t))}}var z,W=n(671921);function H(e){const{title:t,type:n,className:r}=e;return i.createElement("h3",{className:C()(W.title,"Small"===n&&W.small,"Normal"===n&&W.normal,"Large"===n&&W.large,r)},t)}!function(e){e.Small="Small",e.Normal="Normal",e.Large="Large"}(z||(z={}));var j=n(872910);function Z(e){const{style:t,children:n,isMobile:r}=e;return i.createElement("div",{style:t,className:C()(j.container,r&&j.mobile)},n)}var q=n(72571),G=n(805184),U=n(464856);function K(e){const{className:t,icon:n,title:r,description:o,buttonText:a,buttonAction:s}=e;return i.createElement("div",{className:C()(U.container,t)},n&&i.createElement(q.Icon,{icon:n,className:U.image}),r&&i.createElement("h3",{className:U.title},r),o&&i.createElement("p",{className:U.description},o),a&&s&&i.createElement(G.Button,{onClick:s,className:U.button},a))}function V(e){const[t,n]=(0,i.useState)(null);function r(e){return e.findIndex((e=>(null==t?void 0:t.id)===e.id))}return[t,n,function(){n(function(){var n;const i=r(e),o=i===e.length-1;return null===t||-1===i?null!==(n=e[0])&&void 0!==n?n:null:o?e[i]:e[i+1]}())},function(){n(function(){var n;const i=r(e);return null===t||0===i||-1===i?null!==(n=e[0])&&void 0!==n?n:null:e[i-1]}())}]}
var X=n(512991),Y=n(360847),J=n(528598);function Q(e){const{reference:t,data:o,isOpened:a,onClose:s,applyStudy:l,shouldReturnFocus:c}=e,[d,u]=(0,i.useState)(""),h=(0,i.useMemo)((()=>(0,X.createRegExpList)(d,v)),[d]),p=(0,i.useMemo)((()=>d?(0,X.rankedSearch)({data:o,rules:h,queryString:d,primaryKey:"shortDescription",secondaryKey:"title",optionalPrimaryKey:"shortTitle",tertiaryKey:"tags"}):o),[d,h,o]),m=(0,i.useMemo)((()=>p.slice().sort($)),[p]),{highlightedItem:g,selectedItem:f,selectedNodeReference:_,scrollContainerRef:w,searchInputRef:y,onClickStudy:b,handleKeyDown:I}=function(e,t,n,r){let o=0;const[a,s]=(0,i.useState)(null),l=(0,i.useRef)(null),c=(0,i.useRef)(null),[d,u,h,p]=V(t),m=(0,i.useRef)(null);return(0,i.useEffect)((()=>{e?g(0):u(null)}),[e]),(0,i.useEffect)((()=>{void 0!==r&&(g(0),u(null))}),[r]),(0,i.useEffect)((()=>(a&&(o=setTimeout((()=>{s(null)}),1500)),()=>{clearInterval(o)})),[a]),{highlightedItem:a,scrollContainerRef:l,selectedNodeReference:c,selectedItem:d,searchInputRef:m,onClickStudy:function(e){n&&(n(e),u(e),s(e))},handleKeyDown:function(e){const[t,i]=function(e,t){if(null===e.current||null===t.current)return[0,0];const n=e.current.getBoundingClientRect(),i=t.current.getBoundingClientRect(),{height:r}=n,o=n.top-i.top,a=n.bottom-i.bottom+r<0?0:r,s=o-r>0?0:r,{scrollTop:l}=t.current;return[l-s,l+a]}(c,l);if(40===(0,L.hashFromEvent)(e)&&(e.preventDefault(),h(),g(i)),38===(0,L.hashFromEvent)(e)&&(e.preventDefault(),p(),g(t)),13===(0,L.hashFromEvent)(e)&&d){if(!n)return;n(d),s(d)}}};function g(e){null!==l.current&&l.current.scrollTo&&l.current.scrollTo(0,e)}}(a,m,l),D=""===d&&!m.length;return(0,i.useEffect)((()=>{var e;a||u(""),S.CheckMobile.any()||null===(e=y.current)||void 0===e||e.focus()}),[a]),i.createElement(E.AdaptivePopupDialog,{className:C()(J.dialogLibrary),isOpened:a,onClose:s,onClickOutside:s,title:r.t(null,void 0,n(184549)),dataName:"indicators-dialog",onKeyDown:I,shouldReturnFocus:c,ref:t,render:()=>i.createElement(i.Fragment,null,i.createElement(R.DialogSearch,{reference:y,placeholder:r.t(null,void 0,n(508573)),onChange:P,onFocus:B}),i.createElement(N.DialogSidebarWrapper,null,i.createElement(k,{reference:w,className:J.scroll},D?i.createElement(x.Spinner,null):m.length?i.createElement(i.Fragment,null,i.createElement(Z,null,i.createElement(H,{title:r.t(null,void 0,n(7378))})),m.map((e=>i.createElement(A,{key:e.id,item:e,onClick:()=>b(e),query:d,regExpRules:h,reference:(null==f?void 0:f.id)===e.id?_:void 0,isSelected:(null==f?void 0:f.id)===e.id,isHighlighted:(null==g?void 0:g.id)===e.id,favoriteClickHandler:t=>{t.stopPropagation(),(0,Y.toggleFavorite)(e.title)}})))):i.createElement(K,{className:J.noContentBlock,description:r.t(null,void 0,n(770269))}))))});function P(e){u(e.target.value)}function B(){var e;d.length>0&&(null===(e=y.current)||void 0===e||e.select())}}function $(e,t){return e.isFavorite===t.isFavorite?0:e.isFavorite?-1:1}var ee=n(559410),te=n(676725),ne=n(240534),ie=n(996038),re=n(632227),oe=n(12481),ae=n(110963),se=n(177335),le=n(499084);function ce(e){
const{text:t}=e,n=(0,ae.useHintShowAnimation)(2500);return i.createElement("div",{className:se.container},i.createElement("div",{className:se.centerElement},i.createElement("div",{className:b(se.contentWrap,n&&se.noticeShowed)},i.createElement(q.Icon,{icon:le,className:se.icon}),i.createElement("div",{className:se.textWrap},t))))}class de{constructor(e){this._showed=!1,this._wrap=document.createElement("div"),this._container=e,this._debouncedHide=(0,oe.default)((()=>this.hide()),3e3)}show(e){this._wrap&&!this._showed&&(this._showed=!0,this._container.appendChild(this._wrap),re.render(i.createElement(ce,{text:r.t(null,{replace:{studyTitle:e}},n(333673))}),this._wrap),this._debouncedHide())}hide(){this._wrap&&(this._showed=!1,re.unmountComponentAtNode(this._wrap),this._wrap.remove())}destroy(){this.hide(),delete this._wrap}}var ue=n(596058);function he(e,t){return e[t]||[]}var pe=n(651674);class me extends class{constructor(e){this._searchInputRef=i.createRef(),this._dialog=i.createRef(),this._rootInstance=null,this._visibility=new ne.WatchedValue(!1),this._container=document.createElement("div"),this._isForceRender=!1,this._parentSources=[],this._isDestroyed=!1,this._deepFundamentalsHistoryNotificationHasBeenShown=!1,this._hintRenderer=null,this._showDeepFundamentalsHistoryNotification=()=>{},this._chartWidgetCollection=e}isDestroyed(){return this._isDestroyed}visible(){return this._visibility.readonly()}resetAllStudies(){}updateFavorites(){}open(e,t,n,i,r){this._parentSources=e,this._updateSymbol(),this._setProps({isOpened:!0,shouldReturnFocus:null==r?void 0:r.shouldReturnFocus}),this._visibility.setValue(!0),ee.emit("indicators_dialog")}show(e){this.open([],void 0,void 0,void 0,e)}hide(){var e;this._parentSources=[],this._setProps({isOpened:!1}),this._visibility.setValue(!1),null===(e=this._hintRenderer)||void 0===e||e.destroy(),this._hintRenderer=null}destroy(){var e,t;this._isDestroyed=!0,null===(e=this._hintRenderer)||void 0===e||e.destroy(),null===(t=this._rootInstance)||void 0===t||t.unmount(),this._rootInstance=null}_shouldPreventRender(){return this._isDestroyed||!this._isForceRender&&!this._getProps().value().isOpened}_getRenderData(){return{props:this._getProps().value(),container:this._getContainer()}}_applyStudy(e,t){var n;e.isGrayed?ee.emit("onGrayedObjectClicked",{type:"study",name:e.shortDescription}):(S.CheckMobile.any()||null===(n=this._searchInputRef.current)||void 0===n||n.select(),async function(e,t,n,i,r,a){const s=e.activeChartWidget.value();if(!s)return null;const{studyData:l}=t;if(!l)return Promise.resolve(null);const c=l.descriptor;if("java"===c.type){const e=(0,f.tryFindStudyLineToolNameByStudyId)(c.studyId);if(null!==e)return await(0,f.initLineTool)(e),o.tool.setValue(e),null}return s.insertStudy(l.descriptor,n,{stubTitle:t.shortDescription,isFundamental:t.isFundamental,isOverlay:t.isOverlay},void 0,a)}(this._chartWidgetCollection,e,this._parentSources,0,this._symbol,(()=>this._showHint(e.title))).then((t=>{var n,i,r;null===t&&(null===(n=this._hintRenderer)||void 0===n||n.hide()),
null!==t&&((0,ue.hasConfirmInputs)(t.metaInfo().inputs)||(0,ue.isSymbolicStudy)(t.metaInfo()))&&(null===(i=this._hintRenderer)||void 0===i||i.show(e.title));window.is_authenticated;S.CheckMobile.any()||(null===document.activeElement||document.activeElement===document.body||null!==this._dialog.current&&this._dialog.current.contains(document.activeElement))&&(null===(r=this._searchInputRef.current)||void 0===r||r.focus())})))}_setProps(e){const t=this._getProps().value(),{isOpened:n}=t;this._isForceRender=n&&"isOpened"in e&&!e.isOpened;const i={...t,...e};this._getProps().setValue(i)}_requestBuiltInJavaStudies(){return(0,te.studyMetaInfoRepository)().findAllJavaStudies()}_focus(){var e;this._getProps().value().isOpened&&(null===(e=this._dialog.current)||void 0===e||e.focus())}_getContainer(){return this._container}_getDialog(){return this._dialog}_getSymbol(){return this._symbol}_updateSymbol(){this._symbol=void 0}_showHint(e){var t,n,i;if(window.matchMedia(ie.DialogBreakpoints.TabletSmall).matches){if(null===(t=this._hintRenderer)||void 0===t||t.hide(),!this._hintRenderer){const e=null===(n=this._dialog.current)||void 0===n?void 0:n.getElement();e&&(this._hintRenderer=new de(e))}null===(i=this._hintRenderer)||void 0===i||i.show(e)}}}{constructor(e,t){super(e),this._options={onWidget:!1},this._indicatorData=[],t&&(this._options=t),this._props=new ne.WatchedValue({data:[],applyStudy:this._applyStudy.bind(this),isOpened:!1,reference:this._getDialog(),onClose:this.hide.bind(this)}),this._getProps().subscribe(this._render.bind(this)),this._init()}_getProps(){return this._props}async _init(){const e=function(e){const t={};return e.forEach((e=>{const{studyData:n}=e;if(!n)return;const{packageName:i}=n;i in t?t[i].push(e):t[i]=[e]})),t}(function(e,t=!0){return e.filter((e=>{const n=!!t||!function(e){return e.isStrategy}(e);return!e.isHidden&&n}))}((await this._requestBuiltInJavaStudies()).map(w)));this._indicatorData=await async function(e,t){let n={...t};return[...he(n,"tv-basicstudies"),...he(n,"Script$STD"),...he(n,"tv-volumebyprice")].filter((e=>!e.isStrategy)).sort(_)}(this._options.onWidget,e),this._setFavorites(),this._setProps({data:this._indicatorData}),Y.favoriteAdded.subscribe(null,(()=>this._refreshFavorites())),Y.favoriteRemoved.subscribe(null,(()=>this._refreshFavorites()))}_setFavorites(){g.enabled("items_favoriting")&&this._indicatorData.forEach((e=>{e.isFavorite=(0,Y.isFavorite)(e.title)}))}_refreshFavorites(){this._setFavorites(),this._setProps({data:this._indicatorData})}_render(){if(this._shouldPreventRender())return;const{props:e,container:t}=this._getRenderData(),n=i.createElement(Q,{...e});this._rootInstance?this._rootInstance.render(n):this._rootInstance=(0,pe.createReactRoot)(n,t)}}},577687:(e,t,n)=>{"use strict";n.d(t,{FavoriteButton:()=>u});var i=n(609838),r=n(50959),o=n(497754),a=n(72571),s=n(239146),l=n(648010),c=n(998992);const d={add:i.t(null,void 0,n(869207)),remove:i.t(null,void 0,n(685106))};function u(e){const{className:t,isFilled:n,isActive:i,onClick:u,...h}=e;return r.createElement(a.Icon,{
...h,className:o(c.favorite,"apply-common-tooltip",n&&c.checked,i&&c.active,t),icon:n?s:l,onClick:u,title:n?d.remove:d.add})}},512991:(e,t,n)=>{"use strict";n.d(t,{createRegExpList:()=>a,getHighlightedChars:()=>s,rankedSearch:()=>o});var i,r=n(41899);function o(e){const{data:t,rules:n,queryString:i,isPreventedFromFiltering:o,primaryKey:a,secondaryKey:s=a,optionalPrimaryKey:l,tertiaryKey:c}=e;return t.map((e=>{const t=l&&e[l]?e[l]:e[a],o=e[s],d=c&&e[c];let u,h=0;return n.forEach((e=>{var n,a,s,l,c;const{re:p,fullMatch:m}=e;if(p.lastIndex=0,(0,r.isString)(t)&&t&&t.toLowerCase()===i.toLowerCase())return h=4,void(u=null===(n=t.match(m))||void 0===n?void 0:n.index);if((0,r.isString)(t)&&m.test(t))return h=3,void(u=null===(a=t.match(m))||void 0===a?void 0:a.index);if((0,r.isString)(o)&&m.test(o))return h=2,void(u=null===(s=o.match(m))||void 0===s?void 0:s.index);if((0,r.isString)(o)&&p.test(o))return h=2,void(u=null===(l=o.match(p))||void 0===l?void 0:l.index);if(Array.isArray(d))for(const e of d)if(m.test(e))return h=1,void(u=null===(c=e.match(m))||void 0===c?void 0:c.index)})),{matchPriority:h,matchIndex:u,item:e}})).filter((e=>o||e.matchPriority)).sort(((e,t)=>{if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}function a(e,t){const n=[],i=e.toLowerCase(),r=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${l(e)}`:l(e)})`)).join("(.*?)")+"(.*)";return n.push({fullMatch:new RegExp(`(${l(e)})`,"i"),re:new RegExp(`^${r}`,"i"),reserveRe:new RegExp(r,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(i)&&n.push({fullMatch:t[i],re:t[i],fuzzyHighlight:!1}),n}function s(e,t,n){const i=[];return e&&n?(n.forEach((e=>{const{fullMatch:n,re:r,reserveRe:o}=e;n.lastIndex=0,r.lastIndex=0;const a=n.exec(t),s=a||r.exec(t)||o&&o.exec(t);if(e.fuzzyHighlight=!a,s)if(e.fuzzyHighlight){let e=s.index;for(let t=1;t<s.length;t++){const n=s[t],r=s[t].length;if(t%2){const t=n.startsWith(" ")||n.startsWith("/")||n.startsWith("-");i[t?e+1:e]=!0}e+=r}}else for(let e=0;e<s[0].length;e++)i[s.index+e]=!0})),i):i}function l(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}!function(e){e[e.Low=0]="Low",e[e.MediumLow=1]="MediumLow",e[e.Medium=2]="Medium",e[e.High=3]="High",e[e.Highest=4]="Highest"}(i||(i={}))},260598:(e,t,n)=>{"use strict";n.d(t,{HighlightedText:()=>s});var i=n(50959),r=n(497754),o=n(512991),a=n(532248);function s(e){const{queryString:t,rules:n,text:s,className:l}=e,c=(0,i.useMemo)((()=>(0,o.getHighlightedChars)(t,s,n)),[t,n,s]);return i.createElement(i.Fragment,null,c.length?s.split("").map(((e,t)=>i.createElement(i.Fragment,{key:t},c[t]?i.createElement("span",{className:r(a.highlighted,l)},e):i.createElement("span",null,e)))):s)}},930052:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>r});var i=n(50959);class r extends i.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={
query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addEventListener("change",this._handleChange)}_unsubscribe(e){e.removeEventListener("change",this._handleChange)}}},494707:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>a});var i=n(50959),r=n(497754),o=n(547625);function a(e){return i.createElement("div",{className:r(o.separator,e.className)})}},813113:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>l,PortalContext:()=>c});var i=n(50959),r=n(632227),o=n(925931),a=n(801808),s=n(682925);class l extends i.PureComponent{constructor(){super(...arguments),this._uuid=(0,o.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"";const t=this.props.className;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute("data-focus-trap")&&e.setAttribute("data-focus-trap","true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),r.createPortal(i.createElement(c.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,a.getRootOverlapManager)():this.context}}l.contextType=s.SlotContext;const c=i.createContext(null)},132455:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>i.Spinner});var i=n(672511)},904237:(e,t,n)=>{"use strict";var i=n(632227);t.createRoot=i.createRoot,i.hydrateRoot},360847:(e,t,n)=>{"use strict";n.r(t),n.d(t,{favoriteAdded:()=>o,favoriteRemoved:()=>a,favoritesSynced:()=>s,isFavorite:()=>d,saveFavorites:()=>p,toggleFavorite:()=>c});var i=n(329452),r=n(870122);const o=new i.Delegate,a=new i.Delegate,s=new i.Delegate;let l=[];function c(e){return-1===u(e)?(function(e){!d(e)&&(l.push(e),p(),o.fire(e))}(e),!0):(function(e){const t=u(e);-1!==t&&(l.splice(t,1),p(),a.fire(e))}(e),!1)}function d(e){return-1!==u(e)}function u(e){return l.indexOf(e)}function h(){var e,t;l=[];const n=Boolean(void 0===(0,r.getValue)("chart.favoriteLibraryIndicators")),i=(0,r.getJSON)("chart.favoriteLibraryIndicators",[]);if(l.push(...i),0===l.length&&n&&"undefined"!=typeof window){const n=JSON.parse(null!==(t=null===(e=window.urlParams)||void 0===e?void 0:e.favorites)&&void 0!==t?t:"{}").indicators;n&&Array.isArray(n)&&l.push(...n)}s.fire()}function p(){const e=l.slice();(0,r.setJSON)("chart.favoriteLibraryIndicators",e)}h(),r.onSync.subscribe(null,h)},499084:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16Zm3.87-12.15c.36.2.49.66.28 1.02l-4 7a.75.75 0 0 1-1.18.16l-3-3a.75.75 0 1 1 1.06-1.06l2.3 2.3 3.52-6.14a.75.75 0 0 1 1.02-.28Z"/></svg>'},117105:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 21 21m0-21-21 21"/></svg>'},315130:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 15 15m0-15-15 15"/></svg>'},238822:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 11 11m0-11-11 11"/></svg>'},663346:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 9 9m0-9-9 9"/></svg>'},534983:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 7 7m0-7-7 7"/></svg>'},568648:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentcolor" stroke-width="1.3" d="M12 9l5 5-5 5"/></svg>'},969859:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M12.4 12.5a7 7 0 1 0-4.9 2 7 7 0 0 0 4.9-2zm0 0l5.101 5"/></svg>'},239146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},648010:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.116.236.26.038 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'},654313:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M18.5 12.5a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm-1.25 5.8a7.5 7.5 0 1 1 1.06-1.06l4.22 4.23.53.53L22 23.06l-.53-.53-4.22-4.22Z"/></svg>'},925931:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>i});let i=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);