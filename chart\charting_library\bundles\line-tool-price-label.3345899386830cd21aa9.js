"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6484],{402578:(e,r,i)=>{i.r(r),i.d(r,{LineToolPriceLabel:()=>s});var t=i(792535),o=i(889868),n=i(981856);class s extends o.LineDataSource{constructor(e,r,t,o){super(e,r??s.createProperties(e.backgroundTheme().spawnOwnership()),t,o),Promise.all([i.e(6290),i.e(6881),i.e(5579),i.e(1583)]).then(i.bind(i,883303)).then((e=>{this._setPaneViews([new e.PriceLabelPaneView(this,this._model)])}))}pointsCount(){return 1}name(){return"Price Label"}static createProperties(e,r){const i=new t.DefaultProperty({defaultName:"linetoolpricelabel",state:r,theme:e});return this._configureProperties(i),i}_normalizePoint(e,r){return super._normalizePointWithoutOffset(e)??super._normalizePoint(e,r)}async _getPropertyDefinitionsViewModelClass(){return(await Promise.all([i.e(6406),i.e(8511),i.e(5234),i.e(4590),i.e(8537)]).then(i.bind(i,920765))).PriceLabelDefinitionsViewModel}static _configureProperties(e){super._configureProperties(e),e.addChild("linesColors",new n.LineToolColorsProperty([e.childs().borderColor])),e.addChild("textsColors",new n.LineToolColorsProperty([e.childs().color]))}}}}]);