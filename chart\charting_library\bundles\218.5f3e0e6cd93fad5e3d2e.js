(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[218],{958027:e=>{e.exports={group:"group-NGVKpq85"}},531011:e=>{e.exports={checkboxWrapper:"checkboxWrapper-MWIiSXlK",checkbox:"checkbox-MWIiSXlK",checkboxLabel:"checkboxLabel-MWIiSXlK"}},145736:e=>{e.exports={stopLossTypeAnchor:"stopLossTypeAnchor-r39yCzUu",stopLossTypeAnchorText:"stopLossTypeAnchorText-r39yCzUu"}},928711:e=>{e.exports={labels:"labels-Llv4yjs6",rewardRisk:"rewardRisk-Llv4yjs6",rewardRiskText:"rewardRiskText-Llv4yjs6",rewardRiskFieldName:"rewardRiskFieldName-Llv4yjs6",bracketControl:"bracketControl-Llv4yjs6",control:"control-Llv4yjs6",wait:"wait-Llv4yjs6",clickableTextColor:"clickableTextColor-Llv4yjs6",inputGroup:"inputGroup-Llv4yjs6",rightBlock:"rightBlock-Llv4yjs6",checkboxWrapper:"checkboxWrapper-Llv4yjs6",label:"label-Llv4yjs6",bracketInCurrency:"bracketInCurrency-Llv4yjs6",bracketInPercent:"bracketInPercent-Llv4yjs6",bracketInPips:"bracketInPips-Llv4yjs6",bracketInPrice:"bracketInPrice-Llv4yjs6"}},497324:e=>{e.exports={currency:"currency-oqD5JCWK"}},694371:e=>{e.exports={disabled:"disabled-MXBoIkH5",listItem:"listItem-MXBoIkH5",listItemMarker:"listItemMarker-MXBoIkH5",listItemData:"listItemData-MXBoIkH5",listItemTitle:"listItemTitle-MXBoIkH5",sideBuy:"sideBuy-MXBoIkH5",sideSell:"sideSell-MXBoIkH5",divider:"divider-MXBoIkH5"}},969537:e=>{e.exports={wrapper:"wrapper-kb0OuE9E",header:"header-kb0OuE9E",disabled:"disabled-kb0OuE9E"}},754146:e=>{e.exports={controls:"controls-VP5GaBfG",cancelButton:"cancelButton-VP5GaBfG",mobile:"mobile-VP5GaBfG"}},410983:e=>{e.exports={button:"button-pP_E6i3F"}},175377:e=>{e.exports={container:"container-o0n6KbaV",button:"button-o0n6KbaV",topRadiusDisabled:"topRadiusDisabled-o0n6KbaV",rightRadiusDisabled:"rightRadiusDisabled-o0n6KbaV",bottomRadiusDisabled:"bottomRadiusDisabled-o0n6KbaV",leftRadiusDisabled:"leftRadiusDisabled-o0n6KbaV"}},893562:e=>{e.exports={body:"body-X70j90Zy"}},133169:e=>{e.exports={footer:"footer-fSCU20uC"}},999600:e=>{e.exports={header:"header-BitPgYCK",close:"close-BitPgYCK"}},722018:e=>{e.exports={message:"message-tNeiOkjz",error:"error-tNeiOkjz"}},536323:e=>{e.exports={panTile:"panTile-eoLBmZ4v",rootWrapper:"rootWrapper-eoLBmZ4v",panAreaWrapperStyle:"panAreaWrapperStyle-eoLBmZ4v",panAreaStyle:"panAreaStyle-eoLBmZ4v",drawerWrapperStyle:"drawerWrapperStyle-eoLBmZ4v",hidden:"hidden-eoLBmZ4v",scroll:"scroll-eoLBmZ4v",closedContentStyle:"closedContentStyle-eoLBmZ4v",mainContentStyle:"mainContentStyle-eoLBmZ4v",controlsWrapper:"controlsWrapper-eoLBmZ4v",controlsContent:"controlsContent-eoLBmZ4v",drawer:"drawer-eoLBmZ4v",animate:"animate-eoLBmZ4v"}},493455:(e,t,n)=>{"use strict";n.d(t,{ControlGroup:()=>c});var r=n(50959),o=n(497754),i=n(380327),a=n(958027),s=n.n(a);function l(e,t,n){return{isTop:e<t,isRight:e%t==t-1,isBottom:e>=t*(n-1),isLeft:e%t==0}}function c(e){const{children:t,rows:n,cols:a,disablePositionAdjustment:c,className:d,role:u,...p}=e,m=r.Children.count(t),k=a??m,b=n??function(e,t){return Math.ceil(e/t)}(m,k),g=(0,r.useMemo)((()=>{const e=[]
;for(let t=0;t<m;t++)e.push({isGrouped:!0,cellState:l(t,k,b),disablePositionAdjustment:c});return e}),[m,k,b,c]),h=r.Children.map(t,((e,t)=>r.createElement(i.ControlGroupContext.Provider,{value:g[t]},e))),y={"--ui-lib-control-group-cols":k.toString(10),"--ui-lib-control-group-rows":b.toString(10)};return r.createElement("span",{className:o(s().group,d),style:y,role:u,...p},h)}},308825:(e,t,n)=>{"use strict";n.d(t,{BracketControlGroup:()=>N});var r,o=n(497754),i=n(50959),a=n(609838),s=n(661851),l=n(972535),c=n(599016),d=n(493455),u=n(763281),p=n(587125),m=n(459409);function k(e){const{inputMode:t,control:n,className:r,error:o,errorMessage:a,errorHandler:l,onFocus:c,intent:d,highlight:u,highlightRemoveRoundBorder:k,disabled:b,fontSizeStyle:g,isFractional:h,min:y,max:f,id:v,tabIndex:C,primaryFieldSelectorTooltip:I,"aria-controls":w}=e,P=(0,i.useRef)(null),S=(0,s.useObservable)(n.value$,n.getValue()),T="function"==typeof n.step?n.step(S):n.step;return i.createElement(m.PrimaryInputSelector,{inputId:v,disabled:b||-1===C,isSelected:u,inputElement:P.current,handleSelect:c,tooltipText:I},i.createElement(p.NumberInput,{id:v,inputMode:t,intent:d,highlight:u,highlightRemoveRoundBorder:k,value:S,min:y,max:f,onValueChange:e=>{c?.(),n.setValue(e),n.onModifiedCallback?.()},className:r,step:T,error:o,errorMessage:a,errorHandler:l,inputReference:t=>{e.inputReference?.(t),P.current=t},noReadonlyStyles:!0,disabled:b,fontSizeStyle:g,formatter:n.formatter,mode:h?"fractional":"float","aria-current":u,"aria-controls":u?w:void 0,readonly:!u,tabIndex:C,autoSelectOnFocus:!1}))}function b(e){const{bracketType:t,isPipsControlShown:n,isPercentControlShown:r,isCurrencyControlShown:o}=e,i=function(e){return e===c.BracketType.TakeProfit?{pips:"take-profit-pips-field",price:"take-profit-price-field",riskInPercent:"take-profit-risk-in-percent-field",riskInCurrency:"take-profit--risk-in-currency-field"}:{pips:"stop-loss-pips-field",price:"stop-loss-price-field",riskInPercent:"stop-loss-risk-in-percent-field",riskInCurrency:"stop-loss-risk-in-currency-field"}}(t);return n||delete i.pips,r||delete i.riskInPercent,o||delete i.riskInCurrency,i}!function(e){e.AbsolutePrice="absolute-price-field",e.RelativePrice="relative-price-field",e.Quantity="quantity-field",e.QuantityCalculation="quantity-calculation-field",e.TakeProfitPips="take-profit-pips-field",e.TakeProfitPrice="take-profit-price-field",e.TakeProfitRiskInCurrency="take-profit--risk-in-currency-field",e.TakeProfitRiskInPercent="take-profit-risk-in-percent-field",e.StopLossPips="stop-loss-pips-field",e.StopLossPrice="stop-loss-price-field",e.StopLossRiskInPercent="stop-loss-risk-in-percent-field",e.StopLossRiskInCurrency="stop-loss-risk-in-currency-field"}(r||(r={}));var g=n(302946),h=n(820660),y=n(224567),f=n(89489),v=n(499421),C=n(431520),I=n(145736);const w={[c.BracketType.StopLoss]:a.t(null,void 0,n(719702)),[c.BracketType.TakeProfit]:a.t(null,void 0,n(255739)),[c.BracketType.TrailingStop]:a.t(null,void 0,n(992201)),[c.BracketType.GuaranteedStop]:a.t(null,void 0,n(337539))};function P(e){
const{items:t,selectedType:r,enableStopLoss:o,setBracketType:s}=e,{anchorProps:l,popoverProps:c}=(0,v.useMenuAnchorProps)({refStyle:"ref"}),d=(0,h.useEnterAndSpaceKeyDownHandler)(o),u=(0,i.useCallback)((e=>{e.stopPropagation(),e.preventDefault(),o()}),[o]),p=(0,i.useCallback)((e=>{s(e),o()}),[o,s]),m=(0,i.useMemo)((()=>t.map((e=>({title:w[e],selected:e===r,onClick:()=>p(e)})))),[t,r,p]);return i.createElement("div",{onClick:u,onKeyDown:d,"data-name":"stop-loss-type-selection"},i.createElement(y.Tooltip,{isRtl:(0,C.isRtl)(),content:a.t(null,void 0,n(102202)),trigger:[y.Trigger.Hover,y.Trigger.Focus]},i.createElement("div",{...l,tabIndex:0,className:I.stopLossTypeAnchor},i.createElement("span",{className:I.stopLossTypeAnchorText,"data-name":"bracket-checkbox-label"},w[r]))),i.createElement(f.MenuPopover,{...c,items:m}))}var S=n(531011);const T={[c.BracketType.StopLoss]:a.t(null,void 0,n(719702)),[c.BracketType.TakeProfit]:a.t(null,void 0,n(255739)),[c.BracketType.TrailingStop]:a.t(null,void 0,n(992201)),[c.BracketType.GuaranteedStop]:a.t(null,void 0,n(337539))};function E(e){const{type:t,enabled:n,controlState:r,stopLossTypes:o,setBracketType:a,onToggleEnabled:s,supportStopLoss:l,supportTrailingStop:d,supportGuaranteedStop:u}=e,{labelDisabled:p,checkboxDisabled:m}=r,k=t!==c.BracketType.TakeProfit,b=T[t];let y;if(k&&(l||d||u)&&!p){const e=()=>{n||s()};y=i.createElement(P,{selectedType:t,items:o,setBracketType:a,enableStopLoss:e})}else y=i.createElement("span",{className:S.checkboxLabel,"data-name":"bracket-checkbox-label"},b);const f=(0,h.useEnterAndSpaceKeyDownHandler)(s);return i.createElement("div",{"data-name":"checkbox-wrapper",className:S.checkboxWrapper,onKeyDown:f},i.createElement(g.Checkbox,{tabIndex:0,checked:n,label:y,className:S.checkbox,onChange:s,disabled:m,"data-name":`order-ticket-${k?"loss":"profit"}-checkbox-bracket`}))}var B=n(928711);const L={[c.BracketType.StopLoss]:a.t(null,void 0,n(510083)),[c.BracketType.TakeProfit]:a.t(null,void 0,n(973110)),[c.BracketType.TrailingStop]:a.t(null,void 0,n(108810)),[c.BracketType.GuaranteedStop]:a.t(null,void 0,n(743575))};function x(e,t){return e.filter((e=>e!==t)).join(" ")}function H(e,t){return o(B.control,!e&&B.wait,0===t&&B.bracketInPips,1===t&&B.bracketInPrice,2===t&&B.bracketInCurrency,3===t&&B.bracketInPercent)}function M(e){const{type:t,enabled:n,controlState:r,focusedControl:a,error:s,stopLossTypes:l,pips:p,price:m,riskInCurrency:g,riskInPercent:h,setBracketType:y,errorHandler:f,handlePipsInputRef:v,handlePriceInputRef:C,isPipsControlHidden:I,isCryptoBracket:w,showRiskControlsAndInfo:P,fontSizeStyle:S,supportStopLoss:T,supportTrailingStop:M,supportGuaranteedStop:R,onFocus:N,onToggleEnabled:D}=e,A=(0,i.useContext)(u.WidgetContext),F=(0,i.useContext)(u.SettingsContext),{inputDisabled:O}=r,z=t!==c.BracketType.TakeProfit,W=Boolean(s&&s.res),{onPipsFocus:G,onPriceFocus:V,onRiskInPercentFocus:j,onRiskInCurrencyFocus:$}=(0,i.useMemo)((()=>({onPipsFocus:()=>N(0),onPriceFocus:()=>N(1),onRiskInPercentFocus:()=>N(3),onRiskInCurrencyFocus:()=>N(2)})),[N]),K=(0,
i.useCallback)((e=>n&&a===e),[n,a]),{pipsClassName:Z,priceClassName:_,riskInCurrencyClassName:X,riskInPercentClassName:q}=(0,i.useMemo)((()=>({pipsClassName:H(n,0),priceClassName:H(n,1),riskInCurrencyClassName:H(n,2),riskInPercentClassName:H(n,3)})),[n]),U=(0,i.useCallback)((e=>{const t=K(e);return{highlight:t,intent:W&&t?"danger":void 0}}),[W,K]),Y=s&&s.msg,Q=A.isFractional,J=w?"showCryptoBracketsInCurrency":"showBracketsInCurrency",ee=P&&F[w?"showCryptoBracketsInPercent":"showBracketsInPercent"],te=P&&F[J],{bracketFieldsIds:ne,allBracketFieldsIds:re}=(0,i.useMemo)((()=>{const e=b({isPercentControlShown:ee,isCurrencyControlShown:te,bracketType:t,isPipsControlShown:!I});return{bracketFieldsIds:e,allBracketFieldsIds:Object.values(e)}}),[t,I,ee,te]),{pipsAriaControls:oe,priceAriaControls:ie,riskInCurrencyAriaControls:ae,riskInPercentAriaControls:se}=(0,i.useMemo)((()=>({pipsAriaControls:x(re,ne.pips),priceAriaControls:x(re,ne.price),riskInCurrencyAriaControls:x(re,ne.riskInCurrency),riskInPercentAriaControls:x(re,ne.riskInPercent)})),[re,ne]),le=L[t],ce=n?void 0:-1;return i.createElement("div",{className:o(B.bracketControl,z&&B.rightBlock),"data-name":z?"stop-loss-bracket-control":"take-profit-bracket-control"},i.createElement(E,{type:t,enabled:n,controlState:r,stopLossTypes:l,setBracketType:y,onToggleEnabled:D,supportStopLoss:T,supportTrailingStop:M,supportGuaranteedStop:R}),i.createElement(d.ControlGroup,{cols:1,className:B.inputGroup},I?[]:i.createElement(k,{...U(0),control:p,className:Z,onFocus:G,errorHandler:f,error:W&&K(0),errorMessage:Y,inputReference:v,disabled:O,fontSizeStyle:S,id:ne.pips,"aria-controls":oe,tabIndex:ce,primaryFieldSelectorTooltip:le}),i.createElement(k,{...U(1),control:m,className:_,onFocus:V,errorHandler:f,error:W&&K(1),errorMessage:Y,inputReference:C,disabled:O,fontSizeStyle:S,inputMode:"text",isFractional:Q,id:ne.price,"aria-controls":ie,tabIndex:ce,primaryFieldSelectorTooltip:le}),te?i.createElement(k,{...U(2),control:g,className:X,onFocus:$,errorHandler:f,error:W&&K(2),errorMessage:Y,disabled:O,fontSizeStyle:S,tabIndex:ce,id:ne.riskInCurrency,"aria-controls":ae,primaryFieldSelectorTooltip:le}):[],ee?i.createElement(k,{...U(3),control:h,className:q,onFocus:j,errorHandler:f,error:W&&K(3),errorMessage:Y,disabled:O,fontSizeStyle:S,key:ne.riskInPercent,tabIndex:ce,id:ne.riskInPercent,"aria-controls":se,primaryFieldSelectorTooltip:le}):[]))}function R(e){const{model:t,isCryptoBracket:n,orderTicketFocus:r,isPipsControlHidden:o}=e,{enabled$:a,error$:d,focusedControl$:u,bracketType$:p,stopLossTypes:m,pips:k,price:b,riskInCurrency:g,riskInPercent:h,isValuesInitialized$:y,controlState:f,onControlFocused:v,supportTrailingStop:C,supportGuaranteedStop:I,supportStopLoss:w,showRiskControlsAndInfo:P,getFocusedControl:S,getEnabled:T,getBracketType:E,getError:B,setFocusedControl:L,setEnabled:x,setBracketType:H,setControlError:R}=t,N=(0,s.useObservable)(u,S()),D=(0,s.useObservable)(p,E()),A=(0,s.useObservable)(a,T()),F=(0,s.useObservable)(d,B()),O=(0,s.useObservable)(y,!1),z=(0,i.useRef)(null),W=(0,
i.useRef)(null);(0,i.useEffect)((()=>{if(l.mobiletouch||!A)return;const e=e=>{null!==e&&O&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))},t=D===c.BracketType.TakeProfit&&3===r,n=(D===c.BracketType.StopLoss||D===c.BracketType.GuaranteedStop)&&4===r,o=t||n,i=D===c.BracketType.TrailingStop&&4===r;o&&e(W.current),i&&e(z.current)}),[O]);const G=(0,i.useCallback)((e=>{O&&(A||x(!0),N!==e&&L(e),v.fire())}),[O,A,N,x,L]);return i.createElement(M,{type:D,enabled:A,error:F,stopLossTypes:m,pips:k,price:b,riskInCurrency:g,riskInPercent:h,focusedControl:N,onFocus:G,onToggleEnabled:function(){x(!A),v.fire()},errorHandler:function(e){R(e)},handlePipsInputRef:e=>z.current=e,handlePriceInputRef:e=>W.current=e,setBracketType:H,isPipsControlHidden:o,controlState:f,isCryptoBracket:n,showRiskControlsAndInfo:P,supportStopLoss:w,supportTrailingStop:C,supportGuaranteedStop:I,fontSizeStyle:"medium"})}function N(e){const{model:t,focus:r}=e,l=(0,i.useContext)(u.SettingsContext),c=(0,s.useObservable)(t.rewardRisk$),d=t.takeProfitModel&&t.takeProfitModel.roundToPriceStepRequired||t.stopLossModel&&t.stopLossModel.roundToPriceStepRequired,p="supportCryptoBrackets"in t&&!0===t.supportCryptoBrackets,m=p?"showCryptoBracketsInCurrency":"showBracketsInCurrency",k=p?"showCryptoBracketsInPercent":"showBracketsInPercent",b=t.showRiskControlsAndInfo;return i.createElement(i.Fragment,null,t.takeProfitModel&&i.createElement(R,{model:t.takeProfitModel,orderTicketFocus:r,isCryptoBracket:p,isPipsControlHidden:d}),t.takeProfitModel&&t.stopLossModel&&i.createElement("div",{className:B.labels},i.createElement("div",{className:o(B.rewardRisk,c&&"apply-common-tooltip"),title:c?a.t(null,void 0,n(114365)):void 0},i.createElement("span",{className:B.rewardRiskText},c)),!d&&i.createElement(D,{title:"forex"===t.symbolType?a.t(null,void 0,n(394408)):a.t(null,void 0,n(824821))}),i.createElement(D,{title:a.t(null,void 0,n(7953))}),b&&l[m]&&i.createElement(D,{title:t.takeProfitModel?t.takeProfitModel.currency:""}),b&&l[k]&&i.createElement(D,{title:"%",tooltipText:a.t(null,void 0,n(697968))})),t.stopLossModel&&i.createElement(R,{model:t.stopLossModel,orderTicketFocus:r,isCryptoBracket:p,isPipsControlHidden:d}))}function D(e){const{title:t,tooltipText:n}=e;return i.createElement("span",{className:o(B.rewardRiskFieldName,"apply-common-tooltip"),title:n??t},t)}},556013:(e,t,n)=>{"use strict";n.d(t,{OrderPanelCustomFields:()=>s});var r=n(50959),o=n(363111),i=n(763281),a=n(423994);function s(e){const{customFieldModels:t,onClose:n}=e,{orderPanelStatus:s}=(0,r.useContext)(i.WidgetContext);return r.createElement(a.CustomFields,{customFieldModels:t,isWait:s===o.OrderPanelStatus.Wait,forceUserToSelectValue:s===o.OrderPanelStatus.Active,onClose:n})}},274837:(e,t,n)=>{"use strict";n.d(t,{useCommonDialogHandlers:()=>s});var r=n(50959),o=n(559410),i=n(206594),a=n(996038);function s(e){const{isOpened:t,onOpen:n,onClose:s}=e;(0,r.useEffect)((()=>{const e=()=>{s?.()};o.subscribe(i.CLOSE_POPUPS_AND_DIALOGS_COMMAND,e,null)
;const r=window.matchMedia(a.DialogBreakpoints.TabletSmall),l=window.matchMedia("(orientation: portrait)"),c=()=>{t?n?.(r.matches):s?.()};return c(),l.addEventListener("change",c),()=>{o.unsubscribe(i.CLOSE_POPUPS_AND_DIALOGS_COMMAND,e,null),l.removeEventListener("change",c)}}),[t,s,n])}},204926:(e,t,n)=>{"use strict";n.d(t,{InfoTable:()=>b});var r=n(50959),o=n(497754),i=n(584811),a=n(431520),s=n(234404),l=n(737563),c=n(497324);const d=e=>{const{currency:t,error:n,isLoading:o,value:i}=e;return o?r.createElement(s.Loader,{size:"small",staticPosition:!0}):n?r.createElement(l.IconQuestionInformation,{icon:"exclamation",intent:"warning",ariaLabel:n,tooltip:n,size:"small"}):t?r.createElement(r.Fragment,null,r.createElement("span",null,i),r.createElement("span",null," "),r.createElement("span",{className:c.currency},t)):r.createElement("span",null,i)};var u=n(694371);const p=e=>{const{row:t,error:n,disabled:s,isLoading:l,isDataApproximate:c}=e;if("separator"===t.type)return r.createElement(i.Divider,{className:u.divider,orientation:"horizontal",size:"xsmall",type:"primary"});let p=t.value||"—";return c&&t.value&&(p=(0,a.forceLTRStr)("≈ "+p)),r.createElement("div",{className:t.listMarker?u.listItemMarker:void 0},r.createElement("div",{className:o(u.listItem,s&&u.disabled)},r.createElement("div",{className:u.listItemTitle},t.title),r.createElement("div",{className:o(u.listItemData,m(t.type))},r.createElement(d,{value:p,currency:t.value?t.currency:void 0,error:n,isLoading:l}))))};function m(e){switch(e){case 0:return u.sideBuy;case 1:return u.sideSell;default:return}}var k=n(969537);function b(e){const{rows:t,header:n,disabled:i,isDataApproximate:a,error:s,isLoading:l}=e;return r.createElement("div",{className:o(k.wrapper,i&&k.disabled)},n&&r.createElement("div",{className:k.header},n),t.map(((e,t)=>r.createElement(p,{key:t,row:e,disabled:i,error:s,isDataApproximate:a,isLoading:l}))))}},110686:(e,t,n)=>{"use strict";n.d(t,{OrderDrawerControlContent:()=>m});var r=n(50959),o=n(423869),i=n(69111),a=n(497754),s=n.n(a),l=n(228837),c=n(234404),d=n(661851);function u(e){const{reference:t,buttonModel:n,side$:o,getSide:i,isButtonDisabled$:a,isButtonDisabled:s,loading$:u,doneButtonClick:p}=e,m=(0,d.useObservable)(a,s()),k=(0,d.useObservable)(u,!1),{primaryText:b}=(0,d.useObservable)(n.value$,n.getValue()),g=1===(0,d.useObservable)(o,i())?"blue":"red";return r.createElement(l.SquareButton,{reference:t,size:"medium",variant:"primary",color:g,disabled:k||m,onClick:()=>p(),"data-name":"place-and-modify-button"},k?r.createElement(c.Loader,null):b)}var p=n(754146);function m(e){const{onClose:t,sideModel:n,side:a,isButtonDisabled$:c,isButtonDisabled:d,loading$:m,doneButtonClick:k,buttonModel:b}=e;return r.createElement("div",{className:p.controls},r.createElement(l.SquareButton,{size:"medium",variant:"secondary",color:"gray",onClick:t,className:s()(p.cancelButton,(0,i.isOnMobileAppPage)("any")&&p.mobile)},"Cancel"),void 0!==b&&r.createElement(u,{buttonModel:b,side$:void 0!==n?n.value$:o.EMPTY,getSide:()=>a(),isButtonDisabled$:c,
isButtonDisabled:()=>d(),loading$:m,doneButtonClick:()=>k()}))}},570446:(e,t,n)=>{"use strict";n.d(t,{PlaceAndModifyButton:()=>c});var r=n(50959),o=n(423869),i=n(234404),a=n(228837),s=n(661851),l=n(410983);function c(e){const{model:t,reference:n,buttonModel:c}=e,d=t.hasOwnProperty("sideModel")&&"sideModel"in t?t.sideModel.value$:o.EMPTY,u=(0,s.useObservable)(t.isButtonDisabled$,t.isButtonDisabled()),p=(0,s.useObservable)(t.loading$,!1),m=(0,s.useObservable)(c.value$,c.getValue()),k=1===(0,s.useObservable)(d,t.side())?"blue":"red";return p?r.createElement(a.SquareButton,{reference:n,className:l.button,size:"xlarge",disabled:!0,"data-name":"place-and-modify-button"},r.createElement(i.Loader,null)):r.createElement(a.SquareButton,{...m,reference:n,className:l.button,size:"xlarge",color:k,disabled:u,onClick:()=>t.doneButtonClick(),"data-name":"place-and-modify-button"},void 0===m.secondaryText?m.primaryText:null)}},459409:(e,t,n)=>{"use strict";n.d(t,{PrimaryInputSelector:()=>p});var r=n(50959),o=n(497754),i=n.n(o),a=n(224567),s=n(380327),l=n(820660),c=n(930202);const d=[9,16,1040,1033];var u=n(175377);const p=(0,r.memo)((function(e){const t=(0,r.useContext)(s.ControlGroupContext),{inputId:n,children:o,disabled:p,isSelected:m,tooltipText:k,inputElement:b,handleSelect:g}=e,h=(0,r.useCallback)((()=>{g(),b?.focus()}),[b,g]),y=(0,l.useEnterAndSpaceKeyDownHandler)(h,{stopAllPropagation:!0}),f=(v=h,(0,r.useCallback)((e=>{const t=(0,c.hashFromEvent)(e);d.includes(t)||v()}),[v]));var v;const C=i()(u.button,{[u.bottomRadiusDisabled]:!t?.cellState.isBottom,[u.leftRadiusDisabled]:!t?.cellState.isLeft,[u.rightRadiusDisabled]:!t?.cellState.isRight,[u.topRadiusDisabled]:!t?.cellState.isTop});return(0,r.useEffect)((()=>(b&&!m&&b.addEventListener("keydown",f),()=>{b?.removeEventListener("keydown",f)})),[m,b,f]),r.createElement("div",{className:u.container},!m&&!p&&r.createElement(a.Tooltip,{isRtl:!1,content:k,trigger:a.Trigger.Focus},r.createElement("button",{"aria-controls":n,"aria-label":k,className:C,onKeyDown:y})),r.createElement("div",{tabIndex:-1,onMouseDown:g},o))}))},930894:(e,t,n)=>{"use strict";n.d(t,{Body:()=>a});var r=n(50959),o=n(497754);n(999600),n(608636),n(878112);n(133169);var i=n(893562);function a(e){return r.createElement("div",{className:o(i.body,e.className),ref:e.reference},e.children)}n(908783),n(722018)},977540:(e,t,n)=>{"use strict";n.d(t,{ResizableDrawer:()=>E,ResizableDrawerContentHeight:()=>r});var r,o=n(50959),i=n(111553),a=n(497754),s=n.n(a),l=n(69111),c=n(111982),d=n(650151),u=n(747830),p=n(702054),m=n(589637),k=n(297265),b=n(536323);!function(e){e[e.Collapsed=0]="Collapsed",e[e.Medium=1]="Medium",e[e.Full=2]="Full"}(r||(r={}));const g={panAreaHeight:16,controlsContentHeight:64,panAreaInvisibleHeight:48,drawerWidth:{horz:60,vert:100},maximumDrawerWidth:400,lowClientHeightBreakpoint:316,swipeDistanceThreshold:1,swipeVelocityThreshold:.3,animationTiming:150},h={[r.Collapsed]:{ascendingBreakPoint:null,descendingBreakPoint:20,descendingBreakPointForLowHeight:90,position:64},[r.Medium]:{ascendingBreakPoint:30,
descendingBreakPoint:50,position:40},[r.Full]:{ascendingBreakPoint:85,descendingBreakPoint:null,position:95}};function y(e,t){return Math.round(t*e/100)}function f(e,t){const n=y(h[r.Full].position,e),o=h[r.Collapsed].position;return t>n?n:t<o?o:t}function v(e,t){const n=y(h[r.Full].position,e.windowInnerHeight),o=h[r.Collapsed].position,i=e.moveInitialPoint+t;return i>n?n-e.moveInitialPoint:i<o?o-e.moveInitialPoint:t}function C(e,t){const n=e>g.lowClientHeightBreakpoint?y(h[r.Collapsed].descendingBreakPoint,e):h[r.Collapsed].descendingBreakPointForLowHeight,o=h[r.Collapsed].position;if(t>n)return 1;if(t<=o)return 0;return(t-o)/(n-o)}function I(e,t){return{moveInitialPoint:f(e.windowInnerHeight,t+g.controlsContentHeight),heightIncrement:v(e,0),animate:!0}}function w(e){const t=f(e.windowInnerHeight,h[r.Collapsed].position);return{moveInitialPoint:t,heightIncrement:v(e,0),animate:!0,opacity:C(e.windowInnerHeight,t)}}function P(e,t){const n=y(e.windowInnerHeight,h[r.Medium].position),o=(t.current?.offsetHeight??0)+g.panAreaHeight;if(o<n)return I(e,o);const i=f(e.windowInnerHeight,n);return{moveInitialPoint:i,heightIncrement:v(e,0),animate:!0,opacity:C(e.windowInnerHeight,i)}}function S(e,t){const n=y(e.windowInnerHeight,h[r.Full].position),o=(t.current?.offsetHeight??0)+g.panAreaHeight;if(o<n)return I(e,o);const i=f(e.windowInnerHeight,n);return{moveInitialPoint:i,heightIncrement:v(e,0),animate:!0,opacity:C(e.windowInnerHeight,i)}}function T(e){return{heightIncrement:0,moveInitialPoint:e,isSwiped:!1,isDragging:!1,animate:!1,opacity:C(document.documentElement.clientHeight,e),windowInnerHeight:document.documentElement.clientHeight,windowInnerWidth:document.documentElement.clientWidth}}function E(e){const{children:t,closedContent:n,controlsContent:a,disableSwipe:E,disablePan:B,initialContentHeight:L=r.Collapsed}=e,x=(0,o.useRef)(null),H=(0,o.useRef)(null),M=(0,o.useRef)(null),R=(0,k.useWatchedValueReadonly)({watchedValue:p.watchedTheme}),[N,D]=(0,o.useState)(T(h[r.Collapsed].position)),A=(0,o.useMemo)((()=>{const e=!(N.moveInitialPoint+N.heightIncrement<=h[r.Collapsed].position);return!N.isDragging&&!N.animate&&e}),[N.moveInitialPoint,N.isDragging,N.animate]),F=(0,o.useMemo)((()=>{const e=N.windowInnerHeight-g.panAreaHeight,t=N.moveInitialPoint+N.heightIncrement-g.panAreaHeight;return N.isDragging||N.animate?e:t}),[N.moveInitialPoint,N.isDragging,N.animate]),O=N.isDragging||N.animate?N.windowInnerHeight-(N.moveInitialPoint+N.heightIncrement):0;(0,o.useEffect)((()=>{const e=function(e,t){switch(e){case r.Collapsed:return h[r.Collapsed].position;case r.Medium:return y(h[r.Medium].position,document.documentElement.clientHeight);case r.Full:const e=t?.offsetHeight;return void 0===e?0:f(document.documentElement.clientHeight,e+g.panAreaHeight+g.controlsContentHeight)}}(L,H.current);D(T(e))}),[]),(0,o.useEffect)((()=>{const e=(0,d.ensureNotNull)(x.current),t=new i(e);return t.get("pan").set({direction:Hammer.DIRECTION_VERTICAL}),t.get("swipe").set({direction:Hammer.DIRECTION_VERTICAL,threshold:g.swipeDistanceThreshold,
velocity:g.swipeVelocityThreshold}),B||(t.on("panend",(()=>{D((e=>{if(e.isSwiped)return{...e,isSwiped:!1,isDragging:!1};const t={...e,isDragging:!1},n=y(h[r.Collapsed].descendingBreakPoint,e.windowInnerHeight),o=y(h[r.Full].ascendingBreakPoint,e.windowInnerHeight),i=e.moveInitialPoint+e.heightIncrement;let a={};return a=i<n?w(e):i>o?S(e,H):function(e,t){const n=e.heightIncrement+e.moveInitialPoint,r=(t.current?.offsetHeight??0)+g.panAreaHeight;if(r<n)return I(e,r);const o=f(e.windowInnerHeight,n);return{moveInitialPoint:o,heightIncrement:v(e,0),opacity:C(e.windowInnerHeight,o)}}(e,H),{...t,...a}}))})),t.on("pan",(e=>{D((t=>{if(t.isSwiped)return t;const n=v(t,-e.deltaY);return{...t,isDragging:!0,heightIncrement:n,opacity:C(t.windowInnerHeight,n+t.moveInitialPoint)}}))}))),E||t.on("swipe",(e=>{Math.abs(e.velocityY)<g.swipeVelocityThreshold||D((t=>{const n={...t,isSwiped:!0};let o={};const i=e.direction===Hammer.DIRECTION_UP,a=e.direction===Hammer.DIRECTION_DOWN,s=t.moveInitialPoint+t.heightIncrement,l=s<y(h[r.Medium].ascendingBreakPoint,t.windowInnerHeight),c=s<y(h[r.Medium].descendingBreakPoint,t.windowInnerHeight);return i?o=l?P(t,H):S(t,H):a&&(o=c?w(t):P(t,H)),{...n,...o}}))})),()=>{t.off("panend"),B||t.off("pan"),E||t.off("swipe"),t.destroy()}}),[]),(0,o.useEffect)((()=>{function e(){D((e=>{const t=e.moveInitialPoint+e.heightIncrement<=h[r.Collapsed].position,n=document.documentElement.clientHeight/e.windowInnerHeight,o=Math.round(e.moveInitialPoint*n);return{...e,windowInnerHeight:document.documentElement.clientHeight,windowInnerWidth:document.documentElement.clientWidth,moveInitialPoint:t?e.moveInitialPoint:f(document.documentElement.clientHeight,o)}}))}return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[]),(0,o.useEffect)((()=>{if(!N.animate)return;const e=window.setTimeout((()=>{D((e=>({...e,animate:!1})))}),g.animationTiming);return()=>{window.clearTimeout(e)}}),[N.animate]);const z=100-100*N.opacity,W=(0,l.isOnMobileAppPage)("any")&&R===c.StdTheme.Dark?"color-black":"color-bg-primary",G={"--resizable-drawer-height":F,"--resizable-drawer-opacity":N.opacity,"--resizable-drawer-animate":N.animate?1:0,"--resizeable-drawer-drawer-closed-height":h[r.Collapsed].position,"--resizeable-drawer-animation-timing":g.animationTiming,"--resizeable-drawer-pan-area-height":g.panAreaHeight,"--resizeable-drawer-pan-area-invisible-height":g.panAreaInvisibleHeight,"--resizable-drawer-translate-y":O,"--controls-content-height":g.controlsContentHeight+"px","--resizable-drawer-bg":(0,m.generateColor)(u.themes[R].getThemedColor(W),z)};return o.createElement("div",null,o.createElement("div",{className:s()(b.drawer),style:G},o.createElement("div",{className:s()(b.rootWrapper,N.animate&&b.animate),ref:M},o.createElement("div",{className:s()(b.panAreaWrapperStyle,N.animate&&b.animate)},o.createElement("div",{className:b.panAreaStyle,ref:x},a&&o.createElement("div",{className:s()(b.controlsWrapper,N.animate&&b.animate)},o.createElement("div",{className:b.controlsContent},a))),o.createElement("div",{
className:b.panTile})),o.createElement("div",{className:s()(b.drawerWrapperStyle,N.animate&&b.animate,A&&b.scroll,!A&&b.hidden)},n&&o.createElement("div",{className:b.closedContentStyle},n),o.createElement("div",{className:b.mainContentStyle,ref:H},t)))))}},608636:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="currentColor" d="M5.18 6.6L1.3 2.7.6 2 2 .59l.7.7 3.9 3.9 3.89-3.9.7-.7L12.61 2l-.71.7L8 6.6l3.89 3.89.7.7-1.4 1.42-.71-.71L6.58 8 2.72 11.9l-.71.7-1.41-1.4.7-.71 3.9-3.9z"/></svg>'}}]);