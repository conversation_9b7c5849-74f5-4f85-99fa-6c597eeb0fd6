"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[5122],{212815:(e,r,s)=>{s.r(r),s.d(r,{LineToolBrush:()=>n});var t=s(792535),i=s(504031),o=s(981856);class n extends i.LineToolBrushBase{constructor(e,r,s,t){super(e,r??n.createProperties(e.backgroundTheme().spawnOwnership()),s,t),this._loadPaneViews(e)}smooth(){return this.properties().childs().smooth.value()}name(){return"Brush"}static createProperties(e,r){const s=new t.DefaultProperty({defaultName:"linetoolbrush",state:r,theme:e});return this._configureProperties(s),s}_loadPaneViews(e){Promise.all([s.e(6290),s.e(6881),s.e(5579),s.e(1583)]).then(s.bind(s,60137)).then((r=>{this._setPaneViews([new r.BrushPaneView(this,e)])}))}_getPropertyDefinitionsViewModelClass(){return Promise.all([s.e(6406),s.e(8511),s.e(5234),s.e(4590),s.e(8537)]).then(s.bind(s,416828)).then((e=>e.BrushDefinitionsViewModel))}static _configureProperties(e){super._configureProperties(e),e.addChild("backgroundsColors",new o.LineToolColorsProperty([e.childs().backgroundColor],e.childs().fillBackground)),e.hasChild("linestyle")&&e.removeProperty("linestyle"),e.hasChild("linesStyles")&&e.removeProperty("linesStyles")}}}}]);