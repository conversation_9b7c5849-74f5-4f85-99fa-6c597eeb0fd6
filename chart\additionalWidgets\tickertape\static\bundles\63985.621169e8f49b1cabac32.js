(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[63985],{800828:(e,t,s)=>{"use strict";s.d(t,{createCache:()=>o});var r=s(650151);class n{constructor(e){this._cache=e}async getValue(e){const t=await this._cache.match(e);if(!t)return null;return"application/json"===(0,r.ensureNotNull)(t.headers.get("Content-Type"))?t.json():t.text()}async setValue(e,t){return"string"==typeof t?this._cache.put(e,new Response(t,{headers:{"content-type":"text/plain"}})):this._cache.put(e,new Response(JSON.stringify(t),{headers:{"content-type":"application/json"}}))}async migrateValue(e,t){const s=await this.getValue(e);s&&(await this.setValue(t,s),await this._cache.delete(e))}}class a{constructor(){this._data=new Map}async getValue(e){return this._data.get(e)??null}async setValue(e,t){this._data.set(e,t)}async migrateValue(e,t){this._data.has(e)&&(this._data.set(t,this._data.get(e)),this._data.delete(e))}}async function o(e){try{const t=await caches.open(e);return new n(t)}catch{return new a}}},803155:(e,t,s)=>{e.exports=[{name:s.i18next(null,{context:"symbols_type"},s(398361)),value:"",search_type:"undefined"},{name:s.i18next(null,{context:"symbols_type"},s(965331)),value:"stocks",search_type:"stocks"},{name:s.i18next(null,{context:"symbols_type"},s(589740)),value:"funds",search_type:"funds"},{name:s.i18next(null,{context:"symbols_type"},s(141504)),value:"futures",search_type:"futures"},{name:s.i18next(null,{context:"symbols_type"},s(570135)),value:"forex",search_type:"forex"},{name:s.i18next(null,{context:"symbols_type"},s(869328)),value:"cfd",search_type:"cfd"},{name:s.i18next(null,{context:"symbols_type"},s(125354)),value:"bitcoin,crypto",search_type:"crypto"},{name:s.i18next(null,{context:"symbols_type"},s(875313)),value:"index",search_type:"index"},{name:s.i18next(null,{context:"symbols_type"},s(207586)),value:"bond",search_type:"bond"},{name:s.i18next(null,{context:"symbols_type"},s(2380)),value:"economic",search_type:"economic"},{name:s.i18next(null,{context:"symbols_type"},s(703374)),value:"options",search_type:"options"}]},37010:(e,t,s)=>{"use strict";s.d(t,{AuthToken:()=>i});var r=s(735566),n=s(547465),a=s(803912);const o=(0,r.getLogger)("ChartApi.AuthToken",{color:"#173"});class i{constructor(){this.invalidated=new n.Delegate,this._tid=0,this._onOnline=()=>{o.logInfo("Online, new token will be fetched if needed"),this.get()},window.loginStateChange||(window.loginStateChange=new n.Delegate),window.loginStateChange.subscribe(this,(e=>{e||(this._set(window.user.auth_token),this.invalidated.fire())})),this._set(window.user.auth_token),window.addEventListener("online",this._onOnline)}destroy(){window.removeEventListener("online",this._onOnline)}get(e){return window.is_authenticated?!e&&performance.now()<this._cache.monoValidThru&&Date.now()<this._cache.wallValidThru?(o.logInfo(`Using cached token ${this._cache.token}`),Promise.resolve(this._cache.token)):window.navigator.onLine?this._fetch(Boolean(e),0).then((e=>{if(!window.is_authenticated)throw new Error("User logged out while the request was in flight")
;return this._set(e),e})).catch((e=>(o.logError(`Error fetching new token: ${e&&e.message}`),""))):(o.logWarn("Offline, cannot fetch new token, using cached token"),Promise.resolve(this._cache.token)):Promise.resolve("")}reset(){this._set(void 0),this.invalidated.fire()}_set(e){if(window.is_authenticated&&void 0!==e){const t=function(e){if(""===e)return 144e5;try{const t=JSON.parse(atob(e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"))),s=1e3*(t.exp-t.iat);if(!isFinite(s))throw new Error("invalid expiration");return Math.max(s-3e5,3e5)}catch(t){return o.logError(`${e} is invalid: ${t.message}`),144e5}}(e);this._cache={token:e,monoValidThru:performance.now()+t,wallValidThru:Date.now()+t},clearTimeout(this._tid),this._tid=setTimeout((()=>{window.navigator.onLine&&this.reset()}),t),o.logInfo(`Cached for ${t} ms: ${e}`)}else this._cache={token:"",monoValidThru:-1/0,wallValidThru:-1/0},clearTimeout(this._tid),o.logInfo("Cache dropped")}_fetch(e,t){return o.logNormal(`Fetching a new token, grabSession=${e}`),(0,a.fetch)("/quote_token/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:e?"grabSession=true":""}).then((e=>{if(!e.ok)throw new Error(`Response status is not ok: ${e.status}`);return e.json().then(String)}),(s=>{if(t>=3)throw s;return o.logWarn("Request failed, will retry"),function(e){return new Promise((t=>setTimeout(t,1e3*e)))}(t).then((()=>this._fetch(e,t+1)))}))}}},181929:(e,t,s)=>{"use strict";var r;s.d(t,{ChartApiMessageMethod:()=>r}),function(e){e.Connected="connected",e.Disconnected="disconnected",e.ReconnectBailout="reconnect_bailout"}(r||(r={}))},403526:(e,t,s)=>{"use strict";s.d(t,{JSONDataHandler:()=>o});var r=s(241361);function n(e){return{index:e.i,value:e.v}}function a(e){for(const t of Object.keys(e)){const s=e[t];s.t&&(s.turnaround=e[t].t),"s"in e[t]&&e[t].s&&!s.series&&(s.series=e[t].s.map(n)),"st"in e[t]&&e[t].st&&!s.series&&(s.series=e[t].st.map(n))}}class o{set_broker(e){return[e]}set_data_quality(){return["low"]}quote_create_session(e){return[e]}quote_delete_session(e){return[e]}quote_set_fields(e,t){return[e].concat(t)}quote_add_symbols(e,t){return[e].concat(t)}quote_remove_symbols(e,t){return[e].concat(t)}quote_fast_symbols(e,t){return[e].concat(t)}quote_hibernate_all(e){return[e]}depth_create_session(e,t,s){return[e,t,s]}depth_delete_session(e){return[e]}depth_set_symbol(e,t){return[e,t]}depth_clear_symbol(e){return[e]}depth_set_scale(e,t){return[e,t]}chart_create_session(e,t){return[e,t?"disable_statistics":""]}chart_delete_session(e){return[e]}set_auth_token(e){return[e]}set_locale(e,t){return[e,t]}switch_timezone(e,t){return[e,t]}resolve_symbol(e,t,s){return[e,t,s]}create_series(e,t,s,r,n,a,o){return[e,t,s=s||"",r,n,a,o]}remove_series(e,t){return[e,t]}modify_series(e,t,s,r,n,a){return[e,t,s=s||"",r,n,a]}request_more_data(e,t,s){return[e,t,s]}set_future_tickmarks_mode(e,t){return[e,t]}request_studies_metadata(e){return[e]}create_study(e,t,s,r,n,a){return[e,t,s=s||"",r,n].concat(a)}create_child_study(e,t,s,r,n,a){return this.create_study(e,t,s,r,n,a)}
remove_study(e,t){return[e,t]}modify_study(e,t,s,r){return[e,t,s=s||""].concat(r)}notify_study(e,t,s,r){return[e,t,s=s||"",r]}create_pointset(e,t,s,r,n,a){return[e,t,s=s||"",r,n].concat(a)}modify_pointset(e,t,s,r){return[e,t,s=s||""].concat(r)}remove_pointset(e,t){return[e,t]}request_more_tickmarks(e,t,s){return[e,t,s]}get_first_bar_time(e,t,s){return[e,t,s]}replay_create_session(e){return[e]}replay_delete_session(e){return[e]}replay_reset(e,t,s){return[e,t,s]}replay_start(e,t,s){return[e,t,s]}replay_stop(e,t){return[e,t]}replay_step(e,t,s){return[e,t,s]}replay_add_series(e,t,s,r){return[e,t,s,r]}replay_remove_series(e,t,s,r){return[e,t,s,r]}replay_set_resolution(e,t,s){return[e,t,s]}replay_get_depth(e,t,s,r){return[e,t,s,r]}convertTimescaleResponse(e){const t=e.marks.map((e=>({span:e[0],time:e[1],index:e[2]}))),s=void 0===e.index_diff?[]:e.index_diff.map((e=>({old:e[0],new:e[1]})));return{...e,marks:t,index_diff:s,clear:0===e.changes.length&&0===s.length&&0===t.length}}getDataUpdateObjects(e){return e.params[0]}getTimescaleObjects(e){return e.params[0]}getTimescaleChangeset(e){return e.params[1]}prepareDataUpdateObjects(e,t,s){for(const[n,a]of Object.entries(t)){const t="plots"in a?a.plots:a.series,o={customId:n,turnaround:a.turnaround,plots:t};"ns"in a&&a.ns&&(o.nonseries=a.ns),"lbs"in a&&a.lbs&&(o.lastBar={closeTime:a.lbs.bar_close_time});for(const e of o.plots)for(const[t,s]of Object.entries(e.value))s&&Math.abs(s)>=1e100&&(e.value[+t]=void 0);s(e,n,{method:r.WebchartChartSessionResponseMethod.DataUpdate,params:o})}}unpack(e){const t=JSON.parse(e);t.m&&t.p&&(t.method=t.m,t.params=t.p,t.time=t.t);const s=t.params[1];switch(t.method){case"qsd":t.method="quote_symbol_data",s.symbolname=s.n,s.status=s.s,s.values=s.v,s.values.change=s.v.ch,s.values.last_price=s.v.lp,s.values.change_percent=s.v.chp,delete s.n,delete s.s,delete s.v,delete s.values.ch,delete s.values.lp,delete s.values.chp;break;case"du":t.method="data_update",a(s);break;case"clear_data":for(const e of Object.keys(s))s[e].turnaround=s[e].t;break;case"timescale_update":a(s)}return t}prepareEncodeMessage(e,t){return JSON.stringify({m:e,p:t})}request_data_problems(){return[]}}},903656:(e,t,s)=>{"use strict";s.d(t,{HandlerInfo:()=>r});class r{constructor(e,t,s){this.handler=e,this.customId=t,this.singleShot=s}}},241361:(e,t,s)=>{"use strict";var r,n,a;s.d(t,{WebchartChartSessionResponseMethod:()=>n,WebchartReplaySessionResponseMethod:()=>a,WebchartResponseMethod:()=>r}),function(e){e.RequestDataProblems="request_data_problems",e.ProtocolError="protocol_error",e.ProtocolSwitched="protocol_switched",e.CriticalError="critical_error"}(r||(r={})),function(e){e.StudiesMetadata="studies_metadata",e.TimeScaleUpdate="timescale_update",e.TickMarkUpdate="tickmark_update",e.DU="du",e.DataUpdate="data_update",e.IndexUpdate="index_update",e.TimeScaleCompleted="timescale_completed",e.QSD="qsd",e.QuoteListField="quote_list_fields",e.QuoteSymbolData="quote_symbol_data",e.QuoteCompleted="quote_completed",e.DepthSymbolError="depth_symbol_error",
e.DepthSymbolSuccess="depth_symbol_success",e.DepthData="dd",e.DepthDataUpdate="dpu",e.DepthBarLastValue="depth_bar_last_value",e.ClearData="clear_data",e.SeriesTimeFrame="series_timeframe",e.SymbolResolved="symbol_resolved",e.SeriesError="series_error",e.SeriesCompleted="series_completed",e.StudyLoading="study_loading"}(n||(n={})),function(e){e.ReplayError="replay_error",e.ReplayPoint="replay_point",e.ReplayOk="replay_ok",e.ReplayResolutions="replay_resolutions",e.ReplayDataEnd="replay_data_end",e.ReplayInstanceId="replay_instance_id",e.ReplayDepth="replay_depth"}(a||(a={}))},894645:(e,t,s)=>{"use strict";s.d(t,{getDefaultResolutions:()=>a});var r=s(931924),n=s(303999);function a(){const e=["1","3","5","15","30","45","60","120","180","240","1D","1W","1M","3M","6M","12M","1R","10R","100R","1000R"];if(!r.enabled("widget")){let t=["1S","5S","10S","15S","30S","45s"];return(0,n.isTicksEnabled)()&&(t=["1T","10T","100T","1000T"].concat(t)),t.concat(e)}return e}},13692:(e,t,s)=>{"use strict";s.d(t,{types:()=>r});const r={ECONOMIC:"economic",QUANDL:"quandl"}},646244:(e,t,s)=>{"use strict";function r(e){const t={};if(-1===e.indexOf("@"))t.shortId=e,t.packageId="tv-basicstudies",t.id=e+"@"+t.packageId,t.version=1;else{const s=e.split("@");t.shortId=s[0];const r=s[1].split("-");if(3===r.length)t.packageId=r.slice(0,2).join("-"),t.id=t.shortId+"@"+t.packageId,t.version=parseInt(r[2]);else if(1===r.length&&"decisionbar"===r[0])t.packageId="les-"+r[0],t.id=t.shortId+"@"+t.packageId,t.version=1;else{if(1!==r.length)throw new Error("unexpected study id:"+e);t.packageId="tv-"+r[0],t.id=t.shortId+"@"+t.packageId,t.version=1}}if(t.fullId=t.id+"-"+t.version,"tv-scripting"===t.packageId){const e=t.shortId;if(0===e.indexOf("Script$")||0===e.indexOf("StrategyScript$")){const s=e.indexOf("_");t.productId=s>=0?e.substring(0,s):t.packageId}else t.productId=t.packageId}else t.productId=t.packageId;return t}s.d(t,{parseIdString:()=>r})},107447:(e,t,s)=>{"use strict";function r(e){if(void 0===e)return"";if(e instanceof Error){let t=e.message;return e.stack&&(t+=" "+e.stack),t}return"string"==typeof e?e.toString():JSON.stringify(e)}s.d(t,{errorToString:()=>r})}}]);