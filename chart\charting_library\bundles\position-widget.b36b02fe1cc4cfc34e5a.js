(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[8751],{872379:e=>{e.exports={dialog:"dialog-YZ_qHmON",dialogBody:"dialogBody-YZ_qHmON"}},932106:e=>{e.exports={positionInfo:"positionInfo-oWbWR7tt",title:"title-oWbWR7tt",contentWrapper:"contentWrapper-oWbWR7tt"}},414921:e=>{e.exports={positionPanel:"positionPanel-vffbLvO_"}},109798:e=>{e.exports={positionWidget:"positionWidget-mbUCea4m",separator:"separator-mbUCea4m",brackets:"brackets-mbUCea4m",customFieldsWrapper:"customFieldsWrapper-mbUCea4m",infoWrapper:"infoWrapper-mbUCea4m",warning:"warning-mbUCea4m",button:"button-mbUCea4m"}},953517:(e,o,t)=>{"use strict";t.d(o,{useKeyboardActionHandler:()=>n.useKeyboardActionHandler,useKeyboardClose:()=>n.useKeyboardClose,useKeyboardEventHandler:()=>n.useKeyboardEventHandler,useKeyboardOpen:()=>n.useKeyboardOpen,useKeyboardToggle:()=>n.useKeyboardToggle});var n=t(865968)},562152:(e,o,t)=>{"use strict";t.r(o),t.d(o,{closePositionDialog:()=>A,mountPositionPanel:()=>L,showPositionDialog:()=>T,showPositionDrawer:()=>U,unmountPositionPanel:()=>z});var n=t(50959),a=t(632227),r=t(801808),s=t(930894),l=t(533408),i=t(601227),d=t(180185),c=t(918460),u=t(671945),m=t(650151),p=t(970267),b=t(308825),g=t(570446),f=t(363111),v=t(661851),C=t(204926),E=t(932106);function w(e){const{model:o}=e,t=(0,v.useObservable)(o.infoTableData$,{rows:[]});return n.createElement("div",{className:E.positionInfo},n.createElement("div",{className:E.title},o.title()),n.createElement("div",{className:E.contentWrapper},n.createElement(C.InfoTable,{rows:t.rows,header:t.header,disabled:!1})))}var O=t(556013),h=t(763281),y=t(109798);const P=(0,u.getLogger)("Trading.OrderPanel");function M(e){const{model:o,focus:t,shouldShowActionButton:a}=e,r=(0,n.useRef)(null),s=(0,v.useObservable)(o.warning$),{mode:l}=(0,n.useContext)(h.WidgetContext);(0,n.useEffect)((()=>{null!==r.current&&void 0===t&&r.current.focus()}),[]);const u=(0,n.useCallback)((e=>{const t=(0,d.hashFromEvent)(e);13!==t&&t!==d.hashShiftPlusEnter||o.isButtonDisabled()||(i.CheckMobile.any()?document.activeElement instanceof HTMLElement?document.activeElement.blur():P.logWarn("Failed to deselect: active element is not HTMLElement"):o.doneButtonClick())}),[o]),C=o.customFieldsModel.getCustomFieldsModels(),E=function(e,o){return o===f.OrderEditorDisplayMode.Panel&&null!==e.positionInfoModel&&e.positionInfoModel.getOrderInfoTableRowsCount()>0}(o,l);return n.createElement("div",{className:y.positionWidget,onKeyDown:u,tabIndex:-1,ref:r},(l===f.OrderEditorDisplayMode.Popup||l===f.OrderEditorDisplayMode.ResizableDrawer)&&n.createElement(p.HeaderContainer,{...o.headerStateValue}),n.createElement("div",{className:y.brackets},n.createElement(b.BracketControlGroup,{model:o,focus:t})),C.length>0&&n.createElement("div",{className:y.customFieldsWrapper},n.createElement("div",{className:y.separator}),n.createElement(O.OrderPanelCustomFields,{customFieldModels:C})),s&&n.createElement(c.Informer,{className:y.warning,content:s,informerIntent:"default"}),void 0!==o.buttonModel&&a&&n.createElement("div",{
className:y.button},n.createElement(g.PlaceAndModifyButton,{model:o,buttonModel:o.buttonModel})),E&&n.createElement("div",{className:y.infoWrapper},n.createElement("div",{className:y.separator}),n.createElement(w,{"data-name":"position-info",model:(0,m.ensureNotNull)(o.positionInfoModel)})))}var W=t(274837),D=t(872379);const N=n.memo((e=>{const{model:o,isOpened:t,focus:a,onOpen:r,onClose:i}=e;return(0,W.useCommonDialogHandlers)({isOpened:t,onOpen:r,onClose:i}),n.createElement(l.AdaptivePopupDialog,{className:D.dialog,isOpened:t,dataName:"position-dialog-popup",render:function(){return n.createElement(s.Body,{className:D.dialogBody},n.createElement(M,{key:o.id,model:o,focus:a,shouldShowActionButton:!0}))},onClose:()=>i?.()})}));var k=t(910549),B=t(977540),$=t(110686);const F=n.memo((e=>{const{model:o,isOpened:t,focus:a,onOpen:r,onClose:s}=e;return(0,W.useCommonDialogHandlers)({isOpened:t,onOpen:r,onClose:s}),n.createElement(B.ResizableDrawer,{controlsContent:n.createElement($.OrderDrawerControlContent,{buttonModel:o.buttonModel,onClose:s,side:()=>o.side(),isButtonDisabled$:o.isButtonDisabled$,isButtonDisabled:()=>o.isButtonDisabled(),loading$:o.loading$,doneButtonClick:()=>o.doneButtonClick()}),disableSwipe:!1,disablePan:!1,initialContentHeight:B.ResizableDrawerContentHeight.Full},n.createElement(k.PopupContext.Consumer,null,(()=>n.createElement(M,{model:o,key:o.id,focus:a,shouldShowActionButton:!1}))))}));var R=t(414921);function H(e){return n.createElement("div",{"data-name":"position-panel",className:R.positionPanel},n.createElement(M,{...e,key:e.model.id,shouldShowActionButton:!0}))}var x=t(651674);let I=null,K=null;function T(e){const{viewModel:o,settings$:t,mode:a,focus:r,onClose:s,onOpen:l}=e,i=n.createElement(h.OrderTicketProvider,{settings$:t,mode:a,orderPanelStatus$:o.status$,isFractional:o.isFractional},n.createElement(N,{model:o,isOpened:!0,focus:r,onOpen:l,onClose:()=>A(s)}));K?K.render(i):(I=document.createElement("div"),document.body.appendChild(I),K=(0,x.createReactRoot)(i,I))}const S="position-drawer-widget";function A(e){I&&(document.body.removeChild(I),I=null),K?.unmount(),K=null;(0,r.getRootOverlapManager)().removeWindow(S),e?.()}function U(e){const{viewModel:o,settings$:t,mode:a,focus:s,onClose:l,onOpen:i}=e,d=(0,r.getRootOverlapManager)().ensureWindow(S),c=n.createElement(h.OrderTicketProvider,{settings$:t,mode:a,orderPanelStatus$:o.status$,isFractional:o.isFractional},n.createElement(F,{model:o,isOpened:!0,focus:s,onOpen:i,onClose:()=>A(l)}));K?K.render(c):K=(0,x.createReactRoot)(c,d)}function L(e,o,t,r,s){a.render(n.createElement(h.OrderTicketProvider,{settings$:o,mode:t,orderPanelStatus$:e.status$,isFractional:e.isFractional},n.createElement(H,{model:e,focus:s})),r)}function z(e){a.unmountComponentAtNode(e)}},317140:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m16.47 7.47 1.06 1.06L12.06 14l5.47 5.47-1.06 1.06L9.94 14l6.53-6.53Z"/></svg>'}}]);