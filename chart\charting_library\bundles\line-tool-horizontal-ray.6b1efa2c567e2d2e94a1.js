"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[574],{76410:(e,t,i)=>{i.d(t,{LineToolHorzLinePriceAxisView:()=>n});var r=i(887946),s=i(589637);class n extends r.PriceAxisView{constructor(e){super(),this._source=e}_updateRendererData(e,t,i){e.visible=!1;const r=this._source.points(),n=this._source.priceScale();if(0===r.length||null===n||n.isEmpty())return;const o=r[0];if(!isFinite(o.price))return;const l=this._source.ownerSource(),a=null!==l?l.firstValue():null;if(null===a)return;const c=(0,s.resetTransparency)(this._source.properties().linecolor.value());i.background=c,i.textColor=this.generateTextColor(c),i.coordinate=n.priceToCoordinate(o.price,a),e.text=n.formatPrice(o.price,a),e.visible=!0}}},257235:(e,t,i)=>{i.r(t),i.d(t,{LineToolHorzRay:()=>l});var r=i(792535),s=i(571772),n=i(76410),o=i(889868);class l extends o.LineDataSource{constructor(e,t,r,s){super(e,t??l.createProperties(e.backgroundTheme().spawnOwnership()),r,s),this._priceAxisView=new n.LineToolHorzLinePriceAxisView(this),Promise.all([i.e(6290),i.e(6881),i.e(5579),i.e(1583)]).then(i.bind(i,462789)).then((({HorzRayPaneView:e})=>{this._setPaneViews([new e(this,this._model)])}))}pointsCount(){return 1}name(){return"Horizontal Ray"}priceAxisViews(e,t){return this.isSourceHidden()||t!==this.priceScale()||!this._model.selection().isSelected(this)&&!this.properties().childs().showPrice.value()||e!==this._model.paneForSource(this)?null:[this._priceAxisView]}updateAllViews(e){super.updateAllViews(e),this._priceAxisView.update(e)}template(){const e=super.template();return e.text=this.properties().childs().text.value(),e}canHasAlert(){return!0}static createProperties(e,t){const i=new r.DefaultProperty({defaultName:"linetoolhorzray",state:t,theme:e});return this._configureProperties(i),i}_getAlertPlots(){const e=this._points[0],t={index:e.index+1,price:e.price},i=this._linePointsToAlertPlot([e,t],null,!1,!0);return null!==i?[i]:[]}async _getPropertyDefinitionsViewModelClass(){return(await Promise.all([i.e(6406),i.e(8511),i.e(5234),i.e(4590),i.e(8537)]).then(i.bind(i,328717))).HorizontalRayDefinitionsViewModel}_applyTemplateImpl(e){super._applyTemplateImpl(e),this.properties().childs().text.setValue(e.text||"")}static _configureProperties(e){super._configureProperties(e),e.hasChild("text")||e.addChild("text",new s.Property("")),e.addExcludedKey("text",1)}}}}]);